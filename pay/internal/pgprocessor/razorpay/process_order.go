// nolint: funlen
package razorpay

import (
	"github.com/jonboulle/clockwork"

	"github.com/epifi/be-common/pkg/idgen"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	actorPb "github.com/epifi/gamma/api/actor"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	recurringpaymentpb "github.com/epifi/gamma/api/recurringpayment"
	usersPb "github.com/epifi/gamma/api/user"
	vgPgPb "github.com/epifi/gamma/api/vendorgateway/pg"
	payConfig "github.com/epifi/gamma/pay/config/server"
	"github.com/epifi/gamma/pay/dao"
)

type PgProcessor struct {
	conf                 *payConfig.PgParams
	orderDao             dao.OrderDao
	transactionDao       dao.TransactionDao
	vendorOrderMapDao    dao.OrderVendorOrderMapDao
	vgPgClient           vgPgPb.PaymentGatewayClient
	piClient             piPb.PiClient
	usersClient          usersPb.UsersClient
	actorClient          actorPb.ActorClient
	rpClient             recurringpaymentpb.RecurringPaymentServiceClient
	clock                clockwork.Clock
	uuidGen              idgen.IUuidGenerator
	ownershipToTxnExecMp *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]
}

func NewPgProcessor(
	conf *payConfig.PgParams,
	orderDao dao.OrderDao,
	transactionDao dao.TransactionDao,
	vendorOrderMapDao dao.OrderVendorOrderMapDao,
	vgPgClient vgPgPb.PaymentGatewayClient,
	piClient piPb.PiClient,
	usersClient usersPb.UsersClient,
	actorClient actorPb.ActorClient,
	rpClient recurringpaymentpb.RecurringPaymentServiceClient,
	clock clockwork.Clock,
	uuidGen idgen.IUuidGenerator,
	ownershipToTxnExecMp *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
) *PgProcessor {
	proc := &PgProcessor{
		conf:                 conf,
		orderDao:             orderDao,
		transactionDao:       transactionDao,
		vendorOrderMapDao:    vendorOrderMapDao,
		vgPgClient:           vgPgClient,
		piClient:             piClient,
		usersClient:          usersClient,
		actorClient:          actorClient,
		rpClient:             rpClient,
		clock:                clock,
		ownershipToTxnExecMp: ownershipToTxnExecMp,
		uuidGen:              uuidGen,
	}
	return proc
}
