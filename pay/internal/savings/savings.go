package savings

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	balanceEnum "github.com/epifi/gamma/api/accounts/balance/enums"
	actorPb "github.com/epifi/gamma/api/actor"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type SavingsProcessor struct {
	actorClient          actorPb.ActorClient
	savingsClient        savingsPb.SavingsClient
	accountBalanceClient accountBalancePb.BalanceClient
}

func NewSavingsProcessor(
	actorClient actorPb.ActorClient,
	savingsClient savingsPb.SavingsClient, accountBalanceClient accountBalancePb.BalanceClient) *SavingsProcessor {
	return &SavingsProcessor{
		actorClient:          actorClient,
		savingsClient:        savingsClient,
		accountBalanceClient: accountBalanceClient,
	}
}

func (s *SavingsProcessor) GetSavingsAccountForActor(ctx context.Context, actorId string) (*savingsPb.Account, error) {
	actorRes, err := s.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{
		ActorId: actorId,
	})
	switch {
	case err != nil:
		return nil, fmt.Errorf("error in getting actor details %w", err)
	case !actorRes.GetStatus().IsSuccess():
		return nil, fmt.Errorf("non-success rpc status for GetEntityDetailsByActorId %v",
			zap.String(logger.RPC_STATUS, actorRes.GetStatus().String()))
	}
	return s.GetSavingsAccountForEntityId(ctx, actorRes.GetEntityId())
}

func (s *SavingsProcessor) GetSavingsAccountForEntityId(ctx context.Context, entityId string) (*savingsPb.Account, error) {

	savingsRes, err := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
			PrimaryUserId: entityId,
		},
	})
	switch {
	case err != nil:
		return nil, fmt.Errorf("error in GetAccount %w", err)
	case savingsRes.GetAccount() == nil:
		return nil, fmt.Errorf("nil account received for entityId %v", entityId)
	}
	return savingsRes.GetAccount(), nil
}

func (s *SavingsProcessor) GetSavingsAccountByAccountNumberAndIfsc(ctx context.Context, accountNo, ifsc string) (*savingsPb.Account, error) {
	acctReq := &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_ExternalId{
			ExternalId: &savingsPb.BankAccountIdentifier{
				AccountNo: accountNo,
				IfscCode:  ifsc,
			}},
	}
	acctRes, err := s.savingsClient.GetAccount(ctx, acctReq)
	switch {
	case err != nil && status.Code(err) != codes.NotFound:
		return nil, fmt.Errorf("error in GetAccount %w", err)
	case err != nil && status.Code(err) == codes.NotFound:
		return nil, fmt.Errorf("savings account not found %w", epifierrors.ErrRecordNotFound)
	case acctRes.GetAccount() == nil:
		return nil, fmt.Errorf("nil account received for  %v:%v", accountNo, ifsc)
	}
	return acctRes.GetAccount(), nil
}

func (s *SavingsProcessor) GetSavingsAccountBalanceForActor(ctx context.Context, actorId string, dataFreshness balanceEnum.DataFreshness) (*moneyPb.Money, error) {
	account, err := s.GetSavingsAccountForActor(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("error in Getting Account for Actor %w", err)
	}
	res, err := s.accountBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
		Identifier: &accountBalancePb.GetAccountBalanceRequest_Id{
			Id: account.GetId(),
		},
		ActorId:       actorId,
		DataFreshness: dataFreshness,
	})

	if err = epifigrpc.RPCError(res, err); err != nil {
		return nil, fmt.Errorf("error in getting account balance for actor %w", err)
	}
	return res.GetAvailableBalance(), nil
}

func (s *SavingsProcessor) GetSavingsAccountByAccountNumberAndVendor(ctx context.Context, accountNo string, vendor commonvgpb.Vendor) (*savingsPb.Account, error) {
	acctReq := &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_AccountNumBankFilter{
			AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
				AccountNumber: accountNo,
				PartnerBank:   vendor,
			}},
	}
	acctRes, err := s.savingsClient.GetAccount(ctx, acctReq)
	switch {
	case err != nil && status.Code(err) != codes.NotFound:
		return nil, fmt.Errorf("error in GetAccount %w", err)
	case err != nil && status.Code(err) == codes.NotFound:
		return nil, fmt.Errorf("savings account not found %w", epifierrors.ErrRecordNotFound)
	case acctRes.GetAccount() == nil:
		return nil, fmt.Errorf("nil account received for  %v:%v", accountNo, vendor)
	}
	return acctRes.GetAccount(), nil
}
