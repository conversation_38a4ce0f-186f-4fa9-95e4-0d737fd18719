package test

import (
	"log"

	"gorm.io/gorm"

	config "github.com/epifi/gamma/pay/config/server"
	payServerConfig "github.com/epifi/gamma/pay/config/server"
	"github.com/epifi/be-common/pkg/logger"
)

// InitTestServer initiates components needed for tests
// Will be invoked from TestMain but can be called from individual tests for *special* cases only
func InitTestServer() (*config.Config, *payServerConfig.Config, *gorm.DB, func()) {
	// Init config
	staticConf, err := config.Load()
	if err != nil {
		log.Fatal("failed to load static config", err)
	}
	// Setup logger
	logger.Init(staticConf.Application.Environment)

	// Init dynamic config
	conf, err := payServerConfig.Load()
	if err != nil {
		log.Fatal("failed to load safe config", err)
	}

	// Setup logger
	logger.Init(conf.Application.Environment)

	return staticConf, conf, nil, func() {
		_ = logger.Log.Sync()
	}
}
