package consumer

import (
	"flag"
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	mockActor "github.com/epifi/gamma/api/actor/mocks"
	mocks2 "github.com/epifi/gamma/api/user/mocks"
	mockOnb "github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/user/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	_, _, teardown = test.InitTestServerV2()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type mockServices struct {
	mockActorClient *mockActor.MockActorClient
	mockUserClient  *mocks2.MockUsersClient
	mockOnbClient   *mockOnb.MockOnboardingClient
}

func getTestServiceWithMock(t *testing.T) (*OnboardingUserUpdateVKYCConsumerService, *mockServices) {
	ctr := gomock.NewController(t)
	mockActorClient := mockActor.NewMockActorClient(ctr)
	mockUserCLient := mocks2.NewMockUsersClient(ctr)
	mockOnbClient := mockOnb.NewMockOnboardingClient(ctr)
	md := &mockServices{
		mockActorClient: mockActorClient,
		mockUserClient:  mockUserCLient,
		mockOnbClient:   mockOnbClient,
	}
	svc := &OnboardingUserUpdateVKYCConsumerService{
		userClient:  mockUserCLient,
		actorClient: mockActorClient,
		onbClient:   mockOnbClient,
	}
	return svc, md
}
