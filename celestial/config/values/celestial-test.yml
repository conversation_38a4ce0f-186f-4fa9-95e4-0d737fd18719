Application:
  Environment: "test"
  Name: "celestial"

Aws:
  Region: "ap-south-1"

PgdbConns:
  Enabled: true
  DbToConfigMap:
    US_STOCKS_ALPACA:
      UsePgdb: true
      PgdbConnAlias: "pgdb_conn"

SignalWorkflowSubscriber:
  SqsSubscriber:
    StartOnServerStart: true
    NumWorkers: 1
    PollingDuration: 20
    MaxMessages: 10
    QueueName: "celestial-signal-workflow-queue"
    RetryStrategy:
      ExponentialBackOff:
        BaseInterval: 1
        MaxAttempts: 8
        TimeUnit: "Second"
  BucketName: "epifi-celestial-extended-sqs"


InitiateWorkflowSubscriber:
  SqsSubscriber:
    StartOnServerStart: true
    NumWorkers: 1
    PollingDuration: 20
    MaxMessages: 10
    QueueName: "celestial-initiate-workflow-queue"
    RetryStrategy:
      ExponentialBackOff:
        BaseInterval: 1
        MaxAttempts: 8
        TimeUnit: "Second"
  BucketName: "epifi-celestial-extended-sqs"

InitiateProcrastinatorSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "celestial-initiate-procrastinator-workflow-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

InitiateWorkflowPublisher:
  QueueName: "celestial-initiate-workflow-queue"
  BucketName: "epifi-celestial-extended-sqs"

SignalWorkflowPublisher:
  QueueName: "celestial-signal-workflow-queue"
  BucketName: "epifi-celestial-extended-sqs"

TemporalClientInitOptions:
  TemporalCodecSecret: "853bbce933313713af7f43bb8fcc1d84"
  UseMigrationCluster: false
