Databases:
  CollateralMgrTspFederalPGDB:
    DbType: "PGDB"
    StatementTimeout: 5m
    Name: "collateral_mgr_tsp_federal"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "uat/rds/postgres"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 1ms
      UseInsecureLog: true

RedisRateLimiterName: "CollateralMgrRedisStore"
RedisClusters:
  CollateralMgrRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 12
