Environment: "test"
ServerPorts:
  HttpPort: 9092

GrpcRateLimiterParams:
  Disable: true

Databases:
  EpifiCRDB:
    DbType: "CRDB"
    AppName: "comms"
    StatementTimeout: 5s
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "epifi_test"
    EnableDebug: true
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  CommsPGDB:
    DbType: "PGDB"
    AppName: "comms"
    StatementTimeout: 5s
    Name: "comms_test"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 10
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  AccrualPGDB:
    AppName: "accrual"
    StatementTimeout: 3m
    Name: "accrual_test"
    EnableDebug: false
    SSLMode: "disable"
    MaxOpenConn: 8
    MaxIdleConn: 4
    MaxConnTtl: "30m"
    DbType: "PGDB"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 500ms
      UseInsecureLog: false

  CasperPGDB:
    AppName: "casper"
    StatementTimeout: 2m
    Name: "casper_test"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 6
    MaxIdleConn: 3
    MaxConnTtl: "30m"
    DbType: "PGDB"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 300ms
      UseInsecureLog: false

  RewardsPGDB:
    AppName: "rewards"
    StatementTimeout: 3m
    Name: "rewards_test"
    EnableDebug: false
    SSLMode: "disable"
    MaxOpenConn: 8
    MaxIdleConn: 4
    MaxConnTtl: "30m"
    DbType: "PGDB"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 500ms
      UseInsecureLog: false

  CmsPGDB:
    AppName: "cms"
    StatementTimeout: 2m
    Name: "cms_test"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 6
    MaxIdleConn: 3
    MaxConnTtl: "30m"
    DbType: "PGDB"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 300ms
      UseInsecureLog: false

  QuestPGDB:
    DbType: "PGDB"
    AppName: "quest"
    StatementTimeout: 10s
    Name: "quest_test"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 10
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 1m
      UseInsecureLog: true

  NudgePGDB:
    StatementTimeout: 5m
    DbType: "PGDB"
    Name: "nudge_test"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 20
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 1ms
      UseInsecureLog: false

  SegmentPGDB:
    StatementTimeout: 5m
    DbType: "PGDB"
    Name: "segment_test"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 20
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 1ms
      UseInsecureLog: false

RedisRateLimiterName: "RewardsRedisStore"
RedisClusters:
  CommsRedisStore:
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 5
  RewardsRedisStore:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 0
  CasperRedisStore:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 11
  NudgeRedisStore:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 0
  NudgeRankingRedisStore:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 10
  SegmentRedisStore:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 3
    HystrixCommand:
      CommandName: "segment_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 500
        ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
  CustomDelayQueueRedisStore:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 3
  QuestRedisStore:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 10
    ClientName: rewards-quest-sdk
    HystrixCommand:
      CommandName: "quest_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 500
        ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80

RueidisRedisClients:
  CollapserRueidisRedisStore:
    Addrs:
      - "localhost:6379"
    RedisDB: 0
    ClientName: "growth-infra-collapser"
    Hystrix:
      CommandName: "collapser_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 2500
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
    EnableSecureRedis: false

KeycloakAuth:
  TokenURL: "https://localhost:5400/realms/InternalNonProd/protocol/openid-connect/token"
  OIDC:
    ProviderURL: "https://localhost:5400/realms/InternalNonProd"
    ClientSecretKey: "{\"clientSecret\":\"sampleSecret\"}"
    GoOIDC:
      ClientID: "sampleClient"
      SkipClientIDCheck: true

JarvisInterceptorConf:
  DisableAuth: true
