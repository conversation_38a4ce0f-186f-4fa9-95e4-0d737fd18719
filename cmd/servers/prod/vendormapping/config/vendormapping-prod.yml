Databases:
  VendormappingPGDB:
    DbType: "PGDB"
    AppName: "vendormapping"
    StatementTimeout: 10s
    Name: "vendormapping"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/vendormapping"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

RedisRateLimiterName: "VendorMappingRedisStore"
RedisClusters:
  VendorMappingRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-16913.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:16913"
      ClientName: vendormapping
    AuthDetails:
      SecretPath: "prod/redis/vendormapping/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
