Databases:
  EpifiCRDB:
    DbType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  EpifiWealthCRDB:
    DBType: "CRDB"
    AppName: "investment"
    StatementTimeout: 10s
    Username: "epifi_wealth_dev_user"
    Password: ""
    Name: "epifi_wealth"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_wealth_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_wealth_dev_user.key"
    MaxOpenConn: 30
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  P2pInvestmentLiquiloansCRDB:
    DbType: "CRDB"
    AppName: "p2pinvestment"
    StatementTimeout: 10s
    Username: "p2pinvestment_liquiloans_dev_user"
    Password: ""
    Name: "p2pinvestment_liquiloans"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.p2pinvestment_liquiloans_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.p2pinvestment_liquiloans_dev_user.key"
    MaxOpenConn: 20
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  UsstocksAlpacaPGDB:
    DbType: "PGDB"
    AppName: "usstocks"
    StatementTimeout: 10s
    Name: "usstocks_alpaca"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 10
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/usstocks_alpaca_dev_user"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  BudgetingPGDB:
    DbType: "PGDB"
    AppName: "budgeting"
    StatementTimeout: 10s
    Name: "budgeting"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 10
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/budgeting"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: true

  EpifiWealthAnalyticsPGDB:
    DbType: "PGDB"
    AppName: "investment_analyser"
    StatementTimeout: 10s
    Name: "epifi_wealth_analytics"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/epifi-wealth-analytics"
    GormV2:
      LogLevelGormV2: "SILENT"
      SlowQueryLogThreshold: 60s

  CategorizerPGDB:
    DbType: "PGDB"
    AppName: "categorizer"
    StatementTimeout: 10s
    Name: "categorizer"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/categorizer_dev_user"
    GormV2:
      LogLevelGormV2: "SILENT"
      SlowQueryLogThreshold: 60s

  InsightsPGDB:
    DbType: "PGDB"
    AppName: "insights"
    StatementTimeout: 10s
    Name: "insights"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 30
    MaxIdleConn: 20
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/insights"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  NpsPGDB:
    DbType: "PGDB"
    AppName: "nps"
    StatementTimeout: 10s
    Name: "nps"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/nps_dev_user"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  ActorInsightsPGDB:
    DbType: "PGDB"
    AppName: "insights"
    StatementTimeout: 10s
    Name: "actor_insights"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/actor-insights"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  FitttPGDB:
    DbType: "PGDB"
    AppName: "fittt"
    StatementTimeout: 10s
    Name: "fittt"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 10
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/fittt_dev_user"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: true

  RmsPGDB:
    DbType: "PGDB"
    AppName: "rms"
    StatementTimeout: 10s
    Name: "rms"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 10
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/rms_dev_user"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: true

  VendordataPGDB:
    DbType: "PGDB"
    AppName: "vendordata"
    StatementTimeout: 10s
    Name: "vendordata"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/vendordata"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  StocksPGDB:
    DBType: "PGDB"
    AppName: "stocks"
    StatementTimeout: 10s
    EnableDebug: false
    Name: "stocks"
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/stocks_dev_user"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  InsightsFederalPGDB:
    DbType: "PGDB"
    AppName: "insights_federal"
    StatementTimeout: 10s
    Name: "insights_federal"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/insights_federal_dev_user"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

DBConfigMap:
  US_STOCKS_ALPACA:
    DbType: "PGDB"
    AppName: "usstocks"
    StatementTimeout: 10s
    Name: "usstocks_alpaca"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 10
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/usstocks_alpaca_dev_user"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  EPIFI_WEALTH:
    DBType: "CRDB"
    AppName: "investment"
    StatementTimeout: 10s
    Username: "epifi_wealth_dev_user"
    Password: ""
    Name: "epifi_wealth"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_wealth_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_wealth_dev_user.key"
    MaxOpenConn: 20
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  EPIFI_TECH:
    DBType: "CRDB"
    AppName: "investment"
    StatementTimeout: 5s
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 10
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  P2P_INVESTMENT_LIQUILOANS:
    DBType: "CRDB"
    AppName: "p2pinvestment"
    StatementTimeout: 10s
    Username: "p2pinvestment_liquiloans_dev_user"
    Password: ""
    Name: "p2pinvestment_liquiloans"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.p2pinvestment_liquiloans_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.p2pinvestment_liquiloans_dev_user.key"
    MaxOpenConn: 20
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  STOCK_GUARDIAN_TSP:
    DbType: "CRDB"
    AppName: "investment"
    StatementTimeout: 10s
    Username: "stockguardian_tsp_crdb_dev_user"
    Password: ""
    Name: "stockguardian_tsp_crdb"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.stockguardian_tsp_crdb_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.stockguardian_tsp_crdb_dev_user.key"
    MaxOpenConn: 60
    MaxIdleConn: 20
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  FEDERAL_BANK:
    DbType: "PGDB"
    AppName: "insights"
    StatementTimeout: 5s
    Name: "insights_federal"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/insights_federal_dev_user"
    GormV2:
      LogLevelGormV2: "SILENT"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false

Secrets:
  Ids:
    ZincCredentials: "prod/investment/zinc"

RedisRateLimiterName: "InvestmentRedisStore"
RedisClusters:
  CategorizerVMRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-11355.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11355"
      Password: ""
    AuthDetails:
      SecretPath: "prod/redis/wealth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
  InvestmentRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-11355.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11355"
      Password: ""
    AuthDetails:
      SecretPath: "prod/redis/wealth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
  MfCatalogRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-11355.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11355"
      Password: ""
    AuthDetails:
      SecretPath: "prod/redis/wealth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
  P2pInvestmentRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-11355.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11355"
      Password: ""
    AuthDetails:
      SecretPath: "prod/redis/wealth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
  USStocksAccountRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-11355.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11355"
      Password: ""
    AuthDetails:
      SecretPath: "prod/redis/wealth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
  USStocksRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-11355.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11355"
      Password: ""
    AuthDetails:
      SecretPath: "prod/redis/wealth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
  DynamicUIElementCacheRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-11355.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11355"
      Password: ""
    AuthDetails:
      SecretPath: "prod/redis/wealth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
  SearchRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-13988.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:13988"
      Password: ""
      DB: 0
    AuthDetails:
      SecretPath: "prod/redis/search/fullaccess"
      Environment: "prod"
      Region: "ap-south-1"
  FitttRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-11355.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11355"
    AuthDetails:
      SecretPath: "prod/redis/wealth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
  WealthRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-11355.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11355"
    AuthDetails:
      SecretPath: "prod/redis/wealth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: wealthonboarding
  AnalyserRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-11355.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11355"
      Password: ""
    AuthDetails:
      SecretPath: "prod/redis/wealth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
  SecuritiesRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-11355.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11355"
      Password: "" ## empty string for no password
    AuthDetails:
      SecretPath: "prod/redis/wealth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"


Profiling:
  AutomaticProfiling:
    EnableAutoProfiling: true
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 95
    MemoryPercentageLimit: 90
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  PyroscopeProfiling:
    EnablePyroscope: true

RueidisRedisClients:
  CollapserRueidisRedisStore:
    Addrs:
      - "redis-11355.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11355"
    AuthDetails:
      SecretPath: "prod/redis/wealth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: "wealth-collapser"
    Hystrix:
      CommandName: "collapser_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 2500
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
    EnableSecureRedis: true

UsecaseDbConfigMap:
  EPIFI_TECH:USE_CASE_NET_WORTH_ASSET_HISTORY:
    DBType: "CRDB"
    AppName: "investment"
    StatementTimeout: 5s
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 10
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false
  EPIFI_WEALTH:USE_CASE_NET_WORTH_ASSET_HISTORY:
    DbType: "PGDB"
    AppName: "insights"
    StatementTimeout: 10s
    Name: "epifi_wealth_analytics"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/epifi-wealth-analytics"
    GormV2:
      LogLevelGormV2: "SILENT"
      SlowQueryLogThreshold: 200ms
  FEDERAL_BANK:USE_CASE_NET_WORTH_ASSET_HISTORY:
    DbType: "PGDB"
    AppName: "insights"
    StatementTimeout: 5s
    Name: "insights_federal"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/insights_federal_dev_user"
    GormV2:
      LogLevelGormV2: "SILENT"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false

TemporalClientInitOptions:
  TemporalCodecAesKey: "prod/temporal/codec-encryption-key"
  UseMigrationCluster: false
