package migratetopgdb

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/multierr"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifierrors"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"

	nebulaActivityPb "github.com/epifi/gamma/api/nebula/activity"
	"github.com/epifi/gamma/nebula/config/worker/genconf"
	"github.com/epifi/gamma/nebula/internal/allocator"
)

type Processor struct {
	conf            *genconf.Config
	dbConnAllocator *allocator.DbConnAllocator
}

func NewProcessor(conf *genconf.Config, dbConnAllocator *allocator.DbConnAllocator) *Processor {
	return &Processor{conf, dbConnAllocator}
}

type BulkSyncHeartBeat struct {
	SkipDestCountCheck bool
	LastSyncTill       time.Time

	SkipGettingComputedColumns bool
	ComputedColumns            []string
}

type DeltaSyncHeartBeat struct {
	LastSyncTill               time.Time
	SkipGettingComputedColumns bool
	ComputedColumns            []string
}

type activityRequest interface {
	GetSourceTable() string
	GetDestTable() string
}

func logWithIdentifier(req activityRequest, fields ...interface{}) []interface{} {
	res := []interface{}{
		zap.String("sourceTable", req.GetSourceTable()),
		zap.String("destTable", req.GetDestTable()),
	}
	return append(res, fields...)
}

func (p *Processor) BulkSyncTable(ctx context.Context, req *nebulaActivityPb.BulkSyncTableRequest) (*nebulaActivityPb.BulkSyncTableResponse, error) {
	logger := activity.GetLogger(ctx)
	sourceDb, err := p.dbConnAllocator.GetCrdbConn(req.GetSourceDb())
	if err != nil {
		logger.Error("Error getting crdb connection", logWithIdentifier(req.GetTableMigrationConfig(), zap.Error(err))...)
		return nil, errors.Wrap(epifierrors.ErrTransient,
			fmt.Sprintf("Error creating crdb connection for db: %s, error: %s", req.GetSourceDb(), err))
	}

	destDb, err := p.dbConnAllocator.GetPgdbConn(req.GetDestDb())
	if err != nil {
		logger.Error("Error getting pgdb connection", logWithIdentifier(req.GetTableMigrationConfig(), zap.Error(err))...)
		return nil, errors.Wrap(epifierrors.ErrTransient,
			fmt.Sprintf("Error creating pgdb connection for db: %s, error: %s", req.GetDestDb(), err))
	}

	var heartbeat BulkSyncHeartBeat

	if activity.HasHeartbeatDetails(ctx) {
		if err := activity.GetHeartbeatDetails(ctx, &heartbeat); err != nil {
			logger.Error("Error getting hearbeat", logWithIdentifier(req.GetTableMigrationConfig())...)
			return &nebulaActivityPb.BulkSyncTableResponse{}, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("Error getting heartbeat: %s", err))
		}
	}

	cancelFn := activityPkg.SendPeriodicHeartbeat(ctx, time.Second*30, func() interface{} {
		return heartbeat
	})
	defer cancelFn()

	// Remove this check for bulk sync resumability scenario
	var count int64
	if err := destDb.Table(req.GetTableMigrationConfig().GetDestTable()).Count(&count).Error; err != nil {
		logger.Error("Error while getting count from target DB", logWithIdentifier(req.GetTableMigrationConfig(), zap.Error(err))...)
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while getting count from target DB: %s", err))
	}
	if count != 0 {
		logger.Error("Target table is not empty", logWithIdentifier(req.GetTableMigrationConfig())...)
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("target table is not empty"))
	}

	bulkSyncEndTime, err := bulkSync(ctx, &heartbeat, sourceDb, destDb, p.conf.MigrateToPgdbConf(), req)
	if err != nil {
		logger.Error("Error while bulk syncing", logWithIdentifier(req.GetTableMigrationConfig(), zap.Error(err))...)
		return nil, err
	}

	return &nebulaActivityPb.BulkSyncTableResponse{BulkSyncEndTime: timestampPb.New(bulkSyncEndTime)}, nil
}

func (p *Processor) DeltaSyncTable(ctx context.Context, req *nebulaActivityPb.DeltaSyncTableRequest) (*nebulaActivityPb.DeltaSyncTableResponse, error) {
	logger := activity.GetLogger(ctx)
	sourceDb, err := p.dbConnAllocator.GetCrdbConn(req.GetSourceDb())
	if err != nil {
		logger.Error("Error getting crdb connection", logWithIdentifier(req.GetTableMigrationConfig(), zap.Error(err))...)
		return &nebulaActivityPb.DeltaSyncTableResponse{}, multierr.Combine(epifierrors.ErrTransient,
			fmt.Errorf("Error creating connection for db: %s, error: %v", req.GetSourceDb(), err.Error()))
	}

	destDb, err := p.dbConnAllocator.GetPgdbConn(req.GetDestDb())
	if err != nil {
		logger.Error("Error getting pgdb connection", logWithIdentifier(req.GetTableMigrationConfig(), zap.Error(err))...)
		return &nebulaActivityPb.DeltaSyncTableResponse{}, multierr.Combine(epifierrors.ErrTransient,
			fmt.Errorf("Error creating connection for db: %s, error: %w", req.GetDestDb(), err))
	}

	var heartbeat DeltaSyncHeartBeat
	if activity.HasHeartbeatDetails(ctx) {
		if err := activity.GetHeartbeatDetails(ctx, &heartbeat); err != nil {
			logger.Error("Error getting hearbeat", logWithIdentifier(req.GetTableMigrationConfig())...)
			return &nebulaActivityPb.DeltaSyncTableResponse{}, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("Error getting heartbeat: %s", err))
		}
	}

	cancelFn := activityPkg.SendPeriodicHeartbeat(ctx, time.Second*30, func() interface{} {
		return heartbeat
	})
	defer cancelFn()

	err = deltaSync(ctx, &heartbeat, sourceDb, destDb, p.conf.MigrateToPgdbConf(), req.GetTableMigrationConfig(), req.GetSyncStartTime().AsTime())
	if err != nil {
		logger.Error("Error while delta syncing", logWithIdentifier(req.GetTableMigrationConfig(), zap.Error(err))...)
		return nil, err
	}
	return &nebulaActivityPb.DeltaSyncTableResponse{}, nil
}
