package dao_test

import (
	"context"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	categorizerPb "github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/categorizer/dao"
	"github.com/epifi/be-common/pkg/datetime"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
)

func TestCrowdAggregatedCategoryDaoImpl_Create(t *testing.T) {
	type args struct {
		ctx                context.Context
		crowdAggCategories []*categorizerPb.CrowdAggregatedCategory
		filterOptions      []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		want    []*categorizerPb.CrowdAggregatedCategory
		wantErr bool
	}{
		{
			name: "successfully create crowd agg db categories",
			args: args{
				ctx: context.Background(),
				crowdAggCategories: []*categorizerPb.CrowdAggregatedCategory{
					{
						PiId:            "piId2",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
						OntologyIds: []string{
							"ontologyId1",
							"ontologyId2",
						},
					},
					{
						PiId:            "piId3",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
						OntologyIds: []string{
							"ontologyId1",
						},
					},
				},
			},
			want: []*categorizerPb.CrowdAggregatedCategory{
				{
					PiId:            "piId2",
					AccountingEntry: payment.AccountingEntryType_DEBIT,
					OntologyIds: []string{
						"ontologyId1",
						"ontologyId2",
					},
				},
				{
					PiId:            "piId3",
					AccountingEntry: payment.AccountingEntryType_DEBIT,
					OntologyIds: []string{
						"ontologyId1",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "successfully update on conflict crowd agg db categories",
			args: args{
				ctx: context.Background(),
				crowdAggCategories: []*categorizerPb.CrowdAggregatedCategory{
					{
						PiId:            "piId1",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
						OntologyIds: []string{
							"ontologyId2",
						},
					},
					{
						PiId:            "piId3",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
						OntologyIds: []string{
							"ontologyId1",
						},
					},
				},
				filterOptions: []storagev2.FilterOption{
					dao.ClauseOnConflictDoUpdateWithTargetWhere([]string{"pi_id", "accounting_entry"}, true, nil, "deleted_at is null"),
				},
			},
			want: []*categorizerPb.CrowdAggregatedCategory{
				{
					PiId:            "piId1",
					AccountingEntry: payment.AccountingEntryType_DEBIT,
					OntologyIds: []string{
						"ontologyId2",
					},
				},
				{
					PiId:            "piId3",
					AccountingEntry: payment.AccountingEntryType_DEBIT,
					OntologyIds: []string{
						"ontologyId1",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "duplicate crowd agg db category entries",
			args: args{
				ctx: context.Background(),
				crowdAggCategories: []*categorizerPb.CrowdAggregatedCategory{
					{
						PiId:            "piId2",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
						OntologyIds: []string{
							"ontologyId1",
							"ontologyId2",
						},
					},
					{
						PiId:            "piId2",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
						OntologyIds: []string{
							"ontologyId1",
						},
					},
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "pi id cannot be empty",
			args: args{
				ctx: context.Background(),
				crowdAggCategories: []*categorizerPb.CrowdAggregatedCategory{
					{
						PiId:            "piId3",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
						OntologyIds: []string{
							"ontologyId1",
						},
					},
					{
						PiId:            "",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
						OntologyIds: []string{
							"ontologyId1",
							"ontologyId2",
						},
					},
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "accounting entry cannot be unspecified",
			args: args{
				ctx: context.Background(),
				crowdAggCategories: []*categorizerPb.CrowdAggregatedCategory{
					{
						PiId:            "piId3",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
						OntologyIds: []string{
							"ontologyId1",
						},
					},
					{
						PiId:            "piId2",
						AccountingEntry: payment.AccountingEntryType_ACCOUNTING_ENTRY_TYPE_UNSPECIFIED,
						OntologyIds: []string{
							"ontologyId1",
							"ontologyId2",
						},
					},
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Id is not uuid",
			args: args{
				ctx: context.Background(),
				crowdAggCategories: []*categorizerPb.CrowdAggregatedCategory{
					{
						Id:              "id",
						PiId:            "piId2",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
						OntologyIds: []string{
							"ontologyId1",
							"ontologyId2",
						},
					},
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Empty list of crowd aggregated categories",
			args: args{
				ctx:                context.Background(),
				crowdAggCategories: []*categorizerPb.CrowdAggregatedCategory{},
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, ts.pgdb, ts.conf.CategorizerDB.GetName(), affectedTestTables)
			c := dao.NewCrowdAggregatedCategoryDaoImpl(ts.pgdb)
			got, err := c.Create(tt.args.ctx, tt.args.crowdAggCategories, tt.args.filterOptions...)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isEqualCrowdAggregatedCategories(got, tt.want) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func isEqualCrowdAggregatedCategories(got, want []*categorizerPb.CrowdAggregatedCategory) bool {
	if len(got) != len(want) {
		return false
	}
	for idx, gotCrowdAggCat := range got {
		wantCrowdAggCat := want[idx]
		if gotCrowdAggCat.GetId() == "" {
			return false
		}
		wantCrowdAggCat.Id = gotCrowdAggCat.GetId()
		wantCrowdAggCat.CreatedAt = gotCrowdAggCat.GetCreatedAt()
		wantCrowdAggCat.UpdatedAt = gotCrowdAggCat.GetUpdatedAt()
		wantCrowdAggCat.DeletedAt = gotCrowdAggCat.GetDeletedAt()
		if diff := cmp.Diff(gotCrowdAggCat, wantCrowdAggCat, protocmp.Transform()); diff != "" {
			return false
		}
	}
	return true
}

func TestCrowdAggregatedCategoryDaoImpl_GetByPiIdAndAccountingEntry(t *testing.T) {
	type args struct {
		ctx             context.Context
		piId            string
		accountingEntry payment.AccountingEntryType
	}
	tests := []struct {
		name    string
		args    args
		want    *categorizerPb.CrowdAggregatedCategory
		wantErr bool
	}{
		{
			name: "successfully get crowd aggregated categories",
			args: args{
				ctx:             context.Background(),
				piId:            "piId1",
				accountingEntry: payment.AccountingEntryType_DEBIT,
			},
			want: &categorizerPb.CrowdAggregatedCategory{
				Id:              "********-278b-47df-a456-0fea8e29f7d4",
				PiId:            "piId1",
				AccountingEntry: payment.AccountingEntryType_DEBIT,
				OntologyIds:     []string{"ontologyId1"},
				CreatedAt:       timestamppb.New(time.Date(2023, 1, 10, 0, 0, 0, 0, datetime.IST)),
				UpdatedAt:       timestamppb.New(time.Date(2023, 1, 10, 0, 0, 0, 0, datetime.IST)),
				DeletedAt:       nil,
			},
			wantErr: false,
		},
		{
			name: "no record found",
			args: args{
				ctx:             context.Background(),
				piId:            "piId100",
				accountingEntry: payment.AccountingEntryType_DEBIT,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "piId is empty",
			args: args{
				ctx:             context.Background(),
				piId:            "",
				accountingEntry: payment.AccountingEntryType_DEBIT,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "accounting entry is unspecified",
			args: args{
				ctx:             context.Background(),
				piId:            "piId",
				accountingEntry: payment.AccountingEntryType_ACCOUNTING_ENTRY_TYPE_UNSPECIFIED,
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, ts.pgdb, ts.conf.CategorizerDB.GetName(), affectedTestTables)
			c := dao.NewCrowdAggregatedCategoryDaoImpl(ts.pgdb)
			got, err := c.GetByPiIdAndAccountingEntry(tt.args.ctx, tt.args.piId, tt.args.accountingEntry)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByPiIdAndAccountingEntry() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetByPiIdAndAccountingEntry() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCrowdAggregatedCategoryDaoImpl_BatchGetByPiIdAndAccountingEntry(t *testing.T) {
	type args struct {
		ctx                    context.Context
		piAccountingEntryPairs []*dao.PiIdAndAccountingEntryPair
	}
	tests := []struct {
		name    string
		args    args
		want    []*categorizerPb.CrowdAggregatedCategory
		wantErr bool
	}{
		{
			name: "successfully fetch multiple crowd aggregated categories",
			args: args{
				ctx: context.Background(),
				piAccountingEntryPairs: []*dao.PiIdAndAccountingEntryPair{
					{
						PiId:            "piId1",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
					},
					{
						PiId:            "piId21",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
					},
					{
						PiId:            "piId1",
						AccountingEntry: payment.AccountingEntryType_CREDIT,
					},
				},
			},
			want: []*categorizerPb.CrowdAggregatedCategory{
				{
					Id:              "********-278b-47df-a456-0fea8e29f7d4",
					PiId:            "piId1",
					AccountingEntry: payment.AccountingEntryType_DEBIT,
					OntologyIds:     []string{"ontologyId1"},
					CreatedAt:       timestamppb.New(time.Date(2023, 1, 10, 0, 0, 0, 0, datetime.IST)),
					UpdatedAt:       timestamppb.New(time.Date(2023, 1, 10, 0, 0, 0, 0, datetime.IST)),
					DeletedAt:       nil,
				},
				{
					Id:              "56eaa955-808a-43f6-b203-fd88ef234534",
					PiId:            "piId1",
					AccountingEntry: payment.AccountingEntryType_CREDIT,
					OntologyIds:     []string{"ontologyId2"},
					CreatedAt:       timestamppb.New(time.Date(2023, 1, 10, 0, 0, 0, 0, datetime.IST)),
					UpdatedAt:       timestamppb.New(time.Date(2023, 1, 10, 0, 0, 0, 0, datetime.IST)),
					DeletedAt:       nil,
				},
				{
					Id:              "a5b750fe-c2d3-40d1-a1d6-dbc7aadd67ad",
					PiId:            "piId21",
					AccountingEntry: payment.AccountingEntryType_DEBIT,
					OntologyIds:     []string{"ontologyId3", "ontologyId4"},
					CreatedAt:       timestamppb.New(time.Date(2023, 1, 10, 0, 0, 0, 0, datetime.IST)),
					UpdatedAt:       timestamppb.New(time.Date(2023, 1, 10, 0, 0, 0, 0, datetime.IST)),
					DeletedAt:       nil,
				},
			},
			wantErr: false,
		},
		{
			name: "piId is empty",
			args: args{
				ctx: context.Background(),
				piAccountingEntryPairs: []*dao.PiIdAndAccountingEntryPair{
					{
						PiId:            "piId1",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
					},
					{
						PiId:            "",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
					},
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "accounting entry is unspecified",
			args: args{
				ctx: context.Background(),
				piAccountingEntryPairs: []*dao.PiIdAndAccountingEntryPair{
					{
						PiId:            "piId1",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
					},
					{
						PiId:            "piId2",
						AccountingEntry: payment.AccountingEntryType_ACCOUNTING_ENTRY_TYPE_UNSPECIFIED,
					},
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "no record found",
			args: args{
				ctx: context.Background(),
				piAccountingEntryPairs: []*dao.PiIdAndAccountingEntryPair{
					{
						PiId:            "piId400",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
					},
					{
						PiId:            "piId500",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
					},
				},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, ts.pgdb, ts.conf.CategorizerDB.GetName(), affectedTestTables)
			c := dao.NewCrowdAggregatedCategoryDaoImpl(ts.pgdb)
			got, err := c.BatchGetByPiIdAndAccountingEntry(tt.args.ctx, tt.args.piAccountingEntryPairs)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetByPiIdAndAccountingEntry() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("BatchGetByPiIdAndAccountingEntry() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCrowdAggregatedCategoryDaoImpl_DeleteByPiIdAndAccountingEntry(t *testing.T) {
	type args struct {
		ctx                    context.Context
		piAccountingEntryPairs []*dao.PiIdAndAccountingEntryPair
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "successfully soft delete multiple crowd aggregated category",
			args: args{
				ctx: context.Background(),
				piAccountingEntryPairs: []*dao.PiIdAndAccountingEntryPair{
					{
						PiId:            "piId1",
						AccountingEntry: payment.AccountingEntryType_CREDIT,
					},
					{
						PiId:            "piId1",
						AccountingEntry: payment.AccountingEntryType_DEBIT,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, ts.pgdb, ts.conf.CategorizerDB.GetName(), affectedTestTables)
			c := dao.NewCrowdAggregatedCategoryDaoImpl(ts.pgdb)
			err := c.DeleteByPiIdAndAccountingEntry(tt.args.ctx, tt.args.piAccountingEntryPairs)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeleteByPiIdAndAccountingEntry() error = %v, wantErr %v", err, tt.wantErr)
			}

			if err == nil {
				// check if the entry was deleted or not
				got, err := c.BatchGetByPiIdAndAccountingEntry(tt.args.ctx, tt.args.piAccountingEntryPairs)
				if err != nil {
					t.Errorf("failed to get crowd aggregated categories: %v", err)
				}
				if len(got) != 0 {
					t.Errorf("crowd aggregated categories were not deleted")
				}
			}
		})
	}
}
