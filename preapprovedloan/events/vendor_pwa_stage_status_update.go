package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

type VendorPwaStageStatusUpdateEvent struct {
	ActorId    string
	ProspectId string
	EventName  string
	SessionId  string
	EventId    string
	Timestamp  time.Time
	EventType  string
	// custom properties
	LoanRequestId string
	LoanVendor    string
	LoanProgram   string
	StageName     string
	StageStatus   string
	UpdatedAt     time.Time
}

func NewVendorPwaStageStatusUpdateEvent(actorId, loanRequestId, loanVendor, loanProgram, stageName, stageStatus string, updateTime time.Time) *VendorPwaStageStatusUpdateEvent {
	return &VendorPwaStageStatusUpdateEvent{
		ActorId:   actorId,
		EventType: events.EventTrack,
		EventName: LoansVendorPwaStageStatusUpdate,
		EventId:   uuid.NewString(),
		Timestamp: time.Now(),
		// custom properties
		LoanRequestId: loanRequestId,
		LoanVendor:    loanVendor,
		LoanProgram:   loanProgram,
		StageName:     stageName,
		StageStatus:   stageStatus,
		UpdatedAt:     updateTime,
	}
}

func (c *VendorPwaStageStatusUpdateEvent) GetEventType() string {
	return c.EventType
}

func (c *VendorPwaStageStatusUpdateEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *VendorPwaStageStatusUpdateEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *VendorPwaStageStatusUpdateEvent) GetEventId() string {
	return c.EventId
}

func (c *VendorPwaStageStatusUpdateEvent) GetUserId() string {
	return c.ActorId
}

func (c *VendorPwaStageStatusUpdateEvent) GetProspectId() string {
	return c.ProspectId
}

func (c *VendorPwaStageStatusUpdateEvent) GetEventName() string {
	return c.EventName
}
