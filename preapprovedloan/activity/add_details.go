package activity

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
)

func (p *Processor) GetReferencesAdditionStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		lg := activity.GetLogger(ctx)
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}

		if len(lse.GetDetails().GetApplicantData().GetReferences()) == 0 {
			lg.Error("references data not entered by user")
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("references data not entered by user"))
		}

		return res, nil
	})
	return actRes, actErr
}
