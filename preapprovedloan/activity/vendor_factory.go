package activity

import (
	"context"
	"fmt"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/activity/vendors"
)

type VendorSpecificActivity interface {
	PopulateLOEC(ctx context.Context, req *palActivityPb.ProcessChildBatchActivityRequest) error
}

type Factory struct {
	federal                *vendors.Federal
	liquiloansPersonalLoan *vendors.LiquiLoansPersonalLoan
	liquiloansEarlySalary  *vendors.LiquiLoansEarlySalary
}

var (
	VendorNames = map[string]palPb.Vendor{
		"federal":    1,
		"liquiloans": 2,
	}
)

var (
	VendorPrograms = map[string]palPb.LoanProgram{
		"personal_loan": 1,
		"early_salary":  2,
	}
)

func NewFactory(
	federal *vendors.Federal,
	liquiloansPersonalLoan *vendors.LiquiLoansPersonalLoan,
	liquiloansEarlySalary *vendors.LiquiLoansEarlySalary,
) *Factory {
	return &Factory{
		federal:                federal,
		liquiloansPersonalLoan: liquiloansPersonalLoan,
		liquiloansEarlySalary:  liquiloansEarlySalary,
	}
}

func (f *Factory) getVendor(vendor palPb.Vendor, program palPb.LoanProgram) (VendorSpecificActivity, error) {
	switch vendor {
	case palPb.Vendor_FEDERAL:
		return f.federal, nil
	case palPb.Vendor_LIQUILOANS:
		switch program {
		case palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
			return f.liquiloansPersonalLoan, nil
		case palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
			return f.liquiloansEarlySalary, nil
		default:
			return nil, fmt.Errorf("no valid program found for vendor liquiloans")
		}
	default:
		return nil, fmt.Errorf("no valid vendor found")
	}
}
