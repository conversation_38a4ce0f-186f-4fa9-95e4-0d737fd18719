package idfc

import (
	"net/http"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	onceV2 "github.com/epifi/be-common/pkg/counter/once/v2"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	authPb "github.com/epifi/gamma/api/auth/orchestrator"
	consentPb "github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/docs"
	kycPb "github.com/epifi/gamma/api/kyc"
	idfcVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
	"github.com/epifi/gamma/pkg/feature/release"
	workerGenConf "github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/idfc"
	"github.com/epifi/gamma/preapprovedloan/helper"
	types2 "github.com/epifi/gamma/preapprovedloan/wire/types"
)

type Processor struct {
	loanStepExecutionDao   dao.LoanStepExecutionsDao
	loanApplicantDao       dao.LoanApplicantDao
	loanRequestDao         dao.LoanRequestsDao
	loanAccountDao         dao.LoanAccountsDao
	loanActivityDao        dao.LoanActivityDao
	loanOfferDao           dao.LoanOffersDao
	loanInstallmentInfoDao dao.LoanInstallmentInfoDao
	rpcHelper              *helper.RpcHelper
	deeplinkProvider       *idfc.Provider
	idfcPalVgClient        idfcVgPb.IdfcClient
	docsClient             docs.DocsClient
	httpClient             *http.Client
	s3Client               s3.S3Client
	orchestratorClient     authPb.OrchestratorClient
	lvClient               livenessPb.LivenessClient
	txnExecutor            storageV2.TxnExecutor
	deeplinkFactory        *deeplink.ProviderFactory
	consentClient          consentPb.ConsentClient
	kycClient              kycPb.KycClient
	conf                   *workerGenConf.Config
	commsHelper            *helper.CommsHelper
	multiDbDOnceMgr        onceV2.MultiDbDoOnce
	time                   datetimePkg.Time
	releaseEvaluator       release.IEvaluator
}

func NewProcessor(
	loanStepExecutionDao dao.LoanStepExecutionsDao,
	loanApplicantDao dao.LoanApplicantDao,
	loanRequestDao dao.LoanRequestsDao,
	loanAccountDao dao.LoanAccountsDao,
	loanActivityDao dao.LoanActivityDao,
	loanOfferDao dao.LoanOffersDao,
	loanInstallmentInfoDao dao.LoanInstallmentInfoDao,
	rpcHelper *helper.RpcHelper,
	idfcPalVgClient idfcVgPb.IdfcClient,
	deeplinkProvider *idfc.Provider,
	docsClient docs.DocsClient,
	httpClient *http.Client,
	s3client types2.PreApprovedLoanS3Client,
	authClient authPb.OrchestratorClient,
	lvClient livenessPb.LivenessClient,
	txnExecutor storageV2.TxnExecutor,
	deeplinkFactory *deeplink.ProviderFactory,
	consentClient consentPb.ConsentClient,
	kycClient kycPb.KycClient,
	conf *workerGenConf.Config,
	commsHelper *helper.CommsHelper,
	multiDbDOnceMgr onceV2.MultiDbDoOnce,
	time datetimePkg.Time,
	releaseEvaluator release.IEvaluator,
) *Processor {
	return &Processor{
		loanStepExecutionDao:   loanStepExecutionDao,
		loanApplicantDao:       loanApplicantDao,
		loanRequestDao:         loanRequestDao,
		loanActivityDao:        loanActivityDao,
		loanInstallmentInfoDao: loanInstallmentInfoDao,
		loanAccountDao:         loanAccountDao,
		loanOfferDao:           loanOfferDao,
		rpcHelper:              rpcHelper,
		idfcPalVgClient:        idfcPalVgClient,
		deeplinkProvider:       deeplinkProvider,
		docsClient:             docsClient,
		httpClient:             httpClient,
		s3Client:               s3client,
		orchestratorClient:     authClient,
		lvClient:               lvClient,
		txnExecutor:            txnExecutor,
		deeplinkFactory:        deeplinkFactory,
		consentClient:          consentClient,
		kycClient:              kycClient,
		conf:                   conf,
		commsHelper:            commsHelper,
		multiDbDOnceMgr:        multiDbDOnceMgr,
		time:                   time,
		releaseEvaluator:       releaseEvaluator,
	}
}
