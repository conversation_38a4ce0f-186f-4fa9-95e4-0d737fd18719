package stock_guardian

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	palEnumsPb "github.com/epifi/gamma/api/preapprovedloan/enums"
	sgApplicationPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/application"
	sgVendorBrePb "github.com/epifi/gamma/api/stockguardian/vendors/inhouse/bre"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	calculatorTypes "github.com/epifi/gamma/preapprovedloan/calculator/types"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

// nolint:funlen,gocritic
func (p *Processor) SgVendorBre(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		// fetch loan request
		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			if errors.Is(lrErr, epifierrors.ErrRecordNotFound) {
				palActivity.MarkLoanStepFail(lse)
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}

		oldLoanOffer, err := p.loanOfferDao.GetById(ctx, lr.GetOfferId())
		if err != nil {
			lg.Error("failed to fetch old loan offer by id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch old loan offer by id, err: %v", err))
		}

		loec, err := p.loecDao.GetById(ctx, oldLoanOffer.GetLoanOfferEligibilityCriteriaId())
		if err != nil {
			lg.Error("failed to get loec by id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get loec by id, err: %v", err))
		}

		// check if user is non fi core user
		userFeatProp, err := helper.GetUserFeatureProperty(ctx, lse.GetActorId(), p.onbClient, p.savingsClient)
		if err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in rpcHelper.IsNonFiCoreUser err: %v", err))
		}

		// fetch loan offer
		breResp, breErr := p.sgApplicationApiGateway.GetLoanOffer(ctx, &sgApplicationPb.GetLoanOfferRequest{
			LoanHeader: &sgApplicationPb.LoanHeader{
				ClientId: clientId,
			},
			ApplicationId:    lr.GetVendorRequestId(),
			IsOpenMarketUser: !userFeatProp.IsFiSAHolder,
			BreParams: &sgApplicationPb.GetLoanOfferRequest_BreParams{
				PolicyParams: &sgVendorBrePb.PolicyParams{
					DataInfo: &sgVendorBrePb.DataInfo{
						AaData: &sgVendorBrePb.DataInfo_AaData{
							MedianAmountSalaryLast_180Days: loec.GetPolicyParams().GetDataInfo().GetAaData().GetMedianAmountSalaryLast_180Days(),
						},
						IsB2BSalaryUser:         loec.GetPolicyParams().GetDataInfo().GetIsB2BSalaryUser(),
						MonthlyIncome:           loec.GetPolicyParams().GetDataInfo().GetMonthlyIncome(),
						MonthsSinceSalaryActive: loec.GetPolicyParams().GetDataInfo().GetMonthsSinceSalaryActive(),
						SalaryCreditDay:         loec.GetPolicyParams().GetDataInfo().GetSalaryCreditDay(),
					},
				},
				DesiredLoanAmount: loec.GetDataRequirementDetails().GetDesiredLoanAmount(),
			},
		})
		if te := epifigrpc.RPCError(breResp, breErr); te != nil {
			err = p.handleLoanOfferResponse(ctx, breResp, loec, oldLoanOffer)
			if err != nil {
				lg.Error("got error from loan offer response", zap.Error(err))
				return nil, err
			}
			lg.Error("failed to get loan offer", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get loan offer, err: %v", te))
		}

		// loec status already in approved status by FI BRE, only updating substatus
		loec.SubStatus = palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR
		loecFMs := []palPb.LoanOfferEligibilityCriteriaFieldMask{
			palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
		}
		newLoanOffer := p.getLoanOfferFromVendorResponse(breResp.GetDetails(), lr.GetActorId(), req.GetLoanProgram(), oldLoanOffer)
		areNewAndOldOfferSame, err := palActivity.AreOfferConstraintsSame(oldLoanOffer, newLoanOffer)
		if err != nil {
			lg.Error("error in checking if old and new offers are same", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in checking if old and new offers are same, err: %v", err))
		}
		if !areNewAndOldOfferSame {
			newOfferId, newOfferErr := p.createNewLoanOfferAndUpdateLr(ctx, newLoanOffer, oldLoanOffer, res.GetLoanStep(), lr)
			if newOfferErr != nil {
				lg.Error("error in creating new loan offer and updating lr", zap.Error(newOfferErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, newOfferErr.Error())
			}
			loec.SubStatus = palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR_WITH_CHANGED_OFFER
			loec.OfferId = newOfferId
			loecFMs = append(loecFMs, palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_OFFER_ID)
		}

		err = p.loecDao.Update(ctx, loec, loecFMs)
		if err != nil {
			lg.Error("failed to update loec with approved by vendor", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update loec with approved by vendor, err: %v", err))
		}
		if res.GetLoanStep().GetSubStatus() != palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_DETAILS_REVISED {
			// mark LSE success
			palActivity.MarkLoanStepSuccess(lse)
		}
		return res, nil
	})
	return actRes, actErr
}

func (p *Processor) getLoanOfferFromVendorResponse(offerRes *sgApplicationPb.LoanOfferDetailsResponse, actorId string, lp palPb.LoanProgram, oldOffer *palPb.LoanOffer) *palPb.LoanOffer {
	return &palPb.LoanOffer{
		ActorId:       actorId,
		Vendor:        palPb.Vendor_STOCK_GUARDIAN_LSP,
		VendorOfferId: uuid.New().String(),
		OfferConstraints: &palPb.OfferConstraints{
			MinLoanAmount:   offerRes.GetMinAmount(),
			MaxLoanAmount:   offerRes.GetMaxAmount(),
			MaxEmiAmount:    offerRes.GetMaxEmiAmount(),
			MaxTenureMonths: offerRes.GetMaxTenureInMonths(),
			MinTenureMonths: offerRes.GetMinTenureInMonths(),
		},
		ProcessingInfo: &palPb.OfferProcessingInfo{
			ProcessingFee: []*palPb.RangeData{
				{
					Value: &palPb.RangeData_Percentage{Percentage: offerRes.GetProcessingFeePercentage()},
					Start: offerRes.GetMinAmount().GetUnits(),
					End:   offerRes.GetMaxAmount().GetUnits(),
				},
			},
			InterestRate: []*palPb.RangeData{
				{
					Value: &palPb.RangeData_Percentage{Percentage: offerRes.GetInterestPercentage()},
					Start: offerRes.GetMinAmount().GetUnits(),
					End:   offerRes.GetMaxAmount().GetUnits(),
				},
			},
			Gst:           offerRes.GetGstPercentage(),
			ApplicationId: oldOffer.GetProcessingInfo().GetApplicationId(),
		},
		ValidSince:  timestampPb.Now(),
		ValidTill:   timestampPb.New(time.Now().AddDate(0, 0, 30)),
		LoanProgram: lp,
	}
}

// this function should only be used if a different offer is generated real time by stock guardian
func (p *Processor) createNewLoanOfferAndUpdateLr(ctx context.Context, newOffer *palPb.LoanOffer, oldOffer *palPb.LoanOffer, lse *palPb.LoanStepExecution, lr *palPb.LoanRequest) (string, error) {
	if lr.GetDetails().GetOldOfferId() != "" {
		return "", errors.New("offer is already updated once for this request")
	}
	txnErr := p.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		oldOffer.DeactivatedAt = timestampPb.Now()
		err := p.loanOfferDao.Update(txnCtx, oldOffer, []palPb.LoanOfferFieldMask{
			palPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_DEACTIVATED_AT,
		})
		if err != nil {
			return errors.Wrap(err, "failed to update old loan offer")
		}

		newOffer.LoanOfferEligibilityCriteriaId = oldOffer.GetLoanOfferEligibilityCriteriaId()
		newOffer, err = p.loanOfferDao.Create(txnCtx, newOffer)
		if err != nil {
			return errors.Wrap(err, "failed to create new loan offer from vendor offer")
		}

		// 1. If the existing loan request is valid with the new offer, just update the new offer id in loan request and continue to next stage
		// 2. Else update the lse sub status to LOAN_DETAILS_REVISED, update the next action and wait for user confirmation signal
		isLrValid, err := palActivity.IsLoanRequestValidWithNewOffer(lr, newOffer)
		if err != nil {
			return errors.Wrap(err, "error in checking if loan request is valid with new offer")
		}

		var lrFieldMasks []palPb.LoanRequestFieldMask

		if !isLrValid {
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_DETAILS_REVISED
			updateLseErr := p.loanStepExecutionDao.Update(txnCtx, lse, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS})
			if updateLseErr != nil {
				return errors.Wrap(updateLseErr, "failed to update loan step")
			}
			calculator, calculatorErr := p.calculatorFactory.GetCalculator(
				ctx,
				calculatorTypes.NewRequest(
					palEnumsPb.CalculatorAccuracy_CALCULATOR_ACCURACY_ACCURATE,
					newOffer.GetOfferConstraints().GetMinLoanAmount(),
					newOffer.GetOfferConstraints().GetMaxTenureMonths(),
					datetime.TimeToDateInLoc(time.Now(), datetime.IST),
					newOffer,
					nil,
				),
			)
			if calculatorErr != nil {
				return errors.Wrap(calculatorErr, "error in getting calculator")
			}
			lr.NextAction = baseprovider.GetRevisedOfferIntroDeeplink(newOffer, lr, calculator.GetEmiAmount())
			lrFieldMasks = append(lrFieldMasks, palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION)
		}
		lr.OfferId = newOffer.GetId()
		lr.GetDetails().OldOfferId = oldOffer.GetId()
		lrFieldMasks = append(lrFieldMasks, palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_OFFER_ID, palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_DETAILS)
		err = p.loanRequestDao.Update(txnCtx, lr, lrFieldMasks)
		if err != nil {
			return errors.Wrap(err, "failed to update offer id in loan request")
		}
		return nil
	})
	if txnErr != nil {
		return "", txnErr
	}
	return newOffer.GetId(), nil
}

func (p *Processor) handleLoanOfferResponse(ctx context.Context, breResp *sgApplicationPb.GetLoanOfferResponse, loec *palPb.LoanOfferEligibilityCriteria,
	oldOffer *palPb.LoanOffer) error {
	var errToReturn error
	switch breResp.GetStatus().GetCode() {
	case getStatusCode(sgApplicationPb.GetLoanOfferResponse_INTERNAL):
		return errors.Wrap(epifierrors.ErrTransient, "getting internal while fetching loan offer from vendor")
	case getStatusCode(sgApplicationPb.GetLoanOfferResponse_OFFER_GENERATION_IN_PROGRESS):
		return errors.Wrap(epifierrors.ErrTransient, "offer generation in progress")
	case getStatusCode(sgApplicationPb.GetLoanOfferResponse_OFFER_REJECTED):
		errToReturn = errors.Wrap(epifierrors.ErrPermanent, "user is rejected to get any offer")
	case getStatusCode(sgApplicationPb.GetLoanOfferResponse_OFFER_EXPIRED):
		errToReturn = errors.Wrap(epifierrors.ErrPermanent, "offer is expired for the user")
	case getStatusCode(sgApplicationPb.GetLoanOfferResponse_OFFER_LOCKED_BY_ANOTHER_APPLICATION):
		errToReturn = errors.Wrap(epifierrors.ErrPermanent, "offer is mapped to some another application already")
	default:
		return nil
	}

	// if permanent error, offer is set to get deactivated and loec to be marked as rejected by vendor
	txnErr := p.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		err := p.loanOfferDao.DeactivateLoanOffer(txnCtx, oldOffer.GetId())
		if err != nil {
			return errors.Wrap(err, "failed to deactivate old loan offer")
		}

		loec.Status = palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED
		loec.SubStatus = palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_VENDOR
		err = p.loecDao.Update(txnCtx, loec, []palPb.LoanOfferEligibilityCriteriaFieldMask{
			palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
			palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
		})
		if err != nil {
			return errors.Wrap(err, "failed to update offer id in loec")
		}
		return nil
	})
	if txnErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating lo and loec, err: %v", txnErr))
	}
	return errToReturn
}

func getStatusCode(sgStatusEnum sgApplicationPb.GetLoanOfferResponse_Status) uint32 {
	return uint32(sgApplicationPb.GetLoanOfferResponse_Status_value[sgStatusEnum.String()])
}
