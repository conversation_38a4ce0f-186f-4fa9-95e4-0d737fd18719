// nolint:dupl,gocritic
package provider

import (
	"context"
	"fmt"

	consentPb "github.com/epifi/gamma/api/consent"
	dePb "github.com/epifi/gamma/api/dynamic_elements"
	"github.com/epifi/gamma/preapprovedloan/consent"
)

type SavingsAccountBalancesScreenProvider struct {
	helper *DynamicElementsProviderHelper
}

func NewSavingsAccountBalancesScreenProvider(helper *DynamicElementsProviderHelper) *SavingsAccountBalancesScreenProvider {
	return &SavingsAccountBalancesScreenProvider{
		helper: helper,
	}
}

var _ IDynamicElementsProvider = &SavingsAccountBalancesScreenProvider{}

func (h *SavingsAccountBalancesScreenProvider) GetDynamicElements(ctx context.Context, req *GetDynamicElementsRequest) ([]*dePb.DynamicElement, error) {
	elem, err := h.helper.FetchCombinedConsentDynamicElementIfApplicable(ctx, req,
		consentPb.ConsentType_CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2,
		consentPb.ConsentType_CONSENT_SMS_DATA_PROCESSING_CONSENT,
		consent.W2TDataSharingAndSmsFetchingConsentDynamicElementId)
	if err != nil {
		return nil, fmt.Errorf("error while fetching dynamic element : %w", err)
	}

	if elem == nil {
		return nil, nil
	}

	return []*dePb.DynamicElement{elem}, nil
}
