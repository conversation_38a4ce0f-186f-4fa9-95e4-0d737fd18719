package utils

import (
	"math"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	decimalPkg "github.com/epifi/be-common/pkg/decimal"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

// TODO: the below code is taken from the old implementation, add better documentation
func GetTenure(loanAmount *moneyPb.Money, emiAmount *moneyPb.Money, interestRate float64) float64 {
	loanAmountPaise, _ := moneyPkg.ToPaise(loanAmount)
	emiAmountPaise, _ := moneyPkg.ToPaise(emiAmount)
	r := interestRate / (12 * 100)
	// crp = 1/(1-r*la/ea)
	crp := 1 / (1 - r*float64(loanAmountPaise)/float64(emiAmountPaise))
	tenure := math.Log(crp) / math.Log(1+r)
	return tenure
}

// TODO: the below code is taken from the old implementation, add better documentation
func CalculateApr(loanDisbursedAmount *moneyPb.Money, emiAmount *moneyPb.Money, tenureInMonths int32) float64 {
	loanDisbursedAmountFloat, _ := moneyPkg.ToDecimal(loanDisbursedAmount).Float64()
	emiFloat, _ := moneyPkg.ToDecimal(emiAmount).Float64()
	return (helper.GetIrr(loanDisbursedAmountFloat, emiFloat, tenureInMonths) * 12) * 100
}

// AddPercentage adds percentage to a number
// e.g.: AddPercentage(3, 18) => 3.54
// TODO: use decimal package so that we won't get float point issues
func AddPercentage(number float64, percentage float64) float64 {
	return number + (number * percentage / 100)
}

func GetPercentageAsRatio(percentage float64) decimal.Decimal {
	return decimal.NewFromFloat(percentage).Div(decimalPkg.OneHundred)
}

// GetLendenLoanSlabForAmount returns the loan slab for the given loan amount
// Note: This assumes that the slab amounts are non-overlapping
func GetLendenLoanSlabForAmount(loanOffer *preapprovedloan.LoanOffer, loanAmount *moneyPb.Money) (*preapprovedloan.LendenLoanSlab, error) {
	var matchingSlab *preapprovedloan.LendenLoanSlab
	for _, slab := range loanOffer.GetOfferConstraints().GetLendenConstraintInfo().GetLoanSlabs() {
		isAmountGreaterOrEqual, err := moneyPkg.IsGreaterThanOrEqual(loanAmount, slab.GetMinAmount())
		if err != nil {
			return nil, errors.Wrapf(err, "error comparing loan amount: %v with slab min amount: %v", loanAmount, slab.GetMinAmount())
		}
		isAmountLessOrEqual, err := moneyPkg.IsLessThanOrEqual(loanAmount, slab.GetMaxAmount())
		if err != nil {
			return nil, errors.Wrapf(err, "error comparing loan amount: %v with slab max amount: %v", loanAmount, slab.GetMaxAmount())
		}
		if isAmountGreaterOrEqual && isAmountLessOrEqual {
			matchingSlab = slab
			break
		}
	}
	if matchingSlab == nil {
		return nil, errors.Errorf("no loan slab found for loan amount: %v, slabs: %v", loanAmount, loanOffer.GetOfferConstraints().GetLendenConstraintInfo().GetLoanSlabs())
	}
	return matchingSlab, nil
}
