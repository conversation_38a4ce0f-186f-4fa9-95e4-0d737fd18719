package stage_executor_providers

import (
	"errors"

	"go.temporal.io/sdk/workflow"

	preapprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/secured_loans/workflow/stage_executor_providers/providers"
	"github.com/epifi/gamma/preapprovedloan/secured_loans/workflow/stage_executor_providers/providers/lamf/fiftyfin"
)

type GetPortfolioFetchStageExecutorRequest struct {
	Vendor      preapprovedLoanPb.Vendor
	LoanProgram preapprovedLoanPb.LoanProgram
}

func GetPortfolioFetchStageExecutor(_ workflow.Context, req GetPortfolioFetchStageExecutorRequest) (providers.IPortfolioFetchExecutor, error) {
	switch req.LoanProgram {
	case preapprovedLoanPb.LoanProgram_LOAN_PROGRAM_LAMF:
		return &fiftyfin.Provider{}, nil
	default:
		return nil, errors.New("no executor found for this loan program and vendor")
	}
}
