// Code generated by MockGen. DO NOT EDIT.
// Source: provider.go

// Package mock_providers is a generated GoMock package.
package mock_providers

import (
	context "context"
	reflect "reflect"

	preapprovedloan "github.com/epifi/gamma/api/preapprovedloan"
	providers "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers"
	gomock "github.com/golang/mock/gomock"
)

// MockILoanScheduleProvider is a mock of ILoanScheduleProvider interface.
type MockILoanScheduleProvider struct {
	ctrl     *gomock.Controller
	recorder *MockILoanScheduleProviderMockRecorder
}

// MockILoanScheduleProviderMockRecorder is the mock recorder for MockILoanScheduleProvider.
type MockILoanScheduleProviderMockRecorder struct {
	mock *MockILoanScheduleProvider
}

// NewMockILoanScheduleProvider creates a new mock instance.
func NewMockILoanScheduleProvider(ctrl *gomock.Controller) *MockILoanScheduleProvider {
	mock := &MockILoanScheduleProvider{ctrl: ctrl}
	mock.recorder = &MockILoanScheduleProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockILoanScheduleProvider) EXPECT() *MockILoanScheduleProviderMockRecorder {
	return m.recorder
}

// FetchLoanScheduleFromVendor mocks base method.
func (m *MockILoanScheduleProvider) FetchLoanScheduleFromVendor(arg0 context.Context, arg1 *providers.FetchLoanScheduleRequest) (*providers.FetchLoanScheduleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchLoanScheduleFromVendor", arg0, arg1)
	ret0, _ := ret[0].(*providers.FetchLoanScheduleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchLoanScheduleFromVendor indicates an expected call of FetchLoanScheduleFromVendor.
func (mr *MockILoanScheduleProviderMockRecorder) FetchLoanScheduleFromVendor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchLoanScheduleFromVendor", reflect.TypeOf((*MockILoanScheduleProvider)(nil).FetchLoanScheduleFromVendor), arg0, arg1)
}

// MockIPaymentAllocator is a mock of IPaymentAllocator interface.
type MockIPaymentAllocator struct {
	ctrl     *gomock.Controller
	recorder *MockIPaymentAllocatorMockRecorder
}

// MockIPaymentAllocatorMockRecorder is the mock recorder for MockIPaymentAllocator.
type MockIPaymentAllocatorMockRecorder struct {
	mock *MockIPaymentAllocator
}

// NewMockIPaymentAllocator creates a new mock instance.
func NewMockIPaymentAllocator(ctrl *gomock.Controller) *MockIPaymentAllocator {
	mock := &MockIPaymentAllocator{ctrl: ctrl}
	mock.recorder = &MockIPaymentAllocatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPaymentAllocator) EXPECT() *MockIPaymentAllocatorMockRecorder {
	return m.recorder
}

// GetPaymentAllocationConfig mocks base method.
func (m *MockIPaymentAllocator) GetPaymentAllocationConfig(arg0 context.Context, arg1 *providers.GetPaymentAllocationConfigRequest) (*providers.GetPaymentAllocationConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentAllocationConfig", arg0, arg1)
	ret0, _ := ret[0].(*providers.GetPaymentAllocationConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentAllocationConfig indicates an expected call of GetPaymentAllocationConfig.
func (mr *MockIPaymentAllocatorMockRecorder) GetPaymentAllocationConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentAllocationConfig", reflect.TypeOf((*MockIPaymentAllocator)(nil).GetPaymentAllocationConfig), arg0, arg1)
}

// MockILoanCollectionProvider is a mock of ILoanCollectionProvider interface.
type MockILoanCollectionProvider struct {
	ctrl     *gomock.Controller
	recorder *MockILoanCollectionProviderMockRecorder
}

// MockILoanCollectionProviderMockRecorder is the mock recorder for MockILoanCollectionProvider.
type MockILoanCollectionProviderMockRecorder struct {
	mock *MockILoanCollectionProvider
}

// NewMockILoanCollectionProvider creates a new mock instance.
func NewMockILoanCollectionProvider(ctrl *gomock.Controller) *MockILoanCollectionProvider {
	mock := &MockILoanCollectionProvider{ctrl: ctrl}
	mock.recorder = &MockILoanCollectionProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockILoanCollectionProvider) EXPECT() *MockILoanCollectionProviderMockRecorder {
	return m.recorder
}

// CollectionExecutionBreakup mocks base method.
func (m *MockILoanCollectionProvider) CollectionExecutionBreakup(arg0 context.Context, arg1 *providers.CollectionExecutionBreakupRequest) (*providers.CollectionExecutionBreakupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CollectionExecutionBreakup", arg0, arg1)
	ret0, _ := ret[0].(*providers.CollectionExecutionBreakupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CollectionExecutionBreakup indicates an expected call of CollectionExecutionBreakup.
func (mr *MockILoanCollectionProviderMockRecorder) CollectionExecutionBreakup(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CollectionExecutionBreakup", reflect.TypeOf((*MockILoanCollectionProvider)(nil).CollectionExecutionBreakup), arg0, arg1)
}

// MockIOutstandingLoanAmountGetter is a mock of IOutstandingLoanAmountGetter interface.
type MockIOutstandingLoanAmountGetter struct {
	ctrl     *gomock.Controller
	recorder *MockIOutstandingLoanAmountGetterMockRecorder
}

// MockIOutstandingLoanAmountGetterMockRecorder is the mock recorder for MockIOutstandingLoanAmountGetter.
type MockIOutstandingLoanAmountGetterMockRecorder struct {
	mock *MockIOutstandingLoanAmountGetter
}

// NewMockIOutstandingLoanAmountGetter creates a new mock instance.
func NewMockIOutstandingLoanAmountGetter(ctrl *gomock.Controller) *MockIOutstandingLoanAmountGetter {
	mock := &MockIOutstandingLoanAmountGetter{ctrl: ctrl}
	mock.recorder = &MockIOutstandingLoanAmountGetterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIOutstandingLoanAmountGetter) EXPECT() *MockIOutstandingLoanAmountGetterMockRecorder {
	return m.recorder
}

// GetOutstandingLoanAmount mocks base method.
func (m *MockIOutstandingLoanAmountGetter) GetOutstandingLoanAmount(ctx context.Context, req *providers.GetOutstandingLoanAmountRequest) (*providers.GetOutstandingLoanAmountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOutstandingLoanAmount", ctx, req)
	ret0, _ := ret[0].(*providers.GetOutstandingLoanAmountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOutstandingLoanAmount indicates an expected call of GetOutstandingLoanAmount.
func (mr *MockIOutstandingLoanAmountGetterMockRecorder) GetOutstandingLoanAmount(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOutstandingLoanAmount", reflect.TypeOf((*MockIOutstandingLoanAmountGetter)(nil).GetOutstandingLoanAmount), ctx, req)
}

// MockIUserDataProvider is a mock of IUserDataProvider interface.
type MockIUserDataProvider struct {
	ctrl     *gomock.Controller
	recorder *MockIUserDataProviderMockRecorder
}

// MockIUserDataProviderMockRecorder is the mock recorder for MockIUserDataProvider.
type MockIUserDataProviderMockRecorder struct {
	mock *MockIUserDataProvider
}

// NewMockIUserDataProvider creates a new mock instance.
func NewMockIUserDataProvider(ctrl *gomock.Controller) *MockIUserDataProvider {
	mock := &MockIUserDataProvider{ctrl: ctrl}
	mock.recorder = &MockIUserDataProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIUserDataProvider) EXPECT() *MockIUserDataProviderMockRecorder {
	return m.recorder
}

// FetchUserKycDetails mocks base method.
func (m *MockIUserDataProvider) FetchUserKycDetails(arg0 context.Context, arg1 *providers.FetchUserKycDetailsRequest) (*providers.FetchUserKycDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchUserKycDetails", arg0, arg1)
	ret0, _ := ret[0].(*providers.FetchUserKycDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchUserKycDetails indicates an expected call of FetchUserKycDetails.
func (mr *MockIUserDataProviderMockRecorder) FetchUserKycDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchUserKycDetails", reflect.TypeOf((*MockIUserDataProvider)(nil).FetchUserKycDetails), arg0, arg1)
}

// MockIChargesDataProvider is a mock of IChargesDataProvider interface.
type MockIChargesDataProvider struct {
	ctrl     *gomock.Controller
	recorder *MockIChargesDataProviderMockRecorder
}

// MockIChargesDataProviderMockRecorder is the mock recorder for MockIChargesDataProvider.
type MockIChargesDataProviderMockRecorder struct {
	mock *MockIChargesDataProvider
}

// NewMockIChargesDataProvider creates a new mock instance.
func NewMockIChargesDataProvider(ctrl *gomock.Controller) *MockIChargesDataProvider {
	mock := &MockIChargesDataProvider{ctrl: ctrl}
	mock.recorder = &MockIChargesDataProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIChargesDataProvider) EXPECT() *MockIChargesDataProviderMockRecorder {
	return m.recorder
}

// ChargesEvaluator mocks base method.
func (m *MockIChargesDataProvider) ChargesEvaluator(ctx context.Context, request *providers.ChargesEvaluatorRequest) (*providers.ChargesEvaluatorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChargesEvaluator", ctx, request)
	ret0, _ := ret[0].(*providers.ChargesEvaluatorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChargesEvaluator indicates an expected call of ChargesEvaluator.
func (mr *MockIChargesDataProviderMockRecorder) ChargesEvaluator(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChargesEvaluator", reflect.TypeOf((*MockIChargesDataProvider)(nil).ChargesEvaluator), ctx, request)
}

// MockITransactionDataProvider is a mock of ITransactionDataProvider interface.
type MockITransactionDataProvider struct {
	ctrl     *gomock.Controller
	recorder *MockITransactionDataProviderMockRecorder
}

// MockITransactionDataProviderMockRecorder is the mock recorder for MockITransactionDataProvider.
type MockITransactionDataProviderMockRecorder struct {
	mock *MockITransactionDataProvider
}

// NewMockITransactionDataProvider creates a new mock instance.
func NewMockITransactionDataProvider(ctrl *gomock.Controller) *MockITransactionDataProvider {
	mock := &MockITransactionDataProvider{ctrl: ctrl}
	mock.recorder = &MockITransactionDataProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockITransactionDataProvider) EXPECT() *MockITransactionDataProviderMockRecorder {
	return m.recorder
}

// GetTransactionData mocks base method.
func (m *MockITransactionDataProvider) GetTransactionData(ctx context.Context, request *providers.GetTransactionDataRequest) (*providers.GetTransactionDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionData", ctx, request)
	ret0, _ := ret[0].(*providers.GetTransactionDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionData indicates an expected call of GetTransactionData.
func (mr *MockITransactionDataProviderMockRecorder) GetTransactionData(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionData", reflect.TypeOf((*MockITransactionDataProvider)(nil).GetTransactionData), ctx, request)
}

// MockILoanForeClosureDetailsProvider is a mock of ILoanForeClosureDetailsProvider interface.
type MockILoanForeClosureDetailsProvider struct {
	ctrl     *gomock.Controller
	recorder *MockILoanForeClosureDetailsProviderMockRecorder
}

// MockILoanForeClosureDetailsProviderMockRecorder is the mock recorder for MockILoanForeClosureDetailsProvider.
type MockILoanForeClosureDetailsProviderMockRecorder struct {
	mock *MockILoanForeClosureDetailsProvider
}

// NewMockILoanForeClosureDetailsProvider creates a new mock instance.
func NewMockILoanForeClosureDetailsProvider(ctrl *gomock.Controller) *MockILoanForeClosureDetailsProvider {
	mock := &MockILoanForeClosureDetailsProvider{ctrl: ctrl}
	mock.recorder = &MockILoanForeClosureDetailsProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockILoanForeClosureDetailsProvider) EXPECT() *MockILoanForeClosureDetailsProviderMockRecorder {
	return m.recorder
}

// FetchLoanPreClosureDetailsFromVendor mocks base method.
func (m *MockILoanForeClosureDetailsProvider) FetchLoanPreClosureDetailsFromVendor(ctx context.Context, loanAccount *preapprovedloan.LoanAccount) (*providers.FetchLoanPreClosureDetailsFromVendorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchLoanPreClosureDetailsFromVendor", ctx, loanAccount)
	ret0, _ := ret[0].(*providers.FetchLoanPreClosureDetailsFromVendorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchLoanPreClosureDetailsFromVendor indicates an expected call of FetchLoanPreClosureDetailsFromVendor.
func (mr *MockILoanForeClosureDetailsProviderMockRecorder) FetchLoanPreClosureDetailsFromVendor(ctx, loanAccount interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchLoanPreClosureDetailsFromVendor", reflect.TypeOf((*MockILoanForeClosureDetailsProvider)(nil).FetchLoanPreClosureDetailsFromVendor), ctx, loanAccount)
}

// MockILoanCancellationDetailsProvider is a mock of ILoanCancellationDetailsProvider interface.
type MockILoanCancellationDetailsProvider struct {
	ctrl     *gomock.Controller
	recorder *MockILoanCancellationDetailsProviderMockRecorder
}

// MockILoanCancellationDetailsProviderMockRecorder is the mock recorder for MockILoanCancellationDetailsProvider.
type MockILoanCancellationDetailsProviderMockRecorder struct {
	mock *MockILoanCancellationDetailsProvider
}

// NewMockILoanCancellationDetailsProvider creates a new mock instance.
func NewMockILoanCancellationDetailsProvider(ctrl *gomock.Controller) *MockILoanCancellationDetailsProvider {
	mock := &MockILoanCancellationDetailsProvider{ctrl: ctrl}
	mock.recorder = &MockILoanCancellationDetailsProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockILoanCancellationDetailsProvider) EXPECT() *MockILoanCancellationDetailsProviderMockRecorder {
	return m.recorder
}

// FetchLoanCancellationDetailsFromVendor mocks base method.
func (m *MockILoanCancellationDetailsProvider) FetchLoanCancellationDetailsFromVendor(ctx context.Context, loanAccount *preapprovedloan.LoanAccount) (*providers.FetchLoanCancellationDetailsFromVendorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchLoanCancellationDetailsFromVendor", ctx, loanAccount)
	ret0, _ := ret[0].(*providers.FetchLoanCancellationDetailsFromVendorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchLoanCancellationDetailsFromVendor indicates an expected call of FetchLoanCancellationDetailsFromVendor.
func (mr *MockILoanCancellationDetailsProviderMockRecorder) FetchLoanCancellationDetailsFromVendor(ctx, loanAccount interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchLoanCancellationDetailsFromVendor", reflect.TypeOf((*MockILoanCancellationDetailsProvider)(nil).FetchLoanCancellationDetailsFromVendor), ctx, loanAccount)
}

// MockIPostPaymentEmiLevelAllocationProvider is a mock of IPostPaymentEmiLevelAllocationProvider interface.
type MockIPostPaymentEmiLevelAllocationProvider struct {
	ctrl     *gomock.Controller
	recorder *MockIPostPaymentEmiLevelAllocationProviderMockRecorder
}

// MockIPostPaymentEmiLevelAllocationProviderMockRecorder is the mock recorder for MockIPostPaymentEmiLevelAllocationProvider.
type MockIPostPaymentEmiLevelAllocationProviderMockRecorder struct {
	mock *MockIPostPaymentEmiLevelAllocationProvider
}

// NewMockIPostPaymentEmiLevelAllocationProvider creates a new mock instance.
func NewMockIPostPaymentEmiLevelAllocationProvider(ctrl *gomock.Controller) *MockIPostPaymentEmiLevelAllocationProvider {
	mock := &MockIPostPaymentEmiLevelAllocationProvider{ctrl: ctrl}
	mock.recorder = &MockIPostPaymentEmiLevelAllocationProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPostPaymentEmiLevelAllocationProvider) EXPECT() *MockIPostPaymentEmiLevelAllocationProviderMockRecorder {
	return m.recorder
}

// GetPostPaymentEmiLevelAllocation mocks base method.
func (m *MockIPostPaymentEmiLevelAllocationProvider) GetPostPaymentEmiLevelAllocation(ctx context.Context, request *providers.GetPostPaymentEmiLevelAllocationRequest) (*providers.GetPostPaymentEmiLevelAllocationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPostPaymentEmiLevelAllocation", ctx, request)
	ret0, _ := ret[0].(*providers.GetPostPaymentEmiLevelAllocationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostPaymentEmiLevelAllocation indicates an expected call of GetPostPaymentEmiLevelAllocation.
func (mr *MockIPostPaymentEmiLevelAllocationProviderMockRecorder) GetPostPaymentEmiLevelAllocation(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostPaymentEmiLevelAllocation", reflect.TypeOf((*MockIPostPaymentEmiLevelAllocationProvider)(nil).GetPostPaymentEmiLevelAllocation), ctx, request)
}

// MockIAutoPayExecutionAmountProvider is a mock of IAutoPayExecutionAmountProvider interface.
type MockIAutoPayExecutionAmountProvider struct {
	ctrl     *gomock.Controller
	recorder *MockIAutoPayExecutionAmountProviderMockRecorder
}

// MockIAutoPayExecutionAmountProviderMockRecorder is the mock recorder for MockIAutoPayExecutionAmountProvider.
type MockIAutoPayExecutionAmountProviderMockRecorder struct {
	mock *MockIAutoPayExecutionAmountProvider
}

// NewMockIAutoPayExecutionAmountProvider creates a new mock instance.
func NewMockIAutoPayExecutionAmountProvider(ctrl *gomock.Controller) *MockIAutoPayExecutionAmountProvider {
	mock := &MockIAutoPayExecutionAmountProvider{ctrl: ctrl}
	mock.recorder = &MockIAutoPayExecutionAmountProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAutoPayExecutionAmountProvider) EXPECT() *MockIAutoPayExecutionAmountProviderMockRecorder {
	return m.recorder
}

// GetAutoPayExecutionAmount mocks base method.
func (m *MockIAutoPayExecutionAmountProvider) GetAutoPayExecutionAmount(ctx context.Context, request *providers.GetAutoPayExecutionAmountRequest) (*providers.GetAutoPayExecutionAmountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAutoPayExecutionAmount", ctx, request)
	ret0, _ := ret[0].(*providers.GetAutoPayExecutionAmountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAutoPayExecutionAmount indicates an expected call of GetAutoPayExecutionAmount.
func (mr *MockIAutoPayExecutionAmountProviderMockRecorder) GetAutoPayExecutionAmount(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAutoPayExecutionAmount", reflect.TypeOf((*MockIAutoPayExecutionAmountProvider)(nil).GetAutoPayExecutionAmount), ctx, request)
}

// MockIVkycDataProvider is a mock of IVkycDataProvider interface.
type MockIVkycDataProvider struct {
	ctrl     *gomock.Controller
	recorder *MockIVkycDataProviderMockRecorder
}

// MockIVkycDataProviderMockRecorder is the mock recorder for MockIVkycDataProvider.
type MockIVkycDataProviderMockRecorder struct {
	mock *MockIVkycDataProvider
}

// NewMockIVkycDataProvider creates a new mock instance.
func NewMockIVkycDataProvider(ctrl *gomock.Controller) *MockIVkycDataProvider {
	mock := &MockIVkycDataProvider{ctrl: ctrl}
	mock.recorder = &MockIVkycDataProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIVkycDataProvider) EXPECT() *MockIVkycDataProviderMockRecorder {
	return m.recorder
}

// GetVkycdata mocks base method.
func (m *MockIVkycDataProvider) GetVkycdata(ctx context.Context, request *providers.GetVkycDataRequest) (*providers.GetVkycDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVkycdata", ctx, request)
	ret0, _ := ret[0].(*providers.GetVkycDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVkycdata indicates an expected call of GetVkycdata.
func (mr *MockIVkycDataProviderMockRecorder) GetVkycdata(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVkycdata", reflect.TypeOf((*MockIVkycDataProvider)(nil).GetVkycdata), ctx, request)
}

// MockIFetchPwaRedirectionData is a mock of IFetchPwaRedirectionData interface.
type MockIFetchPwaRedirectionData struct {
	ctrl     *gomock.Controller
	recorder *MockIFetchPwaRedirectionDataMockRecorder
}

// MockIFetchPwaRedirectionDataMockRecorder is the mock recorder for MockIFetchPwaRedirectionData.
type MockIFetchPwaRedirectionDataMockRecorder struct {
	mock *MockIFetchPwaRedirectionData
}

// NewMockIFetchPwaRedirectionData creates a new mock instance.
func NewMockIFetchPwaRedirectionData(ctrl *gomock.Controller) *MockIFetchPwaRedirectionData {
	mock := &MockIFetchPwaRedirectionData{ctrl: ctrl}
	mock.recorder = &MockIFetchPwaRedirectionDataMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIFetchPwaRedirectionData) EXPECT() *MockIFetchPwaRedirectionDataMockRecorder {
	return m.recorder
}

// GetPwaJourneyUrl mocks base method.
func (m *MockIFetchPwaRedirectionData) GetPwaJourneyUrl(ctx context.Context, request *providers.GetPwaJourneyUrlRequest) (*providers.GetPwaJourneyUrlResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPwaJourneyUrl", ctx, request)
	ret0, _ := ret[0].(*providers.GetPwaJourneyUrlResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPwaJourneyUrl indicates an expected call of GetPwaJourneyUrl.
func (mr *MockIFetchPwaRedirectionDataMockRecorder) GetPwaJourneyUrl(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPwaJourneyUrl", reflect.TypeOf((*MockIFetchPwaRedirectionData)(nil).GetPwaJourneyUrl), ctx, request)
}

// MockIFetchPrepayPaymentLink is a mock of IFetchPrepayPaymentLink interface.
type MockIFetchPrepayPaymentLink struct {
	ctrl     *gomock.Controller
	recorder *MockIFetchPrepayPaymentLinkMockRecorder
}

// MockIFetchPrepayPaymentLinkMockRecorder is the mock recorder for MockIFetchPrepayPaymentLink.
type MockIFetchPrepayPaymentLinkMockRecorder struct {
	mock *MockIFetchPrepayPaymentLink
}

// NewMockIFetchPrepayPaymentLink creates a new mock instance.
func NewMockIFetchPrepayPaymentLink(ctrl *gomock.Controller) *MockIFetchPrepayPaymentLink {
	mock := &MockIFetchPrepayPaymentLink{ctrl: ctrl}
	mock.recorder = &MockIFetchPrepayPaymentLinkMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIFetchPrepayPaymentLink) EXPECT() *MockIFetchPrepayPaymentLinkMockRecorder {
	return m.recorder
}

// GetPaymentLink mocks base method.
func (m *MockIFetchPrepayPaymentLink) GetPaymentLink(arg0 context.Context, arg1 *providers.GetPaymentLinkReq) (*providers.GetPaymentLinkResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentLink", arg0, arg1)
	ret0, _ := ret[0].(*providers.GetPaymentLinkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentLink indicates an expected call of GetPaymentLink.
func (mr *MockIFetchPrepayPaymentLinkMockRecorder) GetPaymentLink(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentLink", reflect.TypeOf((*MockIFetchPrepayPaymentLink)(nil).GetPaymentLink), arg0, arg1)
}
