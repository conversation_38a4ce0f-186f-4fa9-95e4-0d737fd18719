package stockguardian

import (
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"

	"go.temporal.io/sdk/workflow"
)

type VendorBre struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewVendorBre() *VendorBre {
	return &VendorBre{}
}

var _ stages.IStage = &VendorBre{}

func (ca *VendorBre) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	actReq := providers.GetActivityRequest(req, req.LoanProgram)
	actRes := &palActivityPb.PalActivityResponse{}
	activityErr := activityPkg.Execute(ctx, palNs.SgVendorBre, actRes, actReq)
	if providers.IsActivityError(ctx, actRes.GetLoanStep(), actRes.GetLseFieldMasks(), res, activityErr, false) {
		return res, activityErr
	}

	res.LoanStep = actRes.GetLoanStep()
	res.LseFieldMasks = actRes.GetLseFieldMasks()

	ver := workflow.GetVersion(ctx, "sg-revised-offer-impl", workflow.DefaultVersion, 1)
	if ver == 1 && res.GetLoanStep().GetSubStatus() == palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_DETAILS_REVISED {
		revisedErr := providers.WaitForRevisedOfferConfirmationSignal(ctx)
		if revisedErr != nil {
			if epifitemporal.HasSignalReceivedTimedOut(revisedErr) {
				res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
			}
			return res, revisedErr
		}
		res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	}

	return res, nil
}

func (ca *VendorBre) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE
}

func (ca *VendorBre) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageVendorBre
}
