package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"

	orderPb "github.com/epifi/gamma/api/order"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/preapprovedloan/dao"
	rpcHelper "github.com/epifi/gamma/preapprovedloan/helper"
)

var _ LoanOrderProcessor = &PalDisbursementProcessor{}

type PalDisbursementProcessor struct {
	RpcHelper       *rpcHelper.RpcHelper
	LoanAccountDao  dao.LoanAccountsDao
	LoanActivityDao dao.LoanActivityDao
	LoanRequestsDao dao.LoanRequestsDao
}

const (
	liquiloansActorId = "actor-liquiloans"
)

func (p *PalDisbursementProcessor) Handle(ctx context.Context, req *orderPb.OrderUpdate) error {
	logger.Info(ctx, "PAL disbursement handler")

	// TODO(harish): move it to LL provider
	if req.GetOrderWithTransactions().GetOrder().GetFromActorId() == liquiloansActorId {
		return p.processLiquiloansDisbursementOrder(ctx, req)
	}
	return nil
}

func (p *PalDisbursementProcessor) processLiquiloansDisbursementOrder(ctx context.Context, req *orderPb.OrderUpdate) error {
	if len(req.GetOrderWithTransactions().GetTransactions()) < 1 {
		return fmt.Errorf("no transactions exist in the order update packet, %w", epifierrors.ErrPermanent)
	}
	txn := req.GetOrderWithTransactions().GetTransactions()[0]
	ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_LIQUILOANS_PL)

	loanAccounts, err := p.LoanAccountDao.GetByActorIdAndVendor(ctx, req.GetOrderWithTransactions().GetOrder().GetToActorId(), palPb.Vendor_LIQUILOANS)
	if err != nil || len(loanAccounts) < 1 {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return fmt.Errorf("no loan account exists yet, %v, %w", err, epifierrors.ErrTransient)
		}
		return fmt.Errorf("failed to fetch loan account, %v, %w", err, epifierrors.ErrTransient)
	}

	loanAccount := loanAccounts[0]
	loanActivities, err := p.LoanActivityDao.GetByAccountIdAndTypesAndCount(ctx, loanAccount.GetId(), []palPb.LoanActivityType{palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_LOAN_DISBURSEMENT}, 1)
	if err != nil || len(loanActivities) < 1 {
		return fmt.Errorf("failed to fetch loan activity, %v, %w", err, epifierrors.ErrTransient)
	}

	loanActivity := loanActivities[0]
	loanActivity.ReferenceId = txn.GetId()
	if loanActivity.GetDetails() == nil {
		loanActivity.Details = &palPb.LoanActivityDetails{
			Amount: req.GetOrderWithTransactions().GetOrder().GetAmount(),
		}
	}
	loanActivity.GetDetails().TransactionId = txn.GetId()

	err = p.LoanActivityDao.Update(ctx, loanActivity, []palPb.LoanActivityFieldMask{
		palPb.LoanActivityFieldMask_LOAN_ACTIVITY_FIELD_MASK_REFERENCE_ID,
		palPb.LoanActivityFieldMask_LOAN_ACTIVITY_FIELD_MASK_DETAILS})
	if err != nil {
		return fmt.Errorf("failed to update loan activity, %v, %w", err, epifierrors.ErrTransient)
	}

	return nil
}
