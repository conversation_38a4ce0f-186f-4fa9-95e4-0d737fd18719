package service

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	searchPb "github.com/epifi/gamma/api/search"
	"github.com/epifi/gamma/api/search/preview_page"
	"github.com/epifi/be-common/pkg/logger"
	preview_page_dao "github.com/epifi/gamma/search/dao/preview_page"
	"github.com/epifi/gamma/search/preview_page/ui"
)

func (ss *SearchServer) GetPreviewPage(ctx context.Context, request *searchPb.GetPreviewPageRequest) (*searchPb.GetPreviewPageResponse, error) {
	var (
		componentsResp []*preview_page.Component
		err            error
	)
	isConfigBasedQuery := ss.isConfigBasedQuery(request.GetQuery())
	if isConfigBasedQuery {
		return &searchPb.GetPreviewPageResponse{
			Status: rpc.StatusOk(),
		}, nil
	}
	actorId := request.GetActorId()
	req := &preview_page_dao.BuildPreviewPageComponentsRequest{
		UserActorId: actorId,
		Query:       request.GetQuery(),
		IsInternal:  ss.isInternalUser(ctx, actorId),
	}
	componentsResp, err = ss.PreviewPageHandler.BuildPreviewPageComponents(ctx, req)
	if err != nil {
		logger.Error(ctx, "error in getting preview page", zap.Error(err))
		return &searchPb.GetPreviewPageResponse{
			Status: rpc.StatusInternal(),
		}, err
	}
	return &searchPb.GetPreviewPageResponse{
		Status: rpc.StatusOk(),
		PreviewPageLayout: &preview_page.Layout{
			Components: componentsResp,
			SubTitle:   fmt.Sprintf(ui.PreviewPageSubtitle, ui.ColorSlate, ui.ColorWhite, request.GetQuery()),
		},
	}, nil
}
