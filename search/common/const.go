package common

// Regular Expression constants.
const (
	IfscCodeRegex      = "IFSC CODE"
	BankAccountNoRegex = "BANK ACCOUNT NUMBER"
	UpiIdRegex         = "UPI ID"
	AmountRegex        = "AMOUNT"
	NumberRegex        = "NUMBER"
)

// Regular Expression Map.
var RegexMap = map[string]string{
	IfscCodeRegex:      `[A-Z]{4}0[A-Z0-9]{6}`,
	BankAccountNoRegex: `[0-9]{9,18}`,
	UpiIdRegex:         `[a-zA-Z0-9.\-_]{2,49}@[a-zA-Z._]{2,49}`,
	AmountRegex:        `[₹]?[0-9]+[.][0-9]+`,
	NumberRegex:        `[0-9]+`,
}

// Strings that will be replaced when there is a regex map.
var RegexReplaceString = map[string]string{
	IfscCodeRegex:      "IFSC CODE",
	BankAccountNoRegex: "BANK ACCOUNT NUMBER",
	UpiIdRegex:         "UPI ID",
	AmountRegex:        "AMOUNT",
	NumberRegex:        "NUMBER",
}

// Bank Icon S3 URLs
const (
	FiIcon      = "https://epifi-icons.pointz.in/quick-link-icons/Fi+Federal+Bank.svg"
	JupiterIcon = "https://epifi-icons.pointz.in/quick-link-icons/Jupiter+Federal+Bank.svg"
)

type BankMeta struct {
	bankName string
	bankIcon string
}

var bankCodeToBankMetaMap = map[string]BankMeta{
	"SBIN": {
		"State Bank of India",
		"https://epifi-icons.pointz.in/quick-link-icons/SBI+Bank.svg",
	},
	"UTIB": {
		"Axis Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/Axis+Bank.svg",
	},
	"BARB": {
		"Bank of Baroda",
		"https://epifi-icons.pointz.in/quick-link-icons/Bank+of+Baroda.svg",
	},
	"BKID": {
		"Bank of India",
		"https://epifi-icons.pointz.in/quick-link-icons/Bank+of+India.svg",
	},
	"INDB": {
		"IndusInd Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/IndusInd+Bank.svg",
	},
	"PUNB": {
		"Punjab National Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/Punjab+National+Bank.svg",
	},
	"KKBK": {
		"Kotak Mahindra Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/Kotak+Mahindra+Bank.svg",
	},
	"ICIC": {
		"ICICI Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/ICICI+Bank.svg",
	},
	"YESB": {
		"Yes Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/Yes+Bank.svg",
	},
	"HDFC": {
		"HDFC Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/HDFC+Bank.svg",
	},
	"HSBC": {
		"HSBC Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/HSBC+Bank.svg",
	},
	"IBKL": {
		"IDBI Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/IDBI+Bank.svg",
	},
	"IDFB": {
		"IDFC Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/IDFC+Bank.svg",
	},
	"SCBL": {
		"Standard Chartered Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/Standard+Chartered+Bank.svg",
	},
	"UBIN": {
		"Union Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/Union+Bank.svg",
	},
	"CNRB": {
		"Canara Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/Canara+Bank.svg",
	},
	"BDBL": {
		"Bandhan Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/Bandhan+Bank.svg",
	},
	"FDRL": {
		"Federal Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/Federal+Bank.svg",
	},
	"PSIB": {
		"Punjab and Sind Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/Punjab+%26+Sind+Bank.svg",
	},
	"CITI": {
		"Citi Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/Citi+Bank.svg",
	},
	"UCBA": {
		"UCO Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/UCO+Bank.svg",
	},
	"RATN": {
		"RBL Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/RBL+Bank.svg",
	},
	"CBIN": {
		"Central Bank of India",
		"https://epifi-icons.pointz.in/quick-link-icons/Central+Bank+of+India.svg",
	},
	"IDIB": {
		"Indian Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/Indian+Bank.svg",
	},
	"IOBA": {
		"Indian Overseas Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/Indian+Overseas+Bank.svg",
	},
	"KARB": {
		"Karnataka Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/Karnataka+Bank.svg",
	},
	"AUBL": {
		"AU Small Finance Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/AU+Small+Finance+Bank.svg",
	},
	"KVBL": {
		"Karur Vyasya Bank",
		"https://epifi-icons.pointz.in/quick-link-icons/Karur+Vysya+Bank.svg",
	},
}

func (b *BankMeta) GetBankName() string {
	if b == nil {
		return ""
	}
	return b.bankName
}

func (b *BankMeta) GetBankIcon() string {
	if b == nil {
		return ""
	}
	return b.bankIcon
}
