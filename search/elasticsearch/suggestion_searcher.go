package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	dao "github.com/epifi/gamma/search/dao/index"
	"github.com/epifi/gamma/search/indexer"
	"github.com/epifi/gamma/search/queryBuilder"
)

// type to distinguish between same ES type for wire initialization
type EsClientSuggestion ES

// type to distinguish between same index type for wire initialization
type SuggestionIndexName string
type SuggestionPrefixIndexName string

type SuggestionIndexSearcher struct {
	prefixIndex SuggestionPrefixIndexName
	es          ES
	qBuilder    queryBuilder.QueryBuilder
}

func NewSuggestionIndexSearcher(index SuggestionIndexName, prefixIndex SuggestionPrefixIndexName, esClient EsClientSuggestion) (*SuggestionIndexSearcher, error) {
	qBuilder := queryBuilder.NewQueryBuilder()
	return &SuggestionIndexSearcher{
		prefixIndex: prefixIndex,
		es:          esClient,
		qBuilder:    qBuilder,
	}, nil
}

//nolint:dupl
func (suggestionSearcher *SuggestionIndexSearcher) ExecuteQuery(ctx context.Context, indexName, input string) (*elastic.SearchResult, error) {
	respBytes, _, err := suggestionSearcher.es.Query(ctx, input, indexName)
	if err != nil {
		return nil, fmt.Errorf("error in querying: %w", err)
	}
	searchResult := elastic.SearchResult{}
	err = json.Unmarshal(respBytes, &searchResult)
	if err != nil {
		return nil, fmt.Errorf("error in resp json unmarshal: %w", err)
	}
	if searchResult.Error != nil {
		var rootCause []string
		if searchResult.Error.RootCause != nil {
			for idx := range searchResult.Error.RootCause {
				rootCause = append(rootCause, searchResult.Error.RootCause[idx].Reason)
			}
		}
		zapFields := []zap.Field{zap.String("es-query-error", searchResult.Error.Reason)}
		if rootCause != nil {
			zapFields = append(zapFields, zap.Strings("rootcause", rootCause))
		}
		logger.Error(ctx, "error in es response", zapFields...)
		return nil, fmt.Errorf("error in es response: %s", searchResult.Error.Reason)
	}
	return &searchResult, nil
}

func (suggestionSearcher *SuggestionIndexSearcher) QueryBuilder(ctx context.Context, keyword string) (string, error) {
	resp, err := suggestionSearcher.qBuilder.SuggestionPrefixMatchQuery(ctx, strings.ToLower(keyword))
	if err != nil {
		return "", err
	}
	return resp, nil
}

// TODO(sukeesh): Find a better way to merge QueryBuilder and PersonalizedQueryBuilder functions
func (suggestionSearcher *SuggestionIndexSearcher) PersonalizedQueryBuilder(ctx context.Context, actorID, keyword string) (string, error) {
	resp, err := suggestionSearcher.qBuilder.PersonalizedSuggestionPrefixMatchQuery(ctx, actorID, strings.ToLower(keyword))
	if err != nil {
		return "", err
	}
	return resp, nil
}

// nolint:funlen
func (suggestionSearcher *SuggestionIndexSearcher) GetSuggestions(ctx context.Context, actorID, ques string) ([]string, error) {
	var suggestionResults *elastic.SearchResult
	var suggestionError error

	query, err := suggestionSearcher.QueryBuilder(ctx, ques)
	if err != nil {
		return nil, errors.Wrap(err, "error while building query")
	}
	suggestionResults, err = suggestionSearcher.ExecuteQuery(ctx, indexer.INDEX_SUGGESTION_PREFIX, query)
	if err != nil {
		return nil, errors.Wrap(err, "error while executing the query")
	}
	if suggestionResults == nil || suggestionResults.TotalHits() == 0 {
		return nil, nil
	}
	if suggestionError != nil {
		logger.Error(ctx, "error while querying  suggestion", zap.Error(suggestionError))
	}
	return rankAndMergeResults(suggestionResults)
}

func rankAndMergeResults(suggestionResults *elastic.SearchResult) ([]string, error) {
	var suggestionResp []string
	var suggestionDetail dao.Suggestion
	for _, suggestionResult := range suggestionResults.Hits.Hits {
		if err := json.Unmarshal(suggestionResult.Source, &suggestionDetail); err != nil {
			return nil, errors.Wrap(err, "error while parsing suggestionDetail")
		} else {
			suggestionResp = append(suggestionResp, suggestionDetail.GetQuery())
		}
	}
	return suggestionResp, nil
}
