//nolint:protogetter
package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"

	searchPb "github.com/epifi/gamma/api/search"
	"github.com/epifi/be-common/pkg/logger"
	dao_query "github.com/epifi/gamma/search/dao/query"
	"github.com/epifi/gamma/search/queryBuilder"
)

// type to distinguish between same ES type for wire initialization
type ESClientCompanyName ES

// type to distinguish between same index type for wire initialization
type CompanyNameIndexName string

type CompNameSuggSource struct {
	CompanyName     string   `json:"company_name"`
	VendorId        string   `json:"vendor_id"`
	IsEpfRegistered bool     `json:"is_epf_registered"`
	Suggest         []string `json:"suggest"`
	Score           float64  `json:"score"`
	Text            string   `json:"text"`
}

/*
BanksIndexSearcherImpl struct implements Repo interface : represents bank autocomplete index
fields:
-- es ES interface
-- index string -- index, this repo will talk to
*/
type CompanyNameIndexSearcherImpl struct {
	index             CompanyNameIndexName
	es                ES
	qBuilder          queryBuilder.QueryBuilder
	autoComplFallBack []func(ctx context.Context, queryParams *dao_query.CompanyNameSuggestQueryParams) ([]*CompNameSuggSource, error)
}

func NewCompanyNameIndexSearcherImpl(index CompanyNameIndexName, esClient ESClientCompanyName) (*CompanyNameIndexSearcherImpl, error) {
	qBuilder := queryBuilder.NewQueryBuilder()
	searcher := &CompanyNameIndexSearcherImpl{
		index:    index,
		es:       esClient,
		qBuilder: qBuilder,
	}
	searcher.autoComplFallBack = []func(ctx context.Context, queryParams *dao_query.CompanyNameSuggestQueryParams) ([]*CompNameSuggSource, error){
		searcher.getSuggestResp,
		searcher.getSearchResp,
	}
	return searcher, nil
}

// nolint:dupl
// executes input query on elasticsearch.
// TODO: extract this function to common interface
func (searcher *CompanyNameIndexSearcherImpl) ExecuteQuery(ctx context.Context, input string) (*elastic.SearchResult, error) {
	respBytes, _, err := searcher.es.Query(ctx, input, string(searcher.index))
	if err != nil {
		return nil, fmt.Errorf("error in querying: %w", err)
	}
	var esResp elastic.SearchResult
	err = json.Unmarshal(respBytes, &esResp)
	if err != nil {
		return nil, fmt.Errorf("error in resp json unmarshal: %w", err)
	}
	if esResp.Status == 400 || esResp.Error != nil {
		return nil, fmt.Errorf("error in post query to es: %s", esResp.Error.Reason)
	}
	return &esResp, nil
}

func (searcher *CompanyNameIndexSearcherImpl) ConnectionChk(ctx context.Context) error {
	return searcher.es.ConnectionChk(ctx)
}

// CompanyNameAutoCompl function applies on CompanyNameIndexSearcherImpl, which contains a autocomplete index
// This function builds autocomplete query from template, and runs the query on es
func (searcher *CompanyNameIndexSearcherImpl) CompanyNameAutoCompl(ctx context.Context,
	queryParams *dao_query.CompanyNameSuggestQueryParams) (*searchPb.CompanyNameAutocompleteResult, error) {
	var autoCompleteResult *searchPb.CompanyNameAutocompleteResult
	var err error
	autoCompleteResult = searcher.buildAutoCompResult(ctx, queryParams)
	if len(autoCompleteResult.Suggestions) == 0 {
		err = fmt.Errorf("no suggs found")
	}
	return autoCompleteResult, err
}

func (searcher *CompanyNameIndexSearcherImpl) buildAutoCompResult(ctx context.Context, queryParams *dao_query.CompanyNameSuggestQueryParams) *searchPb.CompanyNameAutocompleteResult {
	acResult := searchPb.CompanyNameAutocompleteResult{
		Suggestions: []*searchPb.CompanyNameSuggestion{},
	}
	companyNames := searcher.runFallBackFuncs(ctx, queryParams)
	acResult.Suggestions = companyNames
	return &acResult
}

func (searcher *CompanyNameIndexSearcherImpl) runFallBackFuncs(ctx context.Context, queryParams *dao_query.CompanyNameSuggestQueryParams) []*searchPb.CompanyNameSuggestion {
	var allSugg []*CompNameSuggSource
	for idx := range searcher.autoComplFallBack {
		if len(allSugg) >= queryParams.Size {
			break
		}
		currFunc := searcher.autoComplFallBack[idx]
		logger.Debug(ctx, "length is < rqd size. falling to next function", zap.Int("curr-len", len(allSugg)), zap.Int("func-idx", idx))
		currSuggs, err := currFunc(ctx, queryParams)
		if err != nil {
			logger.Error(ctx, "error in getting suggestions", zap.Error(err))
			continue
		}
		allSugg = append(allSugg, currSuggs...)
	}
	return dedupeByCompanyName(ctx, allSugg)
}

func (searcher *CompanyNameIndexSearcherImpl) getSuggestResp(ctx context.Context, queryParams *dao_query.CompanyNameSuggestQueryParams) ([]*CompNameSuggSource, error) {
	autocompleteQuery, err := searcher.qBuilder.CompanyNameAutoCompQuery(ctx, queryParams)
	if err != nil {
		return nil, fmt.Errorf("error in templating autocomplete query: %w", err)
	}
	startTime := time.Now()
	esResp, err := searcher.ExecuteQuery(ctx, autocompleteQuery)
	EsMetricsRecorder.RecordCompanyNameSuggestQuery(startTime)
	if err != nil {
		return nil, fmt.Errorf("error in querying elasticsaerch: %w", err)
	}
	var allSugg []elastic.SearchSuggestionOption
	if companyNameSugg, ok := esResp.Suggest["company-name-suggest"]; ok {
		allSugg = append(allSugg, companyNameSugg[0].Options...)
	}
	if len(allSugg) < queryParams.Size {
		remaining := queryParams.Size - len(allSugg)
		logger.Info(ctx, "company-name-suggest not sufficient. Falling back to company-name-fuzzy-sugg",
			zap.Int("#company-name-sugg", len(allSugg)), zap.Int("#company-name-fuzzy-sugg", remaining))
		if companyNameSuggFuzzy, ok := esResp.Suggest["company-name-fuzzy-suggest"]; ok {
			var high int
			if remaining < len(companyNameSuggFuzzy[0].Options) {
				high = remaining
			} else {
				high = len(companyNameSuggFuzzy[0].Options)
			}
			for i := 0; i < high; i++ {
				allSugg = append(allSugg, companyNameSuggFuzzy[0].Options[i])
			}
		}
	}
	var unmarshalledSugg []*CompNameSuggSource
	for idx := range allSugg {
		curr := allSugg[idx]
		var currSource CompNameSuggSource
		err := json.Unmarshal(curr.Source, &currSource)
		if err != nil {
			logger.Error(ctx, "error in unmarshal json - source", zap.Error(err))
			continue
		}
		currSource.Score = allSugg[idx].Score
		currSource.Text = allSugg[idx].Text
		unmarshalledSugg = append(unmarshalledSugg, &currSource)
	}
	return unmarshalledSugg, nil
}

func (searcher *CompanyNameIndexSearcherImpl) getSearchResp(ctx context.Context, queryParams *dao_query.CompanyNameSuggestQueryParams) ([]*CompNameSuggSource, error) {
	searchQuery, err := searcher.qBuilder.CompanyNameSearchQuery(ctx, queryParams)
	if err != nil {
		return nil, fmt.Errorf("error in building company name search query: %w", err)
	}
	startTime := time.Now()
	esResp, err := searcher.ExecuteQuery(ctx, searchQuery)
	EsMetricsRecorder.RecordCompanyNameSuggestQuery(startTime)
	if err != nil {
		return nil, fmt.Errorf("error in querying es: %w", err)
	}
	if esResp == nil || esResp.Hits == nil || esResp.Hits.Hits == nil || len(esResp.Hits.Hits) == 0 {
		return nil, fmt.Errorf("es resp nil or error: %w", err)
	}
	var searchResp []*CompNameSuggSource
	for idxHits := range esResp.Hits.Hits {
		src := esResp.Hits.Hits[idxHits].Source
		var currSource CompNameSuggSource
		err := json.Unmarshal(src, &currSource)
		if err != nil {
			logger.Error(ctx, "Skipping record, error in unmarshal..", zap.Error(err))
			continue
		}
		searchResp = append(searchResp, &currSource)
	}
	return searchResp, nil
}

// deduplicate by company
func dedupeByCompanyName(ctx context.Context, suggestions []*CompNameSuggSource) []*searchPb.CompanyNameSuggestion {
	var suggs []*searchPb.CompanyNameSuggestion
	unique := map[string]bool{}
	for idx := range suggestions {
		// only keep unique suggestions by name
		if _, set := unique[suggestions[idx].CompanyName]; !set {
			unique[suggestions[idx].CompanyName] = true
		} else {
			logger.Debug(ctx, "repeated suggestion. Skip result", zap.Any("result", suggestions[idx]))
			continue
		}
		tmp := searchPb.CompanyNameSuggestion{
			CompanyName:     suggestions[idx].CompanyName,
			VendorId:        suggestions[idx].VendorId,
			IsEpfRegistered: suggestions[idx].IsEpfRegistered,
			Score:           suggestions[idx].Score,
			MatchedText:     suggestions[idx].Text,
		}
		suggs = append(suggs, &tmp)
	}
	return suggs
}
