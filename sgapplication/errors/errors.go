package errors

import "fmt"

var LoanAmountGreaterThanAllowedAmount = fmt.<PERSON>rro<PERSON>("loan amount is greater than allowed amount")
var LoanAmountLesserThanAllowedAmount = fmt.Errorf("loan amount is lesser than allowed amount")
var TenureNotWithinAllowedRange = fmt.Errorf("tenure is not within the allowed range")
var InterestRateNotWithinAllowedRange = fmt.Errorf("interest rate not within allowed range")
var InvalidStage = fmt.Errorf("invalid stage for given client and product id")
