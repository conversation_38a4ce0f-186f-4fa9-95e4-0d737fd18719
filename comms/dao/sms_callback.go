package dao

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"time"

	commspb "github.com/epifi/gamma/api/comms"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/comms/model"

	"github.com/pkg/errors"
	gormv2 "gorm.io/gorm"
)

type SmsCallbackDao struct {
	db *gormv2.DB
}

func NewSmsCallbackDao(db types.CommsPGDB) *SmsCallbackDao {
	return &SmsCallbackDao{
		db: db,
	}
}

var _ ISmsCallbackDao = &SmsCallbackDao{}

func (s *SmsCallbackDao) Create(ctx context.Context, msg *model.SmsCallback) (*model.SmsCallback, error) {
	defer metric_util.TrackDuration("comms/dao", "SmsCallbackDao", "Create", time.Now())
	if msg.ResponseId == "" || msg.Vendor == commonvgpb.Vendor_VENDOR_UNSPECIFIED || msg.Medium == commspb.Medium_MEDIUM_UNSPECIFIED {
		return nil, fmt.Errorf("mandatory params in create missing")
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	if err := db.Create(msg).Error; err != nil {
		return nil, errors.Wrap(err, "error creating sms callback record")
	}
	return msg, nil
}

func (s *SmsCallbackDao) Get(ctx context.Context, responseId string) (*model.SmsCallback, error) {
	defer metric_util.TrackDuration("comms/dao", "SmsCallbackDao", "Get", time.Now())
	if responseId == "" {
		return nil, fmt.Errorf("response id cannot be nil")
	}
	smsCallback := &model.SmsCallback{}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	if err := db.Where("response_id = ?", responseId).First(smsCallback).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return smsCallback, nil
}

func (s *SmsCallbackDao) Update(ctx context.Context, responseId string, msg *model.SmsCallback) error {
	defer metric_util.TrackDuration("comms/dao", "SmsCallbackDao", "Update", time.Now())
	if responseId == "" {
		return fmt.Errorf("response id cannot be nil for update")
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	if err := db.Model(&model.SmsCallback{}).Where("response_id = ?", responseId).Updates(msg).Error; err != nil {
		return err
	}
	return nil
}
