package consumer

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	queuePb "github.com/epifi/be-common/api/queue"
	actorPb "github.com/epifi/gamma/api/actor"
	pb "github.com/epifi/gamma/api/comms"
	commsDeviceTokenPb "github.com/epifi/gamma/api/comms/device_token"
	"github.com/epifi/gamma/api/frontend/fcm"
	userPb "github.com/epifi/gamma/api/user"
	fcmPb "github.com/epifi/gamma/api/vendorgateway/fcm"
	"github.com/epifi/gamma/comms/dao"
	"github.com/epifi/gamma/comms/helper"
	"github.com/epifi/gamma/comms/metrics"
	"github.com/epifi/gamma/comms/model"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/pkg/errors"
	"go.uber.org/zap"
)

type NotificationServiceGD struct {
	fcmClient            fcmPb.FCMClient
	dao                  dao.CommsStorage
	userClient           userPb.UsersClient
	deviceTokenDao       dao.DeviceTokenStorage
	actorClient          actorPb.ActorClient
	helperSvc            helper.IHelperService
	commsNotificationDao dao.ICommsNotificationDao
}

func NewNotificationServiceGD(fcmClient fcmPb.FCMClient, dao dao.CommsStorage, userClient userPb.UsersClient,
	deviceTokenDao dao.DeviceTokenStorage, actorClient actorPb.ActorClient, helperSvc helper.IHelperService,
	commsNotificationDao dao.ICommsNotificationDao) *NotificationServiceGD {
	return &NotificationServiceGD{
		fcmClient:            fcmClient,
		dao:                  dao,
		userClient:           userClient,
		deviceTokenDao:       deviceTokenDao,
		actorClient:          actorClient,
		helperSvc:            helperSvc,
		commsNotificationDao: commsNotificationDao,
	}
}

var (
	NotificationTransFailure = &pb.ProcessMessageResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
		Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}
	NotificationSuccess = &pb.ProcessMessageResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
		Status: queuePb.MessageConsumptionStatus_SUCCESS}}
	NotificationPermFailure = &pb.ProcessMessageResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
		Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE}}
)

// nolint: funlen, protogetter
func (n *NotificationServiceGD) ProcessNotification(ctx context.Context, req *pb.ProcessNotificationRequest) (*pb.ProcessMessageResponse, error) {
	isLastAttempt := req.GetRequestHeader().GetIsLastAttempt()
	// first check the status of message in database,
	actorId := epificontext.ActorIdFromContext(ctx)
	msg, err := n.helperSvc.GetOrCreateCommsMessage(ctx, req.GetSendMessageRequest(), actorId, req.GetCommsMessageId())
	if err != nil {
		logger.Error(ctx, "failed to create or get comms message", zap.Error(err))
		return NotificationTransFailure, errors.Wrap(err, "failure in create or get comms message")
	}

	// No status API present for vendor message ID, hence we rely only on db states and retry if not marked success
	if msg.Status == pb.MessageState_DELIVERED.String() {
		return NotificationSuccess, nil
	}
	actor, err := n.helperSvc.GetActor(ctx, &pb.ActorIdentifier{Identifier: &pb.ActorIdentifier_UserId{UserId: req.GetUserId()}})
	if err != nil {
		n.helperSvc.UpdateStatusInCommsTable(ctx, req.CommsMessageId, pb.MessageState_TRANSIENT_FAILURE, isLastAttempt)
		return NotificationTransFailure, errors.Wrap(err, fmt.Sprintf("cannot get actor id for user id : %s", req.GetUserId()))
	}
	// Info errors for this since we want backward compatibility
	// check if record already exists in db for notification
	_, err = n.commsNotificationDao.Get(ctx, req.GetCommsMessageId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			err = n.helperSvc.AddCommsNotificationInDB(ctx, req.GetNotificationMessage(), actor.GetId(), req.GetCommsMessageId())
			if err != nil {
				logger.Info(ctx, "error while adding notification entry in db", zap.Error(err))
			}
		} else {
			logger.Info(ctx, "error while fetching notification entry from db", zap.Error(err))
		}
	}

	// do not send notification via FCM for in app notification type since client will consume this by our notification rpc
	if req.GetNotificationMessage().GetNotification().GetNotificationType() == fcm.NotificationType_IN_APP {
		n.helperSvc.UpdateStatusInCommsTable(ctx, req.CommsMessageId, pb.MessageState_DELIVERED, isLastAttempt)
		return NotificationSuccess, nil
	}

	deviceTokenMsg, err := n.deviceTokenDao.GetDeviceToken(ctx, &model.FCMDeviceToken{ActorId: actor.GetId()})
	if err != nil || deviceTokenMsg == nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			n.helperSvc.UpdateStatusInCommsTable(ctx, req.CommsMessageId, pb.MessageState_FAILED, isLastAttempt)
			return NotificationPermFailure, errors.Wrap(err, helper.ErrFcmTokenNotFound.Error())
		}
		n.helperSvc.UpdateStatusInCommsTable(ctx, req.CommsMessageId, pb.MessageState_TRANSIENT_FAILURE, isLastAttempt)
		return NotificationTransFailure, errors.Wrap(err, fmt.Sprintf("cannot get device token for the actor id : %s", actor.GetId()))
	}

	// we'll only send notification if the FCM token is in ACTIVE state
	if deviceTokenMsg.Status != commsDeviceTokenPb.DeviceTokenStatus_ACTIVE.String() {
		n.helperSvc.UpdateStatusInCommsTable(ctx, req.CommsMessageId, pb.MessageState_FAILED, isLastAttempt)
		return NotificationPermFailure, helper.ErrFcmTokenInactive
	}
	shouldBlock, err := n.helperSvc.ShouldBlockForDeviceRegistration(ctx, actor, req.GetClientId(), req.GetNotificationMessage())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return NotificationPermFailure, errors.Wrap(helper.ErrFcmTokenNotFound, "device token not found for user")
		}
		return NotificationTransFailure, errors.Wrap(err, fmt.Sprintf("cannot get device details for the actor id : %s", actor.GetId()))
	}
	if shouldBlock {
		return NotificationPermFailure, errors.Wrap(helper.ErrFcmTokenNotFound,
			"device registration is not completed for current device hence dropping the sensitive notification")
	}
	vendorResp, err := n.fcmClient.SendMessage(context.Background(), &fcmPb.SendMessageRequest{
		Header:              &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FCM},
		NotificationMessage: req.GetNotificationMessage(),
		DeviceToken:         deviceTokenMsg.DeviceToken,
		AnalyticsLabel:      req.GetClientId(),
	})
	if te := epifigrpc.RPCError(vendorResp, err); te != nil {
		if vendorResp.GetStatus().IsRecordNotFound() {
			n.helperSvc.UpdateVendorAttemptInCommsTable(ctx, req.GetCommsMessageId(), pb.MessageState_FAILED,
				msg.Retries+1, vendorResp.GetNotificationId(), commonvgpb.Vendor_FCM, isLastAttempt, "", vendorResp.GetSubStatus())
			return NotificationPermFailure, errors.Wrap(te, fmt.Sprintf("error sending notification actor id : %s", actor.GetId()))
		}
		n.helperSvc.UpdateVendorAttemptInCommsTable(ctx, req.GetCommsMessageId(), pb.MessageState_TRANSIENT_FAILURE,
			msg.Retries+1, vendorResp.GetNotificationId(), commonvgpb.Vendor_FCM, isLastAttempt, "", vendorResp.GetSubStatus())
		return NotificationTransFailure, errors.Wrap(te, fmt.Sprintf("error sending notification actor id : %s", actor.GetId()))
	}
	metrics.RecordProcessedCommsRequest(pb.Medium_NOTIFICATION.String(), commonvgpb.Vendor_FCM.String(), req.GetNotificationMessage().GetNotification().GetNotificationType().String())
	// NOTE : If this DB update fails, our system will be in consistent state, since we are not retrying on this DB error
	// because the msg is delivered by the vendor. The decision taken here after multiple discussions is that we will
	// avoid sending multiple messages to our user. This situation is highly unlikely to occur when the DB Get is working
	// vut updates are not working. Whenever this happens it leaves the message in Failed/Created state while actually
	// it will be delivered to user.
	n.helperSvc.UpdateVendorAttemptInCommsTable(ctx, req.GetCommsMessageId(), pb.MessageState_DELIVERED,
		msg.Retries+1, vendorResp.GetNotificationId(), commonvgpb.Vendor_FCM, isLastAttempt, "", pb.CommsMessageSubStatus_COMMS_MESSAGE_SUB_STATUS_NOTIFICATION_DELIVERED)
	return NotificationSuccess, nil
}
