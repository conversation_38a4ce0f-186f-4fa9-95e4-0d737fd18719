//nolint:dupl
package handlers

import (
	"context"

	"github.com/pkg/errors"

	commsPb "github.com/epifi/gamma/api/comms"

	whatsappBotPb "github.com/epifi/gamma/api/whatsapp_bot"
	"github.com/epifi/gamma/whatsappbot/common"
	"github.com/epifi/gamma/whatsappbot/sender"
)

// this handler will send user help related information i.e info for our customer support channels
type BenefitsHandler struct {
	// common sender to be used by all handlers for sending the reply to user
	sender *sender.WhatsappBotsender
}

func NewBenefitsHandler(sender *sender.WhatsappBotsender) *BenefitsHandler {
	return &BenefitsHandler{
		sender: sender,
	}
}

func (g *BenefitsHandler) HandleIntent(ctx context.Context, intent whatsappBotPb.UserIntent, messageInfo *common.MessageInfo) error {
	err := g.sender.SendWhatsappReply(ctx, &commsPb.SendMessageRequest{
		Type:   commsPb.QoS_BEST_EFFORT,
		Medium: commsPb.Medium_WHATSAPP,
		UserIdentifier: &commsPb.SendMessageRequest_PhoneNumber{
			PhoneNumber: messageInfo.PhoneNumber.ToString(),
		},
		Message: &commsPb.SendMessageRequest_Whatsapp{
			Whatsapp: &commsPb.WhatsappMessage{
				WhatsappOption: &commsPb.WhatsappOption{
					Option: &commsPb.WhatsappOption_FiBotBenefitsWhatsappOption{
						FiBotBenefitsWhatsappOption: &commsPb.FiBotBenefitsWhatsappOption{
							WhatsappType: commsPb.WhatsappType_FI_BOT_BENEFITS,
							Option: &commsPb.FiBotBenefitsWhatsappOption_FiBotBenefitsWhatsappOptionV1{
								FiBotBenefitsWhatsappOptionV1: &commsPb.FiBotBenefitsWhatsappOptionV1{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
								},
							},
						},
					},
				},
			},
		},
	})
	if err != nil {
		return errors.Wrap(err, "error while sending user reply")
	}
	return nil
}
