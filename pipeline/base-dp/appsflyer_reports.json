{"database": "appsflyer_reports", "type": "vendor", "snowflake_schema_name": "APPSFLYER", "partition_col": "partition_column", "hive_sync_enabled": "true", "bq_write": true, "af_p360_url": "https://hq1.appsflyer.com/api/raw-data/export/app/{app_id}/{report_name}/v5?from={from_date}&to={to_date}&additional_fields=blocked_reason_rule,store_reinstall,impressions,contributor3_match_type,gp_click_time,match_type,mediation_network,oaid,deeplink_url,blocked_reason,blocked_sub_reason,gp_broadcast_referrer,gp_install_begin,custom_data,rejected_reason,device_download_time,keyword_match_type,contributor1_match_type,contributor2_match_type,device_model,monetization_network,segment,is_lat,gp_referrer,blocked_reason_value,store_product_page,device_category,app_type,rejected_reason_value,ad_unit,keyword_id,placement,network_account_id,install_app_store,amazon_aid,att", "af_organic_in_app_events": "https://hq1.appsflyer.com/api/raw-data/export/app/{app_id}/{report_name}/v5?from={from_date}&to={to_date}&event_name=af_level_achieved,af_purchase,af_src_com,kappa,omega,sigma,z3075dnjfshx&additional_fields=blocked_reason_rule,store_reinstall,impressions,contributor3_match_type,custom_dimension,conversion_type,gp_click_time,match_type,mediation_network,oaid,deeplink_url,blocked_reason,blocked_sub_reason,gp_broadcast_referrer,gp_install_begin,campaign_type,custom_data,rejected_reason,device_download_time,keyword_match_type,contributor1_match_type,contributor2_match_type,device_model,monetization_network,segment,gp_referrer,blocked_reason_value,device_category,app_type,rejected_reason_value,ad_unit,keyword_id,placement,network_account_id,install_app_store,amazon_aid,att", "af_in_app_events": "https://hq1.appsflyer.com/api/raw-data/export/app/{app_id}/{report_name}/v5?from={from_date}&to={to_date}&event_name=af_level_achieved,af_purchase,af_src_com,kappa,omega,sigma,z3075dnjfshx&additional_fields=blocked_reason_rule,store_reinstall,impressions,contributor3_match_type,custom_dimension,conversion_type,gp_click_time,match_type,mediation_network,oaid,deeplink_url,blocked_reason,blocked_sub_reason,gp_broadcast_referrer,gp_install_begin,campaign_type,custom_data,rejected_reason,device_download_time,keyword_match_type,contributor1_match_type,contributor2_match_type,device_model,monetization_network,segment,is_lat,gp_referrer,blocked_reason_value,store_product_page,device_category,app_type,rejected_reason_value,ad_unit,keyword_id,placement,network_account_id,install_app_store,amazon_aid,att", "tables": {"P360_android_details": {"snowflake_database": "EPIFI_DATALAKE_TECH", "primary_key": "id", "updated_at_key": "Install_Time", "transactional": true, "csv_mode": "PERMISSIVE", "contains_headers": true, "table_size": "small", "base_etl": {"add_uid": {"name": "id"}}, "ds_etl": {}, "sf-dna_etl": {"drop": ["Event_Revenue", "Event_Revenue_Currency", "Event_Revenue_USD", "Event_Source", "Is_Receipt_Validated", "Keywords", "Campaign_ID", "Adset_ID", "Cost_Model", "Cost_<PERSON>ur<PERSON>cy", "DMA", "IP", "WIFI", "Operator", "Carrier", "IMEI", "App_ID", "Bundle_ID", "Is_Retargeting", "Retargeting_Conversion_Type", "Attribution_Lookback", "Reengagement_Window", "User_Agent", "HTTP_Referrer", "Original_URL", "Contributor_3_Match_Type", "Google_Play_Click_Time", "Google_Play_Broadcast_Referrer", "Google_Play_Install_Begin_Time", "Keyword_Match_Type", "Contributor_1_Match_Type", "Contributor_2_Match_Type", "Monetization_Network", "Segment", "Is_LAT", "Google_Play_Referrer", "Store_Product_Page", "Device_Category", "App_Type", "Ad_Unit", "Keyword_ID", "Placement", "Network_Account_ID", "Amazon_Fire_ID", "ATT"]}}, "P360_ios_details": {"snowflake_database": "EPIFI_DATALAKE_TECH", "primary_key": "id", "updated_at_key": "Install_Time", "transactional": true, "contains_headers": true, "csv_mode": "PERMISSIVE", "table_size": "small", "base_etl": {"add_uid": {"name": "id"}}, "ds_etl": {}, "sf-dna_etl": {"drop": ["Event_Revenue", "Event_Revenue_Currency", "Event_Revenue_USD", "Event_Source", "Is_Receipt_Validated", "Keywords", "Campaign_ID", "Adset_ID", "Cost_Model", "Cost_<PERSON>ur<PERSON>cy", "DMA", "IP", "WIFI", "Operator", "Carrier", "IMEI", "App_ID", "Bundle_ID", "Is_Retargeting", "Retargeting_Conversion_Type", "Attribution_Lookback", "Reengagement_Window", "User_Agent", "HTTP_Referrer", "Original_URL", "Contributor_3_Match_Type", "Google_Play_Click_Time", "Google_Play_Broadcast_Referrer", "Google_Play_Install_Begin_Time", "Keyword_Match_Type", "Contributor_1_Match_Type", "Contributor_2_Match_Type", "Monetization_Network", "Segment", "Is_LAT", "Google_Play_Referrer", "Store_Product_Page", "Device_Category", "App_Type", "Ad_Unit", "Keyword_ID", "Placement", "Network_Account_ID", "Amazon_Fire_ID", "ATT"]}}, "af_in_app_events": {"snowflake_database": "EPIFI_DATALAKE_TECH", "primary_key": "id", "updated_at_key": "Event_Time", "transactional": true, "contains_headers": true, "csv_mode": "PERMISSIVE", "table_size": "small", "base_etl": {"add_uid": {"name": "id"}}, "ds_etl": {}, "sf-dna_etl": {"drop": ["Event_Revenue", "Event_Revenue_Currency", "Event_Revenue_USD", "Event_Source", "Is_Receipt_Validated", "Keywords", "Campaign_ID", "Adset_ID", "Cost_Model", "Cost_<PERSON>ur<PERSON>cy", "DMA", "IP", "WIFI", "Operator", "Carrier", "IMEI", "App_ID", "Bundle_ID", "Is_Retargeting", "Retargeting_Conversion_Type", "Attribution_Lookback", "Reengagement_Window", "User_Agent", "HTTP_Referrer", "Original_URL", "Contributor_3_Match_Type", "Google_Play_Click_Time", "Google_Play_Broadcast_Referrer", "Google_Play_Install_Begin_Time", "Keyword_Match_Type", "Contributor_1_Match_Type", "Contributor_2_Match_Type", "Monetization_Network", "Segment", "Is_LAT", "Google_Play_Referrer", "Store_Product_Page", "Device_Category", "App_Type", "Ad_Unit", "Keyword_ID", "Placement", "Network_Account_ID", "Amazon_Fire_ID", "ATT"]}}}}