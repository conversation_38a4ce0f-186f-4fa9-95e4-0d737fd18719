{"type": "record", "name": "MyClass", "namespace": "com.acme.avro", "fields": [{"name": "DEBIT", "type": {"type": "record", "namespace": "Record", "name": "DEBIT", "fields": [{"name": "additional_details", "type": "string"}, {"name": "batch_serial_id", "type": "string"}, {"name": "country_code", "type": "string"}, {"name": "instrument_details", "type": {"type": "record", "namespace": "MyClass.DEBIT", "name": "instrument_details", "fields": []}}, {"name": "particulars", "type": "string"}, {"name": "cbs_id", "type": "string"}, {"name": "value_date", "type": "string"}]}}]}