{"type": "record", "name": "Record", "namespace": "ca.dataedu", "fields": [{"name": "actualInstallmentAmount", "type": {"type": "record", "namespace": "Record", "name": "actualInstallmentAmount", "fields": [{"name": "currencyCode", "type": "string"}, {"name": "nanos", "type": "long"}, {"name": "units", "type": "string"}]}}, {"name": "chargesApplied", "type": {"type": "record", "namespace": "Record", "name": "chargesApplied", "fields": [{"name": "bounceCharges", "type": {"type": "record", "namespace": "Record.chargesApplied", "name": "bounceCharges", "fields": [{"name": "currencyCode", "type": "string"}, {"name": "nanos", "type": "long"}, {"name": "units", "type": "string"}]}}, {"name": "latePaymentInterest", "type": {"type": "record", "namespace": "Record.chargesApplied", "name": "latePaymentInterest", "fields": [{"name": "currencyCode", "type": "string"}, {"name": "nanos", "type": "long"}, {"name": "units", "type": "string"}]}}, {"name": "otherCharges", "type": {"type": "record", "namespace": "Record.chargesApplied", "name": "otherCharges", "fields": [{"name": "currencyCode", "type": "string"}, {"name": "nanos", "type": "long"}, {"name": "units", "type": "string"}]}}]}}, {"name": "interestOutstandingAmt", "type": {"type": "record", "namespace": "Record", "name": "interestOutstandingAmt", "fields": [{"name": "currencyCode", "type": "string"}, {"name": "nanos", "type": "long"}, {"name": "units", "type": "string"}]}}, {"name": "paidInterestAmt", "type": {"type": "record", "namespace": "Record", "name": "paidInterestAmt", "fields": [{"name": "currencyCode", "type": "string"}, {"name": "nanos", "type": "long"}, {"name": "units", "type": "string"}]}}, {"name": "paidPrincipalAmt", "type": {"type": "record", "namespace": "Record", "name": "paidPrincipalAmt", "fields": [{"name": "currencyCode", "type": "string"}, {"name": "nanos", "type": "long"}, {"name": "units", "type": "string"}]}}, {"name": "principalOutstandingAmt", "type": {"type": "record", "namespace": "Record", "name": "principalOutstandingAmt", "fields": [{"name": "currencyCode", "type": "string"}, {"name": "nanos", "type": "long"}, {"name": "units", "type": "string"}]}}]}