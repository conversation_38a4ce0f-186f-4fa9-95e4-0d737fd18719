-- PINOINT DOC FOR INTRO & COLUMN CHANGE PROCEDURE - https://docs.google.com/document/d/1RnG6YEN21t7DH_2jz3I9gIQnsEGZMYVOb0eIIyBffl0/edit#
SELECT distinct
    sa.PRIMARY_ACTOR_ID AS ACTOR_ID,
    SA.ACCOUNT_STATUS AS ACCNT_STATUS,
    USER_DETAILS.ACC_BLOCK_STATUS,
    user_details.ACCESS_REVOKE_DETAILS_REASON,
    user_details.blocked_date,
    USER_DETAILS.days_since_blocked,
    DATE(SA.OPEN_DATE_IST) AS ACCNT_OPEN_DATE,
    CASE
        WHEN DATEDIFF(day,SA.OPEN_DATE_IST,current_date()) BETWEEN 0 AND 30 THEN '0-30'
        WHEN DATEDIFF(day,SA.OPEN_DATE_IST,current_date()) BETWEEN 31 AND 60 THEN '31-60'
        WHEN DATEDIFF(day,SA.OPEN_DATE_IST,current_date()) BETWEEN 61 AND 90 THEN '61-90'
        WHEN DATEDIFF(day,SA.OPEN_DATE_IST,current_date()) BETWEEN 91 AND 120 THEN '91-120'
        WHEN DATEDIFF(day,SA.OPEN_DATE_IST,current_date()) BETWEEN 121 AND 180 THEN '121-180'
        ELSE '180+'
    END AS ACCOUNT_OPEN_SINCE,
    DATEDIFF(day,SA.OPEN_DATE_IST,current_date()) AS ACCOUNT_OPEN_SINCE_UNGROUPED,
    IFNULL(TO_CHAR(350-DATEDIFF(day,USER_DETAILS.CUST_CREATION_TIME,current_date())),'NA')  AS DAYS_TO_EXPIRY,
    case when onb.r <= 600 then 'CONTROL' else 'TEST' end as holdout,
    DATE(ONB.COMPLETED_AT_IST) AS ONB_COMPLETE_DATE,
    IFNULL(ONB.ACQ_CHANNEL_L3,'NA') AS ACQ_CHANNEL_L3,
    IFNULL(ONB.ACQ_CHANNEL_L2,'NA') AS ACQ_CHANNEL_L2,
    IFNULL(ONB.ACQ_CHANNEL_L1,'NA') AS ACQ_CHANNEL_L1,
    IFNULL(u.GENDER,'NA') AS GENDER,
    o.AGE_BUCKETS,
    o.KYC_LEVEL_AT_OPENING ,
    o.CURRENT_KYC_LEVEL,
    o.OS_FLAG as device_os,
    IFNULL(WA_PREF,'NA') AS WA_PREF,
    CASE
        WHEN ACCOUNT_BALANCE = 0 THEN '0'
        WHEN ACCOUNT_BALANCE BETWEEN 0 AND 100 THEN '1-100'
        WHEN ACCOUNT_BALANCE BETWEEN 101 AND 500 THEN '101-500'
        WHEN ACCOUNT_BALANCE BETWEEN 501 AND 1000 THEN '501-1000'
        WHEN ACCOUNT_BALANCE BETWEEN 1001 AND 5000 THEN '1001-5000'
        WHEN ACCOUNT_BALANCE BETWEEN 5001 AND 10000 THEN '5001-10000'
        WHEN ACCOUNT_BALANCE > 10000 THEN '>10000' END AS BAL_CATEGORY,
    --IFNULL(DA.CNT_ACTIVE_SD,0) AS CNT_ACTIVE_SD,
    IFNULL(HAS_MANUAL_DEPOSIT,0) AS HAS_MANUAL_DEPOSIT,
    IFNULL(HAS_REWARD_DEPOSIT,0) AS HAS_REWARD_DEPOSIT,
    --SD_DATE.LAST_SD_DATE,
    CASE
        WHEN DATEDIFF(DAY,SD_DATE.LAST_SD_DATE, current_date()) BETWEEN 0 AND 90 THEN TO_CHAR(DATEDIFF(day,LAST_DEBIT_TXN_DATE,current_date()))
        WHEN DATEDIFF(day,SD_DATE.LAST_SD_DATE,current_date()) BETWEEN 91 AND 120 THEN '91-120'
        WHEN DATEDIFF(day,SD_DATE.LAST_SD_DATE,current_date()) BETWEEN 121 AND 180 THEN '121-180'
        ELSE '180+'
    END AS DAYS_SINCE_LAST_SD_FUNDED,
    IFNULL(DID_DEBIT_CARD_TXN,0) AS DID_DEBIT_CARD_TXN,
    IFNULL(DID_UPI_NEFT_DEBIT_TXN,0) AS DID_UPI_NEFT_DEBIT_TXN,
    IFNULL(LAST_DEBIT_TXN_DATE,'1970-01-01') AS LAST_DEBIT_TXN_DATE,
    CASE
        WHEN DATEDIFF(DAY,LAST_DEBIT_TXN_DATE, current_date()) BETWEEN 0 AND 90 THEN TO_CHAR(DATEDIFF(DAY,LAST_DEBIT_TXN_DATE,current_date()))
        WHEN DATEDIFF(day,LAST_DEBIT_TXN_DATE,current_date()) BETWEEN 91 AND 120 THEN '91-120'
        WHEN DATEDIFF(day,LAST_DEBIT_TXN_DATE,current_date()) BETWEEN 121 AND 180 THEN '121-180'
        ELSE '180+'
    END AS DAYS_SINCE_LAST_DEBIT_TXN,
    IFNULL(LAST_DEBIT_UPI_TXN_DATE,'1970-01-01') AS LAST_DEBIT_UPI_TXN_DATE,
    LAST_DEBIT_CARD_TXN_CARD,
    IFNULL(LAST_CREDIT_TXN_DATE,'1970-01-01') AS LAST_CREDIT_TXN_DATE,
    CASE
        WHEN DATEDIFF(DAY,LAST_CREDIT_TXN_DATE, current_date()) BETWEEN 0 AND 90 THEN TO_CHAR(DATEDIFF(DAY,LAST_CREDIT_TXN_DATE,current_date()))
        WHEN DATEDIFF(DAY,LAST_CREDIT_TXN_DATE,current_date()) BETWEEN 91 AND 120 THEN '91-120'
        WHEN DATEDIFF(DAY,LAST_CREDIT_TXN_DATE,current_date()) BETWEEN 121 AND 180 THEN '121-180'
        ELSE '180+'
    END AS DAYS_SINCE_LAST_CREDIT_TXN,
    --- FIT SUBSCRIPTION DETAILS
    COALESCE(FIT_SUB.ACTIVE_FIT_RULES,0) AS ACTIVE_FIT_RULES,
    --COALESCE(FIT_SUB.ACTIVE_SPORTS_FIT_RULES,0) AS ACTIVE_SPORTS_FIT_RULES,
    COALESCE(FIT_SUB.ACTIVE_MERCHANT_FIT_RULES,0) AS ACTIVE_MERCHANT_FIT_RULES,
    --COALESCE(FIT_SUB.ACTIVE_WELLNESS_FIT_RULES,0) AS ACTIVE_WELLNESS_FIT_RULES,
    --COALESCE(FIT_SUB.ACTIVE_GROWTH_FIT_RULES,0) AS ACTIVE_GROWTH_FIT_RULES,
    --COALESCE(FIT_SUB.LAST_RULE_ACTIVATION_DATE,'2099-01-01') AS LAST_RULE_ACTIVATION_DATE,
    COALESCE(FIT_SUB.TOTAL_FIT_RULES,0) AS TOTAL_FIT_RULES,
    COALESCE(FIT_SUB.TOTAL_SPORTS_FIT_RULES,0) AS TOTAL_SPORTS_FIT_RULES,
    --COALESCE(FIT_SUB.TOTAL_MERCHANT_FIT_RULES,0) AS TOTAL_MERCHANT_FIT_RULES,
    --COALESCE(FIT_SUB.TOTAL_WELLNESS_FIT_RULES,0) AS TOTAL_WELLNESS_FIT_RULES,
    --COALESCE(FIT_SUB.TOTAL_GROWTH_FIT_RULES,0) AS TOTAL_GROWTH_FIT_RULES,
    --COALESCE(FIT_SUB.L7_FIT_SUBSCRIPTIONS,0) AS L7_FIT_SUBSCRIPTIONS,
    --COALESCE(FIT_SUB.L14_FIT_SUBSCRIPTIONS,0) AS L14_FIT_SUBSCRIPTIONS,
    --COALESCE(FIT_SUB.L28_FIT_SUBSCRIPTIONS,0) AS L28_FIT_SUBSCRIPTIONS,
    NVL(FIT_SUB.ROUND_UP_INVEST_STATUS,'NEVER') AS ROUND_UP_INVEST_STATUS,
    COALESCE(ACTIVE_DAILY_SIP_RULES,0) AS ACTIVE_DAILY_SIP_RULES,
    COALESCE(ACTIVE_WEEKLY_SIP_RULES,0) AS ACTIVE_WEEKLY_SIP_RULES,
    COALESCE(ACTIVE_MONTHLY_SIP_RULES,0) AS ACTIVE_MONTHLY_SIP_RULES,
    COALESCE(PAUSED_DAILY_SIP_RULES,0) AS PAUSED_DAILY_SIP_RULES,
    COALESCE(PAUSED_WEEKLY_SIP_RULES,0) AS PAUSED_WEEKLY_SIP_RULES,
    COALESCE(PAUSED_MONTHLY_SIP_RULES,0) AS PAUSED_MONTHLY_SIP_RULES,
    CASE WHEN FI_SAVE_LEAGUE.ACTOR_ID IS NOT NULL THEN 'TRUE' ELSE 'FALSE' END AS JOINED_FI_SAVINGS_LEAGUE,

    --- FIT SAVINGS DETAILS
    COALESCE(FIT_SAVE.FIT_TOTAL_AMOUNT_SAVED,0) AS FIT_TOTAL_AMOUNT_SAVED,
    COALESCE(FIT_SAVE.FIT_SPORTS_FIT_AMOUNT_SAVED,0) AS FIT_SPORTS_FIT_AMOUNT_SAVED,
    COALESCE(FIT_SAVE.FIT_MERCHANT_AMOUNT_SAVED,0) AS FIT_MERCHANT_AMOUNT_SAVED,
    COALESCE(FIT_SAVE.FIT_WELLNESS_AMOUNT_SAVED,0) AS FIT_WELLNESS_AMOUNT_SAVED,
    COALESCE(FIT_SAVE.FIT_GROWTH_AMOUNT_SAVED,0) AS FIT_GROWTH_AMOUNT_SAVED,
    --COALESCE(FIT_SAVE.FIT_LAST_SAVED_DATE,'2099-01-01') AS FIT_LAST_SAVED_DATE,
    --CASE
    --    WHEN DATEDIFF(DAY,FIT_SAVE.FIT_LAST_SAVED_DATE, current_date()) BETWEEN 0 AND 90 THEN TO_CHAR(DATEDIFF(day,last_credit_Txn_date,current_date()))
    --    WHEN DATEDIFF(day,FIT_SAVE.FIT_LAST_SAVED_DATE,current_date()) BETWEEN 91 AND 120 THEN '91-120'
    --    WHEN DATEDIFF(day,FIT_SAVE.FIT_LAST_SAVED_DATE,current_date()) BETWEEN 121 AND 180 THEN '121-180'
    --    ELSE '180+'
    --END AS DAYS_SINCE_LAST_SAVED_FIT,
    --COALESCE(FIT_SAVE.L7_FIT_SAVINGS,0) AS L7_FIT_SAVINGS,
    --COALESCE(FIT_SAVE.L14_FIT_SAVINGS,0) AS L14_FIT_SAVINGS,
    --COALESCE(FIT_SAVE.L28_FIT_SAVINGS,0) AS L28_FIT_SAVINGS,
    CASE WHEN TXN_HEALTHY_WEALTHY_WISE_PREV_DAY>0 THEN 'YES' ELSE 'NO' END AS TXN_HEALTHY_WEALTHY_WISE_PREV_DAY,
    CASE WHEN TXN_SHOP_TILL_YOU_DROP_PREV_DAY>0 THEN 'YES' ELSE 'NO' END AS TXN_SHOP_TILL_YOU_DROP_PREV_DAY,
    COALESCE(FIT_AMOUNT_SAVED_LAST30DAYS,0) AS FIT_AMOUNT_SAVED_LAST30DAYS,
    CASE WHEN FIT_SAVED_COUNT=1 THEN 'YES' ELSE 'NO' END AS FIT_SAVED_FIRST_TIME,

    CASE WHEN REFEREE.REFEREE_ACTOR_ID IS NULL THEN 0 ELSE 1 END AS IS_REFERRED_ACTOR,
    --Credit_SUCCEEDED_FLAG,
    case
        when TXN_CREDIT.ACTOR_TO_ID IS NULL then 'NA'
        when  o.CURRENT_KYC_LEVEL = 'MIN_KYC'  then PERCENT_KYC_LIMIT
        else 'NA'
    end as PERCENT_KYC_LIMIT,
    --PERCENT_KYC_VALUE
    case when TXN_CREDIT.ACTOR_TO_ID IS NULL then 'NA' else TO_CHAR(PERCENT_KYC_VALUE) end as PERCENT_KYC_VALUE,
    COALESCE(attempted_kyc,0) AS attempted_kyc,
    to_date(COALESCE(LAST_SEARCH_SUBMITTED,'1970-01-01')) AS LAST_SEARCH_SUBMITTED,
    IFNULL(IS_REFFERAL_ELIGIBLE,'No') AS IS_REFFERAL_ELIGIBLE,
    IFNULL(referrals_code_claims,0) AS referrals_code_claims,
    IFNULL(referrals_acc_created,0) AS referrals_acc_created,
    IFNULL(referrer_reward_given,0) AS referrer_reward_given,
    IFNULL(referrer_reward_given_post_season_1,0) AS referrer_reward_given_post_season_1,
    IFNULL(referrer_reward_given_post_season_2,0) AS referrer_reward_given_post_season_2,
    loadedscreen_Date,ClickedShareCode_Date,vkyc_status.vkyc_status as vkyc_status,vkyc_status.vkyc_date as vkyc_date,

    case
        when coalesce(NET_BALA,0)=0 then '0'
        when coalesce(NET_BALA,0)>=1 and coalesce(NET_BALA,0)<=500 then '1_500'
        when coalesce(NET_BALA,0)>=501 and coalesce(NET_BALA,0)<=1000 then '501_1000'
        when coalesce(NET_BALA,0)>=1001 and coalesce(NET_BALA,0)<=3000 then '1001_3000'
        when coalesce(NET_BALA,0)>=3001 and coalesce(NET_BALA,0)<=5000 then '3001_5000'
        when coalesce(NET_BALA,0)>=5001 and coalesce(NET_BALA,0)<=10000 then '5001_10000'
        else '10000_plus'
    end as FI_COINS_BALANCE_CATEGORY,
    IFNULL((CASE WHEN DC_ACTIVATED=1 THEN 'ACTIVATED' ELSE 'NOT ACTIVATED' END),'NA') AS DC_ACTIVATED,
    IFNULL(UD.UNINSTALLED_FLAG,'NO') AS UNINSTALLED_FLAG,
    CASE WHEN COALESCE(transaction_amount,0)=0 THEN '0'
        WHEN COALESCE(transaction_amount,0) BETWEEN 1 AND 100 THEN '1_100'
        WHEN COALESCE(transaction_amount,0) BETWEEN 101 AND 500 THEN '101_500'
        WHEN COALESCE(transaction_amount,0) BETWEEN 501 AND 1000 THEN '501_1000'
        WHEN COALESCE(transaction_amount,0) BETWEEN 1001 AND 5000 THEN '1001_5000'
        WHEN COALESCE(transaction_amount,0) BETWEEN 5001 AND 10000 THEN '5001_10000'
        ELSE '10000+'
    END AS LAST_ADDFUNDS_AMOUNT,
    COALESCE(LAST_DATE_OF_PAY_REWARD_EARNED,date('1970-01-01')) as LAST_DATE_OF_PAY_REWARD_EARNED,
    COALESCE(LAST_DATE_OF_ADD_FUNDS_REWARD_EARNED,date('1970-01-01')) as LAST_DATE_OF_ADD_FUNDS_REWARD_EARNED,
    COALESCE(LAST_DATE_OF_FIRST_UPI_REWARD_EARNED,date('1970-01-01')) as LAST_DATE_OF_FIRST_UPI_REWARD_EARNED,
    COALESCE(LAST_DATE_OF_FIT_REWARD_EARNED,date('1970-01-01')) as LAST_DATE_OF_FIT_REWARD_EARNED,
    COALESCE(LAST_DATE_OF_FIRST_FIT_RULE_REWARD_EARNED,date('1970-01-01')) as LAST_DATE_OF_FIRST_FIT_RULE_REWARD_EARNED,
    COALESCE(DC_TXNS_LAST_30_DAYS,0) AS DC_TXNS_LAST_30_DAYS,
    COALESCE(UPI_TXNS_LAST_30_DAYS,0) AS UPI_TXNS_LAST_30_DAYS,

    -- mf
    nvl(mf_l.mf_lumpsum_investment_done,0) as mf_lumpsum_investment_done,
    case when mf_l.actor_id is null then -999
    else DATEDIFF(day,current_date(),last_mf_txn_date) end as Days_since_last_investment_done_lumpsum,
    -- p2p
    nvl(p2p_invest.is_eligible_p2p,0) as is_eligible_p2p,
    nvl(p2p_invest.is_closetoeligible_p2p_vkycpending,0) as is_closetoeligible_p2p_vkycpending,
    nvl(p2p_invest.no_of_p2p_investments,0) as no_of_p2p_investments,
    nvl(p2p_invest.is_noteligible_minKYC,0) as is_noteligible_minKYC,
    nvl(p2p_invest.is_noteligible_balance,0) as is_noteligible_balance,
    nvl(to_char(datediff(day,Jump.last_jump_invested,current_date())),'NEVER') as days_since_invested_in_jump,
    case when Jump.used_limit < 10000 then '0-10k'
        when Jump.used_limit < 25000 then '10k-25k'
        when Jump.used_limit < 50000 then '25k-50k'
        when Jump.used_limit < 100000 then '50k-1L'
        when Jump.used_limit < 200000 then '1L-2L'
        when Jump.used_limit < 300000 then '2L-3L'
        when Jump.used_limit < 400000 then '3L-4L'
        when Jump.used_limit < 500000 then '4L-5L'
        when Jump.used_limit < 600000 then '5L-6L'
        when Jump.used_limit < 700000 then '6L-7L'
        when Jump.used_limit < 800000 then '7L-8L'
        when Jump.used_limit < 900000 then '8L-9L'
        when Jump.used_limit >= 900000 then '>9L'
        else 'NA' end as p2p_limit_used,
   case
        when coalesce(bal_7_day.avg_bal_last_7_days,0)=0 then 'A: 0'
        when coalesce(bal_7_day.avg_bal_last_7_days,0)>=0 and coalesce(bal_7_day.avg_bal_last_7_days,0)<1000 then 'B: 1-1000'
        when coalesce(bal_7_day.avg_bal_last_7_days,0)>=1000 and coalesce(bal_7_day.avg_bal_last_7_days,0)<5000 then 'C: 1_5K'
        when coalesce(bal_7_day.avg_bal_last_7_days,0)>=5000 and coalesce(bal_7_day.avg_bal_last_7_days,0)<10000 then 'D: 5_10K'
        when coalesce(bal_7_day.avg_bal_last_7_days,0)>=10000 and coalesce(bal_7_day.avg_bal_last_7_days,0)<50000 then 'E: 10_50K'
        when coalesce(bal_7_day.avg_bal_last_7_days,0)>=50000 and coalesce(bal_7_day.avg_bal_last_7_days,0)<100000 then 'F: 50_100K'
        else 'G: >=100K'
    end as avg_bal_last_7_days,

    coalesce(salary_account_status.is_salary_account_status,'not a salary user') as is_salary_account_status,
    coalesce(verified_company_salary_not_verified.verified_company_salary_not_verified,0) as verified_company_salary_not_verified,
    coalesce(received_salary_joiningbonus.received_salary_joiningbonus,0) as received_salary_joiningbonus,
    coalesce(made_firsttxn_aftersalarycredit.made_firsttxn_aftersalarycredit,0) as made_firsttxn_aftersalarycredit,
    coalesce(salary_banner_clicked,0) as salary_banner_clicked,
    coalesce(salary_clicked_upgrade_now,0) as salary_clicked_upgrade_now,

    ----mutual funds------------------------------------------------------------------------------------------------

    coalesce(fit_auto_invest.no_autoinvest_rules,0) as no_autoinvest_rules,
    coalesce(fit_auto_invest.no_active_autoinvest_rules,0) as no_active_autoinvest_rules,
    coalesce(fit_exec_once.flag_autoinvest_executed,0) as flag_autoinvest_executed,
    coalesce(mf_land_mfdashboard.no_times_landed_mfDashboard,0) as no_times_landed_mfDashboard,
    coalesce(mf_land_collections.flag_landed_explore_collections,0) as flag_landed_explore_collections,
    coalesce(mf_yesterday_events.flag_viewed_fund_yesterday,0) as flag_viewed_fund_yesterday,
    coalesce(mf_yesterday_events.flag_fundpage_clicked_yesterday,0) as flag_fundpage_clicked_yesterday,
    coalesce(ca_gmail_kra.flag_ca_gmail_kra,0) as flag_ca_gmail_kra,

    -----------------------------------------------------------------------------------------------------------------

    ref_drop_of.tat_from_shared_to_load,ref_drop_of.tat_from_current_date_to_loaded_screen,
    vkyc_drop_of.tat_from_started_call_to_loaded_Screen,vkyc_drop_of.tat_from_current_date_to_loaded_vkyc_screen,
    card_form.physical_flag,card_state.tat_from_delivery,card_state.activation_state,
    card_international.L30_International_TXN_AMT,card_international.Lifetime_International_TXN_AMT,card_international.L30_International_Active,card_international.Lifetime_International_Active, coalesce(connected_accounts.no_of_connected_accs,0) AS no_of_connected_accs,
    coalesce(Bank_used_to_add_funds.FROM_BANK_NAME,'NA') AS FROM_BANK_NAME,
    days_since_app_active,
    case when days_since_app_active<0 then '<0'
    when days_since_app_active between 0 and 15 then '0-15'
    when days_since_app_active between 16 and 30 then '16-30'
    when days_since_app_active between 31 and 60 then '31-60'
    when days_since_app_active between 61 and 90 then '61-90'
    when days_since_app_active between 91 and 120 then '91-120'
    when days_since_app_active between 121 and 180 then '121-180'
    when days_since_app_active between 181 and 270 then '181-270'
    when days_since_app_active > 270 then '270+'
    end as days_since_app_active_bucket,

    COALESCE(LAST_MONTH_SPENDS.MONTH_NAME,'NA') AS LAST_MONTH_NAME,
    ROUND(COALESCE(LAST_MONTH_SPENDS.TOTAL_DEBIT,0)) AS TOTAL_SPENT_LAST_MONTH,
    COALESCE(LAST_MONTH_SPENDS.DISPLAY_CATEGORY,'NA') AS HIGHEST_SPEND_CATEGORY_LAST_MONTH,
    ROUND(COALESCE(LAST_MONTH_SPENDS.CATEGORY_DEBIT,0)) AS SPEND_AMOUNT_HIGHEST_CAT_LAST_MONTH,
    ROUND(COALESCE(LAST_MONTH_SPENDS.CATEGORY_DEBIT_SHARE,0)) AS SPEND_PERCENT_HIGHEST_CAT_LAST_MONTH,

    ROUND(COALESCE(LAST_WEEK_SPENDS.TOTAL_DEBIT,0)) AS TOTAL_SPENT_LAST_WEEK,
    COALESCE(LAST_WEEK_SPENDS.DISPLAY_CATEGORY,'NA') AS HIGHEST_SPEND_CATEGORY_LAST_WEEK,
    ROUND(COALESCE(LAST_WEEK_SPENDS.CATEGORY_DEBIT,0)) AS SPEND_AMOUNT_HIGHEST_CAT_LAST_WEEK,
    ROUND(COALESCE(LAST_WEEK_SPENDS.CATEGORY_DEBIT_SHARE,0)) AS SPEND_PERCENT_HIGHEST_CAT_LAST_WEEK ,
    IFNULL(geo.CITY,'NA') AS CITY,
    IFNULL(geo.CITY_TIER,'NA') AS CITY_TIER,
    IFNULL(geo.STATE,'NA') AS STATE,
    IFNULL(geo.PINCODE,'NA') AS PINCODE,
    o.CREDIT_SCORE_BUCKETS as CREDIT_SCORE_BUCKETS, gu_flag.good_user_flag as good_user_flag, ab_data.user_layer as ab_cohort_segment,
    ----------------------------------------------------------------------------------------------------------------- pre-approved loans -----------------------------------------------------------------------------------------------------------------
    -- NA means no offer, NO means not approved
    IFNULL(PAL.LOAN_OFFER_ELIGIBILITY_APPROVED_FLAG,'NA') AS LOAN_OFFER_ELIGIBILITY_APPROVED_FLAG,
    COALESCE(PAL.LOAN_OFFER_ELIGIBILITY_APPROVED_DATE,date('1970-01-01')) as LOAN_OFFER_ELIGIBILITY_APPROVED_AT_DATE,
    COALESCE(PAL.LOAN_OFFER_ELIGIBILITY_SENT_DATE,date('1970-01-01')) AS LOAN_OFFER_ELIGIBILITY_SENT_DATE,
 	IFNULL(DAYS_SINCE_OFFER_APPROVAL,0) AS DAYS_SINCE_OFFER_APPROVAL,
    IFNULL(PAL.LOAN_APP_JOURNEY_STARTED_FLAG,'NA') AS LOAN_APP_JOURNEY_STARTED_FLAG,
 	COALESCE(PAL.LOAN_APP_JOURNEY_STARTED_DATE,date('1970-01-01')) AS LOAN_APP_JOURNEY_STARTED_DATE,
 	IFNULL(DAYS_SINCE_LOAN_APP_JOURNEY_STARTED,0) AS DAYS_SINCE_LOAN_APP_JOURNEY_STARTED,
    IFNULL(PAL.LOAN_DISBURSAL_FLAG,'NA') AS LOAN_DISBURSAL_FLAG,
    IFNULL(PAL.DAYS_SINCE_LAST_LOAN,0)  AS DAYS_SINCE_LAST_LOAN,
    IFNULL(PAL.OFFER_MAX_LOAN_AMOUNT,0) AS LOAN_OFFER_MAX_LOAN_AMOUNT,
    IFNULL(PAL.OFFER_INTEREST_RATE_PERC,0)  AS OFFER_INTEREST_RATE_PERC,
    IFNULL(PAL.IS_LOAN_OFFER_EXPIRED_FLAG,'NA') AS IS_LOAN_OFFER_EXPIRED_FLAG,
    IFNULL(IS_LATEST_ELIGIBLE_OFFER_FLAG,'NA') AS IS_LATEST_ELIGIBLE_LOAN_OFFER_FLAG,
    -----------------------------------------lending re-engagemnt------------------------------------------------------------------
	IFNULL(LENDING_ELIGIBLE_USER_FLAG,'NO') AS LENDING_ELIGIBLE_USER_FLAG,
	IFNULL(PL_ELIGIBLE_BASE_FLAG,0) AS PL_LENDING_RENG_ELIGIBLE_BASE_FLAG,
	IFNULL(CC_ELIGIBLE_BASE_FLAG,0) AS CC_LENDING_RENG_ELIGIBLE_BASE_FLAG,
/*	IFNULL(APP_ACTIVE_L1D,0) AS LENDING_RENG_APP_ACTIVE_L1D,
	IFNULL(APP_ACTIVE_L7D,0) AS LENDING_RENG_APP_ACTIVE_L7D,
	IFNULL(APP_ACTIVE_L14D,0) AS LENDING_RENG_APP_ACTIVE_L14D,
	IFNULL(APP_ACTIVE_L30D,0) AS LENDING_RENG_APP_ACTIVE_L30D,
	IFNULL(APP_ACTIVE_L60D,0) AS LENDING_RENG_APP_ACTIVE_L60D,
	IFNULL(APP_ACTIVE_L90D,0) AS LENDING_RENG_APP_ACTIVE_L90D,
	IFNULL(TXN_ACTIVE_L1D,0) AS LENDING_RENG_TXN_ACTIVE_L1D,
	IFNULL(TXN_ACTIVE_L7D,0) AS LENDING_RENG_TXN_ACTIVE_L7D,
	IFNULL(TXN_ACTIVE_L14D,0) AS LENDING_RENG_TXN_ACTIVE_L14D,
	IFNULL(TXN_ACTIVE_L30D,0) AS LENDING_RENG_TXN_ACTIVE_L30D,
	IFNULL(TXN_ACTIVE_L60D,0) AS LENDING_RENG_TXN_ACTIVE_L60D,
	IFNULL(TXN_ACTIVE_L90D,0) AS LENDING_RENG_TXN_ACTIVE_L90D,*/
    credit_amt_first_7_days,
    qr_debit_first_7_days,
    p2m_first_7_days, o.employment_type,
CASE
        WHEN DATEDIFF(DAY,last_referral_reward_date, current_date()) BETWEEN 0 AND 30 then '0-30'
        WHEN DATEDIFF(DAY,last_referral_reward_date,current_date()) BETWEEN 31 AND 60 THEN '31-60'
        WHEN DATEDIFF(DAY,last_referral_reward_date,current_date()) BETWEEN 61 AND 90 THEN '61-90'
    WHEN DATEDIFF(DAY,last_referral_reward_date,current_date()) BETWEEN 91 AND 180 THEN '91-180'
    WHEN DATEDIFF(DAY,last_referral_reward_date,current_date()) BETWEEN 181 AND 270 THEN '181-270'
    WHEN DATEDIFF(DAY,last_referral_reward_date,current_date()) BETWEEN 271 AND 365 THEN '271-365'
        ELSE '365+'
    END AS DAYS_SINCE_LAST_referral_reward_grouped,
    DATEDIFF(DAY,last_referral_reward_date, current_date()) as DAYS_SINCE_LAST_referral_reward_ungrouped,
    nvl(food_groceries_last_month,0) as food_groceries_last_month,
    nvl(shop_last_month,0) as shop_last_month,
    nvl(commute_last_month,0) as commute_last_month,
    nvl(personal_care_last_month,0) as personal_care_last_month,
    nvl(gaming_last_month,0) as gaming_last_month,
    IFNULL(sal_one_perc_campaign.sal_one_perc_campaign_flag,'Applicable')  AS sal_one_perc_campaign_flag,
    days_since_referral_eligibile,

    ------ Salary

coalesce(L30_FI_COINS_REDEEMED,0) as L30_FI_COINS_REDEEMED,
    coalesce(L30_FI_COINS_REDEEMED_BUCKET,'0') as L30_FI_COINS_REDEEMED_BUCKET,
    coalesce(visited_salary_intro_page,0) as visited_salary_intro_page,

    coalesce(salary_employer_name,'not_declared') as salary_employer_name,
    coalesce(salary_account_status,'Salary_Registration_Not_Started') as salary_account_status,
    coalesce(registration_date,date('1970-01-01')) as registration_date,
    coalesce(salary_activation_date,date('1970-01-01')) as salary_activation_date,
    coalesce(days_from_registration_date_bucket,'not_salary_registered') as days_from_registration_date_bucket,
    coalesce(days_from_salary_activation_date_bucket,'not_salary_activated') as days_from_salary_activation_date_bucket,
    coalesce(salary_did_txn_post_salary_credit_1_0,0) as salary_did_txn_post_salary_credit_1_0,
    coalesce(salary_did_txn_200_salary_credit_1_0,0) as salary_did_txn_200_salary_credit_1_0,


--------------------USER ACTIVITY STATUS-------------------------------------

    user_activity_status.APP_OPENS_60 as app_opens_60d,
    user_activity_status.days_since_account_created as days_since_account_created,
    user_activity_status.first_28d_eod_bal as first_28d_eod_bal,
    user_activity_status.on_app_debit_txns as on_app_debit_txns,
    user_activity_status.off_app_debit_txns as off_app_debit_txns,
    user_activity_status.total_inc_cred as lifetime_incoming_credit,
    user_activity_status.user_status as user_activity_status
    , aff_flag.aff_flag_v09 as aff_flag_v09,geo2.city_tier_new as city_tier_new




FROM EPIFI_PROD_DB.BASIC_ANALYTICS.ACCOUNT SA
join EPIFI_PROD_DB.BASIC_ANALYTICS.FI_ACCOUNT_USERS u
    on sa.PRIMARY_ACTOR_ID = u.ACTOR_ID
join EPIFI_PROD_DB.BASIC_ANALYTICS.ONB_USER_V2 o
    on sa.PRIMARY_ACTOR_ID = o.ACTOR_ID

LEFT JOIN (
    SELECT
        PRIMARY_ACTOR_ID,
        COUNT(ACCOUNT_ID) AS CNT_ACTIVE_SD,
        MAX(CASE WHEN PROVENANCE = 'USER_APP' THEN 1 ELSE 0 END ) AS HAS_MANUAL_DEPOSIT,
        MAX(CASE WHEN PROVENANCE = 'REWARDS_APP' THEN 1 ELSE 0 END ) AS HAS_REWARD_DEPOSIT
    FROM EPIFI_PROD_DB.BASIC_ANALYTICS.ACCOUNT
    WHERE ACCOUNT_STATUS = 'CREATED' AND ACCOUNT_TYPE = 'SMART_DEPOSIT'
    GROUP BY 1 ) DA --THIS WILL LEAD TO ONE ACTOR HAVING MULTIPLE ROWS IF NOT AGGREGATED BEFOREHAND
ON DA.PRIMARY_ACTOR_ID = sa.PRIMARY_ACTOR_ID

LEFT JOIN (
    SELECT
        AH.ACTOR_ID,
        MAX(CAL_DATE) AS LAST_SD_DATE
    FROM EPIFI_PROD_DB.BASIC_ANALYTICS.ACCOUNT_HISTORY AH
        --INNER JOIN EPIFI_PROD_DB.BASIC_ANALYTICS.ACCOUNT A
        --ON A.PRIMARY_ACTOR_ID=AH.ACTOR_ID AND A.ACCOUNT_ID=AH.ACCOUNT_ID
    WHERE AH.ACCOUNT_TYPE = 'SMART_DEPOSIT'
    GROUP BY 1
    ) SD_DATE
ON SD_DATE.ACTOR_ID=SA.PRIMARY_ACTOR_ID

LEFT JOIN (
    SELECT ACTOR_FROM_ID,
         MAX(CASE WHEN PAYMENT_PROTOCOL = 'CARD' THEN 1 ELSE 0 END) AS DID_DEBIT_CARD_TXN,
         MAX(CASE WHEN PAYMENT_PROTOCOL IN ('UPI', 'NEFT') THEN 1 ELSE 0 END) AS DID_UPI_NEFT_DEBIT_TXN,
         to_date(MAX(SERVER_CREATED_AT_IST)) LAST_DEBIT_TXN_DATE,
         to_date(MAX(CASE WHEN PAYMENT_PROTOCOL ='UPI' THEN SERVER_CREATED_AT_IST END)) LAST_DEBIT_UPI_TXN_DATE,
         to_date(MAX(CASE WHEN PAYMENT_PROTOCOL ='CARD' THEN SERVER_CREATED_AT_IST END)) LAST_DEBIT_CARD_TXN_CARD,
         COUNT(DISTINCT(CASE WHEN PAYMENT_PROTOCOL = 'CARD'AND DATEDIFF(DAY,SERVER_CREATED_AT_IST,CURRENT_DATE())<=30 THEN TRANSACTION_ID ELSE NULL END)) AS DC_TXNS_LAST_30_DAYS,
         COUNT(DISTINCT(CASE WHEN PAYMENT_PROTOCOL = 'UPI'AND DATEDIFF(DAY,SERVER_CREATED_AT_IST,CURRENT_DATE())<=30 THEN TRANSACTION_ID ELSE NULL END)) AS UPI_TXNS_LAST_30_DAYS
    FROM EPIFI_PROD_DB.BASIC_ANALYTICS.TRANSACTIONS txn
    where SENDER_TYPE_GENERAL = 'USER'
    group by 1) TXN--THIS WILL LEAD TO ONE ACTOR HAVING MULTIPLE ROWS IF NOT AGGREGATED BEFOREHAND
ON TXN.ACTOR_FROM_ID = sa.PRIMARY_ACTOR_ID
LEFT JOIN (
    select * from
    (
        select
            actor_to_id as actor_id,
            transaction_amount,
            row_number() over(partition by actor_to_id order by credited_at desc) as rnk
            --max(credited_at) as max_date
        from EPIFI_PROD_DB.BASIC_ANALYTICS.TRANSACTIONS
        where credit_debit ='Credit' and account_to_id is not null and transaction_status='SUCCESS'
        --group by 1
    )txn_add_funds
    where rnk =1
) TXN_FN--THIS WILL LEAD TO ONE ACTOR HAVING MULTIPLE ROWS IF NOT AGGREGATED BEFOREHAND
ON TXN_FN.ACTOR_ID = sa.PRIMARY_ACTOR_ID

LEFT JOIN (
    SELECT
        ACTOR_TO_ID,
        /*case when SUM(CASE WHEN ORDER_STATUS IN ('PAID', 'SETTLED','FULFILLED') THEN 1 ELSE 0 END )= 0  then 'Credit never succeeded'
            else 'Credit succeeded'
            end Credit_SUCCEEDED_FLAG,*/
           to_date(max(server_created_At_ist)) as last_credit_Txn_date,
        case when ROUND(SUM(TRANSACTION_AMOUNT )/2000) between 0 and 20 then 'a. 0-20'
            when ROUND(SUM(TRANSACTION_AMOUNT )/2000) between 21 and 40 then 'b. 20-40'
            when ROUND(SUM(TRANSACTION_AMOUNT )/2000) between 41 and 60 then 'c. 40-60'
             when ROUND(SUM(TRANSACTION_AMOUNT )/2000) between 61 and 80 then 'd. 60-80'
            when ROUND(SUM(TRANSACTION_AMOUNT )/2000) between 81 and 90 then 'e. 80-90'
            when ROUND(SUM(TRANSACTION_AMOUNT )/2000) between 91 and 100 then 'f. 90-100'
            else 'g. > 100' end
            AS PERCENT_KYC_LIMIT,
            ROUND(SUM(TRANSACTION_AMOUNT )/2000) AS PERCENT_KYC_VALUE
    FROM EPIFI_PROD_DB.BASIC_ANALYTICS.TRANSACTIONS
    where RECEIVER_TYPE_GENERAL = 'USER'
    --SELECT PAYMENT_PROTOCOL, COUNT(DISTINCT ACTOR_TO_ID) FROM EPIFI_PROD_DB.BASIC_ANALYTICS.TRANSACTIONS where RECEIVER_TYPE_GENERAL = 'USER' GROUP BY 1
    GROUP BY 1
        ) TXN_CREDIT
  ON TXN_CREDIT.ACTOR_TO_ID = sa.PRIMARY_ACTOR_ID

LEFT JOIN (
    SELECT
        SUB.ACTOR_ID ,
        --COUNT ALL THE ACTIVE RULE THIS ACTOR HAS TO THE ONGOING RULE (EXCLUDING ACTIVE SUBSCRIPTIONS TO THE INACTIVE RULES)
        COUNT(DISTINCT CASE WHEN SUB.VERSION_STATE = 'CURRENT' AND SUB.STATE = 'ACTIVE'
                AND RLS.STATE = 'RULE_STATE_ACTIVE' AND RLS.CATEGORY = 'AUTO_SAVE' THEN SUB.ID END) AS ACTIVE_FIT_RULES,
        COUNT(DISTINCT CASE WHEN RULE_CATEGORY = 'Sports' AND  SUB.VERSION_STATE = 'CURRENT'
            AND SUB.STATE = 'ACTIVE' AND RLS.STATE = 'RULE_STATE_ACTIVE' THEN SUB.ID END) AS ACTIVE_SPORTS_FIT_RULES,
        COUNT(DISTINCT CASE WHEN RULE_CATEGORY = 'Merchant' AND  SUB.VERSION_STATE = 'CURRENT'
            AND SUB.STATE = 'ACTIVE' AND RLS.STATE = 'RULE_STATE_ACTIVE' THEN SUB.ID END) AS ACTIVE_MERCHANT_FIT_RULES,
        --COUNT(DISTINCT CASE WHEN RULE_CATEGORY = 'Wellness' AND  SUB.VERSION_STATE = 'CURRENT'
        --  AND SUB.STATE = 'ACTIVE' AND RLS.STATE = 'RULE_STATE_ACTIVE' THEN SUB.ID END) AS ACTIVE_WELLNESS_FIT_RULES,
        COUNT(DISTINCT CASE WHEN RULE_CATEGORY = 'Growth' AND  SUB.VERSION_STATE = 'CURRENT'
            AND SUB.STATE = 'ACTIVE' AND RLS.STATE = 'RULE_STATE_ACTIVE' THEN SUB.ID END) AS ACTIVE_GROWTH_FIT_RULES,
        MAX(CASE WHEN SUB.VERSION_STATE='CURRENT' AND SUB.STATE='ACTIVE' AND RLS.STATE='RULE_STATE_ACTIVE'
                    AND RLS.NAME='DAILY SIP' THEN 1 ELSE 0 END) AS ACTIVE_DAILY_SIP_RULES,
            MAX(CASE WHEN SUB.VERSION_STATE='CURRENT' AND SUB.STATE='ACTIVE' AND RLS.STATE='RULE_STATE_ACTIVE'
                    AND RLS.NAME='WEEKLY SIP' THEN 1 ELSE 0 END) AS ACTIVE_WEEKLY_SIP_RULES,
            MAX(CASE WHEN SUB.VERSION_STATE='CURRENT' AND SUB.STATE='ACTIVE' AND RLS.STATE='RULE_STATE_ACTIVE'
                    AND RLS.NAME='MONTHLY SIP' THEN 1 ELSE 0 END) AS ACTIVE_MONTHLY_SIP_RULES,
            MAX(CASE WHEN SUB.VERSION_STATE='CURRENT' AND SUB.STATE='INACTIVE' AND RLS.STATE='RULE_STATE_ACTIVE'
                    AND RLS.NAME='DAILY SIP' THEN 1 ELSE 0 END) AS PAUSED_DAILY_SIP_RULES,
            MAX(CASE WHEN SUB.VERSION_STATE='CURRENT' AND SUB.STATE='INACTIVE' AND RLS.STATE='RULE_STATE_ACTIVE'
                    AND RLS.NAME='WEEKLY SIP' THEN 1 ELSE 0 END) AS PAUSED_WEEKLY_SIP_RULES,
            MAX(CASE WHEN SUB.VERSION_STATE='CURRENT' AND SUB.STATE='INACTIVE' AND RLS.STATE='RULE_STATE_ACTIVE'
                    AND RLS.NAME='MONTHLY SIP' THEN 1 ELSE 0 END) AS PAUSED_MONTHLY_SIP_RULES,
        --MAX(CASE WHEN SUB.STATE = 'ACTIVE' THEN DATE(SUB.VERSION_VALID_FROM) END) AS LAST_RULE_ACTIVATION_DATE,
        --- COUNT ALL THE RULES THIS ACTOR EVER HAD (INCLUDING EXPIRED SUBSCRIPTIONS AND INACTIVE RULES)
        --COUNT(DISTINCT SUB.ID) AS TOTAL_FIT_RULES,
        COUNT(DISTINCT CASE WHEN RLS.CATEGORY='AUTO_SAVE' THEN SUB.ID END) AS TOTAL_FIT_RULES,
        COUNT(DISTINCT CASE WHEN RULE_CATEGORY = 'Sports' THEN SUB.ID END) AS TOTAL_SPORTS_FIT_RULES,
        COUNT(DISTINCT CASE WHEN RULE_CATEGORY = 'Merchant' THEN SUB.ID END) AS TOTAL_MERCHANT_FIT_RULES,
        --COUNT(DISTINCT CASE WHEN RULE_CATEGORY = 'Wellness' THEN SUB.ID END) AS TOTAL_WELLNESS_FIT_RULES,
        --COUNT(DISTINCT CASE WHEN RULE_CATEGORY = 'Growth' THEN SUB.ID END) AS TOTAL_GROWTH_FIT_RULES
        MIN(CASE WHEN SUB.VERSION_STATE='CURRENT' AND RLS.NAME='ROUND-UP & INVEST' THEN SUB.STATE ELSE 'NEVER' END) AS ROUND_UP_INVEST_STATUS
        --COUNT(DISTINCT CASE WHEN STATE_CHANGE_REASON = 'NEW_SUBSCRIPTION' AND VERSION_VALID_FROM >= CURRENT_DATE() - 7 THEN SUB.ID END) AS L7_FIT_SUBSCRIPTIONS,
        --COUNT(DISTINCT CASE WHEN STATE_CHANGE_REASON = 'NEW_SUBSCRIPTION' AND VERSION_VALID_FROM >= CURRENT_DATE() - 14 THEN SUB.ID END) AS L14_FIT_SUBSCRIPTIONS,
        --COUNT(DISTINCT CASE WHEN STATE_CHANGE_REASON = 'NEW_SUBSCRIPTION' AND VERSION_VALID_FROM >= CURRENT_DATE() - 28 THEN SUB.ID END) AS L28_FIT_SUBSCRIPTIONS

    FROM EPIFI_PROD_DB.DATA_LAKE.RULE_SUBSCRIPTIONS SUB
    LEFT JOIN EPIFI_PROD_DB.DATA_LAKE.RULES RLS ON SUB.RULE_ID = RLS.ID
    LEFT JOIN EPIFI_PROD_DB.DATA_LAKE.RULE_TAG_MAPPINGS TAG_MAP ON RLS.ID = TAG_MAP.RULE_ID
    LEFT JOIN (
        SELECT
            ID,
            NAME AS RULE_TYPE,
            SPLIT(PATH,'.')[0]::STRING AS RULE_CATEGORY
            --COALESCE(SPLIT(PATH,'.')[1]::STRING , SPLIT(PATH,'.')[0]::STRING) AS RULE_CATEGORY_L1
        FROM EPIFI_PROD_DB.DATA_LAKE.RULE_TAGS
        ) TAG ON TAG_MAP.TAG_ID = TAG.ID
    GROUP BY ACTOR_ID) FIT_SUB
    ON FIT_SUB.ACTOR_ID = sa.PRIMARY_ACTOR_ID


 LEFT JOIN (
     SELECT
        ACTN.ACTOR_ID,
        SUM(CASE WHEN STATUS = 'SUCCESS' AND ACTION_TYPE = 'DEPOSIT' THEN
            PARSE_JSON(EXCTN.ACTION_DATA)['depositAction']['amount']['units'] :: NUMERIC END) AS FIT_TOTAL_AMOUNT_SAVED,
        SUM(CASE WHEN STATUS = 'SUCCESS' AND ACTION_TYPE = 'DEPOSIT' AND RULE_CATEGORY = 'Sports' THEN
            PARSE_JSON(EXCTN.ACTION_DATA)['depositAction']['amount']['units'] :: NUMERIC END) AS FIT_SPORTS_FIT_AMOUNT_SAVED,
        SUM(CASE WHEN STATUS = 'SUCCESS' AND ACTION_TYPE = 'DEPOSIT' AND RULE_CATEGORY = 'Merchant' THEN
            PARSE_JSON(EXCTN.ACTION_DATA)['depositAction']['amount']['units'] :: NUMERIC END) AS FIT_MERCHANT_AMOUNT_SAVED,
        SUM(CASE WHEN STATUS = 'SUCCESS' AND ACTION_TYPE = 'DEPOSIT' AND RULE_CATEGORY = 'Wellness' THEN
            PARSE_JSON(EXCTN.ACTION_DATA)['depositAction']['amount']['units'] :: NUMERIC END) AS FIT_WELLNESS_AMOUNT_SAVED,
        SUM(CASE WHEN STATUS = 'SUCCESS' AND ACTION_TYPE = 'DEPOSIT' AND RULE_CATEGORY = 'Growth' THEN
            PARSE_JSON(EXCTN.ACTION_DATA)['depositAction']['amount']['units'] :: NUMERIC END) AS FIT_GROWTH_AMOUNT_SAVED,
        MAX(CASE WHEN STATUS = 'SUCCESS' AND ACTION_TYPE = 'DEPOSIT' THEN DATE(EXCTN.CREATED_AT) END) AS FIT_LAST_SAVED_DATE,
        /*SUM(CASE WHEN STATUS = 'SUCCESS' AND ACTION_TYPE = 'DEPOSIT' AND ACTN.CREATED_AT >= CURRENT_DATE() - 7
            THEN PARSE_JSON(EXCTN.ACTION_DATA)['depositAction']['amount']['units'] :: NUMERIC ELSE 0 END) AS L7_FIT_SAVINGS,
        SUM(CASE WHEN STATUS = 'SUCCESS' AND ACTION_TYPE = 'DEPOSIT' AND ACTN.CREATED_AT >= CURRENT_DATE() - 14
            THEN PARSE_JSON(EXCTN.ACTION_DATA)['depositAction']['amount']['units'] :: NUMERIC ELSE 0 END) AS L14_FIT_SAVINGS,
        SUM(CASE WHEN STATUS = 'SUCCESS' AND ACTION_TYPE = 'DEPOSIT' AND ACTN.CREATED_AT >= CURRENT_DATE() - 28
            THEN PARSE_JSON(EXCTN.ACTION_DATA)['depositAction']['amount']['units'] :: NUMERIC ELSE 0 END) AS L28_FIT_SAVINGS*/
        COALESCE(COUNT(CASE WHEN UPPER(RLS.NAME) = 'HEALTHY, WEALTHY, WISE' AND DATEDIFF(DAY,EXCTN.CREATED_AT, CURRENT_DATE())=2 AND STATUS = 'SUCCESS' AND ACTION_TYPE = 'DEPOSIT' THEN EXCTN.ID END),0) AS TXN_HEALTHY_WEALTHY_WISE_PREV_DAY, -- check the rule name -- FIT data has 1 day lag 0, so might be false for 1 day
        COALESCE(COUNT(CASE WHEN UPPER(RLS.NAME) LIKE '%SHOP TILL YOU DROP%' AND DATEDIFF(DAY,EXCTN.CREATED_AT, CURRENT_DATE())=2 AND STATUS = 'SUCCESS' AND ACTION_TYPE = 'DEPOSIT' THEN EXCTN.ID END),0) AS TXN_SHOP_TILL_YOU_DROP_PREV_DAY,
        COALESCE(SUM(CASE WHEN STATUS = 'SUCCESS' AND ACTION_TYPE = 'DEPOSIT' AND DATEDIFF(DAY,EXCTN.CREATED_AT,CURRENT_DATE)<=30 THEN
            PARSE_JSON(EXCTN.ACTION_DATA)['depositAction']['amount']['units'] :: NUMERIC END),0) AS FIT_AMOUNT_SAVED_LAST30DAYS,
        COUNT(CASE WHEN STATUS = 'SUCCESS' AND ACTION_TYPE = 'DEPOSIT' THEN EXCTN.ID END) AS FIT_SAVED_COUNT
    FROM EPIFI_PROD_DB.DATA_LAKE.ACTION_EXECUTIONS EXCTN
    LEFT JOIN EPIFI_PROD_DB.DATA_LAKE.FITTT_ACTIONS ACTN
    ON EXCTN.EXECUTION_ID = ACTN.EXECUTION_ID
    LEFT JOIN EPIFI_PROD_DB.DATA_LAKE.RULE_SUBSCRIPTIONS SUB
    ON ACTN.SUBSCRIPTION_ID = SUB.ID AND SUB.STATE_CHANGE_REASON = 'NEW_SUBSCRIPTION'
    LEFT JOIN EPIFI_PROD_DB.DATA_LAKE.RULES RLS
    ON SUB.RULE_ID  = RLS.ID
    LEFT JOIN EPIFI_PROD_DB.DATA_LAKE.RULE_TAG_MAPPINGS TAG_MAP ON RLS.ID = TAG_MAP.RULE_ID
    LEFT JOIN (
    SELECT
        ID,
        NAME AS RULE_TYPE,
        SPLIT(PATH,'.')[0]::STRING AS RULE_CATEGORY
        --COALESCE(SPLIT(PATH,'.')[1]::STRING , SPLIT(PATH,'.')[0]::STRING) AS RULE_CATEGORY_L1
    FROM EPIFI_PROD_DB.DATA_LAKE.RULE_TAGS
    ) TAG ON TAG_MAP.TAG_ID = TAG.ID
    GROUP BY ACTN.ACTOR_ID) FIT_SAVE
    ON FIT_SAVE.ACTOR_ID = sa.PRIMARY_ACTOR_ID

LEFT JOIN (SELECT REFEREE_ACTOR_ID FROM EPIFI_PROD_DB.data_lake.FINITE_CODE_CLAIMS)REFEREE
    ON REFEREE.REFEREE_ACTOR_ID = sa.PRIMARY_ACTOR_ID

LEFT JOIN (select onb.ACTOR_ID, onb.COMPLETED_AT_IST,ACCOUNT_CREATED_AT_IST, ACQ_CHANNEL_L3,ACQ_CHANNEL_L2,ACQ_CHANNEL_L1,
                  row_number() over (partition by to_date(onb.ACCOUNT_CREATED_AT_IST) order by onb.ACCOUNT_CREATED_AT_IST asc) r
            from EPIFI_PROD_DB.BASIC_ANALYTICS.ONB_USER_V2 onb
            where ACCOUNT_ID is not null
order by 3 desc
    ) onb
    ON ONB.ACTOR_ID = sa.PRIMARY_ACTOR_ID
LEFT JOIN (
        SELECT
            o.ACTOR_ID,
            max(case when current_kyc_level = 'MIN_KYC' and account_created_at_ist is not null and v.actor_id is null then 0 else 1 end) as attempted_kyc
        from
            "EPIFI_PROD_DB"."BASIC_ANALYTICS"."ONB_USER_V2" o
            left join "EPIFI_PROD_DB"."DATA_LAKE"."VKYC_SUMMARIES" v
            on o.actor_id = v.actor_id
        GROUP BY 1
)kyc_attempt
ON sa.PRIMARY_ACTOR_ID = kyc_attempt.ACTOR_ID
LEFT JOIN (
        SELECT
            ACTOR_ID,
            MAX(TIMESTAMP_IST) AS LAST_SEARCH_SUBMITTED
        FROM EPIFI_PROD_DB.BASIC_ANALYTICS.search
        WHERE EVENT = 'SubmittedSearch'
        GROUP BY 1
    ) SEARCH
ON sa.PRIMARY_ACTOR_ID = SEARCH.ACTOR_ID
LEFT JOIN  (
    select actor_id,case when is_unlocked='True' then 'Yes' else 'No' end as IS_REFFERAL_ELIGIBLE, datediff(day,unlocked_at_ist,current_date()) as days_since_referral_eligibile
    from "EPIFI_PROD_DB"."DATA_LAKE"."INAPPREFERRAL_UNLOCKS"

) REF_ELG
ON sa.PRIMARY_ACTOR_ID = REF_ELG.ACTOR_ID
LEFT JOIN
    (select ACTOR_ID,coalesce(current_balance,0) as NET_BALA
    from EPIFI_PROD_DB.data_lake.accrual_accounts
        where actor_id is not null ) as fi_coin_balance
on fi_coin_balance.ACTOR_ID=sa.PRIMARY_ACTOR_ID
LEFT JOIN
    (SELECT ACTOR_ID, 'YES' AS UNINSTALLED_FLAG FROM EPIFI_ANALYST_DB.BA_ANALYTICS.UNINSTALL_DUMP GROUP BY 1,2) UD
ON UD.ACTOR_ID = sa.PRIMARY_ACTOR_ID
LEFT JOIN (
    select
        referrer_actor_id as actor_id,
        count(distinct referee_Actor_id) as referrals_code_claims ---- Coloumn Rename
    from "EPIFI_PROD_DB"."DATA_LAKE"."FINITE_CODE_CLAIMS" a
        join  EPIFI_PROD_DB.DATA_LAKE.INAPPREFERRAL_UNLOCKS u
        on u.actor_id = a.referrer_actor_id
    group by 1
    ) REF ON REF.ACTOR_ID=SA.PRIMARY_ACTOR_ID
left join
(    select
        referrer_actor_id as actor_id,
        count(distinct a.referee_Actor_id) as referrals_acc_created
    from "EPIFI_PROD_DB"."DATA_LAKE"."FINITE_CODE_CLAIMS" a
        join  EPIFI_PROD_DB.DATA_LAKE.INAPPREFERRAL_UNLOCKS u
        on u.actor_id = a.referrer_actor_id
        join  EPIFI_PROD_DB.BASIC_ANALYTICS.ONB_USER_V2 o on a.referee_Actor_id = o.actor_id where account_created_at_ist is not null
    group by 1) ref2 ON REF2.ACTOR_ID=SA.PRIMARY_ACTOR_ID
left join
( select a.actor_id,referrer_reward_given,referrer_reward_given_post_season_1,referrer_reward_given_post_season_2
from
(    select
r.actor_id as actor_id,
count(distinct r.reward_id) as referrer_reward_given
from EPIFI_PROD_DB.BASIC_ANALYTICS.REWARDS_DNA r
join  EPIFI_PROD_DB.DATA_LAKE.INAPPREFERRAL_UNLOCKS u on u.actor_id=r.actor_id
where REWARD_ACTION_DESCRIPTION in ('Earned for inviting a friend to Fi','Earned ₹300 for inviting a friend to Fi')  and date(REWARD_DETERMINATION_DATE)>=date(UNLOCKED_AT_IST)
group by 1) a
left join
(  select
r.actor_id as actor_id,
count(distinct r.reward_id) as referrer_reward_given_post_season_1
from EPIFI_PROD_DB.BASIC_ANALYTICS.REWARDS_DNA r
join  EPIFI_PROD_DB.DATA_LAKE.INAPPREFERRAL_UNLOCKS u on u.actor_id=r.actor_id
where REWARD_ACTION_DESCRIPTION in ('Earned for inviting a friend to Fi','Earned ₹300 for inviting a friend to Fi')  and date(REWARD_DETERMINATION_DATE)>=date(UNLOCKED_AT_IST)
and date(REWARD_DETERMINATION_DATE)>'2022-07-12'
AND date(REWARD_DETERMINATION_DATE)<='2022-08-31' group by 1
)b on a.actor_id = b.actor_id
left join
(  select
r.actor_id as actor_id,
count(distinct r.reward_id) as referrer_reward_given_post_season_2
from EPIFI_PROD_DB.BASIC_ANALYTICS.REWARDS_DNA r
join  EPIFI_PROD_DB.DATA_LAKE.INAPPREFERRAL_UNLOCKS u on u.actor_id=r.actor_id
where REWARD_ACTION_DESCRIPTION in ('Earned for inviting a friend to Fi','Earned ₹300 for inviting a friend to Fi')  and date(REWARD_DETERMINATION_DATE)>=date(UNLOCKED_AT_IST)
and REWARD_DETERMINATION_DATE>='2023-02-03 17:00:00.000'
AND date(REWARD_DETERMINATION_DATE)<='2023-02-13' group by 1
)c on a.actor_id = C.actor_id
)ref3 ON REF3.ACTOR_ID=SA.PRIMARY_ACTOR_ID
left join
--latest loadscreendate
(select actor_id,loadedscreen_Date from
(select row_number() OVER (PARTITION BY r.actor_id ORDER BY date(timestamp_ist) desc) as rn,r.actor_id as actor_id,date(timestamp_ist) as loadedscreen_Date
from EPIFI_PROD_DB.BASIC_ANALYTICS.REFERRAL_EVENTS_V2 r
join EPIFI_PROD_DB.DATA_LAKE.INAPPREFERRAL_UNLOCKS u on r.actor_id = u.actor_id
where is_unlocked='True' and event in ('LoadedMyReferralsScreen','LoadedNewReferralsPage')) where rn=1)ref4 on ref4.actor_id = SA.PRIMARY_ACTOR_ID
left join
--latest ClickedShareCodedate
(select actor_id,ClickedShareCode_Date from
(select row_number() OVER (PARTITION BY r.actor_id ORDER BY date(timestamp_ist) desc) as rn,r.actor_id as actor_id,date(timestamp_ist) as ClickedShareCode_Date
from EPIFI_PROD_DB.BASIC_ANALYTICS.REFERRAL_EVENTS_V2 r
join EPIFI_PROD_DB.DATA_LAKE.INAPPREFERRAL_UNLOCKS u on r.actor_id = u.actor_id
where is_unlocked='True' and event in ('ClickedShareCode','ClickedWASharebutton','ClickedMessageShareButton','ClickedCopybutton','ClickedInviteFriendsShareButton')) where rn=1)ref5 on ref5.actor_id = SA.PRIMARY_ACTOR_ID
LEFT JOIN
-- vkyc status
(
        SELECT
            o.ACTOR_ID,
            v.status as vkyc_status, date(v.updated_at_ist) as vkyc_date
        from
            "EPIFI_PROD_DB"."BASIC_ANALYTICS"."ONB_USER_V2" o
            join "EPIFI_PROD_DB"."DATA_LAKE"."VKYC_SUMMARIES" v
            on o.actor_id = v.actor_id and account_created_at_ist is not null
)vkyc_status
ON sa.PRIMARY_ACTOR_ID = vkyc_status.ACTOR_ID
LEFT JOIN (
    SELECT
        ACTOR_ID,
        MAX(CASE WHEN CARD_STATE = 'ACTIVATED' THEN 1 ELSE 0 END) AS DC_ACTIVATED
    FROM EPIFI_PROD_DB.BASIC_ANALYTICS.DEBIT_CARD
    GROUP BY 1
    ) DC_ACT
ON DC_ACT.ACTOR_ID=SA.PRIMARY_ACTOR_ID
LEFT JOIN EPIFI_ANALYST_DB.BA_ANALYTICS.REWARD_CLASSIFIER_SEGMENT as last_date_rewards_segment
on last_date_rewards_segment.ACTOR_ID=sa.PRIMARY_ACTOR_ID

LEFT JOIN (
    SELECT
        DISTINCT ACTOR_ID
    FROM EPIFI_PROD_DB.BASIC_ANALYTICS.FIT_EVENTS
    WHERE EVENT = 'JoinedFiSavingLeague'
) FI_SAVE_LEAGUE
ON FI_SAVE_LEAGUE.ACTOR_ID = sa.PRIMARY_ACTOR_ID

LEFT JOIN (    SELECT
        A.ID AS ACTOR_ID,
        DATE(FED_CUSTOMER_INFO__VENDOR_CREATION_SUCCEEDED_AT) AS CUST_CREATION_TIME,ACCESS_REVOKE_DETAILS_REASON,
        CASE WHEN ACCESS_REVOKE_DETAILS_ACCESS_REVOKE_STATUS ='ACCESS_REVOKE_STATUS_BLOCKED' THEN 'BLOCKED' ELSE 'NOT BLOCKED' END AS ACC_BLOCK_STATUS,
        CASE WHEN ACCESS_REVOKE_DETAILS_ACCESS_REVOKE_STATUS ='ACCESS_REVOKE_STATUS_BLOCKED' then DATE(TIMESTAMPADD('MINUTE','330',ACCESS_REVOKE_DETAILS_UPDATED_AT)) end as blocked_date,
        datediff('days',blocked_date,current_date()) as days_since_blocked
    FROM EPIFI_PROD_DB.DATA_LAKE.USERS U
LEFT JOIN
EPIFI_PROD_DB.DATA_LAKE.ACTORS A
ON U.ID = A.ENTITY_ID
-- where CUST_CREATION_TIME is not null
)   USER_DETAILS
ON USER_DETAILS.ACTOR_ID = sa.PRIMARY_ACTOR_ID

LEFT JOIN
(
    select
        ACTOR_ID,
        'WA PREFERENCE GIVEN' AS WA_PREF
    from "EPIFI_PROD_DB"."DATA_LAKE"."USER_COMMS_PREFERENCES"
    Where
        DELETED_AT_UNIX=0 AND
        medium='WHATSAPP' AND
        PREFERENCE='ON'
    group by 1,2
) WA_PREF
on WA_PREF.ACTOR_ID  = sa.PRIMARY_ACTOR_ID
--- mf columns
left join
(
  select actor_from_id as actor_id,
    1 as mf_lumpsum_investment_done,
    max(date(server_created_at_ist)) as last_mf_txn_date
  from epifi_prod_db.basic_analytics.transactions
  where tags like '%MUTUAL_FUND%' and tags not like '%STANDING_INSTRUCTION%'
    and date(server_created_at_ist) >= '2022-05-01'
    and transaction_status = 'SUCCESS'
  group by actor_from_id
) mf_l
on sa.primary_actor_id = mf_l.actor_id
--- p2p invest
left join
(
  select actor_id,
    max(case when status = 'INVESTOR_STATUS_CREATED' then 1 else 0 end) is_eligible_p2p,
    max(case when details_eligibility_details_failure_reasons like '%CKYC%' then 1 else 0 end) as is_closetoeligible_p2p_vkycpending,
    --max(case when details_eligibility_details_failure_reasons like '%BALANCE%' then 1 else 0) as is_closetoeligible_p2p_amountnotmet
    sum(used_investment_count) as no_of_p2p_investments,
    max(case when details_eligibility_details_failure_reasons like '%FULL_KYC%' then 1 else 0 end) as is_noteligible_minKYC,
    max(case when details_eligibility_details_failure_reasons like '%BALANCE%' then 1 else 0 end) as is_noteligible_balance
  from EPIFI_P2PINVESTMENT_LIQUILOANS_PROD.DATA_LAKE.p2p_investors
  group by actor_id
) as  p2p_invest
on sa.primary_actor_id = p2p_invest.actor_id
left join
(
    select b.actor_id,
           --sum(case when type='INVESTMENT_TRANSACTION_TYPE_INVESTMENT' then a.details_amount_units else -1*a.details_amount_units end) as used_limit
            sum(case when a.type like '%INVESTMENT' and a.status='SUCCESS' then a.details_amount_units end) as used_limit,
            max(case when a.type like '%INVESTMENT' and a.status='SUCCESS' then created_date end) as last_jump_invested
    from EPIFI_P2PINVESTMENT_LIQUILOANS_PROD.DATA_LAKE.p2p_investment_transactions a
    left join EPIFI_P2PINVESTMENT_LIQUILOANS_PROD.DATA_LAKE.p2p_investors b
    on a.investor_id=b.id
    group by b.actor_id
) Jump
on sa.primary_actor_id=Jump.actor_id
left join
(
  select actor_id, min(avg_bal_last_7_days) as avg_bal_last_7_days
  from epifi_prod_db.basic_analytics.account_history
  where upper(account_type) = 'SAVINGS_ACCOUNT' and cal_date = dateadd(day,-1,current_date())
  group by actor_id
) as  bal_7_day
on sa.primary_actor_id = bal_7_day.actor_id


left join

(select Actor_id,salary_programme_status as is_salary_account_status
from
(select Actor_id,salary_activated_once_life_time_1_0,salary_activated_till,
case when salary_activated_till<current_date then 0 else 1 end as salary_activated_now_1_0,
case when salary_activated_now_1_0=1 then 'Active' else 'In-Active' end as salary_programme_status
from
(select b.Actor_id,1 as salary_activated_once_life_time_1_0 ,max(date(active_till)) as salary_activated_till
from "EPIFI_PROD_DB"."DATA_LAKE"."SALARY_PROGRAM_ACTIVATION_HISTORY" as a
left join "EPIFI_PROD_DB"."DATA_LAKE"."SALARY_PROGRAM_REGISTRATIONS" as b
on a.salary_program_registration_id=b.ID
group by 1,2))) as salary_account_status

on sa.primary_actor_id = salary_account_status.actor_id


left join

(select actor_id,1 as verified_company_salary_not_verified
from
(select a.actor_id,employer_verified,case when b.actor_id is not null then 1 else 0 end as salary_txn_verified_1_0
from
(select distinct b.actor_id ,1 as employer_verified
from
"EPIFI_PROD_DB"."DATA_LAKE"."SALARY_PROGRAM_REGISTRATION_STAGE_DETAILS" as a
left join "EPIFI_PROD_DB"."DATA_LAKE"."SALARY_PROGRAM_REGISTRATIONS" as b
on a.salary_program_registration_id=b.ID
where stage_name='REGISTRATION_STAGE_FULL_KYC_COMPLETION') as a
left join
(select distinct actor_id from "EPIFI_PROD_DB"."DATA_LAKE"."SALARY_TXN_VERIFICATION_REQUESTS"
where verification_status='REQUEST_STATUS_VERIFIED') as b
on a.actor_id=b.actor_id)
where employer_verified=1 and salary_txn_verified_1_0=0) as verified_company_salary_not_verified
on sa.primary_actor_id = verified_company_salary_not_verified.actor_id

left join

(select distinct actor_id, 1 as received_salary_joiningbonus
from EPIFI_ANALYST_DB.BA_ANALYTICS.REWARDS_MASTER_tag
where reward_action_description='Salary Account Benefit: Joining bonus for first salary credit') as received_salary_joiningbonus
on sa.primary_actor_id = received_salary_joiningbonus.actor_id

left join

(select distinct actor_id, 1 as made_firsttxn_aftersalarycredit
from EPIFI_ANALYST_DB.BA_ANALYTICS.REWARDS_MASTER_tag
where reward_action_description='Salary Account Benefit: Reward for shopping with Fi') as made_firsttxn_aftersalarycredit
on sa.primary_actor_id=made_firsttxn_aftersalarycredit.actor_id


left join
(select distinct a.ACTOR_ID,1 as salary_banner_clicked,
case when b.actor_id is not null then 1 else 0 end as salary_clicked_upgrade_now
from
"EPIFI_PROD_DB"."DATA_LAKE"."SALARY_PROGRAM_REGISTRATIONS" as a
left join
(select distinct Actor_id,1 as salary_clicked_upgrade_now
from "EPIFI_PROD_DB"."BASIC_ANALYTICS"."SALARY_EVENTS"
where event='ClickedCTAonIntroBanner') as b
on a.ACTOR_ID=b.ACTOR_ID) as salary_banner_clicked
on sa.primary_actor_id=salary_banner_clicked.ACTOR_ID

left join
(select actor_id, tat_from_shared_to_load,tat_from_current_date_to_loaded_screen from
(select a.actor_id as actor_id, last_loaded_page,last_shared_code,datediff('day',last_shared_code,last_loaded_page) as tat_from_shared_to_load,
 datediff('day',last_loaded_page,current_date()) as tat_from_current_date_to_loaded_screen from
(select distinct u.actor_id as actor_id,max(date(timestamp_ist)) as last_loaded_page
from EPIFI_PROD_DB.DATA_LAKE.INAPPREFERRAL_UNLOCKS u
join EPIFI_PROD_DB.BASIC_ANALYTICS.REFERRAL_EVENTS_V2 e on u.actor_id = e.actor_id
where event in ('LoadedMyReferralsScreen','LoadedNewReferralsPage') and is_unlocked = 'True' group by 1
)a left join
(select distinct u.actor_id as actor_id,max(date(timestamp_ist)) as last_shared_code
from EPIFI_PROD_DB.DATA_LAKE.INAPPREFERRAL_UNLOCKS u
join EPIFI_PROD_DB.BASIC_ANALYTICS.REFERRAL_EVENTS_V2 e on u.actor_id = e.actor_id
where  event in ('ClickedShareCode','ClickedWASharebutton','ClickedMessageShareButton','ClickedCopybutton','ClickedInviteFriendsShareButton') and is_unlocked = 'True' group by 1
)b on a.actor_id =b.actor_id))
ref_drop_of on ref_drop_of.actor_id = sa.primary_actor_id
left join
(select actor_id, tat_from_started_call_to_loaded_Screen,tat_from_current_date_to_loaded_vkyc_screen from
(select a.actor_id as actor_id, last_loaded_vkyc_page,last_started_call,datediff('day',last_started_call,last_loaded_vkyc_page) as tat_from_started_call_to_loaded_Screen,
 datediff('day',last_loaded_vkyc_page,current_date()) as tat_from_current_date_to_loaded_vkyc_screen from
(select distinct u.actor_id as actor_id,max(date(timestamp_ist)) as last_loaded_vkyc_page
from EPIFI_PROD_DB.BASIC_ANALYTICS.ONB_USER_V2 u
join EPIFI_PROD_DB.BASIC_ANALYTICS.VKYC_EVENTS_V2 e on u.actor_id = e.actor_id
where event ='VKYCLandsonInfoPage' and u.current_kyc_level='MIN_KYC' group by 1
)a left join
(select distinct u.actor_id as actor_id,max(date(timestamp_ist)) as last_started_call
from EPIFI_PROD_DB.BASIC_ANALYTICS.ONB_USER_V2 u
join EPIFI_PROD_DB.BASIC_ANALYTICS.VKYC_EVENTS_V2 e on u.actor_id = e.actor_id
where event ='VKYCCallCustomerArrived' and u.current_kyc_level='MIN_KYC' group by 1
)b on a.actor_id =b.actor_id))
vkyc_drop_of on vkyc_drop_of.actor_id = sa.primary_actor_id
left join
(select distinct actor_id, case when form='PHYSICAL' then 'Y' else 'N' end as physical_flag from
(select rank() over(PARTITION BY actor_id ORDER BY CREATED_AT_IST desc) as rn,
actor_id,form
from "EPIFI_PROD_DB"."DATA_LAKE"."CARDS"
where state='ACTIVATED')where rn=1)card_form on card_form.actor_id = sa.primary_actor_id
left join
(select actor_id,delivery_date,datediff(day,delivery_date,current_Date()) as tat_from_delivery,activation_state from
(select row_number()over(partition by actor_id order by date(c.updated_at_ist) desc)as rn, c.card_id,c1.actor_id,c2.state as activation_state, date(c.updated_at_ist) as delivery_date from "EPIFI_PROD_DB"."DATA_LAKE"."CARD_TRACKING_REQUESTS" c
join "EPIFI_PROD_DB"."DATA_LAKE"."CARDS" c1 on c.card_id=c1.id
join  "EPIFI_PROD_DB"."DATA_LAKE"."CARD_DELIVERY_TRACKINGS" c2 on c2.card_id=c1.id
where delivery_state='DELIVERED'
and c1.state='ACTIVATED' and c1.form='PHYSICAL')where rn=1 order by 2 desc)
card_state on card_state.actor_id = sa.primary_actor_id
left join
(select actor_from_id as actor_id,L30_International_TXN_AMT,Lifetime_International_TXN_AMT, case when L30_International_TXN_AMT is null then 'Not Active' else 'Active' end as L30_International_Active,
case when Lifetime_International_TXN_AMT is null then 'Not Active' else 'Active' end as Lifetime_International_Active from
(select actor_from_id,
ROUND(SUM(CASE WHEN SERVER_CREATED_AT_IST >= CURRENT_DATE() - 30 and domestic_international='INTERNATIONAL' THEN TRANSACTION_AMOUNT ELSE NULL END)) AS L30_International_TXN_AMT,
ROUND(SUM(CASE WHEN domestic_international='INTERNATIONAL' THEN TRANSACTION_AMOUNT ELSE NULL END)) AS Lifetime_International_TXN_AMT
 from"EPIFI_PROD_DB"."BASIC_ANALYTICS"."TRANSACTIONS"
where actor_from_id is not null
and transaction_status='SUCCESS' and payment_protocol='CARD'
group by 1)) card_international on card_international.actor_id = sa.primary_actor_id

------Mutual Funds---------------------------------------------------------------------------------

left join
(
    select actor_id,
           count(distinct id) as no_autoinvest_rules,
           count(distinct case when subscription_state='ACTIVE' and VERSION_STATE='CURRENT' then id end) as no_active_autoinvest_rules
    from epifi_analyst_db.ba_analytics.fittt_subscriptions_view
    where category='AUTO_INVEST'
    group by actor_id
) fit_auto_invest
on sa.primary_actor_id=fit_auto_invest.actor_id

left join
(
    select actor_id,
           max(case when status='SUCCESS' then 1 else 0 end) as flag_autoinvest_executed
    from epifi_analyst_db.ba_analytics.fittt_executions_view
    where category='AUTO_INVEST'
    group by actor_id
) fit_exec_once
on sa.primary_actor_id=fit_exec_once.actor_id

left join
(
    select actor_id,
           count(distinct session_id) as no_times_landed_mfDashboard
    from epifi_prod_db.basic_analytics.mf_invest_client_events
    where date(timestamp_ist)>=current_date()-30 and event='MfPortfolioPageLoad'
    group by actor_id
) mf_land_mfdashboard
on sa.primary_actor_id=mf_land_mfdashboard.actor_id

left join
(
    select actor_id,
           case when count(distinct session_id)>=2 then 1 else 0 end as flag_landed_explore_collections
    from epifi_prod_db.basic_analytics.mf_invest_client_events
    where date(timestamp_ist)>=current_date()-3 and event='CollectionClicked'
    group by actor_id
) mf_land_collections
on sa.primary_actor_id=mf_land_collections.actor_id

left join
(
    select actor_id,
           max(case when event='FundClicked' then 1 else 0 end) as flag_viewed_fund_yesterday,
           max(case when event in ('InvestMoreClicked',' AutoInvestClicked',' InvestOneTimeClicked') then 1 else 0 end) as flag_fundpage_clicked_yesterday
    from epifi_prod_db.basic_analytics.mf_invest_client_events
    where date(timestamp_ist)=current_date()-1
    group by actor_id
) mf_yesterday_events
on sa.primary_actor_id=mf_yesterday_events.actor_id

left join
(
  select actor_id,
        1 as flag_ca_gmail_kra
  from "EPIFI_PROD_DB"."DATA_LAKE"."GMAIL_QUERY_EXEC_RESULTS"
  where query_name in ('ETMONEY_TRANSACTION', 'ZERODHA_WELCOME', 'CAMS', 'ZERODHA',
  'CAMS_STATEMENT', 'NSDL_STATEMENT', 'HDFC_SECURITIES', 'MUTUAL_FUND_OR_STOCK_INVESTOR', 'IND_MONEY',
  'PAYTM_MONEY', 'BSE_SECURITIES_BALANCE', 'GROWW_INVESTMENT', 'KFINTECH', 'MUTUAL_FUND_INVESTORS','ZERODHA_PAYMENT',
  'ZERODHA_INVESTOR','ETMONEY_REGISTERED','ETMONEY_INVESTMENT_SUCCESS','ETMONEY_INVESTMENT_REQ_PLACED','NSDL',
  'STOCK_INVESTORS','GROWW','BSE','ZERODHA_COIN') AND is_mail_present = true
  group by actor_id
) ca_gmail_kra
on sa.primary_actor_id=ca_gmail_kra.actor_id

--------------------------------------------------------------------------------------------------------------------------------------

------ Connected Accounts ------------------------------------------------------------------------------------------------------------
LEFT JOIN

(SELECT actor_id, count(DISTINCT computed_account_id) AS no_of_connected_accs
FROM
(SELECT DISTINCT COMPUTED_FROM_ACTOR_ID AS Actor_ID, computed_account_id
FROM EPIFI_PROD_DB.DATA_LAKE.AA_TRANSACTIONS
WHERE TRANSACTION_TYPE = 'DEBIT'
UNION
SELECT DISTINCT COMPUTED_TO_ACTOR_ID AS actor_id,  computed_account_id
FROM EPIFI_PROD_DB.DATA_LAKE.AA_TrANSACTIONS
WHERE TRANSACTION_TYPE = 'CREDIT' ) D
GROUP BY 1) AS connected_accounts ON sa.primary_actor_id = connected_accounts.actor_id
-----------------------------------------------------------------------------------------------------------------------------
LEFT JOIN
-------add funds bank Ac ----------------------------------------------------------------------------------------------------
(SELECT *, ROW_NUMBER() OVER (PARTITION BY actor_id ORDER BY total_amt_added desc)  AS rnk
FROM
(SELECT actor_to_id AS actor_id, FROM_BANK_NAME, sum(TRANSACTION_AMOUNT) AS total_amt_added
FROM EPIFI_PROD_DB.BASIC_ANALYTICS.TRANSACTIONS
WHERE ORDER_WORKFLOW IN ('ADD_FUNDS', 'ADD_FUNDS_COLLECT')
AND CREDIT_DEBIT <> 'Debit'
AND FROM_BANK_NAME IS NOT null
AND TRANSACTION_STATUS = 'SUCCESS'
GROUP BY 1,2)
qualify rnk=1) Bank_used_to_add_funds ON sa.primary_actor_id = Bank_used_to_add_funds.actor_id
-----------------------------------------------------------------------------------------------------------------

LEFT JOIN
(
WITH ACCOUNT_DEBIT_TXNS_MONTH AS (
    SELECT
        ACCOUNT_FROM_ID ,
        DISPLAY_CATEGORY ,
        TRANSACTION_AMOUNT,
        SERVER_CREATED_AT_IST
    FROM EPIFI_PROD_DB.BASIC_ANALYTICS."TRANSACTIONS"
    WHERE CREDIT_DEBIT IN ('Debit','Fi->Fi') AND TRANSACTION_STATUS = 'SUCCESS' AND SENDER_TYPE_GENERAL = 'USER'
    AND IFNULL(DATE(DEBITED_AT_IST) ,DATE(SERVER_CREATED_AT_IST)) >= DATE_TRUNC('MONTH', DATEADD('MONTH',-1,LAST_DAY(CURRENT_DATE)))
    AND IFNULL(DATE(DEBITED_AT_IST) ,DATE(SERVER_CREATED_AT_IST)) <= LAST_DAY(DATEADD('MONTH',-1,LAST_DAY(CURRENT_DATE)))
    ),
ACCOUNT_TOTAL AS
    (
    SELECT
        ACCOUNT_FROM_ID,
        MONTHNAME(MAX(SERVER_CREATED_AT_IST)) AS MONTH_NAME,
        SUM(TRANSACTION_AMOUNT) AS TOTAL_DEBIT
    FROM ACCOUNT_DEBIT_TXNS_MONTH
    GROUP BY ACCOUNT_FROM_ID
    ),
ACCOUNT_CATEGORY AS
    (
    SELECT
        ACCOUNT_FROM_ID,
        DISPLAY_CATEGORY ,
        SUM(TRANSACTION_AMOUNT) AS CATEGORY_DEBIT
    FROM ACCOUNT_DEBIT_TXNS_MONTH
    GROUP BY ACCOUNT_FROM_ID, DISPLAY_CATEGORY
    ),
ACCOUNT_CATEGORY_SHARE AS
    (
    SELECT
        C.*,
        T.TOTAL_DEBIT,
        T.MONTH_NAME,
        ROUND(C.CATEGORY_DEBIT / T.TOTAL_DEBIT * 100) AS CATEGORY_DEBIT_SHARE
    FROM ACCOUNT_CATEGORY C
    INNER JOIN ACCOUNT_TOTAL T ON C.ACCOUNT_FROM_ID = T.ACCOUNT_FROM_ID
    ),
ACCOUNT_CATEGORY_SHARE_MAX_CATEGORY AS
    (
    SELECT
        *
    FROM ACCOUNT_CATEGORY_SHARE
    WHERE DISPLAY_CATEGORY NOT IN ('MONEY_TRANSFER_DEBIT','SELF_TRANSFER_DEBIT','MISCELLANEOUS','SELF_TRANSFER_CREDIT','MONEY_TRANSFER_CREDIT')
    QUALIFY ROW_NUMBER() OVER(PARTITION BY ACCOUNT_FROM_ID ORDER BY CATEGORY_DEBIT DESC) = 1
    ),
ACTOR_CATEGORY_SHARE_MAX_CATEGORY AS (
SELECT
    A.PRIMARY_ACTOR_ID AS ACTOR_ID ,
    MONTH_NAME,
    ROUND(TOTAL_DEBIT) AS TOTAL_DEBIT,
    DISPLAY_CATEGORY,
    ROUND(CATEGORY_DEBIT) AS CATEGORY_DEBIT,
    CATEGORY_DEBIT_SHARE
FROM ACCOUNT_CATEGORY_SHARE_MAX_CATEGORY S
INNER JOIN EPIFI_PROD_DB.BASIC_ANALYTICS."ACCOUNT" A
ON S.ACCOUNT_FROM_ID = A.ACCOUNT_ID AND A.ACCOUNT_TYPE  = 'SAVINGS_ACCOUNT' )
SELECT
*
FROM ACTOR_CATEGORY_SHARE_MAX_CATEGORY
) LAST_MONTH_SPENDS ON SA.PRIMARY_ACTOR_ID = LAST_MONTH_SPENDS.ACTOR_ID
------------------------------------------------------------------------------
--WEEKLY SPENDS AND ACCOUNT_CATEGORY
LEFT JOIN (

WITH ACCOUNT_DEBIT_TXNS_WEEK AS (
    SELECT
        ACCOUNT_FROM_ID ,
        DISPLAY_CATEGORY ,
        TRANSACTION_AMOUNT,
        SERVER_CREATED_AT_IST
    FROM EPIFI_PROD_DB.BASIC_ANALYTICS."TRANSACTIONS"
    WHERE CREDIT_DEBIT IN ('Debit','Fi->Fi') AND TRANSACTION_STATUS = 'SUCCESS' AND SENDER_TYPE_GENERAL = 'USER'
    AND IFNULL(DATE(DEBITED_AT_IST) ,DATE(SERVER_CREATED_AT_IST)) >= DATE_TRUNC('WEEK', DATEADD('WEEK',-1,LAST_DAY(CURRENT_DATE,'WEEK')))
    AND IFNULL(DATE(DEBITED_AT_IST) ,DATE(SERVER_CREATED_AT_IST)) <= DATE_TRUNC('WEEK', DATEADD('WEEK',-1,LAST_DAY(CURRENT_DATE,'WEEK'))) + 6
    ),
ACCOUNT_TOTAL AS
    (
    SELECT
        ACCOUNT_FROM_ID,
        SUM(TRANSACTION_AMOUNT) AS TOTAL_DEBIT
    FROM ACCOUNT_DEBIT_TXNS_WEEK
    GROUP BY ACCOUNT_FROM_ID
    ),
ACCOUNT_CATEGORY AS
    (
    SELECT
        ACCOUNT_FROM_ID,
        DISPLAY_CATEGORY ,
        SUM(TRANSACTION_AMOUNT) AS CATEGORY_DEBIT
    FROM ACCOUNT_DEBIT_TXNS_WEEK
    GROUP BY ACCOUNT_FROM_ID, DISPLAY_CATEGORY
    ),
ACCOUNT_CATEGORY_SHARE AS
    (
    SELECT
        C.*,
        T.TOTAL_DEBIT,
        ROUND(C.CATEGORY_DEBIT / T.TOTAL_DEBIT * 100) AS CATEGORY_DEBIT_SHARE
    FROM ACCOUNT_CATEGORY C
    INNER JOIN ACCOUNT_TOTAL T ON C.ACCOUNT_FROM_ID = T.ACCOUNT_FROM_ID
    ),
ACCOUNT_CATEGORY_SHARE_MAX_CATEGORY AS
    (
    SELECT
        *
    FROM ACCOUNT_CATEGORY_SHARE
    WHERE DISPLAY_CATEGORY NOT IN ('MONEY_TRANSFER_DEBIT','SELF_TRANSFER_DEBIT','MISCELLANEOUS','SELF_TRANSFER_CREDIT','MONEY_TRANSFER_CREDIT')
    QUALIFY ROW_NUMBER() OVER(PARTITION BY ACCOUNT_FROM_ID ORDER BY CATEGORY_DEBIT DESC) = 1
    ),
ACTOR_CATEGORY_SHARE_MAX_CATEGORY AS (
SELECT
    A.PRIMARY_ACTOR_ID AS ACTOR_ID ,
    ROUND(TOTAL_DEBIT) AS TOTAL_DEBIT,
    DISPLAY_CATEGORY,
    ROUND(CATEGORY_DEBIT) AS CATEGORY_DEBIT,
    CATEGORY_DEBIT_SHARE
FROM ACCOUNT_CATEGORY_SHARE_MAX_CATEGORY S
INNER JOIN EPIFI_PROD_DB.BASIC_ANALYTICS."ACCOUNT" A
ON S.ACCOUNT_FROM_ID = A.ACCOUNT_ID AND A.ACCOUNT_TYPE  = 'SAVINGS_ACCOUNT' )
SELECT
*
FROM ACTOR_CATEGORY_SHARE_MAX_CATEGORY

) LAST_WEEK_SPENDS ON SA.PRIMARY_ACTOR_ID = LAST_WEEK_SPENDS.ACTOR_ID
left join
(
    select distinct actor_id, datediff(day,last_login,current_date()) as days_since_app_active from
                          (select actor_id,max(date(timestamp_ist)) as last_login from "EPIFI_PROD_DB"."BASIC_ANALYTICS"."HOME_EVENTS" where event = 'HomeScreenLoaded' group by 1)
) app_active on app_active.actor_id = sa.PRIMARY_ACTOR_ID
LEFT JOIN
    (
        SELECT * FROM
        (
        SELECT DISTINCT
            O.ACTOR_ID,
            TRIM(PARSE_JSON(AOI.address)['postalCode'],'"') AS PINCODE,
            PIN.STATE,
            --PIN.DISTRICT,
            CASE
                WHEN UPPER(PIN.DIVISION) LIKE '%BANGALORE%' OR UPPER(PIN.DIVISION) LIKE '%BENGALURU%' THEN 'BENGALURU'
                WHEN UPPER(PIN.DIVISION) LIKE '%CHENNAI%' THEN 'CHENNAI'
                WHEN UPPER(PIN.DIVISION) LIKE '%DELHI%' THEN 'DELHI'
                WHEN UPPER(PIN.DIVISION) LIKE '%HYDERABAD%' THEN 'HYDERABAD'
                WHEN UPPER(PIN.DIVISION) LIKE '%KOLKATA%' OR UPPER(PIN.DIVISION) LIKE '%CALCUTTA%' THEN 'KOLKATA'
                WHEN UPPER(PIN.DIVISION) LIKE '%MUMBAI%' THEN 'MUMBAI'
                WHEN UPPER(PIN.DIVISION) LIKE '%PUNE%' THEN 'PUNE'
                ELSE UPPER(PIN.DIVISION)
            END AS CITY,
            CASE
                WHEN UPPER(PIN.DIVISION) LIKE '%BANGALORE%' OR UPPER(PIN.DIVISION) LIKE '%BENGALURU%' OR
                    UPPER(PIN.DIVISION) LIKE '%CHENNAI%' OR
                    UPPER(PIN.DIVISION) LIKE '%DELHI%' OR
                    UPPER(PIN.DIVISION) LIKE '%HYDERABAD%' OR
                    UPPER(PIN.DIVISION) LIKE '%KOLKATA%' OR
                    UPPER(PIN.DIVISION) LIKE '%CALCUTTA%' OR
                    UPPER(PIN.DIVISION) LIKE '%MUMBAI%' OR
                    UPPER(PIN.DIVISION) LIKE '%PUNE%'
                THEN 'TIER 1'
                WHEN UPPER(PIN.DIVISION) IN ('AGRA','AJMER','ALIGARH','AMRAVATI','AMRITSAR','ASANSOL','AURANGABAD','BAREILLY','BELGAUM','BHAVNAGAR','BHIWANDI','BHOPAL','BHUBANESWAR','BIKANER','CHANDIGARH','DEHRADUN','DHANBAD','DURG','BHILAI','DURGAPUR','ERODE','FARIDABAD','FIROZABAD','GHAZIABAD','GORAKHPUR','GULBARGA','GUNTUR','GWALIOR','GURGAON','GUWAHATI','HUBLI','INDORE','JABALPUR','JALANDHAR','JAMMU','JAMNAGAR','JAMSHEDPUR','JHANSI','JODHPUR','KAKINADA','KANNUR','KOCHI','KOTTAYAM','KOLHAPUR','KOLLAM','KOTA','KOZHIKODE','KURNOOL','LUCKNOW','MADURAI','MALAPPURAM','MATHURA','GOA','MANGALORE','MEERUT','MORADABAD','MYSORE','NANDED','NASHIK','NELLORE','NOIDA','PALAKKAD','PATNA','PONDICHERRY','ALLAHABAD','RAIPUR','RAJKOT','RAJAHMUNDRY','RANCHI','ROURKELA','SANGLI','SILIGURI','SOLAPUR','SRINAGAR','THIRUVANANTHAPURAM','THRISSUR','TIRUCHIRAPALLI','TIRUPATI','TIRUNELVELI','TIRUPUR','TIRUVANNAMALAI','UJJAIN','BIJAPUR','VIRAR','VIJAYAWADA','VELLORE','WARANGAL','BILASPUR','HAMIRPUR','PERINTHALMANNA','PURULIA','SHIMLA','TIRUR','SURAT','VISAKHAPATNAM','COIMBATORE') OR
                UPPER(PIN.DIVISION) LIKE '%CUTTACK%' OR
                UPPER(PIN.DIVISION) LIKE '%BOKARO%' OR
                UPPER(PIN.DIVISION) LIKE '%INDORE%' OR
                UPPER(PIN.DIVISION) LIKE '%JAIPUR%' OR
                UPPER(PIN.DIVISION) LIKE '%KANPUR%' OR
                UPPER(PIN.DIVISION) LIKE '%LUDHIANA%' OR
                UPPER(PIN.DIVISION) LIKE '%SALEM%' OR
                UPPER(PIN.DIVISION) LIKE '%VADODARA%' OR
                UPPER(PIN.DIVISION) LIKE '%VARANASI%' OR
                UPPER(PIN.DIVISION) LIKE '%NAGPUR%' OR
                UPPER(PIN.DIVISION) LIKE '%AHMEDABAD%' OR
                UPPER(PIN.DIVISION) LIKE '%TRIVANDRUM%' OR
                UPPER(PIN.DIVISION) LIKE '%AMARAVATI%' OR
                UPPER(PIN.DIVISION) LIKE '%AURANGABAD%' OR
                UPPER(PIN.DIVISION) LIKE '%NASIK%' OR
                UPPER(PIN.DIVISION) LIKE '%LUCKNOW%' THEN 'TIER 2'
                WHEN PIN.DIVISION IS NULL AND STATE IS NULL THEN 'Not available'
                ELSE 'TIER 3'
            END AS CITY_TIER,
            ROW_NUMBER() OVER(PARTITION BY ACTOR_ID ORDER BY PIN_CODE) AS RNK -- created_at & lat long check for null identifiers
        FROM
            (SELECT
                ACTOR_ID,
                TRIM(PARSE_JSON(stage_details)['stage_mapping']['DEVICE_REGISTRATION']['location_token'],'"') as LOCATION_TOKEN
             FROM
               "EPIFI_PROD_DB"."DATA_LAKE"."ONBOARDING_DETAILS"
             WHERE
                CURRENT_ONBOARDING_STAGE = 'ONBOARDING_COMPLETE'
             )O
             -- LEFT JOIN
             -- EPIFI_PROD_DB.DATA_LAKE.DEVICE_LOCATIONS LOC
             -- ON O.LOCATION_TOKEN = LOC.ID
             LEFT JOIN
             (SELECT
                *
             FROM
                EPIFI_PROD_DB.DATA_LAKE.ADDRESS_FOR_IDENTIFIERS
             WHERE
                IDENTIFIER_TYPE IN ('IDENTIFIER_TYPE_LOCATION_TOKEN', 'IDENTIFIER_TYPE_LOCATION')
             )AOI
           ON O.LOCATION_TOKEN = AOI.IDENTIFIER_VALUE
                LEFT JOIN
            (
            SELECT * FROM (
            SELECT
               STATE,
               PIN_CODE,
               --DISTRICT,
               DIVISION
               ,ROW_NUMBER() OVER(PARTITION BY PIN_CODE ORDER BY UPDATED_AT DESC) AS RN
              FROM
              EPIFI_PROD_DB.DATA_LAKE.PIN_CODE_MASTER
             ) BASE
            WHERE RN=1
            )PIN
            ON PIN.PIN_CODE = trim(parse_json(AOI.address)['postalCode'],'"')
        )
        WHERE RNK=1
    ) geo
    on geo.actor_id=sa.PRIMARY_ACTOR_ID
    ---------------------------------------------------------------------
    left join
    (select distinct actor_id, case when gu_flag='True' then 'Y' else 'N' end as good_user_flag from
    (select distinct actor_id,   eval_date, gu_flag from "EPIFI_PROD_DB"."BASIC_ANALYTICS"."GOOD_USER"
    where eval_date in (select max(eval_date) from  "EPIFI_PROD_DB"."BASIC_ANALYTICS"."GOOD_USER")
    ))
     gu_flag on gu_flag.actor_id=sa.PRIMARY_ACTOR_ID
    left join
    (
        select user_layer, id
        from EPIFI_PROD_DB.DATA_LAKE.ACTORS
          where type = 'USER'
        group by 1,2
    )ab_data
    on sa.PRIMARY_ACTOR_ID = ab_data.id
------- user first 7 days ----
left join
(
    select a.actor_id, account_date as onb_date,
      nvl(credit_amt_first_7_days,0) as credit_amt_first_7_days ,
      nvl(qr_debit_first_7_days,0) as qr_debit_first_7_days,
      nvl(p2m_first_7_days,0) as p2m_first_7_days
    from
    (
      select actor_id, date(account_created_at_ist) as account_date
      from epifi_prod_db.basic_analytics.onb_user_v2
      where date(account_created_at_ist) is not null
    )a
    left join
    (
      select x.actor_id, sum(y.txn_amt) as credit_amt_first_7_days
      from
      (
        select actor_id, date(account_created_at_ist) as account_date
        from epifi_prod_db.basic_analytics.onb_user_v2
        where date(account_created_at_ist) is not null
      )x
      left join
      (
        select actor_to_id as actor_id, date(server_created_at_ist) as txn_date,
          sum(transaction_amount) as txn_amt
        from epifi_prod_db.basic_analytics.transactions
        where account_to_id like '%SV%' and transaction_status = 'SUCCESS'
          and credit_debit <> 'Debit'
          and txn_type not in ('Interest', 'Rewards SD', 'Rewards SA')
        group by actor_to_id, date(server_created_at_ist)
      )y
      on x.actor_id = y.actor_id and y.txn_date between x.account_date and dateadd(day,7,x.account_date)
      group by x.actor_id
    )b
    on a.actor_id = b.actor_id
    left join
    (
      select x.actor_id,
        sum(y.qr_txn) as qr_debit_first_7_days,
        sum(y.p2m_txn) as p2m_first_7_days
      from
      (
        select actor_id, date(account_created_at_ist) as account_date
        from epifi_prod_db.basic_analytics.onb_user_v2
        where date(account_created_at_ist) is not null
      )x
      left join
      (
        select actor_from_id as actor_id,
          date(server_created_at_ist) as txn_date,
          count(distinct case when ui_entry_point = 'SECURE_QR_CODE' then transaction_id end) as qr_txn,
          count(distinct case when p2p_p2m = 'P2M'
              and lower(raw_notification_details) not like '%paytmqr%'
              and ui_entry_point <> 'SECURE_QR_CODE' then transaction_id end) as p2m_txn
        from epifi_prod_db.basic_analytics.transactions
        where account_from_id like '%SV%' and transaction_status = 'SUCCESS'
          and credit_debit <> 'Credit'
          and txn_type not in ('Interest', 'Rewards SD', 'Rewards SA')
        group by actor_from_id, date(server_created_at_ist)
      )y
      on x.actor_id = y.actor_id and y.txn_date between x.account_date and dateadd(day,7,x.account_date)
      group by x.actor_id
    )c
    on a.actor_id = c.actor_id
)user_first_7
on SA.PRIMARY_ACTOR_ID = user_first_7.actor_id
    --------------------- for pre-approved loans -------------
    LEFT JOIN
    (SELECT
        L.OFFER_ACTOR_ID AS PAL_ACTOR_ID,
        L.LOAN_OFFER_ELIGIBILITY_APPROVED_FLAG,
        DATE(L.LOAN_OFFER_ELIGIBILITY_APPROVED_AT_IST) AS LOAN_OFFER_ELIGIBILITY_APPROVED_DATE,
        DATE(L.LOAN_OFFER_ELIGIBILITY_SENT_AT_IST) AS LOAN_OFFER_ELIGIBILITY_SENT_DATE,
        TIMESTAMPDIFF('day',DATE(L.LOAN_OFFER_ELIGIBILITY_APPROVED_AT_IST),CURRENT_DATE()) AS DAYS_SINCE_OFFER_APPROVAL,
        L.ACTOR_ID_STARTERS,
        L.OFFER_ID_STARTERS,
        L.LOAN_APP_JOURNEY_STARTED_DATE,
      	TIMESTAMPDIFF('day',DATE(L.LOAN_APP_JOURNEY_STARTED_DATE),CURRENT_DATE()) AS DAYS_SINCE_LOAN_APP_JOURNEY_STARTED,
        CASE WHEN L.OFFER_ID_STARTERS IS NOT NULL THEN 'YES' ELSE 'NO' END AS LOAN_APP_JOURNEY_STARTED_FLAG,
        CASE WHEN L.LOAN_ACCOUNT_TABLE_ID IS NOT NULL THEN 'YES' ELSE 'NO' END AS LOAN_DISBURSAL_FLAG,
        TIMESTAMPDIFF('day',L.LOAN_ACCOUNT_CREATED_AT_IST,CURRENT_DATE()) AS DAYS_SINCE_LAST_LOAN,
        L.OFFER_MAX_LOAN_AMOUNT,
        L.OFFER_INTEREST_RATE_PERC,
        L.IS_OFFER_EXPIRED_FLAG AS IS_LOAN_OFFER_EXPIRED_FLAG,
        L.IS_LATEST_ELIGIBLE_OFFER_FLAG
    FROM
        EPIFI_ANALYST_DB.BA_ANALYTICS.LOS_LOAN_MASTER L
    WHERE
        L.LOAN_OFFER_ELIGIBILITY_APPROVED_FLAG = 'YES'
        AND IS_LOAN_OFFER_EXPIRED_FLAG = 'NO'
        ) PAL
        ON SA.PRIMARY_ACTOR_ID = PAL.PAL_ACTOR_ID
        --------------------- for lending re-engagment -------------
        LEFT JOIN
        (
        SELECT
			ACTOR_ID AS LENDING_RENG_ACTOR_ID,
			LENDING_ELIGIBLE_USER_FLAG,
			PL_ELIGIBLE_BASE_FLAG,
			CC_ELIGIBLE_BASE_FLAG,
			APP_ACTIVE_L1D,
			APP_ACTIVE_L7D,
			APP_ACTIVE_L14D,
			APP_ACTIVE_L30D,
			APP_ACTIVE_L60D,
			APP_ACTIVE_L90D,
			TXN_ACTIVE_L1D,
			TXN_ACTIVE_L7D,
			TXN_ACTIVE_L14D,
			TXN_ACTIVE_L30D,
			TXN_ACTIVE_L60D,
			TXN_ACTIVE_L90D
		FROM
			EPIFI_ANALYST_DB.BA_ANALYTICS.LENDING_BASE_PROFILE_RE_ENGAGMENT_PROGRAM_BASE_FILE
        )LEND_RENG
        ON SA.PRIMARY_ACTOR_ID = LEND_RENG.LENDING_RENG_ACTOR_ID
---------
left join
(   select actor_id, IFNULL(last_reward_date,'1970-01-01') AS last_referral_reward_date from
  (select actor_id, max(date(REWARD_DETERMINATION_DATE)) as last_reward_date from EPIFI_PROD_DB.BASIC_ANALYTICS.REWARDS_DNA
 where REWARD_ACTION_DESCRIPTION ='Earned for inviting a friend to Fi'
group by 1))referral_TAT on SA.PRIMARY_ACTOR_ID = referral_TAT.ACTOR_ID
--------
left join
(
    select actor_from_id as actor_id,
    sum(distinct case when display_category in ('FOOD_DRINKS','GROCERIES') then transaction_amount end) as food_groceries_last_month,
    sum(distinct case when display_category = 'SHOPPING' then transaction_amount end) as shop_last_month,
    sum(distinct case when display_category = 'COMMUTE' then transaction_amount end) as commute_last_month,
    sum(distinct case when display_category = 'PERSONAL_CARE' then transaction_amount end) as personal_care_last_month,
    sum(distinct case when display_category = 'SPORTS_GAMES' then transaction_amount end) as gaming_last_month
    from epifi_prod_db.basic_analytics.transactions
    where to_varchar(server_created_at_ist,'YYYYMM') =  to_varchar(dateadd(month,-1,current_date()),'YYYYMM')
     and credit_debit in ('Debit')
     and transaction_status = 'SUCCESS'
     and account_from_id like '%SV%'
     and display_category in ('FOOD_DRINKS','GROCERIES','COMMUTE','SHOPPING','PERSONAL_CARE','SPORTS_GAMES')
    group by actor_from_id
)spends
on SA.PRIMARY_ACTOR_ID = spends.ACTOR_ID
---------------- sal 1% campaign cohort-----------------
LEFT JOIN
(select  distinct actor_id, 'Not_applicable' AS sal_one_perc_campaign_flag
    from "EPIFI_PROD_DB"."DATA_LAKE"."SALARY_PROGRAM_ACTIVATION_HISTORY" as a
    left join "EPIFI_PROD_DB"."DATA_LAKE"."SALARY_PROGRAM_REGISTRATIONS" as b
    on a.salary_program_registration_id=b.id
    WHERE date(dateadd(minute, 330, a.created_at)) <= '2023-01-31')sal_one_perc_campaign
    on SA.PRIMARY_ACTOR_ID = sal_one_perc_campaign.actor_id

----------------- USER STATUS LABEL-----------------------
left join
(SELECT base_actor_id, kyc_level, APP_OPENS_60, days_since_account_created, first_28d_eod_bal, on_app_debit_txns, off_app_debit_txns, total_inc_cred,
CASE
    when Account_Open_Flag = '<15' then 'New User'
    when pay_30_count = 1 then 'Active'
    when (pay_30_count = 0 and pay_60_count = 1) then 'ATBI'
    when (pay_30_count = 0 and pay_60_count = 0 and pay_life_count = 1) then 'Inactive'
    when (pay_30_count = 0 and pay_60_count = 0 and pay_life_count = 0) then 'Zero Txn User'
    else 'no_cat'
END AS USER_STATUS

FROM (
SELECT
    base.ACTOR_ID AS base_actor_id,
    base.kyc_level AS kyc_level,
    base.acnt_open_flag AS Account_Open_Flag,
    base.days_since_acnt_created as days_since_account_created,
    COALESCE(EOD.EOD_BALANCE_28D, 0) AS first_28d_eod_bal,
    COALESCE(SUM(app_60.app_opens),0) APP_OPENS_60,
    COALESCE(SUM(transactions_debit.debit_txns),0) as total_debit_txns,
    COALESCE(SUM(transactions_debit.debit_txns_on_app),0) as on_app_debit_txns,
    COALESCE(SUM(transactions_debit.debit_txns - transactions_debit.debit_txns_on_app),0) as off_app_debit_txns,
    CASE WHEN COALESCE(SUM(pay_30.CNT),0) > 0 THEN 1 ELSE 0 END AS PAY_30_COUNT,
    CASE WHEN COALESCE(SUM(pay_60.CNT),0) > 0 THEN 1 ELSE 0 END AS PAY_60_COUNT,
    CASE WHEN COALESCE(SUM(pay_life.CNT),0) > 0 THEN 1 ELSE 0 END AS PAY_LIFE_COUNT,
    COALESCE(SUM(inc_cred_life.Incoming_Credit_Life), 0) AS total_inc_cred

FROM (
SELECT
actor_id,
kyc_level,
CASE WHEN datediff('days',account_created_at_ist, getdate())>15 THEN '15+' ELSE '<15' END AS acnt_open_flag,
datediff('days', date(account_created_at_ist), getdate()) as days_since_acnt_created,
account_id

FROM EPIFI_PROD_DB.BASIC_ANALYTICS.ONB_USER_V2
WHERE account_created_state='CREATED'
)base


LEFT JOIN (
SELECT
actor_id,
COALESCE(count(event_id),0) as app_opens

FROM EPIFI_PROD_DB.BASIC_ANALYTICS.GENERAL_EVENTS
where event = 'OpenedApp'
AND datediff('days', date(TIMESTAMP_IST), getdate())<=59
GROUP BY 1) app_60
ON app_60.actor_id = base.actor_id


LEFT JOIN (
SELECT
ACTOR_FROM_ID,
COALESCE(COUNT(transaction_id),0) AS CNT

FROM EPIFI_PROD_DB.BASIC_ANALYTICS.TRANSACTIONS
WHERE TXN_TYPE = 'Pay'
AND TRANSACTION_STATUS = 'SUCCESS'
AND datediff('days', date(SERVER_CREATED_AT_IST), getdate())<=29
GROUP BY 1 ) pay_30
ON pay_30.ACTOR_FROM_ID = base.actor_id


LEFT JOIN (
SELECT
ACTOR_FROM_ID,
COALESCE(COUNT(transaction_id),0) AS CNT

FROM EPIFI_PROD_DB.BASIC_ANALYTICS.TRANSACTIONS
WHERE TXN_TYPE = 'Pay'
AND TRANSACTION_STATUS = 'SUCCESS'
AND datediff('days', date(SERVER_CREATED_AT_IST), getdate())<=59
GROUP BY 1 ) pay_60
ON pay_60.ACTOR_FROM_ID = base.actor_id


LEFT JOIN (
SELECT
ACTOR_FROM_ID,
COALESCE(COUNT(transaction_id),0) AS CNT

FROM EPIFI_PROD_DB.BASIC_ANALYTICS.TRANSACTIONS
WHERE TXN_TYPE = 'Pay'
AND TRANSACTION_STATUS = 'SUCCESS'
GROUP BY 1 ) pay_life
ON pay_life.ACTOR_FROM_ID = base.actor_id

LEFT JOIN
(
    select distinct actor_from_id,
    coalesce(count(distinct transaction_id),0) as debit_txns,
  	coalesce(count(distinct case when provenance in ('USER_APP','INTERNAL','THIRD_PARTY') then transaction_id end),0) as debit_txns_on_app
    from epifi_prod_db.basic_analytics.transactions
    where transaction_status = 'SUCCESS'
      and TXN_TYPE = 'Pay'
      -- and lower(credit_debit) like '%debit%'
      and lower(credit_debit) not like '%credit%'
    group by 1
) transactions_debit
on transactions_debit.actor_from_id = base.actor_id


LEFT JOIN
(Select ACCOUNT_ID_HISTORY, EOD_BALANCE_28D
FROM EPIFI_ANALYST_DB.BA_ANALYTICS.ONB_USER_ACCOUNTS_EOD_BALANCES
group by 1,2)EOD
ON base.ACCOUNT_ID = EOD.ACCOUNT_ID_HISTORY


LEFT JOIN (
SELECT
COALESCE (txn2.credit_amount, 0) Incoming_Credit_Life,
a.PRIMARY_ACTOR_ID

FROM EPIFI_PROD_DB.BASIC_ANALYTICS.ACCOUNT a

-- credit amount
LEFT JOIN (
SELECT
a.Account_ID,
SUM(COALESCE(txn.TRANSACTION_AMOUNT, 0)) as credit_amount

FROM EPIFI_PROD_DB.BASIC_ANALYTICS.ACCOUNT a
LEFT JOIN
EPIFI_PROD_DB.BASIC_ANALYTICS.TRANSACTIONS txn
ON a.PRIMARY_ACTOR_ID = txn.ACTOR_TO_ID

WHERE
 txn.RECEIVER_TYPE_GENERAL = 'USER'
AND
 txn.TRANSACTION_STATUS = 'SUCCESS'
AND
 txn.ACCOUNT_TO_ID IS NOT NULL
AND
 a.ACCOUNT_TYPE LIKE '%SAVINGS%'

GROUP BY 1 ) AS txn2
ON a.Account_id =  txn2.Account_id
WHERE a.ACCOUNT_TYPE LIKE '%SAVINGS%') inc_cred_life
ON inc_cred_life.PRIMARY_ACTOR_ID = base.ACTOR_ID

GROUP BY 1,2,3,4,5)) user_activity_status
on user_activity_status.base_actor_id = SA.PRIMARY_ACTOR_ID


----------------------------------------------

left join
(select
actor_id,
L30_FI_COINS_REDEEMED,
case
when COALESCE(L30_FI_COINS_REDEEMED,0)=0 then '0'
when COALESCE(L30_FI_COINS_REDEEMED,0)<=500 then '1_500'
when COALESCE(L30_FI_COINS_REDEEMED,0)<=1000 then '501_1000'
when COALESCE(L30_FI_COINS_REDEEMED,0)<=5000 then '1001_5000'
when COALESCE(L30_FI_COINS_REDEEMED,0)<=10000 then '5001_10k'
else '10k_plus' end as L30_FI_COINS_REDEEMED_BUCKET
from
(SELECT ACTOR_ID,SUM(COALESCE(PRICE,0)) as L30_FI_COINS_REDEEMED
FROM EPIFI_ANALYST_DB.BA_ANALYTICS.redemptions_summary
WHERE redemption_state in ('OFFER_REDEMPTION_SUCCESSFUL')
AND date(CREATED_AT)<date(current_date)
AND date(CREATED_AT)>=(date(current_date)-31)
AND ACTOR_ID IS NOT NULL
group by 1)) as l30_fi_coins_redeemed
on SA.PRIMARY_ACTOR_ID = l30_fi_coins_redeemed.actor_id

----- Salary Attributes

left join
(with
registrations as
(select distinct actor_id
from "EPIFI_PROD_DB"."DATA_LAKE"."SALARY_PROGRAM_REGISTRATIONS"),
registration_completed as
(select b.actor_id,min(date(a.created_at)) as registration_date
from
"EPIFI_PROD_DB"."DATA_LAKE"."SALARY_PROGRAM_REGISTRATION_STAGE_DETAILS" as a
left join "EPIFI_PROD_DB"."DATA_LAKE"."SALARY_PROGRAM_REGISTRATIONS" as b
on a.salary_program_registration_id=b.ID
where stage_name='REGISTRATION_STAGE_FULL_KYC_COMPLETION'
and b.actor_id is not null
group by 1),


employer_name as
(select a.actor_id,a.employer_id,coalesce(name_by_source,organization_name) as organization_name
from
(select actor_id,organization_name,employer_id
from
(select actor_id,
 replace(organization_name,'"','') as organization_name,
employer_id,
row_number()over(partition by actor_id order by updated_at desc) as instance
--count(*),count(distinct actor_id)
from epifi_prod_db.data_lake.employment_data)
where instance=1) as a
left join
"EPIFI_PROD_DB"."DATA_LAKE".employer as b
on a.employer_id=b.id ),

salary_active_today as
(select distinct b.actor_id
from "EPIFI_PROD_DB"."DATA_LAKE"."SALARY_PROGRAM_ACTIVATION_HISTORY" as a
left join "EPIFI_PROD_DB"."DATA_LAKE"."SALARY_PROGRAM_REGISTRATIONS" as b
on a.salary_program_registration_id=b.id
where date(active_till)>=current_date
and date(active_from)<=current_date),

salary_active_ever as
(select b.actor_id,min(date(active_from)) as salary_activation_date
from "EPIFI_PROD_DB"."DATA_LAKE"."SALARY_PROGRAM_ACTIVATION_HISTORY" as a
left join "EPIFI_PROD_DB"."DATA_LAKE"."SALARY_PROGRAM_REGISTRATIONS" as b
on a.salary_program_registration_id=b.id
group by 1)



select
a.actor_id,
coalesce(organization_name,'not_declared') as salary_employer_name,
case
when c.actor_id is not null then 'Salary_Active'
when d.actor_id is not null then 'Salary_InActive'
when b.actor_id is not null then 'Salary_Registration_Completed'
when a.actor_id is not null then 'Salary_Registration_Started'
else 'Salary_Registration_Not_Started' end as salary_account_status,
registration_date,
salary_activation_date,
case
when datediff('day', registration_date, current_date)>=1 and datediff('day', registration_date, current_date)<= 3 then '1_3'
when datediff('day', registration_date, current_date)>=4 and datediff('day', registration_date, current_date)<= 7 then '4_7'
when datediff('day', registration_date, current_date)>=8 and datediff('day', registration_date, current_date)<=15 then '8_15'
when datediff('day', registration_date, current_date)>=16 and datediff('day', registration_date, current_date)<=30 then '16_30'
when datediff('day', registration_date, current_date)>=31 and datediff('day', registration_date, current_date)<=60 then '31_60'
when datediff('day', registration_date, current_date)>=61 and datediff('day', registration_date, current_date)<=120 then '61_120'
when datediff('day', registration_date, current_date)>=121 then '121+' end as days_from_registration_date_bucket,


case
when datediff('day', salary_activation_date, current_date)>=1 and datediff('day', salary_activation_date, current_date)<= 3 then '1_3'
when datediff('day', salary_activation_date, current_date)>=4 and datediff('day', salary_activation_date, current_date)<= 7 then '4_7'
when datediff('day', salary_activation_date, current_date)>=8 and datediff('day', salary_activation_date, current_date)<=15 then '8_15'
when datediff('day', salary_activation_date, current_date)>=16 and datediff('day', salary_activation_date, current_date)<=30 then '16_30'
when datediff('day', salary_activation_date, current_date)>=31 and datediff('day', salary_activation_date, current_date)<=60 then '31_60'
when datediff('day', salary_activation_date, current_date)>=61 and datediff('day', salary_activation_date, current_date)<=120 then '61_120'
when datediff('day', salary_activation_date, current_date)>=121 then '121+' end as days_from_salary_activation_date_bucket



from registrations as a
left join registration_completed as b
on a.actor_id=b.actor_id
left join salary_active_today as c
on a.actor_id=c.actor_id
left join salary_active_ever as d
on a.actor_id=d.actor_id
left join employer_name as e
on a.actor_id=e.actor_id) as salary_status_reg_dates
on SA.PRIMARY_ACTOR_ID = salary_status_reg_dates.actor_id

------ post salary activation
left join
(with
salary_active_today as
(select distinct actor_id, current_date as salary_active_dt
from
"EPIFI_PROD_DB"."DATA_LAKE"."SALARY_PROGRAM_ACTIVATION_HISTORY" as a
left join "EPIFI_PROD_DB"."DATA_LAKE"."SALARY_PROGRAM_REGISTRATIONS" as b
on a.salary_program_registration_id=b.id
where active_till>=current_date
and active_from<=current_date),


debit_p2m_txn as
(select
actor_from_id as actor_id,
order_id,
date(order_created_at_ist) as dt,
transaction_amount,
1 as did_p2m_txn,
case when transaction_amount>=200 then 1 else 0 end as did_p2m_txn_200,
extract(year from date(order_created_at_ist)) as year,
extract(month from date(order_created_at_ist)) as month
from EPIFI_PROD_DB.BASIC_ANALYTICS.TRANSACTIONS
where upper(credit_debit)='DEBIT'
and upper(p2p_p2m)='P2M'
and upper(transaction_status)='SUCCESS'
and upper(sender_type_general)='USER'
and upper(payment_protocol) in ('UPI','CARD')
and extract(year from date(order_created_at_ist))=extract(year from date(current_date))
and extract(month from date(order_created_at_ist))=extract(month from date(current_date))),


raw_data as
(select
a.actor_id,
sum(coalesce(did_p2m_txn,0)) as debit_txn,
sum(coalesce(did_p2m_txn_200,0)) as did_txn_200
from salary_active_today as a
left join debit_p2m_txn as b
on a.actor_id=b.actor_id
group by 1)


select actor_id,
case when debit_txn>0 then 1 else 0 end as salary_did_txn_post_salary_credit_1_0,
case when did_txn_200>0 then 1 else 0 end as salary_did_txn_200_salary_credit_1_0
from raw_data
where actor_id is not null) as post_salary_txn
on SA.PRIMARY_ACTOR_ID = post_salary_txn.actor_id

left join
(select distinct ACTOR_ID, 1 as visited_salary_intro_page
from "EPIFI_PROD_DB"."BASIC_ANALYTICS"."SALARY_EVENTS"
where event='ViewedSalaryIntroPage'
and ACTOR_ID is not null) as salary_visited_salary_intro_page
on SA.PRIMARY_ACTOR_ID = salary_visited_salary_intro_page.actor_id

---- affluent flag
left join
(SELECT ACTOR_ID,
CASE WHEN ((credit_score_buckets in ('i. 801-850','j. 851-900'))or(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED > 0.5) and ( BUREAU_SCORE > 850.5) and ( BUREAU_SCORE <= 865.5) and ( PREMIUM_DEVICE_TYPE_Medium_Range <= 0.5)) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED > 0.5) and ( BUREAU_SCORE > 850.5) and ( BUREAU_SCORE > 865.5)) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED <= 0.5) and ( BUREAU_SCORE > 869.5) and ( BUREAU_SCORE > 872.5) ) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED <= 0.5) and ( BUREAU_SCORE > 869.5) and ( BUREAU_SCORE <= 872.5) and ( EMPLOYMENT_TYPE_CLEANED_STUDENT <= 0.5)) or
(( BUREAU_SCORE > 211.5) and ( age <= 25.5) and ( BUREAU_SCORE > 846.5) and ( BUREAU_SCORE > 868.5) and ( BUREAU_SCORE <= 872.5) ) or
(( BUREAU_SCORE > 211.5) and ( age <= 25.5) and ( BUREAU_SCORE > 846.5) and ( BUREAU_SCORE > 868.5) and ( BUREAU_SCORE > 872.5)) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED > 0.5) and ( BUREAU_SCORE > 850.5) and ( BUREAU_SCORE <= 865.5) and ( PREMIUM_DEVICE_TYPE_Medium_Range > 0.5)) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED > 0.5) and ( BUREAU_SCORE <= 850.5) and ( age > 28.5) ) or
(( BUREAU_SCORE > 211.5) and ( age <= 25.5) and ( BUREAU_SCORE > 846.5) and ( BUREAU_SCORE <= 868.5) and ( EMPLOYMENT_TYPE_CLEANED_STUDENT <= 0.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED > 0.5)) or
(( BUREAU_SCORE > 211.5) and ( age <= 25.5) and ( BUREAU_SCORE > 846.5) and ( BUREAU_SCORE <= 868.5) and ( EMPLOYMENT_TYPE_CLEANED_STUDENT <= 0.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED <= 0.5)) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED <= 0.5) and ( BUREAU_SCORE <= 869.5) and ( age > 30.5) and ( BUREAU_SCORE <= 769.5)) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED > 0.5) and ( BUREAU_SCORE <= 850.5) and ( age <= 28.5) and ( PREMIUM_DEVICE_TYPE_Medium_Range <= 0.5)) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED <= 0.5) and ( BUREAU_SCORE > 869.5) and ( BUREAU_SCORE <= 872.5) and ( EMPLOYMENT_TYPE_CLEANED_STUDENT > 0.5))) THEN 'Affluent'
else 'Not affluent' END AS aff_flag_v09,
-- affluence score
case when ((( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED > 0.5) and ( BUREAU_SCORE > 850.5) and ( BUREAU_SCORE <= 865.5) and ( PREMIUM_DEVICE_TYPE_Medium_Range <= 0.5)) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED > 0.5) and ( BUREAU_SCORE > 850.5) and ( BUREAU_SCORE > 865.5)) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED <= 0.5) and ( BUREAU_SCORE > 869.5) and ( BUREAU_SCORE > 872.5) ) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED <= 0.5) and ( BUREAU_SCORE > 869.5) and ( BUREAU_SCORE <= 872.5) and ( EMPLOYMENT_TYPE_CLEANED_STUDENT <= 0.5)) or
(( BUREAU_SCORE > 211.5) and ( age <= 25.5) and ( BUREAU_SCORE > 846.5) and ( BUREAU_SCORE > 868.5) and ( BUREAU_SCORE <= 872.5) ) or
(( BUREAU_SCORE > 211.5) and ( age <= 25.5) and ( BUREAU_SCORE > 846.5) and ( BUREAU_SCORE > 868.5) and ( BUREAU_SCORE > 872.5))) THEN '1'
----
WHEN ((( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED > 0.5) and ( BUREAU_SCORE > 850.5) and ( BUREAU_SCORE <= 865.5) and ( PREMIUM_DEVICE_TYPE_Medium_Range > 0.5)) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED > 0.5) and ( BUREAU_SCORE <= 850.5) and ( age > 28.5) ) or
(( BUREAU_SCORE > 211.5) and ( age <= 25.5) and ( BUREAU_SCORE > 846.5) and ( BUREAU_SCORE <= 868.5) and ( EMPLOYMENT_TYPE_CLEANED_STUDENT <= 0.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED > 0.5)) ) THEN '2'
----
WHEN (
(credit_score_buckets in ('i. 801-850','j. 851-900')) or
(( BUREAU_SCORE > 211.5) and ( age <= 25.5) and ( BUREAU_SCORE > 846.5) and ( BUREAU_SCORE <= 868.5) and ( EMPLOYMENT_TYPE_CLEANED_STUDENT <= 0.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED <= 0.5)) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED <= 0.5) and ( BUREAU_SCORE <= 869.5) and ( age > 30.5) and ( BUREAU_SCORE <= 769.5)) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED > 0.5) and ( BUREAU_SCORE <= 850.5) and ( age <= 28.5) and ( PREMIUM_DEVICE_TYPE_Medium_Range <= 0.5)) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED <= 0.5) and ( BUREAU_SCORE > 869.5) and ( BUREAU_SCORE <= 872.5) and ( EMPLOYMENT_TYPE_CLEANED_STUDENT > 0.5)) ) THEN '3'
---
WHEN (
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED <= 0.5) and ( BUREAU_SCORE <= 869.5) and ( age <= 30.5)) or
(( BUREAU_SCORE > 211.5) and ( age <= 25.5) and ( BUREAU_SCORE <= 846.5) and ( EMPLOYMENT_TYPE_CLEANED_STUDENT <= 0.5) and ( age > 22.5)) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED <= 0.5) and ( BUREAU_SCORE <= 869.5) and ( age > 30.5) and ( BUREAU_SCORE > 769.5)) or
(( BUREAU_SCORE > 211.5) and ( age > 25.5) and ( EMPLOYMENT_TYPE_CLEANED_SALARIED > 0.5) and ( BUREAU_SCORE <= 850.5) and ( age <= 28.5) and ( PREMIUM_DEVICE_TYPE_Medium_Range > 0.5)) or
(( BUREAU_SCORE > 211.5) and ( age <= 25.5) and ( BUREAU_SCORE > 846.5) and ( BUREAU_SCORE <= 868.5) and ( EMPLOYMENT_TYPE_CLEANED_STUDENT > 0.5))) THEN '4'
WHEN ((( BUREAU_SCORE < 211.5) or
(( BUREAU_SCORE > 211.5) and ( age <= 25.5) and ( BUREAU_SCORE <= 846.5) and ( EMPLOYMENT_TYPE_CLEANED_STUDENT <= 0.5) and ( age <= 22.5)) or
(( BUREAU_SCORE > 211.5) and ( age <= 25.5) and ( BUREAU_SCORE <= 846.5) and ( EMPLOYMENT_TYPE_CLEANED_STUDENT > 0.5) and ( age <= 22.5)) or
(( BUREAU_SCORE > 211.5) and ( age <= 25.5) and ( BUREAU_SCORE <= 846.5) and ( EMPLOYMENT_TYPE_CLEANED_STUDENT > 0.5) and ( age > 22.5) ))) THEN '5'
ELSE 'Others' end as affluence_Score_class

from(
	SELECT
    	onb.actor_id,
        date(account_created_at_ist) as dt ,
        year(account_created_at_ist) * 100 + month(account_created_at_ist) AS ym,
        age,
		CASE WHEN EMPLOYMENT_TYPE_CLEANED = 'SALARIED' THEN 1 ELSE 0 END AS EMPLOYMENT_TYPE_CLEANED_SALARIED,
        coalesce(bureau_score,0) AS bureau_score,
        CASE WHEN EMPLOYMENT_TYPE_CLEANED = 'STUDENT' THEN 1 ELSE 0 END AS EMPLOYMENT_TYPE_CLEANED_STUDENT,
        credit_score_buckets,
        CASE WHEN premiumness = 'Medium Range' THEN 1 ELSE 0 END AS PREMIUM_DEVICE_TYPE_Medium_range,
        0 AS minutes_to_check_emp,
        0 AS influence_score
    from EPIFI_PROD_DB.BASIC_ANALYTICS.ONB_USER_V2 as onb
    LEFT JOIN
    (
        select actor_id , max(cb_score) as BUREAU_SCORE
        from (
            select c.actor_id,
            	CASE WHEN C.CREDIT_SCORE IS NULL AND cb.CREDIT_SCORE_BUCKETS not like '%No%' AND cb.CREDIT_SCORE_BUCKETS not like '%Thi%'  THEN substr(CB.CREDIT_SCORE_BUCKETS,4,3) else c.credit_score end as cb_score
            from EPIFI_PROD_DB.DATA_LAKE.CREDIT_REPORTS C
            LEFT JOIN EPIFI_PROD_DB.BASIC_ANALYTICS.CREDIT_BANDS CB
        	ON C.ACTOR_ID  = CB.ACTOR_ID
            )base where cb_Score not like '%No%' and cb_Score not like '%Thi%'
        GROUP BY 1
     ) cb
	on cb.actor_id=onb.actor_id
		left join EPIFI_ANALYST_DB.BA_ANALYTICS.DEVICE_PREMIUMNESS dp
		on lower(dp.device_manufacturer)=lower(onb.device_manufacturer) and lower(dp.device_model)=lower(onb.device_model)
        where date(account_created_at_ist) is not null
        AND account_created_state='CREATED'
)as new_aff) aff_flag on aff_flag.actor_id=SA.PRIMARY_ACTOR_ID

--- new city_tier
left join (select distinct onb.actor_id,  CASE WHEN lower(ct.city_tier) is null then 'Tier C' else ct.city_tier end as city_tier_new
from EPIFI_PROD_DB.BASIC_ANALYTICS.ONB_USER_V2 onb
    LEFT JOIN (
                select *
                from EPIFI_ANALYST_DB.BA_ANALYTICS.ONB_USER_LOCATION_LANDSCAPE
                where len(FINAL_ONBOARDING_LOCATION_PINCODE)=6 and FINAL_ONBOARDING_LOCATION_PINCODE not like'%[a-zA-Z]%' and FINAL_ONBOARDING_LOCATION_PINCODE not like '%-%' and FINAL_ONBOARDING_LOCATION_PINCODE not like '%ward%'
                )loc
                on loc.actor_id = onb.actor_id
                LEFT JOIN (select city_tier, pincode from EPIFI_ANALYST_DB.BA_ANALYTICS.CITY_TIER group by 1,2)ct
                on ct.pincode=loc.FINAL_ONBOARDING_LOCATION_PINCODE
                where account_created_at_ist is not null)geo2 on geo2.actor_id=SA.PRIMARY_ACTOR_ID

-- THESE WHERE ARE FOR THE MAIN SELECT, WRITE JOINS ABOVE THIS
where sa.ACCOUNT_TYPE = 'SAVINGS_ACCOUNT'
and sa.ACCOUNT_STATUS = 'CREATED';