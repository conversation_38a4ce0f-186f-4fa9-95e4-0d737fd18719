syntax = "proto3";

package categorizer;

import "api/firefly/accounting/enums/enums.proto";
import "api/categorizer/enums.proto";
import "api/order/payment/payment_protocol.proto";
import "api/order/workflow.proto";
import "api/payment_instruments/payment_instrument.proto";
import "api/order/payment/accounting_entry_type.proto";
import "api/typesv2/actor.proto";
import "api/typesv2/bank.proto";
import "api/order/order.proto";
import "api/accounts/account_type.proto";



option go_package = "github.com/epifi/gamma/api/categorizer";
option java_package = "com.github.epifi.gamma.api.categorizer";

// represents the request params send to DS categorizer service
message DsCategorizerRequest{
  // represent the txn detail send to ds service
  repeated TransactionRecord transaction_records = 1;
}

// represent the txn detail send to ds service
message TransactionRecord{
  reserved 8, 14, 23;
  //unique identifier for a txn
  string   transaction_id = 1;
  // amount involved in doing the txn
  float    computed_amount = 2;
  // protocol used in doing the txn e.g UPI
  order.payment.PaymentProtocol payment_protocol = 3;
  // directional value depending on txn and actor (DEBIT , CREDIT)
  order.payment.AccountingEntryType   accounting_entry = 4;
  // source of origin of txn e.g USER_APP
  order.OrderProvenance provenance = 5;
  // Pi id of sender PI
  string   from_pi_id = 6;
  // Pi type of sender PI
  paymentinstrument.PaymentInstrumentType  from_pi_type = 7;
  // name of sender actor
  string   from_actor_name = 9;
  // account type of sender actor e.g SAVINGS
  accounts.Type   from_account_type = 10;
  // upi code corresponding to sender actor
  string   from_computed_unique_upi = 11;
  // Pi id of receiver PI
  string   to_pi_id = 12;
  // Pi Type of receiver PI
  paymentinstrument.PaymentInstrumentType  to_pi_type = 13;
  // name of receiver actor
  string   to_actor_name = 15;
  // account type of sender actor e.g SAVINGS
  accounts.Type   to_account_type = 16;
  // upi code corresponding to receiver actor
  string   to_computed_unique_upi = 17;
  // represent the merchant category code of receiver vpa
  string   to_vpa_mcc = 18;
  // represent the subcode of receiver vpa
  string   to_vpa_subcode = 19;
  // represent the merchant category code of receiver card
  string   to_card_mcc = 20;
  // represent the remark added to a txn
  string   trans_remarks = 21;
  // represents the ontology ids ontologies corresponding to gplace types of merchant
  repeated string gplace_ontology_ids = 24;
  // represent the bank name involved in txn
  // Given that there are two accounts involved in a transaction,
  // accounting_entry will influence whose bank account will be here
  // e.g., If accounting_entry = debit, this field would hold from_account details
  api.typesv2.Bank account_bank_name = 25;
  // Keywords or tags identified for a transaction/order
  // e.g., Keywords returned by Parser for AA transactions and which are part of aaTxnUpdate request
  repeated string keywords = 26;
  // DataChannel signifies if txn is fi txn or AA
  DataChannel data_channel = 27;
  // order_tags is list of tags that are related to the transaction. There can be different sources for these tags.
  repeated OrderTag order_tags = 28;
  // type of sender actor
  api.typesv2.ActorType from_actor_type = 29;
  // type of receiver actor
  api.typesv2.ActorType to_actor_type = 30;
  // Transaction particular is additional transaction related information received from notification from the partner banks
  string transaction_particulars = 31;
  // from actor id
  string from_actor_id = 32;
  // to actor id
  string to_actor_id = 33;
  // order workflow
  order.OrderWorkflow order_workflow = 34;
  string from_pi_name = 35;
  string to_pi_name = 36;
  // It is the category inferred by the vendor for credit card txn
  firefly.accounting.enums.TransactionCategory cc_txn_category = 37;
  // crowd aggregated categories for given txn_id and actor_id
  repeated string crowd_aggregated_ontologies = 38;
  // ds_merchant_id is given by data science team to identify a merchant.
  string to_ds_merchant_id = 39;
  string from_ds_merchant_id = 40;
  // template id of the parser, with which the AA txn was parsed. If the txn is not parsed using smart parser then this id will be empty.
  string parser_template_id = 41;
}

// represent the response from ds categorizer service
message  DsCategorizerResponse {
  // represent the categories send by ds service
  repeated Categories  categories = 1;
  //represent the version of ds categorizer model
  string service_version = 2;
}

// categories represent the combination of system and user category and system and user tag for a txn
message Categories  {
  string   transaction_id = 1;
  // system category represent the category predicted by DS model
  repeated Category system_category = 2;
  // user category represent the category added by user
  repeated Category user_category = 3;
  // system tags represent the tag predicted by DS model
  repeated string system_tags = 4;
  // user tag represent the tag added by user
  repeated string   user_tags = 5;
  // directional value depending on txn and actor (DEBIT , CREDIT)
  order.payment.AccountingEntryType accounting_entry = 6;
}

// category is combination of ontology and display name
message Category {
  reserved 1, 2;
  // confidence score generated by DS for ontologies
  float                confidence_score = 3;
  // model which predicts the category
  CategorisationSource                categorisation_source = 4;
  // unique ontology identifier
  string ontology_id = 5;
}

// ontologies
message FiOntology  {
  // first level of ontology
  categorizer.L0 L0 = 1;
  // second level of ontology
  categorizer.L1 L1 = 2;
  // third level of ontology
  categorizer.L2 L2 = 3;
  // fourth level of ontology
  categorizer.L3 L3 = 4;
}

message DsGetCategoryOntologiesRequest {
  repeated DisplayCategory display_categories = 1;
}

message DsGetCategoryOntologiesResponse {
  // each DisplayCategoryOntology corresponds to a requested display_category.
  repeated DisplayCategoryOntology categories = 1;
  //represent the version of ds categorizer model
  string service_version = 2;
}

// A display category can have multiple fi_ontologies
message DisplayCategoryOntology {
  repeated FiOntology fi_ontologies = 1;
}
