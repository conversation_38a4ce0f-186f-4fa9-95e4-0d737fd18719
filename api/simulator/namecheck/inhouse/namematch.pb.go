// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/simulator/namecheck/inhouse/namematch.proto

package inhouse

import (
	inhouse "github.com/epifi/gamma/api/vendors/inhouse"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_api_simulator_namecheck_inhouse_namematch_proto protoreflect.FileDescriptor

var file_api_simulator_namecheck_inhouse_namematch_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2f,
	0x6e, 0x61, 0x6d, 0x65, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x2f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73,
	0x65, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1b, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6e, 0x61, 0x6d,
	0x65, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73,
	0x65, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0x76, 0x0a, 0x09, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x69,
	0x0a, 0x09, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x21, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x15, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x3a, 0x01, 0x2a, 0x22, 0x0a, 0x2f,
	0x6e, 0x61, 0x6d, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x42, 0x70, 0x0a, 0x36, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f,
	0x72, 0x2e, 0x6e, 0x61, 0x6d, 0x65, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x2e, 0x69, 0x6e, 0x68, 0x6f,
	0x75, 0x73, 0x65, 0x5a, 0x36, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x2f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var file_api_simulator_namecheck_inhouse_namematch_proto_goTypes = []interface{}{
	(*inhouse.NameMatchRequest)(nil),  // 0: vendors.inhouse.NameMatchRequest
	(*inhouse.NameMatchResponse)(nil), // 1: vendors.inhouse.NameMatchResponse
}
var file_api_simulator_namecheck_inhouse_namematch_proto_depIdxs = []int32{
	0, // 0: simulator.namecheck.inhouse.NameMatch.NameMatch:input_type -> vendors.inhouse.NameMatchRequest
	1, // 1: simulator.namecheck.inhouse.NameMatch.NameMatch:output_type -> vendors.inhouse.NameMatchResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_simulator_namecheck_inhouse_namematch_proto_init() }
func file_api_simulator_namecheck_inhouse_namematch_proto_init() {
	if File_api_simulator_namecheck_inhouse_namematch_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_simulator_namecheck_inhouse_namematch_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_simulator_namecheck_inhouse_namematch_proto_goTypes,
		DependencyIndexes: file_api_simulator_namecheck_inhouse_namematch_proto_depIdxs,
	}.Build()
	File_api_simulator_namecheck_inhouse_namematch_proto = out.File
	file_api_simulator_namecheck_inhouse_namematch_proto_rawDesc = nil
	file_api_simulator_namecheck_inhouse_namematch_proto_goTypes = nil
	file_api_simulator_namecheck_inhouse_namematch_proto_depIdxs = nil
}
