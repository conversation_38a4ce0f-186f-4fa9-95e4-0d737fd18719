syntax = "proto3";

package simulator.lending.creditcard;

import "api/simulator/lending/creditcard/response.proto";
import "api/vendors/federal/lending/update_credit_card_details.proto";
import "api/vendors/m2p/lending/credit_card.proto";
import "api/vendors/m2p/lending/loan_management_system.proto";
import "api/vendors/m2p/lending/realtime_limit_check.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/simulator/lending/creditcard";
option java_package = "com.github.epifi.gamma.api.simulator.lending.creditcard";

service CreditCard {
  rpc RegisterCustomer (vendors.m2p.lending.RegisterCustomerRequest) returns (vendors.m2p.lending.RegisterCustomerResponse) {
    option (google.api.http) = {
      post: "/kyc/v2/register",
      body: "*"
    };
  }

  rpc SetPreferences (vendors.m2p.lending.SetPreferencesRequest) returns (vendors.m2p.lending.SetPreferencesResponse) {
    option (google.api.http) = {
      post: "/Yappay/business-entity-manager/v2/setPreferences",
      body: "*"
    };
  }

  rpc FetchPreference (vendors.m2p.lending.FetchPreferenceRequest) returns (vendors.m2p.lending.FetchPreferenceResponse) {
    option (google.api.http) = {
      post: "/Yappay/business-entity-manager/fetchPreference",
      body: "*"
    };
  }

  rpc GetUnmaskedCardDetails (vendors.m2p.lending.GetUnmaskedCardDetailsRequest) returns (vendors.m2p.lending.GetUnmaskedCardDetailsResponse) {
    option (google.api.http) = {
      post: "/Yappay/business-entity-manager/v2/getCardList",
      body: "*"
    };
  }

  rpc GetCardList (vendors.m2p.lending.GetCardListRequest) returns (vendors.m2p.lending.GetCardListResponse) {
    option (google.api.http) = {
      post: "/Yappay/business-entity-manager/v3/getCardList",
      body: "*"
    };
  }

  rpc GenerateCVV (vendors.m2p.lending.GenerateCVVRequest) returns (vendors.m2p.lending.GenerateCVVResponse) {
    option (google.api.http) = {
      post: "/Yappay/business-entity-manager/generateCVV",
      body: "*"
    };
  }

  rpc ManageLock (vendors.m2p.lending.ManageLockRequest) returns (vendors.m2p.lending.ManageLockResponse) {
    option (google.api.http) = {
      post: "/Yappay/business-entity-manager/block",
      body: "*"
    };
  }

  rpc RequestPhysicalCard (vendors.m2p.lending.RequestPhysicalCardRequest) returns (vendors.m2p.lending.RequestPhysicalCardResponse) {
    option (google.api.http) = {
      post: "/Yappay/business-entity-manager/requestPhysicalCard",
      body: "*"
    };
  }

  rpc UpdateCustomer (vendors.m2p.lending.UpdateCustomerRequest) returns (vendors.m2p.lending.UpdateCustomerResponse) {
    option (google.api.http) = {
      post: "/Yappay/business-entity-manager/updateentity",
      body: "*"
    };
  }

  rpc ReplaceCard (vendors.m2p.lending.ReplaceCardRequest) returns (vendors.m2p.lending.ReplaceCardResponse) {
    option (google.api.http) = {
      post: "/Yappay/business-entity-manager/replaceCard",
      body: "*"
    };
  }

  rpc GetCardBalance (vendors.m2p.lending.FetchBalanceRequest) returns (vendors.m2p.lending.FetchBalanceResponse) {
    option (google.api.http) = {
      get: "/Yappay/business-entity-manager/fetchbalance/{entity_id}"
    };
  }

  rpc FetchTransactions (vendors.m2p.lending.FetchTransactionsRequest) returns (vendors.m2p.lending.FetchTransactionsResponse) {
    option (google.api.http) = {
      get: "/Yappay/txn-manager/fetchTnxByEntityIdBetween/{customer_id}"
    };
  }

  rpc FetchUnbilledTransactions (vendors.m2p.lending.FetchUnbilledTransactionsSimulatorRequest) returns (vendors.m2p.lending.FetchUnbilledTransactionsResponse) {
    option (google.api.http) = {
      post: "/statement/customer/fetchUnbilledTxns",
      body: "customer_id"
    };
  }

  rpc FetchTransactionStatus (vendors.m2p.lending.FetchTransactionsStatusSimulatorRequest) returns (vendors.m2p.lending.FetchTransactionStatusResponse) {
    option (google.api.http) = {
      post: "/statement/txn-manager/fetch",
      body: "entity_id"
    };
  }

  rpc FetchEligibleTransactionsForEmiConversion (vendors.m2p.lending.GetEligibleTransactionsRequest) returns (vendors.m2p.lending.GetEligibleTransactionsResponse) {
    option (google.api.http) = {
      post: "/loan/eligibility/transactions",
      body: "*"
    };
  }
  rpc FetchTransactionLoanOffers (vendors.m2p.lending.PreviewLoanRequest) returns (vendors.m2p.lending.PreviewLoanResponse) {
    option (google.api.http) = {
      post: "/loan/create/preview",
      body: "*"
    };
  };

  rpc CreateLoan (vendors.m2p.lending.CreateLoanRequest) returns (vendors.m2p.lending.CreateLoanResponse) {
    option (google.api.http) = {
      post: "/loan/create",
      body: "*"
    };
  };

  rpc FetchLoanById (vendors.m2p.lending.GetLoanByIdRequest) returns (vendors.m2p.lending.GetLoanByIdResponse) {
    option (google.api.http) = {
      post: "/loan/getDetails",
      body: "*"
    };
  };

  rpc FetchLoanByStatus (vendors.m2p.lending.GetLoanByStatusRequest) returns (vendors.m2p.lending.GetLoanByStatusResponse) {
    option (google.api.http) = {
      post: "/loan/getByStatus",
      body: "*"
    };
  };

  rpc PreviewPreCloseLoan (vendors.m2p.lending.PreviewPreCloseLoanRequest) returns (vendors.m2p.lending.PreviewPreCloseLoanResponse) {
    option (google.api.http) = {
      post: "/loan/preClose/preview",
      body: "*"
    };
  };

  rpc PreCloseLoan (vendors.m2p.lending.PreCloseLoanRequest) returns (vendors.m2p.lending.PreCloseLoanResponse) {
    option (google.api.http) = {
      post: "/loan/preClose",
      body: "*"
    };
  };

  rpc CancelLoan (vendors.m2p.lending.CancelLoanRequest) returns (vendors.m2p.lending.CancelLoanResponse) {
    option (google.api.http) = {
      post: "/loan/cancel",
      body: "*"
    };
  };

  rpc FetchStatement (vendors.m2p.lending.FetchStatementListRequest) returns (vendors.m2p.lending.FetchStatementListResponse) {
    option (google.api.http) = {
      post: "/statement/customer/fetchStatementDataList",
      body: "*"
    };
  }

  rpc FetchMonthlyStatement (vendors.m2p.lending.FetchMonthlyStatementRequest) returns (vendors.m2p.lending.FetchMonthlyStatementResponse) {
    option (google.api.http) = {
      post: "/statement/customer/fetchStatementData",
      body: "*"
    };
  }

  rpc FetchDueAmount (vendors.m2p.lending.FetchDueAmountRequest) returns (vendors.m2p.lending.FetchDueAmountResponse) {
    option (google.api.http) = {
      post: "/statement/customer/fetchDue",
      body: "*"
    };
  }

  rpc UpdateStatementDate (vendors.m2p.lending.UpdateStatementDateRequest) returns (vendors.m2p.lending.UpdateStatementDateResponse) {
    option (google.api.http) = {
      post: "/statement/update/StatementDate",
      body: "*"
    };
  }


  rpc FetchLimit (vendors.m2p.lending.FetchLimitRequest) returns (vendors.m2p.lending.FetchLimitResponse) {
    option (google.api.http) = {
      post: "/Yappay/business-entity-manager/fetchLimit",
      body: "*"
    };
  }

  rpc SetCreditLimit (vendors.m2p.lending.SetCreditLimitRequest) returns (vendors.m2p.lending.SetCreditLimitResponse) {
    option (google.api.http) = {
      post: "/Yappay/business-entity-manager/setLimit",
      body: "*"
    };
  }

  rpc UpgradeLimit (vendors.m2p.lending.UpgradeLimitRequest) returns (vendors.m2p.lending.UpgradeLimitResponse) {
    option (google.api.http) = {
      post: "/Yappay/business-entity-manager/upgradeLimit",
      body: "*"
    };
  }

  rpc DevAction (DevActionRequest) returns (DevActionResponse) {
    option (google.api.http) = {
      post: "/devAction"
      body: "*"
    };
  }

  rpc RepayLoanAmount (vendors.m2p.lending.RepayLoanAmountRequest) returns (vendors.m2p.lending.RepayLoanAmountResponse) {
    option (google.api.http) = {
      post: "/Yappay/txn-manager/create",
      body: "*"
    };
  }

  rpc FetchRealtimeCreditLimit (vendors.m2p.lending.GetRealtimeLimitRequest) returns (vendors.m2p.lending.GetRealtimeLimitResponse) {
    option (google.api.http) = {
      post: "/limit-check",
      body: "*"
    };
  }

  rpc SetPinV2 (vendors.m2p.lending.SetPinPartnerSdkRequest) returns (vendors.m2p.lending.SetPinPartnerSdkResponse) {
    option (google.api.http) = {
      post: "/Yappay/business-entity-manager/setPin",
      body: "*"
    };
  }

  rpc SecuredRegisterCustomer (vendors.m2p.lending.SecuredCardRegisterCustomerRequest) returns (vendors.m2p.lending.SecuredCardRegisterCustomerResponse) {
    option (google.api.http) = {
      post: "/api/v1/customer/scc/register",
      body: "*"
    };
  }

  rpc RegisterCustomerV2 (vendors.m2p.lending.RegisterCustomerV2Request) returns (vendors.m2p.lending.RegisterCustomerV2Response) {
    option (google.api.http) = {
      post: "/api/v1/users/register",
      body: "*"
    };
  }

  rpc NonPreApprovedUnsecuredRegisterCustomer (vendors.m2p.lending.NonPreApprovedUnsecuredRegisterCustomerRequest) returns (vendors.m2p.lending.NonPreApprovedUnsecuredRegisterCustomerResponse) {
    option (google.api.http) = {
      post: "/api/v1/customer/npa/register",
      body: "*"
    };
  }

  rpc UpdateCreditCardDetails (vendors.federal.lending.CreditCardDetailsUpdateRequest) returns (vendors.federal.lending.CreditCardDetailsUpdateResponse) {
    option (google.api.http) = {
      post: "/detailsInsert",
      body: "*"
    };
  }

  rpc MarkDispute (vendors.m2p.lending.MarkDisputeRequest) returns (vendors.m2p.lending.MarkDisputeResponse) {
    option (google.api.http) = {
      post: "/statement/dispute/create",
      body: "*"
    };
  }

  rpc GetAllDisputes (vendors.m2p.lending.GetAllDisputesRequest) returns (vendors.m2p.lending.GetAllDisputesResponse) {
    option (google.api.http) = {
      post: "/statement/dispute/getAll"
      body: "*"
    };
  }

  // Generic RPC to reverse any CC fees
  // M2P doc: https://m2p-fintech.stoplight.io/docs/credit/735be7e7c979f-create-a-statement-partner-fee-post
  // NOTE: On the actual M2P API, for a valid transaction ID to be reversed, the reversal will happen & if the API gets hits again for the same transaction ID, the response extTxnId will be null and the reversal will not happen again to the customer
  // If the M2P API get hits with a non-existing transaction ID, it will still reverse the amount to the user as on M2P's side there is no such validation check(they said for cases like cashback or rewards), & if the API gets hit again with the same payload, it'll return a 500 response code
  rpc FeeReversal (vendors.m2p.lending.FeeReversalRequest) returns (vendors.m2p.lending.FeeReversalResponse) {
    option (google.api.http) = {
      post: "/statement/partner/fee/reverse"
      body: "*"
    };

  }
}
