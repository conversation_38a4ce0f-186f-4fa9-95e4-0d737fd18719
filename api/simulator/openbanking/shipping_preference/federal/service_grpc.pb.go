// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/simulator/openbanking/shipping_preference/federal/service.proto

package federal

import (
	context "context"
	federal "github.com/epifi/gamma/api/vendors/federal"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ShippingPreference_ShippingAddressUpdate_FullMethodName = "/simulator.openbanking.shipping_preference.federal.ShippingPreference/ShippingAddressUpdate"
)

// ShippingPreferenceClient is the client API for ShippingPreference service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ShippingPreferenceClient interface {
	ShippingAddressUpdate(ctx context.Context, in *federal.ShippingAddressUpdateRequest, opts ...grpc.CallOption) (*federal.ShippingAddressUpdateResponse, error)
}

type shippingPreferenceClient struct {
	cc grpc.ClientConnInterface
}

func NewShippingPreferenceClient(cc grpc.ClientConnInterface) ShippingPreferenceClient {
	return &shippingPreferenceClient{cc}
}

func (c *shippingPreferenceClient) ShippingAddressUpdate(ctx context.Context, in *federal.ShippingAddressUpdateRequest, opts ...grpc.CallOption) (*federal.ShippingAddressUpdateResponse, error) {
	out := new(federal.ShippingAddressUpdateResponse)
	err := c.cc.Invoke(ctx, ShippingPreference_ShippingAddressUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ShippingPreferenceServer is the server API for ShippingPreference service.
// All implementations should embed UnimplementedShippingPreferenceServer
// for forward compatibility
type ShippingPreferenceServer interface {
	ShippingAddressUpdate(context.Context, *federal.ShippingAddressUpdateRequest) (*federal.ShippingAddressUpdateResponse, error)
}

// UnimplementedShippingPreferenceServer should be embedded to have forward compatible implementations.
type UnimplementedShippingPreferenceServer struct {
}

func (UnimplementedShippingPreferenceServer) ShippingAddressUpdate(context.Context, *federal.ShippingAddressUpdateRequest) (*federal.ShippingAddressUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShippingAddressUpdate not implemented")
}

// UnsafeShippingPreferenceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ShippingPreferenceServer will
// result in compilation errors.
type UnsafeShippingPreferenceServer interface {
	mustEmbedUnimplementedShippingPreferenceServer()
}

func RegisterShippingPreferenceServer(s grpc.ServiceRegistrar, srv ShippingPreferenceServer) {
	s.RegisterService(&ShippingPreference_ServiceDesc, srv)
}

func _ShippingPreference_ShippingAddressUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(federal.ShippingAddressUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShippingPreferenceServer).ShippingAddressUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ShippingPreference_ShippingAddressUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShippingPreferenceServer).ShippingAddressUpdate(ctx, req.(*federal.ShippingAddressUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ShippingPreference_ServiceDesc is the grpc.ServiceDesc for ShippingPreference service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ShippingPreference_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "simulator.openbanking.shipping_preference.federal.ShippingPreference",
	HandlerType: (*ShippingPreferenceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ShippingAddressUpdate",
			Handler:    _ShippingPreference_ShippingAddressUpdate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/simulator/openbanking/shipping_preference/federal/service.proto",
}
