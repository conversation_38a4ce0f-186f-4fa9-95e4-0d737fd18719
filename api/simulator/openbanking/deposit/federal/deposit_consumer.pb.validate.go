// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/simulator/openbanking/deposit/federal/deposit_consumer.proto

package federal

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ProcessDepositRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessDepositRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessDepositRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessDepositRequestMultiError, or nil if none found.
func (m *ProcessDepositRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessDepositRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessDepositRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessDepositRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessDepositRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestType

	switch v := m.Request.(type) {
	case *ProcessDepositRequest_CreateRequest:
		if v == nil {
			err := ProcessDepositRequestValidationError{
				field:  "Request",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCreateRequest()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessDepositRequestValidationError{
						field:  "CreateRequest",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessDepositRequestValidationError{
						field:  "CreateRequest",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateRequest()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessDepositRequestValidationError{
					field:  "CreateRequest",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessDepositRequest_CloseRequest:
		if v == nil {
			err := ProcessDepositRequestValidationError{
				field:  "Request",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCloseRequest()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessDepositRequestValidationError{
						field:  "CloseRequest",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessDepositRequestValidationError{
						field:  "CloseRequest",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCloseRequest()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessDepositRequestValidationError{
					field:  "CloseRequest",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessDepositRequest_AutoCloseRequest:
		if v == nil {
			err := ProcessDepositRequestValidationError{
				field:  "Request",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAutoCloseRequest()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessDepositRequestValidationError{
						field:  "AutoCloseRequest",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessDepositRequestValidationError{
						field:  "AutoCloseRequest",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAutoCloseRequest()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessDepositRequestValidationError{
					field:  "AutoCloseRequest",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessDepositRequest_AutoRenewRequest:
		if v == nil {
			err := ProcessDepositRequestValidationError{
				field:  "Request",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAutoRenewRequest()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessDepositRequestValidationError{
						field:  "AutoRenewRequest",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessDepositRequestValidationError{
						field:  "AutoRenewRequest",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAutoRenewRequest()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessDepositRequestValidationError{
					field:  "AutoRenewRequest",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ProcessDepositRequestMultiError(errors)
	}

	return nil
}

// ProcessDepositRequestMultiError is an error wrapping multiple validation
// errors returned by ProcessDepositRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessDepositRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessDepositRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessDepositRequestMultiError) AllErrors() []error { return m }

// ProcessDepositRequestValidationError is the validation error returned by
// ProcessDepositRequest.Validate if the designated constraints aren't met.
type ProcessDepositRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessDepositRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessDepositRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessDepositRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessDepositRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessDepositRequestValidationError) ErrorName() string {
	return "ProcessDepositRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessDepositRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessDepositRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessDepositRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessDepositRequestValidationError{}

// Validate checks the field values on ProcessDepositResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessDepositResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessDepositResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessDepositResponseMultiError, or nil if none found.
func (m *ProcessDepositResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessDepositResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessDepositResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessDepositResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessDepositResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessDepositResponseMultiError(errors)
	}

	return nil
}

// ProcessDepositResponseMultiError is an error wrapping multiple validation
// errors returned by ProcessDepositResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessDepositResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessDepositResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessDepositResponseMultiError) AllErrors() []error { return m }

// ProcessDepositResponseValidationError is the validation error returned by
// ProcessDepositResponse.Validate if the designated constraints aren't met.
type ProcessDepositResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessDepositResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessDepositResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessDepositResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessDepositResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessDepositResponseValidationError) ErrorName() string {
	return "ProcessDepositResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessDepositResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessDepositResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessDepositResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessDepositResponseValidationError{}

// Validate checks the field values on AutoCloseDepositRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AutoCloseDepositRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AutoCloseDepositRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AutoCloseDepositRequestMultiError, or nil if none found.
func (m *AutoCloseDepositRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AutoCloseDepositRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DepositAccountNum

	if len(errors) > 0 {
		return AutoCloseDepositRequestMultiError(errors)
	}

	return nil
}

// AutoCloseDepositRequestMultiError is an error wrapping multiple validation
// errors returned by AutoCloseDepositRequest.ValidateAll() if the designated
// constraints aren't met.
type AutoCloseDepositRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AutoCloseDepositRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AutoCloseDepositRequestMultiError) AllErrors() []error { return m }

// AutoCloseDepositRequestValidationError is the validation error returned by
// AutoCloseDepositRequest.Validate if the designated constraints aren't met.
type AutoCloseDepositRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AutoCloseDepositRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AutoCloseDepositRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AutoCloseDepositRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AutoCloseDepositRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AutoCloseDepositRequestValidationError) ErrorName() string {
	return "AutoCloseDepositRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AutoCloseDepositRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAutoCloseDepositRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AutoCloseDepositRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AutoCloseDepositRequestValidationError{}
