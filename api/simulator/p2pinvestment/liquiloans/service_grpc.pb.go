// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/simulator/p2pinvestment/liquiloans/service.proto

package liquiloans

import (
	context "context"
	liquiloans "github.com/epifi/gamma/api/vendors/liquiloans"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Liquiloans_CreateInvestor_FullMethodName                  = "/simulator.p2pinvestment.liquiloans.Liquiloans/CreateInvestor"
	Liquiloans_GetInvestorDashboard_FullMethodName            = "/simulator.p2pinvestment.liquiloans.Liquiloans/GetInvestorDashboard"
	Liquiloans_AddMoneyInvestorOffline_FullMethodName         = "/simulator.p2pinvestment.liquiloans.Liquiloans/AddMoneyInvestorOffline"
	Liquiloans_WithdrawMoneyInvestor_FullMethodName           = "/simulator.p2pinvestment.liquiloans.Liquiloans/WithdrawMoneyInvestor"
	Liquiloans_GetTransactionByExtTxnId_FullMethodName        = "/simulator.p2pinvestment.liquiloans.Liquiloans/GetTransactionByExtTxnId"
	Liquiloans_GetTransactionById_FullMethodName              = "/simulator.p2pinvestment.liquiloans.Liquiloans/GetTransactionById"
	Liquiloans_UploadInvestorDocs_FullMethodName              = "/simulator.p2pinvestment.liquiloans.Liquiloans/UploadInvestorDocs"
	Liquiloans_IsInvestorExist_FullMethodName                 = "/simulator.p2pinvestment.liquiloans.Liquiloans/IsInvestorExist"
	Liquiloans_GetInvestmentSummary_FullMethodName            = "/simulator.p2pinvestment.liquiloans.Liquiloans/GetInvestmentSummary"
	Liquiloans_VerifyCkyc_FullMethodName                      = "/simulator.p2pinvestment.liquiloans.Liquiloans/VerifyCkyc"
	Liquiloans_GetMaxInvestmentAmount_FullMethodName          = "/simulator.p2pinvestment.liquiloans.Liquiloans/GetMaxInvestmentAmount"
	Liquiloans_CreateMaturityAction_FullMethodName            = "/simulator.p2pinvestment.liquiloans.Liquiloans/CreateMaturityAction"
	Liquiloans_GetMaturityLinkOTP_FullMethodName              = "/simulator.p2pinvestment.liquiloans.Liquiloans/GetMaturityLinkOTP"
	Liquiloans_GetInvestorMaturityTransactions_FullMethodName = "/simulator.p2pinvestment.liquiloans.Liquiloans/GetInvestorMaturityTransactions"
	Liquiloans_GetBulkMaturityTransactions_FullMethodName     = "/simulator.p2pinvestment.liquiloans.Liquiloans/GetBulkMaturityTransactions"
	Liquiloans_CreateMaturityActionByRequestId_FullMethodName = "/simulator.p2pinvestment.liquiloans.Liquiloans/CreateMaturityActionByRequestId"
	Liquiloans_GetCashLedger_FullMethodName                   = "/simulator.p2pinvestment.liquiloans.Liquiloans/GetCashLedger"
	Liquiloans_MaturityUploadInvestorDocs_FullMethodName      = "/simulator.p2pinvestment.liquiloans.Liquiloans/MaturityUploadInvestorDocs"
)

// LiquiloansClient is the client API for Liquiloans service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LiquiloansClient interface {
	CreateInvestor(ctx context.Context, in *liquiloans.CreateInvestorRequest, opts ...grpc.CallOption) (*liquiloans.CreateInvestorResponse, error)
	GetInvestorDashboard(ctx context.Context, in *liquiloans.GetInvestorDashboardRequest, opts ...grpc.CallOption) (*liquiloans.GetInvestorDashboardResponse, error)
	AddMoneyInvestorOffline(ctx context.Context, in *liquiloans.AddMoneyInvestorOfflineRequest, opts ...grpc.CallOption) (*liquiloans.AddMoneyInvestorOfflineResponse, error)
	WithdrawMoneyInvestor(ctx context.Context, in *liquiloans.WithdrawMoneyInvestorRequest, opts ...grpc.CallOption) (*liquiloans.WithdrawMoneyInvestorResponse, error)
	GetTransactionByExtTxnId(ctx context.Context, in *liquiloans.GetTransactionByExtTxnIdRequest, opts ...grpc.CallOption) (*liquiloans.GetTransactionByExtTxnIdResponse, error)
	GetTransactionById(ctx context.Context, in *liquiloans.GetTransactionByIdRequest, opts ...grpc.CallOption) (*liquiloans.GetTransactionByIdResponse, error)
	UploadInvestorDocs(ctx context.Context, in *liquiloans.UploadInvestorDocsRequest, opts ...grpc.CallOption) (*liquiloans.UploadInvestorDocsResponse, error)
	IsInvestorExist(ctx context.Context, in *liquiloans.IsInvestorExistRequest, opts ...grpc.CallOption) (*liquiloans.IsInvestorExistResponse, error)
	GetInvestmentSummary(ctx context.Context, in *liquiloans.GetInvestmentSummaryRequest, opts ...grpc.CallOption) (*liquiloans.GetInvestmentSummaryResponse, error)
	VerifyCkyc(ctx context.Context, in *liquiloans.VerifyCkycRequest, opts ...grpc.CallOption) (*liquiloans.VerifyCkycResponse, error)
	GetMaxInvestmentAmount(ctx context.Context, in *liquiloans.GetMaxInvestmentAmountRequest, opts ...grpc.CallOption) (*liquiloans.GetMaxInvestmentAmountResponse, error)
	CreateMaturityAction(ctx context.Context, in *liquiloans.CreateMaturityActionRequest, opts ...grpc.CallOption) (*liquiloans.CreateMaturityActionResponse, error)
	GetMaturityLinkOTP(ctx context.Context, in *liquiloans.GetMaturityLinkOTPRequest, opts ...grpc.CallOption) (*liquiloans.GetMaturityLinkOTPResponse, error)
	GetInvestorMaturityTransactions(ctx context.Context, in *liquiloans.GetInvestorMaturityTransactionsRequest, opts ...grpc.CallOption) (*liquiloans.GetInvestorMaturityTransactionsResponse, error)
	GetBulkMaturityTransactions(ctx context.Context, in *liquiloans.GetBulkMaturityTransactionsRequest, opts ...grpc.CallOption) (*liquiloans.GetBulkMaturityTransactionsResponse, error)
	CreateMaturityActionByRequestId(ctx context.Context, in *liquiloans.CreateMaturityActionByRequestIdRequest, opts ...grpc.CallOption) (*liquiloans.CreateMaturityActionByRequestIdResponse, error)
	GetCashLedger(ctx context.Context, in *liquiloans.GetCashLedgerRequest, opts ...grpc.CallOption) (*liquiloans.GetCashLedgerResponse, error)
	MaturityUploadInvestorDocs(ctx context.Context, in *liquiloans.MaturityUploadInvestorDocsRequest, opts ...grpc.CallOption) (*liquiloans.MaturityUploadInvestorDocsResponse, error)
}

type liquiloansClient struct {
	cc grpc.ClientConnInterface
}

func NewLiquiloansClient(cc grpc.ClientConnInterface) LiquiloansClient {
	return &liquiloansClient{cc}
}

func (c *liquiloansClient) CreateInvestor(ctx context.Context, in *liquiloans.CreateInvestorRequest, opts ...grpc.CallOption) (*liquiloans.CreateInvestorResponse, error) {
	out := new(liquiloans.CreateInvestorResponse)
	err := c.cc.Invoke(ctx, Liquiloans_CreateInvestor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GetInvestorDashboard(ctx context.Context, in *liquiloans.GetInvestorDashboardRequest, opts ...grpc.CallOption) (*liquiloans.GetInvestorDashboardResponse, error) {
	out := new(liquiloans.GetInvestorDashboardResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetInvestorDashboard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) AddMoneyInvestorOffline(ctx context.Context, in *liquiloans.AddMoneyInvestorOfflineRequest, opts ...grpc.CallOption) (*liquiloans.AddMoneyInvestorOfflineResponse, error) {
	out := new(liquiloans.AddMoneyInvestorOfflineResponse)
	err := c.cc.Invoke(ctx, Liquiloans_AddMoneyInvestorOffline_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) WithdrawMoneyInvestor(ctx context.Context, in *liquiloans.WithdrawMoneyInvestorRequest, opts ...grpc.CallOption) (*liquiloans.WithdrawMoneyInvestorResponse, error) {
	out := new(liquiloans.WithdrawMoneyInvestorResponse)
	err := c.cc.Invoke(ctx, Liquiloans_WithdrawMoneyInvestor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GetTransactionByExtTxnId(ctx context.Context, in *liquiloans.GetTransactionByExtTxnIdRequest, opts ...grpc.CallOption) (*liquiloans.GetTransactionByExtTxnIdResponse, error) {
	out := new(liquiloans.GetTransactionByExtTxnIdResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetTransactionByExtTxnId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GetTransactionById(ctx context.Context, in *liquiloans.GetTransactionByIdRequest, opts ...grpc.CallOption) (*liquiloans.GetTransactionByIdResponse, error) {
	out := new(liquiloans.GetTransactionByIdResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetTransactionById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) UploadInvestorDocs(ctx context.Context, in *liquiloans.UploadInvestorDocsRequest, opts ...grpc.CallOption) (*liquiloans.UploadInvestorDocsResponse, error) {
	out := new(liquiloans.UploadInvestorDocsResponse)
	err := c.cc.Invoke(ctx, Liquiloans_UploadInvestorDocs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) IsInvestorExist(ctx context.Context, in *liquiloans.IsInvestorExistRequest, opts ...grpc.CallOption) (*liquiloans.IsInvestorExistResponse, error) {
	out := new(liquiloans.IsInvestorExistResponse)
	err := c.cc.Invoke(ctx, Liquiloans_IsInvestorExist_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GetInvestmentSummary(ctx context.Context, in *liquiloans.GetInvestmentSummaryRequest, opts ...grpc.CallOption) (*liquiloans.GetInvestmentSummaryResponse, error) {
	out := new(liquiloans.GetInvestmentSummaryResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetInvestmentSummary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) VerifyCkyc(ctx context.Context, in *liquiloans.VerifyCkycRequest, opts ...grpc.CallOption) (*liquiloans.VerifyCkycResponse, error) {
	out := new(liquiloans.VerifyCkycResponse)
	err := c.cc.Invoke(ctx, Liquiloans_VerifyCkyc_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GetMaxInvestmentAmount(ctx context.Context, in *liquiloans.GetMaxInvestmentAmountRequest, opts ...grpc.CallOption) (*liquiloans.GetMaxInvestmentAmountResponse, error) {
	out := new(liquiloans.GetMaxInvestmentAmountResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetMaxInvestmentAmount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) CreateMaturityAction(ctx context.Context, in *liquiloans.CreateMaturityActionRequest, opts ...grpc.CallOption) (*liquiloans.CreateMaturityActionResponse, error) {
	out := new(liquiloans.CreateMaturityActionResponse)
	err := c.cc.Invoke(ctx, Liquiloans_CreateMaturityAction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GetMaturityLinkOTP(ctx context.Context, in *liquiloans.GetMaturityLinkOTPRequest, opts ...grpc.CallOption) (*liquiloans.GetMaturityLinkOTPResponse, error) {
	out := new(liquiloans.GetMaturityLinkOTPResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetMaturityLinkOTP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GetInvestorMaturityTransactions(ctx context.Context, in *liquiloans.GetInvestorMaturityTransactionsRequest, opts ...grpc.CallOption) (*liquiloans.GetInvestorMaturityTransactionsResponse, error) {
	out := new(liquiloans.GetInvestorMaturityTransactionsResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetInvestorMaturityTransactions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GetBulkMaturityTransactions(ctx context.Context, in *liquiloans.GetBulkMaturityTransactionsRequest, opts ...grpc.CallOption) (*liquiloans.GetBulkMaturityTransactionsResponse, error) {
	out := new(liquiloans.GetBulkMaturityTransactionsResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetBulkMaturityTransactions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) CreateMaturityActionByRequestId(ctx context.Context, in *liquiloans.CreateMaturityActionByRequestIdRequest, opts ...grpc.CallOption) (*liquiloans.CreateMaturityActionByRequestIdResponse, error) {
	out := new(liquiloans.CreateMaturityActionByRequestIdResponse)
	err := c.cc.Invoke(ctx, Liquiloans_CreateMaturityActionByRequestId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) GetCashLedger(ctx context.Context, in *liquiloans.GetCashLedgerRequest, opts ...grpc.CallOption) (*liquiloans.GetCashLedgerResponse, error) {
	out := new(liquiloans.GetCashLedgerResponse)
	err := c.cc.Invoke(ctx, Liquiloans_GetCashLedger_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liquiloansClient) MaturityUploadInvestorDocs(ctx context.Context, in *liquiloans.MaturityUploadInvestorDocsRequest, opts ...grpc.CallOption) (*liquiloans.MaturityUploadInvestorDocsResponse, error) {
	out := new(liquiloans.MaturityUploadInvestorDocsResponse)
	err := c.cc.Invoke(ctx, Liquiloans_MaturityUploadInvestorDocs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LiquiloansServer is the server API for Liquiloans service.
// All implementations should embed UnimplementedLiquiloansServer
// for forward compatibility
type LiquiloansServer interface {
	CreateInvestor(context.Context, *liquiloans.CreateInvestorRequest) (*liquiloans.CreateInvestorResponse, error)
	GetInvestorDashboard(context.Context, *liquiloans.GetInvestorDashboardRequest) (*liquiloans.GetInvestorDashboardResponse, error)
	AddMoneyInvestorOffline(context.Context, *liquiloans.AddMoneyInvestorOfflineRequest) (*liquiloans.AddMoneyInvestorOfflineResponse, error)
	WithdrawMoneyInvestor(context.Context, *liquiloans.WithdrawMoneyInvestorRequest) (*liquiloans.WithdrawMoneyInvestorResponse, error)
	GetTransactionByExtTxnId(context.Context, *liquiloans.GetTransactionByExtTxnIdRequest) (*liquiloans.GetTransactionByExtTxnIdResponse, error)
	GetTransactionById(context.Context, *liquiloans.GetTransactionByIdRequest) (*liquiloans.GetTransactionByIdResponse, error)
	UploadInvestorDocs(context.Context, *liquiloans.UploadInvestorDocsRequest) (*liquiloans.UploadInvestorDocsResponse, error)
	IsInvestorExist(context.Context, *liquiloans.IsInvestorExistRequest) (*liquiloans.IsInvestorExistResponse, error)
	GetInvestmentSummary(context.Context, *liquiloans.GetInvestmentSummaryRequest) (*liquiloans.GetInvestmentSummaryResponse, error)
	VerifyCkyc(context.Context, *liquiloans.VerifyCkycRequest) (*liquiloans.VerifyCkycResponse, error)
	GetMaxInvestmentAmount(context.Context, *liquiloans.GetMaxInvestmentAmountRequest) (*liquiloans.GetMaxInvestmentAmountResponse, error)
	CreateMaturityAction(context.Context, *liquiloans.CreateMaturityActionRequest) (*liquiloans.CreateMaturityActionResponse, error)
	GetMaturityLinkOTP(context.Context, *liquiloans.GetMaturityLinkOTPRequest) (*liquiloans.GetMaturityLinkOTPResponse, error)
	GetInvestorMaturityTransactions(context.Context, *liquiloans.GetInvestorMaturityTransactionsRequest) (*liquiloans.GetInvestorMaturityTransactionsResponse, error)
	GetBulkMaturityTransactions(context.Context, *liquiloans.GetBulkMaturityTransactionsRequest) (*liquiloans.GetBulkMaturityTransactionsResponse, error)
	CreateMaturityActionByRequestId(context.Context, *liquiloans.CreateMaturityActionByRequestIdRequest) (*liquiloans.CreateMaturityActionByRequestIdResponse, error)
	GetCashLedger(context.Context, *liquiloans.GetCashLedgerRequest) (*liquiloans.GetCashLedgerResponse, error)
	MaturityUploadInvestorDocs(context.Context, *liquiloans.MaturityUploadInvestorDocsRequest) (*liquiloans.MaturityUploadInvestorDocsResponse, error)
}

// UnimplementedLiquiloansServer should be embedded to have forward compatible implementations.
type UnimplementedLiquiloansServer struct {
}

func (UnimplementedLiquiloansServer) CreateInvestor(context.Context, *liquiloans.CreateInvestorRequest) (*liquiloans.CreateInvestorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateInvestor not implemented")
}
func (UnimplementedLiquiloansServer) GetInvestorDashboard(context.Context, *liquiloans.GetInvestorDashboardRequest) (*liquiloans.GetInvestorDashboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvestorDashboard not implemented")
}
func (UnimplementedLiquiloansServer) AddMoneyInvestorOffline(context.Context, *liquiloans.AddMoneyInvestorOfflineRequest) (*liquiloans.AddMoneyInvestorOfflineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddMoneyInvestorOffline not implemented")
}
func (UnimplementedLiquiloansServer) WithdrawMoneyInvestor(context.Context, *liquiloans.WithdrawMoneyInvestorRequest) (*liquiloans.WithdrawMoneyInvestorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WithdrawMoneyInvestor not implemented")
}
func (UnimplementedLiquiloansServer) GetTransactionByExtTxnId(context.Context, *liquiloans.GetTransactionByExtTxnIdRequest) (*liquiloans.GetTransactionByExtTxnIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionByExtTxnId not implemented")
}
func (UnimplementedLiquiloansServer) GetTransactionById(context.Context, *liquiloans.GetTransactionByIdRequest) (*liquiloans.GetTransactionByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionById not implemented")
}
func (UnimplementedLiquiloansServer) UploadInvestorDocs(context.Context, *liquiloans.UploadInvestorDocsRequest) (*liquiloans.UploadInvestorDocsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadInvestorDocs not implemented")
}
func (UnimplementedLiquiloansServer) IsInvestorExist(context.Context, *liquiloans.IsInvestorExistRequest) (*liquiloans.IsInvestorExistResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsInvestorExist not implemented")
}
func (UnimplementedLiquiloansServer) GetInvestmentSummary(context.Context, *liquiloans.GetInvestmentSummaryRequest) (*liquiloans.GetInvestmentSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvestmentSummary not implemented")
}
func (UnimplementedLiquiloansServer) VerifyCkyc(context.Context, *liquiloans.VerifyCkycRequest) (*liquiloans.VerifyCkycResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyCkyc not implemented")
}
func (UnimplementedLiquiloansServer) GetMaxInvestmentAmount(context.Context, *liquiloans.GetMaxInvestmentAmountRequest) (*liquiloans.GetMaxInvestmentAmountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMaxInvestmentAmount not implemented")
}
func (UnimplementedLiquiloansServer) CreateMaturityAction(context.Context, *liquiloans.CreateMaturityActionRequest) (*liquiloans.CreateMaturityActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMaturityAction not implemented")
}
func (UnimplementedLiquiloansServer) GetMaturityLinkOTP(context.Context, *liquiloans.GetMaturityLinkOTPRequest) (*liquiloans.GetMaturityLinkOTPResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMaturityLinkOTP not implemented")
}
func (UnimplementedLiquiloansServer) GetInvestorMaturityTransactions(context.Context, *liquiloans.GetInvestorMaturityTransactionsRequest) (*liquiloans.GetInvestorMaturityTransactionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvestorMaturityTransactions not implemented")
}
func (UnimplementedLiquiloansServer) GetBulkMaturityTransactions(context.Context, *liquiloans.GetBulkMaturityTransactionsRequest) (*liquiloans.GetBulkMaturityTransactionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBulkMaturityTransactions not implemented")
}
func (UnimplementedLiquiloansServer) CreateMaturityActionByRequestId(context.Context, *liquiloans.CreateMaturityActionByRequestIdRequest) (*liquiloans.CreateMaturityActionByRequestIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMaturityActionByRequestId not implemented")
}
func (UnimplementedLiquiloansServer) GetCashLedger(context.Context, *liquiloans.GetCashLedgerRequest) (*liquiloans.GetCashLedgerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCashLedger not implemented")
}
func (UnimplementedLiquiloansServer) MaturityUploadInvestorDocs(context.Context, *liquiloans.MaturityUploadInvestorDocsRequest) (*liquiloans.MaturityUploadInvestorDocsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MaturityUploadInvestorDocs not implemented")
}

// UnsafeLiquiloansServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LiquiloansServer will
// result in compilation errors.
type UnsafeLiquiloansServer interface {
	mustEmbedUnimplementedLiquiloansServer()
}

func RegisterLiquiloansServer(s grpc.ServiceRegistrar, srv LiquiloansServer) {
	s.RegisterService(&Liquiloans_ServiceDesc, srv)
}

func _Liquiloans_CreateInvestor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.CreateInvestorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).CreateInvestor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_CreateInvestor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).CreateInvestor(ctx, req.(*liquiloans.CreateInvestorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GetInvestorDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.GetInvestorDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetInvestorDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetInvestorDashboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetInvestorDashboard(ctx, req.(*liquiloans.GetInvestorDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_AddMoneyInvestorOffline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.AddMoneyInvestorOfflineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).AddMoneyInvestorOffline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_AddMoneyInvestorOffline_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).AddMoneyInvestorOffline(ctx, req.(*liquiloans.AddMoneyInvestorOfflineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_WithdrawMoneyInvestor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.WithdrawMoneyInvestorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).WithdrawMoneyInvestor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_WithdrawMoneyInvestor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).WithdrawMoneyInvestor(ctx, req.(*liquiloans.WithdrawMoneyInvestorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GetTransactionByExtTxnId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.GetTransactionByExtTxnIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetTransactionByExtTxnId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetTransactionByExtTxnId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetTransactionByExtTxnId(ctx, req.(*liquiloans.GetTransactionByExtTxnIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GetTransactionById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.GetTransactionByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetTransactionById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetTransactionById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetTransactionById(ctx, req.(*liquiloans.GetTransactionByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_UploadInvestorDocs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.UploadInvestorDocsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).UploadInvestorDocs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_UploadInvestorDocs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).UploadInvestorDocs(ctx, req.(*liquiloans.UploadInvestorDocsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_IsInvestorExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.IsInvestorExistRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).IsInvestorExist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_IsInvestorExist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).IsInvestorExist(ctx, req.(*liquiloans.IsInvestorExistRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GetInvestmentSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.GetInvestmentSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetInvestmentSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetInvestmentSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetInvestmentSummary(ctx, req.(*liquiloans.GetInvestmentSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_VerifyCkyc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.VerifyCkycRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).VerifyCkyc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_VerifyCkyc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).VerifyCkyc(ctx, req.(*liquiloans.VerifyCkycRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GetMaxInvestmentAmount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.GetMaxInvestmentAmountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetMaxInvestmentAmount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetMaxInvestmentAmount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetMaxInvestmentAmount(ctx, req.(*liquiloans.GetMaxInvestmentAmountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_CreateMaturityAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.CreateMaturityActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).CreateMaturityAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_CreateMaturityAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).CreateMaturityAction(ctx, req.(*liquiloans.CreateMaturityActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GetMaturityLinkOTP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.GetMaturityLinkOTPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetMaturityLinkOTP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetMaturityLinkOTP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetMaturityLinkOTP(ctx, req.(*liquiloans.GetMaturityLinkOTPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GetInvestorMaturityTransactions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.GetInvestorMaturityTransactionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetInvestorMaturityTransactions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetInvestorMaturityTransactions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetInvestorMaturityTransactions(ctx, req.(*liquiloans.GetInvestorMaturityTransactionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GetBulkMaturityTransactions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.GetBulkMaturityTransactionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetBulkMaturityTransactions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetBulkMaturityTransactions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetBulkMaturityTransactions(ctx, req.(*liquiloans.GetBulkMaturityTransactionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_CreateMaturityActionByRequestId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.CreateMaturityActionByRequestIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).CreateMaturityActionByRequestId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_CreateMaturityActionByRequestId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).CreateMaturityActionByRequestId(ctx, req.(*liquiloans.CreateMaturityActionByRequestIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_GetCashLedger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.GetCashLedgerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).GetCashLedger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_GetCashLedger_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).GetCashLedger(ctx, req.(*liquiloans.GetCashLedgerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liquiloans_MaturityUploadInvestorDocs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(liquiloans.MaturityUploadInvestorDocsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiquiloansServer).MaturityUploadInvestorDocs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liquiloans_MaturityUploadInvestorDocs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiquiloansServer).MaturityUploadInvestorDocs(ctx, req.(*liquiloans.MaturityUploadInvestorDocsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Liquiloans_ServiceDesc is the grpc.ServiceDesc for Liquiloans service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Liquiloans_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "simulator.p2pinvestment.liquiloans.Liquiloans",
	HandlerType: (*LiquiloansServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateInvestor",
			Handler:    _Liquiloans_CreateInvestor_Handler,
		},
		{
			MethodName: "GetInvestorDashboard",
			Handler:    _Liquiloans_GetInvestorDashboard_Handler,
		},
		{
			MethodName: "AddMoneyInvestorOffline",
			Handler:    _Liquiloans_AddMoneyInvestorOffline_Handler,
		},
		{
			MethodName: "WithdrawMoneyInvestor",
			Handler:    _Liquiloans_WithdrawMoneyInvestor_Handler,
		},
		{
			MethodName: "GetTransactionByExtTxnId",
			Handler:    _Liquiloans_GetTransactionByExtTxnId_Handler,
		},
		{
			MethodName: "GetTransactionById",
			Handler:    _Liquiloans_GetTransactionById_Handler,
		},
		{
			MethodName: "UploadInvestorDocs",
			Handler:    _Liquiloans_UploadInvestorDocs_Handler,
		},
		{
			MethodName: "IsInvestorExist",
			Handler:    _Liquiloans_IsInvestorExist_Handler,
		},
		{
			MethodName: "GetInvestmentSummary",
			Handler:    _Liquiloans_GetInvestmentSummary_Handler,
		},
		{
			MethodName: "VerifyCkyc",
			Handler:    _Liquiloans_VerifyCkyc_Handler,
		},
		{
			MethodName: "GetMaxInvestmentAmount",
			Handler:    _Liquiloans_GetMaxInvestmentAmount_Handler,
		},
		{
			MethodName: "CreateMaturityAction",
			Handler:    _Liquiloans_CreateMaturityAction_Handler,
		},
		{
			MethodName: "GetMaturityLinkOTP",
			Handler:    _Liquiloans_GetMaturityLinkOTP_Handler,
		},
		{
			MethodName: "GetInvestorMaturityTransactions",
			Handler:    _Liquiloans_GetInvestorMaturityTransactions_Handler,
		},
		{
			MethodName: "GetBulkMaturityTransactions",
			Handler:    _Liquiloans_GetBulkMaturityTransactions_Handler,
		},
		{
			MethodName: "CreateMaturityActionByRequestId",
			Handler:    _Liquiloans_CreateMaturityActionByRequestId_Handler,
		},
		{
			MethodName: "GetCashLedger",
			Handler:    _Liquiloans_GetCashLedger_Handler,
		},
		{
			MethodName: "MaturityUploadInvestorDocs",
			Handler:    _Liquiloans_MaturityUploadInvestorDocs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/simulator/p2pinvestment/liquiloans/service.proto",
}
