// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendornotification/cx/chatbot/data_collector.proto

package chatbot

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CollectedData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CollectedData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectedData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CollectedDataMultiError, or
// nil if none found.
func (m *CollectedData) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectedData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Data.(type) {
	case *CollectedData_FreezeBotData:
		if v == nil {
			err := CollectedDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFreezeBotData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CollectedDataValidationError{
						field:  "FreezeBotData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CollectedDataValidationError{
						field:  "FreezeBotData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFreezeBotData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CollectedDataValidationError{
					field:  "FreezeBotData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CollectedData_TransactionBotData:
		if v == nil {
			err := CollectedDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTransactionBotData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CollectedDataValidationError{
						field:  "TransactionBotData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CollectedDataValidationError{
						field:  "TransactionBotData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTransactionBotData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CollectedDataValidationError{
					field:  "TransactionBotData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CollectedDataMultiError(errors)
	}

	return nil
}

// CollectedDataMultiError is an error wrapping multiple validation errors
// returned by CollectedData.ValidateAll() if the designated constraints
// aren't met.
type CollectedDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectedDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectedDataMultiError) AllErrors() []error { return m }

// CollectedDataValidationError is the validation error returned by
// CollectedData.Validate if the designated constraints aren't met.
type CollectedDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectedDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectedDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectedDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectedDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectedDataValidationError) ErrorName() string { return "CollectedDataValidationError" }

// Error satisfies the builtin error interface
func (e CollectedDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectedData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectedDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectedDataValidationError{}

// Validate checks the field values on FreezeBotData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FreezeBotData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FreezeBotData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FreezeBotDataMultiError, or
// nil if none found.
func (m *FreezeBotData) ValidateAll() error {
	return m.validate(true)
}

func (m *FreezeBotData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountStats

	// no validation rules for ProcessedFreezeReason

	// no validation rules for FreezeType

	// no validation rules for FormId

	// no validation rules for FormStatus

	// no validation rules for FormExpiryDate

	// no validation rules for LeaComplaintDetails

	if len(errors) > 0 {
		return FreezeBotDataMultiError(errors)
	}

	return nil
}

// FreezeBotDataMultiError is an error wrapping multiple validation errors
// returned by FreezeBotData.ValidateAll() if the designated constraints
// aren't met.
type FreezeBotDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FreezeBotDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FreezeBotDataMultiError) AllErrors() []error { return m }

// FreezeBotDataValidationError is the validation error returned by
// FreezeBotData.Validate if the designated constraints aren't met.
type FreezeBotDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FreezeBotDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FreezeBotDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FreezeBotDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FreezeBotDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FreezeBotDataValidationError) ErrorName() string { return "FreezeBotDataValidationError" }

// Error satisfies the builtin error interface
func (e FreezeBotDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFreezeBotData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FreezeBotDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FreezeBotDataValidationError{}

// Validate checks the field values on TransactionBotData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TransactionBotData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransactionBotData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransactionBotDataMultiError, or nil if none found.
func (m *TransactionBotData) ValidateAll() error {
	return m.validate(true)
}

func (m *TransactionBotData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PaymentProtocol

	// no validation rules for Provenance

	// no validation rules for OrderCreatedAtIst

	// no validation rules for TransactionAmount

	// no validation rules for Tags

	// no validation rules for TransactionType

	// no validation rules for ErrorCode

	if len(errors) > 0 {
		return TransactionBotDataMultiError(errors)
	}

	return nil
}

// TransactionBotDataMultiError is an error wrapping multiple validation errors
// returned by TransactionBotData.ValidateAll() if the designated constraints
// aren't met.
type TransactionBotDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransactionBotDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransactionBotDataMultiError) AllErrors() []error { return m }

// TransactionBotDataValidationError is the validation error returned by
// TransactionBotData.Validate if the designated constraints aren't met.
type TransactionBotDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransactionBotDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransactionBotDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransactionBotDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransactionBotDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransactionBotDataValidationError) ErrorName() string {
	return "TransactionBotDataValidationError"
}

// Error satisfies the builtin error interface
func (e TransactionBotDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransactionBotData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransactionBotDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransactionBotDataValidationError{}
