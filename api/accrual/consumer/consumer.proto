//go:generate gen_queue_pb
// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package accrual.consumer;

import "api/accounts/operstatus/events.proto";
import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/accrual/consumer";
option java_package = "com.github.epifi.gamma.api.accrual.consumer";

service Consumer {
  // ProcessAccountStatusUpdateEvent consumes OperationalStatusUpdateEvent to expire all available fi coins for an actor if account status is OPERATIONAL_STATUS_CLOSED.
  rpc ProcessAccountStatusUpdateEvent (accounts.operstatus.OperationalStatusUpdateEvent) returns (ConsumerResponse);
}

message ConsumerResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
