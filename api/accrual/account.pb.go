// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.23.4
// source: api/accrual/account.proto

package accrual

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AccountType denotes the type of user account.
// Each account is uniquely represented by an actor_id (owner of account) and the account_type (type of account)
// Above line also implies that a given user can have a single account of given account_type.
type AccountType int32

const (
	AccountType_ACCOUNT_TYPE_UNSPECIFIED AccountType = 0
	AccountType_ACCOUNT_TYPE_FICOINS     AccountType = 1
	AccountType_ACCOUNT_TYPE_FI_POINTS   AccountType = 2
)

// Enum value maps for AccountType.
var (
	AccountType_name = map[int32]string{
		0: "ACCOUNT_TYPE_UNSPECIFIED",
		1: "ACCOUNT_TYPE_FICOINS",
		2: "ACCOUNT_TYPE_FI_POINTS",
	}
	AccountType_value = map[string]int32{
		"ACCOUNT_TYPE_UNSPECIFIED": 0,
		"ACCOUNT_TYPE_FICOINS":     1,
		"ACCOUNT_TYPE_FI_POINTS":   2,
	}
)

func (x AccountType) Enum() *AccountType {
	p := new(AccountType)
	*p = x
	return p
}

func (x AccountType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccountType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_accrual_account_proto_enumTypes[0].Descriptor()
}

func (AccountType) Type() protoreflect.EnumType {
	return &file_api_accrual_account_proto_enumTypes[0]
}

func (x AccountType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccountType.Descriptor instead.
func (AccountType) EnumDescriptor() ([]byte, []int) {
	return file_api_accrual_account_proto_rawDescGZIP(), []int{0}
}

// AccountOperationalStatus denotes the operational status of the user account.
type AccountOperationalStatus int32

const (
	AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_UNSPECIFIED AccountOperationalStatus = 0
	// ACCOUNT_OPERATIONAL_STATUS_ACTIVE denotes the account is active.
	AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_ACTIVE AccountOperationalStatus = 1
	// ACCOUNT_OPERATIONAL_STATUS_CLOSED denotes the account is closed.
	AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_CLOSED AccountOperationalStatus = 2
)

// Enum value maps for AccountOperationalStatus.
var (
	AccountOperationalStatus_name = map[int32]string{
		0: "ACCOUNT_OPERATIONAL_STATUS_UNSPECIFIED",
		1: "ACCOUNT_OPERATIONAL_STATUS_ACTIVE",
		2: "ACCOUNT_OPERATIONAL_STATUS_CLOSED",
	}
	AccountOperationalStatus_value = map[string]int32{
		"ACCOUNT_OPERATIONAL_STATUS_UNSPECIFIED": 0,
		"ACCOUNT_OPERATIONAL_STATUS_ACTIVE":      1,
		"ACCOUNT_OPERATIONAL_STATUS_CLOSED":      2,
	}
)

func (x AccountOperationalStatus) Enum() *AccountOperationalStatus {
	p := new(AccountOperationalStatus)
	*p = x
	return p
}

func (x AccountOperationalStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccountOperationalStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_accrual_account_proto_enumTypes[1].Descriptor()
}

func (AccountOperationalStatus) Type() protoreflect.EnumType {
	return &file_api_accrual_account_proto_enumTypes[1]
}

func (x AccountOperationalStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccountOperationalStatus.Descriptor instead.
func (AccountOperationalStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_accrual_account_proto_rawDescGZIP(), []int{1}
}

var File_api_accrual_account_proto protoreflect.FileDescriptor

var file_api_accrual_account_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x63, 0x63,
	0x72, 0x75, 0x61, 0x6c, 0x2a, 0x61, 0x0a, 0x0b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x46, 0x49, 0x43, 0x4f, 0x49, 0x4e, 0x53, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x5f, 0x50,
	0x4f, 0x49, 0x4e, 0x54, 0x53, 0x10, 0x02, 0x2a, 0x94, 0x01, 0x0a, 0x18, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x26, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x25, 0x0a, 0x21, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x02, 0x42, 0x48,
	0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63,
	0x72, 0x75, 0x61, 0x6c, 0x5a, 0x22, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_accrual_account_proto_rawDescOnce sync.Once
	file_api_accrual_account_proto_rawDescData = file_api_accrual_account_proto_rawDesc
)

func file_api_accrual_account_proto_rawDescGZIP() []byte {
	file_api_accrual_account_proto_rawDescOnce.Do(func() {
		file_api_accrual_account_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_accrual_account_proto_rawDescData)
	})
	return file_api_accrual_account_proto_rawDescData
}

var file_api_accrual_account_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_accrual_account_proto_goTypes = []interface{}{
	(AccountType)(0),              // 0: accrual.AccountType
	(AccountOperationalStatus)(0), // 1: accrual.AccountOperationalStatus
}
var file_api_accrual_account_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_accrual_account_proto_init() }
func file_api_accrual_account_proto_init() {
	if File_api_accrual_account_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_accrual_account_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_accrual_account_proto_goTypes,
		DependencyIndexes: file_api_accrual_account_proto_depIdxs,
		EnumInfos:         file_api_accrual_account_proto_enumTypes,
	}.Build()
	File_api_accrual_account_proto = out.File
	file_api_accrual_account_proto_rawDesc = nil
	file_api_accrual_account_proto_goTypes = nil
	file_api_accrual_account_proto_depIdxs = nil
}
