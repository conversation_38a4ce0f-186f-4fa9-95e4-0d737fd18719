syntax = "proto3";

package api.pkg.pay.paymentgateway;

option go_package = "github.com/epifi/be-common/api/pkg/pay/paymentgateway";

// PaymentStatus enum is used to denote the status of a payment gateway payment irrespective of the paymentgateway
// vendor (which can be a one-time payment or can be an authorisation payment for a recurring payment).
// It is placed in pkg/pay since this can be used in any backend repo (not just gamma).
enum PaymentStatus {
  PAYMENT_STATUS_UNSPECIFIED = 0;
  // Payment attempt is created when payer submits payment details at vendor
  PAYMENT_STATUS_CREATED = 1;
  // Payment details are successfully authenticated by the bank, the Payment state changes to Authorized.
  // Amount may be deducted from payer's account but not settled in epiFi account
  PAYMENT_STATUS_AUTHORIZED = 2;
  // Payment is successfully captured by vendor, this is equivalent to success.
  // This means the amount is settled in epiFi account.
  // For Razorpay, any authorization not followed by a capture within 5 days is automatically voided and the amount
  // is refunded.
  PAYMENT_STATUS_CAPTURED = 3;
  // Payment is rolled back to payer's account
  PAYMENT_STATUS_REFUNDED = 4;
  // Payment attempt failed for unsuccessful transaction. Payer will have to retry.
  PAYMENT_STATUS_FAILED = 5;
}

// MandateStatus enum is used to denote the status of a recurring payment registration irrespective of vendor.
// It is placed in pkg/pay since this can be used in any backend repo (not just gamma).
enum MandateStatus {
  // Default unspecified state
  MANDATE_STATUS_UNSPECIFIED = 0;
  // Indicates that the bank is processing the mandate registration.
  MANDATE_STATUS_INITIATED = 1;
  // Indicates that the bank has completed the mandate registration. One of the terminal states.
  MANDATE_STATUS_CONFIRMED = 2;
  // Indicates that the mandate registration has failed. One of the terminal states.
  // In this case the mandate registration needs to be started again.
  MANDATE_STATUS_REJECTED = 3;
  // Indicates that the token/mandate has been cancelled. One of the terminal states.
  MANDATE_STATUS_CANCELLED = 4;
  // Indicates that the token/mandate has been paused by the customer.
  // The token is inactive until resumed by the customer
  MANDATE_STATUS_PAUSED = 5;
}
