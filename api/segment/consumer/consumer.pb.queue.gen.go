// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/segment/consumer
package consumer

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	TriggerSegmentExportMethod           = "TriggerSegmentExport"
	PollSegmentExportMethod              = "PollSegmentExport"
	ProcessSegmentExportPartFileMethod   = "ProcessSegmentExportPartFile"
	UploadSegmentExportPartFileCsvMethod = "UploadSegmentExportPartFileCsv"
	ProcessSegmentExportPartFileV2Method = "ProcessSegmentExportPartFileV2"
	CompareSegmentInstancesMethod        = "CompareSegmentInstances"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &CompareSegmentInstancesRequest{}
var _ queue.ConsumerRequest = &TriggerSegmentExportRequest{}
var _ queue.ConsumerRequest = &PollSegmentExportRequest{}
var _ queue.ConsumerRequest = &ProcessSegmentExportPartFileRequest{}
var _ queue.ConsumerRequest = &ProcessSegmentExportPartFileV2Request{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *CompareSegmentInstancesRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *TriggerSegmentExportRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *PollSegmentExportRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessSegmentExportPartFileRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessSegmentExportPartFileV2Request) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterTriggerSegmentExportMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterTriggerSegmentExportMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, TriggerSegmentExportMethod)
}

// RegisterPollSegmentExportMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterPollSegmentExportMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, PollSegmentExportMethod)
}

// RegisterProcessSegmentExportPartFileMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessSegmentExportPartFileMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessSegmentExportPartFileMethod)
}

// RegisterUploadSegmentExportPartFileCsvMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterUploadSegmentExportPartFileCsvMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, UploadSegmentExportPartFileCsvMethod)
}

// RegisterProcessSegmentExportPartFileV2MethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessSegmentExportPartFileV2MethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessSegmentExportPartFileV2Method)
}

// RegisterCompareSegmentInstancesMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterCompareSegmentInstancesMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, CompareSegmentInstancesMethod)
}
