//go:generate gen_sql -types=AmlProduct,CustomerDetails,AmlScreeningStatus,AmlMatch

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/tsp/aml/data.proto

package aml

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	common "github.com/epifi/be-common/api/typesv2/common"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The product for which screening is requested, screening rules can be configured based on this
type AmlProduct int32

const (
	AmlProduct_AML_PRODUCT_UNSPECIFIED     AmlProduct = 0
	AmlProduct_AML_PRODUCT_US_STOCKS       AmlProduct = 1
	AmlProduct_AML_PRODUCT_MUTUAL_FUNDS    AmlProduct = 2
	AmlProduct_AML_PRODUCT_SAVINGS_ACCOUNT AmlProduct = 3
	AmlProduct_AML_PRODUCT_LOANS           AmlProduct = 4
)

// Enum value maps for AmlProduct.
var (
	AmlProduct_name = map[int32]string{
		0: "AML_PRODUCT_UNSPECIFIED",
		1: "AML_PRODUCT_US_STOCKS",
		2: "AML_PRODUCT_MUTUAL_FUNDS",
		3: "AML_PRODUCT_SAVINGS_ACCOUNT",
		4: "AML_PRODUCT_LOANS",
	}
	AmlProduct_value = map[string]int32{
		"AML_PRODUCT_UNSPECIFIED":     0,
		"AML_PRODUCT_US_STOCKS":       1,
		"AML_PRODUCT_MUTUAL_FUNDS":    2,
		"AML_PRODUCT_SAVINGS_ACCOUNT": 3,
		"AML_PRODUCT_LOANS":           4,
	}
)

func (x AmlProduct) Enum() *AmlProduct {
	p := new(AmlProduct)
	*p = x
	return p
}

func (x AmlProduct) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AmlProduct) Descriptor() protoreflect.EnumDescriptor {
	return file_api_tsp_aml_data_proto_enumTypes[0].Descriptor()
}

func (AmlProduct) Type() protoreflect.EnumType {
	return &file_api_tsp_aml_data_proto_enumTypes[0]
}

func (x AmlProduct) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AmlProduct.Descriptor instead.
func (AmlProduct) EnumDescriptor() ([]byte, []int) {
	return file_api_tsp_aml_data_proto_rawDescGZIP(), []int{0}
}

// New customer will raise a new screening request to vendor
// Update customer will update the customer details in vendor's system and new screening request will not be made
// After updating customer details, customer will be screened with updated details going forward
type AmlScreeningMode int32

const (
	AmlScreeningMode_AML_SCREENING_MODE_UNSPECIFIED     AmlScreeningMode = 0
	AmlScreeningMode_AML_SCREENING_MODE_NEW_CUSTOMER    AmlScreeningMode = 1
	AmlScreeningMode_AML_SCREENING_MODE_UPDATE_CUSTOMER AmlScreeningMode = 2
)

// Enum value maps for AmlScreeningMode.
var (
	AmlScreeningMode_name = map[int32]string{
		0: "AML_SCREENING_MODE_UNSPECIFIED",
		1: "AML_SCREENING_MODE_NEW_CUSTOMER",
		2: "AML_SCREENING_MODE_UPDATE_CUSTOMER",
	}
	AmlScreeningMode_value = map[string]int32{
		"AML_SCREENING_MODE_UNSPECIFIED":     0,
		"AML_SCREENING_MODE_NEW_CUSTOMER":    1,
		"AML_SCREENING_MODE_UPDATE_CUSTOMER": 2,
	}
)

func (x AmlScreeningMode) Enum() *AmlScreeningMode {
	p := new(AmlScreeningMode)
	*p = x
	return p
}

func (x AmlScreeningMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AmlScreeningMode) Descriptor() protoreflect.EnumDescriptor {
	return file_api_tsp_aml_data_proto_enumTypes[1].Descriptor()
}

func (AmlScreeningMode) Type() protoreflect.EnumType {
	return &file_api_tsp_aml_data_proto_enumTypes[1]
}

func (x AmlScreeningMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AmlScreeningMode.Descriptor instead.
func (AmlScreeningMode) EnumDescriptor() ([]byte, []int) {
	return file_api_tsp_aml_data_proto_rawDescGZIP(), []int{1}
}

type AmlWatchlistCategory int32

const (
	AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_UNSPECIFIED AmlWatchlistCategory = 0
	// national and international sanction lists
	AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_SANCTIONS AmlWatchlistCategory = 1
	// law enforcement public domain data
	AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_LAW_ENFORCEMENT AmlWatchlistCategory = 2
	// regulatory enforcement public domain data
	AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_REGULATORY_ENFORCEMENT AmlWatchlistCategory = 3
	// Politically exposed persons list
	AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_PEP AmlWatchlistCategory = 4
	// Adverse media coverage
	AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_ADVERSE_MEDIA AmlWatchlistCategory = 5
	// Any other watchlist
	AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_OTHERS AmlWatchlistCategory = 6
)

// Enum value maps for AmlWatchlistCategory.
var (
	AmlWatchlistCategory_name = map[int32]string{
		0: "AML_WATCHLIST_CATEGORY_UNSPECIFIED",
		1: "AML_WATCHLIST_CATEGORY_SANCTIONS",
		2: "AML_WATCHLIST_CATEGORY_LAW_ENFORCEMENT",
		3: "AML_WATCHLIST_CATEGORY_REGULATORY_ENFORCEMENT",
		4: "AML_WATCHLIST_CATEGORY_PEP",
		5: "AML_WATCHLIST_CATEGORY_ADVERSE_MEDIA",
		6: "AML_WATCHLIST_CATEGORY_OTHERS",
	}
	AmlWatchlistCategory_value = map[string]int32{
		"AML_WATCHLIST_CATEGORY_UNSPECIFIED":            0,
		"AML_WATCHLIST_CATEGORY_SANCTIONS":              1,
		"AML_WATCHLIST_CATEGORY_LAW_ENFORCEMENT":        2,
		"AML_WATCHLIST_CATEGORY_REGULATORY_ENFORCEMENT": 3,
		"AML_WATCHLIST_CATEGORY_PEP":                    4,
		"AML_WATCHLIST_CATEGORY_ADVERSE_MEDIA":          5,
		"AML_WATCHLIST_CATEGORY_OTHERS":                 6,
	}
)

func (x AmlWatchlistCategory) Enum() *AmlWatchlistCategory {
	p := new(AmlWatchlistCategory)
	*p = x
	return p
}

func (x AmlWatchlistCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AmlWatchlistCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_api_tsp_aml_data_proto_enumTypes[2].Descriptor()
}

func (AmlWatchlistCategory) Type() protoreflect.EnumType {
	return &file_api_tsp_aml_data_proto_enumTypes[2]
}

func (x AmlWatchlistCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AmlWatchlistCategory.Descriptor instead.
func (AmlWatchlistCategory) EnumDescriptor() ([]byte, []int) {
	return file_api_tsp_aml_data_proto_rawDescGZIP(), []int{2}
}

// represents the status of the screening attempt
type AmlScreeningStatus int32

const (
	AmlScreeningStatus_AML_SCREENING_STATUS_UNSPECIFIED AmlScreeningStatus = 0
	// screening attempt raised successfully
	AmlScreeningStatus_AML_SCREENING_STATUS_INITIATED AmlScreeningStatus = 1
	// screening attempt failed
	AmlScreeningStatus_AML_SCREENING_STATUS_FAILED AmlScreeningStatus = 2
	// screening is completed
	AmlScreeningStatus_AML_SCREENING_STATUS_SUCCESS AmlScreeningStatus = 3
)

// Enum value maps for AmlScreeningStatus.
var (
	AmlScreeningStatus_name = map[int32]string{
		0: "AML_SCREENING_STATUS_UNSPECIFIED",
		1: "AML_SCREENING_STATUS_INITIATED",
		2: "AML_SCREENING_STATUS_FAILED",
		3: "AML_SCREENING_STATUS_SUCCESS",
	}
	AmlScreeningStatus_value = map[string]int32{
		"AML_SCREENING_STATUS_UNSPECIFIED": 0,
		"AML_SCREENING_STATUS_INITIATED":   1,
		"AML_SCREENING_STATUS_FAILED":      2,
		"AML_SCREENING_STATUS_SUCCESS":     3,
	}
)

func (x AmlScreeningStatus) Enum() *AmlScreeningStatus {
	p := new(AmlScreeningStatus)
	*p = x
	return p
}

func (x AmlScreeningStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AmlScreeningStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_tsp_aml_data_proto_enumTypes[3].Descriptor()
}

func (AmlScreeningStatus) Type() protoreflect.EnumType {
	return &file_api_tsp_aml_data_proto_enumTypes[3]
}

func (x AmlScreeningStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AmlScreeningStatus.Descriptor instead.
func (AmlScreeningStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_tsp_aml_data_proto_rawDescGZIP(), []int{3}
}

// This describes the parameter match based on which case is created
type AmlParameter int32

const (
	AmlParameter_AML_PARAMETER_UNSPECIFIED AmlParameter = 0
	// PAN number matching
	AmlParameter_AML_PARAMETER_PAN AmlParameter = 1
	// Passport number matching
	AmlParameter_AML_PARAMETER_PASSPORT AmlParameter = 2
	// Driving license number matching
	AmlParameter_AML_PARAMETER_DRIVING_LICENSE AmlParameter = 3
	// Deprecated: Marked as deprecated in api/tsp/aml/data.proto.
	AmlParameter_AML_PARAMETER_NAME AmlParameter = 4
	// Exact name matching in any order
	// e.g. Jolly Joseph and Joseph Jolly
	AmlParameter_AML_PARAMETER_EXACT_NAME AmlParameter = 5
	// Name matching with spaces removed
	// e.g. Jolly Joseph and JollyJoseph
	AmlParameter_AML_PARAMETER_BINDING_NAME AmlParameter = 6
	// Fuzzy name matching using a matching algorithm
	// a matching percentage is also returned for this case
	AmlParameter_AML_PARAMETER_FUZZY_NAME AmlParameter = 7
	// First name matching
	// e.g. Jolly Joseph and Jolly Kent
	AmlParameter_AML_PARAMETER_INITIAL_NAME AmlParameter = 8
	// Vowels in the name matching
	// e.g. Jolly Joseph and Jolly Yosef
	AmlParameter_AML_PARAMETER_VOWEL_NAME AmlParameter = 9
	// Alias name matching
	AmlParameter_AML_PARAMETER_ALIAS_NAME AmlParameter = 10
	// DIN number matching
	AmlParameter_AML_PARAMETER_DIN AmlParameter = 11
	// Date of birth fuzzy matching
	AmlParameter_AML_PARAMETER_FUZZY_DOB AmlParameter = 12
	// State in address matching
	AmlParameter_AML_PARAMETER_STATE AmlParameter = 13
	// Country in address matching
	AmlParameter_AML_PARAMETER_COUNTRY AmlParameter = 14
	// City in address matching
	AmlParameter_AML_PARAMETER_CITY AmlParameter = 15
)

// Enum value maps for AmlParameter.
var (
	AmlParameter_name = map[int32]string{
		0:  "AML_PARAMETER_UNSPECIFIED",
		1:  "AML_PARAMETER_PAN",
		2:  "AML_PARAMETER_PASSPORT",
		3:  "AML_PARAMETER_DRIVING_LICENSE",
		4:  "AML_PARAMETER_NAME",
		5:  "AML_PARAMETER_EXACT_NAME",
		6:  "AML_PARAMETER_BINDING_NAME",
		7:  "AML_PARAMETER_FUZZY_NAME",
		8:  "AML_PARAMETER_INITIAL_NAME",
		9:  "AML_PARAMETER_VOWEL_NAME",
		10: "AML_PARAMETER_ALIAS_NAME",
		11: "AML_PARAMETER_DIN",
		12: "AML_PARAMETER_FUZZY_DOB",
		13: "AML_PARAMETER_STATE",
		14: "AML_PARAMETER_COUNTRY",
		15: "AML_PARAMETER_CITY",
	}
	AmlParameter_value = map[string]int32{
		"AML_PARAMETER_UNSPECIFIED":     0,
		"AML_PARAMETER_PAN":             1,
		"AML_PARAMETER_PASSPORT":        2,
		"AML_PARAMETER_DRIVING_LICENSE": 3,
		"AML_PARAMETER_NAME":            4,
		"AML_PARAMETER_EXACT_NAME":      5,
		"AML_PARAMETER_BINDING_NAME":    6,
		"AML_PARAMETER_FUZZY_NAME":      7,
		"AML_PARAMETER_INITIAL_NAME":    8,
		"AML_PARAMETER_VOWEL_NAME":      9,
		"AML_PARAMETER_ALIAS_NAME":      10,
		"AML_PARAMETER_DIN":             11,
		"AML_PARAMETER_FUZZY_DOB":       12,
		"AML_PARAMETER_STATE":           13,
		"AML_PARAMETER_COUNTRY":         14,
		"AML_PARAMETER_CITY":            15,
	}
)

func (x AmlParameter) Enum() *AmlParameter {
	p := new(AmlParameter)
	*p = x
	return p
}

func (x AmlParameter) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AmlParameter) Descriptor() protoreflect.EnumDescriptor {
	return file_api_tsp_aml_data_proto_enumTypes[4].Descriptor()
}

func (AmlParameter) Type() protoreflect.EnumType {
	return &file_api_tsp_aml_data_proto_enumTypes[4]
}

func (x AmlParameter) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AmlParameter.Descriptor instead.
func (AmlParameter) EnumDescriptor() ([]byte, []int) {
	return file_api_tsp_aml_data_proto_rawDescGZIP(), []int{4}
}

// Enum represents if a match is found or not after screening is done by vendor
type AmlMatch int32

const (
	AmlMatch_AML_MATCH_UNSPECIFIED AmlMatch = 0
	// Match found in screening
	AmlMatch_AML_MATCH_FOUND AmlMatch = 1
	// Match not found in screening
	AmlMatch_AML_MATCH_NOT_FOUND AmlMatch = 2
	// Error in creating screening attempt
	AmlMatch_AML_MATCH_ERROR AmlMatch = 3
)

// Enum value maps for AmlMatch.
var (
	AmlMatch_name = map[int32]string{
		0: "AML_MATCH_UNSPECIFIED",
		1: "AML_MATCH_FOUND",
		2: "AML_MATCH_NOT_FOUND",
		3: "AML_MATCH_ERROR",
	}
	AmlMatch_value = map[string]int32{
		"AML_MATCH_UNSPECIFIED": 0,
		"AML_MATCH_FOUND":       1,
		"AML_MATCH_NOT_FOUND":   2,
		"AML_MATCH_ERROR":       3,
	}
)

func (x AmlMatch) Enum() *AmlMatch {
	p := new(AmlMatch)
	*p = x
	return p
}

func (x AmlMatch) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AmlMatch) Descriptor() protoreflect.EnumDescriptor {
	return file_api_tsp_aml_data_proto_enumTypes[5].Descriptor()
}

func (AmlMatch) Type() protoreflect.EnumType {
	return &file_api_tsp_aml_data_proto_enumTypes[5]
}

func (x AmlMatch) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AmlMatch.Descriptor instead.
func (AmlMatch) EnumDescriptor() ([]byte, []int) {
	return file_api_tsp_aml_data_proto_rawDescGZIP(), []int{5}
}

type ReviewStatus int32

const (
	ReviewStatus_REVIEW_STATUS_UNSPECIFIED ReviewStatus = 0
	// review is pending by ops agent
	ReviewStatus_REVIEW_STATUS_PENDING ReviewStatus = 1
	// review is completed by ops agent
	// we cannot store review info like accepted/rejected in epifi DB. So status is stored only as pending or completed
	// UPD: Storing the review decision (accepted/rejected) is now allowed. This enables clients to
	// move ahead without having to wait for the complete decision reasoning,
	// which may arrive async and is needed only for auditing purposes.
	//
	// Deprecated: Marked as deprecated in api/tsp/aml/data.proto.
	ReviewStatus_REVIEW_STATUS_COMPLETED ReviewStatus = 2
	// Case deemed as a false positive and hence is approved by the ops agent
	// The user should NOT be assumed as a person dealing in fraudulent or money laundering activities
	ReviewStatus_REVIEW_STATUS_APPROVED ReviewStatus = 3
	// Case deemed a true positive and hence is rejected by the ops agent
	// The user should be assumed as a person dealing in fraudulent or money laundering activities
	ReviewStatus_REVIEW_STATUS_REJECTED ReviewStatus = 4
)

// Enum value maps for ReviewStatus.
var (
	ReviewStatus_name = map[int32]string{
		0: "REVIEW_STATUS_UNSPECIFIED",
		1: "REVIEW_STATUS_PENDING",
		2: "REVIEW_STATUS_COMPLETED",
		3: "REVIEW_STATUS_APPROVED",
		4: "REVIEW_STATUS_REJECTED",
	}
	ReviewStatus_value = map[string]int32{
		"REVIEW_STATUS_UNSPECIFIED": 0,
		"REVIEW_STATUS_PENDING":     1,
		"REVIEW_STATUS_COMPLETED":   2,
		"REVIEW_STATUS_APPROVED":    3,
		"REVIEW_STATUS_REJECTED":    4,
	}
)

func (x ReviewStatus) Enum() *ReviewStatus {
	p := new(ReviewStatus)
	*p = x
	return p
}

func (x ReviewStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReviewStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_tsp_aml_data_proto_enumTypes[6].Descriptor()
}

func (ReviewStatus) Type() protoreflect.EnumType {
	return &file_api_tsp_aml_data_proto_enumTypes[6]
}

func (x ReviewStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReviewStatus.Descriptor instead.
func (ReviewStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_tsp_aml_data_proto_rawDescGZIP(), []int{6}
}

type CustomerDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the customer - Mandatory
	Name *common.Name `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// father name - NOT Mandatory
	FatherName *common.Name `protobuf:"bytes,2,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
	// mother name - NOT Mandatory
	MotherName *common.Name `protobuf:"bytes,3,opt,name=mother_name,json=motherName,proto3" json:"mother_name,omitempty"`
	// gender - NOT Mandatory
	Gender common.Gender `protobuf:"varint,4,opt,name=gender,proto3,enum=api.typesv2.common.Gender" json:"gender,omitempty"`
	// marital status - NOT Mandatory
	MaritalStatus common.MaritalStatus `protobuf:"varint,5,opt,name=marital_status,json=maritalStatus,proto3,enum=api.typesv2.common.MaritalStatus" json:"marital_status,omitempty"`
	// income slab - NOT Mandatory
	IncomeSlab common.IncomeSlab `protobuf:"varint,6,opt,name=income_slab,json=incomeSlab,proto3,enum=api.typesv2.common.IncomeSlab" json:"income_slab,omitempty"`
	// pan number - NOT Mandatory
	PanNumber string `protobuf:"bytes,7,opt,name=pan_number,json=panNumber,proto3" json:"pan_number,omitempty"`
	// nationality - Mandatory
	Nationality common.Nationality `protobuf:"varint,8,opt,name=nationality,proto3,enum=api.typesv2.common.Nationality" json:"nationality,omitempty"`
	// passport id number - NOT Mandatory
	PassportNumber string `protobuf:"bytes,9,opt,name=passport_number,json=passportNumber,proto3" json:"passport_number,omitempty"`
	// passport expiry date - Mandatory if passport provided
	PassportExpiryDate *date.Date `protobuf:"bytes,10,opt,name=passport_expiry_date,json=passportExpiryDate,proto3" json:"passport_expiry_date,omitempty"`
	// driving license id number - NOT Mandatory
	DrivingLicenseNumber string `protobuf:"bytes,11,opt,name=driving_license_number,json=drivingLicenseNumber,proto3" json:"driving_license_number,omitempty"`
	// passport expiry date - Mandatory if driving license provided
	DrivingLicenseExpiryDate *date.Date `protobuf:"bytes,12,opt,name=driving_license_expiry_date,json=drivingLicenseExpiryDate,proto3" json:"driving_license_expiry_date,omitempty"`
	// voter id number - NOT Mandatory
	VoterId string `protobuf:"bytes,13,opt,name=voter_id,json=voterId,proto3" json:"voter_id,omitempty"`
	// document type of proof of address provided - NOT Mandatory
	PoaType common.DocumentProofType `protobuf:"varint,14,opt,name=poa_type,json=poaType,proto3,enum=api.typesv2.common.DocumentProofType" json:"poa_type,omitempty"`
	// phone number - NOT Mandatory
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,15,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// email - NOT Mandatory
	Email string `protobuf:"bytes,16,opt,name=email,proto3" json:"email,omitempty"`
	// date of birth - NOT Mandatory
	DateOfBirth *date.Date `protobuf:"bytes,17,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
	// permanent address - NOT Mandatory
	PermanentAddress *common.PostalAddress `protobuf:"bytes,18,opt,name=permanent_address,json=permanentAddress,proto3" json:"permanent_address,omitempty"`
	// correspondence address - NOT Mandatory
	CorrespondenceAddress *common.PostalAddress `protobuf:"bytes,19,opt,name=correspondence_address,json=correspondenceAddress,proto3" json:"correspondence_address,omitempty"`
	// politically exposed status - NOT Mandatory
	PoliticallyExposedStatus common.PoliticallyExposedStatus `protobuf:"varint,20,opt,name=politically_exposed_status,json=politicallyExposedStatus,proto3,enum=api.typesv2.common.PoliticallyExposedStatus" json:"politically_exposed_status,omitempty"`
	// employment type - NOT Mandatory
	EmploymentType common.EmploymentType `protobuf:"varint,21,opt,name=employment_type,json=employmentType,proto3,enum=api.typesv2.common.EmploymentType" json:"employment_type,omitempty"`
}

func (x *CustomerDetails) Reset() {
	*x = CustomerDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tsp_aml_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerDetails) ProtoMessage() {}

func (x *CustomerDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_tsp_aml_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerDetails.ProtoReflect.Descriptor instead.
func (*CustomerDetails) Descriptor() ([]byte, []int) {
	return file_api_tsp_aml_data_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerDetails) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *CustomerDetails) GetFatherName() *common.Name {
	if x != nil {
		return x.FatherName
	}
	return nil
}

func (x *CustomerDetails) GetMotherName() *common.Name {
	if x != nil {
		return x.MotherName
	}
	return nil
}

func (x *CustomerDetails) GetGender() common.Gender {
	if x != nil {
		return x.Gender
	}
	return common.Gender(0)
}

func (x *CustomerDetails) GetMaritalStatus() common.MaritalStatus {
	if x != nil {
		return x.MaritalStatus
	}
	return common.MaritalStatus(0)
}

func (x *CustomerDetails) GetIncomeSlab() common.IncomeSlab {
	if x != nil {
		return x.IncomeSlab
	}
	return common.IncomeSlab(0)
}

func (x *CustomerDetails) GetPanNumber() string {
	if x != nil {
		return x.PanNumber
	}
	return ""
}

func (x *CustomerDetails) GetNationality() common.Nationality {
	if x != nil {
		return x.Nationality
	}
	return common.Nationality(0)
}

func (x *CustomerDetails) GetPassportNumber() string {
	if x != nil {
		return x.PassportNumber
	}
	return ""
}

func (x *CustomerDetails) GetPassportExpiryDate() *date.Date {
	if x != nil {
		return x.PassportExpiryDate
	}
	return nil
}

func (x *CustomerDetails) GetDrivingLicenseNumber() string {
	if x != nil {
		return x.DrivingLicenseNumber
	}
	return ""
}

func (x *CustomerDetails) GetDrivingLicenseExpiryDate() *date.Date {
	if x != nil {
		return x.DrivingLicenseExpiryDate
	}
	return nil
}

func (x *CustomerDetails) GetVoterId() string {
	if x != nil {
		return x.VoterId
	}
	return ""
}

func (x *CustomerDetails) GetPoaType() common.DocumentProofType {
	if x != nil {
		return x.PoaType
	}
	return common.DocumentProofType(0)
}

func (x *CustomerDetails) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *CustomerDetails) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CustomerDetails) GetDateOfBirth() *date.Date {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *CustomerDetails) GetPermanentAddress() *common.PostalAddress {
	if x != nil {
		return x.PermanentAddress
	}
	return nil
}

func (x *CustomerDetails) GetCorrespondenceAddress() *common.PostalAddress {
	if x != nil {
		return x.CorrespondenceAddress
	}
	return nil
}

func (x *CustomerDetails) GetPoliticallyExposedStatus() common.PoliticallyExposedStatus {
	if x != nil {
		return x.PoliticallyExposedStatus
	}
	return common.PoliticallyExposedStatus(0)
}

func (x *CustomerDetails) GetEmploymentType() common.EmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return common.EmploymentType(0)
}

// MatchData has the details of the match to be sent to the calling service
type MatchData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// watchlist category against which match is found
	//
	// Deprecated: Marked as deprecated in api/tsp/aml/data.proto.
	MatchingWatchlistCategory AmlWatchlistCategory `protobuf:"varint,1,opt,name=matching_watchlist_category,json=matchingWatchlistCategory,proto3,enum=tsp.aml.AmlWatchlistCategory" json:"matching_watchlist_category,omitempty"`
	// actual watchlist name for which match is found
	//
	// Deprecated: Marked as deprecated in api/tsp/aml/data.proto.
	MatchingWatchlist string `protobuf:"bytes,2,opt,name=matching_watchlist,json=matchingWatchlist,proto3" json:"matching_watchlist,omitempty"`
	// name of the customer in the watchlist record
	MatchingRecordName string `protobuf:"bytes,3,opt,name=matching_record_name,json=matchingRecordName,proto3" json:"matching_record_name,omitempty"`
	// parameter for which the match is found
	MatchingParameter AmlParameter `protobuf:"varint,4,opt,name=matching_parameter,json=matchingParameter,proto3,enum=tsp.aml.AmlParameter" json:"matching_parameter,omitempty"`
	// applicable when fuzzy matching is enabled
	MatchingPercentage float64 `protobuf:"fixed64,5,opt,name=matching_percentage,json=matchingPercentage,proto3" json:"matching_percentage,omitempty"`
}

func (x *MatchData) Reset() {
	*x = MatchData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tsp_aml_data_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchData) ProtoMessage() {}

func (x *MatchData) ProtoReflect() protoreflect.Message {
	mi := &file_api_tsp_aml_data_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchData.ProtoReflect.Descriptor instead.
func (*MatchData) Descriptor() ([]byte, []int) {
	return file_api_tsp_aml_data_proto_rawDescGZIP(), []int{1}
}

// Deprecated: Marked as deprecated in api/tsp/aml/data.proto.
func (x *MatchData) GetMatchingWatchlistCategory() AmlWatchlistCategory {
	if x != nil {
		return x.MatchingWatchlistCategory
	}
	return AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/tsp/aml/data.proto.
func (x *MatchData) GetMatchingWatchlist() string {
	if x != nil {
		return x.MatchingWatchlist
	}
	return ""
}

func (x *MatchData) GetMatchingRecordName() string {
	if x != nil {
		return x.MatchingRecordName
	}
	return ""
}

func (x *MatchData) GetMatchingParameter() AmlParameter {
	if x != nil {
		return x.MatchingParameter
	}
	return AmlParameter_AML_PARAMETER_UNSPECIFIED
}

func (x *MatchData) GetMatchingPercentage() float64 {
	if x != nil {
		return x.MatchingPercentage
	}
	return 0
}

var File_api_tsp_aml_data_proto protoreflect.FileDescriptor

var file_api_tsp_aml_data_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x73, 0x70, 0x2f, 0x61, 0x6d, 0x6c, 0x2f, 0x64, 0x61,
	0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x74, 0x73, 0x70, 0x2e, 0x61, 0x6d,
	0x6c, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x67, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69, 0x6e, 0x63, 0x6f,
	0x6d, 0x65, 0x5f, 0x73, 0x6c, 0x61, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c,
	0x6c, 0x79, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb1, 0x0a, 0x0a, 0x0f, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x36, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x4e, 0x61, 0x6d, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x0a, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x39, 0x0a, 0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a,
	0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x67, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x48,
	0x0a, 0x0e, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x61, 0x72, 0x69,
	0x74, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x6d, 0x61, 0x72, 0x69, 0x74,
	0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a, 0x0b, 0x69, 0x6e, 0x63, 0x6f,
	0x6d, 0x65, 0x5f, 0x73, 0x6c, 0x61, 0x62, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x53, 0x6c, 0x61, 0x62, 0x52, 0x0a, 0x69,
	0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x53, 0x6c, 0x61, 0x62, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x6e,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x61, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x4b, 0x0a, 0x0b, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x43,
	0x0a, 0x14, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52,
	0x12, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x64, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x6c,
	0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x14, 0x64, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x63, 0x65,
	0x6e, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x50, 0x0a, 0x1b, 0x64, 0x72, 0x69,
	0x76, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x18, 0x64, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73,
	0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x76,
	0x6f, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76,
	0x6f, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x08, 0x70, 0x6f, 0x61, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x07, 0x70, 0x6f, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52,
	0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x35, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69,
	0x72, 0x74, 0x68, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x64, 0x61,
	0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x12, 0x4e, 0x0a, 0x11, 0x70, 0x65, 0x72,
	0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65,
	0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x58, 0x0a, 0x16, 0x63, 0x6f, 0x72,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x15, 0x63, 0x6f,
	0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x6a, 0x0a, 0x1a, 0x70, 0x6f, 0x6c, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c,
	0x6c, 0x79, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x6c,
	0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x6c, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x18, 0x70, 0x6f, 0x6c, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c,
	0x6c, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x4b, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x65, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0xca, 0x02, 0x0a,
	0x09, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x12, 0x61, 0x0a, 0x1b, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x5f, 0x77, 0x61, 0x74, 0x63, 0x68, 0x6c, 0x69, 0x73, 0x74,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1d, 0x2e, 0x74, 0x73, 0x70, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x41, 0x6d, 0x6c, 0x57, 0x61, 0x74,
	0x63, 0x68, 0x6c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x19, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x74, 0x63,
	0x68, 0x6c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x31, 0x0a,
	0x12, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x5f, 0x77, 0x61, 0x74, 0x63, 0x68, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x74, 0x63, 0x68, 0x6c, 0x69, 0x73, 0x74,
	0x12, 0x30, 0x0a, 0x14, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x44, 0x0a, 0x12, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x5f, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x74, 0x73, 0x70, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x41, 0x6d, 0x6c, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x11, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x13, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x50,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x2a, 0x9a, 0x01, 0x0a, 0x0a, 0x41, 0x6d,
	0x6c, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x4d, 0x4c, 0x5f,
	0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x52, 0x4f,
	0x44, 0x55, 0x43, 0x54, 0x5f, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x10, 0x01,
	0x12, 0x1c, 0x0a, 0x18, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f,
	0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x02, 0x12, 0x1f,
	0x0a, 0x1b, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x53, 0x41,
	0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x03, 0x12,
	0x15, 0x0a, 0x11, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x53, 0x10, 0x04, 0x2a, 0x83, 0x01, 0x0a, 0x10, 0x41, 0x6d, 0x6c, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x41,
	0x4d, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x4f, 0x44,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x23, 0x0a, 0x1f, 0x41, 0x4d, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x49, 0x4e, 0x47,
	0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x45, 0x52, 0x10, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x4d, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x02, 0x2a, 0xb0, 0x02, 0x0a,
	0x14, 0x41, 0x6d, 0x6c, 0x57, 0x61, 0x74, 0x63, 0x68, 0x6c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x4d, 0x4c, 0x5f, 0x57, 0x41, 0x54,
	0x43, 0x48, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x24, 0x0a,
	0x20, 0x41, 0x4d, 0x4c, 0x5f, 0x57, 0x41, 0x54, 0x43, 0x48, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x53, 0x41, 0x4e, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x53, 0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26, 0x41, 0x4d, 0x4c, 0x5f, 0x57, 0x41, 0x54, 0x43, 0x48,
	0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4c, 0x41,
	0x57, 0x5f, 0x45, 0x4e, 0x46, 0x4f, 0x52, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12,
	0x31, 0x0a, 0x2d, 0x41, 0x4d, 0x4c, 0x5f, 0x57, 0x41, 0x54, 0x43, 0x48, 0x4c, 0x49, 0x53, 0x54,
	0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x52, 0x45, 0x47, 0x55, 0x4c, 0x41,
	0x54, 0x4f, 0x52, 0x59, 0x5f, 0x45, 0x4e, 0x46, 0x4f, 0x52, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x4d, 0x4c, 0x5f, 0x57, 0x41, 0x54, 0x43, 0x48, 0x4c,
	0x49, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x50, 0x45, 0x50,
	0x10, 0x04, 0x12, 0x28, 0x0a, 0x24, 0x41, 0x4d, 0x4c, 0x5f, 0x57, 0x41, 0x54, 0x43, 0x48, 0x4c,
	0x49, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x44, 0x56,
	0x45, 0x52, 0x53, 0x45, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x10, 0x05, 0x12, 0x21, 0x0a, 0x1d,
	0x41, 0x4d, 0x4c, 0x5f, 0x57, 0x41, 0x54, 0x43, 0x48, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x53, 0x10, 0x06, 0x2a,
	0xa1, 0x01, 0x0a, 0x12, 0x41, 0x6d, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x4d, 0x4c, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e,
	0x41, 0x4d, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01,
	0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x4d, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x49, 0x4e,
	0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x02, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x4d, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x49,
	0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x10, 0x03, 0x2a, 0xd7, 0x03, 0x0a, 0x0c, 0x41, 0x6d, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41,
	0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d,
	0x45, 0x54, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x4e, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x4d,
	0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x53, 0x53,
	0x50, 0x4f, 0x52, 0x54, 0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x41,
	0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x44, 0x52, 0x49, 0x56, 0x49, 0x4e, 0x47, 0x5f,
	0x4c, 0x49, 0x43, 0x45, 0x4e, 0x53, 0x45, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x12, 0x41, 0x4d, 0x4c,
	0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10,
	0x04, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x41, 0x52,
	0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x41, 0x43, 0x54, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x10, 0x05, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d,
	0x45, 0x54, 0x45, 0x52, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x10, 0x06, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d,
	0x45, 0x54, 0x45, 0x52, 0x5f, 0x46, 0x55, 0x5a, 0x5a, 0x59, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10,
	0x07, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54,
	0x45, 0x52, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10,
	0x08, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54,
	0x45, 0x52, 0x5f, 0x56, 0x4f, 0x57, 0x45, 0x4c, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x09, 0x12,
	0x1c, 0x0a, 0x18, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52,
	0x5f, 0x41, 0x4c, 0x49, 0x41, 0x53, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x0a, 0x12, 0x15, 0x0a,
	0x11, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x44,
	0x49, 0x4e, 0x10, 0x0b, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41,
	0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x46, 0x55, 0x5a, 0x5a, 0x59, 0x5f, 0x44, 0x4f, 0x42, 0x10,
	0x0c, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54,
	0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x0d, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x4d,
	0x4c, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x52, 0x59, 0x10, 0x0e, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x4d, 0x4c, 0x5f, 0x50, 0x41, 0x52,
	0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x43, 0x49, 0x54, 0x59, 0x10, 0x0f, 0x2a, 0x68, 0x0a,
	0x08, 0x41, 0x6d, 0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x4d, 0x4c,
	0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x4d, 0x4c, 0x5f, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x4d, 0x4c,
	0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44,
	0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x4d, 0x4c, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x03, 0x2a, 0xa1, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x45, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x10, 0x01, 0x12, 0x1f, 0x0a, 0x17, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x02, 0x1a,
	0x02, 0x08, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x03, 0x12,
	0x1a, 0x0a, 0x16, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x04, 0x42, 0x4e, 0x0a, 0x25, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x72, 0x69, 0x6e, 0x67, 0x6f, 0x74, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x73, 0x70,
	0x2e, 0x61, 0x6d, 0x6c, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x72, 0x69, 0x6e, 0x67, 0x6f, 0x74, 0x74, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x73, 0x70, 0x2f, 0x61, 0x6d, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_tsp_aml_data_proto_rawDescOnce sync.Once
	file_api_tsp_aml_data_proto_rawDescData = file_api_tsp_aml_data_proto_rawDesc
)

func file_api_tsp_aml_data_proto_rawDescGZIP() []byte {
	file_api_tsp_aml_data_proto_rawDescOnce.Do(func() {
		file_api_tsp_aml_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_tsp_aml_data_proto_rawDescData)
	})
	return file_api_tsp_aml_data_proto_rawDescData
}

var file_api_tsp_aml_data_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_api_tsp_aml_data_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_tsp_aml_data_proto_goTypes = []interface{}{
	(AmlProduct)(0),                      // 0: tsp.aml.AmlProduct
	(AmlScreeningMode)(0),                // 1: tsp.aml.AmlScreeningMode
	(AmlWatchlistCategory)(0),            // 2: tsp.aml.AmlWatchlistCategory
	(AmlScreeningStatus)(0),              // 3: tsp.aml.AmlScreeningStatus
	(AmlParameter)(0),                    // 4: tsp.aml.AmlParameter
	(AmlMatch)(0),                        // 5: tsp.aml.AmlMatch
	(ReviewStatus)(0),                    // 6: tsp.aml.ReviewStatus
	(*CustomerDetails)(nil),              // 7: tsp.aml.CustomerDetails
	(*MatchData)(nil),                    // 8: tsp.aml.MatchData
	(*common.Name)(nil),                  // 9: api.typesv2.common.Name
	(common.Gender)(0),                   // 10: api.typesv2.common.Gender
	(common.MaritalStatus)(0),            // 11: api.typesv2.common.MaritalStatus
	(common.IncomeSlab)(0),               // 12: api.typesv2.common.IncomeSlab
	(common.Nationality)(0),              // 13: api.typesv2.common.Nationality
	(*date.Date)(nil),                    // 14: google.type.Date
	(common.DocumentProofType)(0),        // 15: api.typesv2.common.DocumentProofType
	(*common.PhoneNumber)(nil),           // 16: api.typesv2.common.PhoneNumber
	(*common.PostalAddress)(nil),         // 17: api.typesv2.common.PostalAddress
	(common.PoliticallyExposedStatus)(0), // 18: api.typesv2.common.PoliticallyExposedStatus
	(common.EmploymentType)(0),           // 19: api.typesv2.common.EmploymentType
}
var file_api_tsp_aml_data_proto_depIdxs = []int32{
	9,  // 0: tsp.aml.CustomerDetails.name:type_name -> api.typesv2.common.Name
	9,  // 1: tsp.aml.CustomerDetails.father_name:type_name -> api.typesv2.common.Name
	9,  // 2: tsp.aml.CustomerDetails.mother_name:type_name -> api.typesv2.common.Name
	10, // 3: tsp.aml.CustomerDetails.gender:type_name -> api.typesv2.common.Gender
	11, // 4: tsp.aml.CustomerDetails.marital_status:type_name -> api.typesv2.common.MaritalStatus
	12, // 5: tsp.aml.CustomerDetails.income_slab:type_name -> api.typesv2.common.IncomeSlab
	13, // 6: tsp.aml.CustomerDetails.nationality:type_name -> api.typesv2.common.Nationality
	14, // 7: tsp.aml.CustomerDetails.passport_expiry_date:type_name -> google.type.Date
	14, // 8: tsp.aml.CustomerDetails.driving_license_expiry_date:type_name -> google.type.Date
	15, // 9: tsp.aml.CustomerDetails.poa_type:type_name -> api.typesv2.common.DocumentProofType
	16, // 10: tsp.aml.CustomerDetails.phone_number:type_name -> api.typesv2.common.PhoneNumber
	14, // 11: tsp.aml.CustomerDetails.date_of_birth:type_name -> google.type.Date
	17, // 12: tsp.aml.CustomerDetails.permanent_address:type_name -> api.typesv2.common.PostalAddress
	17, // 13: tsp.aml.CustomerDetails.correspondence_address:type_name -> api.typesv2.common.PostalAddress
	18, // 14: tsp.aml.CustomerDetails.politically_exposed_status:type_name -> api.typesv2.common.PoliticallyExposedStatus
	19, // 15: tsp.aml.CustomerDetails.employment_type:type_name -> api.typesv2.common.EmploymentType
	2,  // 16: tsp.aml.MatchData.matching_watchlist_category:type_name -> tsp.aml.AmlWatchlistCategory
	4,  // 17: tsp.aml.MatchData.matching_parameter:type_name -> tsp.aml.AmlParameter
	18, // [18:18] is the sub-list for method output_type
	18, // [18:18] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_api_tsp_aml_data_proto_init() }
func file_api_tsp_aml_data_proto_init() {
	if File_api_tsp_aml_data_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_tsp_aml_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tsp_aml_data_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_tsp_aml_data_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_tsp_aml_data_proto_goTypes,
		DependencyIndexes: file_api_tsp_aml_data_proto_depIdxs,
		EnumInfos:         file_api_tsp_aml_data_proto_enumTypes,
		MessageInfos:      file_api_tsp_aml_data_proto_msgTypes,
	}.Build()
	File_api_tsp_aml_data_proto = out.File
	file_api_tsp_aml_data_proto_rawDesc = nil
	file_api_tsp_aml_data_proto_goTypes = nil
	file_api_tsp_aml_data_proto_depIdxs = nil
}
