syntax = "proto3";

package vendors.inhouse;

option go_package = "github.com/epifi/gamma/api/vendors/inhouse";
option java_package = "com.github.epifi.gamma.api.vendors.inhouse";

message GetPDScoreRequest {
  string actor_id = 1 [json_name = "actor_id"];
  Context context = 2 [json_name = "context"];
  string credit_report = 3 [json_name = "credit_report"];
}

message Context {
  string model_context = 1 [json_name = "model_context"];
  string model_version = 2 [json_name = "model_version"];
}

message GetPDScoreResponse {
  double score = 1 [json_name = "score"];
  string model_context = 2 [json_name = "model_context"];
  string model_version = 3 [json_name = "model_version"];
}

message GetLoanAffinityScoreRequest {
  string actor_id = 1 [json_name = "actor_id"];
  Context context = 2 [json_name = "context"];
  string credit_report = 3 [json_name = "credit_report"];
  UserDetails user_details = 4 [json_name = "user_details"];
  DeviceDetails device_details = 5 [json_name = "device_details"];
  EmploymentDetails employment_details = 6 [json_name = "employment_details"];

  message UserDetails {
    string dob = 1 [json_name = "dob"];
    double latitude = 2 [json_name = "latitude"];
    double longitude = 3 [json_name = "longitude"];
  }
  message DeviceDetails {
    string model = 1 [json_name = "device_model"];
    string manufacturer = 2 [json_name = "device_manufacturer"];
  }
  message EmploymentDetails {
    string employment_type = 1 [json_name = "employment_type"];
  }
}

message GetLoanAffinityScoreResponse {
  double score = 1 [json_name = "score"];
  string model_context = 2 [json_name = "model_context"];
  string model_version = 3 [json_name = "model_version"];
}
