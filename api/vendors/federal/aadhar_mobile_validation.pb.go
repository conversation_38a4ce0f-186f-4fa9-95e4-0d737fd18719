// Defines a service for integrating with the aadhar mobile validate API provided by the partner bank

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/federal/aadhar_mobile_validation.proto

package federal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AadharMobileValidationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestid,proto3" json:"request_id,omitempty"`
	RrnNo     string `protobuf:"bytes,2,opt,name=rrn_no,json=rrnno,proto3" json:"rrn_no,omitempty"`
	UserId    string `protobuf:"bytes,3,opt,name=user_id,json=userid,proto3" json:"user_id,omitempty"`
	Mobile    string `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile,omitempty"`
}

func (x *AadharMobileValidationRequest) Reset() {
	*x = AadharMobileValidationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_aadhar_mobile_validation_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AadharMobileValidationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AadharMobileValidationRequest) ProtoMessage() {}

func (x *AadharMobileValidationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_aadhar_mobile_validation_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AadharMobileValidationRequest.ProtoReflect.Descriptor instead.
func (*AadharMobileValidationRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_aadhar_mobile_validation_proto_rawDescGZIP(), []int{0}
}

func (x *AadharMobileValidationRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *AadharMobileValidationRequest) GetRrnNo() string {
	if x != nil {
		return x.RrnNo
	}
	return ""
}

func (x *AadharMobileValidationRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AadharMobileValidationRequest) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

type AadharMobileValidationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestid,proto3" json:"request_id,omitempty"`
	Status    string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	Message   string `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *AadharMobileValidationResponse) Reset() {
	*x = AadharMobileValidationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_aadhar_mobile_validation_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AadharMobileValidationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AadharMobileValidationResponse) ProtoMessage() {}

func (x *AadharMobileValidationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_aadhar_mobile_validation_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AadharMobileValidationResponse.ProtoReflect.Descriptor instead.
func (*AadharMobileValidationResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_aadhar_mobile_validation_proto_rawDescGZIP(), []int{1}
}

func (x *AadharMobileValidationResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *AadharMobileValidationResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AadharMobileValidationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_api_vendors_federal_aadhar_mobile_validation_proto protoreflect.FileDescriptor

var file_api_vendors_federal_aadhar_mobile_validation_proto_rawDesc = []byte{
	0x0a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65,
	0x64, 0x65, 0x72, 0x61, 0x6c, 0x2f, 0x61, 0x61, 0x64, 0x68, 0x61, 0x72, 0x5f, 0x6d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65,
	0x64, 0x65, 0x72, 0x61, 0x6c, 0x22, 0x86, 0x01, 0x0a, 0x1d, 0x41, 0x61, 0x64, 0x68, 0x61, 0x72,
	0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x72, 0x6e, 0x5f, 0x6e, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x72, 0x6e, 0x6e, 0x6f, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x22, 0x71,
	0x0a, 0x1e, 0x41, 0x61, 0x64, 0x68, 0x61, 0x72, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x42, 0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x5a,
	0x2a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_federal_aadhar_mobile_validation_proto_rawDescOnce sync.Once
	file_api_vendors_federal_aadhar_mobile_validation_proto_rawDescData = file_api_vendors_federal_aadhar_mobile_validation_proto_rawDesc
)

func file_api_vendors_federal_aadhar_mobile_validation_proto_rawDescGZIP() []byte {
	file_api_vendors_federal_aadhar_mobile_validation_proto_rawDescOnce.Do(func() {
		file_api_vendors_federal_aadhar_mobile_validation_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_federal_aadhar_mobile_validation_proto_rawDescData)
	})
	return file_api_vendors_federal_aadhar_mobile_validation_proto_rawDescData
}

var file_api_vendors_federal_aadhar_mobile_validation_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_vendors_federal_aadhar_mobile_validation_proto_goTypes = []interface{}{
	(*AadharMobileValidationRequest)(nil),  // 0: vendors.federal.AadharMobileValidationRequest
	(*AadharMobileValidationResponse)(nil), // 1: vendors.federal.AadharMobileValidationResponse
}
var file_api_vendors_federal_aadhar_mobile_validation_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendors_federal_aadhar_mobile_validation_proto_init() }
func file_api_vendors_federal_aadhar_mobile_validation_proto_init() {
	if File_api_vendors_federal_aadhar_mobile_validation_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_federal_aadhar_mobile_validation_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AadharMobileValidationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_aadhar_mobile_validation_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AadharMobileValidationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_federal_aadhar_mobile_validation_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_federal_aadhar_mobile_validation_proto_goTypes,
		DependencyIndexes: file_api_vendors_federal_aadhar_mobile_validation_proto_depIdxs,
		MessageInfos:      file_api_vendors_federal_aadhar_mobile_validation_proto_msgTypes,
	}.Build()
	File_api_vendors_federal_aadhar_mobile_validation_proto = out.File
	file_api_vendors_federal_aadhar_mobile_validation_proto_rawDesc = nil
	file_api_vendors_federal_aadhar_mobile_validation_proto_goTypes = nil
	file_api_vendors_federal_aadhar_mobile_validation_proto_depIdxs = nil
}
