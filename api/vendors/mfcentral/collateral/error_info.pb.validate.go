// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/mfcentral/collateral/error_info.proto

package collateral

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ErrorInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ErrorInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ErrorInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ErrorInfoMultiError, or nil
// if none found.
func (m *ErrorInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ErrorInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrorCode

	// no validation rules for ErrorMessage

	if len(errors) > 0 {
		return ErrorInfoMultiError(errors)
	}

	return nil
}

// ErrorInfoMultiError is an error wrapping multiple validation errors returned
// by ErrorInfo.ValidateAll() if the designated constraints aren't met.
type ErrorInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ErrorInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ErrorInfoMultiError) AllErrors() []error { return m }

// ErrorInfoValidationError is the validation error returned by
// ErrorInfo.Validate if the designated constraints aren't met.
type ErrorInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ErrorInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ErrorInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ErrorInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ErrorInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ErrorInfoValidationError) ErrorName() string { return "ErrorInfoValidationError" }

// Error satisfies the builtin error interface
func (e ErrorInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sErrorInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ErrorInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ErrorInfoValidationError{}
