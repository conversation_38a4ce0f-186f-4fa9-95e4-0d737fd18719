// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/mfcentral/collateral/mfcentral.proto

package collateral

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MfcentralRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Request   string `protobuf:"bytes,1,opt,name=request,proto3" json:"request,omitempty"`
	Signature string `protobuf:"bytes,2,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *MfcentralRequest) Reset() {
	*x = MfcentralRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_mfcentral_collateral_mfcentral_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MfcentralRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MfcentralRequest) ProtoMessage() {}

func (x *MfcentralRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_mfcentral_collateral_mfcentral_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MfcentralRequest.ProtoReflect.Descriptor instead.
func (*MfcentralRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_mfcentral_collateral_mfcentral_proto_rawDescGZIP(), []int{0}
}

func (x *MfcentralRequest) GetRequest() string {
	if x != nil {
		return x.Request
	}
	return ""
}

func (x *MfcentralRequest) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

type MfcentralResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Response  string `protobuf:"bytes,1,opt,name=response,proto3" json:"response,omitempty"`
	Signature string `protobuf:"bytes,2,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *MfcentralResponse) Reset() {
	*x = MfcentralResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_mfcentral_collateral_mfcentral_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MfcentralResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MfcentralResponse) ProtoMessage() {}

func (x *MfcentralResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_mfcentral_collateral_mfcentral_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MfcentralResponse.ProtoReflect.Descriptor instead.
func (*MfcentralResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_mfcentral_collateral_mfcentral_proto_rawDescGZIP(), []int{1}
}

func (x *MfcentralResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *MfcentralResponse) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

var File_api_vendors_mfcentral_collateral_mfcentral_proto protoreflect.FileDescriptor

var file_api_vendors_mfcentral_collateral_mfcentral_proto_rawDesc = []byte{
	0x0a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x66,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72,
	0x61, 0x6c, 0x2f, 0x6d, 0x66, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x66, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c,
	0x22, 0x4a, 0x0a, 0x10, 0x4d, 0x66, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c,
	0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22, 0x4d, 0x0a, 0x11,
	0x4d, 0x66, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x42, 0x39, 0x5a, 0x37, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2f, 0x6d, 0x66, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6c, 0x6c,
	0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_mfcentral_collateral_mfcentral_proto_rawDescOnce sync.Once
	file_api_vendors_mfcentral_collateral_mfcentral_proto_rawDescData = file_api_vendors_mfcentral_collateral_mfcentral_proto_rawDesc
)

func file_api_vendors_mfcentral_collateral_mfcentral_proto_rawDescGZIP() []byte {
	file_api_vendors_mfcentral_collateral_mfcentral_proto_rawDescOnce.Do(func() {
		file_api_vendors_mfcentral_collateral_mfcentral_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_mfcentral_collateral_mfcentral_proto_rawDescData)
	})
	return file_api_vendors_mfcentral_collateral_mfcentral_proto_rawDescData
}

var file_api_vendors_mfcentral_collateral_mfcentral_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_vendors_mfcentral_collateral_mfcentral_proto_goTypes = []interface{}{
	(*MfcentralRequest)(nil),  // 0: vendors.mfcentral.collateral.MfcentralRequest
	(*MfcentralResponse)(nil), // 1: vendors.mfcentral.collateral.MfcentralResponse
}
var file_api_vendors_mfcentral_collateral_mfcentral_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendors_mfcentral_collateral_mfcentral_proto_init() }
func file_api_vendors_mfcentral_collateral_mfcentral_proto_init() {
	if File_api_vendors_mfcentral_collateral_mfcentral_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_mfcentral_collateral_mfcentral_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MfcentralRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_mfcentral_collateral_mfcentral_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MfcentralResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_mfcentral_collateral_mfcentral_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_mfcentral_collateral_mfcentral_proto_goTypes,
		DependencyIndexes: file_api_vendors_mfcentral_collateral_mfcentral_proto_depIdxs,
		MessageInfos:      file_api_vendors_mfcentral_collateral_mfcentral_proto_msgTypes,
	}.Build()
	File_api_vendors_mfcentral_collateral_mfcentral_proto = out.File
	file_api_vendors_mfcentral_collateral_mfcentral_proto_rawDesc = nil
	file_api_vendors_mfcentral_collateral_mfcentral_proto_goTypes = nil
	file_api_vendors_mfcentral_collateral_mfcentral_proto_depIdxs = nil
}
