// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/saven/creditcard/cc_onboarding_state_update_event.proto

package creditcard

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on
// ProcessCreditCardOnboardingStateUpdateEventRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ProcessCreditCardOnboardingStateUpdateEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessCreditCardOnboardingStateUpdateEventRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ProcessCreditCardOnboardingStateUpdateEventRequestMultiError, or nil if
// none found.
func (m *ProcessCreditCardOnboardingStateUpdateEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCreditCardOnboardingStateUpdateEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetEventPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCreditCardOnboardingStateUpdateEventRequestValidationError{
					field:  "EventPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCreditCardOnboardingStateUpdateEventRequestValidationError{
					field:  "EventPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEventPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCreditCardOnboardingStateUpdateEventRequestValidationError{
				field:  "EventPayload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCreditCardOnboardingStateUpdateEventRequestMultiError(errors)
	}

	return nil
}

// ProcessCreditCardOnboardingStateUpdateEventRequestMultiError is an error
// wrapping multiple validation errors returned by
// ProcessCreditCardOnboardingStateUpdateEventRequest.ValidateAll() if the
// designated constraints aren't met.
type ProcessCreditCardOnboardingStateUpdateEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCreditCardOnboardingStateUpdateEventRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCreditCardOnboardingStateUpdateEventRequestMultiError) AllErrors() []error { return m }

// ProcessCreditCardOnboardingStateUpdateEventRequestValidationError is the
// validation error returned by
// ProcessCreditCardOnboardingStateUpdateEventRequest.Validate if the
// designated constraints aren't met.
type ProcessCreditCardOnboardingStateUpdateEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCreditCardOnboardingStateUpdateEventRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ProcessCreditCardOnboardingStateUpdateEventRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ProcessCreditCardOnboardingStateUpdateEventRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ProcessCreditCardOnboardingStateUpdateEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCreditCardOnboardingStateUpdateEventRequestValidationError) ErrorName() string {
	return "ProcessCreditCardOnboardingStateUpdateEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCreditCardOnboardingStateUpdateEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCreditCardOnboardingStateUpdateEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCreditCardOnboardingStateUpdateEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCreditCardOnboardingStateUpdateEventRequestValidationError{}

// Validate checks the field values on
// CreditCardOnboardingStateUpdateEventPayload with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreditCardOnboardingStateUpdateEventPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreditCardOnboardingStateUpdateEventPayload with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CreditCardOnboardingStateUpdateEventPayloadMultiError, or nil if none found.
func (m *CreditCardOnboardingStateUpdateEventPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditCardOnboardingStateUpdateEventPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VendorInternalUserId

	// no validation rules for VendorWorkflowId

	if all {
		switch v := interface{}(m.GetWorkflowPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardOnboardingStateUpdateEventPayloadValidationError{
					field:  "WorkflowPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardOnboardingStateUpdateEventPayloadValidationError{
					field:  "WorkflowPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWorkflowPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardOnboardingStateUpdateEventPayloadValidationError{
				field:  "WorkflowPayload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreditCardOnboardingStateUpdateEventPayloadMultiError(errors)
	}

	return nil
}

// CreditCardOnboardingStateUpdateEventPayloadMultiError is an error wrapping
// multiple validation errors returned by
// CreditCardOnboardingStateUpdateEventPayload.ValidateAll() if the designated
// constraints aren't met.
type CreditCardOnboardingStateUpdateEventPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditCardOnboardingStateUpdateEventPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditCardOnboardingStateUpdateEventPayloadMultiError) AllErrors() []error { return m }

// CreditCardOnboardingStateUpdateEventPayloadValidationError is the validation
// error returned by CreditCardOnboardingStateUpdateEventPayload.Validate if
// the designated constraints aren't met.
type CreditCardOnboardingStateUpdateEventPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditCardOnboardingStateUpdateEventPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditCardOnboardingStateUpdateEventPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditCardOnboardingStateUpdateEventPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditCardOnboardingStateUpdateEventPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditCardOnboardingStateUpdateEventPayloadValidationError) ErrorName() string {
	return "CreditCardOnboardingStateUpdateEventPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e CreditCardOnboardingStateUpdateEventPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditCardOnboardingStateUpdateEventPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditCardOnboardingStateUpdateEventPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditCardOnboardingStateUpdateEventPayloadValidationError{}

// Validate checks the field values on CreditCardOnboardingWorkflowPayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreditCardOnboardingWorkflowPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreditCardOnboardingWorkflowPayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreditCardOnboardingWorkflowPayloadMultiError, or nil if none found.
func (m *CreditCardOnboardingWorkflowPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditCardOnboardingWorkflowPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CurrentState

	// no validation rules for NextState

	// no validation rules for OnboardingStatus

	// no validation rules for WorkflowMessage

	if len(errors) > 0 {
		return CreditCardOnboardingWorkflowPayloadMultiError(errors)
	}

	return nil
}

// CreditCardOnboardingWorkflowPayloadMultiError is an error wrapping multiple
// validation errors returned by
// CreditCardOnboardingWorkflowPayload.ValidateAll() if the designated
// constraints aren't met.
type CreditCardOnboardingWorkflowPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditCardOnboardingWorkflowPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditCardOnboardingWorkflowPayloadMultiError) AllErrors() []error { return m }

// CreditCardOnboardingWorkflowPayloadValidationError is the validation error
// returned by CreditCardOnboardingWorkflowPayload.Validate if the designated
// constraints aren't met.
type CreditCardOnboardingWorkflowPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditCardOnboardingWorkflowPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditCardOnboardingWorkflowPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditCardOnboardingWorkflowPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditCardOnboardingWorkflowPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditCardOnboardingWorkflowPayloadValidationError) ErrorName() string {
	return "CreditCardOnboardingWorkflowPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e CreditCardOnboardingWorkflowPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditCardOnboardingWorkflowPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditCardOnboardingWorkflowPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditCardOnboardingWorkflowPayloadValidationError{}

// Validate checks the field values on UpdateCreditCardOnboardingRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateCreditCardOnboardingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCreditCardOnboardingRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateCreditCardOnboardingRequestMultiError, or nil if none found.
func (m *UpdateCreditCardOnboardingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCreditCardOnboardingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EventType

	// no validation rules for UserLocalId

	// no validation rules for ApplicantType

	// no validation rules for ReasonToDiscardOnboarding

	if all {
		switch v := interface{}(m.GetPreApprovedInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCreditCardOnboardingRequestValidationError{
					field:  "PreApprovedInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCreditCardOnboardingRequestValidationError{
					field:  "PreApprovedInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreApprovedInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCreditCardOnboardingRequestValidationError{
				field:  "PreApprovedInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateCreditCardOnboardingRequestMultiError(errors)
	}

	return nil
}

// UpdateCreditCardOnboardingRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateCreditCardOnboardingRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateCreditCardOnboardingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCreditCardOnboardingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCreditCardOnboardingRequestMultiError) AllErrors() []error { return m }

// UpdateCreditCardOnboardingRequestValidationError is the validation error
// returned by UpdateCreditCardOnboardingRequest.Validate if the designated
// constraints aren't met.
type UpdateCreditCardOnboardingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCreditCardOnboardingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCreditCardOnboardingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCreditCardOnboardingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCreditCardOnboardingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCreditCardOnboardingRequestValidationError) ErrorName() string {
	return "UpdateCreditCardOnboardingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCreditCardOnboardingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCreditCardOnboardingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCreditCardOnboardingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCreditCardOnboardingRequestValidationError{}

// Validate checks the field values on UpdateCreditCardOnboardingResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateCreditCardOnboardingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCreditCardOnboardingResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateCreditCardOnboardingResponseMultiError, or nil if none found.
func (m *UpdateCreditCardOnboardingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCreditCardOnboardingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetDetailedStatusInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCreditCardOnboardingResponseValidationError{
					field:  "DetailedStatusInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCreditCardOnboardingResponseValidationError{
					field:  "DetailedStatusInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetailedStatusInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCreditCardOnboardingResponseValidationError{
				field:  "DetailedStatusInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateCreditCardOnboardingResponseMultiError(errors)
	}

	return nil
}

// UpdateCreditCardOnboardingResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateCreditCardOnboardingResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateCreditCardOnboardingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCreditCardOnboardingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCreditCardOnboardingResponseMultiError) AllErrors() []error { return m }

// UpdateCreditCardOnboardingResponseValidationError is the validation error
// returned by UpdateCreditCardOnboardingResponse.Validate if the designated
// constraints aren't met.
type UpdateCreditCardOnboardingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCreditCardOnboardingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCreditCardOnboardingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCreditCardOnboardingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCreditCardOnboardingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCreditCardOnboardingResponseValidationError) ErrorName() string {
	return "UpdateCreditCardOnboardingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCreditCardOnboardingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCreditCardOnboardingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCreditCardOnboardingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCreditCardOnboardingResponseValidationError{}

// Validate checks the field values on UpdateCustomerInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCustomerInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCustomerInfoRequestMultiError, or nil if none found.
func (m *UpdateCustomerInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserLocalId

	if all {
		switch v := interface{}(m.GetDeviceInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCustomerInfoRequestValidationError{
					field:  "DeviceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCustomerInfoRequestValidationError{
					field:  "DeviceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeviceInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCustomerInfoRequestValidationError{
				field:  "DeviceInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateCustomerInfoRequestMultiError(errors)
	}

	return nil
}

// UpdateCustomerInfoRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateCustomerInfoRequest.ValidateAll() if the
// designated constraints aren't met.
type UpdateCustomerInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerInfoRequestMultiError) AllErrors() []error { return m }

// UpdateCustomerInfoRequestValidationError is the validation error returned by
// UpdateCustomerInfoRequest.Validate if the designated constraints aren't met.
type UpdateCustomerInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerInfoRequestValidationError) ErrorName() string {
	return "UpdateCustomerInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerInfoRequestValidationError{}

// Validate checks the field values on UpdateCustomerInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCustomerInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCustomerInfoResponseMultiError, or nil if none found.
func (m *UpdateCustomerInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetDetailedStatusInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCustomerInfoResponseValidationError{
					field:  "DetailedStatusInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCustomerInfoResponseValidationError{
					field:  "DetailedStatusInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetailedStatusInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCustomerInfoResponseValidationError{
				field:  "DetailedStatusInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateCustomerInfoResponseMultiError(errors)
	}

	return nil
}

// UpdateCustomerInfoResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateCustomerInfoResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateCustomerInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerInfoResponseMultiError) AllErrors() []error { return m }

// UpdateCustomerInfoResponseValidationError is the validation error returned
// by UpdateCustomerInfoResponse.Validate if the designated constraints aren't met.
type UpdateCustomerInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerInfoResponseValidationError) ErrorName() string {
	return "UpdateCustomerInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerInfoResponseValidationError{}

// Validate checks the field values on DetailedStatusInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetailedStatusInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetailedStatusInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DetailedStatusInfoMultiError, or nil if none found.
func (m *DetailedStatusInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DetailedStatusInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Name

	// no validation rules for Message

	if len(errors) > 0 {
		return DetailedStatusInfoMultiError(errors)
	}

	return nil
}

// DetailedStatusInfoMultiError is an error wrapping multiple validation errors
// returned by DetailedStatusInfo.ValidateAll() if the designated constraints
// aren't met.
type DetailedStatusInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetailedStatusInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetailedStatusInfoMultiError) AllErrors() []error { return m }

// DetailedStatusInfoValidationError is the validation error returned by
// DetailedStatusInfo.Validate if the designated constraints aren't met.
type DetailedStatusInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetailedStatusInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetailedStatusInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetailedStatusInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetailedStatusInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetailedStatusInfoValidationError) ErrorName() string {
	return "DetailedStatusInfoValidationError"
}

// Error satisfies the builtin error interface
func (e DetailedStatusInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetailedStatusInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetailedStatusInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetailedStatusInfoValidationError{}

// Validate checks the field values on CommonTokenizedRequestPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CommonTokenizedRequestPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommonTokenizedRequestPayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CommonTokenizedRequestPayloadMultiError, or nil if none found.
func (m *CommonTokenizedRequestPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *CommonTokenizedRequestPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	if len(errors) > 0 {
		return CommonTokenizedRequestPayloadMultiError(errors)
	}

	return nil
}

// CommonTokenizedRequestPayloadMultiError is an error wrapping multiple
// validation errors returned by CommonTokenizedRequestPayload.ValidateAll()
// if the designated constraints aren't met.
type CommonTokenizedRequestPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommonTokenizedRequestPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommonTokenizedRequestPayloadMultiError) AllErrors() []error { return m }

// CommonTokenizedRequestPayloadValidationError is the validation error
// returned by CommonTokenizedRequestPayload.Validate if the designated
// constraints aren't met.
type CommonTokenizedRequestPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommonTokenizedRequestPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommonTokenizedRequestPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommonTokenizedRequestPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommonTokenizedRequestPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommonTokenizedRequestPayloadValidationError) ErrorName() string {
	return "CommonTokenizedRequestPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e CommonTokenizedRequestPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommonTokenizedRequestPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommonTokenizedRequestPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommonTokenizedRequestPayloadValidationError{}
