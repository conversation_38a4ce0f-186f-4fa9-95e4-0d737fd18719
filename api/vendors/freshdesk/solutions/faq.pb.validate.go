// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/freshdesk/solutions/faq.proto

package solutions

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Category with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Category) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Category with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CategoryMultiError, or nil
// if none found.
func (m *Category) ValidateAll() error {
	return m.validate(true)
}

func (m *Category) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return CategoryMultiError(errors)
	}

	return nil
}

// CategoryMultiError is an error wrapping multiple validation errors returned
// by Category.ValidateAll() if the designated constraints aren't met.
type CategoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CategoryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CategoryMultiError) AllErrors() []error { return m }

// CategoryValidationError is the validation error returned by
// Category.Validate if the designated constraints aren't met.
type CategoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CategoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CategoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CategoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CategoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CategoryValidationError) ErrorName() string { return "CategoryValidationError" }

// Error satisfies the builtin error interface
func (e CategoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCategory.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CategoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CategoryValidationError{}

// Validate checks the field values on Folder with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Folder) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Folder with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in FolderMultiError, or nil if none found.
func (m *Folder) ValidateAll() error {
	return m.validate(true)
}

func (m *Folder) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Visibility

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return FolderMultiError(errors)
	}

	return nil
}

// FolderMultiError is an error wrapping multiple validation errors returned by
// Folder.ValidateAll() if the designated constraints aren't met.
type FolderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FolderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FolderMultiError) AllErrors() []error { return m }

// FolderValidationError is the validation error returned by Folder.Validate if
// the designated constraints aren't met.
type FolderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FolderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FolderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FolderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FolderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FolderValidationError) ErrorName() string { return "FolderValidationError" }

// Error satisfies the builtin error interface
func (e FolderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFolder.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FolderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FolderValidationError{}

// Validate checks the field values on Article with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Article) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Article with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ArticleMultiError, or nil if none found.
func (m *Article) ValidateAll() error {
	return m.validate(true)
}

func (m *Article) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Title

	// no validation rules for Description

	// no validation rules for DescriptionText

	// no validation rules for CategoryId

	// no validation rules for FolderId

	// no validation rules for AgentId

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return ArticleMultiError(errors)
	}

	return nil
}

// ArticleMultiError is an error wrapping multiple validation errors returned
// by Article.ValidateAll() if the designated constraints aren't met.
type ArticleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ArticleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ArticleMultiError) AllErrors() []error { return m }

// ArticleValidationError is the validation error returned by Article.Validate
// if the designated constraints aren't met.
type ArticleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ArticleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ArticleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ArticleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ArticleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ArticleValidationError) ErrorName() string { return "ArticleValidationError" }

// Error satisfies the builtin error interface
func (e ArticleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sArticle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ArticleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ArticleValidationError{}
