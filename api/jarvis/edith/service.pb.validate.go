// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/jarvis/edith/service.proto

package edith

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	types "github.com/epifi/gamma/api/jarvis/types"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = types.FormStatus(0)
)

// Validate checks the field values on UpdateFormsCacheRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFormsCacheRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFormsCacheRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateFormsCacheRequestMultiError, or nil if none found.
func (m *UpdateFormsCacheRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFormsCacheRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUpdatedAtStart()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFormsCacheRequestValidationError{
					field:  "UpdatedAtStart",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFormsCacheRequestValidationError{
					field:  "UpdatedAtStart",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAtStart()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFormsCacheRequestValidationError{
				field:  "UpdatedAtStart",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAtEnd()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFormsCacheRequestValidationError{
					field:  "UpdatedAtEnd",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFormsCacheRequestValidationError{
					field:  "UpdatedAtEnd",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAtEnd()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFormsCacheRequestValidationError{
				field:  "UpdatedAtEnd",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateFormsCacheRequestMultiError(errors)
	}

	return nil
}

// UpdateFormsCacheRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateFormsCacheRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateFormsCacheRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFormsCacheRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFormsCacheRequestMultiError) AllErrors() []error { return m }

// UpdateFormsCacheRequestValidationError is the validation error returned by
// UpdateFormsCacheRequest.Validate if the designated constraints aren't met.
type UpdateFormsCacheRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFormsCacheRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFormsCacheRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFormsCacheRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFormsCacheRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFormsCacheRequestValidationError) ErrorName() string {
	return "UpdateFormsCacheRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFormsCacheRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFormsCacheRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFormsCacheRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFormsCacheRequestValidationError{}

// Validate checks the field values on UpdateFormsCacheResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFormsCacheResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFormsCacheResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateFormsCacheResponseMultiError, or nil if none found.
func (m *UpdateFormsCacheResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFormsCacheResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFormsCacheResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFormsCacheResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFormsCacheResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateFormsCacheResponseMultiError(errors)
	}

	return nil
}

// UpdateFormsCacheResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateFormsCacheResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateFormsCacheResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFormsCacheResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFormsCacheResponseMultiError) AllErrors() []error { return m }

// UpdateFormsCacheResponseValidationError is the validation error returned by
// UpdateFormsCacheResponse.Validate if the designated constraints aren't met.
type UpdateFormsCacheResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFormsCacheResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFormsCacheResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFormsCacheResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFormsCacheResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFormsCacheResponseValidationError) ErrorName() string {
	return "UpdateFormsCacheResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFormsCacheResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFormsCacheResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFormsCacheResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFormsCacheResponseValidationError{}

// Validate checks the field values on CreateTicketRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTicketRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTicketRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTicketRequestMultiError, or nil if none found.
func (m *CreateTicketRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTicketRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFormVers()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTicketRequestValidationError{
					field:  "FormVers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTicketRequestValidationError{
					field:  "FormVers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFormVers()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTicketRequestValidationError{
				field:  "FormVers",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateTicketRequestMultiError(errors)
	}

	return nil
}

// CreateTicketRequestMultiError is an error wrapping multiple validation
// errors returned by CreateTicketRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateTicketRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTicketRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTicketRequestMultiError) AllErrors() []error { return m }

// CreateTicketRequestValidationError is the validation error returned by
// CreateTicketRequest.Validate if the designated constraints aren't met.
type CreateTicketRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTicketRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTicketRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTicketRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTicketRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTicketRequestValidationError) ErrorName() string {
	return "CreateTicketRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTicketRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTicketRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTicketRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTicketRequestValidationError{}

// Validate checks the field values on CreateTicketResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTicketResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTicketResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTicketResponseMultiError, or nil if none found.
func (m *CreateTicketResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTicketResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTicketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTicketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTicketResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFormVers()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTicketResponseValidationError{
					field:  "FormVers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTicketResponseValidationError{
					field:  "FormVers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFormVers()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTicketResponseValidationError{
				field:  "FormVers",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateTicketResponseMultiError(errors)
	}

	return nil
}

// CreateTicketResponseMultiError is an error wrapping multiple validation
// errors returned by CreateTicketResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateTicketResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTicketResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTicketResponseMultiError) AllErrors() []error { return m }

// CreateTicketResponseValidationError is the validation error returned by
// CreateTicketResponse.Validate if the designated constraints aren't met.
type CreateTicketResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTicketResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTicketResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTicketResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTicketResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTicketResponseValidationError) ErrorName() string {
	return "CreateTicketResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTicketResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTicketResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTicketResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTicketResponseValidationError{}

// Validate checks the field values on UpdateFormStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFormStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFormStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateFormStatusRequestMultiError, or nil if none found.
func (m *UpdateFormStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFormStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FormId

	// no validation rules for FormStatus

	// no validation rules for LastUpdatedBy

	if len(errors) > 0 {
		return UpdateFormStatusRequestMultiError(errors)
	}

	return nil
}

// UpdateFormStatusRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateFormStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateFormStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFormStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFormStatusRequestMultiError) AllErrors() []error { return m }

// UpdateFormStatusRequestValidationError is the validation error returned by
// UpdateFormStatusRequest.Validate if the designated constraints aren't met.
type UpdateFormStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFormStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFormStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFormStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFormStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFormStatusRequestValidationError) ErrorName() string {
	return "UpdateFormStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFormStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFormStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFormStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFormStatusRequestValidationError{}

// Validate checks the field values on UpdateFormStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFormStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFormStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateFormStatusResponseMultiError, or nil if none found.
func (m *UpdateFormStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFormStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFormStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFormStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFormStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetForm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFormStatusResponseValidationError{
					field:  "Form",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFormStatusResponseValidationError{
					field:  "Form",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetForm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFormStatusResponseValidationError{
				field:  "Form",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateFormStatusResponseMultiError(errors)
	}

	return nil
}

// UpdateFormStatusResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateFormStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateFormStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFormStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFormStatusResponseMultiError) AllErrors() []error { return m }

// UpdateFormStatusResponseValidationError is the validation error returned by
// UpdateFormStatusResponse.Validate if the designated constraints aren't met.
type UpdateFormStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFormStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFormStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFormStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFormStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFormStatusResponseValidationError) ErrorName() string {
	return "UpdateFormStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFormStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFormStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFormStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFormStatusResponseValidationError{}

// Validate checks the field values on UpdateTicketRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTicketRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTicketRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTicketRequestMultiError, or nil if none found.
func (m *UpdateTicketRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTicketRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFormVers()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTicketRequestValidationError{
					field:  "FormVers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTicketRequestValidationError{
					field:  "FormVers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFormVers()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTicketRequestValidationError{
				field:  "FormVers",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetJarvisHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTicketRequestValidationError{
					field:  "JarvisHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTicketRequestValidationError{
					field:  "JarvisHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJarvisHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTicketRequestValidationError{
				field:  "JarvisHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateFieldMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTicketRequestValidationError{
					field:  "UpdateFieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTicketRequestValidationError{
					field:  "UpdateFieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateFieldMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTicketRequestValidationError{
				field:  "UpdateFieldMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateTicketRequestMultiError(errors)
	}

	return nil
}

// UpdateTicketRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateTicketRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateTicketRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTicketRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTicketRequestMultiError) AllErrors() []error { return m }

// UpdateTicketRequestValidationError is the validation error returned by
// UpdateTicketRequest.Validate if the designated constraints aren't met.
type UpdateTicketRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTicketRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTicketRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTicketRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTicketRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTicketRequestValidationError) ErrorName() string {
	return "UpdateTicketRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTicketRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTicketRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTicketRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTicketRequestValidationError{}

// Validate checks the field values on UpdateTicketResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTicketResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTicketResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTicketResponseMultiError, or nil if none found.
func (m *UpdateTicketResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTicketResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTicketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTicketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTicketResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFormVers()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTicketResponseValidationError{
					field:  "FormVers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTicketResponseValidationError{
					field:  "FormVers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFormVers()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTicketResponseValidationError{
				field:  "FormVers",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateTicketResponseMultiError(errors)
	}

	return nil
}

// UpdateTicketResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateTicketResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateTicketResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTicketResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTicketResponseMultiError) AllErrors() []error { return m }

// UpdateTicketResponseValidationError is the validation error returned by
// UpdateTicketResponse.Validate if the designated constraints aren't met.
type UpdateTicketResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTicketResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTicketResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTicketResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTicketResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTicketResponseValidationError) ErrorName() string {
	return "UpdateTicketResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTicketResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTicketResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTicketResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTicketResponseValidationError{}

// Validate checks the field values on GetTicketsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetTicketsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTicketsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTicketsRequestMultiError, or nil if none found.
func (m *GetTicketsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTicketsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketsRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFieldMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketsRequestValidationError{
					field:  "FieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketsRequestValidationError{
					field:  "FieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFieldMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketsRequestValidationError{
				field:  "FieldMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAtStart()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketsRequestValidationError{
					field:  "CreatedAtStart",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketsRequestValidationError{
					field:  "CreatedAtStart",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAtStart()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketsRequestValidationError{
				field:  "CreatedAtStart",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAtEnd()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketsRequestValidationError{
					field:  "CreatedAtEnd",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketsRequestValidationError{
					field:  "CreatedAtEnd",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAtEnd()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketsRequestValidationError{
				field:  "CreatedAtEnd",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTicketsRequestMultiError(errors)
	}

	return nil
}

// GetTicketsRequestMultiError is an error wrapping multiple validation errors
// returned by GetTicketsRequest.ValidateAll() if the designated constraints
// aren't met.
type GetTicketsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTicketsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTicketsRequestMultiError) AllErrors() []error { return m }

// GetTicketsRequestValidationError is the validation error returned by
// GetTicketsRequest.Validate if the designated constraints aren't met.
type GetTicketsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTicketsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTicketsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTicketsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTicketsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTicketsRequestValidationError) ErrorName() string {
	return "GetTicketsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTicketsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTicketsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTicketsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTicketsRequestValidationError{}

// Validate checks the field values on GetTicketsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTicketsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTicketsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTicketsResponseMultiError, or nil if none found.
func (m *GetTicketsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTicketsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketsResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketsResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketsResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetVersions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTicketsResponseValidationError{
						field:  fmt.Sprintf("Versions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTicketsResponseValidationError{
						field:  fmt.Sprintf("Versions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTicketsResponseValidationError{
					field:  fmt.Sprintf("Versions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTicketsResponseMultiError(errors)
	}

	return nil
}

// GetTicketsResponseMultiError is an error wrapping multiple validation errors
// returned by GetTicketsResponse.ValidateAll() if the designated constraints
// aren't met.
type GetTicketsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTicketsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTicketsResponseMultiError) AllErrors() []error { return m }

// GetTicketsResponseValidationError is the validation error returned by
// GetTicketsResponse.Validate if the designated constraints aren't met.
type GetTicketsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTicketsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTicketsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTicketsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTicketsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTicketsResponseValidationError) ErrorName() string {
	return "GetTicketsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTicketsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTicketsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTicketsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTicketsResponseValidationError{}

// Validate checks the field values on GetFormsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetFormsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFormsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFormsRequestMultiError, or nil if none found.
func (m *GetFormsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFormsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFormsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFormsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFormsRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFieldMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFormsRequestValidationError{
					field:  "FieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFormsRequestValidationError{
					field:  "FieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFieldMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFormsRequestValidationError{
				field:  "FieldMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAtStart()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFormsRequestValidationError{
					field:  "CreatedAtStart",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFormsRequestValidationError{
					field:  "CreatedAtStart",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAtStart()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFormsRequestValidationError{
				field:  "CreatedAtStart",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAtEnd()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFormsRequestValidationError{
					field:  "CreatedAtEnd",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFormsRequestValidationError{
					field:  "CreatedAtEnd",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAtEnd()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFormsRequestValidationError{
				field:  "CreatedAtEnd",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Name

	if len(errors) > 0 {
		return GetFormsRequestMultiError(errors)
	}

	return nil
}

// GetFormsRequestMultiError is an error wrapping multiple validation errors
// returned by GetFormsRequest.ValidateAll() if the designated constraints
// aren't met.
type GetFormsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFormsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFormsRequestMultiError) AllErrors() []error { return m }

// GetFormsRequestValidationError is the validation error returned by
// GetFormsRequest.Validate if the designated constraints aren't met.
type GetFormsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFormsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFormsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFormsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFormsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFormsRequestValidationError) ErrorName() string { return "GetFormsRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetFormsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFormsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFormsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFormsRequestValidationError{}

// Validate checks the field values on GetFormsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetFormsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFormsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFormsResponseMultiError, or nil if none found.
func (m *GetFormsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFormsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFormsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFormsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFormsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFormsResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFormsResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFormsResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetForms() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFormsResponseValidationError{
						field:  fmt.Sprintf("Forms[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFormsResponseValidationError{
						field:  fmt.Sprintf("Forms[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFormsResponseValidationError{
					field:  fmt.Sprintf("Forms[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFormsResponseMultiError(errors)
	}

	return nil
}

// GetFormsResponseMultiError is an error wrapping multiple validation errors
// returned by GetFormsResponse.ValidateAll() if the designated constraints
// aren't met.
type GetFormsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFormsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFormsResponseMultiError) AllErrors() []error { return m }

// GetFormsResponseValidationError is the validation error returned by
// GetFormsResponse.Validate if the designated constraints aren't met.
type GetFormsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFormsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFormsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFormsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFormsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFormsResponseValidationError) ErrorName() string { return "GetFormsResponseValidationError" }

// Error satisfies the builtin error interface
func (e GetFormsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFormsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFormsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFormsResponseValidationError{}

// Validate checks the field values on GetApproversFromApproverRoleRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetApproversFromApproverRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetApproversFromApproverRoleRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetApproversFromApproverRoleRequestMultiError, or nil if none found.
func (m *GetApproversFromApproverRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetApproversFromApproverRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetApproversFromApproverRoleRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetApproversFromApproverRoleRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetApproversFromApproverRoleRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFieldMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetApproversFromApproverRoleRequestValidationError{
					field:  "FieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetApproversFromApproverRoleRequestValidationError{
					field:  "FieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFieldMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetApproversFromApproverRoleRequestValidationError{
				field:  "FieldMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApproverRole

	// no validation rules for Approver

	if len(errors) > 0 {
		return GetApproversFromApproverRoleRequestMultiError(errors)
	}

	return nil
}

// GetApproversFromApproverRoleRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetApproversFromApproverRoleRequest.ValidateAll() if the designated
// constraints aren't met.
type GetApproversFromApproverRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetApproversFromApproverRoleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetApproversFromApproverRoleRequestMultiError) AllErrors() []error { return m }

// GetApproversFromApproverRoleRequestValidationError is the validation error
// returned by GetApproversFromApproverRoleRequest.Validate if the designated
// constraints aren't met.
type GetApproversFromApproverRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetApproversFromApproverRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetApproversFromApproverRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetApproversFromApproverRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetApproversFromApproverRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetApproversFromApproverRoleRequestValidationError) ErrorName() string {
	return "GetApproversFromApproverRoleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetApproversFromApproverRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetApproversFromApproverRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetApproversFromApproverRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetApproversFromApproverRoleRequestValidationError{}

// Validate checks the field values on GetApproversFromApproverRoleResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetApproversFromApproverRoleResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetApproversFromApproverRoleResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetApproversFromApproverRoleResponseMultiError, or nil if none found.
func (m *GetApproversFromApproverRoleResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetApproversFromApproverRoleResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetApproversFromApproverRoleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetApproversFromApproverRoleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetApproversFromApproverRoleResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetApproversFromApproverRoleResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetApproversFromApproverRoleResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetApproversFromApproverRoleResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetApproverList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetApproversFromApproverRoleResponseValidationError{
						field:  fmt.Sprintf("ApproverList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetApproversFromApproverRoleResponseValidationError{
						field:  fmt.Sprintf("ApproverList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetApproversFromApproverRoleResponseValidationError{
					field:  fmt.Sprintf("ApproverList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetApproversFromApproverRoleResponseMultiError(errors)
	}

	return nil
}

// GetApproversFromApproverRoleResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetApproversFromApproverRoleResponse.ValidateAll() if the designated
// constraints aren't met.
type GetApproversFromApproverRoleResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetApproversFromApproverRoleResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetApproversFromApproverRoleResponseMultiError) AllErrors() []error { return m }

// GetApproversFromApproverRoleResponseValidationError is the validation error
// returned by GetApproversFromApproverRoleResponse.Validate if the designated
// constraints aren't met.
type GetApproversFromApproverRoleResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetApproversFromApproverRoleResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetApproversFromApproverRoleResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetApproversFromApproverRoleResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetApproversFromApproverRoleResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetApproversFromApproverRoleResponseValidationError) ErrorName() string {
	return "GetApproversFromApproverRoleResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetApproversFromApproverRoleResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetApproversFromApproverRoleResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetApproversFromApproverRoleResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetApproversFromApproverRoleResponseValidationError{}

// Validate checks the field values on GetFormFromEntityIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFormFromEntityIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFormFromEntityIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFormFromEntityIdRequestMultiError, or nil if none found.
func (m *GetFormFromEntityIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFormFromEntityIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetEntityId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFormFromEntityIdRequestValidationError{
					field:  "EntityId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFormFromEntityIdRequestValidationError{
					field:  "EntityId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEntityId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFormFromEntityIdRequestValidationError{
				field:  "EntityId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFieldMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFormFromEntityIdRequestValidationError{
					field:  "FieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFormFromEntityIdRequestValidationError{
					field:  "FieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFieldMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFormFromEntityIdRequestValidationError{
				field:  "FieldMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFormFromEntityIdRequestMultiError(errors)
	}

	return nil
}

// GetFormFromEntityIdRequestMultiError is an error wrapping multiple
// validation errors returned by GetFormFromEntityIdRequest.ValidateAll() if
// the designated constraints aren't met.
type GetFormFromEntityIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFormFromEntityIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFormFromEntityIdRequestMultiError) AllErrors() []error { return m }

// GetFormFromEntityIdRequestValidationError is the validation error returned
// by GetFormFromEntityIdRequest.Validate if the designated constraints aren't met.
type GetFormFromEntityIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFormFromEntityIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFormFromEntityIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFormFromEntityIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFormFromEntityIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFormFromEntityIdRequestValidationError) ErrorName() string {
	return "GetFormFromEntityIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFormFromEntityIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFormFromEntityIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFormFromEntityIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFormFromEntityIdRequestValidationError{}

// Validate checks the field values on GetFormFromEntityIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFormFromEntityIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFormFromEntityIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFormFromEntityIdResponseMultiError, or nil if none found.
func (m *GetFormFromEntityIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFormFromEntityIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFormFromEntityIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFormFromEntityIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFormFromEntityIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetForm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFormFromEntityIdResponseValidationError{
					field:  "Form",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFormFromEntityIdResponseValidationError{
					field:  "Form",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetForm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFormFromEntityIdResponseValidationError{
				field:  "Form",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFormFromEntityIdResponseMultiError(errors)
	}

	return nil
}

// GetFormFromEntityIdResponseMultiError is an error wrapping multiple
// validation errors returned by GetFormFromEntityIdResponse.ValidateAll() if
// the designated constraints aren't met.
type GetFormFromEntityIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFormFromEntityIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFormFromEntityIdResponseMultiError) AllErrors() []error { return m }

// GetFormFromEntityIdResponseValidationError is the validation error returned
// by GetFormFromEntityIdResponse.Validate if the designated constraints
// aren't met.
type GetFormFromEntityIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFormFromEntityIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFormFromEntityIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFormFromEntityIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFormFromEntityIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFormFromEntityIdResponseValidationError) ErrorName() string {
	return "GetFormFromEntityIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFormFromEntityIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFormFromEntityIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFormFromEntityIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFormFromEntityIdResponseValidationError{}

// Validate checks the field values on FormEntityId with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FormEntityId) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FormEntityId with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FormEntityIdMultiError, or
// nil if none found.
func (m *FormEntityId) ValidateAll() error {
	return m.validate(true)
}

func (m *FormEntityId) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.FormEntityId.(type) {
	case *FormEntityId_FormId:
		if v == nil {
			err := FormEntityIdValidationError{
				field:  "FormEntityId",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for FormId
	case *FormEntityId_FormName:
		if v == nil {
			err := FormEntityIdValidationError{
				field:  "FormEntityId",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for FormName
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return FormEntityIdMultiError(errors)
	}

	return nil
}

// FormEntityIdMultiError is an error wrapping multiple validation errors
// returned by FormEntityId.ValidateAll() if the designated constraints aren't met.
type FormEntityIdMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FormEntityIdMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FormEntityIdMultiError) AllErrors() []error { return m }

// FormEntityIdValidationError is the validation error returned by
// FormEntityId.Validate if the designated constraints aren't met.
type FormEntityIdValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FormEntityIdValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FormEntityIdValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FormEntityIdValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FormEntityIdValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FormEntityIdValidationError) ErrorName() string { return "FormEntityIdValidationError" }

// Error satisfies the builtin error interface
func (e FormEntityIdValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFormEntityId.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FormEntityIdValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FormEntityIdValidationError{}
