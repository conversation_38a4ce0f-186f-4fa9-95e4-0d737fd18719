syntax = "proto3";

package api.jarvis.frontend;

import "api/pkg/web/components.proto";

option go_package = "github.com/epifi/gamma/api/jarvis/frontend";
option java_package = "com.github.epifi.gamma.api.jarvis/frontend";

// Form is used in
// constructing new form page,
// returning response for created forms with the values given by users in value field of input components
// constructing edit form page with current values of form as default values for input components
// returning response for edit form with values edited by users in value field of input components
message Ticket {
  // title for create / edit forms page
  pkg.web.Text title = 1;
  // link for user manuel
  pkg.web.Link user_manual_link = 2;
  // provides label and default / current values for form_name
  // label and dropdown values with default / current value for area
  // label & default / current value for duration and checkbox to run form till significant result is observed as OrInputElements
  // label & default / current value for description and tags
  pkg.web.Form ticket_details_form = 3;
  pkg.web.Form approver_email = 8;
  // submit button
  pkg.web.ActionButton submit_btn = 9;
  // entity id to carry information to next flow
  EntityId entity_id = 10;
}

// FormView view page of form details
message TicketView {
  // Entity id of the selected form
  EntityId entity_id = 1;
  // label and value pair
  // 1. name of form
  // 2. status of form
  // 3. description of form
  // 4. area of form
  // 5. duration (as string) for which form is supposed to run.
  //    this can be empty for form that is currently running
  // 6. start time of form (as string)
  // 7. end time of form (as string)
  // 8. segment information
  // 9. user layer segment percent start
  // 10. user layer segment percent end
  // label and multi values
  // 11. list of user groups
  // 12. tags given to form
  // multi label and value pairs
  // 13. App Version Condition for android & IOS
  pkg.web.FormView ticket_details_form_view = 2;
  // all versions of the form
  pkg.web.Table versions = 6;
  // json difference viewer will show the difference between the old data version json
  // and the new data version json
  pkg.web.JsonDiffView json_difference_viewer = 7;
  // button array which contains approve, run, unpause, stop
  pkg.web.ActionButtons buttons = 8;
}


message EntityId {
  oneof entity_id {
    // id of the form version
    string form_vers_id = 1;
    // id of the form
    string form_id = 2;
    // name of the form
    string form_name = 3;
  }
}
