//go:generate gen_sql -types=CollectedDataType
syntax = "proto3";

package rewards;

option go_package = "github.com/epifi/gamma/api/rewards";
option java_package = "com.github.epifi.gamma.api.rewards";

// Types of Collected Event. This has been kept outside pipeline_events.proto as it's being used in reward.proto as well
// and keeping it inside pipeline_events.proto was leading to cyclic dependency.
enum CollectedDataType {
  UNSPECIFIED_COLLECTED_DATA_TYPE = 0;
  ORDER = 1;
  USER_SEARCH = 2;
  MANUAL_GIVEAWAY = 3;
  FITTT = 4;
  EXTRA_INTEREST_SD_BONUS_PAYOUT = 5;
  MIN_BALANCE = 6;
  CA_ACCOUNT_UPDATE = 7;
  KYC = 8;
  FITTT_SPORTS_LEADERBOARD = 9;
  SAVINGS_ACCOUNT_UPDATE = 10;
  SALARY_DETECTION = 11;
  ONBOARDING_STAGE_UPDATE = 12;
  CREDIT_CARD_TRANSACTION = 13;
  CREDIT_CARD_BILLING = 14;
  SALARY_PROGRAM_STATUS_UPDATE = 15;
  CREDIT_CARD_REQUEST_STAGE_UPDATE = 16;
  CREDIT_REPORT_DOWNLOAD = 17;
  OFFER_REDEMPTION_STATUS_UPDATE = 18;

  // special event used to unlock rewards only
  // is triggered from within rewards system
  UNLOCK_REWARD_EVENT = 19;

  // investment retention reward event data type
  INVESTMENT_RETENTION_REWARD_EVENT = 20;

  // investment withdrawal event data type, used to unlock a reward
  INVESTMENT_WITHDRAWAL_EVENT = 21;

  // for giving monthly rewards to plus, infinite, salary tier users
  TIERING_PERIODIC_REWARD_EVENT = 22;

  // tiering tier update event
  TIERING_TIER_UPDATE_EVENT = 23;

  // this event is used to unlock the usstocks reward
  USSTOCKS_REWARD_UNLOCK_EVENT = 24;

  // for giving DC tap n pay rewards
  DEBIT_CARD_SWITCH_NOTIFICATION_EVENT = 25;
  // event for us stock order (buy/sell)
  USSTOCKS_ORDER = 26;
  // event for epf passbook import
  EPF_PASSBOOK_IMPORT = 27;
  // data sync event for connected account
  CONNECTED_ACCOUNT_DATA_SYNC = 28;
  // event for mutual fund external orders update
  MUTUAL_FUND_EXTERNAL_ORDER_UPDATE = 29;
  // used for rewarding users based on nudge completion
  ACTOR_NUDGE_STATUS_UPDATE_EVENT = 30;
  // used to process reward fulfillment event trigger from external vendors on our vnotificationgw, for ex - Saven for credit card rewards
  VENDOR_REWARD_FULFILLMENT_EVENT = 31;
  NET_WORTH_ASSET_CONNECTED_EVENT = 32;
}
