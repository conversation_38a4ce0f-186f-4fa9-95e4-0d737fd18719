// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/rewards/service.proto

package rewards

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	feedback_engine "github.com/epifi/gamma/api/inapphelp/feedback_engine"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// visibility filer in the request
type VisibilityType int32

const (
	VisibilityType_VISIBILITY_TYPE_UNSPECIFIED VisibilityType = 0
	// visible on the UI
	VisibilityType_VISIBLE VisibilityType = 1
	// hidden on the UI
	VisibilityType_HIDDEN VisibilityType = 2
)

// Enum value maps for VisibilityType.
var (
	VisibilityType_name = map[int32]string{
		0: "VISIBILITY_TYPE_UNSPECIFIED",
		1: "VISIBLE",
		2: "HIDDEN",
	}
	VisibilityType_value = map[string]int32{
		"VISIBILITY_TYPE_UNSPECIFIED": 0,
		"VISIBLE":                     1,
		"HIDDEN":                      2,
	}
)

func (x VisibilityType) Enum() *VisibilityType {
	p := new(VisibilityType)
	*p = x
	return p
}

func (x VisibilityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VisibilityType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_service_proto_enumTypes[0].Descriptor()
}

func (VisibilityType) Type() protoreflect.EnumType {
	return &file_api_rewards_service_proto_enumTypes[0]
}

func (x VisibilityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VisibilityType.Descriptor instead.
func (VisibilityType) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{0}
}

type GetAllRewardsAndProjectionResponse_RewardEntityType int32

const (
	GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_UNSPECIFIED GetAllRewardsAndProjectionResponse_RewardEntityType = 0
	// represents an actual reward that's generated and stored as a reward
	GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD GetAllRewardsAndProjectionResponse_RewardEntityType = 1
	// represents the `projected options` of a projection (not the actual contribution of the projection towards the reward)
	GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_PROJECTED_REWARD GetAllRewardsAndProjectionResponse_RewardEntityType = 2
	// represents the `reward contributions` of a projected reward, i.e., the part of the projected value that was actualised as a part of some reward
	GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_ACTUALISED_PROJECTED_REWARD GetAllRewardsAndProjectionResponse_RewardEntityType = 3
)

// Enum value maps for GetAllRewardsAndProjectionResponse_RewardEntityType.
var (
	GetAllRewardsAndProjectionResponse_RewardEntityType_name = map[int32]string{
		0: "REWARD_ENTITY_TYPE_UNSPECIFIED",
		1: "REWARD_ENTITY_TYPE_GENERATED_REWARD",
		2: "REWARD_ENTITY_TYPE_PROJECTED_REWARD",
		3: "REWARD_ENTITY_TYPE_ACTUALISED_PROJECTED_REWARD",
	}
	GetAllRewardsAndProjectionResponse_RewardEntityType_value = map[string]int32{
		"REWARD_ENTITY_TYPE_UNSPECIFIED":                 0,
		"REWARD_ENTITY_TYPE_GENERATED_REWARD":            1,
		"REWARD_ENTITY_TYPE_PROJECTED_REWARD":            2,
		"REWARD_ENTITY_TYPE_ACTUALISED_PROJECTED_REWARD": 3,
	}
)

func (x GetAllRewardsAndProjectionResponse_RewardEntityType) Enum() *GetAllRewardsAndProjectionResponse_RewardEntityType {
	p := new(GetAllRewardsAndProjectionResponse_RewardEntityType)
	*p = x
	return p
}

func (x GetAllRewardsAndProjectionResponse_RewardEntityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAllRewardsAndProjectionResponse_RewardEntityType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_service_proto_enumTypes[1].Descriptor()
}

func (GetAllRewardsAndProjectionResponse_RewardEntityType) Type() protoreflect.EnumType {
	return &file_api_rewards_service_proto_enumTypes[1]
}

func (x GetAllRewardsAndProjectionResponse_RewardEntityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAllRewardsAndProjectionResponse_RewardEntityType.Descriptor instead.
func (GetAllRewardsAndProjectionResponse_RewardEntityType) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{40, 0}
}

type GetEstimatedRewardsInTimeDurationForTierRequest_UserTier int32

const (
	GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_UNSPECIFIED         GetEstimatedRewardsInTimeDurationForTierRequest_UserTier = 0
	GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_BASIC            GetEstimatedRewardsInTimeDurationForTierRequest_UserTier = 1
	GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_PLUS             GetEstimatedRewardsInTimeDurationForTierRequest_UserTier = 2
	GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_INFINITE         GetEstimatedRewardsInTimeDurationForTierRequest_UserTier = 3
	GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_SALARY           GetEstimatedRewardsInTimeDurationForTierRequest_UserTier = 4
	GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_SALARY_LITE      GetEstimatedRewardsInTimeDurationForTierRequest_UserTier = 5
	GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_AA_SALARY        GetEstimatedRewardsInTimeDurationForTierRequest_UserTier = 6
	GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_AA_SALARY_BAND_1 GetEstimatedRewardsInTimeDurationForTierRequest_UserTier = 7
	GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_AA_SALARY_BAND_2 GetEstimatedRewardsInTimeDurationForTierRequest_UserTier = 8
	GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_AA_SALARY_BAND_3 GetEstimatedRewardsInTimeDurationForTierRequest_UserTier = 9
	GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_REGULAR          GetEstimatedRewardsInTimeDurationForTierRequest_UserTier = 10
	GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_FI_SALARY_BASIC     GetEstimatedRewardsInTimeDurationForTierRequest_UserTier = 11
)

// Enum value maps for GetEstimatedRewardsInTimeDurationForTierRequest_UserTier.
var (
	GetEstimatedRewardsInTimeDurationForTierRequest_UserTier_name = map[int32]string{
		0:  "USER_TIER_UNSPECIFIED",
		1:  "USER_TIER_FI_BASIC",
		2:  "USER_TIER_FI_PLUS",
		3:  "USER_TIER_FI_INFINITE",
		4:  "USER_TIER_FI_SALARY",
		5:  "USER_TIER_FI_SALARY_LITE",
		6:  "USER_TIER_FI_AA_SALARY",
		7:  "USER_TIER_FI_AA_SALARY_BAND_1",
		8:  "USER_TIER_FI_AA_SALARY_BAND_2",
		9:  "USER_TIER_FI_AA_SALARY_BAND_3",
		10: "USER_TIER_FI_REGULAR",
		11: "USER_TIER_FI_SALARY_BASIC",
	}
	GetEstimatedRewardsInTimeDurationForTierRequest_UserTier_value = map[string]int32{
		"USER_TIER_UNSPECIFIED":         0,
		"USER_TIER_FI_BASIC":            1,
		"USER_TIER_FI_PLUS":             2,
		"USER_TIER_FI_INFINITE":         3,
		"USER_TIER_FI_SALARY":           4,
		"USER_TIER_FI_SALARY_LITE":      5,
		"USER_TIER_FI_AA_SALARY":        6,
		"USER_TIER_FI_AA_SALARY_BAND_1": 7,
		"USER_TIER_FI_AA_SALARY_BAND_2": 8,
		"USER_TIER_FI_AA_SALARY_BAND_3": 9,
		"USER_TIER_FI_REGULAR":          10,
		"USER_TIER_FI_SALARY_BASIC":     11,
	}
)

func (x GetEstimatedRewardsInTimeDurationForTierRequest_UserTier) Enum() *GetEstimatedRewardsInTimeDurationForTierRequest_UserTier {
	p := new(GetEstimatedRewardsInTimeDurationForTierRequest_UserTier)
	*p = x
	return p
}

func (x GetEstimatedRewardsInTimeDurationForTierRequest_UserTier) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetEstimatedRewardsInTimeDurationForTierRequest_UserTier) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_service_proto_enumTypes[2].Descriptor()
}

func (GetEstimatedRewardsInTimeDurationForTierRequest_UserTier) Type() protoreflect.EnumType {
	return &file_api_rewards_service_proto_enumTypes[2]
}

func (x GetEstimatedRewardsInTimeDurationForTierRequest_UserTier) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetEstimatedRewardsInTimeDurationForTierRequest_UserTier.Descriptor instead.
func (GetEstimatedRewardsInTimeDurationForTierRequest_UserTier) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{43, 0}
}

type RewardsByActorIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// deprecated in favour of filters_v2
	//
	// Deprecated: Marked as deprecated in api/rewards/service.proto.
	Filter      *RewardsByActorIdRequest_Filter    `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	PageContext *rpc.PageContextRequest            `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	FiltersV2   *RewardsByActorIdRequest_FiltersV2 `protobuf:"bytes,4,opt,name=filters_v2,json=filtersV2,proto3" json:"filters_v2,omitempty"`
}

func (x *RewardsByActorIdRequest) Reset() {
	*x = RewardsByActorIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardsByActorIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardsByActorIdRequest) ProtoMessage() {}

func (x *RewardsByActorIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardsByActorIdRequest.ProtoReflect.Descriptor instead.
func (*RewardsByActorIdRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{0}
}

func (x *RewardsByActorIdRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/rewards/service.proto.
func (x *RewardsByActorIdRequest) GetFilter() *RewardsByActorIdRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *RewardsByActorIdRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *RewardsByActorIdRequest) GetFiltersV2() *RewardsByActorIdRequest_FiltersV2 {
	if x != nil {
		return x.FiltersV2
	}
	return nil
}

type RewardsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PageContext *rpc.PageContextResponse `protobuf:"bytes,2,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	Rewards     []*Reward                `protobuf:"bytes,3,rep,name=rewards,proto3" json:"rewards,omitempty"`
}

func (x *RewardsResponse) Reset() {
	*x = RewardsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardsResponse) ProtoMessage() {}

func (x *RewardsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardsResponse.ProtoReflect.Descriptor instead.
func (*RewardsResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{1}
}

func (x *RewardsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *RewardsResponse) GetPageContext() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *RewardsResponse) GetRewards() []*Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type RewardsByRewardIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardId string `protobuf:"bytes,1,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`
}

func (x *RewardsByRewardIdRequest) Reset() {
	*x = RewardsByRewardIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardsByRewardIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardsByRewardIdRequest) ProtoMessage() {}

func (x *RewardsByRewardIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardsByRewardIdRequest.ProtoReflect.Descriptor instead.
func (*RewardsByRewardIdRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{2}
}

func (x *RewardsByRewardIdRequest) GetRewardId() string {
	if x != nil {
		return x.RewardId
	}
	return ""
}

type RewardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Reward *Reward     `protobuf:"bytes,2,opt,name=reward,proto3" json:"reward,omitempty"`
}

func (x *RewardResponse) Reset() {
	*x = RewardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardResponse) ProtoMessage() {}

func (x *RewardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardResponse.ProtoReflect.Descriptor instead.
func (*RewardResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{3}
}

func (x *RewardResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *RewardResponse) GetReward() *Reward {
	if x != nil {
		return x.Reward
	}
	return nil
}

type GetRewardsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// one of ids or external_ref_ids is mandatory
	Ids             []string        `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	ExternalRefIds  []string        `protobuf:"bytes,2,rep,name=external_ref_ids,json=externalRefIds,proto3" json:"external_ref_ids,omitempty"`
	RewardOfferType RewardOfferType `protobuf:"varint,3,opt,name=reward_offer_type,json=rewardOfferType,proto3,enum=rewards.RewardOfferType" json:"reward_offer_type,omitempty"` // todo (utkarsh) : add pagination support
}

func (x *GetRewardsRequest) Reset() {
	*x = GetRewardsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardsRequest) ProtoMessage() {}

func (x *GetRewardsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardsRequest.ProtoReflect.Descriptor instead.
func (*GetRewardsRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetRewardsRequest) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *GetRewardsRequest) GetExternalRefIds() []string {
	if x != nil {
		return x.ExternalRefIds
	}
	return nil
}

func (x *GetRewardsRequest) GetRewardOfferType() RewardOfferType {
	if x != nil {
		return x.RewardOfferType
	}
	return RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE
}

type GetRewardsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Rewards []*Reward   `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"`
}

func (x *GetRewardsResponse) Reset() {
	*x = GetRewardsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardsResponse) ProtoMessage() {}

func (x *GetRewardsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardsResponse.ProtoReflect.Descriptor instead.
func (*GetRewardsResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetRewardsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRewardsResponse) GetRewards() []*Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type ChooseRewardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardId       string `protobuf:"bytes,1,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`
	RewardOptionId string `protobuf:"bytes,2,opt,name=reward_option_id,json=rewardOptionId,proto3" json:"reward_option_id,omitempty"`
	// metadata required for claiming a reward eg: nominee details
	// in case of SD reward
	ClaimMetadata *RewardClaimMetadata `protobuf:"bytes,3,opt,name=claim_metadata,json=claimMetadata,proto3" json:"claim_metadata,omitempty"`
}

func (x *ChooseRewardRequest) Reset() {
	*x = ChooseRewardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChooseRewardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChooseRewardRequest) ProtoMessage() {}

func (x *ChooseRewardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChooseRewardRequest.ProtoReflect.Descriptor instead.
func (*ChooseRewardRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{6}
}

func (x *ChooseRewardRequest) GetRewardId() string {
	if x != nil {
		return x.RewardId
	}
	return ""
}

func (x *ChooseRewardRequest) GetRewardOptionId() string {
	if x != nil {
		return x.RewardOptionId
	}
	return ""
}

func (x *ChooseRewardRequest) GetClaimMetadata() *RewardClaimMetadata {
	if x != nil {
		return x.ClaimMetadata
	}
	return nil
}

type ChooseRewardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ChooseRewardResponse) Reset() {
	*x = ChooseRewardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChooseRewardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChooseRewardResponse) ProtoMessage() {}

func (x *ChooseRewardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChooseRewardResponse.ProtoReflect.Descriptor instead.
func (*ChooseRewardResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{7}
}

func (x *ChooseRewardResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetUnOpenedRewardsCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id for which we need to return the count
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// fromTime can be used when we only need un-opened rewards count after a particular time
	FromTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=from_time,json=fromTime,proto3" json:"from_time,omitempty"`
	// uptoTime can be used when we only need un-opened rewards count upto a particular time
	UptoTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=upto_time,json=uptoTime,proto3" json:"upto_time,omitempty"`
}

func (x *GetUnOpenedRewardsCountRequest) Reset() {
	*x = GetUnOpenedRewardsCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUnOpenedRewardsCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUnOpenedRewardsCountRequest) ProtoMessage() {}

func (x *GetUnOpenedRewardsCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUnOpenedRewardsCountRequest.ProtoReflect.Descriptor instead.
func (*GetUnOpenedRewardsCountRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetUnOpenedRewardsCountRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetUnOpenedRewardsCountRequest) GetFromTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTime
	}
	return nil
}

func (x *GetUnOpenedRewardsCountRequest) GetUptoTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UptoTime
	}
	return nil
}

type GetUnOpenedRewardsCountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// unopened reward count
	UnopenedRewardCount int32 `protobuf:"varint,2,opt,name=unopened_reward_count,json=unopenedRewardCount,proto3" json:"unopened_reward_count,omitempty"`
}

func (x *GetUnOpenedRewardsCountResponse) Reset() {
	*x = GetUnOpenedRewardsCountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUnOpenedRewardsCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUnOpenedRewardsCountResponse) ProtoMessage() {}

func (x *GetUnOpenedRewardsCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUnOpenedRewardsCountResponse.ProtoReflect.Descriptor instead.
func (*GetUnOpenedRewardsCountResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetUnOpenedRewardsCountResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetUnOpenedRewardsCountResponse) GetUnopenedRewardCount() int32 {
	if x != nil {
		return x.UnopenedRewardCount
	}
	return 0
}

type GetRewardSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id for which we need to return the reward summary
	ActorId string                          `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Filter  *GetRewardSummaryRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *GetRewardSummaryRequest) Reset() {
	*x = GetRewardSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardSummaryRequest) ProtoMessage() {}

func (x *GetRewardSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardSummaryRequest.ProtoReflect.Descriptor instead.
func (*GetRewardSummaryRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetRewardSummaryRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetRewardSummaryRequest) GetFilter() *GetRewardSummaryRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type GetRewardSummaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// total cash reward amount earned
	TotalCashRewardEarned *money.Money `protobuf:"bytes,2,opt,name=total_cash_reward_earned,json=totalCashRewardEarned,proto3" json:"total_cash_reward_earned,omitempty"`
	// total sid reward amount earned
	TotalSidRewardEarned *money.Money `protobuf:"bytes,3,opt,name=total_sid_reward_earned,json=totalSidRewardEarned,proto3" json:"total_sid_reward_earned,omitempty"`
	// total fi coins earned
	TotalFiCoinsEarned int32 `protobuf:"varint,4,opt,name=total_fi_coins_earned,json=totalFiCoinsEarned,proto3" json:"total_fi_coins_earned,omitempty"`
	// total cash amount that is in_processing state.
	// i.e claimed but haven't been credited yet.
	TotalInProcessingCashRewardAmount *money.Money `protobuf:"bytes,5,opt,name=total_in_processing_cash_reward_amount,json=totalInProcessingCashRewardAmount,proto3" json:"total_in_processing_cash_reward_amount,omitempty"`
	// total sid amount that is in_processing state.
	// i.e claimed but haven't been credited yet.
	TotalInProcessingSidRewardAmount *money.Money `protobuf:"bytes,6,opt,name=total_in_processing_sid_reward_amount,json=totalInProcessingSidRewardAmount,proto3" json:"total_in_processing_sid_reward_amount,omitempty"`
	// total fi coins that are in_processing
	// i.e claimed but haven't been credited yet.
	TotalInProcessingFiCoins int32 `protobuf:"varint,7,opt,name=total_in_processing_fi_coins,json=totalInProcessingFiCoins,proto3" json:"total_in_processing_fi_coins,omitempty"`
	// count of rewards
	TotalCountOfRewards int32 `protobuf:"varint,8,opt,name=total_count_of_rewards,json=totalCountOfRewards,proto3" json:"total_count_of_rewards,omitempty"`
	// count of cash rewards
	TotalCountOfCashRewards int32 `protobuf:"varint,9,opt,name=total_count_of_cash_rewards,json=totalCountOfCashRewards,proto3" json:"total_count_of_cash_rewards,omitempty"`
	// count of sd rewards
	TotalCountOfSidRewards int32 `protobuf:"varint,10,opt,name=total_count_of_sid_rewards,json=totalCountOfSidRewards,proto3" json:"total_count_of_sid_rewards,omitempty"`
	// count of fi coin rewards
	TotalCountOfFiCoinRewards int32 `protobuf:"varint,11,opt,name=total_count_of_fi_coin_rewards,json=totalCountOfFiCoinRewards,proto3" json:"total_count_of_fi_coin_rewards,omitempty"`
	// total cash (and related) rewards that are currently locked for actor
	// i.e. pending on some action or time period
	TotalLockedCashRewardAmount *money.Money `protobuf:"bytes,12,opt,name=total_locked_cash_reward_amount,json=totalLockedCashRewardAmount,proto3" json:"total_locked_cash_reward_amount,omitempty"`
	// total fi-coins that are currently in locked state
	// i.e. pending on some action or time period
	TotalLockedFiCoinsRewardAmount int32 `protobuf:"varint,13,opt,name=total_locked_fi_coins_reward_amount,json=totalLockedFiCoinsRewardAmount,proto3" json:"total_locked_fi_coins_reward_amount,omitempty"`
}

func (x *GetRewardSummaryResponse) Reset() {
	*x = GetRewardSummaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardSummaryResponse) ProtoMessage() {}

func (x *GetRewardSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardSummaryResponse.ProtoReflect.Descriptor instead.
func (*GetRewardSummaryResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetRewardSummaryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRewardSummaryResponse) GetTotalCashRewardEarned() *money.Money {
	if x != nil {
		return x.TotalCashRewardEarned
	}
	return nil
}

func (x *GetRewardSummaryResponse) GetTotalSidRewardEarned() *money.Money {
	if x != nil {
		return x.TotalSidRewardEarned
	}
	return nil
}

func (x *GetRewardSummaryResponse) GetTotalFiCoinsEarned() int32 {
	if x != nil {
		return x.TotalFiCoinsEarned
	}
	return 0
}

func (x *GetRewardSummaryResponse) GetTotalInProcessingCashRewardAmount() *money.Money {
	if x != nil {
		return x.TotalInProcessingCashRewardAmount
	}
	return nil
}

func (x *GetRewardSummaryResponse) GetTotalInProcessingSidRewardAmount() *money.Money {
	if x != nil {
		return x.TotalInProcessingSidRewardAmount
	}
	return nil
}

func (x *GetRewardSummaryResponse) GetTotalInProcessingFiCoins() int32 {
	if x != nil {
		return x.TotalInProcessingFiCoins
	}
	return 0
}

func (x *GetRewardSummaryResponse) GetTotalCountOfRewards() int32 {
	if x != nil {
		return x.TotalCountOfRewards
	}
	return 0
}

func (x *GetRewardSummaryResponse) GetTotalCountOfCashRewards() int32 {
	if x != nil {
		return x.TotalCountOfCashRewards
	}
	return 0
}

func (x *GetRewardSummaryResponse) GetTotalCountOfSidRewards() int32 {
	if x != nil {
		return x.TotalCountOfSidRewards
	}
	return 0
}

func (x *GetRewardSummaryResponse) GetTotalCountOfFiCoinRewards() int32 {
	if x != nil {
		return x.TotalCountOfFiCoinRewards
	}
	return 0
}

func (x *GetRewardSummaryResponse) GetTotalLockedCashRewardAmount() *money.Money {
	if x != nil {
		return x.TotalLockedCashRewardAmount
	}
	return nil
}

func (x *GetRewardSummaryResponse) GetTotalLockedFiCoinsRewardAmount() int32 {
	if x != nil {
		return x.TotalLockedFiCoinsRewardAmount
	}
	return 0
}

type RetryRewardProcessingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardId string `protobuf:"bytes,1,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`
}

func (x *RetryRewardProcessingRequest) Reset() {
	*x = RetryRewardProcessingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryRewardProcessingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryRewardProcessingRequest) ProtoMessage() {}

func (x *RetryRewardProcessingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryRewardProcessingRequest.ProtoReflect.Descriptor instead.
func (*RetryRewardProcessingRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{12}
}

func (x *RetryRewardProcessingRequest) GetRewardId() string {
	if x != nil {
		return x.RewardId
	}
	return ""
}

type RetryRewardProcessingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *RetryRewardProcessingResponse) Reset() {
	*x = RetryRewardProcessingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryRewardProcessingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryRewardProcessingResponse) ProtoMessage() {}

func (x *RetryRewardProcessingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryRewardProcessingResponse.ProtoReflect.Descriptor instead.
func (*RetryRewardProcessingResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{13}
}

func (x *RetryRewardProcessingResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetReferralRewardsCappingInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// date on which referrals were unlocked for the actor
	ReferralsUnlockDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=referrals_unlock_date,json=referralsUnlockDate,proto3" json:"referrals_unlock_date,omitempty"`
}

func (x *GetReferralRewardsCappingInfoRequest) Reset() {
	*x = GetReferralRewardsCappingInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReferralRewardsCappingInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReferralRewardsCappingInfoRequest) ProtoMessage() {}

func (x *GetReferralRewardsCappingInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReferralRewardsCappingInfoRequest.ProtoReflect.Descriptor instead.
func (*GetReferralRewardsCappingInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetReferralRewardsCappingInfoRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetReferralRewardsCappingInfoRequest) GetReferralsUnlockDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ReferralsUnlockDate
	}
	return nil
}

type GetReferralRewardsCappingInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// is_max_cap_hit will be true if rewarded referrals max cap has been hit for the actor,
	// which means they won't be rewarded any more for any more referees that they onboard.
	IsMaxCapHit bool `protobuf:"varint,2,opt,name=is_max_cap_hit,json=isMaxCapHit,proto3" json:"is_max_cap_hit,omitempty"`
	// max_cap_reset_date will be nil if is_max_cap_hit is false,
	// and will contain the date from which the actor can start earning rewards for referrals again if is_max_cap_hit is true.
	MaxCapResetDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=max_cap_reset_date,json=maxCapResetDate,proto3" json:"max_cap_reset_date,omitempty"`
	// last_cap_reset_date will contain the date on which the referral rewards were last reset.
	// for users who have just unlocked referrals and haven't completed a single referral rewards capping duration,
	// this date will be the referrals unlock date. This won't be nil in any case.
	LastCapResetDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=last_cap_reset_date,json=lastCapResetDate,proto3" json:"last_cap_reset_date,omitempty"`
	// maximum no. of rewards which can be earned basis the capping.
	// note: if max cap is not yet hit, then this will have the zeroth value, i.e. field not to be referred if max cap is not hit.
	MaxCap int32 `protobuf:"varint,5,opt,name=max_cap,json=maxCap,proto3" json:"max_cap,omitempty"`
}

func (x *GetReferralRewardsCappingInfoResponse) Reset() {
	*x = GetReferralRewardsCappingInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReferralRewardsCappingInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReferralRewardsCappingInfoResponse) ProtoMessage() {}

func (x *GetReferralRewardsCappingInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReferralRewardsCappingInfoResponse.ProtoReflect.Descriptor instead.
func (*GetReferralRewardsCappingInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetReferralRewardsCappingInfoResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetReferralRewardsCappingInfoResponse) GetIsMaxCapHit() bool {
	if x != nil {
		return x.IsMaxCapHit
	}
	return false
}

func (x *GetReferralRewardsCappingInfoResponse) GetMaxCapResetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.MaxCapResetDate
	}
	return nil
}

func (x *GetReferralRewardsCappingInfoResponse) GetLastCapResetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.LastCapResetDate
	}
	return nil
}

func (x *GetReferralRewardsCappingInfoResponse) GetMaxCap() int32 {
	if x != nil {
		return x.MaxCap
	}
	return 0
}

type GetReferralRewardsCappingConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetReferralRewardsCappingConfigRequest) Reset() {
	*x = GetReferralRewardsCappingConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReferralRewardsCappingConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReferralRewardsCappingConfigRequest) ProtoMessage() {}

func (x *GetReferralRewardsCappingConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReferralRewardsCappingConfigRequest.ProtoReflect.Descriptor instead.
func (*GetReferralRewardsCappingConfigRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{16}
}

type GetReferralRewardsCappingConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// max number of referral rewards an actor can earn during each capping period
	RewardsCap uint32 `protobuf:"varint,2,opt,name=rewards_cap,json=rewardsCap,proto3" json:"rewards_cap,omitempty"`
	// time duration in which the capping is applicable, after which it's reset for a user
	CappingPeriod *durationpb.Duration `protobuf:"bytes,3,opt,name=capping_period,json=cappingPeriod,proto3" json:"capping_period,omitempty"`
}

func (x *GetReferralRewardsCappingConfigResponse) Reset() {
	*x = GetReferralRewardsCappingConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReferralRewardsCappingConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReferralRewardsCappingConfigResponse) ProtoMessage() {}

func (x *GetReferralRewardsCappingConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReferralRewardsCappingConfigResponse.ProtoReflect.Descriptor instead.
func (*GetReferralRewardsCappingConfigResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetReferralRewardsCappingConfigResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetReferralRewardsCappingConfigResponse) GetRewardsCap() uint32 {
	if x != nil {
		return x.RewardsCap
	}
	return 0
}

func (x *GetReferralRewardsCappingConfigResponse) GetCappingPeriod() *durationpb.Duration {
	if x != nil {
		return x.CappingPeriod
	}
	return nil
}

type GetAutoClaimableRewardsCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetAutoClaimableRewardsCountRequest) Reset() {
	*x = GetAutoClaimableRewardsCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAutoClaimableRewardsCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAutoClaimableRewardsCountRequest) ProtoMessage() {}

func (x *GetAutoClaimableRewardsCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAutoClaimableRewardsCountRequest.ProtoReflect.Descriptor instead.
func (*GetAutoClaimableRewardsCountRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetAutoClaimableRewardsCountRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetAutoClaimableRewardsCountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ClaimableRewardsCount uint32      `protobuf:"varint,2,opt,name=claimable_rewards_count,json=claimableRewardsCount,proto3" json:"claimable_rewards_count,omitempty"`
}

func (x *GetAutoClaimableRewardsCountResponse) Reset() {
	*x = GetAutoClaimableRewardsCountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAutoClaimableRewardsCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAutoClaimableRewardsCountResponse) ProtoMessage() {}

func (x *GetAutoClaimableRewardsCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAutoClaimableRewardsCountResponse.ProtoReflect.Descriptor instead.
func (*GetAutoClaimableRewardsCountResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetAutoClaimableRewardsCountResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAutoClaimableRewardsCountResponse) GetClaimableRewardsCount() uint32 {
	if x != nil {
		return x.ClaimableRewardsCount
	}
	return 0
}

type BulkClaimRewardsWithDefaultOptionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor for whom we want to claim rewards in bulk
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// max number of rewards that can be claimed in bulk
	MaxRewardsToClaim uint32 `protobuf:"varint,2,opt,name=max_rewards_to_claim,json=maxRewardsToClaim,proto3" json:"max_rewards_to_claim,omitempty"`
}

func (x *BulkClaimRewardsWithDefaultOptionRequest) Reset() {
	*x = BulkClaimRewardsWithDefaultOptionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkClaimRewardsWithDefaultOptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkClaimRewardsWithDefaultOptionRequest) ProtoMessage() {}

func (x *BulkClaimRewardsWithDefaultOptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkClaimRewardsWithDefaultOptionRequest.ProtoReflect.Descriptor instead.
func (*BulkClaimRewardsWithDefaultOptionRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{20}
}

func (x *BulkClaimRewardsWithDefaultOptionRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *BulkClaimRewardsWithDefaultOptionRequest) GetMaxRewardsToClaim() uint32 {
	if x != nil {
		return x.MaxRewardsToClaim
	}
	return 0
}

type BulkClaimRewardsWithDefaultOptionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *BulkClaimRewardsWithDefaultOptionResponse) Reset() {
	*x = BulkClaimRewardsWithDefaultOptionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkClaimRewardsWithDefaultOptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkClaimRewardsWithDefaultOptionResponse) ProtoMessage() {}

func (x *BulkClaimRewardsWithDefaultOptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkClaimRewardsWithDefaultOptionResponse.ProtoReflect.Descriptor instead.
func (*BulkClaimRewardsWithDefaultOptionResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{21}
}

func (x *BulkClaimRewardsWithDefaultOptionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type TriggerManualGiveawayRewardForActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor for whom the manual-giveaway reward needs to be triggered.
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// id of manual-giveaway rewardOffer that is to be used to generating the reward.
	RewardOfferId string `protobuf:"bytes,2,opt,name=reward_offer_id,json=rewardOfferId,proto3" json:"reward_offer_id,omitempty"`
	// unique request id to be passed by client for triggering a manual giveaway reward.
	ClientRequestId string `protobuf:"bytes,3,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	// optional reward amount, useful only when the configured manual-giveaway rewardOffer supports configurable reward amount.
	RewardAmount float32 `protobuf:"fixed32,4,opt,name=reward_amount,json=rewardAmount,proto3" json:"reward_amount,omitempty"`
}

func (x *TriggerManualGiveawayRewardForActorRequest) Reset() {
	*x = TriggerManualGiveawayRewardForActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerManualGiveawayRewardForActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerManualGiveawayRewardForActorRequest) ProtoMessage() {}

func (x *TriggerManualGiveawayRewardForActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerManualGiveawayRewardForActorRequest.ProtoReflect.Descriptor instead.
func (*TriggerManualGiveawayRewardForActorRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{22}
}

func (x *TriggerManualGiveawayRewardForActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *TriggerManualGiveawayRewardForActorRequest) GetRewardOfferId() string {
	if x != nil {
		return x.RewardOfferId
	}
	return ""
}

func (x *TriggerManualGiveawayRewardForActorRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *TriggerManualGiveawayRewardForActorRequest) GetRewardAmount() float32 {
	if x != nil {
		return x.RewardAmount
	}
	return 0
}

type TriggerManualGiveawayRewardForActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *TriggerManualGiveawayRewardForActorResponse) Reset() {
	*x = TriggerManualGiveawayRewardForActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerManualGiveawayRewardForActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerManualGiveawayRewardForActorResponse) ProtoMessage() {}

func (x *TriggerManualGiveawayRewardForActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerManualGiveawayRewardForActorResponse.ProtoReflect.Descriptor instead.
func (*TriggerManualGiveawayRewardForActorResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{23}
}

func (x *TriggerManualGiveawayRewardForActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetCreditCardLinkedRewardDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reward/reward-clawback refIds, will be txnExternalId for rewards-given/clawbacks-done at a txn level, billId for rewards-given at a bill level.
	RefIds []string `protobuf:"bytes,1,rep,name=ref_ids,json=refIds,proto3" json:"ref_ids,omitempty"`
	// offer type whose rewards/clawbacks need to be fetched, offer type will be CREDIT_CARD_SPENDS_1X_OFFER for fetching cc 1x rewards/reward-clawbacks.
	RewardOfferType RewardOfferType `protobuf:"varint,2,opt,name=reward_offer_type,json=rewardOfferType,proto3,enum=rewards.RewardOfferType" json:"reward_offer_type,omitempty"`
}

func (x *GetCreditCardLinkedRewardDetailsRequest) Reset() {
	*x = GetCreditCardLinkedRewardDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardLinkedRewardDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardLinkedRewardDetailsRequest) ProtoMessage() {}

func (x *GetCreditCardLinkedRewardDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardLinkedRewardDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetCreditCardLinkedRewardDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetCreditCardLinkedRewardDetailsRequest) GetRefIds() []string {
	if x != nil {
		return x.RefIds
	}
	return nil
}

func (x *GetCreditCardLinkedRewardDetailsRequest) GetRewardOfferType() RewardOfferType {
	if x != nil {
		return x.RewardOfferType
	}
	return RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE
}

type GetCreditCardLinkedRewardDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// details of multiple rewards/reward-clawbacks based on passed refIds.
	DetailsList []*GetCreditCardLinkedRewardDetailsResponse_Details `protobuf:"bytes,2,rep,name=details_list,json=detailsList,proto3" json:"details_list,omitempty"`
}

func (x *GetCreditCardLinkedRewardDetailsResponse) Reset() {
	*x = GetCreditCardLinkedRewardDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardLinkedRewardDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardLinkedRewardDetailsResponse) ProtoMessage() {}

func (x *GetCreditCardLinkedRewardDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardLinkedRewardDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetCreditCardLinkedRewardDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetCreditCardLinkedRewardDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCreditCardLinkedRewardDetailsResponse) GetDetailsList() []*GetCreditCardLinkedRewardDetailsResponse_Details {
	if x != nil {
		return x.DetailsList
	}
	return nil
}

type GetCreditCard1XRewardsSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// credit card account ID for which we need to fetch 1x rewards summary
	CreditCardAccountId string `protobuf:"bytes,1,opt,name=credit_card_account_id,json=creditCardAccountId,proto3" json:"credit_card_account_id,omitempty"`
	// time window (from/till) for which we want to fetch credit cards rewards summary.
	TimeWindow *TimeWindow `protobuf:"bytes,2,opt,name=time_window,json=timeWindow,proto3" json:"time_window,omitempty"`
}

func (x *GetCreditCard1XRewardsSummaryRequest) Reset() {
	*x = GetCreditCard1XRewardsSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCard1XRewardsSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCard1XRewardsSummaryRequest) ProtoMessage() {}

func (x *GetCreditCard1XRewardsSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCard1XRewardsSummaryRequest.ProtoReflect.Descriptor instead.
func (*GetCreditCard1XRewardsSummaryRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{26}
}

func (x *GetCreditCard1XRewardsSummaryRequest) GetCreditCardAccountId() string {
	if x != nil {
		return x.CreditCardAccountId
	}
	return ""
}

func (x *GetCreditCard1XRewardsSummaryRequest) GetTimeWindow() *TimeWindow {
	if x != nil {
		return x.TimeWindow
	}
	return nil
}

type GetCreditCard1XRewardsSummaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// rewards in processed state
	Processed_1XFiCoins uint32 `protobuf:"varint,2,opt,name=processed_1x_fi_coins,json=processed1xFiCoins,proto3" json:"processed_1x_fi_coins,omitempty"`
	// rewards in states other than "processed"
	Processing_1XFiCoins uint32 `protobuf:"varint,3,opt,name=processing_1x_fi_coins,json=processing1xFiCoins,proto3" json:"processing_1x_fi_coins,omitempty"`
	// leaderboard containing a list of all merchants (out of a configured list of merchants)
	// for which some reward has been generated (processing state doesn't matter in this case)
	// sorted by the amount of reward_units, in descending order.
	MerchantRewardAggregates []*GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate `protobuf:"bytes,4,rep,name=merchant_reward_aggregates,json=merchantRewardAggregates,proto3" json:"merchant_reward_aggregates,omitempty"`
}

func (x *GetCreditCard1XRewardsSummaryResponse) Reset() {
	*x = GetCreditCard1XRewardsSummaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCard1XRewardsSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCard1XRewardsSummaryResponse) ProtoMessage() {}

func (x *GetCreditCard1XRewardsSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCard1XRewardsSummaryResponse.ProtoReflect.Descriptor instead.
func (*GetCreditCard1XRewardsSummaryResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetCreditCard1XRewardsSummaryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCreditCard1XRewardsSummaryResponse) GetProcessed_1XFiCoins() uint32 {
	if x != nil {
		return x.Processed_1XFiCoins
	}
	return 0
}

func (x *GetCreditCard1XRewardsSummaryResponse) GetProcessing_1XFiCoins() uint32 {
	if x != nil {
		return x.Processing_1XFiCoins
	}
	return 0
}

func (x *GetCreditCard1XRewardsSummaryResponse) GetMerchantRewardAggregates() []*GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate {
	if x != nil {
		return x.MerchantRewardAggregates
	}
	return nil
}

type TimeWindow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FromTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=from_time,json=fromTime,proto3" json:"from_time,omitempty"`
	TillTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=till_time,json=tillTime,proto3" json:"till_time,omitempty"`
}

func (x *TimeWindow) Reset() {
	*x = TimeWindow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeWindow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeWindow) ProtoMessage() {}

func (x *TimeWindow) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeWindow.ProtoReflect.Descriptor instead.
func (*TimeWindow) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{28}
}

func (x *TimeWindow) GetFromTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTime
	}
	return nil
}

func (x *TimeWindow) GetTillTime() *timestamppb.Timestamp {
	if x != nil {
		return x.TillTime
	}
	return nil
}

type GetRewardsCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string                          `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Filters *GetRewardsCountRequest_Filters `protobuf:"bytes,2,opt,name=filters,proto3" json:"filters,omitempty"`
}

func (x *GetRewardsCountRequest) Reset() {
	*x = GetRewardsCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardsCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardsCountRequest) ProtoMessage() {}

func (x *GetRewardsCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardsCountRequest.ProtoReflect.Descriptor instead.
func (*GetRewardsCountRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{29}
}

func (x *GetRewardsCountRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetRewardsCountRequest) GetFilters() *GetRewardsCountRequest_Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

type GetRewardsCountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// count of rewards
	RewardsCount uint32 `protobuf:"varint,2,opt,name=rewards_count,json=rewardsCount,proto3" json:"rewards_count,omitempty"`
}

func (x *GetRewardsCountResponse) Reset() {
	*x = GetRewardsCountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardsCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardsCountResponse) ProtoMessage() {}

func (x *GetRewardsCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardsCountResponse.ProtoReflect.Descriptor instead.
func (*GetRewardsCountResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{30}
}

func (x *GetRewardsCountResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRewardsCountResponse) GetRewardsCount() uint32 {
	if x != nil {
		return x.RewardsCount
	}
	return 0
}

type GetRewardUnitsUtilisationForActorAndOfferInMonthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId       string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RewardOfferId string `protobuf:"bytes,2,opt,name=reward_offer_id,json=rewardOfferId,proto3" json:"reward_offer_id,omitempty"`
	// any date of the month in which utilization is to be checked
	Date *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty"`
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) Reset() {
	*x = GetRewardUnitsUtilisationForActorAndOfferInMonthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) ProtoMessage() {}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardUnitsUtilisationForActorAndOfferInMonthRequest.ProtoReflect.Descriptor instead.
func (*GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{31}
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) GetRewardOfferId() string {
	if x != nil {
		return x.RewardOfferId
	}
	return ""
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

type GetRewardUnitsUtilisationForActorAndOfferInMonthResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// utilization of offer for given actor in the month
	MonthlyRewardOfferRewardUnitsActorUtilisation *RewardOfferRewardUnitsActorUtilisationInTimePeriod `protobuf:"bytes,2,opt,name=monthly_reward_offer_reward_units_actor_utilisation,json=monthlyRewardOfferRewardUnitsActorUtilisation,proto3" json:"monthly_reward_offer_reward_units_actor_utilisation,omitempty"`
	// count of rewards generated in the given month
	MonthlyRewardCount uint32 `protobuf:"varint,3,opt,name=monthly_reward_count,json=monthlyRewardCount,proto3" json:"monthly_reward_count,omitempty"`
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) Reset() {
	*x = GetRewardUnitsUtilisationForActorAndOfferInMonthResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) ProtoMessage() {}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardUnitsUtilisationForActorAndOfferInMonthResponse.ProtoReflect.Descriptor instead.
func (*GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{32}
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) GetMonthlyRewardOfferRewardUnitsActorUtilisation() *RewardOfferRewardUnitsActorUtilisationInTimePeriod {
	if x != nil {
		return x.MonthlyRewardOfferRewardUnitsActorUtilisation
	}
	return nil
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) GetMonthlyRewardCount() uint32 {
	if x != nil {
		return x.MonthlyRewardCount
	}
	return 0
}

type GetRewardUtilisationForActorAndOfferRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId       string                                               `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RewardOfferId string                                               `protobuf:"bytes,2,opt,name=reward_offer_id,json=rewardOfferId,proto3" json:"reward_offer_id,omitempty"`
	Filters       *GetRewardUtilisationForActorAndOfferRequest_Filters `protobuf:"bytes,3,opt,name=filters,proto3" json:"filters,omitempty"`
}

func (x *GetRewardUtilisationForActorAndOfferRequest) Reset() {
	*x = GetRewardUtilisationForActorAndOfferRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardUtilisationForActorAndOfferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardUtilisationForActorAndOfferRequest) ProtoMessage() {}

func (x *GetRewardUtilisationForActorAndOfferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardUtilisationForActorAndOfferRequest.ProtoReflect.Descriptor instead.
func (*GetRewardUtilisationForActorAndOfferRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{33}
}

func (x *GetRewardUtilisationForActorAndOfferRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetRewardUtilisationForActorAndOfferRequest) GetRewardOfferId() string {
	if x != nil {
		return x.RewardOfferId
	}
	return ""
}

func (x *GetRewardUtilisationForActorAndOfferRequest) GetFilters() *GetRewardUtilisationForActorAndOfferRequest_Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

type GetRewardUtilisationForActorAndOfferResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// will be false if respective capping is set for the given reward offer
	// ie. if global level capping is set in case we want to fetch global level utilization
	// ie. if monthly capping is set in case we want to fetch all reward units generated while reward offer was active
	// if it is true then reward_offer_units_utilization_for_actor will be nil
	IsCappingNotConfiguredForRewardOffer bool `protobuf:"varint,2,opt,name=is_capping_not_configured_for_reward_offer,json=isCappingNotConfiguredForRewardOffer,proto3" json:"is_capping_not_configured_for_reward_offer,omitempty"`
	// utilization of offer for given actor
	// utilization data will only be populated if capping is set for reward offer
	RewardOfferRewardUnitsUtilizationForActor *GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor `protobuf:"bytes,3,opt,name=reward_offer_reward_units_utilization_for_actor,json=rewardOfferRewardUnitsUtilizationForActor,proto3" json:"reward_offer_reward_units_utilization_for_actor,omitempty"`
	// count of rewards generated in reward offer period for the given actor
	TotalRewardsCount uint32 `protobuf:"varint,4,opt,name=total_rewards_count,json=totalRewardsCount,proto3" json:"total_rewards_count,omitempty"`
}

func (x *GetRewardUtilisationForActorAndOfferResponse) Reset() {
	*x = GetRewardUtilisationForActorAndOfferResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardUtilisationForActorAndOfferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardUtilisationForActorAndOfferResponse) ProtoMessage() {}

func (x *GetRewardUtilisationForActorAndOfferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardUtilisationForActorAndOfferResponse.ProtoReflect.Descriptor instead.
func (*GetRewardUtilisationForActorAndOfferResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{34}
}

func (x *GetRewardUtilisationForActorAndOfferResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRewardUtilisationForActorAndOfferResponse) GetIsCappingNotConfiguredForRewardOffer() bool {
	if x != nil {
		return x.IsCappingNotConfiguredForRewardOffer
	}
	return false
}

func (x *GetRewardUtilisationForActorAndOfferResponse) GetRewardOfferRewardUnitsUtilizationForActor() *GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor {
	if x != nil {
		return x.RewardOfferRewardUnitsUtilizationForActor
	}
	return nil
}

func (x *GetRewardUtilisationForActorAndOfferResponse) GetTotalRewardsCount() uint32 {
	if x != nil {
		return x.TotalRewardsCount
	}
	return 0
}

type BulkClaimRewardsWithDefaultOptionV2Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor for whom we want to claim all unopened money plants rewards in bulk
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *BulkClaimRewardsWithDefaultOptionV2Request) Reset() {
	*x = BulkClaimRewardsWithDefaultOptionV2Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkClaimRewardsWithDefaultOptionV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkClaimRewardsWithDefaultOptionV2Request) ProtoMessage() {}

func (x *BulkClaimRewardsWithDefaultOptionV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkClaimRewardsWithDefaultOptionV2Request.ProtoReflect.Descriptor instead.
func (*BulkClaimRewardsWithDefaultOptionV2Request) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{35}
}

func (x *BulkClaimRewardsWithDefaultOptionV2Request) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type BulkClaimRewardsWithDefaultOptionV2Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *BulkClaimRewardsWithDefaultOptionV2Response) Reset() {
	*x = BulkClaimRewardsWithDefaultOptionV2Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkClaimRewardsWithDefaultOptionV2Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkClaimRewardsWithDefaultOptionV2Response) ProtoMessage() {}

func (x *BulkClaimRewardsWithDefaultOptionV2Response) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkClaimRewardsWithDefaultOptionV2Response.ProtoReflect.Descriptor instead.
func (*BulkClaimRewardsWithDefaultOptionV2Response) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{36}
}

func (x *BulkClaimRewardsWithDefaultOptionV2Response) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetBulkClaimRewardsProcessingStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetBulkClaimRewardsProcessingStateRequest) Reset() {
	*x = GetBulkClaimRewardsProcessingStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBulkClaimRewardsProcessingStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBulkClaimRewardsProcessingStateRequest) ProtoMessage() {}

func (x *GetBulkClaimRewardsProcessingStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBulkClaimRewardsProcessingStateRequest.ProtoReflect.Descriptor instead.
func (*GetBulkClaimRewardsProcessingStateRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{37}
}

func (x *GetBulkClaimRewardsProcessingStateRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetBulkClaimRewardsProcessingStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// returns true if still bulk claim rewards flow is in processing state.
	IsProcessingState bool `protobuf:"varint,2,opt,name=is_processing_state,json=isProcessingState,proto3" json:"is_processing_state,omitempty"`
}

func (x *GetBulkClaimRewardsProcessingStateResponse) Reset() {
	*x = GetBulkClaimRewardsProcessingStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBulkClaimRewardsProcessingStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBulkClaimRewardsProcessingStateResponse) ProtoMessage() {}

func (x *GetBulkClaimRewardsProcessingStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBulkClaimRewardsProcessingStateResponse.ProtoReflect.Descriptor instead.
func (*GetBulkClaimRewardsProcessingStateResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{38}
}

func (x *GetBulkClaimRewardsProcessingStateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetBulkClaimRewardsProcessingStateResponse) GetIsProcessingState() bool {
	if x != nil {
		return x.IsProcessingState
	}
	return false
}

type GetAllRewardsAndProjectionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor for whom we want to fetch the generated reward or projection for given filters
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// type of action that would have generated the reward (mandatory)
	ActionType CollectedDataType `protobuf:"varint,2,opt,name=action_type,json=actionType,proto3,enum=rewards.CollectedDataType" json:"action_type,omitempty"`
	// list of event ref IDs that might have generated reward or projection (max 30 ref_ids can be provided in a single go)
	RefIds []string `protobuf:"bytes,3,rep,name=ref_ids,json=refIds,proto3" json:"ref_ids,omitempty"`
}

func (x *GetAllRewardsAndProjectionRequest) Reset() {
	*x = GetAllRewardsAndProjectionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllRewardsAndProjectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllRewardsAndProjectionRequest) ProtoMessage() {}

func (x *GetAllRewardsAndProjectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllRewardsAndProjectionRequest.ProtoReflect.Descriptor instead.
func (*GetAllRewardsAndProjectionRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{39}
}

func (x *GetAllRewardsAndProjectionRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAllRewardsAndProjectionRequest) GetActionType() CollectedDataType {
	if x != nil {
		return x.ActionType
	}
	return CollectedDataType_UNSPECIFIED_COLLECTED_DATA_TYPE
}

func (x *GetAllRewardsAndProjectionRequest) GetRefIds() []string {
	if x != nil {
		return x.RefIds
	}
	return nil
}

type GetAllRewardsAndProjectionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// map of ref_ids to reward_entity
	RefIdToRewardEntitiesMap map[string]*GetAllRewardsAndProjectionResponse_RewardEntities `protobuf:"bytes,2,rep,name=ref_id_to_reward_entities_map,json=refIdToRewardEntitiesMap,proto3" json:"ref_id_to_reward_entities_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetAllRewardsAndProjectionResponse) Reset() {
	*x = GetAllRewardsAndProjectionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllRewardsAndProjectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllRewardsAndProjectionResponse) ProtoMessage() {}

func (x *GetAllRewardsAndProjectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllRewardsAndProjectionResponse.ProtoReflect.Descriptor instead.
func (*GetAllRewardsAndProjectionResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{40}
}

func (x *GetAllRewardsAndProjectionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAllRewardsAndProjectionResponse) GetRefIdToRewardEntitiesMap() map[string]*GetAllRewardsAndProjectionResponse_RewardEntities {
	if x != nil {
		return x.RefIdToRewardEntitiesMap
	}
	return nil
}

type GetRewardAggregatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id for which we need to return the reward summary
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// filters on which we want to perform aggregates
	Filter *GetRewardAggregatesRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *GetRewardAggregatesRequest) Reset() {
	*x = GetRewardAggregatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardAggregatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardAggregatesRequest) ProtoMessage() {}

func (x *GetRewardAggregatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardAggregatesRequest.ProtoReflect.Descriptor instead.
func (*GetRewardAggregatesRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{41}
}

func (x *GetRewardAggregatesRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetRewardAggregatesRequest) GetFilter() *GetRewardAggregatesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type GetRewardAggregatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// aggregates of different reward types
	Aggregates []*RewardOptionMinimal `protobuf:"bytes,2,rep,name=aggregates,proto3" json:"aggregates,omitempty"`
}

func (x *GetRewardAggregatesResponse) Reset() {
	*x = GetRewardAggregatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardAggregatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardAggregatesResponse) ProtoMessage() {}

func (x *GetRewardAggregatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardAggregatesResponse.ProtoReflect.Descriptor instead.
func (*GetRewardAggregatesResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{42}
}

func (x *GetRewardAggregatesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRewardAggregatesResponse) GetAggregates() []*RewardOptionMinimal {
	if x != nil {
		return x.Aggregates
	}
	return nil
}

type GetEstimatedRewardsInTimeDurationForTierRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor for whom we want to fetch estimate
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// tier for which we want to estimate rewards
	UserTier GetEstimatedRewardsInTimeDurationForTierRequest_UserTier `protobuf:"varint,2,opt,name=user_tier,json=userTier,proto3,enum=rewards.GetEstimatedRewardsInTimeDurationForTierRequest_UserTier" json:"user_tier,omitempty"`
	// duration in which we want to estimate rewards
	TimeWindow *TimeWindow `protobuf:"bytes,3,opt,name=time_window,json=timeWindow,proto3" json:"time_window,omitempty"`
}

func (x *GetEstimatedRewardsInTimeDurationForTierRequest) Reset() {
	*x = GetEstimatedRewardsInTimeDurationForTierRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEstimatedRewardsInTimeDurationForTierRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEstimatedRewardsInTimeDurationForTierRequest) ProtoMessage() {}

func (x *GetEstimatedRewardsInTimeDurationForTierRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEstimatedRewardsInTimeDurationForTierRequest.ProtoReflect.Descriptor instead.
func (*GetEstimatedRewardsInTimeDurationForTierRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{43}
}

func (x *GetEstimatedRewardsInTimeDurationForTierRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetEstimatedRewardsInTimeDurationForTierRequest) GetUserTier() GetEstimatedRewardsInTimeDurationForTierRequest_UserTier {
	if x != nil {
		return x.UserTier
	}
	return GetEstimatedRewardsInTimeDurationForTierRequest_USER_TIER_UNSPECIFIED
}

func (x *GetEstimatedRewardsInTimeDurationForTierRequest) GetTimeWindow() *TimeWindow {
	if x != nil {
		return x.TimeWindow
	}
	return nil
}

type GetEstimatedRewardsInTimeDurationForTierResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// estimate of anchor fi coin rewards that a user could potentially earn for the given tier, in the given duration
	AnchorFiCoinRewards uint32 `protobuf:"varint,2,opt,name=anchor_fi_coin_rewards,json=anchorFiCoinRewards,proto3" json:"anchor_fi_coin_rewards,omitempty"`
	// estimate of cashback rewards that a user could potentially earn for the given tier, in the given duration
	CashbackReward float32 `protobuf:"fixed32,3,opt,name=cashback_reward,json=cashbackReward,proto3" json:"cashback_reward,omitempty"`
}

func (x *GetEstimatedRewardsInTimeDurationForTierResponse) Reset() {
	*x = GetEstimatedRewardsInTimeDurationForTierResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEstimatedRewardsInTimeDurationForTierResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEstimatedRewardsInTimeDurationForTierResponse) ProtoMessage() {}

func (x *GetEstimatedRewardsInTimeDurationForTierResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEstimatedRewardsInTimeDurationForTierResponse.ProtoReflect.Descriptor instead.
func (*GetEstimatedRewardsInTimeDurationForTierResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{44}
}

func (x *GetEstimatedRewardsInTimeDurationForTierResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetEstimatedRewardsInTimeDurationForTierResponse) GetAnchorFiCoinRewards() uint32 {
	if x != nil {
		return x.AnchorFiCoinRewards
	}
	return 0
}

func (x *GetEstimatedRewardsInTimeDurationForTierResponse) GetCashbackReward() float32 {
	if x != nil {
		return x.CashbackReward
	}
	return 0
}

type ForceRetryRewardProcessingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the reward to be retried
	RewardId string `protobuf:"bytes,1,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`
}

func (x *ForceRetryRewardProcessingRequest) Reset() {
	*x = ForceRetryRewardProcessingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForceRetryRewardProcessingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForceRetryRewardProcessingRequest) ProtoMessage() {}

func (x *ForceRetryRewardProcessingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForceRetryRewardProcessingRequest.ProtoReflect.Descriptor instead.
func (*ForceRetryRewardProcessingRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{45}
}

func (x *ForceRetryRewardProcessingRequest) GetRewardId() string {
	if x != nil {
		return x.RewardId
	}
	return ""
}

type ForceRetryRewardProcessingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ForceRetryRewardProcessingResponse) Reset() {
	*x = ForceRetryRewardProcessingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForceRetryRewardProcessingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForceRetryRewardProcessingResponse) ProtoMessage() {}

func (x *ForceRetryRewardProcessingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForceRetryRewardProcessingResponse.ProtoReflect.Descriptor instead.
func (*ForceRetryRewardProcessingResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{46}
}

func (x *ForceRetryRewardProcessingResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// contains 'and' and 'or' filters
type RewardsByActorIdRequest_FiltersV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rewards that satisfy any condition will be returned.
	// NOTE: only some of the fields from Filter are supported
	OrFilter *RewardsByActorIdRequest_Filter `protobuf:"bytes,4,opt,name=or_filter,json=orFilter,proto3" json:"or_filter,omitempty"`
	// rewards that satisfy all the given conditions will be returned.
	AndFilter *RewardsByActorIdRequest_Filter `protobuf:"bytes,5,opt,name=and_filter,json=andFilter,proto3" json:"and_filter,omitempty"`
}

func (x *RewardsByActorIdRequest_FiltersV2) Reset() {
	*x = RewardsByActorIdRequest_FiltersV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardsByActorIdRequest_FiltersV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardsByActorIdRequest_FiltersV2) ProtoMessage() {}

func (x *RewardsByActorIdRequest_FiltersV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardsByActorIdRequest_FiltersV2.ProtoReflect.Descriptor instead.
func (*RewardsByActorIdRequest_FiltersV2) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *RewardsByActorIdRequest_FiltersV2) GetOrFilter() *RewardsByActorIdRequest_Filter {
	if x != nil {
		return x.OrFilter
	}
	return nil
}

func (x *RewardsByActorIdRequest_FiltersV2) GetAndFilter() *RewardsByActorIdRequest_Filter {
	if x != nil {
		return x.AndFilter
	}
	return nil
}

// all supported filters
type RewardsByActorIdRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardId       string                 `protobuf:"bytes,1,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`
	RewardType     RewardType             `protobuf:"varint,2,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	VisibilityType VisibilityType         `protobuf:"varint,3,opt,name=visibility_type,json=visibilityType,proto3,enum=rewards.VisibilityType" json:"visibility_type,omitempty"`
	StartDate      *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate        *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	RewardOfferId  string                 `protobuf:"bytes,6,opt,name=reward_offer_id,json=rewardOfferId,proto3" json:"reward_offer_id,omitempty"`
	// deprecated in favour of RewardOfferTypes
	//
	// Deprecated: Marked as deprecated in api/rewards/service.proto.
	RewardOfferType RewardOfferType     `protobuf:"varint,7,opt,name=reward_offer_type,json=rewardOfferType,proto3,enum=rewards.RewardOfferType" json:"reward_offer_type,omitempty"`
	ExternalRefList []string            `protobuf:"bytes,8,rep,name=external_ref_list,json=externalRefList,proto3" json:"external_ref_list,omitempty"`
	Statuses        []RewardStatus      `protobuf:"varint,9,rep,packed,name=statuses,proto3,enum=rewards.RewardStatus" json:"statuses,omitempty"`
	RefIds          []string            `protobuf:"bytes,10,rep,name=ref_ids,json=refIds,proto3" json:"ref_ids,omitempty"`
	ActionTypes     []CollectedDataType `protobuf:"varint,11,rep,packed,name=action_types,json=actionTypes,proto3,enum=rewards.CollectedDataType" json:"action_types,omitempty"`
	ClawbackRefIds  []string            `protobuf:"bytes,12,rep,name=clawback_ref_ids,json=clawbackRefIds,proto3" json:"clawback_ref_ids,omitempty"`
	ClaimType       ClaimType           `protobuf:"varint,13,opt,name=claim_type,json=claimType,proto3,enum=rewards.ClaimType" json:"claim_type,omitempty"`
	// if this is set then RewardOfferType will be ignored
	RewardOfferTypes []RewardOfferType `protobuf:"varint,14,rep,packed,name=reward_offer_types,json=rewardOfferTypes,proto3,enum=rewards.RewardOfferType" json:"reward_offer_types,omitempty"`
}

func (x *RewardsByActorIdRequest_Filter) Reset() {
	*x = RewardsByActorIdRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardsByActorIdRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardsByActorIdRequest_Filter) ProtoMessage() {}

func (x *RewardsByActorIdRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardsByActorIdRequest_Filter.ProtoReflect.Descriptor instead.
func (*RewardsByActorIdRequest_Filter) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{0, 1}
}

func (x *RewardsByActorIdRequest_Filter) GetRewardId() string {
	if x != nil {
		return x.RewardId
	}
	return ""
}

func (x *RewardsByActorIdRequest_Filter) GetRewardType() RewardType {
	if x != nil {
		return x.RewardType
	}
	return RewardType_REWARD_TYPE_UNSPECIFIED
}

func (x *RewardsByActorIdRequest_Filter) GetVisibilityType() VisibilityType {
	if x != nil {
		return x.VisibilityType
	}
	return VisibilityType_VISIBILITY_TYPE_UNSPECIFIED
}

func (x *RewardsByActorIdRequest_Filter) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *RewardsByActorIdRequest_Filter) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *RewardsByActorIdRequest_Filter) GetRewardOfferId() string {
	if x != nil {
		return x.RewardOfferId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/rewards/service.proto.
func (x *RewardsByActorIdRequest_Filter) GetRewardOfferType() RewardOfferType {
	if x != nil {
		return x.RewardOfferType
	}
	return RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE
}

func (x *RewardsByActorIdRequest_Filter) GetExternalRefList() []string {
	if x != nil {
		return x.ExternalRefList
	}
	return nil
}

func (x *RewardsByActorIdRequest_Filter) GetStatuses() []RewardStatus {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *RewardsByActorIdRequest_Filter) GetRefIds() []string {
	if x != nil {
		return x.RefIds
	}
	return nil
}

func (x *RewardsByActorIdRequest_Filter) GetActionTypes() []CollectedDataType {
	if x != nil {
		return x.ActionTypes
	}
	return nil
}

func (x *RewardsByActorIdRequest_Filter) GetClawbackRefIds() []string {
	if x != nil {
		return x.ClawbackRefIds
	}
	return nil
}

func (x *RewardsByActorIdRequest_Filter) GetClaimType() ClaimType {
	if x != nil {
		return x.ClaimType
	}
	return ClaimType_CLAIM_TYPE_UNSPECIFIED
}

func (x *RewardsByActorIdRequest_Filter) GetRewardOfferTypes() []RewardOfferType {
	if x != nil {
		return x.RewardOfferTypes
	}
	return nil
}

// filter to use while fetching rewards for calculating summary.
type GetRewardSummaryRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated in favour of RewardOfferTypes
	// if passed with RewardOfferTypes, this will be ignored
	//
	// Deprecated: Marked as deprecated in api/rewards/service.proto.
	RewardOfferType RewardOfferType `protobuf:"varint,1,opt,name=reward_offer_type,json=rewardOfferType,proto3,enum=rewards.RewardOfferType" json:"reward_offer_type,omitempty"`
	ExternalRefIds  []string        `protobuf:"bytes,2,rep,name=external_ref_ids,json=externalRefIds,proto3" json:"external_ref_ids,omitempty"`
	// fromTime can be used when we only need summary of earned rewards after a particular time
	FromTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=from_time,json=fromTime,proto3" json:"from_time,omitempty"`
	// uptoTime can be used when we only need summary of earned rewards upto a particular time
	UptoTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=upto_time,json=uptoTime,proto3" json:"upto_time,omitempty"`
	// different action types that lead to generation of reward
	ActionTypes      []CollectedDataType `protobuf:"varint,5,rep,packed,name=action_types,json=actionTypes,proto3,enum=rewards.CollectedDataType" json:"action_types,omitempty"`
	RewardOfferTypes []RewardOfferType   `protobuf:"varint,6,rep,packed,name=reward_offer_types,json=rewardOfferTypes,proto3,enum=rewards.RewardOfferType" json:"reward_offer_types,omitempty"`
}

func (x *GetRewardSummaryRequest_Filter) Reset() {
	*x = GetRewardSummaryRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardSummaryRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardSummaryRequest_Filter) ProtoMessage() {}

func (x *GetRewardSummaryRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardSummaryRequest_Filter.ProtoReflect.Descriptor instead.
func (*GetRewardSummaryRequest_Filter) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{10, 0}
}

// Deprecated: Marked as deprecated in api/rewards/service.proto.
func (x *GetRewardSummaryRequest_Filter) GetRewardOfferType() RewardOfferType {
	if x != nil {
		return x.RewardOfferType
	}
	return RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE
}

func (x *GetRewardSummaryRequest_Filter) GetExternalRefIds() []string {
	if x != nil {
		return x.ExternalRefIds
	}
	return nil
}

func (x *GetRewardSummaryRequest_Filter) GetFromTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTime
	}
	return nil
}

func (x *GetRewardSummaryRequest_Filter) GetUptoTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UptoTime
	}
	return nil
}

func (x *GetRewardSummaryRequest_Filter) GetActionTypes() []CollectedDataType {
	if x != nil {
		return x.ActionTypes
	}
	return nil
}

func (x *GetRewardSummaryRequest_Filter) GetRewardOfferTypes() []RewardOfferType {
	if x != nil {
		return x.RewardOfferTypes
	}
	return nil
}

type GetCreditCardLinkedRewardDetailsResponse_Details struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Details:
	//
	//	*GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails
	//	*GetCreditCardLinkedRewardDetailsResponse_Details_RewardClawbackDetails
	Details isGetCreditCardLinkedRewardDetailsResponse_Details_Details `protobuf_oneof:"details"`
}

func (x *GetCreditCardLinkedRewardDetailsResponse_Details) Reset() {
	*x = GetCreditCardLinkedRewardDetailsResponse_Details{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardLinkedRewardDetailsResponse_Details) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardLinkedRewardDetailsResponse_Details) ProtoMessage() {}

func (x *GetCreditCardLinkedRewardDetailsResponse_Details) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardLinkedRewardDetailsResponse_Details.ProtoReflect.Descriptor instead.
func (*GetCreditCardLinkedRewardDetailsResponse_Details) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{25, 0}
}

func (m *GetCreditCardLinkedRewardDetailsResponse_Details) GetDetails() isGetCreditCardLinkedRewardDetailsResponse_Details_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *GetCreditCardLinkedRewardDetailsResponse_Details) GetRewardDetails() *GetCreditCardLinkedRewardDetailsResponse_RewardDetails {
	if x, ok := x.GetDetails().(*GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails); ok {
		return x.RewardDetails
	}
	return nil
}

func (x *GetCreditCardLinkedRewardDetailsResponse_Details) GetRewardClawbackDetails() *GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails {
	if x, ok := x.GetDetails().(*GetCreditCardLinkedRewardDetailsResponse_Details_RewardClawbackDetails); ok {
		return x.RewardClawbackDetails
	}
	return nil
}

type isGetCreditCardLinkedRewardDetailsResponse_Details_Details interface {
	isGetCreditCardLinkedRewardDetailsResponse_Details_Details()
}

type GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails struct {
	RewardDetails *GetCreditCardLinkedRewardDetailsResponse_RewardDetails `protobuf:"bytes,1,opt,name=reward_details,json=rewardDetails,proto3,oneof"`
}

type GetCreditCardLinkedRewardDetailsResponse_Details_RewardClawbackDetails struct {
	RewardClawbackDetails *GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails `protobuf:"bytes,2,opt,name=reward_clawback_details,json=rewardClawbackDetails,proto3,oneof"`
}

func (*GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails) isGetCreditCardLinkedRewardDetailsResponse_Details_Details() {
}

func (*GetCreditCardLinkedRewardDetailsResponse_Details_RewardClawbackDetails) isGetCreditCardLinkedRewardDetailsResponse_Details_Details() {
}

type GetCreditCardLinkedRewardDetailsResponse_RewardDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardUnits       float32         `protobuf:"fixed32,1,opt,name=reward_units,json=rewardUnits,proto3" json:"reward_units,omitempty"`
	RewardType        RewardType      `protobuf:"varint,2,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	Status            RewardStatus    `protobuf:"varint,3,opt,name=status,proto3,enum=rewards.RewardStatus" json:"status,omitempty"`
	RefId             string          `protobuf:"bytes,4,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
	RewardMetadata    *RewardMetadata `protobuf:"bytes,6,opt,name=reward_metadata,json=rewardMetadata,proto3" json:"reward_metadata,omitempty"`
	IsProjectedReward bool            `protobuf:"varint,7,opt,name=is_projected_reward,json=isProjectedReward,proto3" json:"is_projected_reward,omitempty"`
}

func (x *GetCreditCardLinkedRewardDetailsResponse_RewardDetails) Reset() {
	*x = GetCreditCardLinkedRewardDetailsResponse_RewardDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardLinkedRewardDetailsResponse_RewardDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardLinkedRewardDetailsResponse_RewardDetails) ProtoMessage() {}

func (x *GetCreditCardLinkedRewardDetailsResponse_RewardDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardLinkedRewardDetailsResponse_RewardDetails.ProtoReflect.Descriptor instead.
func (*GetCreditCardLinkedRewardDetailsResponse_RewardDetails) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{25, 1}
}

func (x *GetCreditCardLinkedRewardDetailsResponse_RewardDetails) GetRewardUnits() float32 {
	if x != nil {
		return x.RewardUnits
	}
	return 0
}

func (x *GetCreditCardLinkedRewardDetailsResponse_RewardDetails) GetRewardType() RewardType {
	if x != nil {
		return x.RewardType
	}
	return RewardType_REWARD_TYPE_UNSPECIFIED
}

func (x *GetCreditCardLinkedRewardDetailsResponse_RewardDetails) GetStatus() RewardStatus {
	if x != nil {
		return x.Status
	}
	return RewardStatus_CREATED
}

func (x *GetCreditCardLinkedRewardDetailsResponse_RewardDetails) GetRefId() string {
	if x != nil {
		return x.RefId
	}
	return ""
}

func (x *GetCreditCardLinkedRewardDetailsResponse_RewardDetails) GetRewardMetadata() *RewardMetadata {
	if x != nil {
		return x.RewardMetadata
	}
	return nil
}

func (x *GetCreditCardLinkedRewardDetailsResponse_RewardDetails) GetIsProjectedReward() bool {
	if x != nil {
		return x.IsProjectedReward
	}
	return false
}

type GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClawedBackRewardUnits float32 `protobuf:"fixed32,1,opt,name=clawed_back_reward_units,json=clawedBackRewardUnits,proto3" json:"clawed_back_reward_units,omitempty"`
	// reward type of the original reward which is clawed back.
	RewardType     RewardType     `protobuf:"varint,2,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	ClawbackStatus ClawbackStatus `protobuf:"varint,3,opt,name=clawback_status,json=clawbackStatus,proto3,enum=rewards.ClawbackStatus" json:"clawback_status,omitempty"`
	ClawbackRefId  string         `protobuf:"bytes,4,opt,name=clawback_ref_id,json=clawbackRefId,proto3" json:"clawback_ref_id,omitempty"`
}

func (x *GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails) Reset() {
	*x = GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails) ProtoMessage() {}

func (x *GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails.ProtoReflect.Descriptor instead.
func (*GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{25, 2}
}

func (x *GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails) GetClawedBackRewardUnits() float32 {
	if x != nil {
		return x.ClawedBackRewardUnits
	}
	return 0
}

func (x *GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails) GetRewardType() RewardType {
	if x != nil {
		return x.RewardType
	}
	return RewardType_REWARD_TYPE_UNSPECIFIED
}

func (x *GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails) GetClawbackStatus() ClawbackStatus {
	if x != nil {
		return x.ClawbackStatus
	}
	return ClawbackStatus_CLAWBACK_STATUS_UNSPECIFIED
}

func (x *GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails) GetClawbackRefId() string {
	if x != nil {
		return x.ClawbackRefId
	}
	return ""
}

// merchant_reward_aggregate contains aggregate 1x rewards given for a particular merchant
type GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MerchantName string `protobuf:"bytes,1,opt,name=merchant_name,json=merchantName,proto3" json:"merchant_name,omitempty"`
	// net reward units given for the merchant
	NetFiCoins uint32 `protobuf:"varint,3,opt,name=net_fi_coins,json=netFiCoins,proto3" json:"net_fi_coins,omitempty"`
}

func (x *GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate) Reset() {
	*x = GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate) ProtoMessage() {}

func (x *GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate.ProtoReflect.Descriptor instead.
func (*GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{27, 0}
}

func (x *GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate) GetMerchantName() string {
	if x != nil {
		return x.MerchantName
	}
	return ""
}

func (x *GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate) GetNetFiCoins() uint32 {
	if x != nil {
		return x.NetFiCoins
	}
	return 0
}

type GetRewardsCountRequest_Filters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// only those rewards that satisfy all the conditions will be included in count
	AndFilter *GetRewardsCountRequest_FilterFields `protobuf:"bytes,1,opt,name=and_filter,json=andFilter,proto3" json:"and_filter,omitempty"`
	// those rewards that satisfy any of the conditions will be included in count
	OrFilter *GetRewardsCountRequest_FilterFields `protobuf:"bytes,2,opt,name=or_filter,json=orFilter,proto3" json:"or_filter,omitempty"`
}

func (x *GetRewardsCountRequest_Filters) Reset() {
	*x = GetRewardsCountRequest_Filters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardsCountRequest_Filters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardsCountRequest_Filters) ProtoMessage() {}

func (x *GetRewardsCountRequest_Filters) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardsCountRequest_Filters.ProtoReflect.Descriptor instead.
func (*GetRewardsCountRequest_Filters) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{29, 0}
}

func (x *GetRewardsCountRequest_Filters) GetAndFilter() *GetRewardsCountRequest_FilterFields {
	if x != nil {
		return x.AndFilter
	}
	return nil
}

func (x *GetRewardsCountRequest_Filters) GetOrFilter() *GetRewardsCountRequest_FilterFields {
	if x != nil {
		return x.OrFilter
	}
	return nil
}

type GetRewardsCountRequest_FilterFields struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reward count will be filtered [from_time,till_time), i.e., including from_time but not till_time
	FromTime         *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=from_time,json=fromTime,proto3" json:"from_time,omitempty"`
	TillTime         *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=till_time,json=tillTime,proto3" json:"till_time,omitempty"`
	RewardOfferTypes []RewardOfferType      `protobuf:"varint,3,rep,packed,name=reward_offer_types,json=rewardOfferTypes,proto3,enum=rewards.RewardOfferType" json:"reward_offer_types,omitempty"`
	RewardTypes      []RewardType           `protobuf:"varint,4,rep,packed,name=reward_types,json=rewardTypes,proto3,enum=rewards.RewardType" json:"reward_types,omitempty"`
}

func (x *GetRewardsCountRequest_FilterFields) Reset() {
	*x = GetRewardsCountRequest_FilterFields{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardsCountRequest_FilterFields) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardsCountRequest_FilterFields) ProtoMessage() {}

func (x *GetRewardsCountRequest_FilterFields) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardsCountRequest_FilterFields.ProtoReflect.Descriptor instead.
func (*GetRewardsCountRequest_FilterFields) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{29, 1}
}

func (x *GetRewardsCountRequest_FilterFields) GetFromTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTime
	}
	return nil
}

func (x *GetRewardsCountRequest_FilterFields) GetTillTime() *timestamppb.Timestamp {
	if x != nil {
		return x.TillTime
	}
	return nil
}

func (x *GetRewardsCountRequest_FilterFields) GetRewardOfferTypes() []RewardOfferType {
	if x != nil {
		return x.RewardOfferTypes
	}
	return nil
}

func (x *GetRewardsCountRequest_FilterFields) GetRewardTypes() []RewardType {
	if x != nil {
		return x.RewardTypes
	}
	return nil
}

type GetRewardUtilisationForActorAndOfferRequest_Filters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// any date of the month in which units utilization is to be checked
	// if this is given then units utilization will be fetched for month, else it will fetch all reward units generated while reward offer was active
	// rewards count will always be fetched for reward offer period
	TimestampOfMonth *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=timestamp_of_month,json=timestampOfMonth,proto3" json:"timestamp_of_month,omitempty"`
}

func (x *GetRewardUtilisationForActorAndOfferRequest_Filters) Reset() {
	*x = GetRewardUtilisationForActorAndOfferRequest_Filters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardUtilisationForActorAndOfferRequest_Filters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardUtilisationForActorAndOfferRequest_Filters) ProtoMessage() {}

func (x *GetRewardUtilisationForActorAndOfferRequest_Filters) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardUtilisationForActorAndOfferRequest_Filters.ProtoReflect.Descriptor instead.
func (*GetRewardUtilisationForActorAndOfferRequest_Filters) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{33, 0}
}

func (x *GetRewardUtilisationForActorAndOfferRequest_Filters) GetTimestampOfMonth() *timestamppb.Timestamp {
	if x != nil {
		return x.TimestampOfMonth
	}
	return nil
}

// contains utilization data only if capping is applicable for the particular reward offer
// if no capping is applicable for reward offer then utilization data will not be populated
type GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FiCoinsUnits uint32 `protobuf:"varint,2,opt,name=fi_coins_units,json=fiCoinsUnits,proto3" json:"fi_coins_units,omitempty"`
	CashUnits    uint32 `protobuf:"varint,3,opt,name=cash_units,json=cashUnits,proto3" json:"cash_units,omitempty"`
	SdCashUnits  uint32 `protobuf:"varint,4,opt,name=sd_cash_units,json=sdCashUnits,proto3" json:"sd_cash_units,omitempty"`
}

func (x *GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor) Reset() {
	*x = GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor) ProtoMessage() {
}

func (x *GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor.ProtoReflect.Descriptor instead.
func (*GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{34, 0}
}

func (x *GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor) GetFiCoinsUnits() uint32 {
	if x != nil {
		return x.FiCoinsUnits
	}
	return 0
}

func (x *GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor) GetCashUnits() uint32 {
	if x != nil {
		return x.CashUnits
	}
	return 0
}

func (x *GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor) GetSdCashUnits() uint32 {
	if x != nil {
		return x.SdCashUnits
	}
	return 0
}

type GetAllRewardsAndProjectionResponse_RewardEntities struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardEntities []*GetAllRewardsAndProjectionResponse_RewardEntity `protobuf:"bytes,1,rep,name=reward_entities,json=rewardEntities,proto3" json:"reward_entities,omitempty"`
}

func (x *GetAllRewardsAndProjectionResponse_RewardEntities) Reset() {
	*x = GetAllRewardsAndProjectionResponse_RewardEntities{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllRewardsAndProjectionResponse_RewardEntities) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllRewardsAndProjectionResponse_RewardEntities) ProtoMessage() {}

func (x *GetAllRewardsAndProjectionResponse_RewardEntities) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllRewardsAndProjectionResponse_RewardEntities.ProtoReflect.Descriptor instead.
func (*GetAllRewardsAndProjectionResponse_RewardEntities) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{40, 1}
}

func (x *GetAllRewardsAndProjectionResponse_RewardEntities) GetRewardEntities() []*GetAllRewardsAndProjectionResponse_RewardEntity {
	if x != nil {
		return x.RewardEntities
	}
	return nil
}

// details of the reward/projection
type GetAllRewardsAndProjectionResponse_RewardEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// entity ID (reward ID or projection ID)
	EntityId string `protobuf:"bytes,1,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	// details of the options that are part of the reward/projection
	RewardOptions []*RewardOptionMinimal `protobuf:"bytes,2,rep,name=reward_options,json=rewardOptions,proto3" json:"reward_options,omitempty"`
	// type of offer that generated the reward/projection
	OfferType RewardOfferType `protobuf:"varint,3,opt,name=offer_type,json=offerType,proto3,enum=rewards.RewardOfferType" json:"offer_type,omitempty"`
	// id of the offer that generated this reward entity
	OfferId string `protobuf:"bytes,4,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	// type of reward entity, i.e. actual reward, projected reward, or actualised part of projected reward
	RewardEntityType GetAllRewardsAndProjectionResponse_RewardEntityType `protobuf:"varint,5,opt,name=reward_entity_type,json=rewardEntityType,proto3,enum=rewards.GetAllRewardsAndProjectionResponse_RewardEntityType" json:"reward_entity_type,omitempty"`
}

func (x *GetAllRewardsAndProjectionResponse_RewardEntity) Reset() {
	*x = GetAllRewardsAndProjectionResponse_RewardEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllRewardsAndProjectionResponse_RewardEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllRewardsAndProjectionResponse_RewardEntity) ProtoMessage() {}

func (x *GetAllRewardsAndProjectionResponse_RewardEntity) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllRewardsAndProjectionResponse_RewardEntity.ProtoReflect.Descriptor instead.
func (*GetAllRewardsAndProjectionResponse_RewardEntity) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{40, 2}
}

func (x *GetAllRewardsAndProjectionResponse_RewardEntity) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

func (x *GetAllRewardsAndProjectionResponse_RewardEntity) GetRewardOptions() []*RewardOptionMinimal {
	if x != nil {
		return x.RewardOptions
	}
	return nil
}

func (x *GetAllRewardsAndProjectionResponse_RewardEntity) GetOfferType() RewardOfferType {
	if x != nil {
		return x.OfferType
	}
	return RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE
}

func (x *GetAllRewardsAndProjectionResponse_RewardEntity) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *GetAllRewardsAndProjectionResponse_RewardEntity) GetRewardEntityType() GetAllRewardsAndProjectionResponse_RewardEntityType {
	if x != nil {
		return x.RewardEntityType
	}
	return GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_UNSPECIFIED
}

// filter to use while fetching rewards for calculating summary.
type GetRewardAggregatesRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// different offer types that generated the rewards
	OfferTypes []RewardOfferType `protobuf:"varint,1,rep,packed,name=offer_types,json=offerTypes,proto3,enum=rewards.RewardOfferType" json:"offer_types,omitempty"`
	// different action types that lead to generation of reward
	ActionTypes []CollectedDataType `protobuf:"varint,5,rep,packed,name=action_types,json=actionTypes,proto3,enum=rewards.CollectedDataType" json:"action_types,omitempty"`
	// list of time ranges in which we want to fetch the aggregates
	TimeWindows []*TimeWindow `protobuf:"bytes,6,rep,name=time_windows,json=timeWindows,proto3" json:"time_windows,omitempty"`
}

func (x *GetRewardAggregatesRequest_Filter) Reset() {
	*x = GetRewardAggregatesRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardAggregatesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardAggregatesRequest_Filter) ProtoMessage() {}

func (x *GetRewardAggregatesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardAggregatesRequest_Filter.ProtoReflect.Descriptor instead.
func (*GetRewardAggregatesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_api_rewards_service_proto_rawDescGZIP(), []int{41, 0}
}

func (x *GetRewardAggregatesRequest_Filter) GetOfferTypes() []RewardOfferType {
	if x != nil {
		return x.OfferTypes
	}
	return nil
}

func (x *GetRewardAggregatesRequest_Filter) GetActionTypes() []CollectedDataType {
	if x != nil {
		return x.ActionTypes
	}
	return nil
}

func (x *GetRewardAggregatesRequest_Filter) GetTimeWindows() []*TimeWindow {
	if x != nil {
		return x.TimeWindows
	}
	return nil
}

var File_api_rewards_service_proto protoreflect.FileDescriptor

var file_api_rewards_service_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6c, 0x61, 0x77, 0x62, 0x61,
	0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x70, 0x63, 0x2f, 0x70, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x48, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x61, 0x70, 0x70, 0x68, 0x65,
	0x6c, 0x70, 0x2f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x65, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xfc, 0x08, 0x0a, 0x17, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x42, 0x79, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x42, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x0c,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x49, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x5f, 0x76, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x42, 0x79,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x56, 0x32, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x56, 0x32, 0x1a, 0x99, 0x01, 0x0a, 0x09, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x56,
	0x32, 0x12, 0x44, 0x0a, 0x09, 0x6f, 0x72, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x42, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x08, 0x6f,
	0x72, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x46, 0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x42, 0x79, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a,
	0xdd, 0x05, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a,
	0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0e, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x11, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f,
	0x72, 0x65, 0x66, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x31, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x65, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0a, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x66, 0x49, 0x64, 0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x1a, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x6c,
	0x61, 0x77, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0c,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6c, 0x61, 0x77, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65,
	0x66, 0x49, 0x64, 0x73, 0x12, 0x31, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x63, 0x6c,
	0x61, 0x69, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x46, 0x0a, 0x12, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x0e, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22,
	0x9e, 0x01, 0x0a, 0x0f, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x29, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x22, 0x37, 0x0a, 0x18, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x42, 0x79, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x22, 0x5e, 0x0a, 0x0e, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x27, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x22, 0x95, 0x01, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64,
	0x73, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65,
	0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x49, 0x64, 0x73, 0x12, 0x44, 0x0a, 0x11, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x64, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x07,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x07,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0xa1, 0x01, 0x0a, 0x13, 0x43, 0x68, 0x6f, 0x6f,
	0x73, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0e, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43,
	0x6c, 0x61, 0x69, 0x6d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x63, 0x6c,
	0x61, 0x69, 0x6d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3b, 0x0a, 0x14, 0x43,
	0x68, 0x6f, 0x6f, 0x73, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xad, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74,
	0x55, 0x6e, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x37, 0x0a, 0x09, 0x75, 0x70, 0x74, 0x6f, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08,
	0x75, 0x70, 0x74, 0x6f, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x7a, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x55,
	0x6e, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x32, 0x0a, 0x15, 0x75, 0x6e, 0x6f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x13, 0x75, 0x6e, 0x6f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0xed, 0x03, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xf5, 0x02, 0x0a,
	0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x48, 0x0a, 0x11, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65,
	0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x49, 0x64, 0x73, 0x12, 0x37, 0x0a, 0x09, 0x66,
	0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x75, 0x70, 0x74, 0x6f, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x08, 0x75, 0x70, 0x74, 0x6f, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3d, 0x0a,
	0x0c, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x46, 0x0a, 0x12,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x10, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x22, 0xaf, 0x07, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x18, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x63, 0x61, 0x73, 0x68, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x65, 0x61, 0x72, 0x6e,
	0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x15, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x43, 0x61, 0x73, 0x68, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x61, 0x72,
	0x6e, 0x65, 0x64, 0x12, 0x49, 0x0a, 0x17, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x64,
	0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x65, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53,
	0x69, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x12, 0x31,
	0x0a, 0x15, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73,
	0x5f, 0x65, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x45, 0x61, 0x72, 0x6e, 0x65,
	0x64, 0x12, 0x65, 0x0a, 0x26, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x5f, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x21, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x73, 0x68, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x63, 0x0a, 0x25, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f,
	0x73, 0x69, 0x64, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x20, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x69,
	0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3e, 0x0a,
	0x1c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x18, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x12, 0x33, 0x0a,
	0x16, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6f, 0x66, 0x5f,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x66, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x12, 0x3c, 0x0a, 0x1b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x17, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x4f, 0x66, 0x43, 0x61, 0x73, 0x68, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x12, 0x3a, 0x0a, 0x1a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x6f, 0x66, 0x5f, 0x73, 0x69, 0x64, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x4f, 0x66, 0x53, 0x69, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x41, 0x0a, 0x1e,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x66,
	0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x19, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x4f, 0x66, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12,
	0x58, 0x0a, 0x1f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x5f,
	0x63, 0x61, 0x73, 0x68, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x1b, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x43, 0x61, 0x73, 0x68, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x23, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69,
	0x6e, 0x73, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x1e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4c, 0x6f, 0x63,
	0x6b, 0x65, 0x64, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x3b, 0x0a, 0x1c, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x22, 0x44, 0x0a, 0x1d, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x91, 0x01, 0x0a, 0x24, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x43, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x4e, 0x0a,
	0x15, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x73, 0x5f, 0x75, 0x6e, 0x6c, 0x6f, 0x63,
	0x6b, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x13, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x61, 0x6c, 0x73, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x65, 0x22, 0x9e, 0x02,
	0x0a, 0x25, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x43, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0e,
	0x69, 0x73, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x68, 0x69, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x4d, 0x61, 0x78, 0x43, 0x61, 0x70, 0x48, 0x69,
	0x74, 0x12, 0x47, 0x0a, 0x12, 0x6d, 0x61, 0x78, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x72, 0x65, 0x73,
	0x65, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x6d, 0x61, 0x78, 0x43, 0x61,
	0x70, 0x52, 0x65, 0x73, 0x65, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x49, 0x0a, 0x13, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x43, 0x61, 0x70, 0x52, 0x65, 0x73, 0x65,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x63, 0x61, 0x70,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x43, 0x61, 0x70, 0x22, 0x28,
	0x0a, 0x26, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x43, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xb1, 0x01, 0x0a, 0x27, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43,
	0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x5f, 0x63, 0x61, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x61, 0x70, 0x12, 0x40, 0x0a, 0x0e, 0x63, 0x61,
	0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x63,
	0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0x40, 0x0a, 0x23,
	0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x83,
	0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x61,
	0x62, 0x6c, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x0a, 0x17,
	0x63, 0x6c, 0x61, 0x69, 0x6d, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x15, 0x63,
	0x6c, 0x61, 0x69, 0x6d, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x76, 0x0a, 0x28, 0x42, 0x75, 0x6c, 0x6b, 0x43, 0x6c, 0x61, 0x69,
	0x6d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x6d,
	0x61, 0x78, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x63, 0x6c,
	0x61, 0x69, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x6d, 0x61, 0x78, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x54, 0x6f, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x22, 0x50, 0x0a, 0x29,
	0x42, 0x75, 0x6c, 0x6b, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xe0,
	0x01, 0x0a, 0x2a, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c,
	0x47, 0x69, 0x76, 0x65, 0x61, 0x77, 0x61, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x46, 0x6f,
	0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x0d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0xb0, 0x01, 0x01, 0x52, 0x0f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x52, 0x0a, 0x2b, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x61, 0x6e, 0x75,
	0x61, 0x6c, 0x47, 0x69, 0x76, 0x65, 0x61, 0x77, 0x61, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x9c, 0x01, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x21, 0x0a, 0x07, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x06, 0x72, 0x65,
	0x66, 0x49, 0x64, 0x73, 0x12, 0x4e, 0x0a, 0x11, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x20, 0x00, 0x52, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x22, 0xc8, 0x07, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5c, 0x0a, 0x0c, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x1a, 0x82, 0x02, 0x0a, 0x07, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x68, 0x0a, 0x0e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0d, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x81, 0x01, 0x0a, 0x17, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6c, 0x61, 0x77, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6c, 0x61, 0x77, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x15, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43,
	0x6c, 0x61, 0x77, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x09,
	0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0xa0, 0x02, 0x0a, 0x0d, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x34,
	0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2e, 0x0a, 0x13,
	0x69, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x73, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x1a, 0xf0, 0x01, 0x0a,
	0x15, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6c, 0x61, 0x77, 0x62, 0x61, 0x63, 0x6b, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x63, 0x6c, 0x61, 0x77, 0x65, 0x64,
	0x5f, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15, 0x63, 0x6c, 0x61, 0x77, 0x65, 0x64,
	0x42, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12,
	0x34, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x0f, 0x63, 0x6c, 0x61, 0x77, 0x62, 0x61, 0x63,
	0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6c, 0x61, 0x77, 0x62, 0x61, 0x63,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x63, 0x6c, 0x61, 0x77, 0x62, 0x61, 0x63,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6c, 0x61, 0x77, 0x62,
	0x61, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x63, 0x6c, 0x61, 0x77, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x66, 0x49, 0x64, 0x22,
	0x9b, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x31, 0x58, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x16, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3e, 0x0a,
	0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x22, 0x9d, 0x03,
	0x0a, 0x25, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x31,
	0x58, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x31, 0x0a, 0x15,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x31, 0x78, 0x5f, 0x66, 0x69, 0x5f,
	0x63, 0x6f, 0x69, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x12, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x31, 0x78, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x12,
	0x33, 0x0a, 0x16, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x31, 0x78,
	0x5f, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x13, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x31, 0x78, 0x46, 0x69, 0x43,
	0x6f, 0x69, 0x6e, 0x73, 0x12, 0x84, 0x01, 0x0a, 0x1a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61,
	0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x31, 0x58, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x52, 0x18, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x1a, 0x60, 0x0a, 0x17, 0x4d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x6e,
	0x65, 0x74, 0x5f, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x6e, 0x65, 0x74, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x22, 0x7e, 0x0a,
	0x0a, 0x54, 0x69, 0x6d, 0x65, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x37, 0x0a, 0x09, 0x66,
	0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x74, 0x69, 0x6c, 0x6c, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x08, 0x74, 0x69, 0x6c, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xa8, 0x04,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x41,
	0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x1a, 0xa1, 0x01, 0x0a, 0x07, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x4b, 0x0a,
	0x0a, 0x61, 0x6e, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x52,
	0x09, 0x61, 0x6e, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x09, 0x6f, 0x72,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x52, 0x08, 0x6f, 0x72, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x80, 0x02, 0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x37, 0x0a, 0x09, 0x74, 0x69, 0x6c, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08,
	0x74, 0x69, 0x6c, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x12, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x36, 0x0a, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x63, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xac, 0x01,
	0x0a, 0x37, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73,
	0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x41, 0x6e, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x4d, 0x6f, 0x6e,
	0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x22, 0xbb, 0x02, 0x0a,
	0x38, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x55,
	0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x41, 0x6e, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0xa7,
	0x01, 0x0a, 0x33, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x74, 0x69, 0x6c, 0x69,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x2d, 0x6d, 0x6f, 0x6e, 0x74, 0x68,
	0x6c, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x74, 0x69,
	0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x6d, 0x6f, 0x6e, 0x74,
	0x68, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x12, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x9d, 0x02, 0x0a, 0x2b, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x6e, 0x64, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x56, 0x0a,
	0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x6e, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x1a, 0x53, 0x0a, 0x07, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x12, 0x48, 0x0a, 0x12, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6f, 0x66,
	0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x4f, 0x66, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x22, 0xba, 0x04, 0x0a, 0x2c, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x6e, 0x64, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x58, 0x0a, 0x2a, 0x69, 0x73, 0x5f, 0x63, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x6e,
	0x6f, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x6f,
	0x72, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x24, 0x69, 0x73, 0x43, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x4e,
	0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x12, 0xc3, 0x01, 0x0a, 0x2f, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x5f, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x6e, 0x64, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69,
	0x74, 0x73, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x29, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x55, 0x74, 0x69,
	0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x12, 0x2e, 0x0a, 0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x1a, 0x94, 0x01, 0x0a, 0x29, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x55, 0x74, 0x69, 0x6c, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x24,
	0x0a, 0x0e, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x66, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x55,
	0x6e, 0x69, 0x74, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x75, 0x6e, 0x69,
	0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x63, 0x61, 0x73, 0x68, 0x55, 0x6e,
	0x69, 0x74, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x64, 0x5f, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x73, 0x64, 0x43, 0x61,
	0x73, 0x68, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x22, 0x47, 0x0a, 0x2a, 0x42, 0x75, 0x6c, 0x6b, 0x43,
	0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x57, 0x69, 0x74, 0x68, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x32, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x22, 0x52, 0x0a, 0x2b, 0x42, 0x75, 0x6c, 0x6b, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x46, 0x0a, 0x29, 0x47, 0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x43,
	0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x81, 0x01, 0x0a,
	0x2a, 0x47, 0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x2e, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69,
	0x73, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x22, 0xb5, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x41, 0x6e, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x04, 0x18, 0x64, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x0b,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1a, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x07, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x01, 0x10, 0x1e,
	0x52, 0x06, 0x72, 0x65, 0x66, 0x49, 0x64, 0x73, 0x22, 0xc7, 0x07, 0x0a, 0x22, 0x47, 0x65, 0x74,
	0x41, 0x6c, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x6e, 0x64, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x8a, 0x01, 0x0a, 0x1d, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x5f,
	0x74, 0x6f, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69,
	0x65, 0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x41, 0x6e, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x66, 0x49, 0x64, 0x54,
	0x6f, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x18, 0x72, 0x65, 0x66, 0x49, 0x64, 0x54, 0x6f,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x4d, 0x61,
	0x70, 0x1a, 0x87, 0x01, 0x0a, 0x1d, 0x52, 0x65, 0x66, 0x49, 0x64, 0x54, 0x6f, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x50, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x6e, 0x64, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x73, 0x0a, 0x0e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x61, 0x0a,
	0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x6e,
	0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x52, 0x0e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x1a, 0xb0, 0x02, 0x0a, 0x0c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x43,
	0x0a, 0x0e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x69, 0x6e,
	0x69, 0x6d, 0x61, 0x6c, 0x52, 0x0d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x37, 0x0a, 0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x09, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x6a, 0x0a, 0x12, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x3c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x6e, 0x64, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x10, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x22, 0xbc, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x52, 0x45, 0x57, 0x41,
	0x52, 0x44, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x27, 0x0a, 0x23,
	0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x57,
	0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f,
	0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x4a,
	0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x10, 0x02, 0x12, 0x32,
	0x0a, 0x2e, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x55, 0x41, 0x4c, 0x49, 0x53, 0x45, 0x44, 0x5f,
	0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44,
	0x10, 0x03, 0x22, 0xb8, 0x02, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x1a, 0xba, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x0b, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x36, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77,
	0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73, 0x22, 0x80, 0x01,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x3c, 0x0a, 0x0a, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x69, 0x6e,
	0x69, 0x6d, 0x61, 0x6c, 0x52, 0x0a, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73,
	0x22, 0xde, 0x04, 0x0a, 0x2f, 0x47, 0x65, 0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65,
	0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x49, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18,
	0x64, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x68, 0x0a, 0x09, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x49, 0x6e, 0x54, 0x69, 0x6d,
	0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x54, 0x69, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x69, 0x65, 0x72,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x54, 0x69, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x77, 0x69, 0x6e,
	0x64, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x52, 0x0a,
	0x74, 0x69, 0x6d, 0x65, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x22, 0xe4, 0x02, 0x0a, 0x08, 0x55,
	0x73, 0x65, 0x72, 0x54, 0x69, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x15, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x54, 0x49, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f,
	0x46, 0x49, 0x5f, 0x42, 0x41, 0x53, 0x49, 0x43, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x50, 0x4c, 0x55, 0x53, 0x10,
	0x02, 0x12, 0x19, 0x0a, 0x15, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46,
	0x49, 0x5f, 0x49, 0x4e, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x53, 0x41, 0x4c,
	0x41, 0x52, 0x59, 0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x54, 0x49,
	0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x4c, 0x49, 0x54,
	0x45, 0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x54, 0x49, 0x45, 0x52,
	0x5f, 0x46, 0x49, 0x5f, 0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x10, 0x06, 0x12,
	0x21, 0x0a, 0x1d, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f,
	0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x41, 0x4e, 0x44, 0x5f, 0x31,
	0x10, 0x07, 0x12, 0x21, 0x0a, 0x1d, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f,
	0x46, 0x49, 0x5f, 0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x41, 0x4e,
	0x44, 0x5f, 0x32, 0x10, 0x08, 0x12, 0x21, 0x0a, 0x1d, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x54, 0x49,
	0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f,
	0x42, 0x41, 0x4e, 0x44, 0x5f, 0x33, 0x10, 0x09, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x52, 0x45, 0x47, 0x55, 0x4c, 0x41, 0x52,
	0x10, 0x0a, 0x12, 0x1d, 0x0a, 0x19, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f,
	0x46, 0x49, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x41, 0x53, 0x49, 0x43, 0x10,
	0x0b, 0x22, 0xb5, 0x01, 0x0a, 0x30, 0x47, 0x65, 0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74,
	0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x49, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x0a, 0x16, 0x61,
	0x6e, 0x63, 0x68, 0x6f, 0x72, 0x5f, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x5f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x13, 0x61, 0x6e, 0x63,
	0x68, 0x6f, 0x72, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x12, 0x27, 0x0a, 0x0f, 0x63, 0x61, 0x73, 0x68, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x63, 0x61, 0x73, 0x68, 0x62,
	0x61, 0x63, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x22, 0x40, 0x0a, 0x21, 0x46, 0x6f, 0x72,
	0x63, 0x65, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x22, 0x49, 0x0a, 0x22, 0x46,
	0x6f, 0x72, 0x63, 0x65, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2a, 0x4a, 0x0a, 0x0e, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x56, 0x49, 0x53, 0x49,
	0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x56, 0x49, 0x53,
	0x49, 0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x48, 0x49, 0x44, 0x44, 0x45, 0x4e,
	0x10, 0x02, 0x32, 0x96, 0x17, 0x0a, 0x10, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x47, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x53, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x42, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x20,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x42, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x54, 0x0a, 0x14,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x42, 0x79, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x49, 0x64, 0x12, 0x21, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x42, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x47, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x12, 0x1a, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4d, 0x0a, 0x0c, 0x43,
	0x68, 0x6f, 0x6f, 0x73, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x1c, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6e, 0x0a, 0x17, 0x47, 0x65,
	0x74, 0x55, 0x6e, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x55, 0x6e, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x6e, 0x4f, 0x70,
	0x65, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x20,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x21, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x68, 0x0a, 0x15, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x12, 0x25,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x80, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x2d, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x61,
	0x70, 0x70, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2e, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x61, 0x70,
	0x70, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x61, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2f, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x43, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x43, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x8f, 0x01, 0x0a, 0x21,
	0x42, 0x75, 0x6c, 0x6b, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x31, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x42, 0x75, 0x6c, 0x6b,
	0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x57, 0x69, 0x74, 0x68,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x42,
	0x75, 0x6c, 0x6b, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x57,
	0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x7d, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x61, 0x62, 0x6c,
	0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x43,
	0x6c, 0x61, 0x69, 0x6d, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x6c, 0x61,
	0x69, 0x6d, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x90, 0x01, 0x0a,
	0x23, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x47, 0x69,
	0x76, 0x65, 0x61, 0x77, 0x61, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x46, 0x6f, 0x72, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x12, 0x33, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x54,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x47, 0x69, 0x76, 0x65,
	0x61, 0x77, 0x61, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x61, 0x6e, 0x75, 0x61,
	0x6c, 0x47, 0x69, 0x76, 0x65, 0x61, 0x77, 0x61, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x46,
	0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x87, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x30, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6e, 0x6b,
	0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69,
	0x6e, 0x6b, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x1d, 0x47, 0x65, 0x74,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x31, 0x58, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x2d, 0x2e, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x31, 0x58, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x31, 0x58, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0xb7, 0x01, 0x0a, 0x30, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69,
	0x74, 0x73, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x6e, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x12, 0x40, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x55, 0x74, 0x69,
	0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x41, 0x6e, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x55,
	0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x41, 0x6e, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x24, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x6e, 0x64, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x12, 0x34, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x6e, 0x64, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x74, 0x69, 0x6c,
	0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41,
	0x6e, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x90, 0x01, 0x0a, 0x23, 0x42, 0x75, 0x6c, 0x6b, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x32, 0x12, 0x33, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x43, 0x6c, 0x61, 0x69, 0x6d,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x8d, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x43, 0x6c,
	0x61, 0x69, 0x6d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x32, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x43, 0x6c, 0x61, 0x69, 0x6d,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x43,
	0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x76, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x41, 0x6e, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x2a, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x6c, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x6e, 0x64, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x6e, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x60, 0x0a, 0x13, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65,
	0x73, 0x12, 0x23, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9f, 0x01, 0x0a,
	0x28, 0x47, 0x65, 0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x49, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x54, 0x69, 0x65, 0x72, 0x12, 0x38, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x49, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65,
	0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x49, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46,
	0x6f, 0x72, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75,
	0x0a, 0x1a, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x12, 0x2a, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x52, 0x65, 0x74, 0x72,
	0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x18, 0x49, 0x73, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x12, 0x3a, 0x2e, 0x69, 0x6e, 0x61, 0x70, 0x70, 0x68, 0x65, 0x6c, 0x70, 0x2e, 0x66,
	0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x49,
	0x73, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f,
	0x72, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b,
	0x2e, 0x69, 0x6e, 0x61, 0x70, 0x70, 0x68, 0x65, 0x6c, 0x70, 0x2e, 0x66, 0x65, 0x65, 0x64, 0x62,
	0x61, 0x63, 0x6b, 0x5f, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x49, 0x73, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x48, 0x0a, 0x22, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x5a, 0x22, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_rewards_service_proto_rawDescOnce sync.Once
	file_api_rewards_service_proto_rawDescData = file_api_rewards_service_proto_rawDesc
)

func file_api_rewards_service_proto_rawDescGZIP() []byte {
	file_api_rewards_service_proto_rawDescOnce.Do(func() {
		file_api_rewards_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_rewards_service_proto_rawDescData)
	})
	return file_api_rewards_service_proto_rawDescData
}

var file_api_rewards_service_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_rewards_service_proto_msgTypes = make([]protoimpl.MessageInfo, 62)
var file_api_rewards_service_proto_goTypes = []interface{}{
	(VisibilityType)(0), // 0: rewards.VisibilityType
	(GetAllRewardsAndProjectionResponse_RewardEntityType)(0),                                       // 1: rewards.GetAllRewardsAndProjectionResponse.RewardEntityType
	(GetEstimatedRewardsInTimeDurationForTierRequest_UserTier)(0),                                  // 2: rewards.GetEstimatedRewardsInTimeDurationForTierRequest.UserTier
	(*RewardsByActorIdRequest)(nil),                                                                // 3: rewards.RewardsByActorIdRequest
	(*RewardsResponse)(nil),                                                                        // 4: rewards.RewardsResponse
	(*RewardsByRewardIdRequest)(nil),                                                               // 5: rewards.RewardsByRewardIdRequest
	(*RewardResponse)(nil),                                                                         // 6: rewards.RewardResponse
	(*GetRewardsRequest)(nil),                                                                      // 7: rewards.GetRewardsRequest
	(*GetRewardsResponse)(nil),                                                                     // 8: rewards.GetRewardsResponse
	(*ChooseRewardRequest)(nil),                                                                    // 9: rewards.ChooseRewardRequest
	(*ChooseRewardResponse)(nil),                                                                   // 10: rewards.ChooseRewardResponse
	(*GetUnOpenedRewardsCountRequest)(nil),                                                         // 11: rewards.GetUnOpenedRewardsCountRequest
	(*GetUnOpenedRewardsCountResponse)(nil),                                                        // 12: rewards.GetUnOpenedRewardsCountResponse
	(*GetRewardSummaryRequest)(nil),                                                                // 13: rewards.GetRewardSummaryRequest
	(*GetRewardSummaryResponse)(nil),                                                               // 14: rewards.GetRewardSummaryResponse
	(*RetryRewardProcessingRequest)(nil),                                                           // 15: rewards.RetryRewardProcessingRequest
	(*RetryRewardProcessingResponse)(nil),                                                          // 16: rewards.RetryRewardProcessingResponse
	(*GetReferralRewardsCappingInfoRequest)(nil),                                                   // 17: rewards.GetReferralRewardsCappingInfoRequest
	(*GetReferralRewardsCappingInfoResponse)(nil),                                                  // 18: rewards.GetReferralRewardsCappingInfoResponse
	(*GetReferralRewardsCappingConfigRequest)(nil),                                                 // 19: rewards.GetReferralRewardsCappingConfigRequest
	(*GetReferralRewardsCappingConfigResponse)(nil),                                                // 20: rewards.GetReferralRewardsCappingConfigResponse
	(*GetAutoClaimableRewardsCountRequest)(nil),                                                    // 21: rewards.GetAutoClaimableRewardsCountRequest
	(*GetAutoClaimableRewardsCountResponse)(nil),                                                   // 22: rewards.GetAutoClaimableRewardsCountResponse
	(*BulkClaimRewardsWithDefaultOptionRequest)(nil),                                               // 23: rewards.BulkClaimRewardsWithDefaultOptionRequest
	(*BulkClaimRewardsWithDefaultOptionResponse)(nil),                                              // 24: rewards.BulkClaimRewardsWithDefaultOptionResponse
	(*TriggerManualGiveawayRewardForActorRequest)(nil),                                             // 25: rewards.TriggerManualGiveawayRewardForActorRequest
	(*TriggerManualGiveawayRewardForActorResponse)(nil),                                            // 26: rewards.TriggerManualGiveawayRewardForActorResponse
	(*GetCreditCardLinkedRewardDetailsRequest)(nil),                                                // 27: rewards.GetCreditCardLinkedRewardDetailsRequest
	(*GetCreditCardLinkedRewardDetailsResponse)(nil),                                               // 28: rewards.GetCreditCardLinkedRewardDetailsResponse
	(*GetCreditCard1XRewardsSummaryRequest)(nil),                                                   // 29: rewards.GetCreditCard1XRewardsSummaryRequest
	(*GetCreditCard1XRewardsSummaryResponse)(nil),                                                  // 30: rewards.GetCreditCard1XRewardsSummaryResponse
	(*TimeWindow)(nil),                                                                             // 31: rewards.TimeWindow
	(*GetRewardsCountRequest)(nil),                                                                 // 32: rewards.GetRewardsCountRequest
	(*GetRewardsCountResponse)(nil),                                                                // 33: rewards.GetRewardsCountResponse
	(*GetRewardUnitsUtilisationForActorAndOfferInMonthRequest)(nil),                                // 34: rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthRequest
	(*GetRewardUnitsUtilisationForActorAndOfferInMonthResponse)(nil),                               // 35: rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse
	(*GetRewardUtilisationForActorAndOfferRequest)(nil),                                            // 36: rewards.GetRewardUtilisationForActorAndOfferRequest
	(*GetRewardUtilisationForActorAndOfferResponse)(nil),                                           // 37: rewards.GetRewardUtilisationForActorAndOfferResponse
	(*BulkClaimRewardsWithDefaultOptionV2Request)(nil),                                             // 38: rewards.BulkClaimRewardsWithDefaultOptionV2Request
	(*BulkClaimRewardsWithDefaultOptionV2Response)(nil),                                            // 39: rewards.BulkClaimRewardsWithDefaultOptionV2Response
	(*GetBulkClaimRewardsProcessingStateRequest)(nil),                                              // 40: rewards.GetBulkClaimRewardsProcessingStateRequest
	(*GetBulkClaimRewardsProcessingStateResponse)(nil),                                             // 41: rewards.GetBulkClaimRewardsProcessingStateResponse
	(*GetAllRewardsAndProjectionRequest)(nil),                                                      // 42: rewards.GetAllRewardsAndProjectionRequest
	(*GetAllRewardsAndProjectionResponse)(nil),                                                     // 43: rewards.GetAllRewardsAndProjectionResponse
	(*GetRewardAggregatesRequest)(nil),                                                             // 44: rewards.GetRewardAggregatesRequest
	(*GetRewardAggregatesResponse)(nil),                                                            // 45: rewards.GetRewardAggregatesResponse
	(*GetEstimatedRewardsInTimeDurationForTierRequest)(nil),                                        // 46: rewards.GetEstimatedRewardsInTimeDurationForTierRequest
	(*GetEstimatedRewardsInTimeDurationForTierResponse)(nil),                                       // 47: rewards.GetEstimatedRewardsInTimeDurationForTierResponse
	(*ForceRetryRewardProcessingRequest)(nil),                                                      // 48: rewards.ForceRetryRewardProcessingRequest
	(*ForceRetryRewardProcessingResponse)(nil),                                                     // 49: rewards.ForceRetryRewardProcessingResponse
	(*RewardsByActorIdRequest_FiltersV2)(nil),                                                      // 50: rewards.RewardsByActorIdRequest.FiltersV2
	(*RewardsByActorIdRequest_Filter)(nil),                                                         // 51: rewards.RewardsByActorIdRequest.Filter
	(*GetRewardSummaryRequest_Filter)(nil),                                                         // 52: rewards.GetRewardSummaryRequest.Filter
	(*GetCreditCardLinkedRewardDetailsResponse_Details)(nil),                                       // 53: rewards.GetCreditCardLinkedRewardDetailsResponse.Details
	(*GetCreditCardLinkedRewardDetailsResponse_RewardDetails)(nil),                                 // 54: rewards.GetCreditCardLinkedRewardDetailsResponse.RewardDetails
	(*GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails)(nil),                         // 55: rewards.GetCreditCardLinkedRewardDetailsResponse.RewardClawbackDetails
	(*GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate)(nil),                          // 56: rewards.GetCreditCard1XRewardsSummaryResponse.MerchantRewardAggregate
	(*GetRewardsCountRequest_Filters)(nil),                                                         // 57: rewards.GetRewardsCountRequest.Filters
	(*GetRewardsCountRequest_FilterFields)(nil),                                                    // 58: rewards.GetRewardsCountRequest.FilterFields
	(*GetRewardUtilisationForActorAndOfferRequest_Filters)(nil),                                    // 59: rewards.GetRewardUtilisationForActorAndOfferRequest.Filters
	(*GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor)(nil), // 60: rewards.GetRewardUtilisationForActorAndOfferResponse.RewardOfferRewardUnitsUtilizationForActor
	nil, // 61: rewards.GetAllRewardsAndProjectionResponse.RefIdToRewardEntitiesMapEntry
	(*GetAllRewardsAndProjectionResponse_RewardEntities)(nil),  // 62: rewards.GetAllRewardsAndProjectionResponse.RewardEntities
	(*GetAllRewardsAndProjectionResponse_RewardEntity)(nil),    // 63: rewards.GetAllRewardsAndProjectionResponse.RewardEntity
	(*GetRewardAggregatesRequest_Filter)(nil),                  // 64: rewards.GetRewardAggregatesRequest.Filter
	(*rpc.PageContextRequest)(nil),                             // 65: rpc.PageContextRequest
	(*rpc.Status)(nil),                                         // 66: rpc.Status
	(*rpc.PageContextResponse)(nil),                            // 67: rpc.PageContextResponse
	(*Reward)(nil),                                             // 68: rewards.Reward
	(RewardOfferType)(0),                                       // 69: rewards.RewardOfferType
	(*RewardClaimMetadata)(nil),                                // 70: rewards.RewardClaimMetadata
	(*timestamppb.Timestamp)(nil),                              // 71: google.protobuf.Timestamp
	(*money.Money)(nil),                                        // 72: google.type.Money
	(*durationpb.Duration)(nil),                                // 73: google.protobuf.Duration
	(*RewardOfferRewardUnitsActorUtilisationInTimePeriod)(nil), // 74: rewards.RewardOfferRewardUnitsActorUtilisationInTimePeriod
	(CollectedDataType)(0),                                     // 75: rewards.CollectedDataType
	(*RewardOptionMinimal)(nil),                                // 76: rewards.RewardOptionMinimal
	(RewardType)(0),                                            // 77: rewards.RewardType
	(RewardStatus)(0),                                          // 78: rewards.RewardStatus
	(ClaimType)(0),                                             // 79: rewards.ClaimType
	(*RewardMetadata)(nil),                                     // 80: rewards.RewardMetadata
	(ClawbackStatus)(0),                                        // 81: rewards.ClawbackStatus
	(*feedback_engine.IsActorEligibleForSurveyRequest)(nil),    // 82: inapphelp.feedback_engine.IsActorEligibleForSurveyRequest
	(*feedback_engine.IsActorEligibleForSurveyResponse)(nil),   // 83: inapphelp.feedback_engine.IsActorEligibleForSurveyResponse
}
var file_api_rewards_service_proto_depIdxs = []int32{
	51,  // 0: rewards.RewardsByActorIdRequest.filter:type_name -> rewards.RewardsByActorIdRequest.Filter
	65,  // 1: rewards.RewardsByActorIdRequest.page_context:type_name -> rpc.PageContextRequest
	50,  // 2: rewards.RewardsByActorIdRequest.filters_v2:type_name -> rewards.RewardsByActorIdRequest.FiltersV2
	66,  // 3: rewards.RewardsResponse.status:type_name -> rpc.Status
	67,  // 4: rewards.RewardsResponse.page_context:type_name -> rpc.PageContextResponse
	68,  // 5: rewards.RewardsResponse.rewards:type_name -> rewards.Reward
	66,  // 6: rewards.RewardResponse.status:type_name -> rpc.Status
	68,  // 7: rewards.RewardResponse.reward:type_name -> rewards.Reward
	69,  // 8: rewards.GetRewardsRequest.reward_offer_type:type_name -> rewards.RewardOfferType
	66,  // 9: rewards.GetRewardsResponse.status:type_name -> rpc.Status
	68,  // 10: rewards.GetRewardsResponse.rewards:type_name -> rewards.Reward
	70,  // 11: rewards.ChooseRewardRequest.claim_metadata:type_name -> rewards.RewardClaimMetadata
	66,  // 12: rewards.ChooseRewardResponse.status:type_name -> rpc.Status
	71,  // 13: rewards.GetUnOpenedRewardsCountRequest.from_time:type_name -> google.protobuf.Timestamp
	71,  // 14: rewards.GetUnOpenedRewardsCountRequest.upto_time:type_name -> google.protobuf.Timestamp
	66,  // 15: rewards.GetUnOpenedRewardsCountResponse.status:type_name -> rpc.Status
	52,  // 16: rewards.GetRewardSummaryRequest.filter:type_name -> rewards.GetRewardSummaryRequest.Filter
	66,  // 17: rewards.GetRewardSummaryResponse.status:type_name -> rpc.Status
	72,  // 18: rewards.GetRewardSummaryResponse.total_cash_reward_earned:type_name -> google.type.Money
	72,  // 19: rewards.GetRewardSummaryResponse.total_sid_reward_earned:type_name -> google.type.Money
	72,  // 20: rewards.GetRewardSummaryResponse.total_in_processing_cash_reward_amount:type_name -> google.type.Money
	72,  // 21: rewards.GetRewardSummaryResponse.total_in_processing_sid_reward_amount:type_name -> google.type.Money
	72,  // 22: rewards.GetRewardSummaryResponse.total_locked_cash_reward_amount:type_name -> google.type.Money
	66,  // 23: rewards.RetryRewardProcessingResponse.status:type_name -> rpc.Status
	71,  // 24: rewards.GetReferralRewardsCappingInfoRequest.referrals_unlock_date:type_name -> google.protobuf.Timestamp
	66,  // 25: rewards.GetReferralRewardsCappingInfoResponse.status:type_name -> rpc.Status
	71,  // 26: rewards.GetReferralRewardsCappingInfoResponse.max_cap_reset_date:type_name -> google.protobuf.Timestamp
	71,  // 27: rewards.GetReferralRewardsCappingInfoResponse.last_cap_reset_date:type_name -> google.protobuf.Timestamp
	66,  // 28: rewards.GetReferralRewardsCappingConfigResponse.status:type_name -> rpc.Status
	73,  // 29: rewards.GetReferralRewardsCappingConfigResponse.capping_period:type_name -> google.protobuf.Duration
	66,  // 30: rewards.GetAutoClaimableRewardsCountResponse.status:type_name -> rpc.Status
	66,  // 31: rewards.BulkClaimRewardsWithDefaultOptionResponse.status:type_name -> rpc.Status
	66,  // 32: rewards.TriggerManualGiveawayRewardForActorResponse.status:type_name -> rpc.Status
	69,  // 33: rewards.GetCreditCardLinkedRewardDetailsRequest.reward_offer_type:type_name -> rewards.RewardOfferType
	66,  // 34: rewards.GetCreditCardLinkedRewardDetailsResponse.status:type_name -> rpc.Status
	53,  // 35: rewards.GetCreditCardLinkedRewardDetailsResponse.details_list:type_name -> rewards.GetCreditCardLinkedRewardDetailsResponse.Details
	31,  // 36: rewards.GetCreditCard1XRewardsSummaryRequest.time_window:type_name -> rewards.TimeWindow
	66,  // 37: rewards.GetCreditCard1XRewardsSummaryResponse.status:type_name -> rpc.Status
	56,  // 38: rewards.GetCreditCard1XRewardsSummaryResponse.merchant_reward_aggregates:type_name -> rewards.GetCreditCard1XRewardsSummaryResponse.MerchantRewardAggregate
	71,  // 39: rewards.TimeWindow.from_time:type_name -> google.protobuf.Timestamp
	71,  // 40: rewards.TimeWindow.till_time:type_name -> google.protobuf.Timestamp
	57,  // 41: rewards.GetRewardsCountRequest.filters:type_name -> rewards.GetRewardsCountRequest.Filters
	66,  // 42: rewards.GetRewardsCountResponse.status:type_name -> rpc.Status
	71,  // 43: rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthRequest.date:type_name -> google.protobuf.Timestamp
	66,  // 44: rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse.status:type_name -> rpc.Status
	74,  // 45: rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse.monthly_reward_offer_reward_units_actor_utilisation:type_name -> rewards.RewardOfferRewardUnitsActorUtilisationInTimePeriod
	59,  // 46: rewards.GetRewardUtilisationForActorAndOfferRequest.filters:type_name -> rewards.GetRewardUtilisationForActorAndOfferRequest.Filters
	66,  // 47: rewards.GetRewardUtilisationForActorAndOfferResponse.status:type_name -> rpc.Status
	60,  // 48: rewards.GetRewardUtilisationForActorAndOfferResponse.reward_offer_reward_units_utilization_for_actor:type_name -> rewards.GetRewardUtilisationForActorAndOfferResponse.RewardOfferRewardUnitsUtilizationForActor
	66,  // 49: rewards.BulkClaimRewardsWithDefaultOptionV2Response.status:type_name -> rpc.Status
	66,  // 50: rewards.GetBulkClaimRewardsProcessingStateResponse.status:type_name -> rpc.Status
	75,  // 51: rewards.GetAllRewardsAndProjectionRequest.action_type:type_name -> rewards.CollectedDataType
	66,  // 52: rewards.GetAllRewardsAndProjectionResponse.status:type_name -> rpc.Status
	61,  // 53: rewards.GetAllRewardsAndProjectionResponse.ref_id_to_reward_entities_map:type_name -> rewards.GetAllRewardsAndProjectionResponse.RefIdToRewardEntitiesMapEntry
	64,  // 54: rewards.GetRewardAggregatesRequest.filter:type_name -> rewards.GetRewardAggregatesRequest.Filter
	66,  // 55: rewards.GetRewardAggregatesResponse.status:type_name -> rpc.Status
	76,  // 56: rewards.GetRewardAggregatesResponse.aggregates:type_name -> rewards.RewardOptionMinimal
	2,   // 57: rewards.GetEstimatedRewardsInTimeDurationForTierRequest.user_tier:type_name -> rewards.GetEstimatedRewardsInTimeDurationForTierRequest.UserTier
	31,  // 58: rewards.GetEstimatedRewardsInTimeDurationForTierRequest.time_window:type_name -> rewards.TimeWindow
	66,  // 59: rewards.GetEstimatedRewardsInTimeDurationForTierResponse.status:type_name -> rpc.Status
	66,  // 60: rewards.ForceRetryRewardProcessingResponse.status:type_name -> rpc.Status
	51,  // 61: rewards.RewardsByActorIdRequest.FiltersV2.or_filter:type_name -> rewards.RewardsByActorIdRequest.Filter
	51,  // 62: rewards.RewardsByActorIdRequest.FiltersV2.and_filter:type_name -> rewards.RewardsByActorIdRequest.Filter
	77,  // 63: rewards.RewardsByActorIdRequest.Filter.reward_type:type_name -> rewards.RewardType
	0,   // 64: rewards.RewardsByActorIdRequest.Filter.visibility_type:type_name -> rewards.VisibilityType
	71,  // 65: rewards.RewardsByActorIdRequest.Filter.start_date:type_name -> google.protobuf.Timestamp
	71,  // 66: rewards.RewardsByActorIdRequest.Filter.end_date:type_name -> google.protobuf.Timestamp
	69,  // 67: rewards.RewardsByActorIdRequest.Filter.reward_offer_type:type_name -> rewards.RewardOfferType
	78,  // 68: rewards.RewardsByActorIdRequest.Filter.statuses:type_name -> rewards.RewardStatus
	75,  // 69: rewards.RewardsByActorIdRequest.Filter.action_types:type_name -> rewards.CollectedDataType
	79,  // 70: rewards.RewardsByActorIdRequest.Filter.claim_type:type_name -> rewards.ClaimType
	69,  // 71: rewards.RewardsByActorIdRequest.Filter.reward_offer_types:type_name -> rewards.RewardOfferType
	69,  // 72: rewards.GetRewardSummaryRequest.Filter.reward_offer_type:type_name -> rewards.RewardOfferType
	71,  // 73: rewards.GetRewardSummaryRequest.Filter.from_time:type_name -> google.protobuf.Timestamp
	71,  // 74: rewards.GetRewardSummaryRequest.Filter.upto_time:type_name -> google.protobuf.Timestamp
	75,  // 75: rewards.GetRewardSummaryRequest.Filter.action_types:type_name -> rewards.CollectedDataType
	69,  // 76: rewards.GetRewardSummaryRequest.Filter.reward_offer_types:type_name -> rewards.RewardOfferType
	54,  // 77: rewards.GetCreditCardLinkedRewardDetailsResponse.Details.reward_details:type_name -> rewards.GetCreditCardLinkedRewardDetailsResponse.RewardDetails
	55,  // 78: rewards.GetCreditCardLinkedRewardDetailsResponse.Details.reward_clawback_details:type_name -> rewards.GetCreditCardLinkedRewardDetailsResponse.RewardClawbackDetails
	77,  // 79: rewards.GetCreditCardLinkedRewardDetailsResponse.RewardDetails.reward_type:type_name -> rewards.RewardType
	78,  // 80: rewards.GetCreditCardLinkedRewardDetailsResponse.RewardDetails.status:type_name -> rewards.RewardStatus
	80,  // 81: rewards.GetCreditCardLinkedRewardDetailsResponse.RewardDetails.reward_metadata:type_name -> rewards.RewardMetadata
	77,  // 82: rewards.GetCreditCardLinkedRewardDetailsResponse.RewardClawbackDetails.reward_type:type_name -> rewards.RewardType
	81,  // 83: rewards.GetCreditCardLinkedRewardDetailsResponse.RewardClawbackDetails.clawback_status:type_name -> rewards.ClawbackStatus
	58,  // 84: rewards.GetRewardsCountRequest.Filters.and_filter:type_name -> rewards.GetRewardsCountRequest.FilterFields
	58,  // 85: rewards.GetRewardsCountRequest.Filters.or_filter:type_name -> rewards.GetRewardsCountRequest.FilterFields
	71,  // 86: rewards.GetRewardsCountRequest.FilterFields.from_time:type_name -> google.protobuf.Timestamp
	71,  // 87: rewards.GetRewardsCountRequest.FilterFields.till_time:type_name -> google.protobuf.Timestamp
	69,  // 88: rewards.GetRewardsCountRequest.FilterFields.reward_offer_types:type_name -> rewards.RewardOfferType
	77,  // 89: rewards.GetRewardsCountRequest.FilterFields.reward_types:type_name -> rewards.RewardType
	71,  // 90: rewards.GetRewardUtilisationForActorAndOfferRequest.Filters.timestamp_of_month:type_name -> google.protobuf.Timestamp
	62,  // 91: rewards.GetAllRewardsAndProjectionResponse.RefIdToRewardEntitiesMapEntry.value:type_name -> rewards.GetAllRewardsAndProjectionResponse.RewardEntities
	63,  // 92: rewards.GetAllRewardsAndProjectionResponse.RewardEntities.reward_entities:type_name -> rewards.GetAllRewardsAndProjectionResponse.RewardEntity
	76,  // 93: rewards.GetAllRewardsAndProjectionResponse.RewardEntity.reward_options:type_name -> rewards.RewardOptionMinimal
	69,  // 94: rewards.GetAllRewardsAndProjectionResponse.RewardEntity.offer_type:type_name -> rewards.RewardOfferType
	1,   // 95: rewards.GetAllRewardsAndProjectionResponse.RewardEntity.reward_entity_type:type_name -> rewards.GetAllRewardsAndProjectionResponse.RewardEntityType
	69,  // 96: rewards.GetRewardAggregatesRequest.Filter.offer_types:type_name -> rewards.RewardOfferType
	75,  // 97: rewards.GetRewardAggregatesRequest.Filter.action_types:type_name -> rewards.CollectedDataType
	31,  // 98: rewards.GetRewardAggregatesRequest.Filter.time_windows:type_name -> rewards.TimeWindow
	3,   // 99: rewards.RewardsGenerator.GetRewardsByActorId:input_type -> rewards.RewardsByActorIdRequest
	5,   // 100: rewards.RewardsGenerator.GetRewardsByRewardId:input_type -> rewards.RewardsByRewardIdRequest
	7,   // 101: rewards.RewardsGenerator.GetRewards:input_type -> rewards.GetRewardsRequest
	9,   // 102: rewards.RewardsGenerator.ChooseReward:input_type -> rewards.ChooseRewardRequest
	11,  // 103: rewards.RewardsGenerator.GetUnOpenedRewardsCount:input_type -> rewards.GetUnOpenedRewardsCountRequest
	13,  // 104: rewards.RewardsGenerator.GetRewardSummary:input_type -> rewards.GetRewardSummaryRequest
	15,  // 105: rewards.RewardsGenerator.RetryRewardProcessing:input_type -> rewards.RetryRewardProcessingRequest
	17,  // 106: rewards.RewardsGenerator.GetReferralRewardsCappingInfo:input_type -> rewards.GetReferralRewardsCappingInfoRequest
	19,  // 107: rewards.RewardsGenerator.GetReferralRewardsCappingConfig:input_type -> rewards.GetReferralRewardsCappingConfigRequest
	23,  // 108: rewards.RewardsGenerator.BulkClaimRewardsWithDefaultOption:input_type -> rewards.BulkClaimRewardsWithDefaultOptionRequest
	21,  // 109: rewards.RewardsGenerator.GetAutoClaimableRewardsCount:input_type -> rewards.GetAutoClaimableRewardsCountRequest
	25,  // 110: rewards.RewardsGenerator.TriggerManualGiveawayRewardForActor:input_type -> rewards.TriggerManualGiveawayRewardForActorRequest
	27,  // 111: rewards.RewardsGenerator.GetCreditCardLinkedRewardDetails:input_type -> rewards.GetCreditCardLinkedRewardDetailsRequest
	29,  // 112: rewards.RewardsGenerator.GetCreditCard1XRewardsSummary:input_type -> rewards.GetCreditCard1XRewardsSummaryRequest
	32,  // 113: rewards.RewardsGenerator.GetRewardsCount:input_type -> rewards.GetRewardsCountRequest
	34,  // 114: rewards.RewardsGenerator.GetRewardUnitsUtilisationForActorAndOfferInMonth:input_type -> rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthRequest
	36,  // 115: rewards.RewardsGenerator.GetRewardUtilisationForActorAndOffer:input_type -> rewards.GetRewardUtilisationForActorAndOfferRequest
	38,  // 116: rewards.RewardsGenerator.BulkClaimRewardsWithDefaultOptionV2:input_type -> rewards.BulkClaimRewardsWithDefaultOptionV2Request
	40,  // 117: rewards.RewardsGenerator.GetBulkClaimRewardsProcessingState:input_type -> rewards.GetBulkClaimRewardsProcessingStateRequest
	42,  // 118: rewards.RewardsGenerator.GetAllRewardsAndProjections:input_type -> rewards.GetAllRewardsAndProjectionRequest
	44,  // 119: rewards.RewardsGenerator.GetRewardAggregates:input_type -> rewards.GetRewardAggregatesRequest
	46,  // 120: rewards.RewardsGenerator.GetEstimatedRewardsInTimeDurationForTier:input_type -> rewards.GetEstimatedRewardsInTimeDurationForTierRequest
	48,  // 121: rewards.RewardsGenerator.ForceRetryRewardProcessing:input_type -> rewards.ForceRetryRewardProcessingRequest
	82,  // 122: rewards.RewardsGenerator.IsActorEligibleForSurvey:input_type -> inapphelp.feedback_engine.IsActorEligibleForSurveyRequest
	4,   // 123: rewards.RewardsGenerator.GetRewardsByActorId:output_type -> rewards.RewardsResponse
	6,   // 124: rewards.RewardsGenerator.GetRewardsByRewardId:output_type -> rewards.RewardResponse
	8,   // 125: rewards.RewardsGenerator.GetRewards:output_type -> rewards.GetRewardsResponse
	10,  // 126: rewards.RewardsGenerator.ChooseReward:output_type -> rewards.ChooseRewardResponse
	12,  // 127: rewards.RewardsGenerator.GetUnOpenedRewardsCount:output_type -> rewards.GetUnOpenedRewardsCountResponse
	14,  // 128: rewards.RewardsGenerator.GetRewardSummary:output_type -> rewards.GetRewardSummaryResponse
	16,  // 129: rewards.RewardsGenerator.RetryRewardProcessing:output_type -> rewards.RetryRewardProcessingResponse
	18,  // 130: rewards.RewardsGenerator.GetReferralRewardsCappingInfo:output_type -> rewards.GetReferralRewardsCappingInfoResponse
	20,  // 131: rewards.RewardsGenerator.GetReferralRewardsCappingConfig:output_type -> rewards.GetReferralRewardsCappingConfigResponse
	24,  // 132: rewards.RewardsGenerator.BulkClaimRewardsWithDefaultOption:output_type -> rewards.BulkClaimRewardsWithDefaultOptionResponse
	22,  // 133: rewards.RewardsGenerator.GetAutoClaimableRewardsCount:output_type -> rewards.GetAutoClaimableRewardsCountResponse
	26,  // 134: rewards.RewardsGenerator.TriggerManualGiveawayRewardForActor:output_type -> rewards.TriggerManualGiveawayRewardForActorResponse
	28,  // 135: rewards.RewardsGenerator.GetCreditCardLinkedRewardDetails:output_type -> rewards.GetCreditCardLinkedRewardDetailsResponse
	30,  // 136: rewards.RewardsGenerator.GetCreditCard1XRewardsSummary:output_type -> rewards.GetCreditCard1XRewardsSummaryResponse
	33,  // 137: rewards.RewardsGenerator.GetRewardsCount:output_type -> rewards.GetRewardsCountResponse
	35,  // 138: rewards.RewardsGenerator.GetRewardUnitsUtilisationForActorAndOfferInMonth:output_type -> rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse
	37,  // 139: rewards.RewardsGenerator.GetRewardUtilisationForActorAndOffer:output_type -> rewards.GetRewardUtilisationForActorAndOfferResponse
	39,  // 140: rewards.RewardsGenerator.BulkClaimRewardsWithDefaultOptionV2:output_type -> rewards.BulkClaimRewardsWithDefaultOptionV2Response
	41,  // 141: rewards.RewardsGenerator.GetBulkClaimRewardsProcessingState:output_type -> rewards.GetBulkClaimRewardsProcessingStateResponse
	43,  // 142: rewards.RewardsGenerator.GetAllRewardsAndProjections:output_type -> rewards.GetAllRewardsAndProjectionResponse
	45,  // 143: rewards.RewardsGenerator.GetRewardAggregates:output_type -> rewards.GetRewardAggregatesResponse
	47,  // 144: rewards.RewardsGenerator.GetEstimatedRewardsInTimeDurationForTier:output_type -> rewards.GetEstimatedRewardsInTimeDurationForTierResponse
	49,  // 145: rewards.RewardsGenerator.ForceRetryRewardProcessing:output_type -> rewards.ForceRetryRewardProcessingResponse
	83,  // 146: rewards.RewardsGenerator.IsActorEligibleForSurvey:output_type -> inapphelp.feedback_engine.IsActorEligibleForSurveyResponse
	123, // [123:147] is the sub-list for method output_type
	99,  // [99:123] is the sub-list for method input_type
	99,  // [99:99] is the sub-list for extension type_name
	99,  // [99:99] is the sub-list for extension extendee
	0,   // [0:99] is the sub-list for field type_name
}

func init() { file_api_rewards_service_proto_init() }
func file_api_rewards_service_proto_init() {
	if File_api_rewards_service_proto != nil {
		return
	}
	file_api_rewards_collected_data_type_proto_init()
	file_api_rewards_reward_proto_init()
	file_api_rewards_reward_clawback_proto_init()
	file_api_rewards_reward_offer_type_proto_init()
	file_api_rewards_reward_units_utilisation_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_rewards_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardsByActorIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardsByRewardIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChooseRewardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChooseRewardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUnOpenedRewardsCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUnOpenedRewardsCountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardSummaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetryRewardProcessingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetryRewardProcessingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReferralRewardsCappingInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReferralRewardsCappingInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReferralRewardsCappingConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReferralRewardsCappingConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAutoClaimableRewardsCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAutoClaimableRewardsCountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkClaimRewardsWithDefaultOptionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkClaimRewardsWithDefaultOptionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerManualGiveawayRewardForActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerManualGiveawayRewardForActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardLinkedRewardDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardLinkedRewardDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCard1XRewardsSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCard1XRewardsSummaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeWindow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardsCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardsCountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardUnitsUtilisationForActorAndOfferInMonthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardUnitsUtilisationForActorAndOfferInMonthResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardUtilisationForActorAndOfferRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardUtilisationForActorAndOfferResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkClaimRewardsWithDefaultOptionV2Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkClaimRewardsWithDefaultOptionV2Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBulkClaimRewardsProcessingStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBulkClaimRewardsProcessingStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllRewardsAndProjectionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllRewardsAndProjectionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardAggregatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardAggregatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEstimatedRewardsInTimeDurationForTierRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEstimatedRewardsInTimeDurationForTierResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForceRetryRewardProcessingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForceRetryRewardProcessingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardsByActorIdRequest_FiltersV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardsByActorIdRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardSummaryRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardLinkedRewardDetailsResponse_Details); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardLinkedRewardDetailsResponse_RewardDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardsCountRequest_Filters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardsCountRequest_FilterFields); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardUtilisationForActorAndOfferRequest_Filters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllRewardsAndProjectionResponse_RewardEntities); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllRewardsAndProjectionResponse_RewardEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardAggregatesRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_rewards_service_proto_msgTypes[50].OneofWrappers = []interface{}{
		(*GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails)(nil),
		(*GetCreditCardLinkedRewardDetailsResponse_Details_RewardClawbackDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_rewards_service_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   62,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_rewards_service_proto_goTypes,
		DependencyIndexes: file_api_rewards_service_proto_depIdxs,
		EnumInfos:         file_api_rewards_service_proto_enumTypes,
		MessageInfos:      file_api_rewards_service_proto_msgTypes,
	}.Build()
	File_api_rewards_service_proto = out.File
	file_api_rewards_service_proto_rawDesc = nil
	file_api_rewards_service_proto_goTypes = nil
	file_api_rewards_service_proto_depIdxs = nil
}
