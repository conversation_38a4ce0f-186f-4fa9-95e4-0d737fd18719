// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package api.fittt.action.aggregator;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/fittt/action/aggregator;aggrpb";
option java_package = "com.github.epifi.gamma.api.fittt.action.aggregator";

// AggregatedExecution is created by aggregating multiple AggregationEntity  to execute as single execution.
message AggregatedExecution {
  // unique reference
  string id = 1;
  // status of execution
  ExecutionStatus status = 2;
  // actor_id of the user for whom the execution is processed
  string actor_id = 3;
  // type of aggregation
  AggregationType aggr_type = 4;
  // key used to group entities
  string aggregation_key = 5;
  // list of ids that needs to be aggregated and executed
  repeated string aggr_entity_ids = 6;

  // creation timestamp
  google.protobuf.Timestamp created_at = 7;
  // last updated time stamp
  google.protobuf.Timestamp updated_at = 8;
  // deleted time stamp
  google.protobuf.Timestamp deleted_at = 9;
}

// AggregationEntity represents executions which need to be aggregated based on aggregation key for each user
message AggregationEntity {
  string id = 1;
  // id reference to FIT action_execution
  string action_execution_id = 2;
  // Type of aggregation like deposit, notification, aggregate mf
  AggregationType aggr_type = 3;
  // status of entity
  AggregationEntityStatus status = 4;
  // Entities are aggregated based on actor_id and aggregation_key
  string actor_id = 5;
  // Event id which led to the action execution
  string event_id = 6;
  // key generated by aggregator service based on event_id to group entities and execute as single action
  string aggregation_key = 7;
  // creation timestamp
  google.protobuf.Timestamp created_at = 8;
  // last updated time stamp
  google.protobuf.Timestamp updated_at = 9;
  // deleted time stamp
  google.protobuf.Timestamp deleted_at = 10;
}

enum AggregationEntityStatus {
  AGGREGATION_STATUS_UNSPECIFIED = 0;
  AGGR_WAITING = 1;
  AGGR_PROCESSING = 2;
  AGGR_FAILED = 3;
  AGGR_SUCCEEDED = 4;
}

enum ExecutionStatus {
  EXECUTION_STATUS_UNSPECIFIED = 0;
  EXECUTION_INITIATED = 1;
  EXECUTION_SUCCESS = 2;
  EXECUTION_FAILED = 3;
}

enum AggregationType {
  EXECUTION_TYPE_UNSPECIFIED = 0;
  AGGREGATED_DEPOSIT = 1;
  AGGREGATED_SPORTS_RULES_DEPOSIT_NOTIFICATION = 2;
  AGGREGATED_DEPOSIT_SMS = 3;
  AGGREGATED_PURCHASE_MUTUAL_FUND = 4;
}

message AggregationInfo{
  AggregationType type = 1;
  // eg: aggAmount >= minMFAmount
  string condition = 2;
}
