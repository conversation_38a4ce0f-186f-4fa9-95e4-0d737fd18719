// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/fittt/devconsole/rule_template.proto

package devconsolepb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	event "github.com/epifi/gamma/api/rms/orchestrator/event"

	ui "github.com/epifi/gamma/api/rms/ui"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = event.EventType(0)

	_ = ui.RuleParamType(0)
)

// Validate checks the field values on RuleTemplate with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RuleTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RuleTemplate with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RuleTemplateMultiError, or
// nil if none found.
func (m *RuleTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *RuleTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for State

	// no validation rules for RuleType

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RuleTemplateValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RuleTemplateValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RuleTemplateValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RuleTemplateMultiError(errors)
	}

	return nil
}

// RuleTemplateMultiError is an error wrapping multiple validation errors
// returned by RuleTemplate.ValidateAll() if the designated constraints aren't met.
type RuleTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RuleTemplateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RuleTemplateMultiError) AllErrors() []error { return m }

// RuleTemplateValidationError is the validation error returned by
// RuleTemplate.Validate if the designated constraints aren't met.
type RuleTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RuleTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RuleTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RuleTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RuleTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RuleTemplateValidationError) ErrorName() string { return "RuleTemplateValidationError" }

// Error satisfies the builtin error interface
func (e RuleTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRuleTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RuleTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RuleTemplateValidationError{}

// Validate checks the field values on RuleData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RuleData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RuleData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RuleDataMultiError, or nil
// if none found.
func (m *RuleData) ValidateAll() error {
	return m.validate(true)
}

func (m *RuleData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetParams()))
		i := 0
		for key := range m.GetParams() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetParams()[key]
			_ = val

			// no validation rules for Params[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, RuleDataValidationError{
							field:  fmt.Sprintf("Params[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, RuleDataValidationError{
							field:  fmt.Sprintf("Params[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return RuleDataValidationError{
						field:  fmt.Sprintf("Params[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for EventType

	if all {
		switch v := interface{}(m.GetCondition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RuleDataValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RuleDataValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCondition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RuleDataValidationError{
				field:  "Condition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RuleDataValidationError{
					field:  "Actions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RuleDataValidationError{
					field:  "Actions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RuleDataValidationError{
				field:  "Actions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaxSubscriptionsPerActor

	// no validation rules for MaxSubscriptions

	if all {
		switch v := interface{}(m.GetDefaultSubscriptionExpiryData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RuleDataValidationError{
					field:  "DefaultSubscriptionExpiryData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RuleDataValidationError{
					field:  "DefaultSubscriptionExpiryData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDefaultSubscriptionExpiryData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RuleDataValidationError{
				field:  "DefaultSubscriptionExpiryData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RuleDataMultiError(errors)
	}

	return nil
}

// RuleDataMultiError is an error wrapping multiple validation errors returned
// by RuleData.ValidateAll() if the designated constraints aren't met.
type RuleDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RuleDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RuleDataMultiError) AllErrors() []error { return m }

// RuleDataValidationError is the validation error returned by
// RuleData.Validate if the designated constraints aren't met.
type RuleDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RuleDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RuleDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RuleDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RuleDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RuleDataValidationError) ErrorName() string { return "RuleDataValidationError" }

// Error satisfies the builtin error interface
func (e RuleDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRuleData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RuleDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RuleDataValidationError{}

// Validate checks the field values on Param with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Param) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Param with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ParamMultiError, or nil if none found.
func (m *Param) ValidateAll() error {
	return m.validate(true)
}

func (m *Param) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for ParamType

	if all {
		switch v := interface{}(m.GetDefaultValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ParamValidationError{
					field:  "DefaultValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ParamValidationError{
					field:  "DefaultValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDefaultValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ParamValidationError{
				field:  "DefaultValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPossibleValues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ParamValidationError{
						field:  fmt.Sprintf("PossibleValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ParamValidationError{
						field:  fmt.Sprintf("PossibleValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ParamValidationError{
					field:  fmt.Sprintf("PossibleValues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ConfType

	if len(errors) > 0 {
		return ParamMultiError(errors)
	}

	return nil
}

// ParamMultiError is an error wrapping multiple validation errors returned by
// Param.ValidateAll() if the designated constraints aren't met.
type ParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ParamMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ParamMultiError) AllErrors() []error { return m }

// ParamValidationError is the validation error returned by Param.Validate if
// the designated constraints aren't met.
type ParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ParamValidationError) ErrorName() string { return "ParamValidationError" }

// Error satisfies the builtin error interface
func (e ParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ParamValidationError{}
