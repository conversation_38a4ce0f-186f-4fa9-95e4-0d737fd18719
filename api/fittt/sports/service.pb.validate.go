// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/fittt/sports/service.proto

package sports

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on InitiateMatchUpdateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateMatchUpdateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateMatchUpdateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateMatchUpdateRequestMultiError, or nil if none found.
func (m *InitiateMatchUpdateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateMatchUpdateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RetrySuffix

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateMatchUpdateRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateMatchUpdateRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateMatchUpdateRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateMatchUpdateRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateMatchUpdateRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateMatchUpdateRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExecScope()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateMatchUpdateRequestValidationError{
					field:  "ExecScope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateMatchUpdateRequestValidationError{
					field:  "ExecScope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExecScope()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateMatchUpdateRequestValidationError{
				field:  "ExecScope",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.MatchRequest.(type) {
	case *InitiateMatchUpdateRequest_CricketMatch:
		if v == nil {
			err := InitiateMatchUpdateRequestValidationError{
				field:  "MatchRequest",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCricketMatch()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InitiateMatchUpdateRequestValidationError{
						field:  "CricketMatch",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InitiateMatchUpdateRequestValidationError{
						field:  "CricketMatch",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCricketMatch()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InitiateMatchUpdateRequestValidationError{
					field:  "CricketMatch",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InitiateMatchUpdateRequest_FootballMatch:
		if v == nil {
			err := InitiateMatchUpdateRequestValidationError{
				field:  "MatchRequest",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFootballMatch()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InitiateMatchUpdateRequestValidationError{
						field:  "FootballMatch",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InitiateMatchUpdateRequestValidationError{
						field:  "FootballMatch",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFootballMatch()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InitiateMatchUpdateRequestValidationError{
					field:  "FootballMatch",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InitiateMatchUpdateRequest_Olympics:
		if v == nil {
			err := InitiateMatchUpdateRequestValidationError{
				field:  "MatchRequest",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOlympics()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InitiateMatchUpdateRequestValidationError{
						field:  "Olympics",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InitiateMatchUpdateRequestValidationError{
						field:  "Olympics",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOlympics()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InitiateMatchUpdateRequestValidationError{
					field:  "Olympics",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return InitiateMatchUpdateRequestMultiError(errors)
	}

	return nil
}

// InitiateMatchUpdateRequestMultiError is an error wrapping multiple
// validation errors returned by InitiateMatchUpdateRequest.ValidateAll() if
// the designated constraints aren't met.
type InitiateMatchUpdateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateMatchUpdateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateMatchUpdateRequestMultiError) AllErrors() []error { return m }

// InitiateMatchUpdateRequestValidationError is the validation error returned
// by InitiateMatchUpdateRequest.Validate if the designated constraints aren't met.
type InitiateMatchUpdateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateMatchUpdateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateMatchUpdateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateMatchUpdateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateMatchUpdateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateMatchUpdateRequestValidationError) ErrorName() string {
	return "InitiateMatchUpdateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateMatchUpdateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateMatchUpdateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateMatchUpdateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateMatchUpdateRequestValidationError{}

// Validate checks the field values on CricketMatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CricketMatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CricketMatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CricketMatchRequestMultiError, or nil if none found.
func (m *CricketMatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CricketMatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetMatchId()) < 1 {
		err := CricketMatchRequestValidationError{
			field:  "MatchId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CricketMatchRequestMultiError(errors)
	}

	return nil
}

// CricketMatchRequestMultiError is an error wrapping multiple validation
// errors returned by CricketMatchRequest.ValidateAll() if the designated
// constraints aren't met.
type CricketMatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CricketMatchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CricketMatchRequestMultiError) AllErrors() []error { return m }

// CricketMatchRequestValidationError is the validation error returned by
// CricketMatchRequest.Validate if the designated constraints aren't met.
type CricketMatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CricketMatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CricketMatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CricketMatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CricketMatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CricketMatchRequestValidationError) ErrorName() string {
	return "CricketMatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CricketMatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCricketMatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CricketMatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CricketMatchRequestValidationError{}

// Validate checks the field values on FootballMatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FootballMatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FootballMatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FootballMatchRequestMultiError, or nil if none found.
func (m *FootballMatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FootballMatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetMatchId()) < 1 {
		err := FootballMatchRequestValidationError{
			field:  "MatchId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FootballMatchRequestMultiError(errors)
	}

	return nil
}

// FootballMatchRequestMultiError is an error wrapping multiple validation
// errors returned by FootballMatchRequest.ValidateAll() if the designated
// constraints aren't met.
type FootballMatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FootballMatchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FootballMatchRequestMultiError) AllErrors() []error { return m }

// FootballMatchRequestValidationError is the validation error returned by
// FootballMatchRequest.Validate if the designated constraints aren't met.
type FootballMatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FootballMatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FootballMatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FootballMatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FootballMatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FootballMatchRequestValidationError) ErrorName() string {
	return "FootballMatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FootballMatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFootballMatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FootballMatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FootballMatchRequestValidationError{}

// Validate checks the field values on OlympicsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OlympicsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OlympicsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OlympicsRequestMultiError, or nil if none found.
func (m *OlympicsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *OlympicsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MedalsCount

	// no validation rules for Description

	if len(errors) > 0 {
		return OlympicsRequestMultiError(errors)
	}

	return nil
}

// OlympicsRequestMultiError is an error wrapping multiple validation errors
// returned by OlympicsRequest.ValidateAll() if the designated constraints
// aren't met.
type OlympicsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OlympicsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OlympicsRequestMultiError) AllErrors() []error { return m }

// OlympicsRequestValidationError is the validation error returned by
// OlympicsRequest.Validate if the designated constraints aren't met.
type OlympicsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OlympicsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OlympicsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OlympicsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OlympicsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OlympicsRequestValidationError) ErrorName() string { return "OlympicsRequestValidationError" }

// Error satisfies the builtin error interface
func (e OlympicsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOlympicsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OlympicsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OlympicsRequestValidationError{}

// Validate checks the field values on InitiateMatchUpdateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateMatchUpdateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateMatchUpdateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateMatchUpdateResponseMultiError, or nil if none found.
func (m *InitiateMatchUpdateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateMatchUpdateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateMatchUpdateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateMatchUpdateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateMatchUpdateResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateMatchUpdateResponseMultiError(errors)
	}

	return nil
}

// InitiateMatchUpdateResponseMultiError is an error wrapping multiple
// validation errors returned by InitiateMatchUpdateResponse.ValidateAll() if
// the designated constraints aren't met.
type InitiateMatchUpdateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateMatchUpdateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateMatchUpdateResponseMultiError) AllErrors() []error { return m }

// InitiateMatchUpdateResponseValidationError is the validation error returned
// by InitiateMatchUpdateResponse.Validate if the designated constraints
// aren't met.
type InitiateMatchUpdateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateMatchUpdateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateMatchUpdateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateMatchUpdateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateMatchUpdateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateMatchUpdateResponseValidationError) ErrorName() string {
	return "InitiateMatchUpdateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateMatchUpdateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateMatchUpdateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateMatchUpdateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateMatchUpdateResponseValidationError{}

// Validate checks the field values on GetMatchesRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetMatchesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMatchesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMatchesRequestMultiError, or nil if none found.
func (m *GetMatchesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMatchesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMatchesRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMatchesRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMatchesRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFieldMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMatchesRequestValidationError{
					field:  "FieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMatchesRequestValidationError{
					field:  "FieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFieldMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMatchesRequestValidationError{
				field:  "FieldMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TournamentTag

	if all {
		switch v := interface{}(m.GetFrom()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMatchesRequestValidationError{
					field:  "From",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMatchesRequestValidationError{
					field:  "From",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFrom()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMatchesRequestValidationError{
				field:  "From",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMatchesRequestValidationError{
					field:  "To",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMatchesRequestValidationError{
					field:  "To",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMatchesRequestValidationError{
				field:  "To",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMatchesRequestMultiError(errors)
	}

	return nil
}

// GetMatchesRequestMultiError is an error wrapping multiple validation errors
// returned by GetMatchesRequest.ValidateAll() if the designated constraints
// aren't met.
type GetMatchesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMatchesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMatchesRequestMultiError) AllErrors() []error { return m }

// GetMatchesRequestValidationError is the validation error returned by
// GetMatchesRequest.Validate if the designated constraints aren't met.
type GetMatchesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMatchesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMatchesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMatchesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMatchesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMatchesRequestValidationError) ErrorName() string {
	return "GetMatchesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMatchesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMatchesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMatchesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMatchesRequestValidationError{}

// Validate checks the field values on GetMatchesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMatchesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMatchesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMatchesResponseMultiError, or nil if none found.
func (m *GetMatchesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMatchesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMatchesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMatchesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMatchesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMatchesResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMatchesResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMatchesResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMatches() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetMatchesResponseValidationError{
						field:  fmt.Sprintf("Matches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetMatchesResponseValidationError{
						field:  fmt.Sprintf("Matches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetMatchesResponseValidationError{
					field:  fmt.Sprintf("Matches[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetMatchesResponseMultiError(errors)
	}

	return nil
}

// GetMatchesResponseMultiError is an error wrapping multiple validation errors
// returned by GetMatchesResponse.ValidateAll() if the designated constraints
// aren't met.
type GetMatchesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMatchesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMatchesResponseMultiError) AllErrors() []error { return m }

// GetMatchesResponseValidationError is the validation error returned by
// GetMatchesResponse.Validate if the designated constraints aren't met.
type GetMatchesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMatchesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMatchesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMatchesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMatchesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMatchesResponseValidationError) ErrorName() string {
	return "GetMatchesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMatchesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMatchesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMatchesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMatchesResponseValidationError{}

// Validate checks the field values on GetMatchesByIdsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMatchesByIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMatchesByIdsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMatchesByIdsRequestMultiError, or nil if none found.
func (m *GetMatchesByIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMatchesByIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFieldMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMatchesByIdsRequestValidationError{
					field:  "FieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMatchesByIdsRequestValidationError{
					field:  "FieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFieldMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMatchesByIdsRequestValidationError{
				field:  "FieldMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMatchesByIdsRequestMultiError(errors)
	}

	return nil
}

// GetMatchesByIdsRequestMultiError is an error wrapping multiple validation
// errors returned by GetMatchesByIdsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMatchesByIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMatchesByIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMatchesByIdsRequestMultiError) AllErrors() []error { return m }

// GetMatchesByIdsRequestValidationError is the validation error returned by
// GetMatchesByIdsRequest.Validate if the designated constraints aren't met.
type GetMatchesByIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMatchesByIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMatchesByIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMatchesByIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMatchesByIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMatchesByIdsRequestValidationError) ErrorName() string {
	return "GetMatchesByIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMatchesByIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMatchesByIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMatchesByIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMatchesByIdsRequestValidationError{}

// Validate checks the field values on GetMatchesByIdsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMatchesByIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMatchesByIdsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMatchesByIdsResponseMultiError, or nil if none found.
func (m *GetMatchesByIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMatchesByIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMatchesByIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMatchesByIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMatchesByIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetMatches()))
		i := 0
		for key := range m.GetMatches() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetMatches()[key]
			_ = val

			// no validation rules for Matches[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetMatchesByIdsResponseValidationError{
							field:  fmt.Sprintf("Matches[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetMatchesByIdsResponseValidationError{
							field:  fmt.Sprintf("Matches[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetMatchesByIdsResponseValidationError{
						field:  fmt.Sprintf("Matches[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetMatchesByIdsResponseMultiError(errors)
	}

	return nil
}

// GetMatchesByIdsResponseMultiError is an error wrapping multiple validation
// errors returned by GetMatchesByIdsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMatchesByIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMatchesByIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMatchesByIdsResponseMultiError) AllErrors() []error { return m }

// GetMatchesByIdsResponseValidationError is the validation error returned by
// GetMatchesByIdsResponse.Validate if the designated constraints aren't met.
type GetMatchesByIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMatchesByIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMatchesByIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMatchesByIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMatchesByIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMatchesByIdsResponseValidationError) ErrorName() string {
	return "GetMatchesByIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMatchesByIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMatchesByIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMatchesByIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMatchesByIdsResponseValidationError{}

// Validate checks the field values on GetMatchesByVendorMatchIdsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetMatchesByVendorMatchIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMatchesByVendorMatchIdsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetMatchesByVendorMatchIdsRequestMultiError, or nil if none found.
func (m *GetMatchesByVendorMatchIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMatchesByVendorMatchIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFieldMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMatchesByVendorMatchIdsRequestValidationError{
					field:  "FieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMatchesByVendorMatchIdsRequestValidationError{
					field:  "FieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFieldMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMatchesByVendorMatchIdsRequestValidationError{
				field:  "FieldMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	if len(errors) > 0 {
		return GetMatchesByVendorMatchIdsRequestMultiError(errors)
	}

	return nil
}

// GetMatchesByVendorMatchIdsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetMatchesByVendorMatchIdsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMatchesByVendorMatchIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMatchesByVendorMatchIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMatchesByVendorMatchIdsRequestMultiError) AllErrors() []error { return m }

// GetMatchesByVendorMatchIdsRequestValidationError is the validation error
// returned by GetMatchesByVendorMatchIdsRequest.Validate if the designated
// constraints aren't met.
type GetMatchesByVendorMatchIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMatchesByVendorMatchIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMatchesByVendorMatchIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMatchesByVendorMatchIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMatchesByVendorMatchIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMatchesByVendorMatchIdsRequestValidationError) ErrorName() string {
	return "GetMatchesByVendorMatchIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMatchesByVendorMatchIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMatchesByVendorMatchIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMatchesByVendorMatchIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMatchesByVendorMatchIdsRequestValidationError{}

// Validate checks the field values on GetMatchesByVendorMatchIdsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetMatchesByVendorMatchIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMatchesByVendorMatchIdsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetMatchesByVendorMatchIdsResponseMultiError, or nil if none found.
func (m *GetMatchesByVendorMatchIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMatchesByVendorMatchIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMatchesByVendorMatchIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMatchesByVendorMatchIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMatchesByVendorMatchIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetMatches()))
		i := 0
		for key := range m.GetMatches() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetMatches()[key]
			_ = val

			// no validation rules for Matches[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetMatchesByVendorMatchIdsResponseValidationError{
							field:  fmt.Sprintf("Matches[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetMatchesByVendorMatchIdsResponseValidationError{
							field:  fmt.Sprintf("Matches[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetMatchesByVendorMatchIdsResponseValidationError{
						field:  fmt.Sprintf("Matches[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetMatchesByVendorMatchIdsResponseMultiError(errors)
	}

	return nil
}

// GetMatchesByVendorMatchIdsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetMatchesByVendorMatchIdsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMatchesByVendorMatchIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMatchesByVendorMatchIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMatchesByVendorMatchIdsResponseMultiError) AllErrors() []error { return m }

// GetMatchesByVendorMatchIdsResponseValidationError is the validation error
// returned by GetMatchesByVendorMatchIdsResponse.Validate if the designated
// constraints aren't met.
type GetMatchesByVendorMatchIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMatchesByVendorMatchIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMatchesByVendorMatchIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMatchesByVendorMatchIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMatchesByVendorMatchIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMatchesByVendorMatchIdsResponseValidationError) ErrorName() string {
	return "GetMatchesByVendorMatchIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMatchesByVendorMatchIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMatchesByVendorMatchIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMatchesByVendorMatchIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMatchesByVendorMatchIdsResponseValidationError{}
