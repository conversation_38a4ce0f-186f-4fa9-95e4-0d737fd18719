// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/firefly/billing/service.proto

package billing

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	enums "github.com/epifi/gamma/api/firefly/billing/enums"
	enums1 "github.com/epifi/gamma/api/firefly/enums"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetAutoRepaymentStatusResponse_RequestStatus int32

const (
	GetAutoRepaymentStatusResponse_REQUEST_STATUS_UNSPECIFIED         GetAutoRepaymentStatusResponse_RequestStatus = 0
	GetAutoRepaymentStatusResponse_REQUEST_STATUS_PAYMENT_INITIATED   GetAutoRepaymentStatusResponse_RequestStatus = 1
	GetAutoRepaymentStatusResponse_REQUEST_STATUS_PAYMENT_IN_PROGRESS GetAutoRepaymentStatusResponse_RequestStatus = 2
	GetAutoRepaymentStatusResponse_REQUEST_STATUS_PAYMENT_SUCCESSFUL  GetAutoRepaymentStatusResponse_RequestStatus = 3
	GetAutoRepaymentStatusResponse_REQUEST_STATUS_PAYMENT_FAILED      GetAutoRepaymentStatusResponse_RequestStatus = 4
)

// Enum value maps for GetAutoRepaymentStatusResponse_RequestStatus.
var (
	GetAutoRepaymentStatusResponse_RequestStatus_name = map[int32]string{
		0: "REQUEST_STATUS_UNSPECIFIED",
		1: "REQUEST_STATUS_PAYMENT_INITIATED",
		2: "REQUEST_STATUS_PAYMENT_IN_PROGRESS",
		3: "REQUEST_STATUS_PAYMENT_SUCCESSFUL",
		4: "REQUEST_STATUS_PAYMENT_FAILED",
	}
	GetAutoRepaymentStatusResponse_RequestStatus_value = map[string]int32{
		"REQUEST_STATUS_UNSPECIFIED":         0,
		"REQUEST_STATUS_PAYMENT_INITIATED":   1,
		"REQUEST_STATUS_PAYMENT_IN_PROGRESS": 2,
		"REQUEST_STATUS_PAYMENT_SUCCESSFUL":  3,
		"REQUEST_STATUS_PAYMENT_FAILED":      4,
	}
)

func (x GetAutoRepaymentStatusResponse_RequestStatus) Enum() *GetAutoRepaymentStatusResponse_RequestStatus {
	p := new(GetAutoRepaymentStatusResponse_RequestStatus)
	*p = x
	return p
}

func (x GetAutoRepaymentStatusResponse_RequestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAutoRepaymentStatusResponse_RequestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_billing_service_proto_enumTypes[0].Descriptor()
}

func (GetAutoRepaymentStatusResponse_RequestStatus) Type() protoreflect.EnumType {
	return &file_api_firefly_billing_service_proto_enumTypes[0]
}

func (x GetAutoRepaymentStatusResponse_RequestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAutoRepaymentStatusResponse_RequestStatus.Descriptor instead.
func (GetAutoRepaymentStatusResponse_RequestStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{5, 0}
}

type GetCreditCardBillsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetCreditCardBillsRequest) Reset() {
	*x = GetCreditCardBillsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardBillsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardBillsRequest) ProtoMessage() {}

func (x *GetCreditCardBillsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardBillsRequest.ProtoReflect.Descriptor instead.
func (*GetCreditCardBillsRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetCreditCardBillsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetCreditCardBillsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status          *rpc.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CreditCardBills []*CreditCardBill `protobuf:"bytes,2,rep,name=credit_card_bills,json=creditCardBills,proto3" json:"credit_card_bills,omitempty"`
}

func (x *GetCreditCardBillsResponse) Reset() {
	*x = GetCreditCardBillsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardBillsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardBillsResponse) ProtoMessage() {}

func (x *GetCreditCardBillsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardBillsResponse.ProtoReflect.Descriptor instead.
func (*GetCreditCardBillsResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetCreditCardBillsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCreditCardBillsResponse) GetCreditCardBills() []*CreditCardBill {
	if x != nil {
		return x.CreditCardBills
	}
	return nil
}

type UpdateStatementDateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// card id for which the statement date has to be updated .
	CardId          string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	NewStatementDay int32  `protobuf:"varint,2,opt,name=new_statement_day,json=newStatementDay,proto3" json:"new_statement_day,omitempty"`
	NewDueDate      int32  `protobuf:"varint,3,opt,name=new_due_date,json=newDueDate,proto3" json:"new_due_date,omitempty"`
}

func (x *UpdateStatementDateRequest) Reset() {
	*x = UpdateStatementDateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStatementDateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStatementDateRequest) ProtoMessage() {}

func (x *UpdateStatementDateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStatementDateRequest.ProtoReflect.Descriptor instead.
func (*UpdateStatementDateRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateStatementDateRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *UpdateStatementDateRequest) GetNewStatementDay() int32 {
	if x != nil {
		return x.NewStatementDay
	}
	return 0
}

func (x *UpdateStatementDateRequest) GetNewDueDate() int32 {
	if x != nil {
		return x.NewDueDate
	}
	return 0
}

type UpdateStatementDateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NextAction *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *UpdateStatementDateResponse) Reset() {
	*x = UpdateStatementDateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStatementDateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStatementDateResponse) ProtoMessage() {}

func (x *UpdateStatementDateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStatementDateResponse.ProtoReflect.Descriptor instead.
func (*UpdateStatementDateResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateStatementDateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateStatementDateResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type GetAutoRepaymentStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier of a particular repayment flow
	ClientReqId string `protobuf:"bytes,1,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
}

func (x *GetAutoRepaymentStatusRequest) Reset() {
	*x = GetAutoRepaymentStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAutoRepaymentStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAutoRepaymentStatusRequest) ProtoMessage() {}

func (x *GetAutoRepaymentStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAutoRepaymentStatusRequest.ProtoReflect.Descriptor instead.
func (*GetAutoRepaymentStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetAutoRepaymentStatusRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

type GetAutoRepaymentStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// unique identifier of the repayment request which can be used in the
	// next call
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	// status of the payment request
	RequestStatus GetAutoRepaymentStatusResponse_RequestStatus `protobuf:"varint,3,opt,name=request_status,json=requestStatus,proto3,enum=firefly.billing.GetAutoRepaymentStatusResponse_RequestStatus" json:"request_status,omitempty"`
}

func (x *GetAutoRepaymentStatusResponse) Reset() {
	*x = GetAutoRepaymentStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAutoRepaymentStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAutoRepaymentStatusResponse) ProtoMessage() {}

func (x *GetAutoRepaymentStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAutoRepaymentStatusResponse.ProtoReflect.Descriptor instead.
func (*GetAutoRepaymentStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetAutoRepaymentStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAutoRepaymentStatusResponse) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *GetAutoRepaymentStatusResponse) GetRequestStatus() GetAutoRepaymentStatusResponse_RequestStatus {
	if x != nil {
		return x.RequestStatus
	}
	return GetAutoRepaymentStatusResponse_REQUEST_STATUS_UNSPECIFIED
}

type InitiateAutoRepaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id of the user for whom the credit card repayment is being done
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// amount of money for which the repayment has to be done
	Amount            *money.Money            `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	PaymentProvenance enums.PaymentProvenance `protobuf:"varint,3,opt,name=payment_provenance,json=paymentProvenance,proto3,enum=firefly.billing.enums.PaymentProvenance" json:"payment_provenance,omitempty"`
	// id to poll the payment status from the external service
	ClientReqId string `protobuf:"bytes,4,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
}

func (x *InitiateAutoRepaymentRequest) Reset() {
	*x = InitiateAutoRepaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateAutoRepaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateAutoRepaymentRequest) ProtoMessage() {}

func (x *InitiateAutoRepaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateAutoRepaymentRequest.ProtoReflect.Descriptor instead.
func (*InitiateAutoRepaymentRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{6}
}

func (x *InitiateAutoRepaymentRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *InitiateAutoRepaymentRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *InitiateAutoRepaymentRequest) GetPaymentProvenance() enums.PaymentProvenance {
	if x != nil {
		return x.PaymentProvenance
	}
	return enums.PaymentProvenance(0)
}

func (x *InitiateAutoRepaymentRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

type InitiateAutoRepaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// id that will be used to poll the status of the fund transfer that has to be done
	ClientRequestId string `protobuf:"bytes,2,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
}

func (x *InitiateAutoRepaymentResponse) Reset() {
	*x = InitiateAutoRepaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateAutoRepaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateAutoRepaymentResponse) ProtoMessage() {}

func (x *InitiateAutoRepaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateAutoRepaymentResponse.ProtoReflect.Descriptor instead.
func (*InitiateAutoRepaymentResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{7}
}

func (x *InitiateAutoRepaymentResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *InitiateAutoRepaymentResponse) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

type FetchAllBillAndBillPaymentsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *FetchAllBillAndBillPaymentsRequest) Reset() {
	*x = FetchAllBillAndBillPaymentsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchAllBillAndBillPaymentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchAllBillAndBillPaymentsRequest) ProtoMessage() {}

func (x *FetchAllBillAndBillPaymentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchAllBillAndBillPaymentsRequest.ProtoReflect.Descriptor instead.
func (*FetchAllBillAndBillPaymentsRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{8}
}

func (x *FetchAllBillAndBillPaymentsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type FetchAllBillAndBillPaymentsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PaymentInfos []*CreditCardPaymentInfo `protobuf:"bytes,2,rep,name=payment_infos,json=paymentInfos,proto3" json:"payment_infos,omitempty"`
	CardBills    []*CreditCardBill        `protobuf:"bytes,3,rep,name=card_bills,json=cardBills,proto3" json:"card_bills,omitempty"`
}

func (x *FetchAllBillAndBillPaymentsResponse) Reset() {
	*x = FetchAllBillAndBillPaymentsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchAllBillAndBillPaymentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchAllBillAndBillPaymentsResponse) ProtoMessage() {}

func (x *FetchAllBillAndBillPaymentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchAllBillAndBillPaymentsResponse.ProtoReflect.Descriptor instead.
func (*FetchAllBillAndBillPaymentsResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{9}
}

func (x *FetchAllBillAndBillPaymentsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FetchAllBillAndBillPaymentsResponse) GetPaymentInfos() []*CreditCardPaymentInfo {
	if x != nil {
		return x.PaymentInfos
	}
	return nil
}

func (x *FetchAllBillAndBillPaymentsResponse) GetCardBills() []*CreditCardBill {
	if x != nil {
		return x.CardBills
	}
	return nil
}

type GetCreditCardBillRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to GetBy:
	//
	//	*GetCreditCardBillRequest_ActorIdAndStatementDate_
	//	*GetCreditCardBillRequest_BillId
	//	*GetCreditCardBillRequest_ActorId
	GetBy isGetCreditCardBillRequest_GetBy `protobuf_oneof:"GetBy"`
}

func (x *GetCreditCardBillRequest) Reset() {
	*x = GetCreditCardBillRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardBillRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardBillRequest) ProtoMessage() {}

func (x *GetCreditCardBillRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardBillRequest.ProtoReflect.Descriptor instead.
func (*GetCreditCardBillRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{10}
}

func (m *GetCreditCardBillRequest) GetGetBy() isGetCreditCardBillRequest_GetBy {
	if m != nil {
		return m.GetBy
	}
	return nil
}

func (x *GetCreditCardBillRequest) GetActorIdAndStatementDate() *GetCreditCardBillRequest_ActorIdAndStatementDate {
	if x, ok := x.GetGetBy().(*GetCreditCardBillRequest_ActorIdAndStatementDate_); ok {
		return x.ActorIdAndStatementDate
	}
	return nil
}

func (x *GetCreditCardBillRequest) GetBillId() string {
	if x, ok := x.GetGetBy().(*GetCreditCardBillRequest_BillId); ok {
		return x.BillId
	}
	return ""
}

func (x *GetCreditCardBillRequest) GetActorId() string {
	if x, ok := x.GetGetBy().(*GetCreditCardBillRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

type isGetCreditCardBillRequest_GetBy interface {
	isGetCreditCardBillRequest_GetBy()
}

type GetCreditCardBillRequest_ActorIdAndStatementDate_ struct {
	ActorIdAndStatementDate *GetCreditCardBillRequest_ActorIdAndStatementDate `protobuf:"bytes,1,opt,name=actor_id_and_statement_date,json=actorIdAndStatementDate,proto3,oneof"`
}

type GetCreditCardBillRequest_BillId struct {
	BillId string `protobuf:"bytes,2,opt,name=bill_id,json=billId,proto3,oneof"`
}

type GetCreditCardBillRequest_ActorId struct {
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3,oneof"`
}

func (*GetCreditCardBillRequest_ActorIdAndStatementDate_) isGetCreditCardBillRequest_GetBy() {}

func (*GetCreditCardBillRequest_BillId) isGetCreditCardBillRequest_GetBy() {}

func (*GetCreditCardBillRequest_ActorId) isGetCreditCardBillRequest_GetBy() {}

type GetCreditCardBillResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CreditCardBill *CreditCardBill `protobuf:"bytes,2,opt,name=credit_card_bill,json=creditCardBill,proto3" json:"credit_card_bill,omitempty"`
	BillWindow     *BillWindow     `protobuf:"bytes,3,opt,name=bill_window,json=billWindow,proto3" json:"bill_window,omitempty"`
}

func (x *GetCreditCardBillResponse) Reset() {
	*x = GetCreditCardBillResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardBillResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardBillResponse) ProtoMessage() {}

func (x *GetCreditCardBillResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardBillResponse.ProtoReflect.Descriptor instead.
func (*GetCreditCardBillResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetCreditCardBillResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCreditCardBillResponse) GetCreditCardBill() *CreditCardBill {
	if x != nil {
		return x.CreditCardBill
	}
	return nil
}

func (x *GetCreditCardBillResponse) GetBillWindow() *BillWindow {
	if x != nil {
		return x.BillWindow
	}
	return nil
}

type BillWindow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// from date for the bill
	// deprecated in favour of from_timestamp
	//
	// Deprecated: Marked as deprecated in api/firefly/billing/service.proto.
	FromDate *date.Date `protobuf:"bytes,1,opt,name=from_date,json=fromDate,proto3" json:"from_date,omitempty"`
	// to date for the bill
	// deprecated in favour of to_timestamp
	//
	// Deprecated: Marked as deprecated in api/firefly/billing/service.proto.
	ToDate *date.Date `protobuf:"bytes,2,opt,name=to_date,json=toDate,proto3" json:"to_date,omitempty"`
	// from date time stamp for the bill
	FromTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=from_timestamp,json=fromTimestamp,proto3" json:"from_timestamp,omitempty"`
	// to date time stamp for the bill
	ToTimestamp *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=to_timestamp,json=toTimestamp,proto3" json:"to_timestamp,omitempty"`
}

func (x *BillWindow) Reset() {
	*x = BillWindow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BillWindow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillWindow) ProtoMessage() {}

func (x *BillWindow) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillWindow.ProtoReflect.Descriptor instead.
func (*BillWindow) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{12}
}

// Deprecated: Marked as deprecated in api/firefly/billing/service.proto.
func (x *BillWindow) GetFromDate() *date.Date {
	if x != nil {
		return x.FromDate
	}
	return nil
}

// Deprecated: Marked as deprecated in api/firefly/billing/service.proto.
func (x *BillWindow) GetToDate() *date.Date {
	if x != nil {
		return x.ToDate
	}
	return nil
}

func (x *BillWindow) GetFromTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTimestamp
	}
	return nil
}

func (x *BillWindow) GetToTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ToTimestamp
	}
	return nil
}

type CreateCreditCardBillRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreditCardBill *CreditCardBill `protobuf:"bytes,2,opt,name=credit_card_bill,json=creditCardBill,proto3" json:"credit_card_bill,omitempty"`
}

func (x *CreateCreditCardBillRequest) Reset() {
	*x = CreateCreditCardBillRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCreditCardBillRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCreditCardBillRequest) ProtoMessage() {}

func (x *CreateCreditCardBillRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCreditCardBillRequest.ProtoReflect.Descriptor instead.
func (*CreateCreditCardBillRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{13}
}

func (x *CreateCreditCardBillRequest) GetCreditCardBill() *CreditCardBill {
	if x != nil {
		return x.CreditCardBill
	}
	return nil
}

type CreateCreditCardBillResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CreditCardBill *CreditCardBill `protobuf:"bytes,2,opt,name=credit_card_bill,json=creditCardBill,proto3" json:"credit_card_bill,omitempty"`
}

func (x *CreateCreditCardBillResponse) Reset() {
	*x = CreateCreditCardBillResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCreditCardBillResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCreditCardBillResponse) ProtoMessage() {}

func (x *CreateCreditCardBillResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCreditCardBillResponse.ProtoReflect.Descriptor instead.
func (*CreateCreditCardBillResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{14}
}

func (x *CreateCreditCardBillResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateCreditCardBillResponse) GetCreditCardBill() *CreditCardBill {
	if x != nil {
		return x.CreditCardBill
	}
	return nil
}

type GetBillAndBillPaymentInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardId  string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Filter for payment status to be returned in firefly.
	PaymentStatuses []enums1.TransactionStatus `protobuf:"varint,3,rep,packed,name=payment_statuses,json=paymentStatuses,proto3,enum=firefly.enums.TransactionStatus" json:"payment_statuses,omitempty"`
}

func (x *GetBillAndBillPaymentInfoRequest) Reset() {
	*x = GetBillAndBillPaymentInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBillAndBillPaymentInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBillAndBillPaymentInfoRequest) ProtoMessage() {}

func (x *GetBillAndBillPaymentInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBillAndBillPaymentInfoRequest.ProtoReflect.Descriptor instead.
func (*GetBillAndBillPaymentInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetBillAndBillPaymentInfoRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *GetBillAndBillPaymentInfoRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetBillAndBillPaymentInfoRequest) GetPaymentStatuses() []enums1.TransactionStatus {
	if x != nil {
		return x.PaymentStatuses
	}
	return nil
}

type GetBillAndBillPaymentInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// latest bill entry for the user
	LatestBill *CreditCardBill `protobuf:"bytes,2,opt,name=latest_bill,json=latestBill,proto3" json:"latest_bill,omitempty"`
	// will be null if no payment attempts have been made or no payment with given status are found.
	Payments []*CreditCardPaymentInfo `protobuf:"bytes,3,rep,name=payments,proto3" json:"payments,omitempty"`
}

func (x *GetBillAndBillPaymentInfoResponse) Reset() {
	*x = GetBillAndBillPaymentInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBillAndBillPaymentInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBillAndBillPaymentInfoResponse) ProtoMessage() {}

func (x *GetBillAndBillPaymentInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBillAndBillPaymentInfoResponse.ProtoReflect.Descriptor instead.
func (*GetBillAndBillPaymentInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetBillAndBillPaymentInfoResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetBillAndBillPaymentInfoResponse) GetLatestBill() *CreditCardBill {
	if x != nil {
		return x.LatestBill
	}
	return nil
}

func (x *GetBillAndBillPaymentInfoResponse) GetPayments() []*CreditCardPaymentInfo {
	if x != nil {
		return x.Payments
	}
	return nil
}

type GetUpcomingBillWindowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor for which upcoming bill window has to be fetched
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetUpcomingBillWindowRequest) Reset() {
	*x = GetUpcomingBillWindowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUpcomingBillWindowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUpcomingBillWindowRequest) ProtoMessage() {}

func (x *GetUpcomingBillWindowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUpcomingBillWindowRequest.ProtoReflect.Descriptor instead.
func (*GetUpcomingBillWindowRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetUpcomingBillWindowRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetUpcomingBillWindowResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                   *rpc.Status                                              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	BillWindowWithAccountIds []*GetUpcomingBillWindowResponse_BillWindowWithAccountId `protobuf:"bytes,2,rep,name=bill_window_with_account_ids,json=billWindowWithAccountIds,proto3" json:"bill_window_with_account_ids,omitempty"`
}

func (x *GetUpcomingBillWindowResponse) Reset() {
	*x = GetUpcomingBillWindowResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUpcomingBillWindowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUpcomingBillWindowResponse) ProtoMessage() {}

func (x *GetUpcomingBillWindowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUpcomingBillWindowResponse.ProtoReflect.Descriptor instead.
func (*GetUpcomingBillWindowResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetUpcomingBillWindowResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetUpcomingBillWindowResponse) GetBillWindowWithAccountIds() []*GetUpcomingBillWindowResponse_BillWindowWithAccountId {
	if x != nil {
		return x.BillWindowWithAccountIds
	}
	return nil
}

type UpsertCreditCardPaymentInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FieldMasks                []enums.CreditCardPaymentInfoFieldMask `protobuf:"varint,1,rep,packed,name=field_masks,json=fieldMasks,proto3,enum=firefly.billing.enums.CreditCardPaymentInfoFieldMask" json:"field_masks,omitempty"`
	CreditCardBillPaymentInfo *CreditCardPaymentInfo                 `protobuf:"bytes,2,opt,name=credit_card_bill_payment_info,json=creditCardBillPaymentInfo,proto3" json:"credit_card_bill_payment_info,omitempty"`
	// One of get by will be used to uniquely determine the credit card payment info before updation/creation.
	//
	// Types that are assignable to Identifier:
	//
	//	*UpsertCreditCardPaymentInfoRequest_OrderId
	//	*UpsertCreditCardPaymentInfoRequest_ExternalTxnId
	Identifier isUpsertCreditCardPaymentInfoRequest_Identifier `protobuf_oneof:"Identifier"`
	// Reference id is used to fetch due information from vendor
	ReferenceId string `protobuf:"bytes,6,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// Actor id is used to fetch the bill in case we want to link the payment info to a bill
	ActorId string `protobuf:"bytes,7,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *UpsertCreditCardPaymentInfoRequest) Reset() {
	*x = UpsertCreditCardPaymentInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertCreditCardPaymentInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertCreditCardPaymentInfoRequest) ProtoMessage() {}

func (x *UpsertCreditCardPaymentInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertCreditCardPaymentInfoRequest.ProtoReflect.Descriptor instead.
func (*UpsertCreditCardPaymentInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{19}
}

func (x *UpsertCreditCardPaymentInfoRequest) GetFieldMasks() []enums.CreditCardPaymentInfoFieldMask {
	if x != nil {
		return x.FieldMasks
	}
	return nil
}

func (x *UpsertCreditCardPaymentInfoRequest) GetCreditCardBillPaymentInfo() *CreditCardPaymentInfo {
	if x != nil {
		return x.CreditCardBillPaymentInfo
	}
	return nil
}

func (m *UpsertCreditCardPaymentInfoRequest) GetIdentifier() isUpsertCreditCardPaymentInfoRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *UpsertCreditCardPaymentInfoRequest) GetOrderId() string {
	if x, ok := x.GetIdentifier().(*UpsertCreditCardPaymentInfoRequest_OrderId); ok {
		return x.OrderId
	}
	return ""
}

func (x *UpsertCreditCardPaymentInfoRequest) GetExternalTxnId() string {
	if x, ok := x.GetIdentifier().(*UpsertCreditCardPaymentInfoRequest_ExternalTxnId); ok {
		return x.ExternalTxnId
	}
	return ""
}

func (x *UpsertCreditCardPaymentInfoRequest) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *UpsertCreditCardPaymentInfoRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type isUpsertCreditCardPaymentInfoRequest_Identifier interface {
	isUpsertCreditCardPaymentInfoRequest_Identifier()
}

type UpsertCreditCardPaymentInfoRequest_OrderId struct {
	// Order id is the id of the order created on pay's end during payment
	OrderId string `protobuf:"bytes,4,opt,name=order_id,json=orderId,proto3,oneof"`
}

type UpsertCreditCardPaymentInfoRequest_ExternalTxnId struct {
	// External txn id is the txn id sent to a vendor and acts as a binding id between federal's txn record and vendors txn record
	ExternalTxnId string `protobuf:"bytes,5,opt,name=external_txn_id,json=externalTxnId,proto3,oneof"`
}

func (*UpsertCreditCardPaymentInfoRequest_OrderId) isUpsertCreditCardPaymentInfoRequest_Identifier() {
}

func (*UpsertCreditCardPaymentInfoRequest_ExternalTxnId) isUpsertCreditCardPaymentInfoRequest_Identifier() {
}

type UpsertCreditCardPaymentInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CreditCardPaymentInfo *CreditCardPaymentInfo `protobuf:"bytes,2,opt,name=credit_card_payment_info,json=creditCardPaymentInfo,proto3" json:"credit_card_payment_info,omitempty"`
}

func (x *UpsertCreditCardPaymentInfoResponse) Reset() {
	*x = UpsertCreditCardPaymentInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertCreditCardPaymentInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertCreditCardPaymentInfoResponse) ProtoMessage() {}

func (x *UpsertCreditCardPaymentInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertCreditCardPaymentInfoResponse.ProtoReflect.Descriptor instead.
func (*UpsertCreditCardPaymentInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{20}
}

func (x *UpsertCreditCardPaymentInfoResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpsertCreditCardPaymentInfoResponse) GetCreditCardPaymentInfo() *CreditCardPaymentInfo {
	if x != nil {
		return x.CreditCardPaymentInfo
	}
	return nil
}

type ProcessVendorRepaymentNotificationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExternalTxnId     string                   `protobuf:"bytes,1,opt,name=external_txn_id,json=externalTxnId,proto3" json:"external_txn_id,omitempty"`
	TransactionStatus enums1.TransactionStatus `protobuf:"varint,3,opt,name=transaction_status,json=transactionStatus,proto3,enum=firefly.enums.TransactionStatus" json:"transaction_status,omitempty"`
	// account id which can be used to add a dedupe in case the external_txn_id duplicates itself
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
}

func (x *ProcessVendorRepaymentNotificationRequest) Reset() {
	*x = ProcessVendorRepaymentNotificationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessVendorRepaymentNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessVendorRepaymentNotificationRequest) ProtoMessage() {}

func (x *ProcessVendorRepaymentNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessVendorRepaymentNotificationRequest.ProtoReflect.Descriptor instead.
func (*ProcessVendorRepaymentNotificationRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{21}
}

func (x *ProcessVendorRepaymentNotificationRequest) GetExternalTxnId() string {
	if x != nil {
		return x.ExternalTxnId
	}
	return ""
}

func (x *ProcessVendorRepaymentNotificationRequest) GetTransactionStatus() enums1.TransactionStatus {
	if x != nil {
		return x.TransactionStatus
	}
	return enums1.TransactionStatus(0)
}

func (x *ProcessVendorRepaymentNotificationRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type ProcessVendorRepaymentNotificationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ProcessVendorRepaymentNotificationResponse) Reset() {
	*x = ProcessVendorRepaymentNotificationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessVendorRepaymentNotificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessVendorRepaymentNotificationResponse) ProtoMessage() {}

func (x *ProcessVendorRepaymentNotificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessVendorRepaymentNotificationResponse.ProtoReflect.Descriptor instead.
func (*ProcessVendorRepaymentNotificationResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{22}
}

func (x *ProcessVendorRepaymentNotificationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetBillInfoForTransactionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetBillInfoForTransactionRequest_ExternalTxnId
	Identifier isGetBillInfoForTransactionRequest_Identifier `protobuf_oneof:"Identifier"`
}

func (x *GetBillInfoForTransactionRequest) Reset() {
	*x = GetBillInfoForTransactionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBillInfoForTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBillInfoForTransactionRequest) ProtoMessage() {}

func (x *GetBillInfoForTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBillInfoForTransactionRequest.ProtoReflect.Descriptor instead.
func (*GetBillInfoForTransactionRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{23}
}

func (m *GetBillInfoForTransactionRequest) GetIdentifier() isGetBillInfoForTransactionRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetBillInfoForTransactionRequest) GetExternalTxnId() string {
	if x, ok := x.GetIdentifier().(*GetBillInfoForTransactionRequest_ExternalTxnId); ok {
		return x.ExternalTxnId
	}
	return ""
}

type isGetBillInfoForTransactionRequest_Identifier interface {
	isGetBillInfoForTransactionRequest_Identifier()
}

type GetBillInfoForTransactionRequest_ExternalTxnId struct {
	ExternalTxnId string `protobuf:"bytes,1,opt,name=external_txn_id,json=externalTxnId,proto3,oneof"`
}

func (*GetBillInfoForTransactionRequest_ExternalTxnId) isGetBillInfoForTransactionRequest_Identifier() {
}

type GetBillInfoForTransactionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	BillId     string      `protobuf:"bytes,2,opt,name=bill_id,json=billId,proto3" json:"bill_id,omitempty"`
	BillWindow *BillWindow `protobuf:"bytes,3,opt,name=bill_window,json=billWindow,proto3" json:"bill_window,omitempty"`
}

func (x *GetBillInfoForTransactionResponse) Reset() {
	*x = GetBillInfoForTransactionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBillInfoForTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBillInfoForTransactionResponse) ProtoMessage() {}

func (x *GetBillInfoForTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBillInfoForTransactionResponse.ProtoReflect.Descriptor instead.
func (*GetBillInfoForTransactionResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetBillInfoForTransactionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetBillInfoForTransactionResponse) GetBillId() string {
	if x != nil {
		return x.BillId
	}
	return ""
}

func (x *GetBillInfoForTransactionResponse) GetBillWindow() *BillWindow {
	if x != nil {
		return x.BillWindow
	}
	return nil
}

type UpdateCreditCardBillRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FieldMasks     []enums.CreditCardBillFieldMask `protobuf:"varint,1,rep,packed,name=field_masks,json=fieldMasks,proto3,enum=firefly.billing.enums.CreditCardBillFieldMask" json:"field_masks,omitempty"`
	CreditCardBill *CreditCardBill                 `protobuf:"bytes,2,opt,name=credit_card_bill,json=creditCardBill,proto3" json:"credit_card_bill,omitempty"`
}

func (x *UpdateCreditCardBillRequest) Reset() {
	*x = UpdateCreditCardBillRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCreditCardBillRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCreditCardBillRequest) ProtoMessage() {}

func (x *UpdateCreditCardBillRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCreditCardBillRequest.ProtoReflect.Descriptor instead.
func (*UpdateCreditCardBillRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateCreditCardBillRequest) GetFieldMasks() []enums.CreditCardBillFieldMask {
	if x != nil {
		return x.FieldMasks
	}
	return nil
}

func (x *UpdateCreditCardBillRequest) GetCreditCardBill() *CreditCardBill {
	if x != nil {
		return x.CreditCardBill
	}
	return nil
}

type UpdateCreditCardBillResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateCreditCardBillResponse) Reset() {
	*x = UpdateCreditCardBillResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCreditCardBillResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCreditCardBillResponse) ProtoMessage() {}

func (x *UpdateCreditCardBillResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCreditCardBillResponse.ProtoReflect.Descriptor instead.
func (*UpdateCreditCardBillResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{26}
}

func (x *UpdateCreditCardBillResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetPreviousBillIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// id of bill whose previous bill id needs to be fetched
	BillId string `protobuf:"bytes,2,opt,name=bill_id,json=billId,proto3" json:"bill_id,omitempty"`
}

func (x *GetPreviousBillIdRequest) Reset() {
	*x = GetPreviousBillIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPreviousBillIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPreviousBillIdRequest) ProtoMessage() {}

func (x *GetPreviousBillIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPreviousBillIdRequest.ProtoReflect.Descriptor instead.
func (*GetPreviousBillIdRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetPreviousBillIdRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetPreviousBillIdRequest) GetBillId() string {
	if x != nil {
		return x.BillId
	}
	return ""
}

type GetPreviousBillIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PreviousBillId string      `protobuf:"bytes,2,opt,name=previous_bill_id,json=previousBillId,proto3" json:"previous_bill_id,omitempty"`
}

func (x *GetPreviousBillIdResponse) Reset() {
	*x = GetPreviousBillIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPreviousBillIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPreviousBillIdResponse) ProtoMessage() {}

func (x *GetPreviousBillIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPreviousBillIdResponse.ProtoReflect.Descriptor instead.
func (*GetPreviousBillIdResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{28}
}

func (x *GetPreviousBillIdResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPreviousBillIdResponse) GetPreviousBillId() string {
	if x != nil {
		return x.PreviousBillId
	}
	return ""
}

type GetCreditCardBillRequest_ActorIdAndStatementDate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId       string                 `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	StatementDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=statement_date,json=statementDate,proto3" json:"statement_date,omitempty"`
}

func (x *GetCreditCardBillRequest_ActorIdAndStatementDate) Reset() {
	*x = GetCreditCardBillRequest_ActorIdAndStatementDate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardBillRequest_ActorIdAndStatementDate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardBillRequest_ActorIdAndStatementDate) ProtoMessage() {}

func (x *GetCreditCardBillRequest_ActorIdAndStatementDate) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardBillRequest_ActorIdAndStatementDate.ProtoReflect.Descriptor instead.
func (*GetCreditCardBillRequest_ActorIdAndStatementDate) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{10, 0}
}

func (x *GetCreditCardBillRequest_ActorIdAndStatementDate) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetCreditCardBillRequest_ActorIdAndStatementDate) GetStatementDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StatementDate
	}
	return nil
}

type GetUpcomingBillWindowResponse_BillWindowWithAccountId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// upcoming bill window for the user
	UpcomingBillWindow *BillWindow `protobuf:"bytes,1,opt,name=upcoming_bill_window,json=upcomingBillWindow,proto3" json:"upcoming_bill_window,omitempty"`
	// credit account id
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
}

func (x *GetUpcomingBillWindowResponse_BillWindowWithAccountId) Reset() {
	*x = GetUpcomingBillWindowResponse_BillWindowWithAccountId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUpcomingBillWindowResponse_BillWindowWithAccountId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUpcomingBillWindowResponse_BillWindowWithAccountId) ProtoMessage() {}

func (x *GetUpcomingBillWindowResponse_BillWindowWithAccountId) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUpcomingBillWindowResponse_BillWindowWithAccountId.ProtoReflect.Descriptor instead.
func (*GetUpcomingBillWindowResponse_BillWindowWithAccountId) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_service_proto_rawDescGZIP(), []int{18, 0}
}

func (x *GetUpcomingBillWindowResponse_BillWindowWithAccountId) GetUpcomingBillWindow() *BillWindow {
	if x != nil {
		return x.UpcomingBillWindow
	}
	return nil
}

func (x *GetUpcomingBillWindowResponse_BillWindowWithAccountId) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

var File_api_firefly_billing_service_proto protoreflect.FileDescriptor

var file_api_firefly_billing_service_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x36,
	0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42,
	0x69, 0x6c, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x8e, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x11, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x0f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x73, 0x22, 0x83, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x11, 0x6e, 0x65, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x64, 0x61, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6e, 0x65, 0x77, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x79, 0x12, 0x20, 0x0a, 0x0c, 0x6e,
	0x65, 0x77, 0x5f, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x6e, 0x65, 0x77, 0x44, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x22, 0x80, 0x01,
	0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x4c, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2b, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x22, 0x99,
	0x03, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x64, 0x0a, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0xc7, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x49,
	0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02,
	0x12, 0x25, 0x0a, 0x21, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x22, 0xeb, 0x01, 0x0a, 0x1c, 0x49,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x57, 0x0a, 0x12, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e,
	0x61, 0x6e, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72,
	0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x22, 0x70, 0x0a, 0x1d, 0x49, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a,
	0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x3f, 0x0a, 0x22, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x41, 0x6c, 0x6c, 0x42, 0x69, 0x6c, 0x6c, 0x41, 0x6e, 0x64, 0x42, 0x69, 0x6c,
	0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xd7, 0x01, 0x0a, 0x23,
	0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x6c, 0x6c, 0x42, 0x69, 0x6c, 0x6c, 0x41, 0x6e, 0x64, 0x42,
	0x69, 0x6c, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x0d, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x3e, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x62, 0x69,
	0x6c, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x09, 0x63, 0x61, 0x72, 0x64,
	0x42, 0x69, 0x6c, 0x6c, 0x73, 0x22, 0xd8, 0x02, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x81, 0x01, 0x0a, 0x1b, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x5f,
	0x61, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x41, 0x6e, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x17, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x41, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x1a, 0x77,
	0x0a, 0x17, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x41, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x47, 0x65, 0x74, 0x42, 0x79,
	0x22, 0xc9, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x10, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x0e,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x12, 0x3c,
	0x0a, 0x0b, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77,
	0x52, 0x0a, 0x62, 0x69, 0x6c, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x22, 0xf2, 0x01, 0x0a,
	0x0a, 0x42, 0x69, 0x6c, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x32, 0x0a, 0x09, 0x66,
	0x72, 0x6f, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x2e, 0x0a, 0x07, 0x74, 0x6f, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x74, 0x6f, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x41, 0x0a, 0x0e, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0d, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x3d, 0x0a, 0x0c, 0x74, 0x6f, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x74, 0x6f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x22, 0x68, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x49, 0x0a, 0x10, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x62, 0x69, 0x6c, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x0e, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x22, 0x8e, 0x01, 0x0a, 0x1c,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x42, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x49, 0x0a, 0x10, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x0e, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x22, 0xa3, 0x01, 0x0a,
	0x20, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x41, 0x6e, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x20, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x65, 0x73, 0x22, 0xce, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x41, 0x6e,
	0x64, 0x42, 0x69, 0x6c, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a,
	0x0b, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42,
	0x69, 0x6c, 0x6c, 0x52, 0x0a, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x12,
	0x42, 0x0a, 0x08, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x22, 0x39, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x42, 0x69, 0x6c, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xd7,
	0x02, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x42, 0x69,
	0x6c, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x86, 0x01, 0x0a, 0x1c, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x77,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x42, 0x69, 0x6c, 0x6c, 0x57, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x6c,
	0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x52, 0x18, 0x62, 0x69, 0x6c, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77,
	0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x1a, 0x87,
	0x01, 0x0a, 0x17, 0x42, 0x69, 0x6c, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x57, 0x69, 0x74,
	0x68, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x4d, 0x0a, 0x14, 0x75, 0x70,
	0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x77, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x57,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x52, 0x12, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x42,
	0x69, 0x6c, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xf9, 0x02, 0x0a, 0x22, 0x55, 0x70, 0x73,
	0x65, 0x72, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x56, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x68, 0x0a, 0x1d, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x19, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1b, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28,
	0x0a, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x54, 0x78, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x22, 0xab, 0x01, 0x0a, 0x23, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x5f, 0x0a, 0x18, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x15, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0xc3, 0x01, 0x0a, 0x29, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x56, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x26, 0x0a, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x78, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x54, 0x78, 0x6e, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x51, 0x0a, 0x2a, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x5a, 0x0a, 0x20, 0x47,
	0x65, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x6f, 0x72, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x28, 0x0a, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x78, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0d, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x54, 0x78, 0x6e, 0x49, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x9f, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x42,
	0x69, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x6f, 0x72, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0b, 0x62,
	0x69, 0x6c, 0x6c, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x52, 0x0a, 0x62,
	0x69, 0x6c, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x22, 0xb9, 0x01, 0x0a, 0x1b, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69,
	0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4f, 0x0a, 0x0b, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2e,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x42, 0x69, 0x6c, 0x6c, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x49, 0x0a, 0x10, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x0e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x42, 0x69, 0x6c, 0x6c, 0x22, 0x43, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x52, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x42, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x22, 0x6a,
	0x0a, 0x19, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x42, 0x69, 0x6c,
	0x6c, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x28, 0x0a, 0x10, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x62, 0x69, 0x6c,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x76,
	0x69, 0x6f, 0x75, 0x73, 0x42, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x32, 0xdb, 0x0d, 0x0a, 0x07, 0x42,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x6c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x12, 0x29, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x75, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x12, 0x2c, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42,
	0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x82, 0x01, 0x0a, 0x19,
	0x47, 0x65, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x41, 0x6e, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x2e, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x42,
	0x69, 0x6c, 0x6c, 0x41, 0x6e, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x41, 0x6e, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x76, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x42,
	0x69, 0x6c, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x2d, 0x2e, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x55,
	0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x42, 0x69, 0x6c, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f,
	0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x70,
	0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x42, 0x69, 0x6c, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x1b, 0x55, 0x70, 0x73,
	0x65, 0x72, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72,
	0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e,
	0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x9d, 0x01, 0x0a, 0x22, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x56,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x2e, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x1b, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x6c, 0x6c,
	0x42, 0x69, 0x6c, 0x6c, 0x41, 0x6e, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x33, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x6c, 0x6c, 0x42, 0x69,
	0x6c, 0x6c, 0x41, 0x6e, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68,
	0x41, 0x6c, 0x6c, 0x42, 0x69, 0x6c, 0x6c, 0x41, 0x6e, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x82,
	0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x6f,
	0x72, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x6f, 0x72, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x32, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x6f, 0x72,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x12, 0x2c, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69,
	0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6a, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x42, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x29, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x42, 0x69, 0x6c, 0x6c, 0x49,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x42, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x15, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65,
	0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2d, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e,
	0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x49,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x79, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f,
	0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f,
	0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x70, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2b,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6d, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x73, 0x12,
	0x2a, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42,
	0x69, 0x6c, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_firefly_billing_service_proto_rawDescOnce sync.Once
	file_api_firefly_billing_service_proto_rawDescData = file_api_firefly_billing_service_proto_rawDesc
)

func file_api_firefly_billing_service_proto_rawDescGZIP() []byte {
	file_api_firefly_billing_service_proto_rawDescOnce.Do(func() {
		file_api_firefly_billing_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_firefly_billing_service_proto_rawDescData)
	})
	return file_api_firefly_billing_service_proto_rawDescData
}

var file_api_firefly_billing_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_firefly_billing_service_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_api_firefly_billing_service_proto_goTypes = []interface{}{
	(GetAutoRepaymentStatusResponse_RequestStatus)(0),             // 0: firefly.billing.GetAutoRepaymentStatusResponse.RequestStatus
	(*GetCreditCardBillsRequest)(nil),                             // 1: firefly.billing.GetCreditCardBillsRequest
	(*GetCreditCardBillsResponse)(nil),                            // 2: firefly.billing.GetCreditCardBillsResponse
	(*UpdateStatementDateRequest)(nil),                            // 3: firefly.billing.UpdateStatementDateRequest
	(*UpdateStatementDateResponse)(nil),                           // 4: firefly.billing.UpdateStatementDateResponse
	(*GetAutoRepaymentStatusRequest)(nil),                         // 5: firefly.billing.GetAutoRepaymentStatusRequest
	(*GetAutoRepaymentStatusResponse)(nil),                        // 6: firefly.billing.GetAutoRepaymentStatusResponse
	(*InitiateAutoRepaymentRequest)(nil),                          // 7: firefly.billing.InitiateAutoRepaymentRequest
	(*InitiateAutoRepaymentResponse)(nil),                         // 8: firefly.billing.InitiateAutoRepaymentResponse
	(*FetchAllBillAndBillPaymentsRequest)(nil),                    // 9: firefly.billing.FetchAllBillAndBillPaymentsRequest
	(*FetchAllBillAndBillPaymentsResponse)(nil),                   // 10: firefly.billing.FetchAllBillAndBillPaymentsResponse
	(*GetCreditCardBillRequest)(nil),                              // 11: firefly.billing.GetCreditCardBillRequest
	(*GetCreditCardBillResponse)(nil),                             // 12: firefly.billing.GetCreditCardBillResponse
	(*BillWindow)(nil),                                            // 13: firefly.billing.BillWindow
	(*CreateCreditCardBillRequest)(nil),                           // 14: firefly.billing.CreateCreditCardBillRequest
	(*CreateCreditCardBillResponse)(nil),                          // 15: firefly.billing.CreateCreditCardBillResponse
	(*GetBillAndBillPaymentInfoRequest)(nil),                      // 16: firefly.billing.GetBillAndBillPaymentInfoRequest
	(*GetBillAndBillPaymentInfoResponse)(nil),                     // 17: firefly.billing.GetBillAndBillPaymentInfoResponse
	(*GetUpcomingBillWindowRequest)(nil),                          // 18: firefly.billing.GetUpcomingBillWindowRequest
	(*GetUpcomingBillWindowResponse)(nil),                         // 19: firefly.billing.GetUpcomingBillWindowResponse
	(*UpsertCreditCardPaymentInfoRequest)(nil),                    // 20: firefly.billing.UpsertCreditCardPaymentInfoRequest
	(*UpsertCreditCardPaymentInfoResponse)(nil),                   // 21: firefly.billing.UpsertCreditCardPaymentInfoResponse
	(*ProcessVendorRepaymentNotificationRequest)(nil),             // 22: firefly.billing.ProcessVendorRepaymentNotificationRequest
	(*ProcessVendorRepaymentNotificationResponse)(nil),            // 23: firefly.billing.ProcessVendorRepaymentNotificationResponse
	(*GetBillInfoForTransactionRequest)(nil),                      // 24: firefly.billing.GetBillInfoForTransactionRequest
	(*GetBillInfoForTransactionResponse)(nil),                     // 25: firefly.billing.GetBillInfoForTransactionResponse
	(*UpdateCreditCardBillRequest)(nil),                           // 26: firefly.billing.UpdateCreditCardBillRequest
	(*UpdateCreditCardBillResponse)(nil),                          // 27: firefly.billing.UpdateCreditCardBillResponse
	(*GetPreviousBillIdRequest)(nil),                              // 28: firefly.billing.GetPreviousBillIdRequest
	(*GetPreviousBillIdResponse)(nil),                             // 29: firefly.billing.GetPreviousBillIdResponse
	(*GetCreditCardBillRequest_ActorIdAndStatementDate)(nil),      // 30: firefly.billing.GetCreditCardBillRequest.ActorIdAndStatementDate
	(*GetUpcomingBillWindowResponse_BillWindowWithAccountId)(nil), // 31: firefly.billing.GetUpcomingBillWindowResponse.BillWindowWithAccountId
	(*rpc.Status)(nil),                                            // 32: rpc.Status
	(*CreditCardBill)(nil),                                        // 33: firefly.billing.CreditCardBill
	(*deeplink.Deeplink)(nil),                                     // 34: frontend.deeplink.Deeplink
	(*money.Money)(nil),                                           // 35: google.type.Money
	(enums.PaymentProvenance)(0),                                  // 36: firefly.billing.enums.PaymentProvenance
	(*CreditCardPaymentInfo)(nil),                                 // 37: firefly.billing.CreditCardPaymentInfo
	(*date.Date)(nil),                                             // 38: google.type.Date
	(*timestamppb.Timestamp)(nil),                                 // 39: google.protobuf.Timestamp
	(enums1.TransactionStatus)(0),                                 // 40: firefly.enums.TransactionStatus
	(enums.CreditCardPaymentInfoFieldMask)(0),                     // 41: firefly.billing.enums.CreditCardPaymentInfoFieldMask
	(enums.CreditCardBillFieldMask)(0),                            // 42: firefly.billing.enums.CreditCardBillFieldMask
}
var file_api_firefly_billing_service_proto_depIdxs = []int32{
	32, // 0: firefly.billing.GetCreditCardBillsResponse.status:type_name -> rpc.Status
	33, // 1: firefly.billing.GetCreditCardBillsResponse.credit_card_bills:type_name -> firefly.billing.CreditCardBill
	32, // 2: firefly.billing.UpdateStatementDateResponse.status:type_name -> rpc.Status
	34, // 3: firefly.billing.UpdateStatementDateResponse.next_action:type_name -> frontend.deeplink.Deeplink
	32, // 4: firefly.billing.GetAutoRepaymentStatusResponse.status:type_name -> rpc.Status
	0,  // 5: firefly.billing.GetAutoRepaymentStatusResponse.request_status:type_name -> firefly.billing.GetAutoRepaymentStatusResponse.RequestStatus
	35, // 6: firefly.billing.InitiateAutoRepaymentRequest.amount:type_name -> google.type.Money
	36, // 7: firefly.billing.InitiateAutoRepaymentRequest.payment_provenance:type_name -> firefly.billing.enums.PaymentProvenance
	32, // 8: firefly.billing.InitiateAutoRepaymentResponse.status:type_name -> rpc.Status
	32, // 9: firefly.billing.FetchAllBillAndBillPaymentsResponse.status:type_name -> rpc.Status
	37, // 10: firefly.billing.FetchAllBillAndBillPaymentsResponse.payment_infos:type_name -> firefly.billing.CreditCardPaymentInfo
	33, // 11: firefly.billing.FetchAllBillAndBillPaymentsResponse.card_bills:type_name -> firefly.billing.CreditCardBill
	30, // 12: firefly.billing.GetCreditCardBillRequest.actor_id_and_statement_date:type_name -> firefly.billing.GetCreditCardBillRequest.ActorIdAndStatementDate
	32, // 13: firefly.billing.GetCreditCardBillResponse.status:type_name -> rpc.Status
	33, // 14: firefly.billing.GetCreditCardBillResponse.credit_card_bill:type_name -> firefly.billing.CreditCardBill
	13, // 15: firefly.billing.GetCreditCardBillResponse.bill_window:type_name -> firefly.billing.BillWindow
	38, // 16: firefly.billing.BillWindow.from_date:type_name -> google.type.Date
	38, // 17: firefly.billing.BillWindow.to_date:type_name -> google.type.Date
	39, // 18: firefly.billing.BillWindow.from_timestamp:type_name -> google.protobuf.Timestamp
	39, // 19: firefly.billing.BillWindow.to_timestamp:type_name -> google.protobuf.Timestamp
	33, // 20: firefly.billing.CreateCreditCardBillRequest.credit_card_bill:type_name -> firefly.billing.CreditCardBill
	32, // 21: firefly.billing.CreateCreditCardBillResponse.status:type_name -> rpc.Status
	33, // 22: firefly.billing.CreateCreditCardBillResponse.credit_card_bill:type_name -> firefly.billing.CreditCardBill
	40, // 23: firefly.billing.GetBillAndBillPaymentInfoRequest.payment_statuses:type_name -> firefly.enums.TransactionStatus
	32, // 24: firefly.billing.GetBillAndBillPaymentInfoResponse.status:type_name -> rpc.Status
	33, // 25: firefly.billing.GetBillAndBillPaymentInfoResponse.latest_bill:type_name -> firefly.billing.CreditCardBill
	37, // 26: firefly.billing.GetBillAndBillPaymentInfoResponse.payments:type_name -> firefly.billing.CreditCardPaymentInfo
	32, // 27: firefly.billing.GetUpcomingBillWindowResponse.status:type_name -> rpc.Status
	31, // 28: firefly.billing.GetUpcomingBillWindowResponse.bill_window_with_account_ids:type_name -> firefly.billing.GetUpcomingBillWindowResponse.BillWindowWithAccountId
	41, // 29: firefly.billing.UpsertCreditCardPaymentInfoRequest.field_masks:type_name -> firefly.billing.enums.CreditCardPaymentInfoFieldMask
	37, // 30: firefly.billing.UpsertCreditCardPaymentInfoRequest.credit_card_bill_payment_info:type_name -> firefly.billing.CreditCardPaymentInfo
	32, // 31: firefly.billing.UpsertCreditCardPaymentInfoResponse.status:type_name -> rpc.Status
	37, // 32: firefly.billing.UpsertCreditCardPaymentInfoResponse.credit_card_payment_info:type_name -> firefly.billing.CreditCardPaymentInfo
	40, // 33: firefly.billing.ProcessVendorRepaymentNotificationRequest.transaction_status:type_name -> firefly.enums.TransactionStatus
	32, // 34: firefly.billing.ProcessVendorRepaymentNotificationResponse.status:type_name -> rpc.Status
	32, // 35: firefly.billing.GetBillInfoForTransactionResponse.status:type_name -> rpc.Status
	13, // 36: firefly.billing.GetBillInfoForTransactionResponse.bill_window:type_name -> firefly.billing.BillWindow
	42, // 37: firefly.billing.UpdateCreditCardBillRequest.field_masks:type_name -> firefly.billing.enums.CreditCardBillFieldMask
	33, // 38: firefly.billing.UpdateCreditCardBillRequest.credit_card_bill:type_name -> firefly.billing.CreditCardBill
	32, // 39: firefly.billing.UpdateCreditCardBillResponse.status:type_name -> rpc.Status
	32, // 40: firefly.billing.GetPreviousBillIdResponse.status:type_name -> rpc.Status
	39, // 41: firefly.billing.GetCreditCardBillRequest.ActorIdAndStatementDate.statement_date:type_name -> google.protobuf.Timestamp
	13, // 42: firefly.billing.GetUpcomingBillWindowResponse.BillWindowWithAccountId.upcoming_bill_window:type_name -> firefly.billing.BillWindow
	11, // 43: firefly.billing.Billing.GetCreditCardBill:input_type -> firefly.billing.GetCreditCardBillRequest
	14, // 44: firefly.billing.Billing.CreateCreditCardBill:input_type -> firefly.billing.CreateCreditCardBillRequest
	16, // 45: firefly.billing.Billing.GetBillAndBillPaymentInfo:input_type -> firefly.billing.GetBillAndBillPaymentInfoRequest
	18, // 46: firefly.billing.Billing.GetUpcomingBillWindow:input_type -> firefly.billing.GetUpcomingBillWindowRequest
	20, // 47: firefly.billing.Billing.UpsertCreditCardPaymentInfo:input_type -> firefly.billing.UpsertCreditCardPaymentInfoRequest
	22, // 48: firefly.billing.Billing.ProcessVendorRepaymentNotification:input_type -> firefly.billing.ProcessVendorRepaymentNotificationRequest
	9,  // 49: firefly.billing.Billing.FetchAllBillAndBillPayments:input_type -> firefly.billing.FetchAllBillAndBillPaymentsRequest
	24, // 50: firefly.billing.Billing.GetBillInfoForTransaction:input_type -> firefly.billing.GetBillInfoForTransactionRequest
	26, // 51: firefly.billing.Billing.UpdateCreditCardBill:input_type -> firefly.billing.UpdateCreditCardBillRequest
	28, // 52: firefly.billing.Billing.GetPreviousBillId:input_type -> firefly.billing.GetPreviousBillIdRequest
	7,  // 53: firefly.billing.Billing.InitiateAutoRepayment:input_type -> firefly.billing.InitiateAutoRepaymentRequest
	5,  // 54: firefly.billing.Billing.GetAutoRepaymentStatus:input_type -> firefly.billing.GetAutoRepaymentStatusRequest
	3,  // 55: firefly.billing.Billing.UpdateStatementDate:input_type -> firefly.billing.UpdateStatementDateRequest
	1,  // 56: firefly.billing.Billing.GetCreditCardBills:input_type -> firefly.billing.GetCreditCardBillsRequest
	12, // 57: firefly.billing.Billing.GetCreditCardBill:output_type -> firefly.billing.GetCreditCardBillResponse
	15, // 58: firefly.billing.Billing.CreateCreditCardBill:output_type -> firefly.billing.CreateCreditCardBillResponse
	17, // 59: firefly.billing.Billing.GetBillAndBillPaymentInfo:output_type -> firefly.billing.GetBillAndBillPaymentInfoResponse
	19, // 60: firefly.billing.Billing.GetUpcomingBillWindow:output_type -> firefly.billing.GetUpcomingBillWindowResponse
	21, // 61: firefly.billing.Billing.UpsertCreditCardPaymentInfo:output_type -> firefly.billing.UpsertCreditCardPaymentInfoResponse
	23, // 62: firefly.billing.Billing.ProcessVendorRepaymentNotification:output_type -> firefly.billing.ProcessVendorRepaymentNotificationResponse
	10, // 63: firefly.billing.Billing.FetchAllBillAndBillPayments:output_type -> firefly.billing.FetchAllBillAndBillPaymentsResponse
	25, // 64: firefly.billing.Billing.GetBillInfoForTransaction:output_type -> firefly.billing.GetBillInfoForTransactionResponse
	27, // 65: firefly.billing.Billing.UpdateCreditCardBill:output_type -> firefly.billing.UpdateCreditCardBillResponse
	29, // 66: firefly.billing.Billing.GetPreviousBillId:output_type -> firefly.billing.GetPreviousBillIdResponse
	8,  // 67: firefly.billing.Billing.InitiateAutoRepayment:output_type -> firefly.billing.InitiateAutoRepaymentResponse
	6,  // 68: firefly.billing.Billing.GetAutoRepaymentStatus:output_type -> firefly.billing.GetAutoRepaymentStatusResponse
	4,  // 69: firefly.billing.Billing.UpdateStatementDate:output_type -> firefly.billing.UpdateStatementDateResponse
	2,  // 70: firefly.billing.Billing.GetCreditCardBills:output_type -> firefly.billing.GetCreditCardBillsResponse
	57, // [57:71] is the sub-list for method output_type
	43, // [43:57] is the sub-list for method input_type
	43, // [43:43] is the sub-list for extension type_name
	43, // [43:43] is the sub-list for extension extendee
	0,  // [0:43] is the sub-list for field type_name
}

func init() { file_api_firefly_billing_service_proto_init() }
func file_api_firefly_billing_service_proto_init() {
	if File_api_firefly_billing_service_proto != nil {
		return
	}
	file_api_firefly_billing_internal_credit_card_bill_proto_init()
	file_api_firefly_billing_internal_credit_card_payment_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_firefly_billing_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardBillsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardBillsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStatementDateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStatementDateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAutoRepaymentStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAutoRepaymentStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateAutoRepaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateAutoRepaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchAllBillAndBillPaymentsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchAllBillAndBillPaymentsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardBillRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardBillResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BillWindow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCreditCardBillRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCreditCardBillResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBillAndBillPaymentInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBillAndBillPaymentInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUpcomingBillWindowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUpcomingBillWindowResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertCreditCardPaymentInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertCreditCardPaymentInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessVendorRepaymentNotificationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessVendorRepaymentNotificationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBillInfoForTransactionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBillInfoForTransactionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCreditCardBillRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCreditCardBillResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPreviousBillIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPreviousBillIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardBillRequest_ActorIdAndStatementDate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUpcomingBillWindowResponse_BillWindowWithAccountId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_firefly_billing_service_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*GetCreditCardBillRequest_ActorIdAndStatementDate_)(nil),
		(*GetCreditCardBillRequest_BillId)(nil),
		(*GetCreditCardBillRequest_ActorId)(nil),
	}
	file_api_firefly_billing_service_proto_msgTypes[19].OneofWrappers = []interface{}{
		(*UpsertCreditCardPaymentInfoRequest_OrderId)(nil),
		(*UpsertCreditCardPaymentInfoRequest_ExternalTxnId)(nil),
	}
	file_api_firefly_billing_service_proto_msgTypes[23].OneofWrappers = []interface{}{
		(*GetBillInfoForTransactionRequest_ExternalTxnId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_firefly_billing_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_firefly_billing_service_proto_goTypes,
		DependencyIndexes: file_api_firefly_billing_service_proto_depIdxs,
		EnumInfos:         file_api_firefly_billing_service_proto_enumTypes,
		MessageInfos:      file_api_firefly_billing_service_proto_msgTypes,
	}.Build()
	File_api_firefly_billing_service_proto = out.File
	file_api_firefly_billing_service_proto_rawDesc = nil
	file_api_firefly_billing_service_proto_goTypes = nil
	file_api_firefly_billing_service_proto_depIdxs = nil
}
