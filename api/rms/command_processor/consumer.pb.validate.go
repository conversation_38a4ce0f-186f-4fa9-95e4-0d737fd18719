// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/rms/command_processor/consumer.proto

package command_processor

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ProcessCommandsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessCommandsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCommandsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessCommandsRequestMultiError, or nil if none found.
func (m *ProcessCommandsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCommandsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCommandsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCommandsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCommandsRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SubscriptionId

	if all {
		switch v := interface{}(m.GetEvent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCommandsRequestValidationError{
					field:  "Event",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCommandsRequestValidationError{
					field:  "Event",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEvent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCommandsRequestValidationError{
				field:  "Event",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCommandsRequestValidationError{
					field:  "Rule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCommandsRequestValidationError{
					field:  "Rule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCommandsRequestValidationError{
				field:  "Rule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubscription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCommandsRequestValidationError{
					field:  "Subscription",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCommandsRequestValidationError{
					field:  "Subscription",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubscription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCommandsRequestValidationError{
				field:  "Subscription",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExistingExecution()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCommandsRequestValidationError{
					field:  "ExistingExecution",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCommandsRequestValidationError{
					field:  "ExistingExecution",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExistingExecution()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCommandsRequestValidationError{
				field:  "ExistingExecution",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCommandsRequestMultiError(errors)
	}

	return nil
}

// ProcessCommandsRequestMultiError is an error wrapping multiple validation
// errors returned by ProcessCommandsRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessCommandsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCommandsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCommandsRequestMultiError) AllErrors() []error { return m }

// ProcessCommandsRequestValidationError is the validation error returned by
// ProcessCommandsRequest.Validate if the designated constraints aren't met.
type ProcessCommandsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCommandsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCommandsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCommandsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCommandsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCommandsRequestValidationError) ErrorName() string {
	return "ProcessCommandsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCommandsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCommandsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCommandsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCommandsRequestValidationError{}

// Validate checks the field values on ProcessCommandsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessCommandsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCommandsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessCommandsResponseMultiError, or nil if none found.
func (m *ProcessCommandsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCommandsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCommandsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCommandsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCommandsResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCommandsResponseMultiError(errors)
	}

	return nil
}

// ProcessCommandsResponseMultiError is an error wrapping multiple validation
// errors returned by ProcessCommandsResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessCommandsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCommandsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCommandsResponseMultiError) AllErrors() []error { return m }

// ProcessCommandsResponseValidationError is the validation error returned by
// ProcessCommandsResponse.Validate if the designated constraints aren't met.
type ProcessCommandsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCommandsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCommandsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCommandsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCommandsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCommandsResponseValidationError) ErrorName() string {
	return "ProcessCommandsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCommandsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCommandsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCommandsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCommandsResponseValidationError{}
