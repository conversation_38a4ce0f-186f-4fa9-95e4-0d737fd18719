//go:generate gen_sql -types=CallMetrics,CallDataDump

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/omegle/service.proto

package omegle

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	enums "github.com/epifi/gringott/api/external/omegle/enums"
	typesv2 "github.com/epifi/gringott/api/external/typesv2"
	date "google.golang.org/genproto/googleapis/type/date"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	postaladdress "google.golang.org/genproto/googleapis/type/postaladdress"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RegisterUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Application ID is a unique identifier for each new VCIP application.
	// Client should generate this random ID (e.g. RAND241010ABDFdfase) and send it.
	// This ID will be used to track the application in the system for further interactions.
	ApplicationId       string               `protobuf:"bytes,5,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	ApplicationFormData *ApplicationFormData `protobuf:"bytes,1,opt,name=application_form_data,json=applicationFormData,proto3" json:"application_form_data,omitempty"`
	Client              vendorgateway.Vendor `protobuf:"varint,2,opt,name=client,proto3,enum=vendorgateway.Vendor" json:"client,omitempty"`
	// Channel enums are provided to clients to specify the flow in which VKYC is being performed during registration.
	// By specifying the appropriate enum, clients help provide relevant information to agents and auditors during the verification of data.
	// This specification will also determine the data that needs to be collected and sent to VKYC by the client for the call.
	Channel         enums.Channel              `protobuf:"varint,3,opt,name=channel,proto3,enum=omegle.enums.Channel" json:"channel,omitempty"`
	DocumentDetails []*typesv2.DocumentDetails `protobuf:"bytes,4,rep,name=document_details,json=documentDetails,proto3" json:"document_details,omitempty"`
}

func (x *RegisterUserRequest) Reset() {
	*x = RegisterUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterUserRequest) ProtoMessage() {}

func (x *RegisterUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterUserRequest.ProtoReflect.Descriptor instead.
func (*RegisterUserRequest) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{0}
}

func (x *RegisterUserRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *RegisterUserRequest) GetApplicationFormData() *ApplicationFormData {
	if x != nil {
		return x.ApplicationFormData
	}
	return nil
}

func (x *RegisterUserRequest) GetClient() vendorgateway.Vendor {
	if x != nil {
		return x.Client
	}
	return vendorgateway.Vendor(0)
}

func (x *RegisterUserRequest) GetChannel() enums.Channel {
	if x != nil {
		return x.Channel
	}
	return enums.Channel(0)
}

func (x *RegisterUserRequest) GetDocumentDetails() []*typesv2.DocumentDetails {
	if x != nil {
		return x.DocumentDetails
	}
	return nil
}

type RegisterUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ApplicationId string      `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *RegisterUserResponse) Reset() {
	*x = RegisterUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterUserResponse) ProtoMessage() {}

func (x *RegisterUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterUserResponse.ProtoReflect.Descriptor instead.
func (*RegisterUserResponse) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{1}
}

func (x *RegisterUserResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *RegisterUserResponse) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type UpdateApplicantDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId       string                     `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	ApplicationFormData *ApplicationFormData       `protobuf:"bytes,2,opt,name=application_form_data,json=applicationFormData,proto3" json:"application_form_data,omitempty"`
	DocumentDetails     []*typesv2.DocumentDetails `protobuf:"bytes,3,rep,name=document_details,json=documentDetails,proto3" json:"document_details,omitempty"`
}

func (x *UpdateApplicantDetailsRequest) Reset() {
	*x = UpdateApplicantDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateApplicantDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateApplicantDetailsRequest) ProtoMessage() {}

func (x *UpdateApplicantDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateApplicantDetailsRequest.ProtoReflect.Descriptor instead.
func (*UpdateApplicantDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateApplicantDetailsRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *UpdateApplicantDetailsRequest) GetApplicationFormData() *ApplicationFormData {
	if x != nil {
		return x.ApplicationFormData
	}
	return nil
}

func (x *UpdateApplicantDetailsRequest) GetDocumentDetails() []*typesv2.DocumentDetails {
	if x != nil {
		return x.DocumentDetails
	}
	return nil
}

type UpdateApplicantDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ApplicationId string      `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *UpdateApplicantDetailsResponse) Reset() {
	*x = UpdateApplicantDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateApplicantDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateApplicantDetailsResponse) ProtoMessage() {}

func (x *UpdateApplicantDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateApplicantDetailsResponse.ProtoReflect.Descriptor instead.
func (*UpdateApplicantDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateApplicantDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateApplicantDetailsResponse) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type ApplicationFormData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicantName        *common.Name                 `protobuf:"bytes,1,opt,name=applicant_name,json=applicantName,proto3" json:"applicant_name,omitempty"`
	PhoneNumber          *common.PhoneNumber          `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	FatherName           *common.Name                 `protobuf:"bytes,3,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
	MotherName           *common.Name                 `protobuf:"bytes,4,opt,name=mother_name,json=motherName,proto3" json:"mother_name,omitempty"`
	Dob                  *date.Date                   `protobuf:"bytes,5,opt,name=dob,proto3" json:"dob,omitempty"`
	Email                string                       `protobuf:"bytes,6,opt,name=email,proto3" json:"email,omitempty"`
	Gender               typesv2.Gender               `protobuf:"varint,7,opt,name=gender,proto3,enum=api.typesv2.Gender" json:"gender,omitempty"`
	CommunicationAddress *postaladdress.PostalAddress `protobuf:"bytes,8,opt,name=communication_address,json=communicationAddress,proto3" json:"communication_address,omitempty"`
	PermanentAddress     *postaladdress.PostalAddress `protobuf:"bytes,9,opt,name=permanent_address,json=permanentAddress,proto3" json:"permanent_address,omitempty"`
	IncomeDetails        *IncomeDetails               `protobuf:"bytes,10,opt,name=incomeDetails,proto3" json:"incomeDetails,omitempty"`
	Location             *latlng.LatLng               `protobuf:"bytes,11,opt,name=location,proto3" json:"location,omitempty"`
	Image                *common.Image                `protobuf:"bytes,12,opt,name=image,proto3" json:"image,omitempty"`
	EmploymentDetails    *EmploymentDetails           `protobuf:"bytes,13,opt,name=employment_details,json=employmentDetails,proto3" json:"employment_details,omitempty"`
	ApplicationType      enums.ApplicationType        `protobuf:"varint,14,opt,name=application_type,json=applicationType,proto3,enum=omegle.enums.ApplicationType" json:"application_type,omitempty"`
	ApplicantType        enums.ApplicantType          `protobuf:"varint,15,opt,name=applicant_type,json=applicantType,proto3,enum=omegle.enums.ApplicantType" json:"applicant_type,omitempty"`
	// Ovd id is the passport number of the user
	// It is send as application id to federal, when we send the call details to federal.
	OvdId                      string                      `protobuf:"bytes,16,opt,name=ovd_id,json=ovdId,proto3" json:"ovd_id,omitempty"`
	AdditionalApplicantDetails *AdditionalApplicantDetails `protobuf:"bytes,17,opt,name=additional_applicant_details,json=additionalApplicantDetails,proto3" json:"additional_applicant_details,omitempty"`
}

func (x *ApplicationFormData) Reset() {
	*x = ApplicationFormData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicationFormData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicationFormData) ProtoMessage() {}

func (x *ApplicationFormData) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicationFormData.ProtoReflect.Descriptor instead.
func (*ApplicationFormData) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{4}
}

func (x *ApplicationFormData) GetApplicantName() *common.Name {
	if x != nil {
		return x.ApplicantName
	}
	return nil
}

func (x *ApplicationFormData) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *ApplicationFormData) GetFatherName() *common.Name {
	if x != nil {
		return x.FatherName
	}
	return nil
}

func (x *ApplicationFormData) GetMotherName() *common.Name {
	if x != nil {
		return x.MotherName
	}
	return nil
}

func (x *ApplicationFormData) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *ApplicationFormData) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ApplicationFormData) GetGender() typesv2.Gender {
	if x != nil {
		return x.Gender
	}
	return typesv2.Gender(0)
}

func (x *ApplicationFormData) GetCommunicationAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.CommunicationAddress
	}
	return nil
}

func (x *ApplicationFormData) GetPermanentAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.PermanentAddress
	}
	return nil
}

func (x *ApplicationFormData) GetIncomeDetails() *IncomeDetails {
	if x != nil {
		return x.IncomeDetails
	}
	return nil
}

func (x *ApplicationFormData) GetLocation() *latlng.LatLng {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *ApplicationFormData) GetImage() *common.Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *ApplicationFormData) GetEmploymentDetails() *EmploymentDetails {
	if x != nil {
		return x.EmploymentDetails
	}
	return nil
}

func (x *ApplicationFormData) GetApplicationType() enums.ApplicationType {
	if x != nil {
		return x.ApplicationType
	}
	return enums.ApplicationType(0)
}

func (x *ApplicationFormData) GetApplicantType() enums.ApplicantType {
	if x != nil {
		return x.ApplicantType
	}
	return enums.ApplicantType(0)
}

func (x *ApplicationFormData) GetOvdId() string {
	if x != nil {
		return x.OvdId
	}
	return ""
}

func (x *ApplicationFormData) GetAdditionalApplicantDetails() *AdditionalApplicantDetails {
	if x != nil {
		return x.AdditionalApplicantDetails
	}
	return nil
}

type StartCallRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId string `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *StartCallRequest) Reset() {
	*x = StartCallRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartCallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartCallRequest) ProtoMessage() {}

func (x *StartCallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartCallRequest.ProtoReflect.Descriptor instead.
func (*StartCallRequest) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{5}
}

func (x *StartCallRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type StartCallResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CallId string      `protobuf:"bytes,2,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
}

func (x *StartCallResponse) Reset() {
	*x = StartCallResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartCallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartCallResponse) ProtoMessage() {}

func (x *StartCallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartCallResponse.ProtoReflect.Descriptor instead.
func (*StartCallResponse) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{6}
}

func (x *StartCallResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *StartCallResponse) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

type CheckCallStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CallId string `protobuf:"bytes,2,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
}

func (x *CheckCallStatusRequest) Reset() {
	*x = CheckCallStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCallStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCallStatusRequest) ProtoMessage() {}

func (x *CheckCallStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCallStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckCallStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{7}
}

func (x *CheckCallStatusRequest) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

type CheckCallStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CallStatus    enums.OverallStatus `protobuf:"varint,2,opt,name=call_status,json=callStatus,proto3,enum=omegle.enums.OverallStatus" json:"call_status,omitempty"`
	FailureReason enums.FailureReason `protobuf:"varint,3,opt,name=failure_reason,json=failureReason,proto3,enum=omegle.enums.FailureReason" json:"failure_reason,omitempty"`
	CallSubStatus enums.CallSubStatus `protobuf:"varint,4,opt,name=call_sub_status,json=callSubStatus,proto3,enum=omegle.enums.CallSubStatus" json:"call_sub_status,omitempty"`
	// In case of call failure, this flag returns if the failure is terminal or not
	// Helps clients in determining if the call can be retried or not
	IsTerminalFailure common.BooleanEnum `protobuf:"varint,5,opt,name=is_terminal_failure,json=isTerminalFailure,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_terminal_failure,omitempty"`
}

func (x *CheckCallStatusResponse) Reset() {
	*x = CheckCallStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCallStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCallStatusResponse) ProtoMessage() {}

func (x *CheckCallStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCallStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckCallStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{8}
}

func (x *CheckCallStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckCallStatusResponse) GetCallStatus() enums.OverallStatus {
	if x != nil {
		return x.CallStatus
	}
	return enums.OverallStatus(0)
}

func (x *CheckCallStatusResponse) GetFailureReason() enums.FailureReason {
	if x != nil {
		return x.FailureReason
	}
	return enums.FailureReason(0)
}

func (x *CheckCallStatusResponse) GetCallSubStatus() enums.CallSubStatus {
	if x != nil {
		return x.CallSubStatus
	}
	return enums.CallSubStatus(0)
}

func (x *CheckCallStatusResponse) GetIsTerminalFailure() common.BooleanEnum {
	if x != nil {
		return x.IsTerminalFailure
	}
	return common.BooleanEnum(0)
}

type GetApplicantDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId string `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *GetApplicantDetailsRequest) Reset() {
	*x = GetApplicantDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicantDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicantDetailsRequest) ProtoMessage() {}

func (x *GetApplicantDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicantDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetApplicantDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetApplicantDetailsRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type GetApplicantDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status            *rpc.Status                `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ApplicantName     *common.Name               `protobuf:"bytes,2,opt,name=applicant_name,json=applicantName,proto3" json:"applicant_name,omitempty"`
	Dob               *date.Date                 `protobuf:"bytes,3,opt,name=dob,proto3" json:"dob,omitempty"`
	Email             string                     `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Gender            typesv2.Gender             `protobuf:"varint,5,opt,name=gender,proto3,enum=api.typesv2.Gender" json:"gender,omitempty"`
	IncomeDetails     *IncomeDetails             `protobuf:"bytes,6,opt,name=income_details,json=incomeDetails,proto3" json:"income_details,omitempty"`
	EmploymentDetails *EmploymentDetails         `protobuf:"bytes,7,opt,name=employment_details,json=employmentDetails,proto3" json:"employment_details,omitempty"`
	GuardianDetails   *GuardianDetails           `protobuf:"bytes,8,opt,name=guardian_details,json=guardianDetails,proto3" json:"guardian_details,omitempty"`
	Image             *common.Image              `protobuf:"bytes,9,opt,name=image,proto3" json:"image,omitempty"`
	MobileNumber      *common.PhoneNumber        `protobuf:"bytes,10,opt,name=mobile_number,json=mobileNumber,proto3" json:"mobile_number,omitempty"`
	DocumentDetails   []*typesv2.DocumentDetails `protobuf:"bytes,11,rep,name=document_details,json=documentDetails,proto3" json:"document_details,omitempty"`
	AddressDetails    *AddressDetails            `protobuf:"bytes,12,opt,name=address_details,json=addressDetails,proto3" json:"address_details,omitempty"`
	PassportNumber    string                     `protobuf:"bytes,13,opt,name=passport_number,json=passportNumber,proto3" json:"passport_number,omitempty"`
	// channel via which user started the vkyc application
	Channel enums.Channel `protobuf:"varint,14,opt,name=channel,proto3,enum=omegle.enums.Channel" json:"channel,omitempty"`
}

func (x *GetApplicantDetailsResponse) Reset() {
	*x = GetApplicantDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicantDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicantDetailsResponse) ProtoMessage() {}

func (x *GetApplicantDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicantDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetApplicantDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetApplicantDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetApplicantDetailsResponse) GetApplicantName() *common.Name {
	if x != nil {
		return x.ApplicantName
	}
	return nil
}

func (x *GetApplicantDetailsResponse) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *GetApplicantDetailsResponse) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetApplicantDetailsResponse) GetGender() typesv2.Gender {
	if x != nil {
		return x.Gender
	}
	return typesv2.Gender(0)
}

func (x *GetApplicantDetailsResponse) GetIncomeDetails() *IncomeDetails {
	if x != nil {
		return x.IncomeDetails
	}
	return nil
}

func (x *GetApplicantDetailsResponse) GetEmploymentDetails() *EmploymentDetails {
	if x != nil {
		return x.EmploymentDetails
	}
	return nil
}

func (x *GetApplicantDetailsResponse) GetGuardianDetails() *GuardianDetails {
	if x != nil {
		return x.GuardianDetails
	}
	return nil
}

func (x *GetApplicantDetailsResponse) GetImage() *common.Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *GetApplicantDetailsResponse) GetMobileNumber() *common.PhoneNumber {
	if x != nil {
		return x.MobileNumber
	}
	return nil
}

func (x *GetApplicantDetailsResponse) GetDocumentDetails() []*typesv2.DocumentDetails {
	if x != nil {
		return x.DocumentDetails
	}
	return nil
}

func (x *GetApplicantDetailsResponse) GetAddressDetails() *AddressDetails {
	if x != nil {
		return x.AddressDetails
	}
	return nil
}

func (x *GetApplicantDetailsResponse) GetPassportNumber() string {
	if x != nil {
		return x.PassportNumber
	}
	return ""
}

func (x *GetApplicantDetailsResponse) GetChannel() enums.Channel {
	if x != nil {
		return x.Channel
	}
	return enums.Channel(0)
}

type StoreCallAndAuditDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CallId string `protobuf:"bytes,1,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	// Types that are assignable to RequestDetails:
	//
	//	*StoreCallAndAuditDetailsRequest_PreCallInfo
	//	*StoreCallAndAuditDetailsRequest_CallDataDump
	//	*StoreCallAndAuditDetailsRequest_AuditorReview
	//	*StoreCallAndAuditDetailsRequest_CallEnded
	RequestDetails isStoreCallAndAuditDetailsRequest_RequestDetails `protobuf_oneof:"request_details"`
	// Won't update the call info and audit details, will only send callback to federal if skip_storing_audit_details is true
	// * Note :- If you are calling from a script, and not passing call info and audit details then you should always set this flag to true, else data will be modified to empty in call infos *
	SkipStoringAuditDetails bool `protobuf:"varint,6,opt,name=skip_storing_audit_details,json=skipStoringAuditDetails,proto3" json:"skip_storing_audit_details,omitempty"`
}

func (x *StoreCallAndAuditDetailsRequest) Reset() {
	*x = StoreCallAndAuditDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreCallAndAuditDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreCallAndAuditDetailsRequest) ProtoMessage() {}

func (x *StoreCallAndAuditDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreCallAndAuditDetailsRequest.ProtoReflect.Descriptor instead.
func (*StoreCallAndAuditDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{11}
}

func (x *StoreCallAndAuditDetailsRequest) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

func (m *StoreCallAndAuditDetailsRequest) GetRequestDetails() isStoreCallAndAuditDetailsRequest_RequestDetails {
	if m != nil {
		return m.RequestDetails
	}
	return nil
}

func (x *StoreCallAndAuditDetailsRequest) GetPreCallInfo() *PreCallInfo {
	if x, ok := x.GetRequestDetails().(*StoreCallAndAuditDetailsRequest_PreCallInfo); ok {
		return x.PreCallInfo
	}
	return nil
}

func (x *StoreCallAndAuditDetailsRequest) GetCallDataDump() *CallDataDump {
	if x, ok := x.GetRequestDetails().(*StoreCallAndAuditDetailsRequest_CallDataDump); ok {
		return x.CallDataDump
	}
	return nil
}

func (x *StoreCallAndAuditDetailsRequest) GetAuditorReview() *AuditorReview {
	if x, ok := x.GetRequestDetails().(*StoreCallAndAuditDetailsRequest_AuditorReview); ok {
		return x.AuditorReview
	}
	return nil
}

func (x *StoreCallAndAuditDetailsRequest) GetCallEnded() *CallEnded {
	if x, ok := x.GetRequestDetails().(*StoreCallAndAuditDetailsRequest_CallEnded); ok {
		return x.CallEnded
	}
	return nil
}

func (x *StoreCallAndAuditDetailsRequest) GetSkipStoringAuditDetails() bool {
	if x != nil {
		return x.SkipStoringAuditDetails
	}
	return false
}

type isStoreCallAndAuditDetailsRequest_RequestDetails interface {
	isStoreCallAndAuditDetailsRequest_RequestDetails()
}

type StoreCallAndAuditDetailsRequest_PreCallInfo struct {
	// PreCallInfo - This will be sent by the client before the call starts
	PreCallInfo *PreCallInfo `protobuf:"bytes,2,opt,name=pre_call_info,json=preCallInfo,proto3,oneof"`
}

type StoreCallAndAuditDetailsRequest_CallDataDump struct {
	// CallDataDump - This will be sent by the client after the agent submits the review or for storing the call data
	CallDataDump *CallDataDump `protobuf:"bytes,3,opt,name=call_data_dump,json=callDataDump,proto3,oneof"`
}

type StoreCallAndAuditDetailsRequest_AuditorReview struct {
	// AuditorReview - This will be sent by the client after the auditor submit the review
	AuditorReview *AuditorReview `protobuf:"bytes,4,opt,name=auditor_review,json=auditorReview,proto3,oneof"`
}

type StoreCallAndAuditDetailsRequest_CallEnded struct {
	// CallEnded - This will be sent by the client after the call ends successfully or call failed or call is in agent review.
	CallEnded *CallEnded `protobuf:"bytes,5,opt,name=call_ended,json=callEnded,proto3,oneof"`
}

func (*StoreCallAndAuditDetailsRequest_PreCallInfo) isStoreCallAndAuditDetailsRequest_RequestDetails() {
}

func (*StoreCallAndAuditDetailsRequest_CallDataDump) isStoreCallAndAuditDetailsRequest_RequestDetails() {
}

func (*StoreCallAndAuditDetailsRequest_AuditorReview) isStoreCallAndAuditDetailsRequest_RequestDetails() {
}

func (*StoreCallAndAuditDetailsRequest_CallEnded) isStoreCallAndAuditDetailsRequest_RequestDetails() {
}

type StoreCallAndAuditDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *StoreCallAndAuditDetailsResponse) Reset() {
	*x = StoreCallAndAuditDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreCallAndAuditDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreCallAndAuditDetailsResponse) ProtoMessage() {}

func (x *StoreCallAndAuditDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreCallAndAuditDetailsResponse.ProtoReflect.Descriptor instead.
func (*StoreCallAndAuditDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{12}
}

func (x *StoreCallAndAuditDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetPendingAuditCallsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetPendingAuditCallsRequest) Reset() {
	*x = GetPendingAuditCallsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPendingAuditCallsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPendingAuditCallsRequest) ProtoMessage() {}

func (x *GetPendingAuditCallsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPendingAuditCallsRequest.ProtoReflect.Descriptor instead.
func (*GetPendingAuditCallsRequest) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{13}
}

type GetPendingAuditCallsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                 *rpc.Status           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PendingCallDetailsList []*PendingCallDetails `protobuf:"bytes,2,rep,name=pending_call_details_list,json=pendingCallDetailsList,proto3" json:"pending_call_details_list,omitempty"`
}

func (x *GetPendingAuditCallsResponse) Reset() {
	*x = GetPendingAuditCallsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPendingAuditCallsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPendingAuditCallsResponse) ProtoMessage() {}

func (x *GetPendingAuditCallsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPendingAuditCallsResponse.ProtoReflect.Descriptor instead.
func (*GetPendingAuditCallsResponse) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetPendingAuditCallsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPendingAuditCallsResponse) GetPendingCallDetailsList() []*PendingCallDetails {
	if x != nil {
		return x.PendingCallDetailsList
	}
	return nil
}

type PendingCallDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CallId          string                 `protobuf:"bytes,1,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	ApplicationId   string                 `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	ApplicantName   string                 `protobuf:"bytes,3,opt,name=applicant_name,json=applicantName,proto3" json:"applicant_name,omitempty"`
	CallCompletedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=call_completed_at,json=callCompletedAt,proto3" json:"call_completed_at,omitempty"`
	AgentName       string                 `protobuf:"bytes,5,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
}

func (x *PendingCallDetails) Reset() {
	*x = PendingCallDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PendingCallDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PendingCallDetails) ProtoMessage() {}

func (x *PendingCallDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PendingCallDetails.ProtoReflect.Descriptor instead.
func (*PendingCallDetails) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{15}
}

func (x *PendingCallDetails) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

func (x *PendingCallDetails) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *PendingCallDetails) GetApplicantName() string {
	if x != nil {
		return x.ApplicantName
	}
	return ""
}

func (x *PendingCallDetails) GetCallCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CallCompletedAt
	}
	return nil
}

func (x *PendingCallDetails) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

type GetPendingAuditCallDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CallId string `protobuf:"bytes,1,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
}

func (x *GetPendingAuditCallDetailsRequest) Reset() {
	*x = GetPendingAuditCallDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPendingAuditCallDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPendingAuditCallDetailsRequest) ProtoMessage() {}

func (x *GetPendingAuditCallDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPendingAuditCallDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetPendingAuditCallDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetPendingAuditCallDetailsRequest) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

type GetPendingAuditCallDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                 *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CallId                 string                 `protobuf:"bytes,2,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	ApplicationId          string                 `protobuf:"bytes,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	ApplicantName          string                 `protobuf:"bytes,4,opt,name=applicant_name,json=applicantName,proto3" json:"applicant_name,omitempty"`
	CallCompletedAt        *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=call_completed_at,json=callCompletedAt,proto3" json:"call_completed_at,omitempty"`
	AgentName              string                 `protobuf:"bytes,6,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	AgentRemark            string                 `protobuf:"bytes,7,opt,name=agent_remark,json=agentRemark,proto3" json:"agent_remark,omitempty"`
	Questions              []*Question            `protobuf:"bytes,8,rep,name=Questions,proto3" json:"Questions,omitempty"`
	AgentDashCallRecording *typesv2.File          `protobuf:"bytes,9,opt,name=agent_dash_call_recording,json=agentDashCallRecording,proto3" json:"agent_dash_call_recording,omitempty"`
}

func (x *GetPendingAuditCallDetailsResponse) Reset() {
	*x = GetPendingAuditCallDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPendingAuditCallDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPendingAuditCallDetailsResponse) ProtoMessage() {}

func (x *GetPendingAuditCallDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPendingAuditCallDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetPendingAuditCallDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_omegle_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetPendingAuditCallDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPendingAuditCallDetailsResponse) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

func (x *GetPendingAuditCallDetailsResponse) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetPendingAuditCallDetailsResponse) GetApplicantName() string {
	if x != nil {
		return x.ApplicantName
	}
	return ""
}

func (x *GetPendingAuditCallDetailsResponse) GetCallCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CallCompletedAt
	}
	return nil
}

func (x *GetPendingAuditCallDetailsResponse) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *GetPendingAuditCallDetailsResponse) GetAgentRemark() string {
	if x != nil {
		return x.AgentRemark
	}
	return ""
}

func (x *GetPendingAuditCallDetailsResponse) GetQuestions() []*Question {
	if x != nil {
		return x.Questions
	}
	return nil
}

func (x *GetPendingAuditCallDetailsResponse) GetAgentDashCallRecording() *typesv2.File {
	if x != nil {
		return x.AgentDashCallRecording
	}
	return nil
}

var File_api_omegle_service_proto protoreflect.FileDescriptor

var file_api_omegle_service_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x6f, 0x6d, 0x65, 0x67,
	0x6c, 0x65, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2f, 0x63,
	0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20,
	0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x6f,
	0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6c, 0x61, 0x74, 0x6c, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb6, 0x02, 0x0a, 0x13, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x15, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x6d, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x13, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72,
	0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x47, 0x0a, 0x10, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x64,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x62,
	0x0a, 0x14, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x22, 0xe0, 0x01, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x15, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6f, 0x6d, 0x65,
	0x67, 0x6c, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46,
	0x6f, 0x72, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x47, 0x0a, 0x10,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x6c, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x22, 0x86, 0x08, 0x0a, 0x13, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x0c,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x39, 0x0a, 0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x0a, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x6d,
	0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x6d, 0x6f, 0x74, 0x68,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x2b, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x4f,
	0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x74,
	0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x14, 0x63, 0x6f, 0x6d, 0x6d, 0x75,
	0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x47, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3b, 0x0a, 0x0d, 0x69, 0x6e, 0x63, 0x6f,
	0x6d, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0d, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2f, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x08, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x48, 0x0a, 0x12, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x45, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x48, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6f, 0x6d,
	0x65, 0x67, 0x6c, 0x65, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x15, 0x0a, 0x06, 0x6f, 0x76, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6f, 0x76, 0x64, 0x49, 0x64, 0x12, 0x64, 0x0a, 0x1c, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6f,
	0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x1a, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x39, 0x0a, 0x10,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x51, 0x0a, 0x11, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x22, 0x31, 0x0a, 0x16, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x22, 0xd6, 0x02,
	0x0a, 0x17, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c,
	0x0a, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0a, 0x63, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x42, 0x0a, 0x0e,
	0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x43, 0x0a, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x6f, 0x6d, 0x65, 0x67,
	0x6c, 0x65, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x75, 0x62,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x53, 0x75, 0x62, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4f, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x11, 0x69, 0x73, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x46,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x22, 0x43, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x92, 0x06, 0x0a, 0x1b,
	0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x3f, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x2b, 0x0a, 0x06,
	0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0e, 0x69, 0x6e, 0x63,
	0x6f, 0x6d, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x49, 0x6e, 0x63, 0x6f, 0x6d,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0d, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x48, 0x0a, 0x12, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x45, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x42, 0x0a, 0x10, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6f, 0x6d,
	0x65, 0x67, 0x6c, 0x65, 0x2e, 0x47, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2f, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x44, 0x0a, 0x0d, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0c,
	0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x47, 0x0a, 0x10,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3f, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x2f, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x22, 0xf7, 0x02, 0x0a, 0x1f, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x6e,
	0x64, 0x41, 0x75, 0x64, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x39, 0x0a,
	0x0d, 0x70, 0x72, 0x65, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x50, 0x72,
	0x65, 0x43, 0x61, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0b, 0x70, 0x72, 0x65,
	0x43, 0x61, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3c, 0x0a, 0x0e, 0x63, 0x61, 0x6c, 0x6c,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x64, 0x75, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x44, 0x61,
	0x74, 0x61, 0x44, 0x75, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x44, 0x61,
	0x74, 0x61, 0x44, 0x75, 0x6d, 0x70, 0x12, 0x3e, 0x0a, 0x0e, 0x61, 0x75, 0x64, 0x69, 0x74, 0x6f,
	0x72, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x75, 0x64, 0x69, 0x74, 0x6f, 0x72,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x32, 0x0a, 0x0a, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x65,
	0x6e, 0x64, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6f, 0x6d, 0x65,
	0x67, 0x6c, 0x65, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x45, 0x6e, 0x64, 0x65, 0x64, 0x48, 0x00, 0x52,
	0x09, 0x63, 0x61, 0x6c, 0x6c, 0x45, 0x6e, 0x64, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x1a, 0x73, 0x6b,
	0x69, 0x70, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x75, 0x64, 0x69, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17,
	0x73, 0x6b, 0x69, 0x70, 0x53, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x75, 0x64, 0x69, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x11, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x47, 0x0a, 0x20, 0x53, 0x74,
	0x6f, 0x72, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x6e, 0x64, 0x41, 0x75, 0x64, 0x69, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x1d, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x41, 0x75, 0x64, 0x69, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x9a, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x41, 0x75, 0x64, 0x69, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x55, 0x0a, 0x19, 0x70, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6f, 0x6d,
	0x65, 0x67, 0x6c, 0x65, 0x2e, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x6c, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x16, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x43, 0x61, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0xe2, 0x01, 0x0a, 0x12, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x6c, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12,
	0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x46, 0x0a,
	0x11, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x3c, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x50, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x41, 0x75, 0x64, 0x69, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x6c,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c,
	0x49, 0x64, 0x22, 0xb8, 0x03, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x41, 0x75, 0x64, 0x69, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x63, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x11, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x63, 0x61,
	0x6c, 0x6c, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12,
	0x2e, 0x0a, 0x09, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x4c, 0x0a, 0x19, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x73, 0x68, 0x5f, 0x63, 0x61,
	0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x16, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x73, 0x68,
	0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x32, 0xf9, 0x05,
	0x0a, 0x06, 0x4f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x12, 0x49, 0x0a, 0x0c, 0x52, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1b, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c,
	0x65, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x25, 0x2e,
	0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x09,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x12, 0x18, 0x2e, 0x6f, 0x6d, 0x65, 0x67,
	0x6c, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52,
	0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1e, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1f, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x5e, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x22, 0x2e, 0x6f, 0x6d, 0x65, 0x67,
	0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e,
	0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x6d, 0x0a, 0x18, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x41,
	0x6e, 0x64, 0x41, 0x75, 0x64, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x27,
	0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x61, 0x6c,
	0x6c, 0x41, 0x6e, 0x64, 0x41, 0x75, 0x64, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65,
	0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x6e, 0x64, 0x41, 0x75, 0x64,
	0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x61, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x41,
	0x75, 0x64, 0x69, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x12, 0x23, 0x2e, 0x6f, 0x6d, 0x65, 0x67,
	0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x75, 0x64,
	0x69, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24,
	0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x41, 0x75, 0x64, 0x69, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x50, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x41, 0x75, 0x64, 0x69, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x29, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x75, 0x64, 0x69, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e,
	0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x41, 0x75, 0x64, 0x69, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x52, 0x0a, 0x21, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x5a, 0x2d,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x72, 0x69, 0x6e, 0x67, 0x6f, 0x74, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_omegle_service_proto_rawDescOnce sync.Once
	file_api_omegle_service_proto_rawDescData = file_api_omegle_service_proto_rawDesc
)

func file_api_omegle_service_proto_rawDescGZIP() []byte {
	file_api_omegle_service_proto_rawDescOnce.Do(func() {
		file_api_omegle_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_omegle_service_proto_rawDescData)
	})
	return file_api_omegle_service_proto_rawDescData
}

var file_api_omegle_service_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_api_omegle_service_proto_goTypes = []interface{}{
	(*RegisterUserRequest)(nil),                // 0: omegle.RegisterUserRequest
	(*RegisterUserResponse)(nil),               // 1: omegle.RegisterUserResponse
	(*UpdateApplicantDetailsRequest)(nil),      // 2: omegle.UpdateApplicantDetailsRequest
	(*UpdateApplicantDetailsResponse)(nil),     // 3: omegle.UpdateApplicantDetailsResponse
	(*ApplicationFormData)(nil),                // 4: omegle.ApplicationFormData
	(*StartCallRequest)(nil),                   // 5: omegle.StartCallRequest
	(*StartCallResponse)(nil),                  // 6: omegle.StartCallResponse
	(*CheckCallStatusRequest)(nil),             // 7: omegle.CheckCallStatusRequest
	(*CheckCallStatusResponse)(nil),            // 8: omegle.CheckCallStatusResponse
	(*GetApplicantDetailsRequest)(nil),         // 9: omegle.GetApplicantDetailsRequest
	(*GetApplicantDetailsResponse)(nil),        // 10: omegle.GetApplicantDetailsResponse
	(*StoreCallAndAuditDetailsRequest)(nil),    // 11: omegle.StoreCallAndAuditDetailsRequest
	(*StoreCallAndAuditDetailsResponse)(nil),   // 12: omegle.StoreCallAndAuditDetailsResponse
	(*GetPendingAuditCallsRequest)(nil),        // 13: omegle.GetPendingAuditCallsRequest
	(*GetPendingAuditCallsResponse)(nil),       // 14: omegle.GetPendingAuditCallsResponse
	(*PendingCallDetails)(nil),                 // 15: omegle.PendingCallDetails
	(*GetPendingAuditCallDetailsRequest)(nil),  // 16: omegle.GetPendingAuditCallDetailsRequest
	(*GetPendingAuditCallDetailsResponse)(nil), // 17: omegle.GetPendingAuditCallDetailsResponse
	(vendorgateway.Vendor)(0),                  // 18: vendorgateway.Vendor
	(enums.Channel)(0),                         // 19: omegle.enums.Channel
	(*typesv2.DocumentDetails)(nil),            // 20: api.typesv2.DocumentDetails
	(*rpc.Status)(nil),                         // 21: rpc.Status
	(*common.Name)(nil),                        // 22: api.typesv2.common.Name
	(*common.PhoneNumber)(nil),                 // 23: api.typesv2.common.PhoneNumber
	(*date.Date)(nil),                          // 24: google.type.Date
	(typesv2.Gender)(0),                        // 25: api.typesv2.Gender
	(*postaladdress.PostalAddress)(nil),        // 26: google.type.PostalAddress
	(*IncomeDetails)(nil),                      // 27: omegle.IncomeDetails
	(*latlng.LatLng)(nil),                      // 28: google.type.LatLng
	(*common.Image)(nil),                       // 29: api.typesv2.common.Image
	(*EmploymentDetails)(nil),                  // 30: omegle.EmploymentDetails
	(enums.ApplicationType)(0),                 // 31: omegle.enums.ApplicationType
	(enums.ApplicantType)(0),                   // 32: omegle.enums.ApplicantType
	(*AdditionalApplicantDetails)(nil),         // 33: omegle.AdditionalApplicantDetails
	(enums.OverallStatus)(0),                   // 34: omegle.enums.OverallStatus
	(enums.FailureReason)(0),                   // 35: omegle.enums.FailureReason
	(enums.CallSubStatus)(0),                   // 36: omegle.enums.CallSubStatus
	(common.BooleanEnum)(0),                    // 37: api.typesv2.common.BooleanEnum
	(*GuardianDetails)(nil),                    // 38: omegle.GuardianDetails
	(*AddressDetails)(nil),                     // 39: omegle.AddressDetails
	(*PreCallInfo)(nil),                        // 40: omegle.PreCallInfo
	(*CallDataDump)(nil),                       // 41: omegle.CallDataDump
	(*AuditorReview)(nil),                      // 42: omegle.AuditorReview
	(*CallEnded)(nil),                          // 43: omegle.CallEnded
	(*timestamppb.Timestamp)(nil),              // 44: google.protobuf.Timestamp
	(*Question)(nil),                           // 45: omegle.Question
	(*typesv2.File)(nil),                       // 46: api.typesv2.File
}
var file_api_omegle_service_proto_depIdxs = []int32{
	4,  // 0: omegle.RegisterUserRequest.application_form_data:type_name -> omegle.ApplicationFormData
	18, // 1: omegle.RegisterUserRequest.client:type_name -> vendorgateway.Vendor
	19, // 2: omegle.RegisterUserRequest.channel:type_name -> omegle.enums.Channel
	20, // 3: omegle.RegisterUserRequest.document_details:type_name -> api.typesv2.DocumentDetails
	21, // 4: omegle.RegisterUserResponse.status:type_name -> rpc.Status
	4,  // 5: omegle.UpdateApplicantDetailsRequest.application_form_data:type_name -> omegle.ApplicationFormData
	20, // 6: omegle.UpdateApplicantDetailsRequest.document_details:type_name -> api.typesv2.DocumentDetails
	21, // 7: omegle.UpdateApplicantDetailsResponse.status:type_name -> rpc.Status
	22, // 8: omegle.ApplicationFormData.applicant_name:type_name -> api.typesv2.common.Name
	23, // 9: omegle.ApplicationFormData.phone_number:type_name -> api.typesv2.common.PhoneNumber
	22, // 10: omegle.ApplicationFormData.father_name:type_name -> api.typesv2.common.Name
	22, // 11: omegle.ApplicationFormData.mother_name:type_name -> api.typesv2.common.Name
	24, // 12: omegle.ApplicationFormData.dob:type_name -> google.type.Date
	25, // 13: omegle.ApplicationFormData.gender:type_name -> api.typesv2.Gender
	26, // 14: omegle.ApplicationFormData.communication_address:type_name -> google.type.PostalAddress
	26, // 15: omegle.ApplicationFormData.permanent_address:type_name -> google.type.PostalAddress
	27, // 16: omegle.ApplicationFormData.incomeDetails:type_name -> omegle.IncomeDetails
	28, // 17: omegle.ApplicationFormData.location:type_name -> google.type.LatLng
	29, // 18: omegle.ApplicationFormData.image:type_name -> api.typesv2.common.Image
	30, // 19: omegle.ApplicationFormData.employment_details:type_name -> omegle.EmploymentDetails
	31, // 20: omegle.ApplicationFormData.application_type:type_name -> omegle.enums.ApplicationType
	32, // 21: omegle.ApplicationFormData.applicant_type:type_name -> omegle.enums.ApplicantType
	33, // 22: omegle.ApplicationFormData.additional_applicant_details:type_name -> omegle.AdditionalApplicantDetails
	21, // 23: omegle.StartCallResponse.status:type_name -> rpc.Status
	21, // 24: omegle.CheckCallStatusResponse.status:type_name -> rpc.Status
	34, // 25: omegle.CheckCallStatusResponse.call_status:type_name -> omegle.enums.OverallStatus
	35, // 26: omegle.CheckCallStatusResponse.failure_reason:type_name -> omegle.enums.FailureReason
	36, // 27: omegle.CheckCallStatusResponse.call_sub_status:type_name -> omegle.enums.CallSubStatus
	37, // 28: omegle.CheckCallStatusResponse.is_terminal_failure:type_name -> api.typesv2.common.BooleanEnum
	21, // 29: omegle.GetApplicantDetailsResponse.status:type_name -> rpc.Status
	22, // 30: omegle.GetApplicantDetailsResponse.applicant_name:type_name -> api.typesv2.common.Name
	24, // 31: omegle.GetApplicantDetailsResponse.dob:type_name -> google.type.Date
	25, // 32: omegle.GetApplicantDetailsResponse.gender:type_name -> api.typesv2.Gender
	27, // 33: omegle.GetApplicantDetailsResponse.income_details:type_name -> omegle.IncomeDetails
	30, // 34: omegle.GetApplicantDetailsResponse.employment_details:type_name -> omegle.EmploymentDetails
	38, // 35: omegle.GetApplicantDetailsResponse.guardian_details:type_name -> omegle.GuardianDetails
	29, // 36: omegle.GetApplicantDetailsResponse.image:type_name -> api.typesv2.common.Image
	23, // 37: omegle.GetApplicantDetailsResponse.mobile_number:type_name -> api.typesv2.common.PhoneNumber
	20, // 38: omegle.GetApplicantDetailsResponse.document_details:type_name -> api.typesv2.DocumentDetails
	39, // 39: omegle.GetApplicantDetailsResponse.address_details:type_name -> omegle.AddressDetails
	19, // 40: omegle.GetApplicantDetailsResponse.channel:type_name -> omegle.enums.Channel
	40, // 41: omegle.StoreCallAndAuditDetailsRequest.pre_call_info:type_name -> omegle.PreCallInfo
	41, // 42: omegle.StoreCallAndAuditDetailsRequest.call_data_dump:type_name -> omegle.CallDataDump
	42, // 43: omegle.StoreCallAndAuditDetailsRequest.auditor_review:type_name -> omegle.AuditorReview
	43, // 44: omegle.StoreCallAndAuditDetailsRequest.call_ended:type_name -> omegle.CallEnded
	21, // 45: omegle.StoreCallAndAuditDetailsResponse.status:type_name -> rpc.Status
	21, // 46: omegle.GetPendingAuditCallsResponse.status:type_name -> rpc.Status
	15, // 47: omegle.GetPendingAuditCallsResponse.pending_call_details_list:type_name -> omegle.PendingCallDetails
	44, // 48: omegle.PendingCallDetails.call_completed_at:type_name -> google.protobuf.Timestamp
	21, // 49: omegle.GetPendingAuditCallDetailsResponse.status:type_name -> rpc.Status
	44, // 50: omegle.GetPendingAuditCallDetailsResponse.call_completed_at:type_name -> google.protobuf.Timestamp
	45, // 51: omegle.GetPendingAuditCallDetailsResponse.Questions:type_name -> omegle.Question
	46, // 52: omegle.GetPendingAuditCallDetailsResponse.agent_dash_call_recording:type_name -> api.typesv2.File
	0,  // 53: omegle.Omegle.RegisterUser:input_type -> omegle.RegisterUserRequest
	2,  // 54: omegle.Omegle.UpdateApplicantDetails:input_type -> omegle.UpdateApplicantDetailsRequest
	5,  // 55: omegle.Omegle.StartCall:input_type -> omegle.StartCallRequest
	7,  // 56: omegle.Omegle.CheckCallStatus:input_type -> omegle.CheckCallStatusRequest
	9,  // 57: omegle.Omegle.GetApplicantDetails:input_type -> omegle.GetApplicantDetailsRequest
	11, // 58: omegle.Omegle.StoreCallAndAuditDetails:input_type -> omegle.StoreCallAndAuditDetailsRequest
	13, // 59: omegle.Omegle.GetPendingAuditCalls:input_type -> omegle.GetPendingAuditCallsRequest
	16, // 60: omegle.Omegle.GetPendingAuditCallDetails:input_type -> omegle.GetPendingAuditCallDetailsRequest
	1,  // 61: omegle.Omegle.RegisterUser:output_type -> omegle.RegisterUserResponse
	3,  // 62: omegle.Omegle.UpdateApplicantDetails:output_type -> omegle.UpdateApplicantDetailsResponse
	6,  // 63: omegle.Omegle.StartCall:output_type -> omegle.StartCallResponse
	8,  // 64: omegle.Omegle.CheckCallStatus:output_type -> omegle.CheckCallStatusResponse
	10, // 65: omegle.Omegle.GetApplicantDetails:output_type -> omegle.GetApplicantDetailsResponse
	12, // 66: omegle.Omegle.StoreCallAndAuditDetails:output_type -> omegle.StoreCallAndAuditDetailsResponse
	14, // 67: omegle.Omegle.GetPendingAuditCalls:output_type -> omegle.GetPendingAuditCallsResponse
	17, // 68: omegle.Omegle.GetPendingAuditCallDetails:output_type -> omegle.GetPendingAuditCallDetailsResponse
	61, // [61:69] is the sub-list for method output_type
	53, // [53:61] is the sub-list for method input_type
	53, // [53:53] is the sub-list for extension type_name
	53, // [53:53] is the sub-list for extension extendee
	0,  // [0:53] is the sub-list for field type_name
}

func init() { file_api_omegle_service_proto_init() }
func file_api_omegle_service_proto_init() {
	if File_api_omegle_service_proto != nil {
		return
	}
	file_api_omegle_call_info_proto_init()
	file_api_omegle_user_application_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_omegle_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateApplicantDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateApplicantDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicationFormData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartCallRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartCallResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCallStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCallStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicantDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicantDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreCallAndAuditDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreCallAndAuditDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPendingAuditCallsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPendingAuditCallsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PendingCallDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPendingAuditCallDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPendingAuditCallDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_omegle_service_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*StoreCallAndAuditDetailsRequest_PreCallInfo)(nil),
		(*StoreCallAndAuditDetailsRequest_CallDataDump)(nil),
		(*StoreCallAndAuditDetailsRequest_AuditorReview)(nil),
		(*StoreCallAndAuditDetailsRequest_CallEnded)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_omegle_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_omegle_service_proto_goTypes,
		DependencyIndexes: file_api_omegle_service_proto_depIdxs,
		MessageInfos:      file_api_omegle_service_proto_msgTypes,
	}.Build()
	File_api_omegle_service_proto = out.File
	file_api_omegle_service_proto_rawDesc = nil
	file_api_omegle_service_proto_goTypes = nil
	file_api_omegle_service_proto_depIdxs = nil
}
