syntax = "proto3";

package frontend.genie;

option go_package = "github.com/epifi/gamma/api/frontend/genie";
option java_package = "com.github.epifi.gamma.api.frontend.genie";

message BKYCRecord {
  // docs ref: https://uidai.gov.in/images/FrontPageUpdates/aadhaar_authentication_api_2_0.pdf

  // Postal Pincode
  string pincode = 1;

  // Post office name. Sometimes, addresses in India are identified
  // by the Post Office, especially in villages
  string postoffice = 2;

  // Gender of the customer
  string gender = 3;

  // Tehsil. Ref: https://dot.gov.in/sites/default/files/Re-verification%20instructions%2023.03.2017.pdf
  string locality = 4;

  // Village or Town or City name.
  string vtcname = 5;

  // Base64 encoded data
  string photo = 6;

  // Related person's name. e.g. value "S/O: Jolly Joseph"
  string careof = 7;

  // Registered mobile number of the customer
  string phone = 8;

  // Date of Birth in format "26-06-1989"
  string dob = 9;

  // Street Identifier
  string street = 10;

  // District / City
  string district = 11;

  // House identifer
  string houseno = 12;

  string state = 13;
  string landmark = 14;
  string email = 15;

  // Customer's Name
  string name = 17;
}

message FailureReason {
  string auth_error_code = 1;
  string error_code = 2;
}

// federal response client received
message FedBKYCResponse {
  // "Y" for success, "N" for failure
  string status = 1;

  // this is used as UID reference key by KYC
  string transaction_id = 2;
  BKYCRecord record = 3;

  FailureReason failure_reason = 4;
}
