syntax = "proto3";

package frontend.inapphelp.actor_activity;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/frontend/inapphelp/actor_activity";
option java_package = "com.github.epifi.gamma.api.frontend.inapphelp.actor_activity";

// a panel can contain multiple recent activity related details with a heading
// refer : https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=5727-11461&t=gTdT8gdMw2cZnkpS-0
message ActivityPanel {
  // heading of the panel of recent activity
  // for example : Need help on your recent activity?, January 2023 etc
  api.typesv2.common.Text heading = 1;
  // a panel can contain multiple activity lists
  // each list can contain recent activities grouped by a specific parameter, for example month and year
  repeated ActivityList activity_lists = 2;
}

message ActivityList {
  // heading of the activity list
  // for example : WEDNESDAY, 2 JANUARY etc
  api.typesv2.common.Text heading = 1;
  // a list of activities
  repeated Activity activities = 2;
}

// RecentActivity contains details of a single element of a list of recent activities
// The list of multiple activities would be shown
message Activity {
  // primary image/icon related to the recent activity
  api.typesv2.common.Image image = 1;
  // text key,value related to the individual recent activity
  // for example : "Travel Fund" as key and "FIT - Keep the change" as value
  api.typesv2.ui.VerticalKeyValuePair text_key_value_pair = 2;
  // icon text component, which can provide additional info related to a recent activity
  // for example : in case of a fund transfer this can be the amount debited/credited
  // or in case of chequebook request it can be an icon showing the status as requested
  api.typesv2.ui.IconTextComponent activity_info = 3;
  // search terms would be used by client to support search
  // to identify this recent activity
  repeated string search_terms = 4;
  // identifier for this activity to fetch more details if required
  string activity_identifier = 5;
  // secondary image related to the recent activity
  // this is demonstrated as a mini image overlapped on top of primary image at bottom right
  // in figma
  api.typesv2.common.Image secondary_image = 6;
  // timestamp when this activity was reported to backend actor activity platform
  google.protobuf.Timestamp reported_at = 7;
}

// ActivityDetails contains all the details about a single activity
// that need to be shown to the user
// refer figma : https://www.figma.com/design/GGiPlpxpvYa2eHdg68m0pg/Help-%E2%80%A2-Workfile?node-id=6849-74184&t=6FRnrJdJizquRaDM-0
message ActivityDetails {
  // image/icon related to the activity
  api.typesv2.common.Image image = 1;
  // title of the activity
  api.typesv2.common.Text title = 2;
  // title_meta can contain additional information related to the title
  // for example, amount in case of transaction activity
  // or number of fi coins awarded in case of a rewards activity
  api.typesv2.ui.IconTextComponent title_meta = 3;
  // description field can contain miscellaneous details about the activity
  // for example, for a transaction it can contain for what purpose was the transaction for
  api.typesv2.ui.IconTextComponent description = 4;
  // status field can be used to denote more details about an activity status
  // for example, in case of a transaction it can contain the status of the transaction
  // or in case of a reward, it can contain more details about the reward
  api.typesv2.ui.IconTextComponent status = 5;
  // section containing more details about the respective activity
  InfoSection info_section = 6;
  // contains section to contact support
  ContactUsSection contact_us_section = 7;
}

message InfoSection {
  // title of the panel
  api.typesv2.common.Text title = 1;
  // "view more" CTA/button which will open another screen on app
  // containing more details of the activity
  api.typesv2.ui.IconTextComponent view_more = 2;
  // vertical key value pairs containing more details of the activity
  // for example : in case of a transaction it can contain fields like from, to, transaction mode, transaction id, time etc
  repeated api.typesv2.ui.VerticalKeyValuePair activity_details = 3;
}

// ContactUsSection is only meant to be used to show users
// the CTAs to contact support under a single recent activity
message ContactUsSection {
  // heading of the contact us section
  // for example : "How would you like to report this issue ?"
  api.typesv2.common.Text heading = 1;
  // list of CTAs containing actions that can be used for contact
  // for example : chat, call, email etc
  repeated frontend.deeplink.Cta ctas = 2;
}
