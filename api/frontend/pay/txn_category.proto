// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package frontend.pay;

import "api/typesv2/ui/icon_text_component.proto";
import "google/protobuf/duration.proto";


option go_package = "github.com/epifi/gamma/api/frontend/pay";
option java_package = "com.github.epifi.gamma.api.frontend.pay";

message TransactionCategoriesData {
  // categories which are applied on the txn
  repeated frontend.pay.TransactionCategory applied_categories = 1;
  // suggested categories which user can tap on to apply
  repeated frontend.pay.TransactionCategory suggested_categories = 2;
  // text below categories to inform about tap action e.g. "Tap on any to update it"
  api.typesv2.ui.IconTextComponent footer_info = 3;
  // text to be displayed when user lands on order receipt page for first time to inform about the category update on tap
  api.typesv2.ui.IconTextComponent pre_update_info_text = 4;
  // text to be displayed when user updates the category in the first order receipt load
  api.typesv2.ui.IconTextComponent post_update_info_text = 5;
  // time after which the pre_update_info_text is rendered on screen after loading the order receipt page
  google.protobuf.Duration pre_update_info_text_display_after_duration = 6;
}


// TransactionCategory represents an encapsulation for a transaction category meta data to be sent to the client
// The client may use this object to display the information on various screens
message TransactionCategory {
  // name to be displayed on the app corresponding to the category
  string display_name = 1;
  // icon corresponding to the category
  string icon_url = 2;
  // Id can be used to uniquely identify the transaction category. UI can send the category ids when recategorizing a transaction.
  string id = 3;
  // Array of search hints within the Category to enhance the frontend Category Search.
  repeated string search_hints = 4;
}

// TransactionScope - represents the scope of the payment for which eligible accounts need to be found
enum PaymentScope {
  // all the accounts eligible for domestic payments come in scope of PAYMENT_SCOPE_DOMESTIC
  PAYMENT_SCOPE_DOMESTIC = 0;
  // all the accounts eligible for international payments come in the scope of PAYMENT_SCOPE_INTERNATIONAL
  PAYMENT_SCOPE_INTERNATIONAL = 1;
}

