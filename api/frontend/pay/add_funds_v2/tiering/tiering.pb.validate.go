// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/pay/add_funds_v2/tiering/tiering.proto

package tiering

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TieringCardOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TieringCardOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TieringCardOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TieringCardOptionsMultiError, or nil if none found.
func (m *TieringCardOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *TieringCardOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringCardOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringCardOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringCardOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringCardOptionsValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringCardOptionsValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringCardOptionsValidationError{
				field:  "BgColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProgressBarDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringCardOptionsValidationError{
					field:  "ProgressBarDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringCardOptionsValidationError{
					field:  "ProgressBarDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProgressBarDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringCardOptionsValidationError{
				field:  "ProgressBarDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringCardOptionsValidationError{
					field:  "BottomInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringCardOptionsValidationError{
					field:  "BottomInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringCardOptionsValidationError{
				field:  "BottomInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomInfoV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringCardOptionsValidationError{
					field:  "BottomInfoV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringCardOptionsValidationError{
					field:  "BottomInfoV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomInfoV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringCardOptionsValidationError{
				field:  "BottomInfoV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TieringCardOptionsMultiError(errors)
	}

	return nil
}

// TieringCardOptionsMultiError is an error wrapping multiple validation errors
// returned by TieringCardOptions.ValidateAll() if the designated constraints
// aren't met.
type TieringCardOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TieringCardOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TieringCardOptionsMultiError) AllErrors() []error { return m }

// TieringCardOptionsValidationError is the validation error returned by
// TieringCardOptions.Validate if the designated constraints aren't met.
type TieringCardOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TieringCardOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TieringCardOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TieringCardOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TieringCardOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TieringCardOptionsValidationError) ErrorName() string {
	return "TieringCardOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e TieringCardOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTieringCardOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TieringCardOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TieringCardOptionsValidationError{}

// Validate checks the field values on TieringCardBottomInfoV2 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TieringCardBottomInfoV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TieringCardBottomInfoV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TieringCardBottomInfoV2MultiError, or nil if none found.
func (m *TieringCardBottomInfoV2) ValidateAll() error {
	return m.validate(true)
}

func (m *TieringCardBottomInfoV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.BottomInfo.(type) {
	case *TieringCardBottomInfoV2_BottomItc:
		if v == nil {
			err := TieringCardBottomInfoV2ValidationError{
				field:  "BottomInfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBottomItc()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TieringCardBottomInfoV2ValidationError{
						field:  "BottomItc",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TieringCardBottomInfoV2ValidationError{
						field:  "BottomItc",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBottomItc()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TieringCardBottomInfoV2ValidationError{
					field:  "BottomItc",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return TieringCardBottomInfoV2MultiError(errors)
	}

	return nil
}

// TieringCardBottomInfoV2MultiError is an error wrapping multiple validation
// errors returned by TieringCardBottomInfoV2.ValidateAll() if the designated
// constraints aren't met.
type TieringCardBottomInfoV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TieringCardBottomInfoV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TieringCardBottomInfoV2MultiError) AllErrors() []error { return m }

// TieringCardBottomInfoV2ValidationError is the validation error returned by
// TieringCardBottomInfoV2.Validate if the designated constraints aren't met.
type TieringCardBottomInfoV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TieringCardBottomInfoV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TieringCardBottomInfoV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TieringCardBottomInfoV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TieringCardBottomInfoV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TieringCardBottomInfoV2ValidationError) ErrorName() string {
	return "TieringCardBottomInfoV2ValidationError"
}

// Error satisfies the builtin error interface
func (e TieringCardBottomInfoV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTieringCardBottomInfoV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TieringCardBottomInfoV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TieringCardBottomInfoV2ValidationError{}

// Validate checks the field values on TieringSuggestionOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TieringSuggestionOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TieringSuggestionOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TieringSuggestionOptionsMultiError, or nil if none found.
func (m *TieringSuggestionOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *TieringSuggestionOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetImageUrl()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringSuggestionOptionsValidationError{
					field:  "ImageUrl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringSuggestionOptionsValidationError{
					field:  "ImageUrl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImageUrl()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringSuggestionOptionsValidationError{
				field:  "ImageUrl",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringSuggestionOptionsValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringSuggestionOptionsValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringSuggestionOptionsValidationError{
				field:  "BgColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringSuggestionOptionsValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringSuggestionOptionsValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringSuggestionOptionsValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TieringSuggestionOptionsMultiError(errors)
	}

	return nil
}

// TieringSuggestionOptionsMultiError is an error wrapping multiple validation
// errors returned by TieringSuggestionOptions.ValidateAll() if the designated
// constraints aren't met.
type TieringSuggestionOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TieringSuggestionOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TieringSuggestionOptionsMultiError) AllErrors() []error { return m }

// TieringSuggestionOptionsValidationError is the validation error returned by
// TieringSuggestionOptions.Validate if the designated constraints aren't met.
type TieringSuggestionOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TieringSuggestionOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TieringSuggestionOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TieringSuggestionOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TieringSuggestionOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TieringSuggestionOptionsValidationError) ErrorName() string {
	return "TieringSuggestionOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e TieringSuggestionOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTieringSuggestionOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TieringSuggestionOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TieringSuggestionOptionsValidationError{}

// Validate checks the field values on ProgressBarDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProgressBarDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProgressBarDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProgressBarDetailsMultiError, or nil if none found.
func (m *ProgressBarDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ProgressBarDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStartNode()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProgressBarDetailsValidationError{
					field:  "StartNode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProgressBarDetailsValidationError{
					field:  "StartNode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartNode()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProgressBarDetailsValidationError{
				field:  "StartNode",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentProgressNode()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProgressBarDetailsValidationError{
					field:  "CurrentProgressNode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProgressBarDetailsValidationError{
					field:  "CurrentProgressNode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentProgressNode()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProgressBarDetailsValidationError{
				field:  "CurrentProgressNode",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndNode()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProgressBarDetailsValidationError{
					field:  "EndNode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProgressBarDetailsValidationError{
					field:  "EndNode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndNode()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProgressBarDetailsValidationError{
				field:  "EndNode",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFilledProgressBarColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProgressBarDetailsValidationError{
					field:  "FilledProgressBarColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProgressBarDetailsValidationError{
					field:  "FilledProgressBarColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilledProgressBarColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProgressBarDetailsValidationError{
				field:  "FilledProgressBarColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUnfilledProgressBarColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProgressBarDetailsValidationError{
					field:  "UnfilledProgressBarColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProgressBarDetailsValidationError{
					field:  "UnfilledProgressBarColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnfilledProgressBarColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProgressBarDetailsValidationError{
				field:  "UnfilledProgressBarColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Progress

	if len(errors) > 0 {
		return ProgressBarDetailsMultiError(errors)
	}

	return nil
}

// ProgressBarDetailsMultiError is an error wrapping multiple validation errors
// returned by ProgressBarDetails.ValidateAll() if the designated constraints
// aren't met.
type ProgressBarDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProgressBarDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProgressBarDetailsMultiError) AllErrors() []error { return m }

// ProgressBarDetailsValidationError is the validation error returned by
// ProgressBarDetails.Validate if the designated constraints aren't met.
type ProgressBarDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProgressBarDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProgressBarDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProgressBarDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProgressBarDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProgressBarDetailsValidationError) ErrorName() string {
	return "ProgressBarDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ProgressBarDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProgressBarDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProgressBarDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProgressBarDetailsValidationError{}

// Validate checks the field values on ProgressBarNode with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ProgressBarNode) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProgressBarNode with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProgressBarNodeMultiError, or nil if none found.
func (m *ProgressBarNode) ValidateAll() error {
	return m.validate(true)
}

func (m *ProgressBarNode) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNodeImageUrl()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProgressBarNodeValidationError{
					field:  "NodeImageUrl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProgressBarNodeValidationError{
					field:  "NodeImageUrl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNodeImageUrl()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProgressBarNodeValidationError{
				field:  "NodeImageUrl",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNodeTitleImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProgressBarNodeValidationError{
					field:  "NodeTitleImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProgressBarNodeValidationError{
					field:  "NodeTitleImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNodeTitleImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProgressBarNodeValidationError{
				field:  "NodeTitleImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNodeTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProgressBarNodeValidationError{
					field:  "NodeTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProgressBarNodeValidationError{
					field:  "NodeTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNodeTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProgressBarNodeValidationError{
				field:  "NodeTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNodeAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProgressBarNodeValidationError{
					field:  "NodeAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProgressBarNodeValidationError{
					field:  "NodeAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNodeAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProgressBarNodeValidationError{
				field:  "NodeAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProgressBarNodeMultiError(errors)
	}

	return nil
}

// ProgressBarNodeMultiError is an error wrapping multiple validation errors
// returned by ProgressBarNode.ValidateAll() if the designated constraints
// aren't met.
type ProgressBarNodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProgressBarNodeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProgressBarNodeMultiError) AllErrors() []error { return m }

// ProgressBarNodeValidationError is the validation error returned by
// ProgressBarNode.Validate if the designated constraints aren't met.
type ProgressBarNodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProgressBarNodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProgressBarNodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProgressBarNodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProgressBarNodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProgressBarNodeValidationError) ErrorName() string { return "ProgressBarNodeValidationError" }

// Error satisfies the builtin error interface
func (e ProgressBarNodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProgressBarNode.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProgressBarNodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProgressBarNodeValidationError{}

// Validate checks the field values on TieringCardBottomInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TieringCardBottomInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TieringCardBottomInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TieringCardBottomInfoMultiError, or nil if none found.
func (m *TieringCardBottomInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *TieringCardBottomInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringCardBottomInfoValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringCardBottomInfoValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringCardBottomInfoValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringCardBottomInfoValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringCardBottomInfoValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringCardBottomInfoValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringCardBottomInfoValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringCardBottomInfoValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringCardBottomInfoValidationError{
				field:  "BgColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TieringCardBottomInfoMultiError(errors)
	}

	return nil
}

// TieringCardBottomInfoMultiError is an error wrapping multiple validation
// errors returned by TieringCardBottomInfo.ValidateAll() if the designated
// constraints aren't met.
type TieringCardBottomInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TieringCardBottomInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TieringCardBottomInfoMultiError) AllErrors() []error { return m }

// TieringCardBottomInfoValidationError is the validation error returned by
// TieringCardBottomInfo.Validate if the designated constraints aren't met.
type TieringCardBottomInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TieringCardBottomInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TieringCardBottomInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TieringCardBottomInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TieringCardBottomInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TieringCardBottomInfoValidationError) ErrorName() string {
	return "TieringCardBottomInfoValidationError"
}

// Error satisfies the builtin error interface
func (e TieringCardBottomInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTieringCardBottomInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TieringCardBottomInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TieringCardBottomInfoValidationError{}
