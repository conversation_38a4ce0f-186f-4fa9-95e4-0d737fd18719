// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/cx/dispute/enums.proto

package dispute

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// type of dispute
type DisputeType int32

const (
	DisputeType_DISPUTE_TYPE_UNSPECIFIED DisputeType = 0
	// the transaction was authorised by the user
	DisputeType_AUTHORISED DisputeType = 1
	// for unauthorised transaction(fraud cases)
	DisputeType_UNAUTHORISED DisputeType = 2
	// beneficiary information was incorrect
	DisputeType_WRONG_BENEFICIARY DisputeType = 3
	// incorrect account number given by user
	DisputeType_WRONG_ACCOUNT_NUMBER DisputeType = 4
	// incorrect amount was sent for current transaction
	DisputeType_WRONG_AMOUNT DisputeType = 5
)

// Enum value maps for DisputeType.
var (
	DisputeType_name = map[int32]string{
		0: "DISPUTE_TYPE_UNSPECIFIED",
		1: "AUTHORISED",
		2: "UNAUTHORISED",
		3: "WRONG_BENEFICIARY",
		4: "WRONG_ACCOUNT_NUMBER",
		5: "WRONG_AMOUNT",
	}
	DisputeType_value = map[string]int32{
		"DISPUTE_TYPE_UNSPECIFIED": 0,
		"AUTHORISED":               1,
		"UNAUTHORISED":             2,
		"WRONG_BENEFICIARY":        3,
		"WRONG_ACCOUNT_NUMBER":     4,
		"WRONG_AMOUNT":             5,
	}
)

func (x DisputeType) Enum() *DisputeType {
	p := new(DisputeType)
	*p = x
	return p
}

func (x DisputeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisputeType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_cx_dispute_enums_proto_enumTypes[0].Descriptor()
}

func (DisputeType) Type() protoreflect.EnumType {
	return &file_api_frontend_cx_dispute_enums_proto_enumTypes[0]
}

func (x DisputeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisputeType.Descriptor instead.
func (DisputeType) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_cx_dispute_enums_proto_rawDescGZIP(), []int{0}
}

// this indicates the type of input to be shown on UI for current question
type AnswerDataType int32

const (
	AnswerDataType_ANSWER_DATA_TYPE_UNSPECIFIED AnswerDataType = 0
	// dropdown should be shown for current question
	// option values for the dropdown will be given in the question meta message
	AnswerDataType_DROPDOWN AnswerDataType = 1
	// text field should be shown for current question
	AnswerDataType_TEXT AnswerDataType = 2
	// input type should be amount for current question
	AnswerDataType_AMOUNT AnswerDataType = 3
	// inpute type should be date for current question
	AnswerDataType_DATE AnswerDataType = 4
	// inpute type should be number for current question
	AnswerDataType_NUMBER AnswerDataType = 5
	// for question with this answer data type, we don't need take any user input and just include this question in the results with empty value
	AnswerDataType_NO_INPUT AnswerDataType = 6
)

// Enum value maps for AnswerDataType.
var (
	AnswerDataType_name = map[int32]string{
		0: "ANSWER_DATA_TYPE_UNSPECIFIED",
		1: "DROPDOWN",
		2: "TEXT",
		3: "AMOUNT",
		4: "DATE",
		5: "NUMBER",
		6: "NO_INPUT",
	}
	AnswerDataType_value = map[string]int32{
		"ANSWER_DATA_TYPE_UNSPECIFIED": 0,
		"DROPDOWN":                     1,
		"TEXT":                         2,
		"AMOUNT":                       3,
		"DATE":                         4,
		"NUMBER":                       5,
		"NO_INPUT":                     6,
	}
)

func (x AnswerDataType) Enum() *AnswerDataType {
	p := new(AnswerDataType)
	*p = x
	return p
}

func (x AnswerDataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AnswerDataType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_cx_dispute_enums_proto_enumTypes[1].Descriptor()
}

func (AnswerDataType) Type() protoreflect.EnumType {
	return &file_api_frontend_cx_dispute_enums_proto_enumTypes[1]
}

func (x AnswerDataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AnswerDataType.Descriptor instead.
func (AnswerDataType) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_cx_dispute_enums_proto_rawDescGZIP(), []int{1}
}

var File_api_frontend_cx_dispute_enums_proto protoreflect.FileDescriptor

var file_api_frontend_cx_dispute_enums_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63,
	0x78, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x63, 0x78, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x2a, 0x90, 0x01, 0x0a, 0x0b, 0x44,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x49,
	0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x55, 0x54, 0x48,
	0x4f, 0x52, 0x49, 0x53, 0x45, 0x44, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x4e, 0x41, 0x55,
	0x54, 0x48, 0x4f, 0x52, 0x49, 0x53, 0x45, 0x44, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x57, 0x52,
	0x4f, 0x4e, 0x47, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x43, 0x49, 0x41, 0x52, 0x59, 0x10,
	0x03, 0x12, 0x18, 0x0a, 0x14, 0x57, 0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c, 0x57,
	0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x05, 0x2a, 0x7a, 0x0a,
	0x0e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x20, 0x0a, 0x1c, 0x41, 0x4e, 0x53, 0x57, 0x45, 0x52, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x52, 0x4f, 0x50, 0x44, 0x4f, 0x57, 0x4e, 0x10, 0x01, 0x12,
	0x08, 0x0a, 0x04, 0x54, 0x45, 0x58, 0x54, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x4d, 0x4f,
	0x55, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x41, 0x54, 0x45, 0x10, 0x04, 0x12,
	0x0a, 0x0a, 0x06, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x4e,
	0x4f, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x10, 0x06, 0x42, 0x60, 0x0a, 0x2e, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x63, 0x78, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x5a, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2f, 0x63, 0x78, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_cx_dispute_enums_proto_rawDescOnce sync.Once
	file_api_frontend_cx_dispute_enums_proto_rawDescData = file_api_frontend_cx_dispute_enums_proto_rawDesc
)

func file_api_frontend_cx_dispute_enums_proto_rawDescGZIP() []byte {
	file_api_frontend_cx_dispute_enums_proto_rawDescOnce.Do(func() {
		file_api_frontend_cx_dispute_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_cx_dispute_enums_proto_rawDescData)
	})
	return file_api_frontend_cx_dispute_enums_proto_rawDescData
}

var file_api_frontend_cx_dispute_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_frontend_cx_dispute_enums_proto_goTypes = []interface{}{
	(DisputeType)(0),    // 0: frontend.cx.dispute.DisputeType
	(AnswerDataType)(0), // 1: frontend.cx.dispute.AnswerDataType
}
var file_api_frontend_cx_dispute_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_frontend_cx_dispute_enums_proto_init() }
func file_api_frontend_cx_dispute_enums_proto_init() {
	if File_api_frontend_cx_dispute_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_cx_dispute_enums_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_cx_dispute_enums_proto_goTypes,
		DependencyIndexes: file_api_frontend_cx_dispute_enums_proto_depIdxs,
		EnumInfos:         file_api_frontend_cx_dispute_enums_proto_enumTypes,
	}.Build()
	File_api_frontend_cx_dispute_enums_proto = out.File
	file_api_frontend_cx_dispute_enums_proto_rawDesc = nil
	file_api_frontend_cx_dispute_enums_proto_goTypes = nil
	file_api_frontend_cx_dispute_enums_proto_depIdxs = nil
}
