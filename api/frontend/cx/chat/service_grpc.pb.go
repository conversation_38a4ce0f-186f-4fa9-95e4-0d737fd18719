// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/frontend/cx/chat/service.proto

package chat

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Chats_GetChatInitInformationForActor_FullMethodName = "/frontend.cx.chat.Chats/GetChatInitInformationForActor"
	Chats_GetReferenceIdForActor_FullMethodName         = "/frontend.cx.chat.Chats/GetReferenceIdForActor"
	Chats_FetchAccessToken_FullMethodName               = "/frontend.cx.chat.Chats/FetchAccessToken"
)

// ChatsClient is the client API for Chats service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ChatsClient interface {
	// service to fetch custom user properties of the actor, freshchat app keys
	// and mapped reference id to uniquely identify the actor
	GetChatInitInformationForActor(ctx context.Context, in *GetChatInitInformationForActorRequest, opts ...grpc.CallOption) (*GetChatInitInformationForActorResponse, error)
	GetReferenceIdForActor(ctx context.Context, in *GetReferenceIdForActorRequest, opts ...grpc.CallOption) (*GetReferenceIdForActorResponse, error)
	FetchAccessToken(ctx context.Context, in *FetchAccessTokenRequest, opts ...grpc.CallOption) (*FetchAccessTokenResponse, error)
}

type chatsClient struct {
	cc grpc.ClientConnInterface
}

func NewChatsClient(cc grpc.ClientConnInterface) ChatsClient {
	return &chatsClient{cc}
}

func (c *chatsClient) GetChatInitInformationForActor(ctx context.Context, in *GetChatInitInformationForActorRequest, opts ...grpc.CallOption) (*GetChatInitInformationForActorResponse, error) {
	out := new(GetChatInitInformationForActorResponse)
	err := c.cc.Invoke(ctx, Chats_GetChatInitInformationForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatsClient) GetReferenceIdForActor(ctx context.Context, in *GetReferenceIdForActorRequest, opts ...grpc.CallOption) (*GetReferenceIdForActorResponse, error) {
	out := new(GetReferenceIdForActorResponse)
	err := c.cc.Invoke(ctx, Chats_GetReferenceIdForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatsClient) FetchAccessToken(ctx context.Context, in *FetchAccessTokenRequest, opts ...grpc.CallOption) (*FetchAccessTokenResponse, error) {
	out := new(FetchAccessTokenResponse)
	err := c.cc.Invoke(ctx, Chats_FetchAccessToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChatsServer is the server API for Chats service.
// All implementations should embed UnimplementedChatsServer
// for forward compatibility
type ChatsServer interface {
	// service to fetch custom user properties of the actor, freshchat app keys
	// and mapped reference id to uniquely identify the actor
	GetChatInitInformationForActor(context.Context, *GetChatInitInformationForActorRequest) (*GetChatInitInformationForActorResponse, error)
	GetReferenceIdForActor(context.Context, *GetReferenceIdForActorRequest) (*GetReferenceIdForActorResponse, error)
	FetchAccessToken(context.Context, *FetchAccessTokenRequest) (*FetchAccessTokenResponse, error)
}

// UnimplementedChatsServer should be embedded to have forward compatible implementations.
type UnimplementedChatsServer struct {
}

func (UnimplementedChatsServer) GetChatInitInformationForActor(context.Context, *GetChatInitInformationForActorRequest) (*GetChatInitInformationForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChatInitInformationForActor not implemented")
}
func (UnimplementedChatsServer) GetReferenceIdForActor(context.Context, *GetReferenceIdForActorRequest) (*GetReferenceIdForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReferenceIdForActor not implemented")
}
func (UnimplementedChatsServer) FetchAccessToken(context.Context, *FetchAccessTokenRequest) (*FetchAccessTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchAccessToken not implemented")
}

// UnsafeChatsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChatsServer will
// result in compilation errors.
type UnsafeChatsServer interface {
	mustEmbedUnimplementedChatsServer()
}

func RegisterChatsServer(s grpc.ServiceRegistrar, srv ChatsServer) {
	s.RegisterService(&Chats_ServiceDesc, srv)
}

func _Chats_GetChatInitInformationForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChatInitInformationForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatsServer).GetChatInitInformationForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Chats_GetChatInitInformationForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatsServer).GetChatInitInformationForActor(ctx, req.(*GetChatInitInformationForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Chats_GetReferenceIdForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReferenceIdForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatsServer).GetReferenceIdForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Chats_GetReferenceIdForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatsServer).GetReferenceIdForActor(ctx, req.(*GetReferenceIdForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Chats_FetchAccessToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchAccessTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatsServer).FetchAccessToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Chats_FetchAccessToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatsServer).FetchAccessToken(ctx, req.(*FetchAccessTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Chats_ServiceDesc is the grpc.ServiceDesc for Chats service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Chats_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "frontend.cx.chat.Chats",
	HandlerType: (*ChatsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChatInitInformationForActor",
			Handler:    _Chats_GetChatInitInformationForActor_Handler,
		},
		{
			MethodName: "GetReferenceIdForActor",
			Handler:    _Chats_GetReferenceIdForActor_Handler,
		},
		{
			MethodName: "FetchAccessToken",
			Handler:    _Chats_FetchAccessToken_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/frontend/cx/chat/service.proto",
}
