// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/docs/service.proto

package docs

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RouteSignFlowRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RouteSignFlowRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RouteSignFlowRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RouteSignFlowRequestMultiError, or nil if none found.
func (m *RouteSignFlowRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RouteSignFlowRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RouteSignFlowRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RouteSignFlowRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RouteSignFlowRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Blob

	if len(errors) > 0 {
		return RouteSignFlowRequestMultiError(errors)
	}

	return nil
}

// RouteSignFlowRequestMultiError is an error wrapping multiple validation
// errors returned by RouteSignFlowRequest.ValidateAll() if the designated
// constraints aren't met.
type RouteSignFlowRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RouteSignFlowRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RouteSignFlowRequestMultiError) AllErrors() []error { return m }

// RouteSignFlowRequestValidationError is the validation error returned by
// RouteSignFlowRequest.Validate if the designated constraints aren't met.
type RouteSignFlowRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RouteSignFlowRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RouteSignFlowRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RouteSignFlowRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RouteSignFlowRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RouteSignFlowRequestValidationError) ErrorName() string {
	return "RouteSignFlowRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RouteSignFlowRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRouteSignFlowRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RouteSignFlowRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RouteSignFlowRequestValidationError{}

// Validate checks the field values on RouteSignFlowResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RouteSignFlowResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RouteSignFlowResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RouteSignFlowResponseMultiError, or nil if none found.
func (m *RouteSignFlowResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RouteSignFlowResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RouteSignFlowResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RouteSignFlowResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RouteSignFlowResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RouteSignFlowResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RouteSignFlowResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RouteSignFlowResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RouteSignFlowResponseMultiError(errors)
	}

	return nil
}

// RouteSignFlowResponseMultiError is an error wrapping multiple validation
// errors returned by RouteSignFlowResponse.ValidateAll() if the designated
// constraints aren't met.
type RouteSignFlowResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RouteSignFlowResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RouteSignFlowResponseMultiError) AllErrors() []error { return m }

// RouteSignFlowResponseValidationError is the validation error returned by
// RouteSignFlowResponse.Validate if the designated constraints aren't met.
type RouteSignFlowResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RouteSignFlowResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RouteSignFlowResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RouteSignFlowResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RouteSignFlowResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RouteSignFlowResponseValidationError) ErrorName() string {
	return "RouteSignFlowResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RouteSignFlowResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRouteSignFlowResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RouteSignFlowResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RouteSignFlowResponseValidationError{}

// Validate checks the field values on UploadFileRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UploadFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadFileRequestMultiError, or nil if none found.
func (m *UploadFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadFileRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadFileRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadFileRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Data

	// no validation rules for Password

	// no validation rules for ClientReqId

	// no validation rules for Flow

	// no validation rules for Blob

	// no validation rules for FileName

	if len(errors) > 0 {
		return UploadFileRequestMultiError(errors)
	}

	return nil
}

// UploadFileRequestMultiError is an error wrapping multiple validation errors
// returned by UploadFileRequest.ValidateAll() if the designated constraints
// aren't met.
type UploadFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadFileRequestMultiError) AllErrors() []error { return m }

// UploadFileRequestValidationError is the validation error returned by
// UploadFileRequest.Validate if the designated constraints aren't met.
type UploadFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadFileRequestValidationError) ErrorName() string {
	return "UploadFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadFileRequestValidationError{}

// Validate checks the field values on UploadFileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadFileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadFileResponseMultiError, or nil if none found.
func (m *UploadFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadFileResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadFileResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadFileResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadFileResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadFileResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadFileResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UploadFileResponseMultiError(errors)
	}

	return nil
}

// UploadFileResponseMultiError is an error wrapping multiple validation errors
// returned by UploadFileResponse.ValidateAll() if the designated constraints
// aren't met.
type UploadFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadFileResponseMultiError) AllErrors() []error { return m }

// UploadFileResponseValidationError is the validation error returned by
// UploadFileResponse.Validate if the designated constraints aren't met.
type UploadFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadFileResponseValidationError) ErrorName() string {
	return "UploadFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UploadFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadFileResponseValidationError{}
