// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/rewards/offer.proto

package rewards

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.ImageContentType(0)
)

// Validate checks the field values on OfferImage with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OfferImage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferImage with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OfferImageMultiError, or
// nil if none found.
func (m *OfferImage) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferImage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ImageType

	// no validation rules for Url

	if len(errors) > 0 {
		return OfferImageMultiError(errors)
	}

	return nil
}

// OfferImageMultiError is an error wrapping multiple validation errors
// returned by OfferImage.ValidateAll() if the designated constraints aren't met.
type OfferImageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferImageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferImageMultiError) AllErrors() []error { return m }

// OfferImageValidationError is the validation error returned by
// OfferImage.Validate if the designated constraints aren't met.
type OfferImageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferImageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferImageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferImageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferImageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferImageValidationError) ErrorName() string { return "OfferImageValidationError" }

// Error satisfies the builtin error interface
func (e OfferImageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferImage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferImageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferImageValidationError{}

// Validate checks the field values on OfferTnc with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OfferTnc) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferTnc with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OfferTncMultiError, or nil
// if none found.
func (m *OfferTnc) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferTnc) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTncListV2() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferTncValidationError{
						field:  fmt.Sprintf("TncListV2[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferTncValidationError{
						field:  fmt.Sprintf("TncListV2[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferTncValidationError{
					field:  fmt.Sprintf("TncListV2[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return OfferTncMultiError(errors)
	}

	return nil
}

// OfferTncMultiError is an error wrapping multiple validation errors returned
// by OfferTnc.ValidateAll() if the designated constraints aren't met.
type OfferTncMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferTncMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferTncMultiError) AllErrors() []error { return m }

// OfferTncValidationError is the validation error returned by
// OfferTnc.Validate if the designated constraints aren't met.
type OfferTncValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferTncValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferTncValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferTncValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferTncValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferTncValidationError) ErrorName() string { return "OfferTncValidationError" }

// Error satisfies the builtin error interface
func (e OfferTncValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferTnc.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferTncValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferTncValidationError{}

// Validate checks the field values on Offer with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Offer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Offer with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in OfferMultiError, or nil if none found.
func (m *Offer) ValidateAll() error {
	return m.validate(true)
}

func (m *Offer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Price

	if all {
		switch v := interface{}(m.GetDisplayDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "DisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "DisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferValidationError{
				field:  "DisplayDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OfferType

	if all {
		switch v := interface{}(m.GetOfferMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "OfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "OfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferValidationError{
				field:  "OfferMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExternalId

	if all {
		switch v := interface{}(m.GetActiveFrom()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "ActiveFrom",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "ActiveFrom",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActiveFrom()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferValidationError{
				field:  "ActiveFrom",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActiveTill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "ActiveTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "ActiveTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActiveTill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferValidationError{
				field:  "ActiveTill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDiscountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "DiscountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "DiscountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDiscountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferValidationError{
				field:  "DiscountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferValidationError{
					field:  fmt.Sprintf("Tags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return OfferMultiError(errors)
	}

	return nil
}

// OfferMultiError is an error wrapping multiple validation errors returned by
// Offer.ValidateAll() if the designated constraints aren't met.
type OfferMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferMultiError) AllErrors() []error { return m }

// OfferValidationError is the validation error returned by Offer.Validate if
// the designated constraints aren't met.
type OfferValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferValidationError) ErrorName() string { return "OfferValidationError" }

// Error satisfies the builtin error interface
func (e OfferValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOffer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferValidationError{}

// Validate checks the field values on OfferMetadata with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferMetadata with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OfferMetadataMultiError, or
// nil if none found.
func (m *OfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.OfferTypeSpecificMetadata.(type) {
	case *OfferMetadata_GiftCardMetadata:
		if v == nil {
			err := OfferMetadataValidationError{
				field:  "OfferTypeSpecificMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetGiftCardMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "GiftCardMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "GiftCardMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGiftCardMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferMetadataValidationError{
					field:  "GiftCardMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OfferMetadata_CharityMetadata:
		if v == nil {
			err := OfferMetadataValidationError{
				field:  "OfferTypeSpecificMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCharityMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "CharityMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "CharityMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCharityMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferMetadataValidationError{
					field:  "CharityMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OfferMetadata_CouponMetadata:
		if v == nil {
			err := OfferMetadataValidationError{
				field:  "OfferTypeSpecificMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCouponMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "CouponMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "CouponMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCouponMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferMetadataValidationError{
					field:  "CouponMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return OfferMetadataMultiError(errors)
	}

	return nil
}

// OfferMetadataMultiError is an error wrapping multiple validation errors
// returned by OfferMetadata.ValidateAll() if the designated constraints
// aren't met.
type OfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferMetadataMultiError) AllErrors() []error { return m }

// OfferMetadataValidationError is the validation error returned by
// OfferMetadata.Validate if the designated constraints aren't met.
type OfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferMetadataValidationError) ErrorName() string { return "OfferMetadataValidationError" }

// Error satisfies the builtin error interface
func (e OfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferMetadataValidationError{}

// Validate checks the field values on GiftCardOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GiftCardOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GiftCardOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GiftCardOfferMetadataMultiError, or nil if none found.
func (m *GiftCardOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *GiftCardOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GiftCardValue

	if len(errors) > 0 {
		return GiftCardOfferMetadataMultiError(errors)
	}

	return nil
}

// GiftCardOfferMetadataMultiError is an error wrapping multiple validation
// errors returned by GiftCardOfferMetadata.ValidateAll() if the designated
// constraints aren't met.
type GiftCardOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GiftCardOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GiftCardOfferMetadataMultiError) AllErrors() []error { return m }

// GiftCardOfferMetadataValidationError is the validation error returned by
// GiftCardOfferMetadata.Validate if the designated constraints aren't met.
type GiftCardOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GiftCardOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GiftCardOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GiftCardOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GiftCardOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GiftCardOfferMetadataValidationError) ErrorName() string {
	return "GiftCardOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e GiftCardOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGiftCardOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GiftCardOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GiftCardOfferMetadataValidationError{}

// Validate checks the field values on CharityOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CharityOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CharityOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CharityOfferMetadataMultiError, or nil if none found.
func (m *CharityOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *CharityOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CharityAmount

	if len(errors) > 0 {
		return CharityOfferMetadataMultiError(errors)
	}

	return nil
}

// CharityOfferMetadataMultiError is an error wrapping multiple validation
// errors returned by CharityOfferMetadata.ValidateAll() if the designated
// constraints aren't met.
type CharityOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CharityOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CharityOfferMetadataMultiError) AllErrors() []error { return m }

// CharityOfferMetadataValidationError is the validation error returned by
// CharityOfferMetadata.Validate if the designated constraints aren't met.
type CharityOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CharityOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CharityOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CharityOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CharityOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CharityOfferMetadataValidationError) ErrorName() string {
	return "CharityOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e CharityOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCharityOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CharityOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CharityOfferMetadataValidationError{}

// Validate checks the field values on CouponOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CouponOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CouponOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CouponOfferMetadataMultiError, or nil if none found.
func (m *CouponOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *CouponOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CouponCode

	// no validation rules for CouponCodeV2

	if len(errors) > 0 {
		return CouponOfferMetadataMultiError(errors)
	}

	return nil
}

// CouponOfferMetadataMultiError is an error wrapping multiple validation
// errors returned by CouponOfferMetadata.ValidateAll() if the designated
// constraints aren't met.
type CouponOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CouponOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CouponOfferMetadataMultiError) AllErrors() []error { return m }

// CouponOfferMetadataValidationError is the validation error returned by
// CouponOfferMetadata.Validate if the designated constraints aren't met.
type CouponOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CouponOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CouponOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CouponOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CouponOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CouponOfferMetadataValidationError) ErrorName() string {
	return "CouponOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e CouponOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCouponOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CouponOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CouponOfferMetadataValidationError{}

// Validate checks the field values on DiscountDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DiscountDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DiscountDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DiscountDetailsMultiError, or nil if none found.
func (m *DiscountDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *DiscountDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DiscountedPrice

	// no validation rules for DisplayMode

	if len(errors) > 0 {
		return DiscountDetailsMultiError(errors)
	}

	return nil
}

// DiscountDetailsMultiError is an error wrapping multiple validation errors
// returned by DiscountDetails.ValidateAll() if the designated constraints
// aren't met.
type DiscountDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DiscountDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DiscountDetailsMultiError) AllErrors() []error { return m }

// DiscountDetailsValidationError is the validation error returned by
// DiscountDetails.Validate if the designated constraints aren't met.
type DiscountDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DiscountDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DiscountDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DiscountDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DiscountDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DiscountDetailsValidationError) ErrorName() string { return "DiscountDetailsValidationError" }

// Error satisfies the builtin error interface
func (e DiscountDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDiscountDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DiscountDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DiscountDetailsValidationError{}

// Validate checks the field values on Offer_DisplayDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Offer_DisplayDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Offer_DisplayDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Offer_DisplayDetailsMultiError, or nil if none found.
func (m *Offer_DisplayDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *Offer_DisplayDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Desc

	for idx, item := range m.GetImages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, Offer_DisplayDetailsValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, Offer_DisplayDetailsValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return Offer_DisplayDetailsValidationError{
					field:  fmt.Sprintf("Images[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetTnc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Offer_DisplayDetailsValidationError{
					field:  "Tnc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Offer_DisplayDetailsValidationError{
					field:  "Tnc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTnc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Offer_DisplayDetailsValidationError{
				field:  "Tnc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	// no validation rules for AfterRedemptionOfferName

	// no validation rules for OfferTitle

	if all {
		switch v := interface{}(m.GetSalaryAccountTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Offer_DisplayDetailsValidationError{
					field:  "SalaryAccountTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Offer_DisplayDetailsValidationError{
					field:  "SalaryAccountTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSalaryAccountTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Offer_DisplayDetailsValidationError{
				field:  "SalaryAccountTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInoperableInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Offer_DisplayDetailsValidationError{
					field:  "InoperableInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Offer_DisplayDetailsValidationError{
					field:  "InoperableInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInoperableInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Offer_DisplayDetailsValidationError{
				field:  "InoperableInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for HomeTitle

	if all {
		switch v := interface{}(m.GetUnredeemableOfferLabel()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Offer_DisplayDetailsValidationError{
					field:  "UnredeemableOfferLabel",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Offer_DisplayDetailsValidationError{
					field:  "UnredeemableOfferLabel",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnredeemableOfferLabel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Offer_DisplayDetailsValidationError{
				field:  "UnredeemableOfferLabel",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BrandName

	// no validation rules for TileImageContentType

	if all {
		switch v := interface{}(m.GetCtaLabel()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Offer_DisplayDetailsValidationError{
					field:  "CtaLabel",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Offer_DisplayDetailsValidationError{
					field:  "CtaLabel",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCtaLabel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Offer_DisplayDetailsValidationError{
				field:  "CtaLabel",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsOfferNotRedeemable

	if all {
		switch v := interface{}(m.GetCtaText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Offer_DisplayDetailsValidationError{
					field:  "CtaText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Offer_DisplayDetailsValidationError{
					field:  "CtaText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCtaText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Offer_DisplayDetailsValidationError{
				field:  "CtaText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShowGreyedOutOfferCard

	// no validation rules for IsSelectiveEligible

	if len(errors) > 0 {
		return Offer_DisplayDetailsMultiError(errors)
	}

	return nil
}

// Offer_DisplayDetailsMultiError is an error wrapping multiple validation
// errors returned by Offer_DisplayDetails.ValidateAll() if the designated
// constraints aren't met.
type Offer_DisplayDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Offer_DisplayDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Offer_DisplayDetailsMultiError) AllErrors() []error { return m }

// Offer_DisplayDetailsValidationError is the validation error returned by
// Offer_DisplayDetails.Validate if the designated constraints aren't met.
type Offer_DisplayDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Offer_DisplayDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Offer_DisplayDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Offer_DisplayDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Offer_DisplayDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Offer_DisplayDetailsValidationError) ErrorName() string {
	return "Offer_DisplayDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e Offer_DisplayDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOffer_DisplayDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Offer_DisplayDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Offer_DisplayDetailsValidationError{}
