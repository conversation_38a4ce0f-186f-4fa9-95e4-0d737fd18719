// Code generated by MockGen. DO NOT EDIT.
// Source: api/frontend/recurringpayment/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	recurringpayment "github.com/epifi/gamma/api/frontend/recurringpayment"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockRecurringPaymentServiceClient is a mock of RecurringPaymentServiceClient interface.
type MockRecurringPaymentServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockRecurringPaymentServiceClientMockRecorder
}

// MockRecurringPaymentServiceClientMockRecorder is the mock recorder for MockRecurringPaymentServiceClient.
type MockRecurringPaymentServiceClientMockRecorder struct {
	mock *MockRecurringPaymentServiceClient
}

// NewMockRecurringPaymentServiceClient creates a new mock instance.
func NewMockRecurringPaymentServiceClient(ctrl *gomock.Controller) *MockRecurringPaymentServiceClient {
	mock := &MockRecurringPaymentServiceClient{ctrl: ctrl}
	mock.recorder = &MockRecurringPaymentServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRecurringPaymentServiceClient) EXPECT() *MockRecurringPaymentServiceClientMockRecorder {
	return m.recorder
}

// AuthoriseRecurringPaymentAction mocks base method.
func (m *MockRecurringPaymentServiceClient) AuthoriseRecurringPaymentAction(ctx context.Context, in *recurringpayment.AuthoriseRecurringPaymentActionRequest, opts ...grpc.CallOption) (*recurringpayment.AuthoriseRecurringPaymentActionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AuthoriseRecurringPaymentAction", varargs...)
	ret0, _ := ret[0].(*recurringpayment.AuthoriseRecurringPaymentActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AuthoriseRecurringPaymentAction indicates an expected call of AuthoriseRecurringPaymentAction.
func (mr *MockRecurringPaymentServiceClientMockRecorder) AuthoriseRecurringPaymentAction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AuthoriseRecurringPaymentAction", reflect.TypeOf((*MockRecurringPaymentServiceClient)(nil).AuthoriseRecurringPaymentAction), varargs...)
}

// CreateModifyAttempt mocks base method.
func (m *MockRecurringPaymentServiceClient) CreateModifyAttempt(ctx context.Context, in *recurringpayment.CreateModifyAttemptRequest, opts ...grpc.CallOption) (*recurringpayment.CreateModifyAttemptResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateModifyAttempt", varargs...)
	ret0, _ := ret[0].(*recurringpayment.CreateModifyAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateModifyAttempt indicates an expected call of CreateModifyAttempt.
func (mr *MockRecurringPaymentServiceClientMockRecorder) CreateModifyAttempt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateModifyAttempt", reflect.TypeOf((*MockRecurringPaymentServiceClient)(nil).CreateModifyAttempt), varargs...)
}

// CreatePauseOrUnpauseAttempt mocks base method.
func (m *MockRecurringPaymentServiceClient) CreatePauseOrUnpauseAttempt(ctx context.Context, in *recurringpayment.CreatePauseOrUnpauseAttemptRequest, opts ...grpc.CallOption) (*recurringpayment.CreatePauseOrUnpauseAttemptResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreatePauseOrUnpauseAttempt", varargs...)
	ret0, _ := ret[0].(*recurringpayment.CreatePauseOrUnpauseAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePauseOrUnpauseAttempt indicates an expected call of CreatePauseOrUnpauseAttempt.
func (mr *MockRecurringPaymentServiceClientMockRecorder) CreatePauseOrUnpauseAttempt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePauseOrUnpauseAttempt", reflect.TypeOf((*MockRecurringPaymentServiceClient)(nil).CreatePauseOrUnpauseAttempt), varargs...)
}

// CreateRecurringPayment mocks base method.
func (m *MockRecurringPaymentServiceClient) CreateRecurringPayment(ctx context.Context, in *recurringpayment.CreateRecurringPaymentRequest, opts ...grpc.CallOption) (*recurringpayment.CreateRecurringPaymentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateRecurringPayment", varargs...)
	ret0, _ := ret[0].(*recurringpayment.CreateRecurringPaymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRecurringPayment indicates an expected call of CreateRecurringPayment.
func (mr *MockRecurringPaymentServiceClientMockRecorder) CreateRecurringPayment(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRecurringPayment", reflect.TypeOf((*MockRecurringPaymentServiceClient)(nil).CreateRecurringPayment), varargs...)
}

// DeclineRecurringPaymentAction mocks base method.
func (m *MockRecurringPaymentServiceClient) DeclineRecurringPaymentAction(ctx context.Context, in *recurringpayment.DeclineRecurringPaymentActionRequest, opts ...grpc.CallOption) (*recurringpayment.DeclineRecurringPaymentActionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeclineRecurringPaymentAction", varargs...)
	ret0, _ := ret[0].(*recurringpayment.DeclineRecurringPaymentActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeclineRecurringPaymentAction indicates an expected call of DeclineRecurringPaymentAction.
func (mr *MockRecurringPaymentServiceClientMockRecorder) DeclineRecurringPaymentAction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeclineRecurringPaymentAction", reflect.TypeOf((*MockRecurringPaymentServiceClient)(nil).DeclineRecurringPaymentAction), varargs...)
}

// GetExecutionsForRecurringPayment mocks base method.
func (m *MockRecurringPaymentServiceClient) GetExecutionsForRecurringPayment(ctx context.Context, in *recurringpayment.GetExecutionsForRecurringPaymentRequest, opts ...grpc.CallOption) (*recurringpayment.GetExecutionsForRecurringPaymentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExecutionsForRecurringPayment", varargs...)
	ret0, _ := ret[0].(*recurringpayment.GetExecutionsForRecurringPaymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExecutionsForRecurringPayment indicates an expected call of GetExecutionsForRecurringPayment.
func (mr *MockRecurringPaymentServiceClientMockRecorder) GetExecutionsForRecurringPayment(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExecutionsForRecurringPayment", reflect.TypeOf((*MockRecurringPaymentServiceClient)(nil).GetExecutionsForRecurringPayment), varargs...)
}

// GetPaymentDetails mocks base method.
func (m *MockRecurringPaymentServiceClient) GetPaymentDetails(ctx context.Context, in *recurringpayment.GetPaymentDetailsRequest, opts ...grpc.CallOption) (*recurringpayment.GetPaymentDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPaymentDetails", varargs...)
	ret0, _ := ret[0].(*recurringpayment.GetPaymentDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentDetails indicates an expected call of GetPaymentDetails.
func (mr *MockRecurringPaymentServiceClientMockRecorder) GetPaymentDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentDetails", reflect.TypeOf((*MockRecurringPaymentServiceClient)(nil).GetPaymentDetails), varargs...)
}

// GetRecurringPaymentActionStatus mocks base method.
func (m *MockRecurringPaymentServiceClient) GetRecurringPaymentActionStatus(ctx context.Context, in *recurringpayment.GetRecurringPaymentActionStatusRequest, opts ...grpc.CallOption) (*recurringpayment.GetRecurringPaymentActionStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRecurringPaymentActionStatus", varargs...)
	ret0, _ := ret[0].(*recurringpayment.GetRecurringPaymentActionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecurringPaymentActionStatus indicates an expected call of GetRecurringPaymentActionStatus.
func (mr *MockRecurringPaymentServiceClientMockRecorder) GetRecurringPaymentActionStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecurringPaymentActionStatus", reflect.TypeOf((*MockRecurringPaymentServiceClient)(nil).GetRecurringPaymentActionStatus), varargs...)
}

// GetRecurringPaymentActionStatusV1 mocks base method.
func (m *MockRecurringPaymentServiceClient) GetRecurringPaymentActionStatusV1(ctx context.Context, in *recurringpayment.GetRecurringPaymentActionStatusV1Request, opts ...grpc.CallOption) (*recurringpayment.GetRecurringPaymentActionStatusV1Response, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRecurringPaymentActionStatusV1", varargs...)
	ret0, _ := ret[0].(*recurringpayment.GetRecurringPaymentActionStatusV1Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecurringPaymentActionStatusV1 indicates an expected call of GetRecurringPaymentActionStatusV1.
func (mr *MockRecurringPaymentServiceClientMockRecorder) GetRecurringPaymentActionStatusV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecurringPaymentActionStatusV1", reflect.TypeOf((*MockRecurringPaymentServiceClient)(nil).GetRecurringPaymentActionStatusV1), varargs...)
}

// GetRecurringPaymentById mocks base method.
func (m *MockRecurringPaymentServiceClient) GetRecurringPaymentById(ctx context.Context, in *recurringpayment.GetRecurringPaymentByIdRequest, opts ...grpc.CallOption) (*recurringpayment.GetRecurringPaymentByIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRecurringPaymentById", varargs...)
	ret0, _ := ret[0].(*recurringpayment.GetRecurringPaymentByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecurringPaymentById indicates an expected call of GetRecurringPaymentById.
func (mr *MockRecurringPaymentServiceClientMockRecorder) GetRecurringPaymentById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecurringPaymentById", reflect.TypeOf((*MockRecurringPaymentServiceClient)(nil).GetRecurringPaymentById), varargs...)
}

// GetRecurringPaymentsCountForActor mocks base method.
func (m *MockRecurringPaymentServiceClient) GetRecurringPaymentsCountForActor(ctx context.Context, in *recurringpayment.GetRecurringPaymentsCountForActorRequest, opts ...grpc.CallOption) (*recurringpayment.GetRecurringPaymentsCountForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRecurringPaymentsCountForActor", varargs...)
	ret0, _ := ret[0].(*recurringpayment.GetRecurringPaymentsCountForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecurringPaymentsCountForActor indicates an expected call of GetRecurringPaymentsCountForActor.
func (mr *MockRecurringPaymentServiceClientMockRecorder) GetRecurringPaymentsCountForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecurringPaymentsCountForActor", reflect.TypeOf((*MockRecurringPaymentServiceClient)(nil).GetRecurringPaymentsCountForActor), varargs...)
}

// GetRecurringPaymentsForActor mocks base method.
func (m *MockRecurringPaymentServiceClient) GetRecurringPaymentsForActor(ctx context.Context, in *recurringpayment.GetRecurringPaymentsForActorRequest, opts ...grpc.CallOption) (*recurringpayment.GetRecurringPaymentsForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRecurringPaymentsForActor", varargs...)
	ret0, _ := ret[0].(*recurringpayment.GetRecurringPaymentsForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecurringPaymentsForActor indicates an expected call of GetRecurringPaymentsForActor.
func (mr *MockRecurringPaymentServiceClientMockRecorder) GetRecurringPaymentsForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecurringPaymentsForActor", reflect.TypeOf((*MockRecurringPaymentServiceClient)(nil).GetRecurringPaymentsForActor), varargs...)
}

// GetUpcomingTransactions mocks base method.
func (m *MockRecurringPaymentServiceClient) GetUpcomingTransactions(ctx context.Context, in *recurringpayment.GetUpcomingTransactionsRequest, opts ...grpc.CallOption) (*recurringpayment.GetUpcomingTransactionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUpcomingTransactions", varargs...)
	ret0, _ := ret[0].(*recurringpayment.GetUpcomingTransactionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpcomingTransactions indicates an expected call of GetUpcomingTransactions.
func (mr *MockRecurringPaymentServiceClientMockRecorder) GetUpcomingTransactions(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpcomingTransactions", reflect.TypeOf((*MockRecurringPaymentServiceClient)(nil).GetUpcomingTransactions), varargs...)
}

// InitiateCreationAuthorisation mocks base method.
func (m *MockRecurringPaymentServiceClient) InitiateCreationAuthorisation(ctx context.Context, in *recurringpayment.InitiateCreationAuthorisationRequest, opts ...grpc.CallOption) (*recurringpayment.InitiateCreationAuthorisationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateCreationAuthorisation", varargs...)
	ret0, _ := ret[0].(*recurringpayment.InitiateCreationAuthorisationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateCreationAuthorisation indicates an expected call of InitiateCreationAuthorisation.
func (mr *MockRecurringPaymentServiceClientMockRecorder) InitiateCreationAuthorisation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateCreationAuthorisation", reflect.TypeOf((*MockRecurringPaymentServiceClient)(nil).InitiateCreationAuthorisation), varargs...)
}

// RevokeRecurringPayment mocks base method.
func (m *MockRecurringPaymentServiceClient) RevokeRecurringPayment(ctx context.Context, in *recurringpayment.RevokeRecurringPaymentRequest, opts ...grpc.CallOption) (*recurringpayment.RevokeRecurringPaymentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RevokeRecurringPayment", varargs...)
	ret0, _ := ret[0].(*recurringpayment.RevokeRecurringPaymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RevokeRecurringPayment indicates an expected call of RevokeRecurringPayment.
func (mr *MockRecurringPaymentServiceClientMockRecorder) RevokeRecurringPayment(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevokeRecurringPayment", reflect.TypeOf((*MockRecurringPaymentServiceClient)(nil).RevokeRecurringPayment), varargs...)
}

// MockRecurringPaymentServiceServer is a mock of RecurringPaymentServiceServer interface.
type MockRecurringPaymentServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockRecurringPaymentServiceServerMockRecorder
}

// MockRecurringPaymentServiceServerMockRecorder is the mock recorder for MockRecurringPaymentServiceServer.
type MockRecurringPaymentServiceServerMockRecorder struct {
	mock *MockRecurringPaymentServiceServer
}

// NewMockRecurringPaymentServiceServer creates a new mock instance.
func NewMockRecurringPaymentServiceServer(ctrl *gomock.Controller) *MockRecurringPaymentServiceServer {
	mock := &MockRecurringPaymentServiceServer{ctrl: ctrl}
	mock.recorder = &MockRecurringPaymentServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRecurringPaymentServiceServer) EXPECT() *MockRecurringPaymentServiceServerMockRecorder {
	return m.recorder
}

// AuthoriseRecurringPaymentAction mocks base method.
func (m *MockRecurringPaymentServiceServer) AuthoriseRecurringPaymentAction(arg0 context.Context, arg1 *recurringpayment.AuthoriseRecurringPaymentActionRequest) (*recurringpayment.AuthoriseRecurringPaymentActionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AuthoriseRecurringPaymentAction", arg0, arg1)
	ret0, _ := ret[0].(*recurringpayment.AuthoriseRecurringPaymentActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AuthoriseRecurringPaymentAction indicates an expected call of AuthoriseRecurringPaymentAction.
func (mr *MockRecurringPaymentServiceServerMockRecorder) AuthoriseRecurringPaymentAction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AuthoriseRecurringPaymentAction", reflect.TypeOf((*MockRecurringPaymentServiceServer)(nil).AuthoriseRecurringPaymentAction), arg0, arg1)
}

// CreateModifyAttempt mocks base method.
func (m *MockRecurringPaymentServiceServer) CreateModifyAttempt(arg0 context.Context, arg1 *recurringpayment.CreateModifyAttemptRequest) (*recurringpayment.CreateModifyAttemptResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateModifyAttempt", arg0, arg1)
	ret0, _ := ret[0].(*recurringpayment.CreateModifyAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateModifyAttempt indicates an expected call of CreateModifyAttempt.
func (mr *MockRecurringPaymentServiceServerMockRecorder) CreateModifyAttempt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateModifyAttempt", reflect.TypeOf((*MockRecurringPaymentServiceServer)(nil).CreateModifyAttempt), arg0, arg1)
}

// CreatePauseOrUnpauseAttempt mocks base method.
func (m *MockRecurringPaymentServiceServer) CreatePauseOrUnpauseAttempt(arg0 context.Context, arg1 *recurringpayment.CreatePauseOrUnpauseAttemptRequest) (*recurringpayment.CreatePauseOrUnpauseAttemptResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePauseOrUnpauseAttempt", arg0, arg1)
	ret0, _ := ret[0].(*recurringpayment.CreatePauseOrUnpauseAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePauseOrUnpauseAttempt indicates an expected call of CreatePauseOrUnpauseAttempt.
func (mr *MockRecurringPaymentServiceServerMockRecorder) CreatePauseOrUnpauseAttempt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePauseOrUnpauseAttempt", reflect.TypeOf((*MockRecurringPaymentServiceServer)(nil).CreatePauseOrUnpauseAttempt), arg0, arg1)
}

// CreateRecurringPayment mocks base method.
func (m *MockRecurringPaymentServiceServer) CreateRecurringPayment(arg0 context.Context, arg1 *recurringpayment.CreateRecurringPaymentRequest) (*recurringpayment.CreateRecurringPaymentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRecurringPayment", arg0, arg1)
	ret0, _ := ret[0].(*recurringpayment.CreateRecurringPaymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRecurringPayment indicates an expected call of CreateRecurringPayment.
func (mr *MockRecurringPaymentServiceServerMockRecorder) CreateRecurringPayment(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRecurringPayment", reflect.TypeOf((*MockRecurringPaymentServiceServer)(nil).CreateRecurringPayment), arg0, arg1)
}

// DeclineRecurringPaymentAction mocks base method.
func (m *MockRecurringPaymentServiceServer) DeclineRecurringPaymentAction(arg0 context.Context, arg1 *recurringpayment.DeclineRecurringPaymentActionRequest) (*recurringpayment.DeclineRecurringPaymentActionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeclineRecurringPaymentAction", arg0, arg1)
	ret0, _ := ret[0].(*recurringpayment.DeclineRecurringPaymentActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeclineRecurringPaymentAction indicates an expected call of DeclineRecurringPaymentAction.
func (mr *MockRecurringPaymentServiceServerMockRecorder) DeclineRecurringPaymentAction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeclineRecurringPaymentAction", reflect.TypeOf((*MockRecurringPaymentServiceServer)(nil).DeclineRecurringPaymentAction), arg0, arg1)
}

// GetExecutionsForRecurringPayment mocks base method.
func (m *MockRecurringPaymentServiceServer) GetExecutionsForRecurringPayment(arg0 context.Context, arg1 *recurringpayment.GetExecutionsForRecurringPaymentRequest) (*recurringpayment.GetExecutionsForRecurringPaymentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExecutionsForRecurringPayment", arg0, arg1)
	ret0, _ := ret[0].(*recurringpayment.GetExecutionsForRecurringPaymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExecutionsForRecurringPayment indicates an expected call of GetExecutionsForRecurringPayment.
func (mr *MockRecurringPaymentServiceServerMockRecorder) GetExecutionsForRecurringPayment(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExecutionsForRecurringPayment", reflect.TypeOf((*MockRecurringPaymentServiceServer)(nil).GetExecutionsForRecurringPayment), arg0, arg1)
}

// GetPaymentDetails mocks base method.
func (m *MockRecurringPaymentServiceServer) GetPaymentDetails(arg0 context.Context, arg1 *recurringpayment.GetPaymentDetailsRequest) (*recurringpayment.GetPaymentDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentDetails", arg0, arg1)
	ret0, _ := ret[0].(*recurringpayment.GetPaymentDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentDetails indicates an expected call of GetPaymentDetails.
func (mr *MockRecurringPaymentServiceServerMockRecorder) GetPaymentDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentDetails", reflect.TypeOf((*MockRecurringPaymentServiceServer)(nil).GetPaymentDetails), arg0, arg1)
}

// GetRecurringPaymentActionStatus mocks base method.
func (m *MockRecurringPaymentServiceServer) GetRecurringPaymentActionStatus(arg0 context.Context, arg1 *recurringpayment.GetRecurringPaymentActionStatusRequest) (*recurringpayment.GetRecurringPaymentActionStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecurringPaymentActionStatus", arg0, arg1)
	ret0, _ := ret[0].(*recurringpayment.GetRecurringPaymentActionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecurringPaymentActionStatus indicates an expected call of GetRecurringPaymentActionStatus.
func (mr *MockRecurringPaymentServiceServerMockRecorder) GetRecurringPaymentActionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecurringPaymentActionStatus", reflect.TypeOf((*MockRecurringPaymentServiceServer)(nil).GetRecurringPaymentActionStatus), arg0, arg1)
}

// GetRecurringPaymentActionStatusV1 mocks base method.
func (m *MockRecurringPaymentServiceServer) GetRecurringPaymentActionStatusV1(arg0 context.Context, arg1 *recurringpayment.GetRecurringPaymentActionStatusV1Request) (*recurringpayment.GetRecurringPaymentActionStatusV1Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecurringPaymentActionStatusV1", arg0, arg1)
	ret0, _ := ret[0].(*recurringpayment.GetRecurringPaymentActionStatusV1Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecurringPaymentActionStatusV1 indicates an expected call of GetRecurringPaymentActionStatusV1.
func (mr *MockRecurringPaymentServiceServerMockRecorder) GetRecurringPaymentActionStatusV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecurringPaymentActionStatusV1", reflect.TypeOf((*MockRecurringPaymentServiceServer)(nil).GetRecurringPaymentActionStatusV1), arg0, arg1)
}

// GetRecurringPaymentById mocks base method.
func (m *MockRecurringPaymentServiceServer) GetRecurringPaymentById(arg0 context.Context, arg1 *recurringpayment.GetRecurringPaymentByIdRequest) (*recurringpayment.GetRecurringPaymentByIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecurringPaymentById", arg0, arg1)
	ret0, _ := ret[0].(*recurringpayment.GetRecurringPaymentByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecurringPaymentById indicates an expected call of GetRecurringPaymentById.
func (mr *MockRecurringPaymentServiceServerMockRecorder) GetRecurringPaymentById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecurringPaymentById", reflect.TypeOf((*MockRecurringPaymentServiceServer)(nil).GetRecurringPaymentById), arg0, arg1)
}

// GetRecurringPaymentsCountForActor mocks base method.
func (m *MockRecurringPaymentServiceServer) GetRecurringPaymentsCountForActor(arg0 context.Context, arg1 *recurringpayment.GetRecurringPaymentsCountForActorRequest) (*recurringpayment.GetRecurringPaymentsCountForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecurringPaymentsCountForActor", arg0, arg1)
	ret0, _ := ret[0].(*recurringpayment.GetRecurringPaymentsCountForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecurringPaymentsCountForActor indicates an expected call of GetRecurringPaymentsCountForActor.
func (mr *MockRecurringPaymentServiceServerMockRecorder) GetRecurringPaymentsCountForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecurringPaymentsCountForActor", reflect.TypeOf((*MockRecurringPaymentServiceServer)(nil).GetRecurringPaymentsCountForActor), arg0, arg1)
}

// GetRecurringPaymentsForActor mocks base method.
func (m *MockRecurringPaymentServiceServer) GetRecurringPaymentsForActor(arg0 context.Context, arg1 *recurringpayment.GetRecurringPaymentsForActorRequest) (*recurringpayment.GetRecurringPaymentsForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecurringPaymentsForActor", arg0, arg1)
	ret0, _ := ret[0].(*recurringpayment.GetRecurringPaymentsForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecurringPaymentsForActor indicates an expected call of GetRecurringPaymentsForActor.
func (mr *MockRecurringPaymentServiceServerMockRecorder) GetRecurringPaymentsForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecurringPaymentsForActor", reflect.TypeOf((*MockRecurringPaymentServiceServer)(nil).GetRecurringPaymentsForActor), arg0, arg1)
}

// GetUpcomingTransactions mocks base method.
func (m *MockRecurringPaymentServiceServer) GetUpcomingTransactions(arg0 context.Context, arg1 *recurringpayment.GetUpcomingTransactionsRequest) (*recurringpayment.GetUpcomingTransactionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpcomingTransactions", arg0, arg1)
	ret0, _ := ret[0].(*recurringpayment.GetUpcomingTransactionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpcomingTransactions indicates an expected call of GetUpcomingTransactions.
func (mr *MockRecurringPaymentServiceServerMockRecorder) GetUpcomingTransactions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpcomingTransactions", reflect.TypeOf((*MockRecurringPaymentServiceServer)(nil).GetUpcomingTransactions), arg0, arg1)
}

// InitiateCreationAuthorisation mocks base method.
func (m *MockRecurringPaymentServiceServer) InitiateCreationAuthorisation(arg0 context.Context, arg1 *recurringpayment.InitiateCreationAuthorisationRequest) (*recurringpayment.InitiateCreationAuthorisationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateCreationAuthorisation", arg0, arg1)
	ret0, _ := ret[0].(*recurringpayment.InitiateCreationAuthorisationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateCreationAuthorisation indicates an expected call of InitiateCreationAuthorisation.
func (mr *MockRecurringPaymentServiceServerMockRecorder) InitiateCreationAuthorisation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateCreationAuthorisation", reflect.TypeOf((*MockRecurringPaymentServiceServer)(nil).InitiateCreationAuthorisation), arg0, arg1)
}

// RevokeRecurringPayment mocks base method.
func (m *MockRecurringPaymentServiceServer) RevokeRecurringPayment(arg0 context.Context, arg1 *recurringpayment.RevokeRecurringPaymentRequest) (*recurringpayment.RevokeRecurringPaymentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RevokeRecurringPayment", arg0, arg1)
	ret0, _ := ret[0].(*recurringpayment.RevokeRecurringPaymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RevokeRecurringPayment indicates an expected call of RevokeRecurringPayment.
func (mr *MockRecurringPaymentServiceServerMockRecorder) RevokeRecurringPayment(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevokeRecurringPayment", reflect.TypeOf((*MockRecurringPaymentServiceServer)(nil).RevokeRecurringPayment), arg0, arg1)
}

// MockUnsafeRecurringPaymentServiceServer is a mock of UnsafeRecurringPaymentServiceServer interface.
type MockUnsafeRecurringPaymentServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeRecurringPaymentServiceServerMockRecorder
}

// MockUnsafeRecurringPaymentServiceServerMockRecorder is the mock recorder for MockUnsafeRecurringPaymentServiceServer.
type MockUnsafeRecurringPaymentServiceServerMockRecorder struct {
	mock *MockUnsafeRecurringPaymentServiceServer
}

// NewMockUnsafeRecurringPaymentServiceServer creates a new mock instance.
func NewMockUnsafeRecurringPaymentServiceServer(ctrl *gomock.Controller) *MockUnsafeRecurringPaymentServiceServer {
	mock := &MockUnsafeRecurringPaymentServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeRecurringPaymentServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeRecurringPaymentServiceServer) EXPECT() *MockUnsafeRecurringPaymentServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedRecurringPaymentServiceServer mocks base method.
func (m *MockUnsafeRecurringPaymentServiceServer) mustEmbedUnimplementedRecurringPaymentServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedRecurringPaymentServiceServer")
}

// mustEmbedUnimplementedRecurringPaymentServiceServer indicates an expected call of mustEmbedUnimplementedRecurringPaymentServiceServer.
func (mr *MockUnsafeRecurringPaymentServiceServerMockRecorder) mustEmbedUnimplementedRecurringPaymentServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedRecurringPaymentServiceServer", reflect.TypeOf((*MockUnsafeRecurringPaymentServiceServer)(nil).mustEmbedUnimplementedRecurringPaymentServiceServer))
}
