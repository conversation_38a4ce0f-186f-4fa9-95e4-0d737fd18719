package networth

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/be-common/pkg/colors"

	networthEnumsPb "github.com/epifi/gamma/api/frontend/insights/networth/enums"
	typesUiPb "github.com/epifi/gamma/api/typesv2/ui"
)

func NewNetWorthManualFormInputComponent(fieldName string, inputStyle networthEnumsPb.NetworthManualFormInputStyle) *NetWorthManualFormInputComponent {
	component := &NetWorthManualFormInputComponent{IsMandatory: true}
	component.WithInputStyle(inputStyle)
	component.WithFieldName(fieldName)
	return component
}

func (n *NetWorthManualFormInputComponent) WithDefaultTitle(text string) {
	n.Title = typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(text, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS))
}

func (n *NetWorthManualFormInputComponent) WithMandatoryDisplayTitle(text string) {
	n.Title = &typesUiPb.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(text, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS),
			commontypes.GetTextFromStringFontColourFontStyle("*", colors.ColorErrorRed, commontypes.FontStyle_OVERLINE_XS_CAPS),
		},
	}
}

func (n *NetWorthManualFormInputComponent) WithDefaultPlaceholderText(text string) {
	n.PlaceholderText = typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(text, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_HEADLINE_L))
}

func (n *NetWorthManualFormInputComponent) WithMandatoryPlaceholderText(text string) {
	n.PlaceholderText = &typesUiPb.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(text, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_HEADLINE_L),
			commontypes.GetTextFromStringFontColourFontStyle("*", colors.ColorErrorRed, commontypes.FontStyle_HEADLINE_L),
		},
	}
}

func (n *NetWorthManualFormInputComponent) WithInputStyle(inputStyle networthEnumsPb.NetworthManualFormInputStyle) {
	n.InputStyle = inputStyle
}

func (n *NetWorthManualFormInputComponent) WithMandatoryInput() {
	n.IsMandatory = true
}

func (n *NetWorthManualFormInputComponent) WithInputData(inputData *NetWorthManualInputData) {
	n.InputData = inputData
}

func (n *NetWorthManualFormInputComponent) WithFieldName(name string) {
	if n.InputData == nil {
		n.InputData = &NetWorthManualInputData{}
	}
	n.InputData.FieldName = name
}

func (n *NetWorthManualFormInputComponent) MakeOptional() {
	n.IsMandatory = false
}

func NewNetWorthManualForm(headerText string, buttonText string) *NetWorthManualForm {
	return &NetWorthManualForm{
		Header:    typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(headerText, colors.ColorOnLightHighEmphasis, commontypes.FontStyle_HEADLINE_L).WithAlignment(commontypes.Text_ALIGNMENT_CENTER)),
		SubmitCta: typesUiPb.NewITC().WithTexts(commontypes.GetPlainStringText(buttonText).WithFontStyle(commontypes.FontStyle_BUTTON_M)),
	}
}

func (nm *NetWorthManualForm) WithActionCta(actionCta ...*AssetActionButton) *NetWorthManualForm {
	actionCtaTemp := make([]*AssetActionButton, 0)
	actionCtaTemp = append(actionCtaTemp, actionCta...)
	if nm == nil {
		return &NetWorthManualForm{
			ActionButtons: actionCtaTemp,
		}
	}
	nm.ActionButtons = actionCtaTemp
	return nm
}

func NewAssetActionButton() *AssetActionButton {
	return &AssetActionButton{}
}

func (nm *AssetActionButton) WithActionType(actionActionType AssetActionType) *AssetActionButton {
	if nm == nil {
		return &AssetActionButton{
			ActionType: actionActionType,
		}
	}
	nm.ActionType = actionActionType
	return nm
}

func (nm *AssetActionButton) WithDisplayText(displayText *typesUiPb.IconTextComponent) *AssetActionButton {
	if nm == nil {
		return &AssetActionButton{
			DisplayText: displayText,
		}
	}
	nm.DisplayText = displayText
	return nm
}

func (nm *NetWorthManualForm) WithComponentsSection(section *NetWorthManualFormComponentsSection) *NetWorthManualForm {
	nm.ComponentsSections = append(nm.ComponentsSections, section)
	return nm
}

func NewNetWorthManualFormComponentsSection(sectionName string) *NetWorthManualFormComponentsSection {
	return &NetWorthManualFormComponentsSection{
		Header: typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(sectionName, colors.ColorOnLightHighEmphasis, commontypes.FontStyle_HEADLINE_L)),
	}
}

func (ncs *NetWorthManualFormComponentsSection) WithInputComponent(component *NetWorthManualFormInputComponent) *NetWorthManualFormComponentsSection {
	ncs.InputComponents = append(ncs.InputComponents, component)
	return ncs
}

func (f *MultiInputOptions) GetSelectedOptions() []*MultiInputOption {
	var selectedOptions []*MultiInputOption
	if f == nil {
		return nil
	}
	for _, option := range f.Options {
		if option.GetIsSelected() {
			selectedOptions = append(selectedOptions, option)
		}
	}
	return selectedOptions
}

func (nm *NetWorthManualForm) GetAllInputComponents() []*NetWorthManualFormInputComponent {
	var inputComponents []*NetWorthManualFormInputComponent
	for _, section := range nm.GetComponentsSections() {
		for _, inputComponent := range section.GetInputComponents() {
			inputComponents = append(inputComponents, inputComponent)
		}
	}
	return inputComponents
}

func (nm *NetWorthManualForm) GetAllInputData() []*NetWorthManualInputData {
	inputData := make([]*NetWorthManualInputData, 0)
	for _, section := range nm.GetComponentsSections() {
		for _, inputComponent := range section.GetInputComponents() {
			inputData = append(inputData, inputComponent.GetInputData())
		}
	}
	return inputData
}

func (in *NetWorthManualInputData) GetInputValueFromSingleOption() *InputOptionValue {
	if in.GetSingleOption().GetInputOptionData().GetInputValue() != nil {
		return in.GetSingleOption().GetInputOptionData().GetInputValue()
	}
	return in.GetSingleOption().GetInputValue()
}
