// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/frontend/config/service.proto

package config

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Config_FetchConfig_FullMethodName = "/frontend.config.Config/FetchConfig"
)

// ConfigClient is the client API for Config service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConfigClient interface {
	// RPC to fetch config for various flows via backend at the time of app start itself.
	// This is particularly useful for cases where we do not want to depend on firebase config
	// to toggle feature flags on/off. Any new config requirements should be done via this.
	// Since this API is called at time of app start auth and device options are set to false
	FetchConfig(ctx context.Context, in *FetchConfigRequest, opts ...grpc.CallOption) (*FetchConfigResponse, error)
}

type configClient struct {
	cc grpc.ClientConnInterface
}

func NewConfigClient(cc grpc.ClientConnInterface) ConfigClient {
	return &configClient{cc}
}

func (c *configClient) FetchConfig(ctx context.Context, in *FetchConfigRequest, opts ...grpc.CallOption) (*FetchConfigResponse, error) {
	out := new(FetchConfigResponse)
	err := c.cc.Invoke(ctx, Config_FetchConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConfigServer is the server API for Config service.
// All implementations should embed UnimplementedConfigServer
// for forward compatibility
type ConfigServer interface {
	// RPC to fetch config for various flows via backend at the time of app start itself.
	// This is particularly useful for cases where we do not want to depend on firebase config
	// to toggle feature flags on/off. Any new config requirements should be done via this.
	// Since this API is called at time of app start auth and device options are set to false
	FetchConfig(context.Context, *FetchConfigRequest) (*FetchConfigResponse, error)
}

// UnimplementedConfigServer should be embedded to have forward compatible implementations.
type UnimplementedConfigServer struct {
}

func (UnimplementedConfigServer) FetchConfig(context.Context, *FetchConfigRequest) (*FetchConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchConfig not implemented")
}

// UnsafeConfigServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConfigServer will
// result in compilation errors.
type UnsafeConfigServer interface {
	mustEmbedUnimplementedConfigServer()
}

func RegisterConfigServer(s grpc.ServiceRegistrar, srv ConfigServer) {
	s.RegisterService(&Config_ServiceDesc, srv)
}

func _Config_FetchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServer).FetchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Config_FetchConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServer).FetchConfig(ctx, req.(*FetchConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Config_ServiceDesc is the grpc.ServiceDesc for Config service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Config_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "frontend.config.Config",
	HandlerType: (*ConfigServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FetchConfig",
			Handler:    _Config_FetchConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/frontend/config/service.proto",
}
