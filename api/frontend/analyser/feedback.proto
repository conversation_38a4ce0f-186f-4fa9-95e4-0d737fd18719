syntax = "proto3";
package frontend.analyser;

import "api/frontend/inapphelp/app_feedback/enums.proto";

option go_package = "github.com/epifi/gamma/api/frontend/analyser";
option java_package = "com.github.epifi.gamma.api.frontend.analyser";

message Feedback {
  ExitFeedback exit_feedback = 1;
  repeated OnScreenFeedback on_screen_feedback = 2;
}

message OnScreenFeedback {
  // This needs to be used by the client as it is to fetch questions and submit the feedback.
  FeedbackParams feedback_params = 1;
  // The feedback needs to be linked to the lens of the given index
  string lens_name = 2;
  // The feedback needs to be linked to the bottom of visual_component of the given index of the selected lens
  int32 visual_component_index = 3;
}

message ExitFeedback {
  // This needs to be used by the client as it is to fetch questions and submit the feedback.
  FeedbackParams feedback_params = 1;
}

message FeedbackParams {
  // Reference id is a unique key which is used to link the feedback to a given feature.
  string reference_id = 1;
  // This is used to fetch the question details linked to the given flow.
  inapphelp.app_feedback.FeedbackSurveyFlow feedback_survey_flow = 2;
  // Stores extra details related to the feedback.
  map<string, string> screen_meta = 3;
  // Request id is a unique identifier for a feedback. It will be used to fetch questions and submit answer to a particular entry in DB.
  string request_id = 4;
}
