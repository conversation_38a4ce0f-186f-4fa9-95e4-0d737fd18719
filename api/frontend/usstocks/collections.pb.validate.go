// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/usstocks/collections.proto

package usstocks

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CollectionListSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CollectionListSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectionListSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CollectionListSectionMultiError, or nil if none found.
func (m *CollectionListSection) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectionListSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCollections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CollectionListSectionValidationError{
						field:  fmt.Sprintf("Collections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CollectionListSectionValidationError{
						field:  fmt.Sprintf("Collections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CollectionListSectionValidationError{
					field:  fmt.Sprintf("Collections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CollectionListSectionMultiError(errors)
	}

	return nil
}

// CollectionListSectionMultiError is an error wrapping multiple validation
// errors returned by CollectionListSection.ValidateAll() if the designated
// constraints aren't met.
type CollectionListSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectionListSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectionListSectionMultiError) AllErrors() []error { return m }

// CollectionListSectionValidationError is the validation error returned by
// CollectionListSection.Validate if the designated constraints aren't met.
type CollectionListSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectionListSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectionListSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectionListSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectionListSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectionListSectionValidationError) ErrorName() string {
	return "CollectionListSectionValidationError"
}

// Error satisfies the builtin error interface
func (e CollectionListSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectionListSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectionListSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectionListSectionValidationError{}

// Validate checks the field values on CollectionListItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CollectionListItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectionListItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CollectionListItemMultiError, or nil if none found.
func (m *CollectionListItem) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectionListItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CollectionId

	// no validation rules for Title

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectionListItemValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectionListItemValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectionListItemValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectionListItemValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectionListItemValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectionListItemValidationError{
				field:  "Tag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CollectionListItemMultiError(errors)
	}

	return nil
}

// CollectionListItemMultiError is an error wrapping multiple validation errors
// returned by CollectionListItem.ValidateAll() if the designated constraints
// aren't met.
type CollectionListItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectionListItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectionListItemMultiError) AllErrors() []error { return m }

// CollectionListItemValidationError is the validation error returned by
// CollectionListItem.Validate if the designated constraints aren't met.
type CollectionListItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectionListItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectionListItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectionListItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectionListItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectionListItemValidationError) ErrorName() string {
	return "CollectionListItemValidationError"
}

// Error satisfies the builtin error interface
func (e CollectionListItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectionListItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectionListItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectionListItemValidationError{}

// Validate checks the field values on CollectionDetailsSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CollectionDetailsSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectionDetailsSection with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CollectionDetailsSectionMultiError, or nil if none found.
func (m *CollectionDetailsSection) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectionDetailsSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectionDetailsSectionValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectionDetailsSectionValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectionDetailsSectionValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSortSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectionDetailsSectionValidationError{
					field:  "SortSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectionDetailsSectionValidationError{
					field:  "SortSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSortSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectionDetailsSectionValidationError{
				field:  "SortSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetStocks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CollectionDetailsSectionValidationError{
						field:  fmt.Sprintf("Stocks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CollectionDetailsSectionValidationError{
						field:  fmt.Sprintf("Stocks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CollectionDetailsSectionValidationError{
					field:  fmt.Sprintf("Stocks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBottomDisclaimerText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectionDetailsSectionValidationError{
					field:  "BottomDisclaimerText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectionDetailsSectionValidationError{
					field:  "BottomDisclaimerText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomDisclaimerText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectionDetailsSectionValidationError{
				field:  "BottomDisclaimerText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CollectionDetailsSectionMultiError(errors)
	}

	return nil
}

// CollectionDetailsSectionMultiError is an error wrapping multiple validation
// errors returned by CollectionDetailsSection.ValidateAll() if the designated
// constraints aren't met.
type CollectionDetailsSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectionDetailsSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectionDetailsSectionMultiError) AllErrors() []error { return m }

// CollectionDetailsSectionValidationError is the validation error returned by
// CollectionDetailsSection.Validate if the designated constraints aren't met.
type CollectionDetailsSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectionDetailsSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectionDetailsSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectionDetailsSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectionDetailsSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectionDetailsSectionValidationError) ErrorName() string {
	return "CollectionDetailsSectionValidationError"
}

// Error satisfies the builtin error interface
func (e CollectionDetailsSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectionDetailsSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectionDetailsSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectionDetailsSectionValidationError{}
