syntax = "proto3";

package frontend.home;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/auth.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/home/<USER>";
import "api/rpc/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/account/enums.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/date.proto";
import "api/typesv2/home/<USER>";
import "api/typesv2/money.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/components/spacer.proto";
import "api/typesv2/ui/sdui/sections/section.proto";
import "api/typesv2/ui/tab_widget.proto";
import "api/typesv2/ui/walkthrough/walkthrough.proto";
import "api/typesv2/ui/widget_themes.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/frontend/home";
option java_package = "com.github.epifi.gamma.api.frontend.home";

// Home contains APIs for application homepage.
service Home {

  // API to get account balance summary such as Current balance, spent etc.
  // https://www.figma.com/file/88IX3angDC0bM5SpEqY4IZ/Home-x-Build-01?node-id=1%3A1080
  rpc AccountBalanceSummary (AccountBalanceSummaryRequest) returns (AccountBalanceSummaryResponse) {
    // Deprecating in favour of GetSavingsBalanceSummary and GetDepositBalanceSummary
    option deprecated = true;
  };
  // API to get the list of recent transactions done by user. These can be one of the following types
  // 1. Send money to someone
  // 2. Receive money from someone
  // 3. Claiming reward
  // 4. Depositing money in account- smart deposit etc.
  rpc RecentUserActivities (RecentUserActivitiesRequest) returns (RecentUserActivitiesResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetSavingsBalanceSummary returns user's savings account balance summary like current balance, spent, etc.
  rpc GetSavingsBalanceSummary (GetSavingsBalanceSummaryRequest) returns (GetSavingsBalanceSummaryResponse);

  // GetDepositBalanceSummary returns account balance summaries for a user's deposit accounts like fixed deposits, smart deposits etc.
  rpc GetDepositBalanceSummary (GetDepositBalanceSummaryRequest) returns (GetDepositBalanceSummaryResponse);

  // Api to get layout for home page
  rpc HomeLayout (HomeLayoutRequest) returns (HomeLayoutResponse) {
    // Deprecating in favour of GetHomeLayoutAndWalkthrough
    option deprecated = true;
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Api to get layout and walkthrough for home page
  // Api will get the latest layout for the user and construct a walkthrough using the previous and current layout
  // Layout will consist of a top section, a middle section and a bottom section each having some elements
  // Walkthrough will consist of a series of steps each tied to an element in the layout, to announce any changes to user
  rpc GetHomeLayoutAndWalkthrough (GetHomeLayoutAndWalkthroughRequest) returns (GetHomeLayoutAndWalkthroughResponse) {
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetAppBottomNavBar rpc will returns the bottom navigation bar icons with ordering and deeplink
  rpc GetAppBottomNavBar (GetAppBottomNavBarRequest) returns (GetAppBottomNavBarResponse) {
    // Deprecating in favour of GetHomeLayoutAndWalkthrough
    option deprecated = true;
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetFiAccountSummary rpc returns all the required details in Fi Savings account summary screen
  // Figma: https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/FFF-Home-V2.0-(WIP)?node-id=824%3A17166
  rpc GetFiAccountSummary (GetFiAccountSummaryRequest) returns (GetFiAccountSummaryResponse);

  // GetFiAccountDashboard rpc returns all the required details in Fi Account dashboard
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=1630%3A35980
  rpc GetFiAccountDashboard (GetFiAccountDashboardRequest) returns (GetFiAccountDashboardResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetUpcomingUserActivities fetches the list of upcoming subscriptions of user. These can be one of the following types
  // 1. upcoming auto invests
  // 2. upcoming auto save to sd's
  // 3. upcoming auto pays
  rpc GetUpcomingUserActivities (GetUpcomingUserActivitiesRequest) returns (GetUpcomingUserActivitiesResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetHomeExplore - returns all home explore contents
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=4155%3A47834&t=fZKf28NCJ0nx5dCb-1
  rpc GetHomeExplore (GetHomeExploreRequest) returns (GetHomeExploreResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  /* GetHomeAppWalkThroughFlows returns app walk though flows that is valid for the user.
   Depending on whether the user is old user upgraded to new app, or if the user is onboarding on new app with
   home v2 directly, we will show different onboarding walk through flows.
   If the user had non-zero balance, we will also show additional walk through of hide balance flow
   */
  rpc GetHomeAppWalkThroughFlows (GetHomeAppWalkThroughFlowsRequest) returns (GetHomeAppWalkThroughFlowsResponse) {
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  /*
    GetHomeProfileInfo returns the nudge and badge shown alongside user's profile image on home page
    Figma: https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=2082%3A50547&t=gPv4Zd9gOiwl2uOr-1
   */
  rpc GetHomeProfileInfo (GetHomeProfileInfoRequest) returns (GetHomeProfileInfoResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  /*
    GetHomeShortcuts returns the list of shortcuts persisted for a user on home page.
   */
  rpc GetHomeShortcuts (GetHomeShortcutsRequest) returns (GetHomeShortcutsResponse) {
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  /*
    GetHomeShortcutOptions returns the list of shortcuts persisted for a user on home page along with list of possible shortcuts to choose from
    Client will allow the user to choose from the possible shortcuts and call SetHomeShortcuts to persist the chosen shortcuts for further display
   */
  rpc GetHomeShortcutOptions (GetHomeShortcutOptionsRequest) returns (GetHomeShortcutOptionsResponse) {
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  /*
    SetHomeShortcuts stores the list of shortcuts chosen by the user on shortcut options page
   */
  rpc SetHomeShortcuts (SetHomeShortcutsRequest) returns (SetHomeShortcutsResponse) {
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  /**
    GetFocussedDashboardInfo returns the info about dashboard card to focus with its metadata about warning
   */
  rpc GetFocussedDashboardInfo (GetFocussedDashboardInfoRequest) returns (GetFocussedDashboardInfoResponse) {
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  /**
    GetNavigationBarHighlights returns navigation bar highlights to draw user's attention to the bar for a new feature launch, etc
   */
  rpc GetNavigationBarHighlights (GetNavigationBarHighlightsRequest) returns (GetNavigationBarHighlightsResponse) {
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  /**
    GetTrustMarker returns the SDUI trust marker widget,
    which usually appears at the bottom of the home screen to increase users' trust.
    The widget includes various metrics such as total users, partner icons, and a button to display stories.
   */
  rpc GetTrustMarker (GetTrustMarkerRequest) returns (GetTrustMarkerResponse) {
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  /**
    GetUpiWidget returns the SDUI Upi widget in home for  D2H Users,
    to connect their UPI accounts in Fi. This Widget includes a Deeplink to start the flow
    of connecting users UPI accounts.
    Figma -> https://www.figma.com/design/ai7gHuFQud7VOsPN0z8wh5/D2H-%E2%80%A2-FFF?node-id=4076-18358&t=DMD4xbhtxu3RXMmt-4
   */
  rpc GetUpiWidget (GetUpiWidgetRequest) returns (GetUpiWidgetResponse) {
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  /**
    GetAlternateAppIcon returns the alternate app icon identifier for user.
    This is used to change the Fi App icon on the user's device based on -
    1. User's savings account tier - Plus, Infinite, Salary, Prime, etc.
    2. Festive season - Holi, Diwali, Christmas, etc.
    3. Combination of any of the above parameters.

    Client will call this API on app launch in a non-blocking manner
    It will use this id to choose the appropriate icon stored in the app resources.
    It will fallback to the default app icon if unspecified id is returned.
  */
  rpc GetAlternateAppIcon (GetAlternateAppIconRequest) returns (GetAlternateAppIconResponse) {
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}

// AccountBalanceSummaryRequest message for AccountBalanceSummary method.
message AccountBalanceSummaryRequest {
  option deprecated = true;
  // Common auth header used for all frontend RPCs.
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
}

// AccountBalanceSummaryResponse message for AccountBalanceSummary method.
message AccountBalanceSummaryResponse {
  option deprecated = true;
  // array of account summaries along with time range
  repeated AccountBalanceSummaryPerTimeRange account_summary_per_time_range = 1;
  // rpc status
  rpc.Status status = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

// If there is any CTA (call to action) button associated with the account summary
// Account Summary can have actions associated with it in the form of buttons like "Add Funds"
// Each of these button will have a deeplink with the information which tells the client where to go
message AccountSummaryCTA {
  string text = 1;
  string icon_url = 2;
  deeplink.Deeplink deeplink = 3;
}

// Account summary per time range comprises of account summary, and detailed summary
// 1. Account summary - contains overall current balance, credit amount, spent amount etc.
// 2. The detailed summary - contains categories of money - opening balance, saved, spent, money in
// Each of the categories again contain sub categories
// Subcategories for spent can be - bills, food, etc.
message AccountBalanceSummaryPerTimeRange {
  // The balance available in the account for transacting
  // In a normal scenario, account balance and current balance are the same.
  //
  // But, sometimes banks tend to put certain chunk of money from an account
  // on hold and is not available for a normal transaction.
  // This can be a result of user initiated mandate or for any
  // upcoming stand instructions.
  api.typesv2.Money current_balance = 1;
  // The balance available in an account as per the account ledger.
  // In a normal scenario, account balance and current balance are the same.
  //
  // But, sometimes banks tend to put certain chunk of money from an account
  // on hold and is not available for a normal transaction.
  // This can be a result of user initiated mandate or for any
  // upcoming stand instructions.
  //
  // The field is populated only in case current balance and ledger
  // balance differs.
  api.typesv2.Money ledger_balance = 12;
  // Boolean flag representing if computed balance at epiFi is stale.
  //
  // The flag is typically set when there are missing transactions in the
  // system and hence, discrepancy in aggregates is expected.
  bool is_stale_balance = 13;
  // arc threshold amount to be shown on account summary
  api.typesv2.Money arc_threshold_amount = 2;
  // money in of the user
  api.typesv2.Money credit_amount = 3;
  // money spent by the user
  api.typesv2.Money spent_amount = 4;
  // money saved by the user
  api.typesv2.Money saved_amount = 5;
  // time range filter for which aggregates are shown.
  // Assuming time range allowed for view to be minimal - 1 month, 6 months, 9 months
  // Value is the string that will displayed for time range choice in drop down for user
  string time_range_filter = 6;
  // array of sections/ categories in the account summary. (eg. category can spent, saved, etc)
  repeated AccountBalanceSummarySection detailed_summary = 7;
  // Smart deposit amount
  api.typesv2.Money smart_deposit = 8;
  // Fixed deposit amount
  api.typesv2.Money fixed_deposit = 9;
  // provident fund
  api.typesv2.Money provident_fund = 10;
  // total deposit
  api.typesv2.Money total_deposit = 11;
  // there can be a list of action buttons for an account
  repeated AccountSummaryCTA accountSummaryCTA = 14;
}

// Account summary comprises of
message AccountBalanceSummarySection {
  // title name of the category
  string title = 1;
  // money value spent in the current category
  api.typesv2.Money value = 2;
  // percentage increase or decrease of current category w.r.t previous month
  float percent_change = 3;
  // colour label for current category
  string label_colour = 4;
  // icon to be shown to represent the category
  api.typesv2.common.Image icon = 5;
  // array of sub section/ or sub categories (eg. under spent category we can have bills, food as subcategories) in account summary
  repeated AccountBalanceSummarySection sections = 6;
}

// Request of RecentUserTransactions rpc
message RecentUserActivitiesRequest {
  // Common auth header used for all frontend RPCs.
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
}

// Response to RecentUserTransactions rpc
message RecentUserActivitiesResponse {
  rpc.Status status = 1;
  // List of transaction response. Responses will be sorted in descending of the execution timestamp
  repeated UserActivityResponse responses = 2;
  frontend.header.ResponseHeader resp_header = 15;
  // Zero state params for Recent user activities section
  RecentUserActivitiesZeroStateParams zero_state_params = 3;
  // VPA of actor to show it on home screen
  // we will show it below recent user activity section
  string vpa = 4;
  // We need to show UPI ID & UPI Number
  // https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=31038-65798&t=A6WTvarDzoyUdmdW-0
  UpiInfoCard upi_info_card = 5;
  // cta to show all recent activities
  api.typesv2.ui.IconTextComponent see_all_activities = 6;
}

// Represents a card to display UPI information, such as UPI ID and UPI Number.
// figma - https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=31038-65798&t=A6WTvarDzoyUdmdW-0
message UpiInfoCard {
  // A list of UPI information elements, such as UPI ID and UPI Number (with/without privacy toggle).
  repeated UpiInfo upi_info = 1;
  // Container styling properties
  api.typesv2.ui.IconTextComponent.ContainerProperties container_properties = 2;
}

// Represents detailed UPI information, such as UPI ID or UPI Number, with copy-able text and optional privacy settings.
message UpiInfo {
  // Label text (left text to display)  (e.g., "UPI ID", "UPI Number")
  api.typesv2.common.Text left_text = 1;
  // The actual value which we will show and also is copy-able
  api.typesv2.common.Text copy_text = 2;
  // Copy action icon
  api.typesv2.common.VisualElement copy_visual_element = 3;
  // Indicates whether a toggle for hiding/showing the value should be displayed.
  // If set to true, the client must display a privacy toggle icon (e.g., for UPI numbers).
  // Note: The client is responsible for saving the toggle state, ensuring it remains consistent when the user returns in subsequent sessions.
  bool privacy_icon = 4;
}

message RecentUserActivitiesZeroStateParams {
  // Number of slots to show on top of recent activities
  int32 additional_zero_slots = 1;
  // Empty state icons
  repeated api.typesv2.common.Image image = 2;
  // Empty state string
  string zero_state_msg = 3;
}

// Details to represent single activity on recent activity list
message UserActivityResponse {
  // name of entity - (user/ account/ reward) with which the transaction happened
  string title = 1;
  // transaction amount. It can be null for activities where the money is not involved
  api.typesv2.Money amount = 2;
  // image of icon
  api.typesv2.common.Image icon = 3;
  // Deeplink to which the user is taken on click
  deeplink.Deeplink link = 4;
  // default colour code if profile not present
  string image_colour_code = 5;
  // colour code will be for credit/debit transactions to other users.
  // If empty, then black can be used
  string title_colour_code = 6;
  frontend.header.ResponseHeader resp_header = 15;
  // icon that denotes the transaction type
  api.typesv2.common.Image txn_type = 7;
  // time for activity
  google.protobuf.Timestamp activity_time = 8;
  // border color for each activity card
  // figma -> https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=28805-14845&t=uSX4pft4XuwNclIQ-4
  api.typesv2.common.ui.widget.BackgroundColour border_color = 9;
}

message GetSavingsBalanceSummaryRequest {
  frontend.header.RequestHeader req = 1;
}

message GetSavingsBalanceSummaryResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // The balance available in the account for transacting
  // In a normal scenario, account ledger balance and current balance are the same.
  //
  // But, sometimes banks tend to put certain chunk of money from an account
  // on hold and it is not available for a normal transaction.
  // This can be a result of user initiated mandate or for any
  // upcoming stand instructions.
  api.typesv2.Money current_balance = 2;

  // The balance available in an account as per the account ledger.
  // In a normal scenario, account ledger balance and current balance are the same.
  //
  // But, sometimes banks tend to put certain chunk of money from an account
  // on hold and it is not available for a normal transaction.
  // This can be a result of user initiated mandate or for any
  // upcoming stand instructions.
  //
  // The field is populated only in case current balance and ledger
  // balance differs.
  api.typesv2.Money ledger_balance = 3 [deprecated = true];

  // money in of the user
  api.typesv2.Money credit_amount = 4;

  // money spent by the user
  api.typesv2.Money spent_amount = 5;

  // money saved by the user
  api.typesv2.Money saved_amount = 6;

  // time range for which aggregates are shown
  string time_range = 7;

  // array of sections/ categories in the account summary. (eg. category can spent, saved, etc)
  repeated AccountBalanceSummarySection detailed_summary = 8;

  // there can be a list of action buttons for an account
  repeated AccountSummaryCTA account_summary_cta = 9;

  // reason for ledger balance mismatch to be surfaced to the user
  // e.g. ledger balance mismatch can be due to quarter end or month end activity at bank's end
  string ledger_bal_mismatch_reason = 10 [deprecated = true];

  // additional info regarding user balance that needs to be surfaced to the user
  // e.g. in case of stale balance we might want to surface some context telling user that balance is stale
  // or in case of txns are missing in system we can show that some transactions are missing due to which
  // summary might be off
  string additional_balance_info = 11 [deprecated = true];

  // timestamp for which balance is returned by the server
  // In case vendor api call succeeds, the timestamp will correspond to the time vendor returned the response
  // In case of computed balance, the timestamp will correspond to the time balance was computed in backend
  // In case last known balance, the timestamp will correspond to the time last known balance was returned by vendor
  // this can be used by client to surface at what time last balance was fetched from the server.
  google.protobuf.Timestamp balance_at = 12;

  // For backward compatibility
  // Boolean flag representing if computed balance at epiFi is stale.
  //
  // The flag is typically set when there are missing transactions in the
  // system and hence, discrepancy in aggregates is expected.
  bool is_stale_balance = 13 [deprecated = true];

  // Optional: tool tip contains additional information to be surfaced to
  // the user regarding balance staleness, etc.
  // Deeplink here can be used to redirect the user to a more informative screen.
  // If not present, we shall hide the 'i' next to current balance on the home screen
  // Example: This tooltip deeplink will be used to navigate when clicked on the 'i' icon near current balance
  // as seen here- https://www.figma.com/file/FUObqwCZu7ShU7fhPH7QgA/FFF-%2F-Home-%26-Summary-%2F-Feb-2022?node-id=296%3A3379
  frontend.deeplink.Deeplink tool_tip = 14;

  // Optional: contains additional information with respect to account balance summary
  // as an example user's balance summary is expected to be out of sync with actual balance
  // under certain scenarios. The text here helps to surface information regarding summary
  // under such scenarios as seen here:
  // https://www.figma.com/file/FUObqwCZu7ShU7fhPH7QgA/FFF-%2F-Home-%26-Summary-%2F-Feb-2022?node-id=296%3A3379
  string balance_summary_info = 15;

  message AccountDiscrepancyInformation {
    // user friendly description explaining root cause for the discrepancy
    string description = 1;
    // Optional: deeplink to redirect the user to a more detailed page
    // in case user needs more information
    // If not present then we should make the description text non-clickable
    frontend.deeplink.Deeplink deeplink = 2;
  }

  // Optional: usually populated in case user has some expected balance discrepancy into their account
  // it can be due to many reasons but not limited to following:
  // ledger balance mis-match
  // due to lien being marked on user's account
  AccountDiscrepancyInformation account_discrepancy_info = 16;
}

message GetDepositBalanceSummaryRequest {
  frontend.header.RequestHeader req = 1;
}

message GetDepositBalanceSummaryResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // Smart deposit amount
  api.typesv2.Money smart_deposit = 2;

  // Fixed deposit amount
  api.typesv2.Money fixed_deposit = 3;

  // provident fund
  api.typesv2.Money provident_fund = 4;

  // total deposit
  api.typesv2.Money total_deposit = 5;

  // time range filter for which aggregates are shown.
  string time_range = 6;

  // array of sections/ categories in the account summary. (eg. category can spent, saved, etc)
  repeated AccountBalanceSummarySection detailed_summary = 7;

  // there can be a list of action buttons for an account
  repeated AccountSummaryCTA account_summary_cta = 8;

  // timestamp for which balance is returned
  // In case vendor api call succeeds, the timestamp will correspond to the time vendor returned the response
  // In case of computed balance, the timestamp will correspond to the time balance was computed in backend
  // In case last known balance, the timestamp will correspond to the time last known balance was returned by vendor
  // this can be used by client to surface at what time last balance was fetched from the server.
  google.protobuf.Timestamp balance_at = 9;
}

message HomeLayoutRequest {
  frontend.header.RequestHeader req = 1;
}

message HomeLayoutResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated HomeWidget home_widgets_top_section = 2;
  repeated HomeWidget home_widgets_bottom_section = 3;
  // Layout id associated with the response. Layout configurations can differ for users
  string layout_id = 4;
}

message GetHomeLayoutAndWalkthroughRequest {
  frontend.header.RequestHeader req = 1;
}

message GetHomeLayoutAndWalkthroughResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // layout will consist of all the necessary sections required to render the home layout structure
  HomeLayoutV2 layout = 2;
  // walkthrough will consist of series of steps required to introduce any changes in the layout
  api.typesv2.ui.walkthrough.Walkthrough walkthrough = 3;
  // Gradient background image for the top section, spanning the top navigation, dashboard,
  // and critical notifications.
  // https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=28047-37963&t=rfZucXsziBfJBkbS-0
  api.typesv2.common.VisualElement top_section_background = 12;
  // top section toolbar Background color. used in New home UI when toolbar gets scroll up.
  api.typesv2.ui.BackgroundColour toolbar_section_background = 4;
}

message HomeLayoutV2 {
  // Layout id associated with the response. Layout configurations can differ for users
  string id = 1;
  // top section will consist of persistent components on the top of home screen
  repeated HomeWidget home_widgets_top_section = 2;
  // middle section will consist of scrollable list of components in the middle of home screen
  repeated HomeWidget home_widgets_middle_section = 3;
  // bottom section will consist of persistent components on the bottom of home screen
  HomeWidget home_widgets_bottom_section = 4;
}

message GetAppBottomNavBarRequest {
  frontend.header.RequestHeader req = 1;
}

message GetAppBottomNavBarResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // List of Icons in bottom navigation bar
  repeated Icon bottom_bar_icons = 3;
  // Sticky_icon_mapping.
  // Key in the mapping will be Icon 'Id' of bottom bar icons
  map<string, Icon> sticky_icon_mapping = 4;
  // Icon type that would be selected
  api.typesv2.home.IconType default_selected_icon_type = 5;
  // No of times user will be shown scan and pay
  int32 occurrences_of_scan_pay_text = 6;
}

message GetFiAccountSummaryRequest {
  frontend.header.RequestHeader req = 1;
  // [Optional] Enum value sent in client requests to trigger a force balance update from Pay backend. This will be nil
  // for normal calls when the page loads for instance
  ForceBalanceUpdate force_balance_update = 2;
  // Request param to control which account (Regular, NRE or NRO) summary to return
  // To maintain backward compatibility, the rpc returns any savings account (belonging to any APO) in case when Account Product Offering is Unspecified
  api.typesv2.account.AccountProductOffering account_product_offering = 3;
  // Request param to control which Partner Bank account to return
  // To maintain backward compatibility, the rpc returns savings account of any partner bank present in DB for user (i.e Federal since it is the only partner bank for savings account currently) in case when partner bank is Unspecified
  vendorgateway.Vendor partner_bank = 4;
}

// Response for the Accounts summary page
//  https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/FFF-Home-V2.0-(WIP)?node-id=824%3A17166
message GetFiAccountSummaryResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // icon representing the bank account. For Fi account, it would be Fi + Federal icon
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/FFF-Home-V2.0-(WIP)?node-id=824%3A17166
  api.typesv2.common.Image account_icon = 2 [deprecated = true];
  // Overall balance of Fi Account
  api.typesv2.Money balance = 4 [deprecated = true];
  // Title to be shown below Bank icon
  api.typesv2.common.Text title = 3 [deprecated = true];
  // All Fi account related information such as Interest rates, account no., IFSC, UPI ID
  repeated FiAccountInfo account_info = 5 [deprecated = true];
  // Details regarding actions on summary page - Add money, Get statement, Account settings, Share account details
  repeated AccountAction account_actions = 6 [deprecated = true];
  // Body of the summary page which has opening balance, spends, invested, money in of the month
  FiAccountSummary account_summary = 7 [deprecated = true];
  // Additional account details such as account type, and interest rates
  AdditionalAccountInfo additional_account_info = 8 [deprecated = true];
  // Last synced data
  string last_synced_msg = 9 [deprecated = true];
  // banking partner details will be shown
  message PartnerBank {
    // Title message
    string title = 1;
    // Bank image
    api.typesv2.common.Image bank_name = 2;
  }
  PartnerBank partner_bank = 10 [deprecated = true];
  // icon will provide warning icon if error in account balance
  Icon bal_icon = 11 [deprecated = true];
  // A CTA action to force refresh balance in  the Fi account summary screen:
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21134-3920&mode=design&t=AGTCHpFp8mrLd1Ex-4
  AccountAction refresh_cta = 12 [deprecated = true];
  // Text to be shown below the Main balance screen in cases like stale/incorrect balance, usually when we surface the
  // force refresh CTA as well:
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21134-3920&mode=design&t=AGTCHpFp8mrLd1Ex-4
  api.typesv2.common.Text last_synced_msg_force_refresh = 13 [deprecated = true];

  // Summary data for each of the tab
  message FiAccountSummaryTabData {
    // Unique id for account summary
    string id = 1;
    // icon representing the bank account. For Fi account, it would be Fi + Federal icon
    // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/FFF-Home-V2.0-(WIP)?node-id=824%3A17166
    api.typesv2.common.Image account_icon = 2;
    // Overall balance of Fi Account
    api.typesv2.Money balance = 3;
    // Title to be shown below Bank icon
    api.typesv2.common.Text title = 4;
    // All Fi account related information such as Interest rates, account no., IFSC, UPI ID
    repeated FiAccountInfo account_info = 5;
    // Details regarding actions on summary page - Add money, Get statement, Account settings, Share account details
    repeated AccountAction account_actions = 6;
    // Body of the summary page which has opening balance, spends, invested, money in of the month
    FiAccountSummary account_summary = 7;
    // Additional account details such as account type, and interest rates
    AdditionalAccountInfo additional_account_info = 8;
    // Last synced data
    string last_synced_msg = 9;
    // banking partner details will be shown
    PartnerBank partner_bank = 10;
    // icon will provide warning icon if error in account balance
    Icon bal_icon = 11;
    // A CTA action to force refresh balance in  the Fi account summary screen:
    // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21134-3920&mode=design&t=AGTCHpFp8mrLd1Ex-4
    AccountAction refresh_cta = 12;
    // Text to be shown below the Main balance screen in cases like stale/incorrect balance, usually when we surface the
    // force refresh CTA as well:
    // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21134-3920&mode=design&t=AGTCHpFp8mrLd1Ex-4
    api.typesv2.common.Text last_synced_msg_force_refresh = 13;
    // Explainer for each of the account
    // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=5354-15208&t=6oeTUJ1kuXmvvVpA-4
    api.typesv2.ui.IconTextComponent explainer = 14;
    // Account Product Offering type for the account
    api.typesv2.account.AccountProductOffering account_product_offering = 15;
  }

  api.typesv2.ui.TabWidget tab_widget = 14;
  // List of fi account summary page data. e,g, NRE,NRO
  repeated FiAccountSummaryTabData fi_account_summary_list = 15;
  // Default tab to be selected
  string selected_tab_id = 16;
  // AMB Entrypoint Banner Message
  Banner amb_entrypoint_banner = 17;
}

/* This object will be used for each account information displayed on savings summary screen.
 Eg; for interest rate information, following would be the FiAccountInfo
  {
  title: "Interest rate".
  value: "3%",
  action: SHOW_INFO,
  }
*/
message FiAccountInfo {
  // Title of account information
  api.typesv2.common.Text title = 1;
  // Value of account information
  api.typesv2.common.Text value = 2;
  enum Action {
    ACTION_UNSPECIFIED = 0;
    // on click, the user can copy the information
    COPY = 1;
    // on click, the user will be shown addition information
    SHOW_INFO = 2;
  }
  Action action = 3;
  // Image to be shown for the action
  api.typesv2.common.Image action_image = 4;
}

message FiAccountSummary {
  // Title of account summary section
  api.typesv2.common.Text title = 1;
  message SummaryEntry {
    // Icon shown representing the summary entry
    api.typesv2.common.Image icon = 1;
    // Title of a summary entry. Values can be eg; "Open Balance", "Spent this month" etc
    api.typesv2.common.Text title = 2;
    // Money value of each summary type.
    api.typesv2.Money money_primary = 3;
    // Money secondary - Money value shown below primary value. This can be processing money in case of investments
    api.typesv2.Money money_secondary = 4;
    // Image to show used to indicate secondary money meaning
    api.typesv2.common.Image image_secondary = 5;
    // Deeplink of the entry
    deeplink.Deeplink deeplink = 6;
  }
  repeated SummaryEntry summary_entries = 2;
}

/*
There are some additional account info to be shown as a separate section on summary page. Eg Account type, Info etc.
AdditionalAccountInfo will be used to show that section.
Eg,
{
  title: "Interest rates & fees",
}
 */
message AdditionalAccountInfo {
  message Info {
    // title of the info.
    api.typesv2.common.Text title = 1;
    // Redirect link
    oneof RedirectLink {
      // Url to be redirected to
      string url = 4;
    }
    oneof Placeholder {
      // image will be used as placeholder - RedirectLink
      api.typesv2.common.Image image = 3;
    }
  }
  // List of additional info - Account type, Interest rate etc.
  repeated Info infos = 1;
}

/*
  There are certain actions the user can take on accounts summary screen. Eg, Add money, Get statement etc.
  AccountAction object will be used to represent a single action.
  Eg.
  {
    title: "ADD MONEY",
    icon: <image url for add money icon>,
    dl_action: <deeplink to add money screen>
  }
 */
message AccountAction {
  // [Optional] title for each account action. This can be optional when certain CTA variants only show Icon. e.g.:
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21134-4020&mode=dev
  api.typesv2.common.Text title = 1;
  // image url for icon image
  api.typesv2.common.Image icon = 2;
  enum ClientAction {
    UNSPECIFIED = 0;
    // To share account details
    SHARE_ACCOUNT_DETAILS = 1;
    // Custom client action to trigger the Fi account summary RPC call on CTA click
    REFRESH_FI_ACCOUNT_SUMMARY_RPC = 2;
  }
  oneof action {
    // deeplink to be used on user click
    deeplink.Deeplink dl_action = 3;
    // Enum to represent client specific actions
    ClientAction client_action = 4;
  }
  // [Optional] color to be rendered for the CTA background. Client uses Figma fallback values, if this is not provided
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 5;
}

message GetFiAccountDashboardRequest {
  frontend.header.RequestHeader req = 1;
  // Request param to control which Dashboard UI version is shown on clients. This param will be not set for old clients
  // and may/may not be set for new clients based on experimentation. See DashboardVersion docs for default handling
  DashboardVersion dashboard_version = 2;
  // Optional field to control zero state card variant to be shown for DASHBOARD_VERSION_V2.
  // This may only be set if dashboard_version is set to DASHBOARD_VERSION_V2
  // Even if not set, the base variant of zero state dashboard cards would be shown
  home.ZeroStateDashboardCardVariant zero_state_dashboard_variant = 3;
  // [Optional] Enum value sent in client requests to trigger a force balance update from Pay backend. This will be nil
  // for normal calls when the page loads for instance
  ForceBalanceUpdate force_balance_update = 4;
  // Request param to control which account (Regular, NRE or NRO) dashboard to return
  // To maintain backward compatibility, the rpc returns any savings account (irrespective of APO ) in case when Account Product Offering is Unspecified
  api.typesv2.account.AccountProductOffering account_product_offering = 5;
  // Request param to control which Partner Bank account to return
  // To maintain backward compatibility, the rpc returns savings account of any partner bank present in DB for user (i.e Federal since it is the only partner bank for savings account currently) in case when partner bank is Unspecified
  vendorgateway.Vendor partner_bank = 6;
}

message GetFiAccountDashboardResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Home dashboard info
  HomeDashboard dashboard_info = 2;
  // Overall balance of Fi savings Account
  api.typesv2.Money balance = 3;
}

message GetUpcomingUserActivitiesRequest {
  frontend.header.RequestHeader req = 1;
}

message GetUpcomingUserActivitiesResponse {
  frontend.header.ResponseHeader resp_header = 1;
  UpcomingUserActivitiesZeroStateParams zero_state_params = 2;
  UpcomingUserActivities upcoming_activities = 3;

  // redirect users to their existing rules management page
  // can be empty if user has not set up any rules
  api.typesv2.ui.IconTextComponent manage_button = 4;
}

message UpcomingUserActivities {
  // list of upcoming activities
  repeated UpcomingActivity upcoming_activity = 1;
}

message UpcomingUserActivitiesZeroStateParams {
  // Number of slots to show on top of upcoming activities
  int32 additional_zero_slots = 1;
  // Empty state icons
  repeated api.typesv2.common.Image image = 2;
  // empty state string
  string zero_state_msg = 3;
  // empty state deeplink
  deeplink.Deeplink zero_state_deeplink = 4;
}

message UpcomingActivity {
  // name of entity - subscription name for which this activity is displayed
  api.typesv2.common.Text title = 1;
  // subscription amount
  api.typesv2.Money amount = 2;
  // the subscription date when it is to be executed
  api.typesv2.Date date = 3;
  // image of icon
  api.typesv2.common.Image icon = 4;
  // the straight upward arrow represents outgoing txn, the additional tilted one represents investment
  // ref: https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=1158%3A32447
  api.typesv2.common.Image transaction_type_icon = 5;
  // Deeplink to which the user is taken on click
  deeplink.Deeplink link = 6;
  // default background colour code if icon not present
  api.typesv2.ui.BackgroundColour image_colour_code = 7;
  // default text colour code if icon not present
  string text_colour_code = 8;
  // border color of the activity
  api.typesv2.common.ui.widget.BackgroundColour border_color = 9;
}

message GetHomeExploreRequest {
  frontend.header.RequestHeader req = 1;
}

message GetHomeExploreResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // List of sections in home explore
  repeated HomeExploreSection sections = 2 [deprecated = true];
  // Figma for new Explore changes
  // Figma -> https://www.figma.com/design/Sqs3y3hNKojUuX0RDbLcg3/%F0%9F%93%8D-Home-Workfile-3?node-id=11778-64070&t=jB5NQzpspXHbUboc-0
  // Explore Page Title eg : Explore the world of Fi
  api.typesv2.common.Text page_title = 3;
  // Search Container in Explore screen
  SearchUiContainer search_ui_container = 4 [deprecated = true];
  // Various sections supporting in Explore screen eg: Promo banner, Explore Icons, feedback Section etc...
  repeated HomeExploreWidget explore_widget_sections = 5;
  // Sticky Icon present in the bottom right of explore screen eg: currently using to edit shortcuts
  Icon sticky_icon = 6;
  // toast text to display after editing shortcuts eg: Shortcuts updated on home
  api.typesv2.common.Text toast_text = 7;
  // Updated Explore Search Widget in Explore screen
  ExploreSearchWidget explore_search_widget = 8;
  // Image background for explore screen
  // figma -> https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=28132-12646&t=Px1PkwrvyU23MOeA-4
  api.typesv2.common.VisualElement top_section_background = 9;
}

message HomeExploreWidget {
  oneof section {
    // Normal Section and Icons in Explore
    HomeExploreSection home_explore_section = 1;
    // Feedback Section in Explore
    ExploreFeedbackSection feedback_section = 2;
    // Home Explore Section with Full Visual Element Card
    FullVisualElementCardSection full_visual_element_card_section = 3;
  }
  // figma -> https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=28132-12690&t=Px1PkwrvyU23MOeA-4
  // [Optional] spacing to be rendered on top of this Home
  api.typesv2.ui.sdui.components.Spacer top_spacer = 4;
  // [Optional] spacing to be rendered on bottom of each widget
  api.typesv2.ui.sdui.components.Spacer bottom_spacer = 5;
}

message ExploreSearchWidget {
  // Background color of the search widget
  api.typesv2.ui.BackgroundColour bg_color = 1;
  // Deeplink to navigate to AskFi Screen
  deeplink.Deeplink deeplink = 2;
  // Left icon contains searchIcon
  api.typesv2.ui.IconTextComponent left_element = 3;
  // Optional right Icon
  api.typesv2.ui.IconTextComponent right_element = 4;
  // Search bar text contents
  ExploreSearchBarTextContent text_content = 5;
  // Border background color
  // deprecating this since it isn't support linear gradients
  // Figma -> https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=28132-12652&t=Px1PkwrvyU23MOeA-4
  api.typesv2.ui.BackgroundColour border_bg_color = 6 [deprecated = true];
  // shadow of the search bar
  api.typesv2.common.ui.widget.Shadow shadow = 7;
  // border of the search bar
  api.typesv2.common.ui.widget.BackgroundColour border_color = 8;
}

message ExploreSearchBarTextContent {
  // Search bar texts to animate
  repeated string queries = 1;
  // Font color of searchbar texts
  string font_color = 2;
}

message SearchUiContainer {
  // search bar ITC in explore screen contains searchIcon, searchBar and Text and Background color of the search bar
  api.typesv2.ui.IconTextComponent search_text_field = 1;
  // deeplink to navigate to AskFi Screen
  deeplink.Deeplink deeplink = 2;
}

message ExploreFeedbackSection {
  // Visual Element in top section of the feedback section
  api.typesv2.common.VisualElement highlight = 1;
  // Title in feedback section eg: Can’t find what you’re looking for?
  api.typesv2.common.Text feedback_title = 2;
  // Subtitle in feedback section eg: If you think we’re missing a feature, let us know.
  api.typesv2.common.Text feedback_subtitle = 3;
  // Button to trigger the feedback bottom sheet eg: Share feedback
  api.typesv2.ui.IconTextComponent feedback_button = 4;
  // Background color for the section
  api.typesv2.ui.BackgroundColour bg_colour = 5;
}

message HomeExploreSection {
  // Title of the section
  api.typesv2.common.Text title = 1;
  // List of icons to be shown in each section
  repeated Icon icon = 2;
  // Background colour for each section
  api.typesv2.ui.BackgroundColour bg_colour = 3;
}

// Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7600-5699&t=WrTAdngQ7DjsA6jd-0
message FullVisualElementCardSection {
  // Title of the section
  // Eg: Get a loan instantly!
  api.typesv2.common.Text title = 1;
  // visual element to be shown on the entire card
  api.typesv2.common.VisualElement visual_element = 2;
  // Deeplink to navigate to the screen
  deeplink.Deeplink deeplink = 3;
  // Background colour for each section
  api.typesv2.ui.BackgroundColour bg_colour = 4;
}

message GetHomeAppWalkThroughFlowsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetHomeAppWalkThroughFlowsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // onboarding walk through explaining features in new home v2
  // Will be nil if not applicable
  OnboardingWalkthrough onboarding_walkthrough = 2 [deprecated = true];
  // PrivacySettingWalkthrough will take the user through how to hide/show balance.
  // privacy_setting_walkthrough - will be nil if not applicable
  PrivacySettingWalkthrough privacy_setting_walkthrough = 3;
  // Dashboard intro card details
  DashboardIntroCardDetails dashboard_intro_card_details = 4 [deprecated = true];
  // Walkthrough for interacting with Shortcuts
  HomeShortcutsWalkthrough shortcuts_walkthrough = 5;
  // Shortcuts intro card details
  DashboardIntroCardDetails shortcuts_intro_card_details = 6;
}

message GetHomeProfileInfoRequest {
  frontend.header.RequestHeader req = 1;
}

message GetHomeProfileInfoResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // User's badge depends on their kyc state, salary account state
  api.typesv2.common.Image badge = 2;
  // profile image
  api.typesv2.common.Image profile_image = 3;
  // Profile nudge
  message ProfileNudge {
    // Icon to show when the nudge expands
    Icon expanded_icon = 1;
    // Ui variant of the Profile notch to render in clients
    UiVariant ui_variant = 2;
    // Instructs clients about which variant of the Profile Icon + Notch UI to render
    enum UiVariant {
      // Existing variant of the Profile Nudge Ui, which is fused with the Profile icon
      PROFILE_NUDGE_UI_VARIANT_UNSPECIFIED = 0;
      // New Profile nudge variant, which is shown separately from the Profile icon:
      // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=22363-21568&mode=design&t=mwdKTvL8UxCjHHNf-4
      PROFILE_NUDGE_UI_VARIANT_STANDALONE = 1;
    }
    // Border color for the profile nudge, supporting both solid and gradient styles.
    // This defines the visual outline of the profile nudge component.
    // Figma - https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=28047-37963&t=swEiRrQfCadlcZ9A-0
    api.typesv2.common.ui.widget.BackgroundColour border_color = 3;
  }
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=2082%3A50547&t=gPv4Zd9gOiwl2uOr-1
  ProfileNudge profile_nudge = 4;
  // Name of the user. This is used to render the initials in the Profile Icon, when the Profile icon is not available
  api.typesv2.common.Name user_name = 5;
  // Border color for the profile notch, supporting both solid and gradient styles.
  // This is used to highlight the profile notch component.
  // Figma - https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=28047-37963&t=swEiRrQfCadlcZ9A-0
  api.typesv2.common.ui.widget.BackgroundColour border_color = 6;
}

message GetHomeShortcutsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetHomeShortcutsResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // Title of the shortcuts section, can be nil
  // **For old design:** Keep this as nil.
  // **For new design:** Pass "Explore Fi" as the title.
  // https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=28047-37963&t=swEiRrQfCadlcZ9A-0
  api.typesv2.common.Text title = 2;

  // zero state of the home shortcuts widget
  // this will be nil if user has set any shortcuts
  api.typesv2.ui.IconTextComponent zero_state = 3;

  // List of icons returned from frontend, the preference of icons are stored in user preferences
  // and when this is called, we send the persisted icon list
  // To save another set of icons, frontend can directly call add user preferences with relevant preference type
  // and value
  // These are the shortcut icons which will be sent to the client to display
  repeated Icon shortcut_icons = 4;

  // Maximum number of shortcuts that can be set by the user. This is used to render some placeholder icons in Ui
  uint32 maximum_no_of_shortcuts = 5;

  // Allows users to view and explore additional shortcuts beyond the visible ones.
  // https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=28047-37963&t=swEiRrQfCadlcZ9A-0
  api.typesv2.ui.IconTextComponent see_all_cta = 6;
  // definition of how many of the icons to be shown in a row by client.
  // eg: if server sends 8 icons, and this is passed as 4, then show 4, 4 in a 2 rows.

  // Defines how many icons should be displayed per row in the UI.
  // Example: If the server sends 8 icons and max_icons_in_row = 4,
  // then client should display them in 'two rows' (4 icons per row).
  uint32 max_icons_in_row = 7;
}

message GetHomeShortcutOptionsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetHomeShortcutOptionsResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // section for showing already chosen shortcut icons
  ChosenShortcutsSection chosen_shortcuts_section = 2;

  // section for showing available shortcut icons
  AvailableShortcutsSection available_shortcuts_section = 3;

  // map of chosen shortcuts section state and list of ctas for bottom cta's (ex: cancel and save for now)
  map<string, BottomCtas> bottom_ctas_chosen_shortcuts_section_state_to_cta_list_map = 4;

  // map of chosen shortcuts section state and list of ctas for back btn cta
  map<string, BottomCtas> back_btn_cta_chosen_shortcuts_section_state_to_list_map = 5;

  message ChosenShortcutsSection {
    // List of shortcut icons stored in user preferences
    repeated Icon chosen_shortcut_icons = 1;

    // maximum number of shortcuts that can be set by the user
    uint32 maximum_no_of_shortcuts = 2;
  }

  message AvailableShortcutsSection {
    // List of rows of available shortcuts to choose from
    repeated AvailableShortcutsRow available_shortcuts_rows = 1;

    message AvailableShortcutsRow {
      // Title of the section
      api.typesv2.common.Text title = 1;
      // List of icons to be shown in each section
      repeated Icon icon = 2;
    }
  }

  message BottomCtas {
    repeated CTA ctas = 1;
  }

  enum ChosenShortcutsSectionState {
    CHOSEN_SHORTCUTS_SECTION_STATE_UNSPECIFIED = 0;

    // if no shortcut icons are chosen by the user
    CHOSEN_SHORTCUTS_SECTION_STATE_ZERO = 1;

    // if some shortcut icons are add or removed by the user
    // state can move from UPDATED to ZERO if user removed all chosen shortcuts
    CHOSEN_SHORTCUTS_SECTION_STATE_UPDATED = 2;
  }
}

message GetUpiWidgetRequest {
  frontend.header.RequestHeader req = 1;
}

/**
  Returns response for rendering the UPI widget on Home:
  https://www.figma.com/design/ai7gHuFQud7VOsPN0z8wh5/D2H-%E2%80%A2-FFF?node-id=4076-18358&t=eJBy7rteRbXACYT6-4
 */
message GetUpiWidgetResponse {
  frontend.header.ResponseHeader resp_header = 1;

  api.typesv2.ui.sdui.sections.Section section = 2;
}

message GetTrustMarkerRequest {
  frontend.header.RequestHeader req = 1;
}

/**
  Gives SDUI trust marker widget and response header as a response
 */
message GetTrustMarkerResponse {
  frontend.header.ResponseHeader resp_header = 1;

  api.typesv2.ui.sdui.sections.Section section = 2;
}

message SetHomeShortcutsRequest {
  frontend.header.RequestHeader req = 1;

  // list of shortcut icon types to be persisted for the user
  repeated string shortcut_icon_ids = 2;
}

message SetHomeShortcutsResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

/**
  Request for GetFocussedDashboardInfo RPC to get the info about focussed dashboard card
 */
message GetFocussedDashboardInfoRequest {
  frontend.header.RequestHeader req = 1;
}

/**
  Response for GetFocussedDashboardInfo RPC to get the info about focussed dashboard card
 */
message GetFocussedDashboardInfoResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // enum to specify the name of the dashboard card to be focussed or animated to
  DashboardWidget.DashboardView.DashboardViewType focussed_dashboard_name = 2;
  // Metadata field for analytics
  Metadata meta_data = 3;
  // metadata about the focussed dashboard card, kept for logging purpose on client
  message Metadata {
    string severity_level = 1;
    // this is the description of the warning
    string warning_description = 2;
  }
}

/* This proto is for communicating within the frontend layer only */
message GetDashboardWarningRequest {
  // actor for whom we want to fetch dashboard warning
  string actor_id = 1;
}

/* This proto is for communicating within the frontend layer only */
message GetDashboardWarningResponse {
  // mandatory: dashboard view for which we're sending warning
  DashboardWidget.DashboardView.DashboardViewType dashboard_view_type = 1;

  // mandatory: severity of the warning
  Severity severity = 2;
  enum Severity {
    SEVERITY_UNSPECIFIED = 0;
    // if an action is overdue, for eg- cc bill payment overdue
    SEVERITY_URGENT_TIME_OVERDUE = 1;
    // if action should be taken within 48 hours
    SEVERITY_SEVERE_TIME_SENSITIVE_48_HOURS = 2;
    // if action should be taken within 7 days
    SEVERITY_CRITICAL_TIME_SENSITIVE_7_DAYS = 3;
    // if action can be taken in more than 7 days
    SEVERITY_HIGH_PRIORITY_TIME_SENSITIVE_MORE_THAN_7_DAYS = 4;
    // if action is high priority but not time sensitive
    SEVERITY_HIGH_PRIORITY_NOT_TIME_SENSITIVE = 5;
    // if action is medium priority and not time sensitive
    SEVERITY_MEDIUM_PRIORITY_NOT_TIME_SENSITIVE = 6;
  }

  // mandatory: description of the warning
  // for eg: cc bill payment overdue
  string description = 3;
}

/**
  Request for GetNavigationBarHighlights RPC to get the info about navigation bar highlights
 */
message GetNavigationBarHighlightsRequest {
  frontend.header.RequestHeader req = 1;

  // additional metadata sent by the client to determine the logic for different navigation bar highlights.
  RequestMetadata request_metadata = 2;

  message RequestMetadata {
    // timestamp of the last visit to the rewards screen, used to determine if the rewards icon should be highlighted or not.
    google.protobuf.Timestamp my_rewards_last_visited_timestamp = 1;
  }
}

/**
  Response for GetNavigationBarHighlights RPC to get the info about navigation bar highlights
 */
message GetNavigationBarHighlightsResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // prioritised list of navigation bar highlights
  // client will store and update this on each call
  repeated NavigationBarHighlight navigation_bar_highlights = 2;

  message NavigationBarHighlight {
    // id to uniquely identify each highlight
    string id = 1;
    // enum to specify the name of the navigation bar icon to be highlighted
    api.typesv2.home.IconType icon_type = 2;
    // visual element to be used to highlight the navigation bar icon
    api.typesv2.common.VisualElement highlight = 3;
    // bounds post which client will stop highlighting the nav icon
    // this will kick in as soon as any one of the bounds is breached
    HighlightUpperBounds upper_bounds = 4;
    message HighlightUpperBounds {
      // max no. of sessions post which highlight won't be shown
      int32 max_sessions = 1;
      // max no. of clicks post which highlight won't be shown
      int32 max_clicks = 2;
    }
    // Metadata field for analytics, kept for logging purpose on client
    // home will make sure that mandatory keys like "Reason", etc are present
    map<string, string> meta_data = 5;
    // show dot on the icon in the navigation bar to indicate the highlight
    // figma - https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=26057-2377&t=ca7Rno3J1Q5GcoiM-0
    bool show_navbar_highlight = 6;
  }

  // time to wait for, before highlighting navigation bar
  int32 highlight_delay_in_milliseconds = 4;
}

/* This proto is for communicating within the frontend layer only */
message GetNavigationBarHighlightRequest {
  // actor for whom we want to fetch navigation bar highlight
  string actor_id = 1;
  // additional request metadata information to determine the logic for navigation bar highlights.
  GetNavigationBarHighlightsRequest.RequestMetadata request_metadata = 2;
}

/* This proto is for communicating within the frontend layer only */
message GetNavigationBarHighlightResponse {
  // navigation bar highlight to be displayed on home
  GetNavigationBarHighlightsResponse.NavigationBarHighlight navigation_bar_highlight = 1;
}

message GetAlternateAppIconRequest {
  frontend.header.RequestHeader req = 1;
}

message GetAlternateAppIconResponse {
  frontend.header.ResponseHeader resp_header = 1;

  AlternateAppIconIdentifier alternate_app_icon_identifier = 2;
}

// enum to specify the alternate app icon to be shown
enum AlternateAppIconIdentifier {
  ALTERNATE_APP_ICON_IDENTIFIER_UNSPECIFIED = 0;
  ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_PLUS = 1;
  ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_INFINITE = 2;
  ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_SALARY = 3;
  ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_PRIME = 4;
  ALTERNATE_APP_ICON_IDENTIFIER_OCCASION_HOLI = 5;
  ALTERNATE_APP_ICON_IDENTIFIER_OCCASION_DIWALI = 6;
  ALTERNATE_APP_ICON_IDENTIFIER_OCCASION_CHRISTMAS = 7;
  ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_PLUS_OCCASION_HOLI = 8;
  ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_PLUS_OCCASION_DIWALI = 9;
  ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_PLUS_OCCASION_CHRISTMAS = 10;
  ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_INFINITE_OCCASION_HOLI = 11;
  ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_INFINITE_OCCASION_DIWALI = 12;
  ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_INFINITE_OCCASION_CHRISTMAS = 13;
  ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_SALARY_OCCASION_HOLI = 14;
  ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_SALARY_OCCASION_DIWALI = 15;
  ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_SALARY_OCCASION_CHRISTMAS = 16;
  ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_PRIME_OCCASION_HOLI = 17;
  ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_PRIME_OCCASION_DIWALI = 18;
  ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_PRIME_OCCASION_CHRISTMAS = 19;
}

// enum sent from the client to identify if the service is requesting to update balance on his home screen.
// Optional field: If not sent then default FORCE_BALANCE_UPDATE_UNSPECIFIED will be set
// If FORCE_BALANCE_UPDATE_NEEDED is passed as value then we will fetch the balance from vendor GetBalance first and fallback will be GetBalanceV1 api
// and vice versa if the FORCE_BALANCE_UPDATE_NEEDED is not provided
enum ForceBalanceUpdate {
  FORCE_BALANCE_UPDATE_UNSPECIFIED = 0;
  FORCE_BALANCE_UPDATE_NEEDED = 1;
  FORCE_BALANCE_UPDATE_NOT_NEEDED = 2;
}
