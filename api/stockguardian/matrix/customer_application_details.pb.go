//go:generate gen_sql -types=StageData

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/stockguardian/matrix/internal/customer_application_details.proto

package matrix

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CustomerApplicationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// ID of the customer application the stage is linked to
	CustomerApplicationId string `protobuf:"bytes,2,opt,name=customer_application_id,json=customerApplicationId,proto3" json:"customer_application_id,omitempty"`
	// Request ID sent by client
	ClientRequestId string `protobuf:"bytes,3,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	// Stage of the application: EKYC, CKYC, VKYC, CROSS_DATA_VALIDATION
	Stage Stage `protobuf:"varint,4,opt,name=stage,proto3,enum=matrix.Stage" json:"stage,omitempty"`
	// Stage status - Initiated, InProgress, Completed, Failed, Expired
	Status StageStatus `protobuf:"varint,5,opt,name=status,proto3,enum=matrix.StageStatus" json:"status,omitempty"`
	// Granular failure status - CKYC_SEARCH_FAILED, EKYC_DATA_VALIDATION_FAILED
	FailureReason FailureReason `protobuf:"varint,6,opt,name=failure_reason,json=failureReason,proto3,enum=matrix.FailureReason" json:"failure_reason,omitempty"`
	// metadata needed for a stage
	StageData     *StageData             `protobuf:"bytes,7,opt,name=stage_data,json=stageData,proto3" json:"stage_data,omitempty"`
	CompletedAt   *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	DeletedAtUnix int64                  `protobuf:"varint,9,opt,name=deleted_at_unix,json=deletedAtUnix,proto3" json:"deleted_at_unix,omitempty"`
	// Reason for deletion of a stage, reason can be RETRIES, EXPIRED
	DeletionReason DeletionReason `protobuf:"varint,10,opt,name=deletion_reason,json=deletionReason,proto3,enum=matrix.DeletionReason" json:"deletion_reason,omitempty"`
}

func (x *CustomerApplicationDetails) Reset() {
	*x = CustomerApplicationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_matrix_internal_customer_application_details_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerApplicationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerApplicationDetails) ProtoMessage() {}

func (x *CustomerApplicationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_matrix_internal_customer_application_details_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerApplicationDetails.ProtoReflect.Descriptor instead.
func (*CustomerApplicationDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_matrix_internal_customer_application_details_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerApplicationDetails) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CustomerApplicationDetails) GetCustomerApplicationId() string {
	if x != nil {
		return x.CustomerApplicationId
	}
	return ""
}

func (x *CustomerApplicationDetails) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *CustomerApplicationDetails) GetStage() Stage {
	if x != nil {
		return x.Stage
	}
	return Stage_STAGE_UNSPECIFIED
}

func (x *CustomerApplicationDetails) GetStatus() StageStatus {
	if x != nil {
		return x.Status
	}
	return StageStatus_STAGE_STATUS_UNSPECIFIED
}

func (x *CustomerApplicationDetails) GetFailureReason() FailureReason {
	if x != nil {
		return x.FailureReason
	}
	return FailureReason_FAILURE_REASON_UNSPECIFIED
}

func (x *CustomerApplicationDetails) GetStageData() *StageData {
	if x != nil {
		return x.StageData
	}
	return nil
}

func (x *CustomerApplicationDetails) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *CustomerApplicationDetails) GetDeletedAtUnix() int64 {
	if x != nil {
		return x.DeletedAtUnix
	}
	return 0
}

func (x *CustomerApplicationDetails) GetDeletionReason() DeletionReason {
	if x != nil {
		return x.DeletionReason
	}
	return DeletionReason_DELETION_REASON_UNSPECIFIED
}

type StageData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StageData) Reset() {
	*x = StageData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_matrix_internal_customer_application_details_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageData) ProtoMessage() {}

func (x *StageData) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_matrix_internal_customer_application_details_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageData.ProtoReflect.Descriptor instead.
func (*StageData) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_matrix_internal_customer_application_details_proto_rawDescGZIP(), []int{1}
}

var File_api_stockguardian_matrix_internal_customer_application_details_proto protoreflect.FileDescriptor

var file_api_stockguardian_matrix_internal_customer_application_details_proto_rawDesc = []byte{
	0x0a, 0x44, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2f, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x1a, 0x24,
	0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61,
	0x6e, 0x2f, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfa, 0x03, 0x0a, 0x1a, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x17, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78,
	0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x2b, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e,
	0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x0e, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x15, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x46, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6d,
	0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x5f, 0x75, 0x6e, 0x69, 0x78, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x55, 0x6e, 0x69,
	0x78, 0x12, 0x3f, 0x0a, 0x0f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x74,
	0x72, 0x69, 0x78, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x52, 0x0e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x22, 0x0b, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x42,
	0x31, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2f, 0x6d, 0x61, 0x74, 0x72,
	0x69, 0x78, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_stockguardian_matrix_internal_customer_application_details_proto_rawDescOnce sync.Once
	file_api_stockguardian_matrix_internal_customer_application_details_proto_rawDescData = file_api_stockguardian_matrix_internal_customer_application_details_proto_rawDesc
)

func file_api_stockguardian_matrix_internal_customer_application_details_proto_rawDescGZIP() []byte {
	file_api_stockguardian_matrix_internal_customer_application_details_proto_rawDescOnce.Do(func() {
		file_api_stockguardian_matrix_internal_customer_application_details_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_stockguardian_matrix_internal_customer_application_details_proto_rawDescData)
	})
	return file_api_stockguardian_matrix_internal_customer_application_details_proto_rawDescData
}

var file_api_stockguardian_matrix_internal_customer_application_details_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_stockguardian_matrix_internal_customer_application_details_proto_goTypes = []interface{}{
	(*CustomerApplicationDetails)(nil), // 0: matrix.CustomerApplicationDetails
	(*StageData)(nil),                  // 1: matrix.StageData
	(Stage)(0),                         // 2: matrix.Stage
	(StageStatus)(0),                   // 3: matrix.StageStatus
	(FailureReason)(0),                 // 4: matrix.FailureReason
	(*timestamppb.Timestamp)(nil),      // 5: google.protobuf.Timestamp
	(DeletionReason)(0),                // 6: matrix.DeletionReason
}
var file_api_stockguardian_matrix_internal_customer_application_details_proto_depIdxs = []int32{
	2, // 0: matrix.CustomerApplicationDetails.stage:type_name -> matrix.Stage
	3, // 1: matrix.CustomerApplicationDetails.status:type_name -> matrix.StageStatus
	4, // 2: matrix.CustomerApplicationDetails.failure_reason:type_name -> matrix.FailureReason
	1, // 3: matrix.CustomerApplicationDetails.stage_data:type_name -> matrix.StageData
	5, // 4: matrix.CustomerApplicationDetails.completed_at:type_name -> google.protobuf.Timestamp
	6, // 5: matrix.CustomerApplicationDetails.deletion_reason:type_name -> matrix.DeletionReason
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_stockguardian_matrix_internal_customer_application_details_proto_init() }
func file_api_stockguardian_matrix_internal_customer_application_details_proto_init() {
	if File_api_stockguardian_matrix_internal_customer_application_details_proto != nil {
		return
	}
	file_api_stockguardian_matrix_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_stockguardian_matrix_internal_customer_application_details_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerApplicationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_matrix_internal_customer_application_details_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_stockguardian_matrix_internal_customer_application_details_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_stockguardian_matrix_internal_customer_application_details_proto_goTypes,
		DependencyIndexes: file_api_stockguardian_matrix_internal_customer_application_details_proto_depIdxs,
		MessageInfos:      file_api_stockguardian_matrix_internal_customer_application_details_proto_msgTypes,
	}.Build()
	File_api_stockguardian_matrix_internal_customer_application_details_proto = out.File
	file_api_stockguardian_matrix_internal_customer_application_details_proto_rawDesc = nil
	file_api_stockguardian_matrix_internal_customer_application_details_proto_goTypes = nil
	file_api_stockguardian_matrix_internal_customer_application_details_proto_depIdxs = nil
}
