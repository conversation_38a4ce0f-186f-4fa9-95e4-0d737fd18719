// Code generated by MockGen. DO NOT EDIT.
// Source: api/stockguardian/matrix/dbstate/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	dbstate "github.com/epifi/be-common/api/sherlock/dev/dbstate"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockDBStateServiceClient is a mock of DBStateServiceClient interface.
type MockDBStateServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockDBStateServiceClientMockRecorder
}

// MockDBStateServiceClientMockRecorder is the mock recorder for MockDBStateServiceClient.
type MockDBStateServiceClientMockRecorder struct {
	mock *MockDBStateServiceClient
}

// NewMockDBStateServiceClient creates a new mock instance.
func NewMockDBStateServiceClient(ctrl *gomock.Controller) *MockDBStateServiceClient {
	mock := &MockDBStateServiceClient{ctrl: ctrl}
	mock.recorder = &MockDBStateServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDBStateServiceClient) EXPECT() *MockDBStateServiceClientMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockDBStateServiceClient) GetData(ctx context.Context, in *dbstate.GetDataRequest, opts ...grpc.CallOption) (*dbstate.GetDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetData", varargs...)
	ret0, _ := ret[0].(*dbstate.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockDBStateServiceClientMockRecorder) GetData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockDBStateServiceClient)(nil).GetData), varargs...)
}

// GetEntityList mocks base method.
func (m *MockDBStateServiceClient) GetEntityList(ctx context.Context, in *dbstate.GetEntityListRequest, opts ...grpc.CallOption) (*dbstate.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEntityList", varargs...)
	ret0, _ := ret[0].(*dbstate.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockDBStateServiceClientMockRecorder) GetEntityList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockDBStateServiceClient)(nil).GetEntityList), varargs...)
}

// GetParameterList mocks base method.
func (m *MockDBStateServiceClient) GetParameterList(ctx context.Context, in *dbstate.GetParameterListRequest, opts ...grpc.CallOption) (*dbstate.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetParameterList", varargs...)
	ret0, _ := ret[0].(*dbstate.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockDBStateServiceClientMockRecorder) GetParameterList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockDBStateServiceClient)(nil).GetParameterList), varargs...)
}

// MockDBStateServiceServer is a mock of DBStateServiceServer interface.
type MockDBStateServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockDBStateServiceServerMockRecorder
}

// MockDBStateServiceServerMockRecorder is the mock recorder for MockDBStateServiceServer.
type MockDBStateServiceServerMockRecorder struct {
	mock *MockDBStateServiceServer
}

// NewMockDBStateServiceServer creates a new mock instance.
func NewMockDBStateServiceServer(ctrl *gomock.Controller) *MockDBStateServiceServer {
	mock := &MockDBStateServiceServer{ctrl: ctrl}
	mock.recorder = &MockDBStateServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDBStateServiceServer) EXPECT() *MockDBStateServiceServerMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockDBStateServiceServer) GetData(arg0 context.Context, arg1 *dbstate.GetDataRequest) (*dbstate.GetDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetData", arg0, arg1)
	ret0, _ := ret[0].(*dbstate.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockDBStateServiceServerMockRecorder) GetData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockDBStateServiceServer)(nil).GetData), arg0, arg1)
}

// GetEntityList mocks base method.
func (m *MockDBStateServiceServer) GetEntityList(arg0 context.Context, arg1 *dbstate.GetEntityListRequest) (*dbstate.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityList", arg0, arg1)
	ret0, _ := ret[0].(*dbstate.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockDBStateServiceServerMockRecorder) GetEntityList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockDBStateServiceServer)(nil).GetEntityList), arg0, arg1)
}

// GetParameterList mocks base method.
func (m *MockDBStateServiceServer) GetParameterList(arg0 context.Context, arg1 *dbstate.GetParameterListRequest) (*dbstate.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParameterList", arg0, arg1)
	ret0, _ := ret[0].(*dbstate.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockDBStateServiceServerMockRecorder) GetParameterList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockDBStateServiceServer)(nil).GetParameterList), arg0, arg1)
}

// MockUnsafeDBStateServiceServer is a mock of UnsafeDBStateServiceServer interface.
type MockUnsafeDBStateServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeDBStateServiceServerMockRecorder
}

// MockUnsafeDBStateServiceServerMockRecorder is the mock recorder for MockUnsafeDBStateServiceServer.
type MockUnsafeDBStateServiceServerMockRecorder struct {
	mock *MockUnsafeDBStateServiceServer
}

// NewMockUnsafeDBStateServiceServer creates a new mock instance.
func NewMockUnsafeDBStateServiceServer(ctrl *gomock.Controller) *MockUnsafeDBStateServiceServer {
	mock := &MockUnsafeDBStateServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeDBStateServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeDBStateServiceServer) EXPECT() *MockUnsafeDBStateServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedDBStateServiceServer mocks base method.
func (m *MockUnsafeDBStateServiceServer) mustEmbedUnimplementedDBStateServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedDBStateServiceServer")
}

// mustEmbedUnimplementedDBStateServiceServer indicates an expected call of mustEmbedUnimplementedDBStateServiceServer.
func (mr *MockUnsafeDBStateServiceServerMockRecorder) mustEmbedUnimplementedDBStateServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedDBStateServiceServer", reflect.TypeOf((*MockUnsafeDBStateServiceServer)(nil).mustEmbedUnimplementedDBStateServiceServer))
}
