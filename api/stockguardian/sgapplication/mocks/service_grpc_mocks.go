// Code generated by MockGen. DO NOT EDIT.
// Source: api/stockguardian/sgapplication/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	sgapplication "github.com/epifi/gamma/api/stockguardian/sgapplication"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockApplicationClient is a mock of ApplicationClient interface.
type MockApplicationClient struct {
	ctrl     *gomock.Controller
	recorder *MockApplicationClientMockRecorder
}

// MockApplicationClientMockRecorder is the mock recorder for MockApplicationClient.
type MockApplicationClientMockRecorder struct {
	mock *MockApplicationClient
}

// NewMockApplicationClient creates a new mock instance.
func NewMockApplicationClient(ctrl *gomock.Controller) *MockApplicationClient {
	mock := &MockApplicationClient{ctrl: ctrl}
	mock.recorder = &MockApplicationClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockApplicationClient) EXPECT() *MockApplicationClientMockRecorder {
	return m.recorder
}

// CancelApplication mocks base method.
func (m *MockApplicationClient) CancelApplication(ctx context.Context, in *sgapplication.CancelApplicationRequest, opts ...grpc.CallOption) (*sgapplication.CancelApplicationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelApplication", varargs...)
	ret0, _ := ret[0].(*sgapplication.CancelApplicationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelApplication indicates an expected call of CancelApplication.
func (mr *MockApplicationClientMockRecorder) CancelApplication(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelApplication", reflect.TypeOf((*MockApplicationClient)(nil).CancelApplication), varargs...)
}

// GetApplicationStatus mocks base method.
func (m *MockApplicationClient) GetApplicationStatus(ctx context.Context, in *sgapplication.GetApplicationStatusRequest, opts ...grpc.CallOption) (*sgapplication.GetApplicationStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetApplicationStatus", varargs...)
	ret0, _ := ret[0].(*sgapplication.GetApplicationStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplicationStatus indicates an expected call of GetApplicationStatus.
func (mr *MockApplicationClientMockRecorder) GetApplicationStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplicationStatus", reflect.TypeOf((*MockApplicationClient)(nil).GetApplicationStatus), varargs...)
}

// GetLoanOffer mocks base method.
func (m *MockApplicationClient) GetLoanOffer(ctx context.Context, in *sgapplication.GetLoanOfferRequest, opts ...grpc.CallOption) (*sgapplication.GetLoanOfferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoanOffer", varargs...)
	ret0, _ := ret[0].(*sgapplication.GetLoanOfferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanOffer indicates an expected call of GetLoanOffer.
func (mr *MockApplicationClientMockRecorder) GetLoanOffer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanOffer", reflect.TypeOf((*MockApplicationClient)(nil).GetLoanOffer), varargs...)
}

// InitDisbursement mocks base method.
func (m *MockApplicationClient) InitDisbursement(ctx context.Context, in *sgapplication.InitDisbursementRequest, opts ...grpc.CallOption) (*sgapplication.InitDisbursementResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitDisbursement", varargs...)
	ret0, _ := ret[0].(*sgapplication.InitDisbursementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitDisbursement indicates an expected call of InitDisbursement.
func (mr *MockApplicationClientMockRecorder) InitDisbursement(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitDisbursement", reflect.TypeOf((*MockApplicationClient)(nil).InitDisbursement), varargs...)
}

// InitDrawDown mocks base method.
func (m *MockApplicationClient) InitDrawDown(ctx context.Context, in *sgapplication.InitDrawDownRequest, opts ...grpc.CallOption) (*sgapplication.InitDrawDownResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitDrawDown", varargs...)
	ret0, _ := ret[0].(*sgapplication.InitDrawDownResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitDrawDown indicates an expected call of InitDrawDown.
func (mr *MockApplicationClientMockRecorder) InitDrawDown(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitDrawDown", reflect.TypeOf((*MockApplicationClient)(nil).InitDrawDown), varargs...)
}

// InitEsign mocks base method.
func (m *MockApplicationClient) InitEsign(ctx context.Context, in *sgapplication.InitEsignRequest, opts ...grpc.CallOption) (*sgapplication.InitEsignResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitEsign", varargs...)
	ret0, _ := ret[0].(*sgapplication.InitEsignResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitEsign indicates an expected call of InitEsign.
func (mr *MockApplicationClientMockRecorder) InitEsign(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitEsign", reflect.TypeOf((*MockApplicationClient)(nil).InitEsign), varargs...)
}

// InitMandate mocks base method.
func (m *MockApplicationClient) InitMandate(ctx context.Context, in *sgapplication.InitMandateRequest, opts ...grpc.CallOption) (*sgapplication.InitMandateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitMandate", varargs...)
	ret0, _ := ret[0].(*sgapplication.InitMandateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitMandate indicates an expected call of InitMandate.
func (mr *MockApplicationClientMockRecorder) InitMandate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitMandate", reflect.TypeOf((*MockApplicationClient)(nil).InitMandate), varargs...)
}

// InitPennyDrop mocks base method.
func (m *MockApplicationClient) InitPennyDrop(ctx context.Context, in *sgapplication.InitPennyDropRequest, opts ...grpc.CallOption) (*sgapplication.InitPennDropResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitPennyDrop", varargs...)
	ret0, _ := ret[0].(*sgapplication.InitPennDropResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitPennyDrop indicates an expected call of InitPennyDrop.
func (mr *MockApplicationClientMockRecorder) InitPennyDrop(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitPennyDrop", reflect.TypeOf((*MockApplicationClient)(nil).InitPennyDrop), varargs...)
}

// InitiateKyc mocks base method.
func (m *MockApplicationClient) InitiateKyc(ctx context.Context, in *sgapplication.InitiateKycRequest, opts ...grpc.CallOption) (*sgapplication.InitiateKycResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateKyc", varargs...)
	ret0, _ := ret[0].(*sgapplication.InitiateKycResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateKyc indicates an expected call of InitiateKyc.
func (mr *MockApplicationClientMockRecorder) InitiateKyc(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateKyc", reflect.TypeOf((*MockApplicationClient)(nil).InitiateKyc), varargs...)
}

// RecordConsent mocks base method.
func (m *MockApplicationClient) RecordConsent(ctx context.Context, in *sgapplication.RecordConsentRequest, opts ...grpc.CallOption) (*sgapplication.RecordConsentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecordConsent", varargs...)
	ret0, _ := ret[0].(*sgapplication.RecordConsentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordConsent indicates an expected call of RecordConsent.
func (mr *MockApplicationClientMockRecorder) RecordConsent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordConsent", reflect.TypeOf((*MockApplicationClient)(nil).RecordConsent), varargs...)
}

// StartApplication mocks base method.
func (m *MockApplicationClient) StartApplication(ctx context.Context, in *sgapplication.StartApplicationRequest, opts ...grpc.CallOption) (*sgapplication.StartApplicationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartApplication", varargs...)
	ret0, _ := ret[0].(*sgapplication.StartApplicationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartApplication indicates an expected call of StartApplication.
func (mr *MockApplicationClientMockRecorder) StartApplication(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartApplication", reflect.TypeOf((*MockApplicationClient)(nil).StartApplication), varargs...)
}

// UpdateApplicationDetails mocks base method.
func (m *MockApplicationClient) UpdateApplicationDetails(ctx context.Context, in *sgapplication.UpdateApplicationDetailsRequest, opts ...grpc.CallOption) (*sgapplication.UpdateApplicationDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateApplicationDetails", varargs...)
	ret0, _ := ret[0].(*sgapplication.UpdateApplicationDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateApplicationDetails indicates an expected call of UpdateApplicationDetails.
func (mr *MockApplicationClientMockRecorder) UpdateApplicationDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateApplicationDetails", reflect.TypeOf((*MockApplicationClient)(nil).UpdateApplicationDetails), varargs...)
}

// UpdateUserDetails mocks base method.
func (m *MockApplicationClient) UpdateUserDetails(ctx context.Context, in *sgapplication.UpdateUserDetailsRequest, opts ...grpc.CallOption) (*sgapplication.UpdateUserDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateUserDetails", varargs...)
	ret0, _ := ret[0].(*sgapplication.UpdateUserDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserDetails indicates an expected call of UpdateUserDetails.
func (mr *MockApplicationClientMockRecorder) UpdateUserDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserDetails", reflect.TypeOf((*MockApplicationClient)(nil).UpdateUserDetails), varargs...)
}

// MockApplicationServer is a mock of ApplicationServer interface.
type MockApplicationServer struct {
	ctrl     *gomock.Controller
	recorder *MockApplicationServerMockRecorder
}

// MockApplicationServerMockRecorder is the mock recorder for MockApplicationServer.
type MockApplicationServerMockRecorder struct {
	mock *MockApplicationServer
}

// NewMockApplicationServer creates a new mock instance.
func NewMockApplicationServer(ctrl *gomock.Controller) *MockApplicationServer {
	mock := &MockApplicationServer{ctrl: ctrl}
	mock.recorder = &MockApplicationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockApplicationServer) EXPECT() *MockApplicationServerMockRecorder {
	return m.recorder
}

// CancelApplication mocks base method.
func (m *MockApplicationServer) CancelApplication(arg0 context.Context, arg1 *sgapplication.CancelApplicationRequest) (*sgapplication.CancelApplicationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelApplication", arg0, arg1)
	ret0, _ := ret[0].(*sgapplication.CancelApplicationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelApplication indicates an expected call of CancelApplication.
func (mr *MockApplicationServerMockRecorder) CancelApplication(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelApplication", reflect.TypeOf((*MockApplicationServer)(nil).CancelApplication), arg0, arg1)
}

// GetApplicationStatus mocks base method.
func (m *MockApplicationServer) GetApplicationStatus(arg0 context.Context, arg1 *sgapplication.GetApplicationStatusRequest) (*sgapplication.GetApplicationStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApplicationStatus", arg0, arg1)
	ret0, _ := ret[0].(*sgapplication.GetApplicationStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplicationStatus indicates an expected call of GetApplicationStatus.
func (mr *MockApplicationServerMockRecorder) GetApplicationStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplicationStatus", reflect.TypeOf((*MockApplicationServer)(nil).GetApplicationStatus), arg0, arg1)
}

// GetLoanOffer mocks base method.
func (m *MockApplicationServer) GetLoanOffer(arg0 context.Context, arg1 *sgapplication.GetLoanOfferRequest) (*sgapplication.GetLoanOfferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoanOffer", arg0, arg1)
	ret0, _ := ret[0].(*sgapplication.GetLoanOfferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanOffer indicates an expected call of GetLoanOffer.
func (mr *MockApplicationServerMockRecorder) GetLoanOffer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanOffer", reflect.TypeOf((*MockApplicationServer)(nil).GetLoanOffer), arg0, arg1)
}

// InitDisbursement mocks base method.
func (m *MockApplicationServer) InitDisbursement(arg0 context.Context, arg1 *sgapplication.InitDisbursementRequest) (*sgapplication.InitDisbursementResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitDisbursement", arg0, arg1)
	ret0, _ := ret[0].(*sgapplication.InitDisbursementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitDisbursement indicates an expected call of InitDisbursement.
func (mr *MockApplicationServerMockRecorder) InitDisbursement(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitDisbursement", reflect.TypeOf((*MockApplicationServer)(nil).InitDisbursement), arg0, arg1)
}

// InitDrawDown mocks base method.
func (m *MockApplicationServer) InitDrawDown(arg0 context.Context, arg1 *sgapplication.InitDrawDownRequest) (*sgapplication.InitDrawDownResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitDrawDown", arg0, arg1)
	ret0, _ := ret[0].(*sgapplication.InitDrawDownResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitDrawDown indicates an expected call of InitDrawDown.
func (mr *MockApplicationServerMockRecorder) InitDrawDown(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitDrawDown", reflect.TypeOf((*MockApplicationServer)(nil).InitDrawDown), arg0, arg1)
}

// InitEsign mocks base method.
func (m *MockApplicationServer) InitEsign(arg0 context.Context, arg1 *sgapplication.InitEsignRequest) (*sgapplication.InitEsignResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitEsign", arg0, arg1)
	ret0, _ := ret[0].(*sgapplication.InitEsignResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitEsign indicates an expected call of InitEsign.
func (mr *MockApplicationServerMockRecorder) InitEsign(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitEsign", reflect.TypeOf((*MockApplicationServer)(nil).InitEsign), arg0, arg1)
}

// InitMandate mocks base method.
func (m *MockApplicationServer) InitMandate(arg0 context.Context, arg1 *sgapplication.InitMandateRequest) (*sgapplication.InitMandateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitMandate", arg0, arg1)
	ret0, _ := ret[0].(*sgapplication.InitMandateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitMandate indicates an expected call of InitMandate.
func (mr *MockApplicationServerMockRecorder) InitMandate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitMandate", reflect.TypeOf((*MockApplicationServer)(nil).InitMandate), arg0, arg1)
}

// InitPennyDrop mocks base method.
func (m *MockApplicationServer) InitPennyDrop(arg0 context.Context, arg1 *sgapplication.InitPennyDropRequest) (*sgapplication.InitPennDropResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitPennyDrop", arg0, arg1)
	ret0, _ := ret[0].(*sgapplication.InitPennDropResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitPennyDrop indicates an expected call of InitPennyDrop.
func (mr *MockApplicationServerMockRecorder) InitPennyDrop(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitPennyDrop", reflect.TypeOf((*MockApplicationServer)(nil).InitPennyDrop), arg0, arg1)
}

// InitiateKyc mocks base method.
func (m *MockApplicationServer) InitiateKyc(arg0 context.Context, arg1 *sgapplication.InitiateKycRequest) (*sgapplication.InitiateKycResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateKyc", arg0, arg1)
	ret0, _ := ret[0].(*sgapplication.InitiateKycResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateKyc indicates an expected call of InitiateKyc.
func (mr *MockApplicationServerMockRecorder) InitiateKyc(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateKyc", reflect.TypeOf((*MockApplicationServer)(nil).InitiateKyc), arg0, arg1)
}

// RecordConsent mocks base method.
func (m *MockApplicationServer) RecordConsent(arg0 context.Context, arg1 *sgapplication.RecordConsentRequest) (*sgapplication.RecordConsentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordConsent", arg0, arg1)
	ret0, _ := ret[0].(*sgapplication.RecordConsentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordConsent indicates an expected call of RecordConsent.
func (mr *MockApplicationServerMockRecorder) RecordConsent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordConsent", reflect.TypeOf((*MockApplicationServer)(nil).RecordConsent), arg0, arg1)
}

// StartApplication mocks base method.
func (m *MockApplicationServer) StartApplication(arg0 context.Context, arg1 *sgapplication.StartApplicationRequest) (*sgapplication.StartApplicationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartApplication", arg0, arg1)
	ret0, _ := ret[0].(*sgapplication.StartApplicationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartApplication indicates an expected call of StartApplication.
func (mr *MockApplicationServerMockRecorder) StartApplication(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartApplication", reflect.TypeOf((*MockApplicationServer)(nil).StartApplication), arg0, arg1)
}

// UpdateApplicationDetails mocks base method.
func (m *MockApplicationServer) UpdateApplicationDetails(arg0 context.Context, arg1 *sgapplication.UpdateApplicationDetailsRequest) (*sgapplication.UpdateApplicationDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateApplicationDetails", arg0, arg1)
	ret0, _ := ret[0].(*sgapplication.UpdateApplicationDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateApplicationDetails indicates an expected call of UpdateApplicationDetails.
func (mr *MockApplicationServerMockRecorder) UpdateApplicationDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateApplicationDetails", reflect.TypeOf((*MockApplicationServer)(nil).UpdateApplicationDetails), arg0, arg1)
}

// UpdateUserDetails mocks base method.
func (m *MockApplicationServer) UpdateUserDetails(arg0 context.Context, arg1 *sgapplication.UpdateUserDetailsRequest) (*sgapplication.UpdateUserDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserDetails", arg0, arg1)
	ret0, _ := ret[0].(*sgapplication.UpdateUserDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserDetails indicates an expected call of UpdateUserDetails.
func (mr *MockApplicationServerMockRecorder) UpdateUserDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserDetails", reflect.TypeOf((*MockApplicationServer)(nil).UpdateUserDetails), arg0, arg1)
}

// MockUnsafeApplicationServer is a mock of UnsafeApplicationServer interface.
type MockUnsafeApplicationServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeApplicationServerMockRecorder
}

// MockUnsafeApplicationServerMockRecorder is the mock recorder for MockUnsafeApplicationServer.
type MockUnsafeApplicationServerMockRecorder struct {
	mock *MockUnsafeApplicationServer
}

// NewMockUnsafeApplicationServer creates a new mock instance.
func NewMockUnsafeApplicationServer(ctrl *gomock.Controller) *MockUnsafeApplicationServer {
	mock := &MockUnsafeApplicationServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeApplicationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeApplicationServer) EXPECT() *MockUnsafeApplicationServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedApplicationServer mocks base method.
func (m *MockUnsafeApplicationServer) mustEmbedUnimplementedApplicationServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedApplicationServer")
}

// mustEmbedUnimplementedApplicationServer indicates an expected call of mustEmbedUnimplementedApplicationServer.
func (mr *MockUnsafeApplicationServerMockRecorder) mustEmbedUnimplementedApplicationServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedApplicationServer", reflect.TypeOf((*MockUnsafeApplicationServer)(nil).mustEmbedUnimplementedApplicationServer))
}
