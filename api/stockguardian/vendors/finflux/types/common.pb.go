// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/stockguardian/vendors/finflux/types/common.proto

package types

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Generic struct used for enums in FinFlux
// Example JSONs:
//
//	{
//	    "id": 2,
//	    "code": "periodFrequencyType.months",
//	    "value": "MONTHS"
//	}
//
//	{
//	  "id": 1,
//	  "code": "legalFormType.person",
//	  "value": "PERSON"
//	}
//
//	{
//	  "id": 300,
//	  "code": "clientStatusType.active",
//	  "value": "Active"
//	}
type Enum struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Code  string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	Value string `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Enum) Reset() {
	*x = Enum{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Enum) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Enum) ProtoMessage() {}

func (x *Enum) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Enum.ProtoReflect.Descriptor instead.
func (*Enum) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_finflux_types_common_proto_rawDescGZIP(), []int{0}
}

func (x *Enum) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Enum) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Enum) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// Generic struct used for various fields like gender, education, constitution, etc.
// Example JSON:
//
//	{
//	  "id": 115,
//	  "name": "Graduation",
//	  "isActive": false,
//	  "mandatory": false
//	}
//
// TODO: rename?
type Option struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IsActive  bool   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	Mandatory bool   `protobuf:"varint,4,opt,name=mandatory,proto3" json:"mandatory,omitempty"`
}

func (x *Option) Reset() {
	*x = Option{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Option) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Option) ProtoMessage() {}

func (x *Option) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Option.ProtoReflect.Descriptor instead.
func (*Option) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_finflux_types_common_proto_rawDescGZIP(), []int{1}
}

func (x *Option) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Option) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Option) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *Option) GetMandatory() bool {
	if x != nil {
		return x.Mandatory
	}
	return false
}

// Example JSON:
//
//	{
//	    "code": "INR",
//	    "name": "Indian Rupee",
//	    "decimalPlaces": 2,
//	    "inMultiplesOf": 0,
//	    "displaySymbol": "₹",
//	    "nameCode": "currency.INR",
//	    "displayLabel": "Indian Rupee (₹)"
//	}
type Currency struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code          string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	DecimalPlaces int32  `protobuf:"varint,3,opt,name=decimal_places,json=decimalPlaces,proto3" json:"decimal_places,omitempty"`
	InMultiplesOf int32  `protobuf:"varint,4,opt,name=in_multiples_of,json=inMultiplesOf,proto3" json:"in_multiples_of,omitempty"`
	DisplaySymbol string `protobuf:"bytes,5,opt,name=display_symbol,json=displaySymbol,proto3" json:"display_symbol,omitempty"`
	NameCode      string `protobuf:"bytes,6,opt,name=name_code,json=nameCode,proto3" json:"name_code,omitempty"`
	DisplayLabel  string `protobuf:"bytes,7,opt,name=display_label,json=displayLabel,proto3" json:"display_label,omitempty"`
}

func (x *Currency) Reset() {
	*x = Currency{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Currency) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Currency) ProtoMessage() {}

func (x *Currency) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Currency.ProtoReflect.Descriptor instead.
func (*Currency) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_finflux_types_common_proto_rawDescGZIP(), []int{2}
}

func (x *Currency) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Currency) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Currency) GetDecimalPlaces() int32 {
	if x != nil {
		return x.DecimalPlaces
	}
	return 0
}

func (x *Currency) GetInMultiplesOf() int32 {
	if x != nil {
		return x.InMultiplesOf
	}
	return 0
}

func (x *Currency) GetDisplaySymbol() string {
	if x != nil {
		return x.DisplaySymbol
	}
	return ""
}

func (x *Currency) GetNameCode() string {
	if x != nil {
		return x.NameCode
	}
	return ""
}

func (x *Currency) GetDisplayLabel() string {
	if x != nil {
		return x.DisplayLabel
	}
	return ""
}

// Example JSON:
//
//	{
//	  "amount": 4.72,
//	  "chargeId": 1,
//	  "canAddChargeToPrincipalForComputation": false,
//	  "canLendCharge": false
//	}
type Charge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amount float64 `protobuf:"fixed64,1,opt,name=amount,proto3" json:"amount,omitempty"`
	// changes based on environment
	ChargeId                              int32 `protobuf:"varint,2,opt,name=charge_id,json=chargeId,proto3" json:"charge_id,omitempty"`
	CanAddChargeToPrincipalForComputation bool  `protobuf:"varint,3,opt,name=can_add_charge_to_principal_for_computation,json=canAddChargeToPrincipalForComputation,proto3" json:"can_add_charge_to_principal_for_computation,omitempty"`
	CanLendCharge                         bool  `protobuf:"varint,4,opt,name=can_lend_charge,json=canLendCharge,proto3" json:"can_lend_charge,omitempty"`
}

func (x *Charge) Reset() {
	*x = Charge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Charge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Charge) ProtoMessage() {}

func (x *Charge) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Charge.ProtoReflect.Descriptor instead.
func (*Charge) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_finflux_types_common_proto_rawDescGZIP(), []int{3}
}

func (x *Charge) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Charge) GetChargeId() int32 {
	if x != nil {
		return x.ChargeId
	}
	return 0
}

func (x *Charge) GetCanAddChargeToPrincipalForComputation() bool {
	if x != nil {
		return x.CanAddChargeToPrincipalForComputation
	}
	return false
}

func (x *Charge) GetCanLendCharge() bool {
	if x != nil {
		return x.CanLendCharge
	}
	return false
}

// Example JSON:
//
//	{
//	  "productChargeId": 24,
//	  "amount": 1.18
//	}
type OverdueCharge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// charge id which can be configured in the FinFlux system
	ProductChargeId int32   `protobuf:"varint,1,opt,name=product_charge_id,json=productChargeId,proto3" json:"product_charge_id,omitempty"`
	Amount          float64 `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *OverdueCharge) Reset() {
	*x = OverdueCharge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OverdueCharge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OverdueCharge) ProtoMessage() {}

func (x *OverdueCharge) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OverdueCharge.ProtoReflect.Descriptor instead.
func (*OverdueCharge) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_finflux_types_common_proto_rawDescGZIP(), []int{4}
}

func (x *OverdueCharge) GetProductChargeId() int32 {
	if x != nil {
		return x.ProductChargeId
	}
	return 0
}

func (x *OverdueCharge) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

// Example JSON:
//
//	{
//	 "developerMessage": "The request caused a data integrity issue to be fired by the database.",
//	 "httpStatusCode": "403",
//	 "defaultUserMessage": "Loan with externalId `123456` already exists",
//	 "userMessageGlobalisationCode": "error.msg.loan.duplicate.externalId",
//	 "errors": [
//	   {
//	     "developerMessage": "Loan with externalId `123456` already exists",
//	     "defaultUserMessage": "Loan with externalId `123456` already exists",
//	     "userMessageGlobalisationCode": "error.msg.loan.duplicate.externalId",
//	     "parameterName": "externalId",
//	     "args": [
//	       {
//	         "value": "123456"
//	       }
//	     ]
//	   }
//	 ],
//	 "errorCode": "G001"
//	}
type CommonErrorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// e.g.: "The request caused a data integrity issue to be fired by the database."
	DeveloperMessage string `protobuf:"bytes,1,opt,name=developer_message,json=developerMessage,proto3" json:"developer_message,omitempty"`
	// e.g.: "403"
	HttpStatusCode string `protobuf:"bytes,2,opt,name=http_status_code,json=httpStatusCode,proto3" json:"http_status_code,omitempty"`
	// e.g.: "Loan with externalId `123456` already exists"
	DefaultUserMessage string `protobuf:"bytes,3,opt,name=default_user_message,json=defaultUserMessage,proto3" json:"default_user_message,omitempty"`
	// e.g.: "error.msg.loan.duplicate.externalId"
	UserMessageGlobalisationCode string `protobuf:"bytes,4,opt,name=user_message_globalisation_code,json=userMessageGlobalisationCode,proto3" json:"user_message_globalisation_code,omitempty"`
	// e.g.: "G001"
	ErrorCode string                       `protobuf:"bytes,6,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	Errors    []*CommonErrorResponse_Error `protobuf:"bytes,7,rep,name=errors,proto3" json:"errors,omitempty"`
}

func (x *CommonErrorResponse) Reset() {
	*x = CommonErrorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonErrorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonErrorResponse) ProtoMessage() {}

func (x *CommonErrorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonErrorResponse.ProtoReflect.Descriptor instead.
func (*CommonErrorResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_finflux_types_common_proto_rawDescGZIP(), []int{5}
}

func (x *CommonErrorResponse) GetDeveloperMessage() string {
	if x != nil {
		return x.DeveloperMessage
	}
	return ""
}

func (x *CommonErrorResponse) GetHttpStatusCode() string {
	if x != nil {
		return x.HttpStatusCode
	}
	return ""
}

func (x *CommonErrorResponse) GetDefaultUserMessage() string {
	if x != nil {
		return x.DefaultUserMessage
	}
	return ""
}

func (x *CommonErrorResponse) GetUserMessageGlobalisationCode() string {
	if x != nil {
		return x.UserMessageGlobalisationCode
	}
	return ""
}

func (x *CommonErrorResponse) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *CommonErrorResponse) GetErrors() []*CommonErrorResponse_Error {
	if x != nil {
		return x.Errors
	}
	return nil
}

type CommonErrorResponse_Error struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeveloperMessage             string `protobuf:"bytes,1,opt,name=developer_message,json=developerMessage,proto3" json:"developer_message,omitempty"`
	DefaultUserMessage           string `protobuf:"bytes,2,opt,name=default_user_message,json=defaultUserMessage,proto3" json:"default_user_message,omitempty"`
	UserMessageGlobalisationCode string `protobuf:"bytes,3,opt,name=user_message_globalisation_code,json=userMessageGlobalisationCode,proto3" json:"user_message_globalisation_code,omitempty"`
	ParameterName                string `protobuf:"bytes,4,opt,name=parameter_name,json=parameterName,proto3" json:"parameter_name,omitempty"`
}

func (x *CommonErrorResponse_Error) Reset() {
	*x = CommonErrorResponse_Error{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonErrorResponse_Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonErrorResponse_Error) ProtoMessage() {}

func (x *CommonErrorResponse_Error) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonErrorResponse_Error.ProtoReflect.Descriptor instead.
func (*CommonErrorResponse_Error) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_finflux_types_common_proto_rawDescGZIP(), []int{5, 0}
}

func (x *CommonErrorResponse_Error) GetDeveloperMessage() string {
	if x != nil {
		return x.DeveloperMessage
	}
	return ""
}

func (x *CommonErrorResponse_Error) GetDefaultUserMessage() string {
	if x != nil {
		return x.DefaultUserMessage
	}
	return ""
}

func (x *CommonErrorResponse_Error) GetUserMessageGlobalisationCode() string {
	if x != nil {
		return x.UserMessageGlobalisationCode
	}
	return ""
}

func (x *CommonErrorResponse_Error) GetParameterName() string {
	if x != nil {
		return x.ParameterName
	}
	return ""
}

var File_api_stockguardian_vendors_finflux_types_common_proto protoreflect.FileDescriptor

var file_api_stockguardian_vendors_finflux_types_common_proto_rawDesc = []byte{
	0x0a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x69, 0x6e, 0x66,
	0x6c, 0x75, 0x78, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x23, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61,
	0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x69,
	0x6e, 0x66, 0x6c, 0x75, 0x78, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0x40, 0x0a, 0x04, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x67, 0x0a,
	0x06, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69,
	0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x22, 0xea, 0x01, 0x0a, 0x08, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x64,
	0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x50, 0x6c, 0x61, 0x63,
	0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x6e, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c,
	0x65, 0x73, 0x5f, 0x6f, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x69, 0x6e, 0x4d,
	0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x73, 0x4f, 0x66, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x53, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x22, 0xc1, 0x01, 0x0a, 0x06, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x2b, 0x63, 0x61, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70,
	0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x25, 0x63, 0x61, 0x6e, 0x41, 0x64, 0x64,
	0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x6f, 0x50, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61,
	0x6c, 0x46, 0x6f, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x26, 0x0a, 0x0f, 0x63, 0x61, 0x6e, 0x5f, 0x6c, 0x65, 0x6e, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x63, 0x61, 0x6e, 0x4c, 0x65, 0x6e,
	0x64, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x22, 0x53, 0x0a, 0x0d, 0x4f, 0x76, 0x65, 0x72, 0x64,
	0x75, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb3, 0x04, 0x0a,
	0x13, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x28, 0x0a, 0x10, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x68, 0x74, 0x74,
	0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x45, 0x0a,
	0x1f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x67, 0x6c,
	0x6f, 0x62, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x75, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x56, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x66,
	0x6c, 0x75, 0x78, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x1a, 0xd4, 0x01, 0x0a, 0x05,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2b, 0x0a, 0x11, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x45, 0x0a, 0x1f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x5f, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x75,
	0x73, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c,
	0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x42, 0x86, 0x01, 0x0a, 0x41, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x72, 0x69, 0x6e, 0x67, 0x6f, 0x74, 0x74,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69,
	0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x66, 0x6c,
	0x75, 0x78, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x5a, 0x41, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x72, 0x69, 0x6e, 0x67,
	0x6f, 0x74, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61,
	0x72, 0x64, 0x69, 0x61, 0x6e, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x69,
	0x6e, 0x66, 0x6c, 0x75, 0x78, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_stockguardian_vendors_finflux_types_common_proto_rawDescOnce sync.Once
	file_api_stockguardian_vendors_finflux_types_common_proto_rawDescData = file_api_stockguardian_vendors_finflux_types_common_proto_rawDesc
)

func file_api_stockguardian_vendors_finflux_types_common_proto_rawDescGZIP() []byte {
	file_api_stockguardian_vendors_finflux_types_common_proto_rawDescOnce.Do(func() {
		file_api_stockguardian_vendors_finflux_types_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_stockguardian_vendors_finflux_types_common_proto_rawDescData)
	})
	return file_api_stockguardian_vendors_finflux_types_common_proto_rawDescData
}

var file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_stockguardian_vendors_finflux_types_common_proto_goTypes = []interface{}{
	(*Enum)(nil),                      // 0: stockguardian.vendors.finflux.types.Enum
	(*Option)(nil),                    // 1: stockguardian.vendors.finflux.types.Option
	(*Currency)(nil),                  // 2: stockguardian.vendors.finflux.types.Currency
	(*Charge)(nil),                    // 3: stockguardian.vendors.finflux.types.Charge
	(*OverdueCharge)(nil),             // 4: stockguardian.vendors.finflux.types.OverdueCharge
	(*CommonErrorResponse)(nil),       // 5: stockguardian.vendors.finflux.types.CommonErrorResponse
	(*CommonErrorResponse_Error)(nil), // 6: stockguardian.vendors.finflux.types.CommonErrorResponse.Error
}
var file_api_stockguardian_vendors_finflux_types_common_proto_depIdxs = []int32{
	6, // 0: stockguardian.vendors.finflux.types.CommonErrorResponse.errors:type_name -> stockguardian.vendors.finflux.types.CommonErrorResponse.Error
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_stockguardian_vendors_finflux_types_common_proto_init() }
func file_api_stockguardian_vendors_finflux_types_common_proto_init() {
	if File_api_stockguardian_vendors_finflux_types_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Enum); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Option); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Currency); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Charge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OverdueCharge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonErrorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonErrorResponse_Error); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_stockguardian_vendors_finflux_types_common_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_stockguardian_vendors_finflux_types_common_proto_goTypes,
		DependencyIndexes: file_api_stockguardian_vendors_finflux_types_common_proto_depIdxs,
		MessageInfos:      file_api_stockguardian_vendors_finflux_types_common_proto_msgTypes,
	}.Build()
	File_api_stockguardian_vendors_finflux_types_common_proto = out.File
	file_api_stockguardian_vendors_finflux_types_common_proto_rawDesc = nil
	file_api_stockguardian_vendors_finflux_types_common_proto_goTypes = nil
	file_api_stockguardian_vendors_finflux_types_common_proto_depIdxs = nil
}
