// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/stockguardian/sgvendorgateway/lms/finflux/types/loan.proto

package types

import (
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LoanSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrincipalDisbursed                 *money.Money `protobuf:"bytes,1,opt,name=principal_disbursed,json=principalDisbursed,proto3" json:"principal_disbursed,omitempty"`
	PrincipalPaid                      *money.Money `protobuf:"bytes,2,opt,name=principal_paid,json=principalPaid,proto3" json:"principal_paid,omitempty"`
	PrincipalWrittenOff                *money.Money `protobuf:"bytes,3,opt,name=principal_written_off,json=principalWrittenOff,proto3" json:"principal_written_off,omitempty"`
	PrincipalOutstanding               *money.Money `protobuf:"bytes,4,opt,name=principal_outstanding,json=principalOutstanding,proto3" json:"principal_outstanding,omitempty"`
	PrincipalFromIncomePosting         *money.Money `protobuf:"bytes,5,opt,name=principal_from_income_posting,json=principalFromIncomePosting,proto3" json:"principal_from_income_posting,omitempty"`
	PrincipalOverdue                   *money.Money `protobuf:"bytes,6,opt,name=principal_overdue,json=principalOverdue,proto3" json:"principal_overdue,omitempty"`
	PrincipalNetDisbursed              *money.Money `protobuf:"bytes,7,opt,name=principal_net_disbursed,json=principalNetDisbursed,proto3" json:"principal_net_disbursed,omitempty"`
	InterestCharged                    *money.Money `protobuf:"bytes,8,opt,name=interest_charged,json=interestCharged,proto3" json:"interest_charged,omitempty"`
	InterestPaid                       *money.Money `protobuf:"bytes,9,opt,name=interest_paid,json=interestPaid,proto3" json:"interest_paid,omitempty"`
	InterestWaived                     *money.Money `protobuf:"bytes,10,opt,name=interest_waived,json=interestWaived,proto3" json:"interest_waived,omitempty"`
	InterestWrittenOff                 *money.Money `protobuf:"bytes,11,opt,name=interest_written_off,json=interestWrittenOff,proto3" json:"interest_written_off,omitempty"`
	InterestOutstanding                *money.Money `protobuf:"bytes,12,opt,name=interest_outstanding,json=interestOutstanding,proto3" json:"interest_outstanding,omitempty"`
	InterestOverdue                    *money.Money `protobuf:"bytes,13,opt,name=interest_overdue,json=interestOverdue,proto3" json:"interest_overdue,omitempty"`
	FeeChargesCharged                  *money.Money `protobuf:"bytes,14,opt,name=fee_charges_charged,json=feeChargesCharged,proto3" json:"fee_charges_charged,omitempty"`
	FeeChargesDueAtDisbursementCharged *money.Money `protobuf:"bytes,15,opt,name=fee_charges_due_at_disbursement_charged,json=feeChargesDueAtDisbursementCharged,proto3" json:"fee_charges_due_at_disbursement_charged,omitempty"`
	FeeChargesPaid                     *money.Money `protobuf:"bytes,16,opt,name=fee_charges_paid,json=feeChargesPaid,proto3" json:"fee_charges_paid,omitempty"`
	FeeChargesWaived                   *money.Money `protobuf:"bytes,17,opt,name=fee_charges_waived,json=feeChargesWaived,proto3" json:"fee_charges_waived,omitempty"`
	FeeChargesWrittenOff               *money.Money `protobuf:"bytes,18,opt,name=fee_charges_written_off,json=feeChargesWrittenOff,proto3" json:"fee_charges_written_off,omitempty"`
	FeeChargesOutstanding              *money.Money `protobuf:"bytes,19,opt,name=fee_charges_outstanding,json=feeChargesOutstanding,proto3" json:"fee_charges_outstanding,omitempty"`
	FeeChargesOverdue                  *money.Money `protobuf:"bytes,20,opt,name=fee_charges_overdue,json=feeChargesOverdue,proto3" json:"fee_charges_overdue,omitempty"`
	PenaltyChargesCharged              *money.Money `protobuf:"bytes,21,opt,name=penalty_charges_charged,json=penaltyChargesCharged,proto3" json:"penalty_charges_charged,omitempty"`
	PenaltyChargesPaid                 *money.Money `protobuf:"bytes,22,opt,name=penalty_charges_paid,json=penaltyChargesPaid,proto3" json:"penalty_charges_paid,omitempty"`
	PenaltyChargesWaived               *money.Money `protobuf:"bytes,23,opt,name=penalty_charges_waived,json=penaltyChargesWaived,proto3" json:"penalty_charges_waived,omitempty"`
	PenaltyChargesWrittenOff           *money.Money `protobuf:"bytes,24,opt,name=penalty_charges_written_off,json=penaltyChargesWrittenOff,proto3" json:"penalty_charges_written_off,omitempty"`
	PenaltyChargesOutstanding          *money.Money `protobuf:"bytes,25,opt,name=penalty_charges_outstanding,json=penaltyChargesOutstanding,proto3" json:"penalty_charges_outstanding,omitempty"`
	PenaltyChargesOverdue              *money.Money `protobuf:"bytes,26,opt,name=penalty_charges_overdue,json=penaltyChargesOverdue,proto3" json:"penalty_charges_overdue,omitempty"`
	TotalExpectedRepayment             *money.Money `protobuf:"bytes,27,opt,name=total_expected_repayment,json=totalExpectedRepayment,proto3" json:"total_expected_repayment,omitempty"`
	TotalRepayment                     *money.Money `protobuf:"bytes,28,opt,name=total_repayment,json=totalRepayment,proto3" json:"total_repayment,omitempty"`
	TotalExpectedCostOfLoan            *money.Money `protobuf:"bytes,29,opt,name=total_expected_cost_of_loan,json=totalExpectedCostOfLoan,proto3" json:"total_expected_cost_of_loan,omitempty"`
	TotalCostOfLoan                    *money.Money `protobuf:"bytes,30,opt,name=total_cost_of_loan,json=totalCostOfLoan,proto3" json:"total_cost_of_loan,omitempty"`
	TotalWaived                        *money.Money `protobuf:"bytes,31,opt,name=total_waived,json=totalWaived,proto3" json:"total_waived,omitempty"`
	TotalWrittenOff                    *money.Money `protobuf:"bytes,32,opt,name=total_written_off,json=totalWrittenOff,proto3" json:"total_written_off,omitempty"`
	TotalOutstanding                   *money.Money `protobuf:"bytes,33,opt,name=total_outstanding,json=totalOutstanding,proto3" json:"total_outstanding,omitempty"`
	TotalOverdue                       *money.Money `protobuf:"bytes,34,opt,name=total_overdue,json=totalOverdue,proto3" json:"total_overdue,omitempty"`
	ExcessAmountPaid                   *money.Money `protobuf:"bytes,35,opt,name=excess_amount_paid,json=excessAmountPaid,proto3" json:"excess_amount_paid,omitempty"`
	AvailableBalance                   *money.Money `protobuf:"bytes,36,opt,name=available_balance,json=availableBalance,proto3" json:"available_balance,omitempty"`
	UpfrontInterestAvailable           *money.Money `protobuf:"bytes,37,opt,name=upfront_interest_available,json=upfrontInterestAvailable,proto3" json:"upfront_interest_available,omitempty"`
	RebateApplied                      *money.Money `protobuf:"bytes,38,opt,name=rebate_applied,json=rebateApplied,proto3" json:"rebate_applied,omitempty"`
	TotalAdvanceEmiAmount              *money.Money `protobuf:"bytes,39,opt,name=total_advance_emi_amount,json=totalAdvanceEmiAmount,proto3" json:"total_advance_emi_amount,omitempty"`
}

func (x *LoanSummary) Reset() {
	*x = LoanSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanSummary) ProtoMessage() {}

func (x *LoanSummary) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanSummary.ProtoReflect.Descriptor instead.
func (*LoanSummary) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_rawDescGZIP(), []int{0}
}

func (x *LoanSummary) GetPrincipalDisbursed() *money.Money {
	if x != nil {
		return x.PrincipalDisbursed
	}
	return nil
}

func (x *LoanSummary) GetPrincipalPaid() *money.Money {
	if x != nil {
		return x.PrincipalPaid
	}
	return nil
}

func (x *LoanSummary) GetPrincipalWrittenOff() *money.Money {
	if x != nil {
		return x.PrincipalWrittenOff
	}
	return nil
}

func (x *LoanSummary) GetPrincipalOutstanding() *money.Money {
	if x != nil {
		return x.PrincipalOutstanding
	}
	return nil
}

func (x *LoanSummary) GetPrincipalFromIncomePosting() *money.Money {
	if x != nil {
		return x.PrincipalFromIncomePosting
	}
	return nil
}

func (x *LoanSummary) GetPrincipalOverdue() *money.Money {
	if x != nil {
		return x.PrincipalOverdue
	}
	return nil
}

func (x *LoanSummary) GetPrincipalNetDisbursed() *money.Money {
	if x != nil {
		return x.PrincipalNetDisbursed
	}
	return nil
}

func (x *LoanSummary) GetInterestCharged() *money.Money {
	if x != nil {
		return x.InterestCharged
	}
	return nil
}

func (x *LoanSummary) GetInterestPaid() *money.Money {
	if x != nil {
		return x.InterestPaid
	}
	return nil
}

func (x *LoanSummary) GetInterestWaived() *money.Money {
	if x != nil {
		return x.InterestWaived
	}
	return nil
}

func (x *LoanSummary) GetInterestWrittenOff() *money.Money {
	if x != nil {
		return x.InterestWrittenOff
	}
	return nil
}

func (x *LoanSummary) GetInterestOutstanding() *money.Money {
	if x != nil {
		return x.InterestOutstanding
	}
	return nil
}

func (x *LoanSummary) GetInterestOverdue() *money.Money {
	if x != nil {
		return x.InterestOverdue
	}
	return nil
}

func (x *LoanSummary) GetFeeChargesCharged() *money.Money {
	if x != nil {
		return x.FeeChargesCharged
	}
	return nil
}

func (x *LoanSummary) GetFeeChargesDueAtDisbursementCharged() *money.Money {
	if x != nil {
		return x.FeeChargesDueAtDisbursementCharged
	}
	return nil
}

func (x *LoanSummary) GetFeeChargesPaid() *money.Money {
	if x != nil {
		return x.FeeChargesPaid
	}
	return nil
}

func (x *LoanSummary) GetFeeChargesWaived() *money.Money {
	if x != nil {
		return x.FeeChargesWaived
	}
	return nil
}

func (x *LoanSummary) GetFeeChargesWrittenOff() *money.Money {
	if x != nil {
		return x.FeeChargesWrittenOff
	}
	return nil
}

func (x *LoanSummary) GetFeeChargesOutstanding() *money.Money {
	if x != nil {
		return x.FeeChargesOutstanding
	}
	return nil
}

func (x *LoanSummary) GetFeeChargesOverdue() *money.Money {
	if x != nil {
		return x.FeeChargesOverdue
	}
	return nil
}

func (x *LoanSummary) GetPenaltyChargesCharged() *money.Money {
	if x != nil {
		return x.PenaltyChargesCharged
	}
	return nil
}

func (x *LoanSummary) GetPenaltyChargesPaid() *money.Money {
	if x != nil {
		return x.PenaltyChargesPaid
	}
	return nil
}

func (x *LoanSummary) GetPenaltyChargesWaived() *money.Money {
	if x != nil {
		return x.PenaltyChargesWaived
	}
	return nil
}

func (x *LoanSummary) GetPenaltyChargesWrittenOff() *money.Money {
	if x != nil {
		return x.PenaltyChargesWrittenOff
	}
	return nil
}

func (x *LoanSummary) GetPenaltyChargesOutstanding() *money.Money {
	if x != nil {
		return x.PenaltyChargesOutstanding
	}
	return nil
}

func (x *LoanSummary) GetPenaltyChargesOverdue() *money.Money {
	if x != nil {
		return x.PenaltyChargesOverdue
	}
	return nil
}

func (x *LoanSummary) GetTotalExpectedRepayment() *money.Money {
	if x != nil {
		return x.TotalExpectedRepayment
	}
	return nil
}

func (x *LoanSummary) GetTotalRepayment() *money.Money {
	if x != nil {
		return x.TotalRepayment
	}
	return nil
}

func (x *LoanSummary) GetTotalExpectedCostOfLoan() *money.Money {
	if x != nil {
		return x.TotalExpectedCostOfLoan
	}
	return nil
}

func (x *LoanSummary) GetTotalCostOfLoan() *money.Money {
	if x != nil {
		return x.TotalCostOfLoan
	}
	return nil
}

func (x *LoanSummary) GetTotalWaived() *money.Money {
	if x != nil {
		return x.TotalWaived
	}
	return nil
}

func (x *LoanSummary) GetTotalWrittenOff() *money.Money {
	if x != nil {
		return x.TotalWrittenOff
	}
	return nil
}

func (x *LoanSummary) GetTotalOutstanding() *money.Money {
	if x != nil {
		return x.TotalOutstanding
	}
	return nil
}

func (x *LoanSummary) GetTotalOverdue() *money.Money {
	if x != nil {
		return x.TotalOverdue
	}
	return nil
}

func (x *LoanSummary) GetExcessAmountPaid() *money.Money {
	if x != nil {
		return x.ExcessAmountPaid
	}
	return nil
}

func (x *LoanSummary) GetAvailableBalance() *money.Money {
	if x != nil {
		return x.AvailableBalance
	}
	return nil
}

func (x *LoanSummary) GetUpfrontInterestAvailable() *money.Money {
	if x != nil {
		return x.UpfrontInterestAvailable
	}
	return nil
}

func (x *LoanSummary) GetRebateApplied() *money.Money {
	if x != nil {
		return x.RebateApplied
	}
	return nil
}

func (x *LoanSummary) GetTotalAdvanceEmiAmount() *money.Money {
	if x != nil {
		return x.TotalAdvanceEmiAmount
	}
	return nil
}

type LoanTimeline struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubmittedOn              *date.Date `protobuf:"bytes,1,opt,name=submitted_on,json=submittedOn,proto3" json:"submitted_on,omitempty"`
	ApprovedOn               *date.Date `protobuf:"bytes,2,opt,name=approved_on,json=approvedOn,proto3" json:"approved_on,omitempty"`
	ExpectedDisbursementDate *date.Date `protobuf:"bytes,3,opt,name=expected_disbursement_date,json=expectedDisbursementDate,proto3" json:"expected_disbursement_date,omitempty"`
	// actual disbursed date
	DisbursedOn          *date.Date `protobuf:"bytes,4,opt,name=disbursed_on,json=disbursedOn,proto3" json:"disbursed_on,omitempty"`
	ExpectedMaturityDate *date.Date `protobuf:"bytes,5,opt,name=expected_maturity_date,json=expectedMaturityDate,proto3" json:"expected_maturity_date,omitempty"`
}

func (x *LoanTimeline) Reset() {
	*x = LoanTimeline{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanTimeline) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanTimeline) ProtoMessage() {}

func (x *LoanTimeline) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanTimeline.ProtoReflect.Descriptor instead.
func (*LoanTimeline) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_rawDescGZIP(), []int{1}
}

func (x *LoanTimeline) GetSubmittedOn() *date.Date {
	if x != nil {
		return x.SubmittedOn
	}
	return nil
}

func (x *LoanTimeline) GetApprovedOn() *date.Date {
	if x != nil {
		return x.ApprovedOn
	}
	return nil
}

func (x *LoanTimeline) GetExpectedDisbursementDate() *date.Date {
	if x != nil {
		return x.ExpectedDisbursementDate
	}
	return nil
}

func (x *LoanTimeline) GetDisbursedOn() *date.Date {
	if x != nil {
		return x.DisbursedOn
	}
	return nil
}

func (x *LoanTimeline) GetExpectedMaturityDate() *date.Date {
	if x != nil {
		return x.ExpectedMaturityDate
	}
	return nil
}

type LoanDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier of the loan in Finflux
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// e.g. "*********"
	AccountNumber string     `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	Status        LoanStatus `protobuf:"varint,3,opt,name=status,proto3,enum=stockguardian.sgvendorgateway.lms.finflux.types.LoanStatus" json:"status,omitempty"`
	// unique identifier of the customer in Finflux
	ClientId                string       `protobuf:"bytes,4,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	PrincipalAmount         *money.Money `protobuf:"bytes,5,opt,name=principal_amount,json=principalAmount,proto3" json:"principal_amount,omitempty"`
	ApprovedPrincipalAmount *money.Money `protobuf:"bytes,6,opt,name=approved_principal_amount,json=approvedPrincipalAmount,proto3" json:"approved_principal_amount,omitempty"`
	ProposedPrincipalAmount *money.Money `protobuf:"bytes,7,opt,name=proposed_principal_amount,json=proposedPrincipalAmount,proto3" json:"proposed_principal_amount,omitempty"`
	AmountPaidInAdvance     *money.Money `protobuf:"bytes,8,opt,name=amount_paid_in_advance,json=amountPaidInAdvance,proto3" json:"amount_paid_in_advance,omitempty"`
	BrokenPeriodInterest    *money.Money `protobuf:"bytes,9,opt,name=broken_period_interest,json=brokenPeriodInterest,proto3" json:"broken_period_interest,omitempty"`
	// e.g. 12
	TermFrequency int32 `protobuf:"varint,10,opt,name=term_frequency,json=termFrequency,proto3" json:"term_frequency,omitempty"`
	// e.g.: "month"
	TermFrequencyType FrequencyType `protobuf:"varint,11,opt,name=term_frequency_type,json=termFrequencyType,proto3,enum=stockguardian.sgvendorgateway.lms.finflux.types.FrequencyType" json:"term_frequency_type,omitempty"`
	// e.g. 12
	NumberOfRepayments int32 `protobuf:"varint,12,opt,name=number_of_repayments,json=numberOfRepayments,proto3" json:"number_of_repayments,omitempty"`
	// e.g. 1
	RepaymentEvery         int32         `protobuf:"varint,13,opt,name=repayment_every,json=repaymentEvery,proto3" json:"repayment_every,omitempty"`
	RepaymentFrequencyType FrequencyType `protobuf:"varint,14,opt,name=repayment_frequency_type,json=repaymentFrequencyType,proto3,enum=stockguardian.sgvendorgateway.lms.finflux.types.FrequencyType" json:"repayment_frequency_type,omitempty"`
	// e.g. 20
	InterestRatePerPeriod     float64       `protobuf:"fixed64,15,opt,name=interest_rate_per_period,json=interestRatePerPeriod,proto3" json:"interest_rate_per_period,omitempty"`
	InterestRateFrequencyType FrequencyType `protobuf:"varint,16,opt,name=interest_rate_frequency_type,json=interestRateFrequencyType,proto3,enum=stockguardian.sgvendorgateway.lms.finflux.types.FrequencyType" json:"interest_rate_frequency_type,omitempty"`
	// e.g. 20
	AnnualInterestRate     float64 `protobuf:"fixed64,17,opt,name=annual_interest_rate,json=annualInterestRate,proto3" json:"annual_interest_rate,omitempty"`
	NumberOfPaidRepayments int32   `protobuf:"varint,18,opt,name=number_of_paid_repayments,json=numberOfPaidRepayments,proto3" json:"number_of_paid_repayments,omitempty"`
	NumberOfDueRepayments  int32   `protobuf:"varint,19,opt,name=number_of_due_repayments,json=numberOfDueRepayments,proto3" json:"number_of_due_repayments,omitempty"`
	// declining balance or flat
	InterestType InterestType `protobuf:"varint,20,opt,name=interest_type,json=interestType,proto3,enum=stockguardian.sgvendorgateway.lms.finflux.types.InterestType" json:"interest_type,omitempty"`
	// e.g. 14
	MinInterestRatePerPeriod float64 `protobuf:"fixed64,21,opt,name=min_interest_rate_per_period,json=minInterestRatePerPeriod,proto3" json:"min_interest_rate_per_period,omitempty"`
	// e.g. 30
	MaxInterestRatePerPeriod float64 `protobuf:"fixed64,22,opt,name=max_interest_rate_per_period,json=maxInterestRatePerPeriod,proto3" json:"max_interest_rate_per_period,omitempty"`
	// e.g. 20
	CurrentInterestRate      float64       `protobuf:"fixed64,23,opt,name=current_interest_rate,json=currentInterestRate,proto3" json:"current_interest_rate,omitempty"`
	IsCancellationAllowed    bool          `protobuf:"varint,24,opt,name=is_cancellation_allowed,json=isCancellationAllowed,proto3" json:"is_cancellation_allowed,omitempty"`
	Timeline                 *LoanTimeline `protobuf:"bytes,25,opt,name=timeline,proto3" json:"timeline,omitempty"`
	Summary                  *LoanSummary  `protobuf:"bytes,26,opt,name=summary,proto3" json:"summary,omitempty"`
	FeeChargedAtDisbursement *money.Money  `protobuf:"bytes,27,opt,name=fee_charged_at_disbursement,json=feeChargedAtDisbursement,proto3" json:"fee_charged_at_disbursement,omitempty"`
	InArrears                bool          `protobuf:"varint,28,opt,name=in_arrears,json=inArrears,proto3" json:"in_arrears,omitempty"`
	// is non-performing asset
	IsNpa bool `protobuf:"varint,29,opt,name=is_npa,json=isNpa,proto3" json:"is_npa,omitempty"`
	// e.g. 926
	CalculatedEmiAmount *money.Money `protobuf:"bytes,30,opt,name=calculated_emi_amount,json=calculatedEmiAmount,proto3" json:"calculated_emi_amount,omitempty"`
	IsFldg              bool         `protobuf:"varint,31,opt,name=is_fldg,json=isFldg,proto3" json:"is_fldg,omitempty"`
	// e.g. 12
	ActualNumberOfRepayments int32        `protobuf:"varint,32,opt,name=actual_number_of_repayments,json=actualNumberOfRepayments,proto3" json:"actual_number_of_repayments,omitempty"`
	TotalRepaymentExpected   *money.Money `protobuf:"bytes,33,opt,name=total_repayment_expected,json=totalRepaymentExpected,proto3" json:"total_repayment_expected,omitempty"`
}

func (x *LoanDetails) Reset() {
	*x = LoanDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanDetails) ProtoMessage() {}

func (x *LoanDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanDetails.ProtoReflect.Descriptor instead.
func (*LoanDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_rawDescGZIP(), []int{2}
}

func (x *LoanDetails) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LoanDetails) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *LoanDetails) GetStatus() LoanStatus {
	if x != nil {
		return x.Status
	}
	return LoanStatus_LOAN_STATUS_UNSPECIFIED
}

func (x *LoanDetails) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *LoanDetails) GetPrincipalAmount() *money.Money {
	if x != nil {
		return x.PrincipalAmount
	}
	return nil
}

func (x *LoanDetails) GetApprovedPrincipalAmount() *money.Money {
	if x != nil {
		return x.ApprovedPrincipalAmount
	}
	return nil
}

func (x *LoanDetails) GetProposedPrincipalAmount() *money.Money {
	if x != nil {
		return x.ProposedPrincipalAmount
	}
	return nil
}

func (x *LoanDetails) GetAmountPaidInAdvance() *money.Money {
	if x != nil {
		return x.AmountPaidInAdvance
	}
	return nil
}

func (x *LoanDetails) GetBrokenPeriodInterest() *money.Money {
	if x != nil {
		return x.BrokenPeriodInterest
	}
	return nil
}

func (x *LoanDetails) GetTermFrequency() int32 {
	if x != nil {
		return x.TermFrequency
	}
	return 0
}

func (x *LoanDetails) GetTermFrequencyType() FrequencyType {
	if x != nil {
		return x.TermFrequencyType
	}
	return FrequencyType_FREQUENCY_TYPE_UNSPECIFIED
}

func (x *LoanDetails) GetNumberOfRepayments() int32 {
	if x != nil {
		return x.NumberOfRepayments
	}
	return 0
}

func (x *LoanDetails) GetRepaymentEvery() int32 {
	if x != nil {
		return x.RepaymentEvery
	}
	return 0
}

func (x *LoanDetails) GetRepaymentFrequencyType() FrequencyType {
	if x != nil {
		return x.RepaymentFrequencyType
	}
	return FrequencyType_FREQUENCY_TYPE_UNSPECIFIED
}

func (x *LoanDetails) GetInterestRatePerPeriod() float64 {
	if x != nil {
		return x.InterestRatePerPeriod
	}
	return 0
}

func (x *LoanDetails) GetInterestRateFrequencyType() FrequencyType {
	if x != nil {
		return x.InterestRateFrequencyType
	}
	return FrequencyType_FREQUENCY_TYPE_UNSPECIFIED
}

func (x *LoanDetails) GetAnnualInterestRate() float64 {
	if x != nil {
		return x.AnnualInterestRate
	}
	return 0
}

func (x *LoanDetails) GetNumberOfPaidRepayments() int32 {
	if x != nil {
		return x.NumberOfPaidRepayments
	}
	return 0
}

func (x *LoanDetails) GetNumberOfDueRepayments() int32 {
	if x != nil {
		return x.NumberOfDueRepayments
	}
	return 0
}

func (x *LoanDetails) GetInterestType() InterestType {
	if x != nil {
		return x.InterestType
	}
	return InterestType_INTEREST_TYPE_UNSPECIFIED
}

func (x *LoanDetails) GetMinInterestRatePerPeriod() float64 {
	if x != nil {
		return x.MinInterestRatePerPeriod
	}
	return 0
}

func (x *LoanDetails) GetMaxInterestRatePerPeriod() float64 {
	if x != nil {
		return x.MaxInterestRatePerPeriod
	}
	return 0
}

func (x *LoanDetails) GetCurrentInterestRate() float64 {
	if x != nil {
		return x.CurrentInterestRate
	}
	return 0
}

func (x *LoanDetails) GetIsCancellationAllowed() bool {
	if x != nil {
		return x.IsCancellationAllowed
	}
	return false
}

func (x *LoanDetails) GetTimeline() *LoanTimeline {
	if x != nil {
		return x.Timeline
	}
	return nil
}

func (x *LoanDetails) GetSummary() *LoanSummary {
	if x != nil {
		return x.Summary
	}
	return nil
}

func (x *LoanDetails) GetFeeChargedAtDisbursement() *money.Money {
	if x != nil {
		return x.FeeChargedAtDisbursement
	}
	return nil
}

func (x *LoanDetails) GetInArrears() bool {
	if x != nil {
		return x.InArrears
	}
	return false
}

func (x *LoanDetails) GetIsNpa() bool {
	if x != nil {
		return x.IsNpa
	}
	return false
}

func (x *LoanDetails) GetCalculatedEmiAmount() *money.Money {
	if x != nil {
		return x.CalculatedEmiAmount
	}
	return nil
}

func (x *LoanDetails) GetIsFldg() bool {
	if x != nil {
		return x.IsFldg
	}
	return false
}

func (x *LoanDetails) GetActualNumberOfRepayments() int32 {
	if x != nil {
		return x.ActualNumberOfRepayments
	}
	return 0
}

func (x *LoanDetails) GetTotalRepaymentExpected() *money.Money {
	if x != nil {
		return x.TotalRepaymentExpected
	}
	return nil
}

type LoanRepaymentPeriod struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// incremental serial number of the repayment
	SerialNumber int32 `protobuf:"varint,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	// start date of the repayment period
	FromDate *date.Date `protobuf:"bytes,2,opt,name=from_date,json=fromDate,proto3" json:"from_date,omitempty"`
	// end date of the repayment period
	DueDate *date.Date `protobuf:"bytes,3,opt,name=due_date,json=dueDate,proto3" json:"due_date,omitempty"`
	// number of days in the repayment period
	DaysInPeriod            int32                   `protobuf:"varint,4,opt,name=days_in_period,json=daysInPeriod,proto3" json:"days_in_period,omitempty"`
	PrincipalComponent      *LoanComponentBreakdown `protobuf:"bytes,5,opt,name=principal_component,json=principalComponent,proto3" json:"principal_component,omitempty"`
	InterestComponent       *LoanComponentBreakdown `protobuf:"bytes,6,opt,name=interest_component,json=interestComponent,proto3" json:"interest_component,omitempty"`
	FeeChargesComponent     *LoanComponentBreakdown `protobuf:"bytes,7,opt,name=fee_charges_component,json=feeChargesComponent,proto3" json:"fee_charges_component,omitempty"`
	PenaltyChargesComponent *LoanComponentBreakdown `protobuf:"bytes,8,opt,name=penalty_charges_component,json=penaltyChargesComponent,proto3" json:"penalty_charges_component,omitempty"`
	// total of all components (principal, interest, fee, penalty)
	TotalComponent *LoanComponentBreakdown `protobuf:"bytes,9,opt,name=total_component,json=totalComponent,proto3" json:"total_component,omitempty"`
	// total principal outstanding before this period
	PrincipalBalanceOutstandingForLoan *money.Money `protobuf:"bytes,10,opt,name=principal_balance_outstanding_for_loan,json=principalBalanceOutstandingForLoan,proto3" json:"principal_balance_outstanding_for_loan,omitempty"`
	TotalActualCostOfLoanForPeriod     *money.Money `protobuf:"bytes,11,opt,name=total_actual_cost_of_loan_for_period,json=totalActualCostOfLoanForPeriod,proto3" json:"total_actual_cost_of_loan_for_period,omitempty"`
	TotalInstallmentAmountForPeriod    *money.Money `protobuf:"bytes,12,opt,name=total_installment_amount_for_period,json=totalInstallmentAmountForPeriod,proto3" json:"total_installment_amount_for_period,omitempty"`
	InterestAdjustedDueToGrace         *money.Money `protobuf:"bytes,13,opt,name=interest_adjusted_due_to_grace,json=interestAdjustedDueToGrace,proto3" json:"interest_adjusted_due_to_grace,omitempty"`
	InterestAccruable                  *money.Money `protobuf:"bytes,14,opt,name=interest_accruable,json=interestAccruable,proto3" json:"interest_accruable,omitempty"`
	// true if all the EMI components (P, I & C) are completely paid off
	IsComplete bool `protobuf:"varint,15,opt,name=is_complete,json=isComplete,proto3" json:"is_complete,omitempty"`
	// date when all the components of the emi (P, I & C) are cleared off
	ObligationsMetOn *date.Date `protobuf:"bytes,16,opt,name=obligations_met_on,json=obligationsMetOn,proto3" json:"obligations_met_on,omitempty"`
}

func (x *LoanRepaymentPeriod) Reset() {
	*x = LoanRepaymentPeriod{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanRepaymentPeriod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanRepaymentPeriod) ProtoMessage() {}

func (x *LoanRepaymentPeriod) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanRepaymentPeriod.ProtoReflect.Descriptor instead.
func (*LoanRepaymentPeriod) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_rawDescGZIP(), []int{3}
}

func (x *LoanRepaymentPeriod) GetSerialNumber() int32 {
	if x != nil {
		return x.SerialNumber
	}
	return 0
}

func (x *LoanRepaymentPeriod) GetFromDate() *date.Date {
	if x != nil {
		return x.FromDate
	}
	return nil
}

func (x *LoanRepaymentPeriod) GetDueDate() *date.Date {
	if x != nil {
		return x.DueDate
	}
	return nil
}

func (x *LoanRepaymentPeriod) GetDaysInPeriod() int32 {
	if x != nil {
		return x.DaysInPeriod
	}
	return 0
}

func (x *LoanRepaymentPeriod) GetPrincipalComponent() *LoanComponentBreakdown {
	if x != nil {
		return x.PrincipalComponent
	}
	return nil
}

func (x *LoanRepaymentPeriod) GetInterestComponent() *LoanComponentBreakdown {
	if x != nil {
		return x.InterestComponent
	}
	return nil
}

func (x *LoanRepaymentPeriod) GetFeeChargesComponent() *LoanComponentBreakdown {
	if x != nil {
		return x.FeeChargesComponent
	}
	return nil
}

func (x *LoanRepaymentPeriod) GetPenaltyChargesComponent() *LoanComponentBreakdown {
	if x != nil {
		return x.PenaltyChargesComponent
	}
	return nil
}

func (x *LoanRepaymentPeriod) GetTotalComponent() *LoanComponentBreakdown {
	if x != nil {
		return x.TotalComponent
	}
	return nil
}

func (x *LoanRepaymentPeriod) GetPrincipalBalanceOutstandingForLoan() *money.Money {
	if x != nil {
		return x.PrincipalBalanceOutstandingForLoan
	}
	return nil
}

func (x *LoanRepaymentPeriod) GetTotalActualCostOfLoanForPeriod() *money.Money {
	if x != nil {
		return x.TotalActualCostOfLoanForPeriod
	}
	return nil
}

func (x *LoanRepaymentPeriod) GetTotalInstallmentAmountForPeriod() *money.Money {
	if x != nil {
		return x.TotalInstallmentAmountForPeriod
	}
	return nil
}

func (x *LoanRepaymentPeriod) GetInterestAdjustedDueToGrace() *money.Money {
	if x != nil {
		return x.InterestAdjustedDueToGrace
	}
	return nil
}

func (x *LoanRepaymentPeriod) GetInterestAccruable() *money.Money {
	if x != nil {
		return x.InterestAccruable
	}
	return nil
}

func (x *LoanRepaymentPeriod) GetIsComplete() bool {
	if x != nil {
		return x.IsComplete
	}
	return false
}

func (x *LoanRepaymentPeriod) GetObligationsMetOn() *date.Date {
	if x != nil {
		return x.ObligationsMetOn
	}
	return nil
}

// component level breakdown of a loan repayment period
// e.g.: principal component, interest component, fee component, penalty component
type LoanComponentBreakdown struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OriginalDue *money.Money `protobuf:"bytes,1,opt,name=original_due,json=originalDue,proto3" json:"original_due,omitempty"`
	// total due amount for the EMI as on the request time
	Due *money.Money `protobuf:"bytes,2,opt,name=due,proto3" json:"due,omitempty"`
	// total amount paid for this component
	// not applicable for loan calculation as loan is not taken yet
	Paid *money.Money `protobuf:"bytes,3,opt,name=paid,proto3" json:"paid,omitempty"`
	// total amount waived for this component
	// not applicable for loan calculation as loan is not taken yet
	Waived *money.Money `protobuf:"bytes,4,opt,name=waived,proto3" json:"waived,omitempty"`
	// not applicable for loan calculation as loan is not taken yet
	WrittenOff *money.Money `protobuf:"bytes,5,opt,name=written_off,json=writtenOff,proto3" json:"written_off,omitempty"`
	// not applicable for loan calculation as loan is not taken yet
	Outstanding *money.Money `protobuf:"bytes,6,opt,name=outstanding,proto3" json:"outstanding,omitempty"`
}

func (x *LoanComponentBreakdown) Reset() {
	*x = LoanComponentBreakdown{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanComponentBreakdown) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanComponentBreakdown) ProtoMessage() {}

func (x *LoanComponentBreakdown) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanComponentBreakdown.ProtoReflect.Descriptor instead.
func (*LoanComponentBreakdown) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_rawDescGZIP(), []int{4}
}

func (x *LoanComponentBreakdown) GetOriginalDue() *money.Money {
	if x != nil {
		return x.OriginalDue
	}
	return nil
}

func (x *LoanComponentBreakdown) GetDue() *money.Money {
	if x != nil {
		return x.Due
	}
	return nil
}

func (x *LoanComponentBreakdown) GetPaid() *money.Money {
	if x != nil {
		return x.Paid
	}
	return nil
}

func (x *LoanComponentBreakdown) GetWaived() *money.Money {
	if x != nil {
		return x.Waived
	}
	return nil
}

func (x *LoanComponentBreakdown) GetWrittenOff() *money.Money {
	if x != nil {
		return x.WrittenOff
	}
	return nil
}

func (x *LoanComponentBreakdown) GetOutstanding() *money.Money {
	if x != nil {
		return x.Outstanding
	}
	return nil
}

var File_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto protoreflect.FileDescriptor

var file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_rawDesc = []byte{
	0x0a, 0x3e, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2f, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6c, 0x6d, 0x73, 0x2f, 0x66, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x78, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e,
	0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x6d, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x78, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x1a, 0x3e, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72,
	0x64, 0x69, 0x61, 0x6e, 0x2f, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2f, 0x6c, 0x6d, 0x73, 0x2f, 0x66, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x78,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xc6, 0x15, 0x0a, 0x0b, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x12, 0x43, 0x0a, 0x13, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f,
	0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x12, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x44, 0x69,
	0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x64, 0x12, 0x39, 0x0a, 0x0e, 0x70, 0x72, 0x69, 0x6e, 0x63,
	0x69, 0x70, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0d, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x50, 0x61,
	0x69, 0x64, 0x12, 0x46, 0x0a, 0x15, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f,
	0x77, 0x72, 0x69, 0x74, 0x74, 0x65, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x13, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c,
	0x57, 0x72, 0x69, 0x74, 0x74, 0x65, 0x6e, 0x4f, 0x66, 0x66, 0x12, 0x47, 0x0a, 0x15, 0x70, 0x72,
	0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x70,
	0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x55, 0x0a, 0x1d, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c,
	0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x70, 0x6f, 0x73,
	0x74, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x1a,
	0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x46, 0x72, 0x6f, 0x6d, 0x49, 0x6e, 0x63,
	0x6f, 0x6d, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x3f, 0x0a, 0x11, 0x70, 0x72,
	0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x64, 0x75, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x70, 0x72, 0x69, 0x6e, 0x63,
	0x69, 0x70, 0x61, 0x6c, 0x4f, 0x76, 0x65, 0x72, 0x64, 0x75, 0x65, 0x12, 0x4a, 0x0a, 0x17, 0x70,
	0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x74, 0x5f, 0x64, 0x69, 0x73,
	0x62, 0x75, 0x72, 0x73, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x15, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x4e, 0x65, 0x74, 0x44, 0x69,
	0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x64, 0x12, 0x3d, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x65, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x12, 0x37, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65,
	0x73, 0x74, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x50, 0x61, 0x69, 0x64, 0x12,
	0x3b, 0x0a, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x77, 0x61, 0x69, 0x76,
	0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x57, 0x61, 0x69, 0x76, 0x65, 0x64, 0x12, 0x44, 0x0a, 0x14,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x74, 0x65, 0x6e,
	0x5f, 0x6f, 0x66, 0x66, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x12,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x57, 0x72, 0x69, 0x74, 0x74, 0x65, 0x6e, 0x4f,
	0x66, 0x66, 0x12, 0x45, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x6f,
	0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x13, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x4f, 0x75,
	0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x3d, 0x0a, 0x10, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x64, 0x75, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x4f, 0x76, 0x65, 0x72, 0x64, 0x75, 0x65, 0x12, 0x42, 0x0a, 0x13, 0x66, 0x65, 0x65, 0x5f,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x66, 0x65, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x12, 0x67, 0x0a, 0x27,
	0x66, 0x65, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x64, 0x75, 0x65, 0x5f,
	0x61, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x22, 0x66, 0x65, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x44, 0x75, 0x65,
	0x41, 0x74, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x64, 0x12, 0x3c, 0x0a, 0x10, 0x66, 0x65, 0x65, 0x5f, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0e, 0x66, 0x65, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x50,
	0x61, 0x69, 0x64, 0x12, 0x40, 0x0a, 0x12, 0x66, 0x65, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x5f, 0x77, 0x61, 0x69, 0x76, 0x65, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x10, 0x66, 0x65, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x57,
	0x61, 0x69, 0x76, 0x65, 0x64, 0x12, 0x49, 0x0a, 0x17, 0x66, 0x65, 0x65, 0x5f, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x74, 0x65, 0x6e, 0x5f, 0x6f, 0x66, 0x66,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x66, 0x65, 0x65, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x57, 0x72, 0x69, 0x74, 0x74, 0x65, 0x6e, 0x4f, 0x66, 0x66,
	0x12, 0x4a, 0x0a, 0x17, 0x66, 0x65, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f,
	0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x15, 0x66, 0x65, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x73, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x42, 0x0a, 0x13,
	0x66, 0x65, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x6f, 0x76, 0x65, 0x72,
	0x64, 0x75, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x66,
	0x65, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x4f, 0x76, 0x65, 0x72, 0x64, 0x75, 0x65,
	0x12, 0x4a, 0x0a, 0x17, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x15, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x12, 0x44, 0x0a, 0x14,
	0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f,
	0x70, 0x61, 0x69, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x12,
	0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x50, 0x61,
	0x69, 0x64, 0x12, 0x48, 0x0a, 0x16, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x77, 0x61, 0x69, 0x76, 0x65, 0x64, 0x18, 0x17, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x57, 0x61, 0x69, 0x76, 0x65, 0x64, 0x12, 0x51, 0x0a, 0x1b,
	0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f,
	0x77, 0x72, 0x69, 0x74, 0x74, 0x65, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x18, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x18, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x57, 0x72, 0x69, 0x74, 0x74, 0x65, 0x6e, 0x4f, 0x66, 0x66, 0x12,
	0x52, 0x0a, 0x1b, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x19, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74,
	0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x4a, 0x0a, 0x17, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x64, 0x75, 0x65, 0x18, 0x1a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x15, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74,
	0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x4f, 0x76, 0x65, 0x72, 0x64, 0x75, 0x65, 0x12,
	0x4c, 0x0a, 0x18, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x16, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3b, 0x0a,
	0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x50, 0x0a, 0x1b, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x73,
	0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x17, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x43, 0x6f, 0x73, 0x74, 0x4f, 0x66, 0x4c, 0x6f, 0x61, 0x6e, 0x12, 0x3f, 0x0a, 0x12,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x6c, 0x6f,
	0x61, 0x6e, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x4f, 0x66, 0x4c, 0x6f, 0x61, 0x6e, 0x12, 0x35, 0x0a,
	0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x77, 0x61, 0x69, 0x76, 0x65, 0x64, 0x18, 0x1f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x57, 0x61,
	0x69, 0x76, 0x65, 0x64, 0x12, 0x3e, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x77, 0x72,
	0x69, 0x74, 0x74, 0x65, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x57, 0x72, 0x69, 0x74, 0x74, 0x65,
	0x6e, 0x4f, 0x66, 0x66, 0x12, 0x3f, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6f, 0x75,
	0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x37, 0x0a, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6f,
	0x76, 0x65, 0x72, 0x64, 0x75, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x76, 0x65, 0x72, 0x64, 0x75, 0x65, 0x12, 0x40,
	0x0a, 0x12, 0x65, 0x78, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x70, 0x61, 0x69, 0x64, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10,
	0x65, 0x78, 0x63, 0x65, 0x73, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x69, 0x64,
	0x12, 0x3f, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x12, 0x50, 0x0a, 0x1a, 0x75, 0x70, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x25, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x18, 0x75, 0x70, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x72, 0x65, 0x62, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x65, 0x64, 0x18, 0x26, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0d, 0x72, 0x65, 0x62, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x12, 0x4b,
	0x0a, 0x18, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x65, 0x6d, 0x69, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x27, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x15, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x76, 0x61, 0x6e,
	0x63, 0x65, 0x45, 0x6d, 0x69, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xc8, 0x02, 0x0a, 0x0c,
	0x4c, 0x6f, 0x61, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x34, 0x0a, 0x0c,
	0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64,
	0x4f, 0x6e, 0x12, 0x32, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x5f, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x4f, 0x6e, 0x12, 0x4f, 0x0a, 0x1a, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x18, 0x65,
	0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x62, 0x75,
	0x72, 0x73, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x0b, 0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x64, 0x4f, 0x6e, 0x12, 0x47, 0x0a,
	0x16, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x61, 0x74, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x14, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4d, 0x61, 0x74, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x44, 0x61, 0x74, 0x65, 0x22, 0xee, 0x10, 0x0a, 0x0b, 0x4c, 0x6f, 0x61, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x53, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x6d,
	0x73, 0x2e, 0x66, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x78, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x3d, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x70,
	0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4e,
	0x0a, 0x19, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x63,
	0x69, 0x70, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x17, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x50,
	0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4e,
	0x0a, 0x19, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x63,
	0x69, 0x70, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x17, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x50,
	0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x47,
	0x0a, 0x16, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x69, 0x6e,
	0x5f, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x13, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x69, 0x64, 0x49, 0x6e,
	0x41, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x48, 0x0a, 0x16, 0x62, 0x72, 0x6f, 0x6b, 0x65,
	0x6e, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x62, 0x72, 0x6f,
	0x6b, 0x65, 0x6e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x65, 0x72, 0x6d, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x74, 0x65, 0x72, 0x6d, 0x46,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x6e, 0x0a, 0x13, 0x74, 0x65, 0x72, 0x6d,
	0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3e, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61,
	0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x6d, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x66, 0x6c, 0x75,
	0x78, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x74, 0x65, 0x72, 0x6d, 0x46, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66,
	0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x76, 0x65, 0x72, 0x79, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x76,
	0x65, 0x72, 0x79, 0x12, 0x78, 0x0a, 0x18, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3e, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61,
	0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x6d, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x66, 0x6c, 0x75,
	0x78, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x16, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a,
	0x18, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x70,
	0x65, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x15, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x50, 0x65, 0x72,
	0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x7f, 0x0a, 0x1c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65,
	0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3e, 0x2e, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x6d, 0x73,
	0x2e, 0x66, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x78, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x46,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x19, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x6e, 0x6e, 0x75, 0x61,
	0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x19, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x72, 0x65, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x50, 0x61, 0x69, 0x64, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f,
	0x66, 0x5f, 0x64, 0x75, 0x65, 0x5f, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66,
	0x44, 0x75, 0x65, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x62, 0x0a,
	0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72,
	0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x6d, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x78,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x3e, 0x0a, 0x1c, 0x6d, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x01, 0x52, 0x18, 0x6d, 0x69, 0x6e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x50, 0x65, 0x72, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x12, 0x3e, 0x0a, 0x1c, 0x6d, 0x61, 0x78, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x01, 0x52, 0x18, 0x6d, 0x61, 0x78, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x50, 0x65, 0x72, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x13, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x52, 0x61, 0x74, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x69, 0x73, 0x5f, 0x63, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x69, 0x73, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x59, 0x0a,
	0x08, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3d, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e,
	0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x6d, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x78, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x08,
	0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x56, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x6d, 0x73, 0x2e, 0x66, 0x69,
	0x6e, 0x66, 0x6c, 0x75, 0x78, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x12, 0x51, 0x0a, 0x1b, 0x66, 0x65, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x18, 0x66, 0x65, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x64, 0x41, 0x74, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x5f, 0x61, 0x72, 0x72, 0x65, 0x61, 0x72,
	0x73, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x6e, 0x41, 0x72, 0x72, 0x65, 0x61,
	0x72, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x6e, 0x70, 0x61, 0x18, 0x1d, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x4e, 0x70, 0x61, 0x12, 0x46, 0x0a, 0x15, 0x63, 0x61, 0x6c,
	0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6d, 0x69, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x13, 0x63, 0x61,
	0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x45, 0x6d, 0x69, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x66, 0x6c, 0x64, 0x67, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x46, 0x6c, 0x64, 0x67, 0x12, 0x3d, 0x0a, 0x1b, 0x61, 0x63,
	0x74, 0x75, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x72,
	0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x20, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x18, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x52,
	0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x4c, 0x0a, 0x18, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x70,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x16, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x45,
	0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0xce, 0x0a, 0x0a, 0x13, 0x4c, 0x6f, 0x61, 0x6e,
	0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x07, 0x64, 0x75, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x64, 0x61, 0x79, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x70, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x64, 0x61, 0x79, 0x73,
	0x49, 0x6e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x78, 0x0a, 0x13, 0x70, 0x72, 0x69, 0x6e,
	0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61,
	0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x6d, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x66, 0x6c, 0x75,
	0x78, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x52, 0x12,
	0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x12, 0x76, 0x0a, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47,
	0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73,
	0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c,
	0x6d, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x78, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x42, 0x72,
	0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x52, 0x11, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x7b, 0x0a, 0x15, 0x66, 0x65,
	0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x6d, 0x73, 0x2e, 0x66, 0x69,
	0x6e, 0x66, 0x6c, 0x75, 0x78, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f,
	0x77, 0x6e, 0x52, 0x13, 0x66, 0x65, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x83, 0x01, 0x0a, 0x19, 0x70, 0x65, 0x6e, 0x61,
	0x6c, 0x74, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x6d, 0x73, 0x2e,
	0x66, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x78, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4c, 0x6f,
	0x61, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b,
	0x64, 0x6f, 0x77, 0x6e, 0x52, 0x17, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x70, 0x0a,
	0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x6d, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x66, 0x6c,
	0x75, 0x78, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x52,
	0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12,
	0x66, 0x0a, 0x26, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x62, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x22, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x46, 0x6f, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x12, 0x60, 0x0a, 0x24, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x6f, 0x66, 0x5f,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x1e, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x4f, 0x66, 0x4c, 0x6f, 0x61, 0x6e,
	0x46, 0x6f, 0x72, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x60, 0x0a, 0x23, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x1f, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x46, 0x6f, 0x72, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x56, 0x0a, 0x1e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x65, 0x64,
	0x5f, 0x64, 0x75, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x67, 0x72, 0x61, 0x63, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x1a, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x65, 0x64, 0x44, 0x75, 0x65, 0x54, 0x6f, 0x47, 0x72,
	0x61, 0x63, 0x65, 0x12, 0x41, 0x0a, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f,
	0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x11, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x41, 0x63, 0x63,
	0x72, 0x75, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x12, 0x6f, 0x62, 0x6c, 0x69, 0x67,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x6d, 0x65, 0x74, 0x5f, 0x6f, 0x6e, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x10, 0x6f, 0x62, 0x6c, 0x69, 0x67, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x74, 0x4f, 0x6e, 0x22, 0xb4, 0x02, 0x0a, 0x16, 0x4c, 0x6f, 0x61,
	0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x64,
	0x6f, 0x77, 0x6e, 0x12, 0x35, 0x0a, 0x0c, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f,
	0x64, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x75, 0x65, 0x12, 0x24, 0x0a, 0x03, 0x64, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x03, 0x64, 0x75, 0x65,
	0x12, 0x26, 0x0a, 0x04, 0x70, 0x61, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x04, 0x70, 0x61, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x77, 0x61, 0x69, 0x76,
	0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x77, 0x61,
	0x69, 0x76, 0x65, 0x64, 0x12, 0x33, 0x0a, 0x0b, 0x77, 0x72, 0x69, 0x74, 0x74, 0x65, 0x6e, 0x5f,
	0x6f, 0x66, 0x66, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x77,
	0x72, 0x69, 0x74, 0x74, 0x65, 0x6e, 0x4f, 0x66, 0x66, 0x12, 0x34, 0x0a, 0x0b, 0x6f, 0x75, 0x74,
	0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0b, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x42,
	0x9e, 0x01, 0x0a, 0x4d, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x72, 0x69, 0x6e, 0x67, 0x6f, 0x74, 0x74, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e,
	0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x6d, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x78, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x5a, 0x4d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x72, 0x69, 0x6e, 0x67, 0x6f, 0x74, 0x74, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2f, 0x73,
	0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6c,
	0x6d, 0x73, 0x2f, 0x66, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x78, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_rawDescOnce sync.Once
	file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_rawDescData = file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_rawDesc
)

func file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_rawDescGZIP() []byte {
	file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_rawDescOnce.Do(func() {
		file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_rawDescData)
	})
	return file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_rawDescData
}

var file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_goTypes = []interface{}{
	(*LoanSummary)(nil),            // 0: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary
	(*LoanTimeline)(nil),           // 1: stockguardian.sgvendorgateway.lms.finflux.types.LoanTimeline
	(*LoanDetails)(nil),            // 2: stockguardian.sgvendorgateway.lms.finflux.types.LoanDetails
	(*LoanRepaymentPeriod)(nil),    // 3: stockguardian.sgvendorgateway.lms.finflux.types.LoanRepaymentPeriod
	(*LoanComponentBreakdown)(nil), // 4: stockguardian.sgvendorgateway.lms.finflux.types.LoanComponentBreakdown
	(*money.Money)(nil),            // 5: google.type.Money
	(*date.Date)(nil),              // 6: google.type.Date
	(LoanStatus)(0),                // 7: stockguardian.sgvendorgateway.lms.finflux.types.LoanStatus
	(FrequencyType)(0),             // 8: stockguardian.sgvendorgateway.lms.finflux.types.FrequencyType
	(InterestType)(0),              // 9: stockguardian.sgvendorgateway.lms.finflux.types.InterestType
}
var file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_depIdxs = []int32{
	5,  // 0: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.principal_disbursed:type_name -> google.type.Money
	5,  // 1: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.principal_paid:type_name -> google.type.Money
	5,  // 2: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.principal_written_off:type_name -> google.type.Money
	5,  // 3: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.principal_outstanding:type_name -> google.type.Money
	5,  // 4: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.principal_from_income_posting:type_name -> google.type.Money
	5,  // 5: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.principal_overdue:type_name -> google.type.Money
	5,  // 6: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.principal_net_disbursed:type_name -> google.type.Money
	5,  // 7: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.interest_charged:type_name -> google.type.Money
	5,  // 8: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.interest_paid:type_name -> google.type.Money
	5,  // 9: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.interest_waived:type_name -> google.type.Money
	5,  // 10: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.interest_written_off:type_name -> google.type.Money
	5,  // 11: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.interest_outstanding:type_name -> google.type.Money
	5,  // 12: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.interest_overdue:type_name -> google.type.Money
	5,  // 13: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.fee_charges_charged:type_name -> google.type.Money
	5,  // 14: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.fee_charges_due_at_disbursement_charged:type_name -> google.type.Money
	5,  // 15: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.fee_charges_paid:type_name -> google.type.Money
	5,  // 16: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.fee_charges_waived:type_name -> google.type.Money
	5,  // 17: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.fee_charges_written_off:type_name -> google.type.Money
	5,  // 18: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.fee_charges_outstanding:type_name -> google.type.Money
	5,  // 19: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.fee_charges_overdue:type_name -> google.type.Money
	5,  // 20: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.penalty_charges_charged:type_name -> google.type.Money
	5,  // 21: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.penalty_charges_paid:type_name -> google.type.Money
	5,  // 22: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.penalty_charges_waived:type_name -> google.type.Money
	5,  // 23: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.penalty_charges_written_off:type_name -> google.type.Money
	5,  // 24: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.penalty_charges_outstanding:type_name -> google.type.Money
	5,  // 25: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.penalty_charges_overdue:type_name -> google.type.Money
	5,  // 26: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.total_expected_repayment:type_name -> google.type.Money
	5,  // 27: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.total_repayment:type_name -> google.type.Money
	5,  // 28: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.total_expected_cost_of_loan:type_name -> google.type.Money
	5,  // 29: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.total_cost_of_loan:type_name -> google.type.Money
	5,  // 30: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.total_waived:type_name -> google.type.Money
	5,  // 31: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.total_written_off:type_name -> google.type.Money
	5,  // 32: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.total_outstanding:type_name -> google.type.Money
	5,  // 33: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.total_overdue:type_name -> google.type.Money
	5,  // 34: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.excess_amount_paid:type_name -> google.type.Money
	5,  // 35: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.available_balance:type_name -> google.type.Money
	5,  // 36: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.upfront_interest_available:type_name -> google.type.Money
	5,  // 37: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.rebate_applied:type_name -> google.type.Money
	5,  // 38: stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary.total_advance_emi_amount:type_name -> google.type.Money
	6,  // 39: stockguardian.sgvendorgateway.lms.finflux.types.LoanTimeline.submitted_on:type_name -> google.type.Date
	6,  // 40: stockguardian.sgvendorgateway.lms.finflux.types.LoanTimeline.approved_on:type_name -> google.type.Date
	6,  // 41: stockguardian.sgvendorgateway.lms.finflux.types.LoanTimeline.expected_disbursement_date:type_name -> google.type.Date
	6,  // 42: stockguardian.sgvendorgateway.lms.finflux.types.LoanTimeline.disbursed_on:type_name -> google.type.Date
	6,  // 43: stockguardian.sgvendorgateway.lms.finflux.types.LoanTimeline.expected_maturity_date:type_name -> google.type.Date
	7,  // 44: stockguardian.sgvendorgateway.lms.finflux.types.LoanDetails.status:type_name -> stockguardian.sgvendorgateway.lms.finflux.types.LoanStatus
	5,  // 45: stockguardian.sgvendorgateway.lms.finflux.types.LoanDetails.principal_amount:type_name -> google.type.Money
	5,  // 46: stockguardian.sgvendorgateway.lms.finflux.types.LoanDetails.approved_principal_amount:type_name -> google.type.Money
	5,  // 47: stockguardian.sgvendorgateway.lms.finflux.types.LoanDetails.proposed_principal_amount:type_name -> google.type.Money
	5,  // 48: stockguardian.sgvendorgateway.lms.finflux.types.LoanDetails.amount_paid_in_advance:type_name -> google.type.Money
	5,  // 49: stockguardian.sgvendorgateway.lms.finflux.types.LoanDetails.broken_period_interest:type_name -> google.type.Money
	8,  // 50: stockguardian.sgvendorgateway.lms.finflux.types.LoanDetails.term_frequency_type:type_name -> stockguardian.sgvendorgateway.lms.finflux.types.FrequencyType
	8,  // 51: stockguardian.sgvendorgateway.lms.finflux.types.LoanDetails.repayment_frequency_type:type_name -> stockguardian.sgvendorgateway.lms.finflux.types.FrequencyType
	8,  // 52: stockguardian.sgvendorgateway.lms.finflux.types.LoanDetails.interest_rate_frequency_type:type_name -> stockguardian.sgvendorgateway.lms.finflux.types.FrequencyType
	9,  // 53: stockguardian.sgvendorgateway.lms.finflux.types.LoanDetails.interest_type:type_name -> stockguardian.sgvendorgateway.lms.finflux.types.InterestType
	1,  // 54: stockguardian.sgvendorgateway.lms.finflux.types.LoanDetails.timeline:type_name -> stockguardian.sgvendorgateway.lms.finflux.types.LoanTimeline
	0,  // 55: stockguardian.sgvendorgateway.lms.finflux.types.LoanDetails.summary:type_name -> stockguardian.sgvendorgateway.lms.finflux.types.LoanSummary
	5,  // 56: stockguardian.sgvendorgateway.lms.finflux.types.LoanDetails.fee_charged_at_disbursement:type_name -> google.type.Money
	5,  // 57: stockguardian.sgvendorgateway.lms.finflux.types.LoanDetails.calculated_emi_amount:type_name -> google.type.Money
	5,  // 58: stockguardian.sgvendorgateway.lms.finflux.types.LoanDetails.total_repayment_expected:type_name -> google.type.Money
	6,  // 59: stockguardian.sgvendorgateway.lms.finflux.types.LoanRepaymentPeriod.from_date:type_name -> google.type.Date
	6,  // 60: stockguardian.sgvendorgateway.lms.finflux.types.LoanRepaymentPeriod.due_date:type_name -> google.type.Date
	4,  // 61: stockguardian.sgvendorgateway.lms.finflux.types.LoanRepaymentPeriod.principal_component:type_name -> stockguardian.sgvendorgateway.lms.finflux.types.LoanComponentBreakdown
	4,  // 62: stockguardian.sgvendorgateway.lms.finflux.types.LoanRepaymentPeriod.interest_component:type_name -> stockguardian.sgvendorgateway.lms.finflux.types.LoanComponentBreakdown
	4,  // 63: stockguardian.sgvendorgateway.lms.finflux.types.LoanRepaymentPeriod.fee_charges_component:type_name -> stockguardian.sgvendorgateway.lms.finflux.types.LoanComponentBreakdown
	4,  // 64: stockguardian.sgvendorgateway.lms.finflux.types.LoanRepaymentPeriod.penalty_charges_component:type_name -> stockguardian.sgvendorgateway.lms.finflux.types.LoanComponentBreakdown
	4,  // 65: stockguardian.sgvendorgateway.lms.finflux.types.LoanRepaymentPeriod.total_component:type_name -> stockguardian.sgvendorgateway.lms.finflux.types.LoanComponentBreakdown
	5,  // 66: stockguardian.sgvendorgateway.lms.finflux.types.LoanRepaymentPeriod.principal_balance_outstanding_for_loan:type_name -> google.type.Money
	5,  // 67: stockguardian.sgvendorgateway.lms.finflux.types.LoanRepaymentPeriod.total_actual_cost_of_loan_for_period:type_name -> google.type.Money
	5,  // 68: stockguardian.sgvendorgateway.lms.finflux.types.LoanRepaymentPeriod.total_installment_amount_for_period:type_name -> google.type.Money
	5,  // 69: stockguardian.sgvendorgateway.lms.finflux.types.LoanRepaymentPeriod.interest_adjusted_due_to_grace:type_name -> google.type.Money
	5,  // 70: stockguardian.sgvendorgateway.lms.finflux.types.LoanRepaymentPeriod.interest_accruable:type_name -> google.type.Money
	6,  // 71: stockguardian.sgvendorgateway.lms.finflux.types.LoanRepaymentPeriod.obligations_met_on:type_name -> google.type.Date
	5,  // 72: stockguardian.sgvendorgateway.lms.finflux.types.LoanComponentBreakdown.original_due:type_name -> google.type.Money
	5,  // 73: stockguardian.sgvendorgateway.lms.finflux.types.LoanComponentBreakdown.due:type_name -> google.type.Money
	5,  // 74: stockguardian.sgvendorgateway.lms.finflux.types.LoanComponentBreakdown.paid:type_name -> google.type.Money
	5,  // 75: stockguardian.sgvendorgateway.lms.finflux.types.LoanComponentBreakdown.waived:type_name -> google.type.Money
	5,  // 76: stockguardian.sgvendorgateway.lms.finflux.types.LoanComponentBreakdown.written_off:type_name -> google.type.Money
	5,  // 77: stockguardian.sgvendorgateway.lms.finflux.types.LoanComponentBreakdown.outstanding:type_name -> google.type.Money
	78, // [78:78] is the sub-list for method output_type
	78, // [78:78] is the sub-list for method input_type
	78, // [78:78] is the sub-list for extension type_name
	78, // [78:78] is the sub-list for extension extendee
	0,  // [0:78] is the sub-list for field type_name
}

func init() { file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_init() }
func file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_init() {
	if File_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto != nil {
		return
	}
	file_api_stockguardian_sgvendorgateway_lms_finflux_types_enum_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanTimeline); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanRepaymentPeriod); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanComponentBreakdown); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_goTypes,
		DependencyIndexes: file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_depIdxs,
		MessageInfos:      file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_msgTypes,
	}.Build()
	File_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto = out.File
	file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_rawDesc = nil
	file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_goTypes = nil
	file_api_stockguardian_sgvendorgateway_lms_finflux_types_loan_proto_depIdxs = nil
}
