// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/salaryprogram/dynamic_ui_element/dynamic_ui_element.proto

package dynamic_ui_element

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on DynamicUIElement with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DynamicUIElement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DynamicUIElement with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DynamicUIElementMultiError, or nil if none found.
func (m *DynamicUIElement) ValidateAll() error {
	return m.validate(true)
}

func (m *DynamicUIElement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for VariantName

	if all {
		switch v := interface{}(m.GetContentJson()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DynamicUIElementValidationError{
					field:  "ContentJson",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DynamicUIElementValidationError{
					field:  "ContentJson",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContentJson()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DynamicUIElementValidationError{
				field:  "ContentJson",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DynamicUIElementValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DynamicUIElementValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DynamicUIElementValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DynamicUIElementValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DynamicUIElementValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DynamicUIElementValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DynamicUIElementValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DynamicUIElementValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DynamicUIElementValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DynamicUIElementMultiError(errors)
	}

	return nil
}

// DynamicUIElementMultiError is an error wrapping multiple validation errors
// returned by DynamicUIElement.ValidateAll() if the designated constraints
// aren't met.
type DynamicUIElementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DynamicUIElementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DynamicUIElementMultiError) AllErrors() []error { return m }

// DynamicUIElementValidationError is the validation error returned by
// DynamicUIElement.Validate if the designated constraints aren't met.
type DynamicUIElementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DynamicUIElementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DynamicUIElementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DynamicUIElementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DynamicUIElementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DynamicUIElementValidationError) ErrorName() string { return "DynamicUIElementValidationError" }

// Error satisfies the builtin error interface
func (e DynamicUIElementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDynamicUIElement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DynamicUIElementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DynamicUIElementValidationError{}

// Validate checks the field values on ContentJson with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ContentJson) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContentJson with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ContentJsonMultiError, or
// nil if none found.
func (m *ContentJson) ValidateAll() error {
	return m.validate(true)
}

func (m *ContentJson) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDynamicElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContentJsonValidationError{
					field:  "DynamicElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContentJsonValidationError{
					field:  "DynamicElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDynamicElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContentJsonValidationError{
				field:  "DynamicElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ContentJsonMultiError(errors)
	}

	return nil
}

// ContentJsonMultiError is an error wrapping multiple validation errors
// returned by ContentJson.ValidateAll() if the designated constraints aren't met.
type ContentJsonMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContentJsonMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContentJsonMultiError) AllErrors() []error { return m }

// ContentJsonValidationError is the validation error returned by
// ContentJson.Validate if the designated constraints aren't met.
type ContentJsonValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContentJsonValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContentJsonValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContentJsonValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContentJsonValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContentJsonValidationError) ErrorName() string { return "ContentJsonValidationError" }

// Error satisfies the builtin error interface
func (e ContentJsonValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContentJson.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContentJsonValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContentJsonValidationError{}
