// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /Users/<USER>/go/src/github.com/epifi/gamma/api/deposit/template.pb.go

package deposit

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing Template while reading from DB
func (a *Template) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the Template in string format in DB
func (a *Template) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for Template
func (a *Template) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for Template
func (a *Template) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing TemplateDisplayDetails while reading from DB
func (a *TemplateDisplayDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the TemplateDisplayDetails in string format in DB
func (a *TemplateDisplayDetails) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for TemplateDisplayDetails
func (a *TemplateDisplayDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for TemplateDisplayDetails
func (a *TemplateDisplayDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing TemplateConsentDetails while reading from DB
func (a *TemplateConsentDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the TemplateConsentDetails in string format in DB
func (a *TemplateConsentDetails) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for TemplateConsentDetails
func (a *TemplateConsentDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for TemplateConsentDetails
func (a *TemplateConsentDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing TemplateVisibilityConstraints while reading from DB
func (a *TemplateVisibilityConstraints) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the TemplateVisibilityConstraints in string format in DB
func (a *TemplateVisibilityConstraints) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for TemplateVisibilityConstraints
func (a *TemplateVisibilityConstraints) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for TemplateVisibilityConstraints
func (a *TemplateVisibilityConstraints) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing TemplateFieldEditableDetails while reading from DB
func (a *TemplateFieldEditableDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the TemplateFieldEditableDetails in string format in DB
func (a *TemplateFieldEditableDetails) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for TemplateFieldEditableDetails
func (a *TemplateFieldEditableDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for TemplateFieldEditableDetails
func (a *TemplateFieldEditableDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing TemplateGoalDetails while reading from DB
func (a *TemplateGoalDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the TemplateGoalDetails in string format in DB
func (a *TemplateGoalDetails) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for TemplateGoalDetails
func (a *TemplateGoalDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for TemplateGoalDetails
func (a *TemplateGoalDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing TemplateTag while reading from DB
func (a *TemplateTag) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the TemplateTag in string format in DB
func (a *TemplateTag) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for TemplateTag
func (a *TemplateTag) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for TemplateTag
func (a *TemplateTag) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
