// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/deposit/deposit_transaction.proto

package deposit

import (
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DepositTxnType int32

const (
	DepositTxnType_DEPOSIT_TXN_TYPE_UNSPECIFIED DepositTxnType = 0
	DepositTxnType_CREATION                     DepositTxnType = 1
	DepositTxnType_CLOSURE                      DepositTxnType = 2
	DepositTxnType_INTEREST_RUN                 DepositTxnType = 3
	DepositTxnType_ADD_FUNDS                    DepositTxnType = 4
)

// Enum value maps for DepositTxnType.
var (
	DepositTxnType_name = map[int32]string{
		0: "DEPOSIT_TXN_TYPE_UNSPECIFIED",
		1: "CREATION",
		2: "CLOSURE",
		3: "INTEREST_RUN",
		4: "ADD_FUNDS",
	}
	DepositTxnType_value = map[string]int32{
		"DEPOSIT_TXN_TYPE_UNSPECIFIED": 0,
		"CREATION":                     1,
		"CLOSURE":                      2,
		"INTEREST_RUN":                 3,
		"ADD_FUNDS":                    4,
	}
)

func (x DepositTxnType) Enum() *DepositTxnType {
	p := new(DepositTxnType)
	*p = x
	return p
}

func (x DepositTxnType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DepositTxnType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_deposit_deposit_transaction_proto_enumTypes[0].Descriptor()
}

func (DepositTxnType) Type() protoreflect.EnumType {
	return &file_api_deposit_deposit_transaction_proto_enumTypes[0]
}

func (x DepositTxnType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DepositTxnType.Descriptor instead.
func (DepositTxnType) EnumDescriptor() ([]byte, []int) {
	return file_api_deposit_deposit_transaction_proto_rawDescGZIP(), []int{0}
}

// DepositTxnFieldMask is the enum representation of all the deposit transaction fields.
type DepositTxnFieldMask int32

const (
	DepositTxnFieldMask_DEPOSIT_TXN_MASK_UNSPECIFIED DepositTxnFieldMask = 0
	DepositTxnFieldMask_DEPOSIT_TXN_ID               DepositTxnFieldMask = 1
	DepositTxnFieldMask_DEPOSIT_TXN_ACCOUNT_ID       DepositTxnFieldMask = 2
	DepositTxnFieldMask_TXN_ID                       DepositTxnFieldMask = 3
	DepositTxnFieldMask_TXN_TYPE                     DepositTxnFieldMask = 4
	DepositTxnFieldMask_TXN_AMOUNT                   DepositTxnFieldMask = 5
	DepositTxnFieldMask_TXN_TIMESTAMP                DepositTxnFieldMask = 6
)

// Enum value maps for DepositTxnFieldMask.
var (
	DepositTxnFieldMask_name = map[int32]string{
		0: "DEPOSIT_TXN_MASK_UNSPECIFIED",
		1: "DEPOSIT_TXN_ID",
		2: "DEPOSIT_TXN_ACCOUNT_ID",
		3: "TXN_ID",
		4: "TXN_TYPE",
		5: "TXN_AMOUNT",
		6: "TXN_TIMESTAMP",
	}
	DepositTxnFieldMask_value = map[string]int32{
		"DEPOSIT_TXN_MASK_UNSPECIFIED": 0,
		"DEPOSIT_TXN_ID":               1,
		"DEPOSIT_TXN_ACCOUNT_ID":       2,
		"TXN_ID":                       3,
		"TXN_TYPE":                     4,
		"TXN_AMOUNT":                   5,
		"TXN_TIMESTAMP":                6,
	}
)

func (x DepositTxnFieldMask) Enum() *DepositTxnFieldMask {
	p := new(DepositTxnFieldMask)
	*p = x
	return p
}

func (x DepositTxnFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DepositTxnFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_deposit_deposit_transaction_proto_enumTypes[1].Descriptor()
}

func (DepositTxnFieldMask) Type() protoreflect.EnumType {
	return &file_api_deposit_deposit_transaction_proto_enumTypes[1]
}

func (x DepositTxnFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DepositTxnFieldMask.Descriptor instead.
func (DepositTxnFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_deposit_deposit_transaction_proto_rawDescGZIP(), []int{1}
}

type DepositTransaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Primary identifier of deposit transaction
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Deposit Account id to which the transaction belongs to
	DepositAccountId string `protobuf:"bytes,2,opt,name=deposit_account_id,json=depositAccountId,proto3" json:"deposit_account_id,omitempty"`
	// Transaction ID
	TxnId string `protobuf:"bytes,3,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
	// Deposit transaction type, could be CLOSURE, CREATION, INTEREST_RUN
	TxnType DepositTxnType `protobuf:"varint,4,opt,name=txn_type,json=txnType,proto3,enum=deposit.DepositTxnType" json:"txn_type,omitempty"`
	// Deposit Transaction amount
	TxnAmount *money.Money `protobuf:"bytes,5,opt,name=txn_amount,json=txnAmount,proto3" json:"txn_amount,omitempty"`
	// Deposit transaction date
	TxnTimestamp *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=txn_timestamp,json=txnTimestamp,proto3" json:"txn_timestamp,omitempty"`
	CreatedAt    *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt    *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt    *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *DepositTransaction) Reset() {
	*x = DepositTransaction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_deposit_deposit_transaction_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositTransaction) ProtoMessage() {}

func (x *DepositTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_api_deposit_deposit_transaction_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositTransaction.ProtoReflect.Descriptor instead.
func (*DepositTransaction) Descriptor() ([]byte, []int) {
	return file_api_deposit_deposit_transaction_proto_rawDescGZIP(), []int{0}
}

func (x *DepositTransaction) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DepositTransaction) GetDepositAccountId() string {
	if x != nil {
		return x.DepositAccountId
	}
	return ""
}

func (x *DepositTransaction) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

func (x *DepositTransaction) GetTxnType() DepositTxnType {
	if x != nil {
		return x.TxnType
	}
	return DepositTxnType_DEPOSIT_TXN_TYPE_UNSPECIFIED
}

func (x *DepositTransaction) GetTxnAmount() *money.Money {
	if x != nil {
		return x.TxnAmount
	}
	return nil
}

func (x *DepositTransaction) GetTxnTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.TxnTimestamp
	}
	return nil
}

func (x *DepositTransaction) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DepositTransaction) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *DepositTransaction) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_deposit_deposit_transaction_proto protoreflect.FileDescriptor

var file_api_deposit_deposit_transaction_proto_rawDesc = []byte{
	0x0a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x2f, 0x64, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc2, 0x03, 0x0a, 0x12, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x08, 0x74, 0x78, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x64, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x78, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x07, 0x74, 0x78, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x74, 0x78,
	0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x09, 0x74, 0x78, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3f, 0x0a,
	0x0d, 0x74, 0x78, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0c, 0x74, 0x78, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x2a,
	0x6e, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x78, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x20, 0x0a, 0x1c, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x54, 0x58, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x10, 0x02, 0x12, 0x10,
	0x0a, 0x0c, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x55, 0x4e, 0x10, 0x03,
	0x12, 0x0d, 0x0a, 0x09, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x04, 0x2a,
	0xa4, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x78, 0x6e, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x20, 0x0a, 0x1c, 0x44, 0x45, 0x50, 0x4f, 0x53,
	0x49, 0x54, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x45, 0x50,
	0x4f, 0x53, 0x49, 0x54, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x1a, 0x0a,
	0x16, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x58, 0x4e,
	0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x58, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x58, 0x4e, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e,
	0x54, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x58, 0x4e, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x53,
	0x54, 0x41, 0x4d, 0x50, 0x10, 0x06, 0x42, 0x48, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5a, 0x22, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_deposit_deposit_transaction_proto_rawDescOnce sync.Once
	file_api_deposit_deposit_transaction_proto_rawDescData = file_api_deposit_deposit_transaction_proto_rawDesc
)

func file_api_deposit_deposit_transaction_proto_rawDescGZIP() []byte {
	file_api_deposit_deposit_transaction_proto_rawDescOnce.Do(func() {
		file_api_deposit_deposit_transaction_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_deposit_deposit_transaction_proto_rawDescData)
	})
	return file_api_deposit_deposit_transaction_proto_rawDescData
}

var file_api_deposit_deposit_transaction_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_deposit_deposit_transaction_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_deposit_deposit_transaction_proto_goTypes = []interface{}{
	(DepositTxnType)(0),           // 0: deposit.DepositTxnType
	(DepositTxnFieldMask)(0),      // 1: deposit.DepositTxnFieldMask
	(*DepositTransaction)(nil),    // 2: deposit.DepositTransaction
	(*money.Money)(nil),           // 3: google.type.Money
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
}
var file_api_deposit_deposit_transaction_proto_depIdxs = []int32{
	0, // 0: deposit.DepositTransaction.txn_type:type_name -> deposit.DepositTxnType
	3, // 1: deposit.DepositTransaction.txn_amount:type_name -> google.type.Money
	4, // 2: deposit.DepositTransaction.txn_timestamp:type_name -> google.protobuf.Timestamp
	4, // 3: deposit.DepositTransaction.created_at:type_name -> google.protobuf.Timestamp
	4, // 4: deposit.DepositTransaction.updated_at:type_name -> google.protobuf.Timestamp
	4, // 5: deposit.DepositTransaction.deleted_at:type_name -> google.protobuf.Timestamp
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_deposit_deposit_transaction_proto_init() }
func file_api_deposit_deposit_transaction_proto_init() {
	if File_api_deposit_deposit_transaction_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_deposit_deposit_transaction_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositTransaction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_deposit_deposit_transaction_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_deposit_deposit_transaction_proto_goTypes,
		DependencyIndexes: file_api_deposit_deposit_transaction_proto_depIdxs,
		EnumInfos:         file_api_deposit_deposit_transaction_proto_enumTypes,
		MessageInfos:      file_api_deposit_deposit_transaction_proto_msgTypes,
	}.Build()
	File_api_deposit_deposit_transaction_proto = out.File
	file_api_deposit_deposit_transaction_proto_rawDesc = nil
	file_api_deposit_deposit_transaction_proto_goTypes = nil
	file_api_deposit_deposit_transaction_proto_depIdxs = nil
}
