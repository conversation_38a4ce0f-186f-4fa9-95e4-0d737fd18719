//nolint:gocritic,dupl
package comms

import (
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"
)

var (
	GenericReplyMsg = &WhatsappMessage{
		WhatsappOption: &WhatsappOption{
			Option: &WhatsappOption_GenericReplyWhatsappOption{
				GenericReplyWhatsappOption: &GenericReplyWhatsappOption{
					WhatsappType: WhatsappType_GENERIC_REPLY,
					Option: &GenericReplyWhatsappOption_GenericReplyWhatsappOptionV1{
						GenericReplyWhatsappOptionV1: &GenericReplyWhatsappOptionV1{
							TemplateVersion: TemplateVersion_VERSION_V1,
						},
					},
				},
			},
		},
	}
	WelcomeBackMsg = &WhatsappMessage{
		WhatsappOption: &WhatsappOption{
			Option: &WhatsappOption_OptBackInWhatsappOption{
				OptBackInWhatsappOption: &OptBackInWhatsappOption{
					WhatsappType: WhatsappType_OPT_BACK_IN,
					Option: &OptBackInWhatsappOption_OptBackInWhatsappOptionV1{
						OptBackInWhatsappOptionV1: &OptBackInWhatsappOptionV1{
							TemplateVersion: TemplateVersion_VERSION_V1,
						},
					},
				},
			},
		},
	}
	OptOutMsg = &WhatsappMessage{
		WhatsappOption: &WhatsappOption{
			Option: &WhatsappOption_OptOutWhatsappOption{
				OptOutWhatsappOption: &OptOutWhatsappOption{
					WhatsappType: WhatsappType_OPT_OUT,
					Option: &OptOutWhatsappOption_OptOutWhatsappOptionV1{
						OptOutWhatsappOptionV1: &OptOutWhatsappOptionV1{
							TemplateVersion: TemplateVersion_VERSION_V1,
						},
					},
				},
			},
		},
	}
)

// Interface which needs to be implemented by all template based whatsapp types we will have
// message needs to be whitelist on vendor platform and message id needs to be configured in comms config for the template
// Adding a new version for a template or adding a new template is as simple as adding new methods here
// service level code does not need to change for any new template additions
type IWhatsappTemplatedOption interface {
	// Returns the type of whatsapp message enum
	GetType() WhatsappType
	// Returns the parameter map in case any dynamic variables are present in whatsapp template
	GetParameterMap() map[string]string
	// Returns the version of the template for the given type passed in request
	GetTemplateVersion() TemplateVersion
}

// IWhatsappMediaExtension defines the extra functions required for Media templates
// Media template types must implement this interface in addition to IWhatsappTemplatedOption
type IWhatsappMediaExtension interface {
	GetMediaContentType() WhatsappMediaContentType
	GetMediaUrl() string
	// GetMediaFileName returns the file name which is to be displayed to the user on the whatsapp message.
	// It will be displayed only for few media types like PDF etc. and won't be used even if we pass for other
	// media types like Images or videos
	GetMediaFileName() string
}

func (w *WhatsappOption_PhysicalCardOrderSuccessWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.PhysicalCardOrderSuccessWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_PhysicalCardOrderSuccessWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.PhysicalCardOrderSuccessWhatsappOption.GetOption().(type) {
	case *PhysicalCardOrderSuccessWhatsappOption_PhysicalCardOrderSuccessWhatsappOptionV1:
		return map[string]string{
			"1": w.PhysicalCardOrderSuccessWhatsappOption.GetPhysicalCardOrderSuccessWhatsappOptionV1().GetCardHolderName().GetFirstName(),
			"2": w.PhysicalCardOrderSuccessWhatsappOption.GetPhysicalCardOrderSuccessWhatsappOptionV1().GetExpectedDeliveryTat(),
		}
	}
	return nil
}

func (w *WhatsappOption_PhysicalCardOrderSuccessWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.PhysicalCardOrderSuccessWhatsappOption.GetOption().(type) {
	case *PhysicalCardOrderSuccessWhatsappOption_PhysicalCardOrderSuccessWhatsappOptionV1:
		return w.PhysicalCardOrderSuccessWhatsappOption.GetPhysicalCardOrderSuccessWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_CardPrintedWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.CardPrintedWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_CardPrintedWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.CardPrintedWhatsappOption.GetOption().(type) {
	case *CardPrintedWhatsappOption_CardPrintedWhatsappOptionV1:
		return map[string]string{
			"1": w.CardPrintedWhatsappOption.GetCardPrintedWhatsappOptionV1().GetCardName(),
			"2": w.CardPrintedWhatsappOption.GetCardPrintedWhatsappOptionV1().GetCardHolderName().GetFirstName(),
			"3": w.CardPrintedWhatsappOption.GetCardPrintedWhatsappOptionV1().GetCardName(),
			"4": w.CardPrintedWhatsappOption.GetCardPrintedWhatsappOptionV1().GetLastFourDigit(),
			"5": w.CardPrintedWhatsappOption.GetCardPrintedWhatsappOptionV1().GetTrackingLink(),
		}
	}
	return nil
}

func (w *WhatsappOption_CardPrintedWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.CardPrintedWhatsappOption.GetOption().(type) {
	case *CardPrintedWhatsappOption_CardPrintedWhatsappOptionV1:
		return w.CardPrintedWhatsappOption.GetCardPrintedWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_CardDispatchedWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.CardDispatchedWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_CardDispatchedWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.CardDispatchedWhatsappOption.GetOption().(type) {
	case *CardDispatchedWhatsappOption_CardDispatchedWhatsappOptionV1:
		return map[string]string{
			"1": w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV1().GetCardName_1(),
			"2": w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV1().GetCardHolderName().GetFirstName(),
			"3": w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV1().GetCardName_2(),
			"4": w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV1().GetLastFourDigit(),
			"5": w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV1().GetShippedDate().AsTime().In(datetime.IST).Format(DateFormat),
			"6": w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV1().GetTrackingLink(),
		}
	case *CardDispatchedWhatsappOption_CardDispatchedWhatsappOptionV2:
		return map[string]string{
			"1":  w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV2().GetCardName(),
			"2":  w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV2().GetCardHolderName().GetFirstName(),
			"3":  w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV2().GetCardName(),
			"4":  w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV2().GetLastFourDigit(),
			"5":  w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV2().GetShippedDate().AsTime().In(datetime.IST).Format(DateFormat),
			"6":  w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV2().GetCourierName(),
			"7":  w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV2().GetTrackingId(),
			"8":  w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV2().GetExpectedDeliveryDate().AsTime().In(datetime.IST).Format(DateFormat),
			"9":  w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV2().GetTrackingLink(),
			"10": w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV2().GetCourierName(),
		}
	}
	return nil
}

func (w *WhatsappOption_CardDispatchedWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.CardDispatchedWhatsappOption.GetOption().(type) {
	case *CardDispatchedWhatsappOption_CardDispatchedWhatsappOptionV1:
		return w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV1().GetTemplateVersion()
	case *CardDispatchedWhatsappOption_CardDispatchedWhatsappOptionV2:
		return w.CardDispatchedWhatsappOption.GetCardDispatchedWhatsappOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_CardDeliveredWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.CardDeliveredWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_CardDeliveredWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.CardDeliveredWhatsappOption.GetOption().(type) {
	case *CardDeliveredWhatsappOption_CardDeliveredWhatsappOptionV1:
		return map[string]string{
			"1": w.CardDeliveredWhatsappOption.GetCardDeliveredWhatsappOptionV1().GetCardHolderName().GetFirstName(),
			"2": w.CardDeliveredWhatsappOption.GetCardDeliveredWhatsappOptionV1().GetCardName_1(),
			"3": w.CardDeliveredWhatsappOption.GetCardDeliveredWhatsappOptionV1().GetLastFourDigit(),
			"4": w.CardDeliveredWhatsappOption.GetCardDeliveredWhatsappOptionV1().GetDeliveredOn().AsTime().In(datetime.IST).Format(DateFormat),
			"5": w.CardDeliveredWhatsappOption.GetCardDeliveredWhatsappOptionV1().GetCardName_2(),
			"6": w.CardDeliveredWhatsappOption.GetCardDeliveredWhatsappOptionV1().GetActivationLink(),
		}
	case *CardDeliveredWhatsappOption_CardDeliveredWhatsappOptionV2:
		return map[string]string{
			"1": w.CardDeliveredWhatsappOption.GetCardDeliveredWhatsappOptionV2().GetCardName(),
			"2": w.CardDeliveredWhatsappOption.GetCardDeliveredWhatsappOptionV2().GetCardName(),
			"3": w.CardDeliveredWhatsappOption.GetCardDeliveredWhatsappOptionV2().GetDeliveredOn().AsTime().In(datetime.IST).Format(DateFormat),
			"4": w.CardDeliveredWhatsappOption.GetCardDeliveredWhatsappOptionV2().GetActivationLink(),
		}
	}
	return nil
}

func (w *WhatsappOption_CardDeliveredWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.CardDeliveredWhatsappOption.GetOption().(type) {
	case *CardDeliveredWhatsappOption_CardDeliveredWhatsappOptionV1:
		return w.CardDeliveredWhatsappOption.GetCardDeliveredWhatsappOptionV1().GetTemplateVersion()
	case *CardDeliveredWhatsappOption_CardDeliveredWhatsappOptionV2:
		return w.CardDeliveredWhatsappOption.GetCardDeliveredWhatsappOptionV2().GetTemplateVersion()

	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_CardOutForDeliveryWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.CardOutForDeliveryWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_CardOutForDeliveryWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.CardOutForDeliveryWhatsappOption.GetOption().(type) {
	case *CardOutForDeliveryWhatsappOption_CardOutForDeliveryWhatsappOptionV1:
		return map[string]string{
			"1": w.CardOutForDeliveryWhatsappOption.GetCardOutForDeliveryWhatsappOptionV1().GetCardName_1(),
			"2": w.CardOutForDeliveryWhatsappOption.GetCardOutForDeliveryWhatsappOptionV1().GetCardHolderName().GetFirstName(),
			"3": w.CardOutForDeliveryWhatsappOption.GetCardOutForDeliveryWhatsappOptionV1().GetCardName_2(),
			"4": w.CardOutForDeliveryWhatsappOption.GetCardOutForDeliveryWhatsappOptionV1().GetLastFourDigit(),
			"5": w.CardOutForDeliveryWhatsappOption.GetCardOutForDeliveryWhatsappOptionV1().GetTrackingLink(),
		}
	case *CardOutForDeliveryWhatsappOption_CardOutForDeliveryWhatsappOptionV2:
		return map[string]string{
			"1": w.CardOutForDeliveryWhatsappOption.GetCardOutForDeliveryWhatsappOptionV2().GetCardName(),
			"2": w.CardOutForDeliveryWhatsappOption.GetCardOutForDeliveryWhatsappOptionV2().GetCardHolderName().GetFirstName(),
			"3": w.CardOutForDeliveryWhatsappOption.GetCardOutForDeliveryWhatsappOptionV2().GetCardName(),
			"4": w.CardOutForDeliveryWhatsappOption.GetCardOutForDeliveryWhatsappOptionV2().GetLastFourDigit(),
			"5": w.CardOutForDeliveryWhatsappOption.GetCardOutForDeliveryWhatsappOptionV2().GetCourierName(),
			"6": w.CardOutForDeliveryWhatsappOption.GetCardOutForDeliveryWhatsappOptionV2().GetTrackingId(),
			"7": w.CardOutForDeliveryWhatsappOption.GetCardOutForDeliveryWhatsappOptionV2().GetCourierName(),
		}
	}
	return nil
}

func (w *WhatsappOption_CardOutForDeliveryWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.CardOutForDeliveryWhatsappOption.GetOption().(type) {
	case *CardOutForDeliveryWhatsappOption_CardOutForDeliveryWhatsappOptionV1:
		return w.CardOutForDeliveryWhatsappOption.GetCardOutForDeliveryWhatsappOptionV1().GetTemplateVersion()
	case *CardOutForDeliveryWhatsappOption_CardOutForDeliveryWhatsappOptionV2:
		return w.CardOutForDeliveryWhatsappOption.GetCardOutForDeliveryWhatsappOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_CardReturnedToOriginWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.CardReturnedToOriginWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_CardReturnedToOriginWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.CardReturnedToOriginWhatsappOption.GetOption().(type) {
	case *CardReturnedToOriginWhatsappOption_CardReturnedToOriginWhatsappOptionV1:
		return map[string]string{
			"1": w.CardReturnedToOriginWhatsappOption.GetCardReturnedToOriginWhatsappOptionV1().GetCardHolderName().GetFirstName(),
			"2": w.CardReturnedToOriginWhatsappOption.GetCardReturnedToOriginWhatsappOptionV1().GetCardName_1(),
			"3": w.CardReturnedToOriginWhatsappOption.GetCardReturnedToOriginWhatsappOptionV1().GetLastFourDigit(),
			"4": w.CardReturnedToOriginWhatsappOption.GetCardReturnedToOriginWhatsappOptionV1().GetDeliveryAttemptedOn().AsTime().In(datetime.IST).Format(DateFormat),
			"5": w.CardReturnedToOriginWhatsappOption.GetCardReturnedToOriginWhatsappOptionV1().GetNextAttemptIn(),
		}
	}
	return nil
}

func (w *WhatsappOption_CardReturnedToOriginWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.CardReturnedToOriginWhatsappOption.GetOption().(type) {
	case *CardReturnedToOriginWhatsappOption_CardReturnedToOriginWhatsappOptionV1:
		return w.CardReturnedToOriginWhatsappOption.GetCardReturnedToOriginWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_DebitCardInsufficientFundsForTransactionWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.DebitCardInsufficientFundsForTransactionWhatsappOption.GetOption().(type) {
	case *DebitCardInsufficientFundsForTransactionWhatsappOption_DebitCardInsufficientFundsForTransactionWhatsappOptionV1:
		return w.DebitCardInsufficientFundsForTransactionWhatsappOption.GetDebitCardInsufficientFundsForTransactionWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_DebitCardInsufficientFundsForTransactionWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.DebitCardInsufficientFundsForTransactionWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_DebitCardInsufficientFundsForTransactionWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.DebitCardInsufficientFundsForTransactionWhatsappOption.GetOption().(type) {
	case *DebitCardInsufficientFundsForTransactionWhatsappOption_DebitCardInsufficientFundsForTransactionWhatsappOptionV1:
		return map[string]string{
			"1": w.DebitCardInsufficientFundsForTransactionWhatsappOption.GetDebitCardInsufficientFundsForTransactionWhatsappOptionV1().GetCardHolderName().ToString(),
			"2": w.DebitCardInsufficientFundsForTransactionWhatsappOption.GetDebitCardInsufficientFundsForTransactionWhatsappOptionV1().GetLastFourDigits(),
			"3": w.DebitCardInsufficientFundsForTransactionWhatsappOption.GetDebitCardInsufficientFundsForTransactionWhatsappOptionV1().GetTxnTime().AsTime().In(datetime.IST).Format(DateFormat),
			"4": w.DebitCardInsufficientFundsForTransactionWhatsappOption.GetDebitCardInsufficientFundsForTransactionWhatsappOptionV1().GetTxnTime().AsTime().In(datetime.IST).Format(datetime.TWENTY_FOUR_HOUR_LAYOUT),
		}
	}
	return nil
}

func (w *WhatsappOption_DebitCardInternationalTransactionsNotEnabledWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.DebitCardInternationalTransactionsNotEnabledWhatsappOption.GetOption().(type) {
	case *DebitCardInternationalTransactionsNotEnabledWhatsappOption_DebitCardInternationalTransactionsNotEnabledWhatsappOptionV1:
		return w.DebitCardInternationalTransactionsNotEnabledWhatsappOption.GetDebitCardInternationalTransactionsNotEnabledWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_DebitCardInternationalTransactionsNotEnabledWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.DebitCardInternationalTransactionsNotEnabledWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_DebitCardInternationalTransactionsNotEnabledWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.DebitCardInternationalTransactionsNotEnabledWhatsappOption.GetOption().(type) {
	case *DebitCardInternationalTransactionsNotEnabledWhatsappOption_DebitCardInternationalTransactionsNotEnabledWhatsappOptionV1:
		return map[string]string{
			"1": w.DebitCardInternationalTransactionsNotEnabledWhatsappOption.GetDebitCardInternationalTransactionsNotEnabledWhatsappOptionV1().GetCardHolderName().ToString(),
			"2": w.DebitCardInternationalTransactionsNotEnabledWhatsappOption.GetDebitCardInternationalTransactionsNotEnabledWhatsappOptionV1().GetLastFourDigits(),
			"3": w.DebitCardInternationalTransactionsNotEnabledWhatsappOption.GetDebitCardInternationalTransactionsNotEnabledWhatsappOptionV1().GetTxnTime().AsTime().In(datetime.IST).Format(DateFormat),
			"4": w.DebitCardInternationalTransactionsNotEnabledWhatsappOption.GetDebitCardInternationalTransactionsNotEnabledWhatsappOptionV1().GetTxnTime().AsTime().In(datetime.IST).Format(datetime.TWENTY_FOUR_HOUR_LAYOUT),
		}
	}
	return nil
}

func (w *WhatsappOption_DebitCardContactlessCardUsageNotEnabledWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.DebitCardContactlessCardUsageNotEnabledWhatsappOption.GetOption().(type) {
	case *DebitCardContactlessCardUsageNotEnabledWhatsappOption_DebitCardContactlessCardUsageNotEnabledWhatsappOptionV1:
		return w.DebitCardContactlessCardUsageNotEnabledWhatsappOption.GetDebitCardContactlessCardUsageNotEnabledWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_DebitCardContactlessCardUsageNotEnabledWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.DebitCardContactlessCardUsageNotEnabledWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_DebitCardContactlessCardUsageNotEnabledWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.DebitCardContactlessCardUsageNotEnabledWhatsappOption.GetOption().(type) {
	case *DebitCardContactlessCardUsageNotEnabledWhatsappOption_DebitCardContactlessCardUsageNotEnabledWhatsappOptionV1:
		return map[string]string{
			"1": w.DebitCardContactlessCardUsageNotEnabledWhatsappOption.GetDebitCardContactlessCardUsageNotEnabledWhatsappOptionV1().GetCardHolderName().ToString(),
			"2": w.DebitCardContactlessCardUsageNotEnabledWhatsappOption.GetDebitCardContactlessCardUsageNotEnabledWhatsappOptionV1().GetLastFourDigits(),
			"3": w.DebitCardContactlessCardUsageNotEnabledWhatsappOption.GetDebitCardContactlessCardUsageNotEnabledWhatsappOptionV1().GetTxnTime().AsTime().In(datetime.IST).Format(DateFormat),
			"4": w.DebitCardContactlessCardUsageNotEnabledWhatsappOption.GetDebitCardContactlessCardUsageNotEnabledWhatsappOptionV1().GetTxnTime().AsTime().In(datetime.IST).Format(datetime.TWENTY_FOUR_HOUR_LAYOUT),
		}
	}
	return nil
}

func (w *WhatsappOption_DebitCardPosUsageNotEnabledWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.DebitCardPosUsageNotEnabledWhatsappOption.GetOption().(type) {
	case *DebitCardPosUsageNotEnabledWhatsappOption_DebitCardPosUsageNotEnabledWhatsappOptionV1:
		return w.DebitCardPosUsageNotEnabledWhatsappOption.GetDebitCardPosUsageNotEnabledWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_DebitCardPosUsageNotEnabledWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.DebitCardPosUsageNotEnabledWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_DebitCardPosUsageNotEnabledWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.DebitCardPosUsageNotEnabledWhatsappOption.GetOption().(type) {
	case *DebitCardPosUsageNotEnabledWhatsappOption_DebitCardPosUsageNotEnabledWhatsappOptionV1:
		return map[string]string{
			"1": w.DebitCardPosUsageNotEnabledWhatsappOption.GetDebitCardPosUsageNotEnabledWhatsappOptionV1().GetCardHolderName().ToString(),
			"2": w.DebitCardPosUsageNotEnabledWhatsappOption.GetDebitCardPosUsageNotEnabledWhatsappOptionV1().GetLastFourDigits(),
			"3": w.DebitCardPosUsageNotEnabledWhatsappOption.GetDebitCardPosUsageNotEnabledWhatsappOptionV1().GetTxnTime().AsTime().In(datetime.IST).Format(DateFormat),
			"4": w.DebitCardPosUsageNotEnabledWhatsappOption.GetDebitCardPosUsageNotEnabledWhatsappOptionV1().GetTxnTime().AsTime().In(datetime.IST).Format(datetime.TWENTY_FOUR_HOUR_LAYOUT),
		}
	}
	return nil
}

func (w *WhatsappOption_DebitCardAtmUsageNotEnabledWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.DebitCardAtmUsageNotEnabledWhatsappOption.GetOption().(type) {
	case *DebitCardAtmUsageNotEnabledWhatsappOption_DebitCardAtmUsageNotEnabledWhatsappOptionV1:
		return w.DebitCardAtmUsageNotEnabledWhatsappOption.GetDebitCardAtmUsageNotEnabledWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_DebitCardAtmUsageNotEnabledWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.DebitCardAtmUsageNotEnabledWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_DebitCardAtmUsageNotEnabledWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.DebitCardAtmUsageNotEnabledWhatsappOption.GetOption().(type) {
	case *DebitCardAtmUsageNotEnabledWhatsappOption_DebitCardAtmUsageNotEnabledWhatsappOptionV1:
		return map[string]string{
			"1": w.DebitCardAtmUsageNotEnabledWhatsappOption.GetDebitCardAtmUsageNotEnabledWhatsappOptionV1().GetCardHolderName().ToString(),
			"2": w.DebitCardAtmUsageNotEnabledWhatsappOption.GetDebitCardAtmUsageNotEnabledWhatsappOptionV1().GetLastFourDigits(),
			"3": w.DebitCardAtmUsageNotEnabledWhatsappOption.GetDebitCardAtmUsageNotEnabledWhatsappOptionV1().GetTxnTime().AsTime().In(datetime.IST).Format(DateFormat),
			"4": w.DebitCardAtmUsageNotEnabledWhatsappOption.GetDebitCardAtmUsageNotEnabledWhatsappOptionV1().GetTxnTime().AsTime().In(datetime.IST).Format(datetime.TWENTY_FOUR_HOUR_LAYOUT),
		}
	}
	return nil
}

func (w *WhatsappOption_DebitCardIntlDailyAllowedWithdrawalLimitReached) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.DebitCardIntlDailyAllowedWithdrawalLimitReached.GetOption().(type) {
	case *DebitCardIntlDailyAllowedWithdrawalLimitReached_DebitCardIntlDailyAllowedWithdrawalLimitReachedV1:
		return w.DebitCardIntlDailyAllowedWithdrawalLimitReached.GetDebitCardIntlDailyAllowedWithdrawalLimitReachedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_DebitCardIntlDailyAllowedWithdrawalLimitReached) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.DebitCardIntlDailyAllowedWithdrawalLimitReached.GetWhatsappType()
}

func (w *WhatsappOption_DebitCardIntlDailyAllowedWithdrawalLimitReached) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.DebitCardIntlDailyAllowedWithdrawalLimitReached.GetOption().(type) {
	case *DebitCardIntlDailyAllowedWithdrawalLimitReached_DebitCardIntlDailyAllowedWithdrawalLimitReachedV1:
		return map[string]string{
			"1": w.DebitCardIntlDailyAllowedWithdrawalLimitReached.GetDebitCardIntlDailyAllowedWithdrawalLimitReachedV1().GetCardHolderName().ToString(),
			"2": w.DebitCardIntlDailyAllowedWithdrawalLimitReached.GetDebitCardIntlDailyAllowedWithdrawalLimitReachedV1().GetCountry(),
			"3": w.DebitCardIntlDailyAllowedWithdrawalLimitReached.GetDebitCardIntlDailyAllowedWithdrawalLimitReachedV1().GetLastFourDigits(),
			"4": w.DebitCardIntlDailyAllowedWithdrawalLimitReached.GetDebitCardIntlDailyAllowedWithdrawalLimitReachedV1().GetTxnTime().AsTime().In(datetime.IST).Format(DateFormat),
			"5": w.DebitCardIntlDailyAllowedWithdrawalLimitReached.GetDebitCardIntlDailyAllowedWithdrawalLimitReachedV1().GetTxnTime().AsTime().In(datetime.IST).Format(datetime.TWENTY_FOUR_HOUR_LAYOUT),
			"6": money.ToDisplayStringWithoutSymbol(w.DebitCardIntlDailyAllowedWithdrawalLimitReached.GetDebitCardIntlDailyAllowedWithdrawalLimitReachedV1().GetCurrentLimit()),
			"7": money.ToDisplayStringWithoutSymbol(w.DebitCardIntlDailyAllowedWithdrawalLimitReached.GetDebitCardIntlDailyAllowedWithdrawalLimitReachedV1().GetMaxLimit()),
			"8": w.DebitCardIntlDailyAllowedWithdrawalLimitReached.GetDebitCardIntlDailyAllowedWithdrawalLimitReachedV1().GetRedirectionUrl(),
		}
	}
	return nil
}

func (w *WhatsappOption_DebitCardIntlDailyMaxWithdrawalLimitReached) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.DebitCardIntlDailyMaxWithdrawalLimitReached.GetOption().(type) {
	case *DebitCardIntlDailyMaxWithdrawalLimitReached_DebitCardIntlDailyMaxWithdrawalLimitReachedV1:
		return w.DebitCardIntlDailyMaxWithdrawalLimitReached.GetDebitCardIntlDailyMaxWithdrawalLimitReachedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_DebitCardIntlDailyMaxWithdrawalLimitReached) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.DebitCardIntlDailyMaxWithdrawalLimitReached.GetWhatsappType()
}

func (w *WhatsappOption_DebitCardIntlDailyMaxWithdrawalLimitReached) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.DebitCardIntlDailyMaxWithdrawalLimitReached.GetOption().(type) {
	case *DebitCardIntlDailyMaxWithdrawalLimitReached_DebitCardIntlDailyMaxWithdrawalLimitReachedV1:
		return map[string]string{
			"1": w.DebitCardIntlDailyMaxWithdrawalLimitReached.GetDebitCardIntlDailyMaxWithdrawalLimitReachedV1().GetCardHolderName().ToString(),
			"2": w.DebitCardIntlDailyMaxWithdrawalLimitReached.GetDebitCardIntlDailyMaxWithdrawalLimitReachedV1().GetCountry(),
			"3": w.DebitCardIntlDailyMaxWithdrawalLimitReached.GetDebitCardIntlDailyMaxWithdrawalLimitReachedV1().GetLastFourDigits(),
			"4": w.DebitCardIntlDailyMaxWithdrawalLimitReached.GetDebitCardIntlDailyMaxWithdrawalLimitReachedV1().GetTxnTime().AsTime().In(datetime.IST).Format(DateFormat),
			"5": w.DebitCardIntlDailyMaxWithdrawalLimitReached.GetDebitCardIntlDailyMaxWithdrawalLimitReachedV1().GetTxnTime().AsTime().In(datetime.IST).Format(datetime.TWENTY_FOUR_HOUR_LAYOUT),
			"6": w.DebitCardIntlDailyMaxWithdrawalLimitReached.GetDebitCardIntlDailyMaxWithdrawalLimitReachedV1().GetCountry(),
			"7": w.DebitCardIntlDailyMaxWithdrawalLimitReached.GetDebitCardIntlDailyMaxWithdrawalLimitReachedV1().GetCountry(),
			"8": money.ToDisplayStringWithoutSymbol(w.DebitCardIntlDailyMaxWithdrawalLimitReached.GetDebitCardIntlDailyMaxWithdrawalLimitReachedV1().GetMaxLimit()),
		}
	}
	return nil
}

func (w *WhatsappOption_FirstDefaultOptInWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.FirstDefaultOptInWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_FirstDefaultOptInWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.FirstDefaultOptInWhatsappOption.GetOption().(type) {
	case *FirstDefaultOptInWhatsappOption_FirstDefaultOptInWhatsappOptionV1:
		return nil
	case *FirstDefaultOptInWhatsappOption_FirstDefaultOptInWhatsappOptionV2:
		return map[string]string{
			"1": w.FirstDefaultOptInWhatsappOption.GetFirstDefaultOptInWhatsappOptionV2().GetFirstName(),
		}
	case *FirstDefaultOptInWhatsappOption_FirstDefaultOptInWhatsappOptionV3:
		return map[string]string{
			"1": w.FirstDefaultOptInWhatsappOption.GetFirstDefaultOptInWhatsappOptionV3().GetFirstName(),
		}
	}
	return nil
}

func (w *WhatsappOption_FirstDefaultOptInWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.FirstDefaultOptInWhatsappOption.GetOption().(type) {
	case *FirstDefaultOptInWhatsappOption_FirstDefaultOptInWhatsappOptionV1:
		return w.FirstDefaultOptInWhatsappOption.GetFirstDefaultOptInWhatsappOptionV1().GetTemplateVersion()
	case *FirstDefaultOptInWhatsappOption_FirstDefaultOptInWhatsappOptionV2:
		return w.FirstDefaultOptInWhatsappOption.GetFirstDefaultOptInWhatsappOptionV2().GetTemplateVersion()
	case *FirstDefaultOptInWhatsappOption_FirstDefaultOptInWhatsappOptionV3:
		return w.FirstDefaultOptInWhatsappOption.GetFirstDefaultOptInWhatsappOptionV3().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_OptOutWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.OptOutWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_OptOutWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.OptOutWhatsappOption.GetOption().(type) {
	case *OptOutWhatsappOption_OptOutWhatsappOptionV1:
		return nil
	}
	return nil
}

func (w *WhatsappOption_OptOutWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.OptOutWhatsappOption.GetOption().(type) {
	case *OptOutWhatsappOption_OptOutWhatsappOptionV1:
		return w.OptOutWhatsappOption.GetOptOutWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_OptBackInWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.OptBackInWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_OptBackInWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.OptBackInWhatsappOption.GetOption().(type) {
	case *OptBackInWhatsappOption_OptBackInWhatsappOptionV1:
		return nil
	}
	return nil
}

func (w *WhatsappOption_OptBackInWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.OptBackInWhatsappOption.GetOption().(type) {
	case *OptBackInWhatsappOption_OptBackInWhatsappOptionV1:
		return w.OptBackInWhatsappOption.GetOptBackInWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_GenericReplyWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.GenericReplyWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_GenericReplyWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.GenericReplyWhatsappOption.GetOption().(type) {
	case *GenericReplyWhatsappOption_GenericReplyWhatsappOptionV1:
		return nil
	}
	return nil
}

func (w *WhatsappOption_GenericReplyWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.GenericReplyWhatsappOption.GetOption().(type) {
	case *GenericReplyWhatsappOption_GenericReplyWhatsappOptionV1:
		return w.GenericReplyWhatsappOption.GetGenericReplyWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_FiEarlyAccessCboWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.FiEarlyAccessCboWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_FiEarlyAccessCboWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.FiEarlyAccessCboWhatsappOption.GetOption().(type) {
	case *FiEarlyAccessCBOWhatsappOption_FiEarlyAccessCboWhatsappOptionV1:
		return nil
	}
	return nil
}

func (w *WhatsappOption_FiEarlyAccessCboWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.FiEarlyAccessCboWhatsappOption.GetOption().(type) {
	case *FiEarlyAccessCBOWhatsappOption_FiEarlyAccessCboWhatsappOptionV1:
		return w.FiEarlyAccessCboWhatsappOption.GetFiEarlyAccessCboWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_FiEarlyAccessNonCboWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.FiEarlyAccessNonCboWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_FiEarlyAccessNonCboWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.FiEarlyAccessNonCboWhatsappOption.GetOption().(type) {
	case *FiEarlyAccessNonCBOWhatsappOption_FiEarlyAccessNonCboWhatsappOptionV1:
		return nil
	}
	return nil
}

func (w *WhatsappOption_FiEarlyAccessNonCboWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.FiEarlyAccessNonCboWhatsappOption.GetOption().(type) {
	case *FiEarlyAccessNonCBOWhatsappOption_FiEarlyAccessNonCboWhatsappOptionV1:
		return w.FiEarlyAccessNonCboWhatsappOption.GetFiEarlyAccessNonCboWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_FiNormalAccessWithoutPlaystoreWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.FiNormalAccessWithoutPlaystoreWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_FiNormalAccessWithoutPlaystoreWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.FiNormalAccessWithoutPlaystoreWhatsappOption.GetOption().(type) {
	case *FiNormalAccessWithoutPlaystoreWhatsappOption_FiNormalAccessWithoutPlaystoreWhatsappOptionV1:
		return nil
	}
	return nil
}

func (w *WhatsappOption_FiNormalAccessWithoutPlaystoreWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.FiNormalAccessWithoutPlaystoreWhatsappOption.GetOption().(type) {
	case *FiNormalAccessWithoutPlaystoreWhatsappOption_FiNormalAccessWithoutPlaystoreWhatsappOptionV1:
		return w.FiNormalAccessWithoutPlaystoreWhatsappOption.GetFiNormalAccessWithoutPlaystoreWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_FiNormalAccessWithPlaystoreWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.FiNormalAccessWithPlaystoreWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_FiNormalAccessWithPlaystoreWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.FiNormalAccessWithPlaystoreWhatsappOption.GetOption().(type) {
	case *FiNormalAccessWithPlaystoreWhatsappOption_FiNormalAccessWithPlaystoreWhatsappOptionV1:
		return nil
	}
	return nil
}

func (w *WhatsappOption_FiNormalAccessWithPlaystoreWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.FiNormalAccessWithPlaystoreWhatsappOption.GetOption().(type) {
	case *FiNormalAccessWithPlaystoreWhatsappOption_FiNormalAccessWithPlaystoreWhatsappOptionV1:
		return w.FiNormalAccessWithPlaystoreWhatsappOption.GetFiNormalAccessWithPlaystoreWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_FiCboAccessWithoutPlaystoreWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.FiCboAccessWithoutPlaystoreWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_FiCboAccessWithoutPlaystoreWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.FiCboAccessWithoutPlaystoreWhatsappOption.GetOption().(type) {
	case *FiCBOAccessWithoutPlaystoreWhatsappOption_FiCboAccessWithoutPlaystoreWhatsappOptionV1:
		return nil
	}
	return nil
}

func (w *WhatsappOption_FiCboAccessWithoutPlaystoreWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.FiCboAccessWithoutPlaystoreWhatsappOption.GetOption().(type) {
	case *FiCBOAccessWithoutPlaystoreWhatsappOption_FiCboAccessWithoutPlaystoreWhatsappOptionV1:
		return w.FiCboAccessWithoutPlaystoreWhatsappOption.GetFiCboAccessWithoutPlaystoreWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_FiCboAccessWithPlaystoreWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.FiCboAccessWithPlaystoreWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_FiCboAccessWithPlaystoreWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.FiCboAccessWithPlaystoreWhatsappOption.GetOption().(type) {
	case *FiCBOAccessWithPlaystoreWhatsappOption_FiCboAccessWithPlaystoreWhatsappOptionV1:
		return nil
	}
	return nil
}

func (w *WhatsappOption_FiCboAccessWithPlaystoreWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.FiCboAccessWithPlaystoreWhatsappOption.GetOption().(type) {
	case *FiCBOAccessWithPlaystoreWhatsappOption_FiCboAccessWithPlaystoreWhatsappOptionV1:
		return w.FiCboAccessWithPlaystoreWhatsappOption.GetFiCboAccessWithPlaystoreWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_VkycFirstReminderWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.VkycFirstReminderWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_VkycFirstReminderWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.VkycFirstReminderWhatsappOption.GetOption().(type) {
	case *VKYCFirstReminderWhatsappOption_VkycFirstReminderWhatsappOptionV1:
		return nil
	}
	return nil
}

func (w *WhatsappOption_VkycFirstReminderWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.VkycFirstReminderWhatsappOption.GetOption().(type) {
	case *VKYCFirstReminderWhatsappOption_VkycFirstReminderWhatsappOptionV1:
		return w.VkycFirstReminderWhatsappOption.GetVkycFirstReminderWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_VkycSecondReminderWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.VkycSecondReminderWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_VkycSecondReminderWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.VkycSecondReminderWhatsappOption.GetOption().(type) {
	case *VKYCSecondReminderWhatsappOption_VkycSecondReminderWhatsappOptionV1:
		return map[string]string{
			"1": w.VkycSecondReminderWhatsappOption.GetVkycSecondReminderWhatsappOptionV1().GetFirstName(),
			"2": w.VkycSecondReminderWhatsappOption.GetVkycSecondReminderWhatsappOptionV1().GetAccountFreezeDate().AsTime().Format("January 2, 2006"),
		}
	}
	return nil
}

func (w *WhatsappOption_VkycSecondReminderWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.VkycSecondReminderWhatsappOption.GetOption().(type) {
	case *VKYCSecondReminderWhatsappOption_VkycSecondReminderWhatsappOptionV1:
		return w.VkycSecondReminderWhatsappOption.GetVkycSecondReminderWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_VkycThirdReminderWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.VkycThirdReminderWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_VkycThirdReminderWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.VkycThirdReminderWhatsappOption.GetOption().(type) {
	case *VKYCThirdReminderWhatsappOption_VkycThirdReminderWhatsappOptionV1:
		return map[string]string{
			"1": w.VkycThirdReminderWhatsappOption.GetVkycThirdReminderWhatsappOptionV1().GetAccountFreezeDate().AsTime().Format("January 2, 2006"),
		}
	}
	return nil
}

func (w *WhatsappOption_VkycThirdReminderWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.VkycThirdReminderWhatsappOption.GetOption().(type) {
	case *VKYCThirdReminderWhatsappOption_VkycThirdReminderWhatsappOptionV1:
		return w.VkycThirdReminderWhatsappOption.GetVkycThirdReminderWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_FiEarlyAccessFiniteCodeWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.FiEarlyAccessFiniteCodeWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_FiEarlyAccessFiniteCodeWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.FiEarlyAccessFiniteCodeWhatsappOption.GetOption().(type) {
	case *FiEarlyAccessFiniteCodeWhatsappOption_FiEarlyAccessFiniteCodeWhatsappOptionV1:
		return map[string]string{
			"1": w.FiEarlyAccessFiniteCodeWhatsappOption.GetFiEarlyAccessFiniteCodeWhatsappOptionV1().GetFirstName(),
			"2": w.FiEarlyAccessFiniteCodeWhatsappOption.GetFiEarlyAccessFiniteCodeWhatsappOptionV1().GetFiniteCode(),
		}
	}
	return nil
}

func (w *WhatsappOption_FiEarlyAccessFiniteCodeWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.FiEarlyAccessFiniteCodeWhatsappOption.GetOption().(type) {
	case *FiEarlyAccessFiniteCodeWhatsappOption_FiEarlyAccessFiniteCodeWhatsappOptionV1:
		return w.FiEarlyAccessFiniteCodeWhatsappOption.GetFiEarlyAccessFiniteCodeWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_FiCboAccessFiniteCodeWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.FiCboAccessFiniteCodeWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_FiCboAccessFiniteCodeWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.FiCboAccessFiniteCodeWhatsappOption.GetOption().(type) {
	case *FiCboAccessFiniteCodeWhatsappOption_FiCboAccessFiniteCodeWhatsappOptionV1:
		return map[string]string{
			"1": w.FiCboAccessFiniteCodeWhatsappOption.GetFiCboAccessFiniteCodeWhatsappOptionV1().GetFirstName(),
			"2": w.FiCboAccessFiniteCodeWhatsappOption.GetFiCboAccessFiniteCodeWhatsappOptionV1().GetFiniteCode(),
		}
	}
	return nil
}

func (w *WhatsappOption_FiCboAccessFiniteCodeWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.FiCboAccessFiniteCodeWhatsappOption.GetOption().(type) {
	case *FiCboAccessFiniteCodeWhatsappOption_FiCboAccessFiniteCodeWhatsappOptionV1:
		return w.FiCboAccessFiniteCodeWhatsappOption.GetFiCboAccessFiniteCodeWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_EarlyAccessFiniteCodeWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.EarlyAccessFiniteCodeWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_EarlyAccessFiniteCodeWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.EarlyAccessFiniteCodeWhatsappOption.GetOption().(type) {
	case *EarlyAccessFiniteCodeWhatsappOption_EarlyAccessFiniteCodeWhatsappOptionV1:
		return map[string]string{
			"1": w.EarlyAccessFiniteCodeWhatsappOption.GetEarlyAccessFiniteCodeWhatsappOptionV1().GetName(),
			"2": w.EarlyAccessFiniteCodeWhatsappOption.GetEarlyAccessFiniteCodeWhatsappOptionV1().GetFiniteCode(),
		}
	}
	return nil
}

func (w *WhatsappOption_EarlyAccessFiniteCodeWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.EarlyAccessFiniteCodeWhatsappOption.GetOption().(type) {
	case *EarlyAccessFiniteCodeWhatsappOption_EarlyAccessFiniteCodeWhatsappOptionV1:
		return w.EarlyAccessFiniteCodeWhatsappOption.GetEarlyAccessFiniteCodeWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_EarlyAccessCboFiniteCodeWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.EarlyAccessCboFiniteCodeWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_EarlyAccessCboFiniteCodeWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.EarlyAccessCboFiniteCodeWhatsappOption.GetOption().(type) {
	case *EarlyAccessCboFiniteCodeWhatsappOption_EarlyAccessCboFiniteCodeWhatsappOptionV1:
		return map[string]string{
			"1": w.EarlyAccessCboFiniteCodeWhatsappOption.GetEarlyAccessCboFiniteCodeWhatsappOptionV1().GetName(),
			"2": w.EarlyAccessCboFiniteCodeWhatsappOption.GetEarlyAccessCboFiniteCodeWhatsappOptionV1().GetFiniteCode(),
		}
	}
	return nil
}

func (w *WhatsappOption_EarlyAccessCboFiniteCodeWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.EarlyAccessCboFiniteCodeWhatsappOption.GetOption().(type) {
	case *EarlyAccessCboFiniteCodeWhatsappOption_EarlyAccessCboFiniteCodeWhatsappOptionV1:
		return w.EarlyAccessCboFiniteCodeWhatsappOption.GetEarlyAccessCboFiniteCodeWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_VkycNewUserReminderWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.VkycNewUserReminderWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_VkycNewUserReminderWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.VkycNewUserReminderWhatsappOption.GetOption().(type) {
	case *VKYCNewUserReminderWhatsappOption_VkycNewUserReminderWhatsappOptionV1:
		return map[string]string{
			"1": w.VkycNewUserReminderWhatsappOption.GetVkycNewUserReminderWhatsappOptionV1().GetFirstName(),
		}
	case *VKYCNewUserReminderWhatsappOption_VkycNewUserReminderWhatsappOptionV2:
		return map[string]string{
			"1": w.VkycNewUserReminderWhatsappOption.GetVkycNewUserReminderWhatsappOptionV2().GetFirstName(),
			"2": w.VkycNewUserReminderWhatsappOption.GetVkycNewUserReminderWhatsappOptionV2().GetTransactionLimit(),
		}
	}
	return nil
}

func (w *WhatsappOption_VkycNewUserReminderWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.VkycNewUserReminderWhatsappOption.GetOption().(type) {
	case *VKYCNewUserReminderWhatsappOption_VkycNewUserReminderWhatsappOptionV1:
		return w.VkycNewUserReminderWhatsappOption.GetVkycNewUserReminderWhatsappOptionV1().GetTemplateVersion()
	case *VKYCNewUserReminderWhatsappOption_VkycNewUserReminderWhatsappOptionV2:
		return w.VkycNewUserReminderWhatsappOption.GetVkycNewUserReminderWhatsappOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_Vkyc_12MValidityReminderWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.Vkyc_12MValidityReminderWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_Vkyc_12MValidityReminderWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.Vkyc_12MValidityReminderWhatsappOption.GetOption().(type) {
	case *VKYC12MValidityReminderWhatsappOption_Vkyc_12MValidityReminderWhatsappOptionV1:
		return map[string]string{
			"1": w.Vkyc_12MValidityReminderWhatsappOption.GetVkyc_12MValidityReminderWhatsappOptionV1().GetFirstName(),
			"2": w.Vkyc_12MValidityReminderWhatsappOption.GetVkyc_12MValidityReminderWhatsappOptionV1().GetMonths(),
		}
	}
	return nil
}

func (w *WhatsappOption_Vkyc_12MValidityReminderWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.Vkyc_12MValidityReminderWhatsappOption.GetOption().(type) {
	case *VKYC12MValidityReminderWhatsappOption_Vkyc_12MValidityReminderWhatsappOptionV1:
		return w.Vkyc_12MValidityReminderWhatsappOption.GetVkyc_12MValidityReminderWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_InvestInMfReminderWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.InvestInMfReminderWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_InvestInMfReminderWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	amountReceived := w.InvestInMfReminderWhatsappOption.GetInvestInMfReminderWhatsappOptionV1().GetAmountReceived()
	switch w.InvestInMfReminderWhatsappOption.GetOption().(type) {
	case *InvestInMfReminderWhatsappOption_InvestInMfReminderWhatsappOptionV1:
		return map[string]string{
			"1": w.InvestInMfReminderWhatsappOption.GetInvestInMfReminderWhatsappOptionV1().GetFirstName(),
			"2": money.ToDisplayStringWithoutSymbol(amountReceived),
			"3": w.InvestInMfReminderWhatsappOption.GetInvestInMfReminderWhatsappOptionV1().GetDeeplink(),
		}
	}
	return nil
}
func (w *WhatsappOption_InvestInMfReminderWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.InvestInMfReminderWhatsappOption.GetOption().(type) {
	case *InvestInMfReminderWhatsappOption_InvestInMfReminderWhatsappOptionV1:
		return w.InvestInMfReminderWhatsappOption.GetInvestInMfReminderWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_DobAndPanDropOffWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.DobAndPanDropOffWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_DobAndPanDropOffWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.DobAndPanDropOffWhatsappOption.GetOption().(type) {
	case *DOBAndPANDropOffWhatsappOption_DobAndPanDropOffWhatsappOptionV1:
		return map[string]string{
			"1": w.DobAndPanDropOffWhatsappOption.GetDobAndPanDropOffWhatsappOptionV1().GetFirstName(),
			"2": w.DobAndPanDropOffWhatsappOption.GetDobAndPanDropOffWhatsappOptionV1().GetRedirectLink(),
		}
	case *DOBAndPANDropOffWhatsappOption_DobAndPanDropOffWhatsappOptionV2:
		return nil

	}
	return nil
}

func (w *WhatsappOption_DobAndPanDropOffWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.DobAndPanDropOffWhatsappOption.GetOption().(type) {
	case *DOBAndPANDropOffWhatsappOption_DobAndPanDropOffWhatsappOptionV1:
		return w.DobAndPanDropOffWhatsappOption.GetDobAndPanDropOffWhatsappOptionV1().GetTemplateVersion()
	case *DOBAndPANDropOffWhatsappOption_DobAndPanDropOffWhatsappOptionV2:
		return w.DobAndPanDropOffWhatsappOption.GetDobAndPanDropOffWhatsappOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_WebOnboardingCompletedWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.WebOnboardingCompletedWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_WebOnboardingCompletedWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.WebOnboardingCompletedWhatsappOption.GetOption().(type) {
	case *WebOnboardingCompletedWhatsappOption_WebOnboardingCompletedWhatsappOptionV1:
		return map[string]string{
			"1": w.WebOnboardingCompletedWhatsappOption.GetWebOnboardingCompletedWhatsappOptionV1().GetRedirectLink(),
		}
	}
	return nil
}

func (w *WhatsappOption_WebOnboardingCompletedWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.WebOnboardingCompletedWhatsappOption.GetOption().(type) {
	case *WebOnboardingCompletedWhatsappOption_WebOnboardingCompletedWhatsappOptionV1:
		return w.WebOnboardingCompletedWhatsappOption.GetWebOnboardingCompletedWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_VkycDropOffFirstReminderWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.VkycDropOffFirstReminderWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_VkycDropOffFirstReminderWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.VkycDropOffFirstReminderWhatsappOption.GetOption().(type) {
	case *VKYCDropOffFirstReminderWhatsappOption_VkycDropOffFirstReminderWhatsappOptionV1:
		return map[string]string{
			"1": w.VkycDropOffFirstReminderWhatsappOption.GetVkycDropOffFirstReminderWhatsappOptionV1().GetFirstName(),
			"2": w.VkycDropOffFirstReminderWhatsappOption.GetVkycDropOffFirstReminderWhatsappOptionV1().GetRedirectLink(),
		}
	}
	return nil
}

func (w *WhatsappOption_VkycDropOffFirstReminderWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.VkycDropOffFirstReminderWhatsappOption.GetOption().(type) {
	case *VKYCDropOffFirstReminderWhatsappOption_VkycDropOffFirstReminderWhatsappOptionV1:
		return w.VkycDropOffFirstReminderWhatsappOption.GetVkycDropOffFirstReminderWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_VkycDropOffSecondReminderWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.VkycDropOffSecondReminderWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_VkycDropOffSecondReminderWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.VkycDropOffSecondReminderWhatsappOption.GetOption().(type) {
	case *VKYCDropOffSecondReminderWhatsappOption_VkycDropOffSecondReminderWhatsappOptionV1:
		return map[string]string{
			"1": w.VkycDropOffSecondReminderWhatsappOption.GetVkycDropOffSecondReminderWhatsappOptionV1().GetFirstName(),
			"2": w.VkycDropOffSecondReminderWhatsappOption.GetVkycDropOffSecondReminderWhatsappOptionV1().GetRedirectLink(),
		}
	}
	return nil
}

func (w *WhatsappOption_VkycDropOffSecondReminderWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.VkycDropOffSecondReminderWhatsappOption.GetOption().(type) {
	case *VKYCDropOffSecondReminderWhatsappOption_VkycDropOffSecondReminderWhatsappOptionV1:
		return w.VkycDropOffSecondReminderWhatsappOption.GetVkycDropOffSecondReminderWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_CategorySpendsExceededReminderWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.CategorySpendsExceededReminderWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_CategorySpendsExceededReminderWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.CategorySpendsExceededReminderWhatsappOption.GetOption().(type) {
	case *CategorySpendsExceededReminderWhatsappOption_ReminderWhatsappOptionV1:
		return map[string]string{
			"1": w.CategorySpendsExceededReminderWhatsappOption.GetReminderWhatsappOptionV1().GetAmount(),
			"2": w.CategorySpendsExceededReminderWhatsappOption.GetReminderWhatsappOptionV1().GetCategory(),
			"3": w.CategorySpendsExceededReminderWhatsappOption.GetReminderWhatsappOptionV1().GetDeeplink(),
		}
	case *CategorySpendsExceededReminderWhatsappOption_ReminderCategorySpendsWhatsappOption:
		return map[string]string{
			"1": w.CategorySpendsExceededReminderWhatsappOption.GetReminderCategorySpendsWhatsappOption().GetConfiguredAmount(),
			"2": w.CategorySpendsExceededReminderWhatsappOption.GetReminderCategorySpendsWhatsappOption().GetCategory(),
			"3": w.CategorySpendsExceededReminderWhatsappOption.GetReminderCategorySpendsWhatsappOption().GetAmount(),
			"4": w.CategorySpendsExceededReminderWhatsappOption.GetReminderCategorySpendsWhatsappOption().GetDeeplink(),
		}
	}

	return nil
}

func (w *WhatsappOption_CategorySpendsExceededReminderWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.CategorySpendsExceededReminderWhatsappOption.GetOption().(type) {
	case *CategorySpendsExceededReminderWhatsappOption_ReminderWhatsappOptionV1:
		return w.CategorySpendsExceededReminderWhatsappOption.GetReminderWhatsappOptionV1().GetTemplateVersion()
	case *CategorySpendsExceededReminderWhatsappOption_ReminderCategorySpendsWhatsappOption:
		return w.CategorySpendsExceededReminderWhatsappOption.GetReminderCategorySpendsWhatsappOption().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_AmountSpendsExceededReminderWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.AmountSpendsExceededReminderWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_AmountSpendsExceededReminderWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.AmountSpendsExceededReminderWhatsappOption.GetOption().(type) {
	case *AmountSpendsExceededReminderWhatsappOption_ReminderWhatsappOptionV1:
		return map[string]string{
			"1": w.AmountSpendsExceededReminderWhatsappOption.GetReminderWhatsappOptionV1().GetAmount(),
			"2": w.AmountSpendsExceededReminderWhatsappOption.GetReminderWhatsappOptionV1().GetDeeplink(),
		}
	case *AmountSpendsExceededReminderWhatsappOption_ReminderAmountSpendsWhatsappOption:
		return map[string]string{
			"1": w.AmountSpendsExceededReminderWhatsappOption.GetReminderAmountSpendsWhatsappOption().GetConfiguredAmount(),
			"2": w.AmountSpendsExceededReminderWhatsappOption.GetReminderAmountSpendsWhatsappOption().GetAmount(),
			"3": w.AmountSpendsExceededReminderWhatsappOption.GetReminderAmountSpendsWhatsappOption().GetDeeplink(),
		}
	}
	return nil
}

func (w *WhatsappOption_AmountSpendsExceededReminderWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.AmountSpendsExceededReminderWhatsappOption.GetOption().(type) {
	case *AmountSpendsExceededReminderWhatsappOption_ReminderWhatsappOptionV1:
		return w.AmountSpendsExceededReminderWhatsappOption.GetReminderWhatsappOptionV1().GetTemplateVersion()
	case *AmountSpendsExceededReminderWhatsappOption_ReminderAmountSpendsWhatsappOption:
		return w.AmountSpendsExceededReminderWhatsappOption.GetReminderAmountSpendsWhatsappOption().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_CreditCardBillPaymentDueDateReminderWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.CreditCardBillPaymentDueDateReminderWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_CreditCardBillPaymentDueDateReminderWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.CreditCardBillPaymentDueDateReminderWhatsappOption.GetOption().(type) {
	case *CreditCardBillPaymentDueDateReminderWhatsappOption_ReminderWhatsappOptionV1:
		return map[string]string{
			"1": w.CreditCardBillPaymentDueDateReminderWhatsappOption.GetReminderWhatsappOptionV1().GetDate().AsTime().Format("January 2, 2006"),
			"2": w.CreditCardBillPaymentDueDateReminderWhatsappOption.GetReminderWhatsappOptionV1().GetDeeplink(),
		}
	case *CreditCardBillPaymentDueDateReminderWhatsappOption_ReminderCcDueDateWhatsappOption:
		return map[string]string{
			"1": w.CreditCardBillPaymentDueDateReminderWhatsappOption.GetReminderCcDueDateWhatsappOption().GetDate().AsTime().Format("January 2, 2006"),
			"2": w.CreditCardBillPaymentDueDateReminderWhatsappOption.GetReminderCcDueDateWhatsappOption().GetAmount(),
			"3": w.CreditCardBillPaymentDueDateReminderWhatsappOption.GetReminderCcDueDateWhatsappOption().GetDeeplink(),
		}
	}
	return nil
}

func (w *WhatsappOption_CreditCardBillPaymentDueDateReminderWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.CreditCardBillPaymentDueDateReminderWhatsappOption.GetOption().(type) {
	case *CreditCardBillPaymentDueDateReminderWhatsappOption_ReminderWhatsappOptionV1:
		return w.CreditCardBillPaymentDueDateReminderWhatsappOption.GetReminderWhatsappOptionV1().GetTemplateVersion()
	case *CreditCardBillPaymentDueDateReminderWhatsappOption_ReminderCcDueDateWhatsappOption:
		return w.CreditCardBillPaymentDueDateReminderWhatsappOption.GetReminderCcDueDateWhatsappOption().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_EpanDownloadedFirstVkycReminderWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.EpanDownloadedFirstVkycReminderWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_EpanDownloadedFirstVkycReminderWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.EpanDownloadedFirstVkycReminderWhatsappOption.GetOption().(type) {
	case *EpanDownloadedFirstVKYCReminderWhatsappOption_EpanDownloadedFirstVkycReminderWhatsappOptionV1:
		return map[string]string{
			"1": w.EpanDownloadedFirstVkycReminderWhatsappOption.GetEpanDownloadedFirstVkycReminderWhatsappOptionV1().GetFirstName(),
		}
	}
	return nil
}

func (w *WhatsappOption_EpanDownloadedFirstVkycReminderWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.EpanDownloadedFirstVkycReminderWhatsappOption.GetOption().(type) {
	case *EpanDownloadedFirstVKYCReminderWhatsappOption_EpanDownloadedFirstVkycReminderWhatsappOptionV1:
		return w.EpanDownloadedFirstVkycReminderWhatsappOption.GetEpanDownloadedFirstVkycReminderWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_EpanDownloadedSecondVkycReminderWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.EpanDownloadedSecondVkycReminderWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_EpanDownloadedSecondVkycReminderWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.EpanDownloadedSecondVkycReminderWhatsappOption.GetOption().(type) {
	case *EpanDownloadedSecondVKYCReminderWhatsappOption_EpanDownloadedSecondVkycReminderWhatsappOptionV1:
		return map[string]string{
			"1": w.EpanDownloadedSecondVkycReminderWhatsappOption.GetEpanDownloadedSecondVkycReminderWhatsappOptionV1().GetFirstName(),
		}
	}
	return nil
}

func (w *WhatsappOption_EpanDownloadedSecondVkycReminderWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.EpanDownloadedSecondVkycReminderWhatsappOption.GetOption().(type) {
	case *EpanDownloadedSecondVKYCReminderWhatsappOption_EpanDownloadedSecondVkycReminderWhatsappOptionV1:
		return w.EpanDownloadedSecondVkycReminderWhatsappOption.GetEpanDownloadedSecondVkycReminderWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_EpanDownloadedThirdVkycReminderWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.EpanDownloadedThirdVkycReminderWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_EpanDownloadedThirdVkycReminderWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.EpanDownloadedThirdVkycReminderWhatsappOption.GetOption().(type) {
	case *EpanDownloadedThirdVKYCReminderWhatsappOption_EpanDownloadedThirdVkycReminderWhatsappOptionV1:
		return map[string]string{
			"1": w.EpanDownloadedThirdVkycReminderWhatsappOption.GetEpanDownloadedThirdVkycReminderWhatsappOptionV1().GetFirstName(),
		}
	}
	return nil
}

func (w *WhatsappOption_EpanDownloadedThirdVkycReminderWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.EpanDownloadedThirdVkycReminderWhatsappOption.GetOption().(type) {
	case *EpanDownloadedThirdVKYCReminderWhatsappOption_EpanDownloadedThirdVkycReminderWhatsappOptionV1:
		return w.EpanDownloadedThirdVkycReminderWhatsappOption.GetEpanDownloadedThirdVkycReminderWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_JumpYearlyAccountStatementWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.JumpYearlyAccountStatementWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_JumpYearlyAccountStatementWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.JumpYearlyAccountStatementWhatsappOption.GetOption().(type) {
	case *JumpYearlyAccountStatementWhatsappOption_JumpYearlyAccountStatementWhatsappOptionV1:
		return map[string]string{
			"1": w.JumpYearlyAccountStatementWhatsappOption.GetJumpYearlyAccountStatementWhatsappOptionV1().GetTotalInvestedAmount(),
			"2": w.JumpYearlyAccountStatementWhatsappOption.GetJumpYearlyAccountStatementWhatsappOptionV1().GetCurrentInvestedAmount(),
		}
	}
	return nil
}

func (w *WhatsappOption_JumpYearlyAccountStatementWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.JumpYearlyAccountStatementWhatsappOption.GetOption().(type) {
	case *JumpYearlyAccountStatementWhatsappOption_JumpYearlyAccountStatementWhatsappOptionV1:
		return w.JumpYearlyAccountStatementWhatsappOption.GetJumpYearlyAccountStatementWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_JumpYearlyAccountStatementWhatsappOption) GetMediaContentType() WhatsappMediaContentType {
	if w == nil {
		return WhatsappMediaContentType_WHATSAPP_MEDIA_CONTENT_TYPE_UNSPECIFIED
	}
	switch w.JumpYearlyAccountStatementWhatsappOption.GetOption().(type) {
	case *JumpYearlyAccountStatementWhatsappOption_JumpYearlyAccountStatementWhatsappOptionV1:
		return w.JumpYearlyAccountStatementWhatsappOption.GetJumpYearlyAccountStatementWhatsappOptionV1().GetMediaContentType()
	}
	return WhatsappMediaContentType_WHATSAPP_MEDIA_CONTENT_TYPE_UNSPECIFIED
}

func (w *WhatsappOption_JumpYearlyAccountStatementWhatsappOption) GetMediaUrl() string {
	if w == nil {
		return ""
	}
	switch w.JumpYearlyAccountStatementWhatsappOption.GetOption().(type) {
	case *JumpYearlyAccountStatementWhatsappOption_JumpYearlyAccountStatementWhatsappOptionV1:
		return w.JumpYearlyAccountStatementWhatsappOption.GetJumpYearlyAccountStatementWhatsappOptionV1().GetMediaUrl()
	}
	return ""
}

func (w *WhatsappOption_JumpYearlyAccountStatementWhatsappOption) GetMediaFileName() string {
	if w == nil {
		return ""
	}
	switch w.JumpYearlyAccountStatementWhatsappOption.GetOption().(type) {
	case *JumpYearlyAccountStatementWhatsappOption_JumpYearlyAccountStatementWhatsappOptionV1:
		return "Jump Annual Statement.pdf"
	}
	return ""
}

func (w *WhatsappOption_SavingsAccountSummaryWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.SavingsAccountSummaryWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_SavingsAccountSummaryWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.SavingsAccountSummaryWhatsappOption.GetOption().(type) {
	case *SavingsAccountSummaryWhatsappOption_SavingsAccountSummaryWhatsappOptionV1:
		return map[string]string{
			"1": w.SavingsAccountSummaryWhatsappOption.GetSavingsAccountSummaryWhatsappOptionV1().GetFirstName(),
		}
	}
	return nil
}

func (w *WhatsappOption_SavingsAccountSummaryWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.SavingsAccountSummaryWhatsappOption.GetOption().(type) {
	case *SavingsAccountSummaryWhatsappOption_SavingsAccountSummaryWhatsappOptionV1:
		return w.SavingsAccountSummaryWhatsappOption.GetSavingsAccountSummaryWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_SavingsAccountSummaryWhatsappOption) GetMediaContentType() WhatsappMediaContentType {
	if w == nil {
		return WhatsappMediaContentType_WHATSAPP_MEDIA_CONTENT_TYPE_UNSPECIFIED
	}
	switch w.SavingsAccountSummaryWhatsappOption.GetOption().(type) {
	case *SavingsAccountSummaryWhatsappOption_SavingsAccountSummaryWhatsappOptionV1:
		return w.SavingsAccountSummaryWhatsappOption.GetSavingsAccountSummaryWhatsappOptionV1().GetMediaContentType()
	}
	return WhatsappMediaContentType_WHATSAPP_MEDIA_CONTENT_TYPE_UNSPECIFIED
}

func (w *WhatsappOption_SavingsAccountSummaryWhatsappOption) GetMediaUrl() string {
	if w == nil {
		return ""
	}
	switch w.SavingsAccountSummaryWhatsappOption.GetOption().(type) {
	case *SavingsAccountSummaryWhatsappOption_SavingsAccountSummaryWhatsappOptionV1:
		return w.SavingsAccountSummaryWhatsappOption.GetSavingsAccountSummaryWhatsappOptionV1().GetMediaUrl()
	}
	return ""
}

func (w *WhatsappOption_SavingsAccountSummaryWhatsappOption) GetMediaFileName() string {
	if w == nil {
		return ""
	}
	switch w.SavingsAccountSummaryWhatsappOption.GetOption().(type) {
	case *SavingsAccountSummaryWhatsappOption_SavingsAccountSummaryWhatsappOptionV1:
		return "Your Federal Bank Account Details.pdf"
	}
	return ""
}

func (w *WhatsappOption_LamfEmandateBounceAlertWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.LamfEmandateBounceAlertWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_LamfEmandateBounceAlertWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.LamfEmandateBounceAlertWhatsappOption.GetOption().(type) {
	case *LamfEmandateBounceAlertWhatsappOption_LamfEmandateBounceAlertWhatsappOptionV1:
		return map[string]string{
			"1": w.LamfEmandateBounceAlertWhatsappOption.GetLamfEmandateBounceAlertWhatsappOptionV1().GetInvestorName(),
			"2": w.LamfEmandateBounceAlertWhatsappOption.GetLamfEmandateBounceAlertWhatsappOptionV1().GetAccountLastFourDigits(),
		}
	}
	return nil
}

func (w *WhatsappOption_LamfEmandateBounceAlertWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.LamfEmandateBounceAlertWhatsappOption.GetOption().(type) {
	case *LamfEmandateBounceAlertWhatsappOption_LamfEmandateBounceAlertWhatsappOptionV1:
		return w.LamfEmandateBounceAlertWhatsappOption.GetLamfEmandateBounceAlertWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_LamfEmiOverdueReminderWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.LamfEmiOverdueReminderWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_LamfEmiOverdueReminderWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.LamfEmiOverdueReminderWhatsappOption.GetOption().(type) {
	case *LamfEmiOverdueReminderWhatsappOption_LamfEmiOverdueReminderWhatsappOptionV1:
		return map[string]string{
			"1": w.LamfEmiOverdueReminderWhatsappOption.GetLamfEmiOverdueReminderWhatsappOptionV1().GetInvestorName(),
		}
	}
	return nil
}

func (w *WhatsappOption_LamfEmiOverdueReminderWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.LamfEmiOverdueReminderWhatsappOption.GetOption().(type) {
	case *LamfEmiOverdueReminderWhatsappOption_LamfEmiOverdueReminderWhatsappOptionV1:
		return w.LamfEmiOverdueReminderWhatsappOption.GetLamfEmiOverdueReminderWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_LamfLowBalanceEmiDueWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.LamfLowBalanceEmiDueWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_LamfLowBalanceEmiDueWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.LamfLowBalanceEmiDueWhatsappOption.GetOption().(type) {
	case *LamfLowBalanceEmiDueWhatsappOption_LamfLowBalanceEmiDueWhatsappOptionV1:
		return map[string]string{
			"1": w.LamfLowBalanceEmiDueWhatsappOption.GetLamfLowBalanceEmiDueWhatsappOptionV1().GetInvestorName(),
			"2": w.LamfLowBalanceEmiDueWhatsappOption.GetLamfLowBalanceEmiDueWhatsappOptionV1().GetEmiAmount(),
			"3": w.LamfLowBalanceEmiDueWhatsappOption.GetLamfLowBalanceEmiDueWhatsappOptionV1().GetDueDate(),
		}
	}
	return nil
}

func (w *WhatsappOption_LamfLowBalanceEmiDueWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.LamfLowBalanceEmiDueWhatsappOption.GetOption().(type) {
	case *LamfLowBalanceEmiDueWhatsappOption_LamfLowBalanceEmiDueWhatsappOptionV1:
		return w.LamfLowBalanceEmiDueWhatsappOption.GetLamfLowBalanceEmiDueWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_CcPanDobDropOffWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.CcPanDobDropOffWhatsappOption.GetOption().(type) {
	case *CcPanDobDropOffWhatsappOption_CcPanDobDropOffWhatsappOptionV1:
		return map[string]string{
			"1": w.CcPanDobDropOffWhatsappOption.GetCcPanDobDropOffWhatsappOptionV1().GetRedirectLink(),
		}
	}
	return nil
}

func (w *WhatsappOption_CcPanDobDropOffWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.CcPanDobDropOffWhatsappOption.GetOption().(type) {
	case *CcPanDobDropOffWhatsappOption_CcPanDobDropOffWhatsappOptionV1:
		return w.CcPanDobDropOffWhatsappOption.GetCcPanDobDropOffWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_CcPanDobDropOffWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.CcPanDobDropOffWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_CcEkycDropOffWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.CcEkycDropOffWhatsappOption.GetOption().(type) {
	case *CcEkycDropOffWhatsappOption_CcEkycDropOffWhatsappOptionV1:
		return map[string]string{}
	}
	return nil
}

func (w *WhatsappOption_CcEkycDropOffWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.CcEkycDropOffWhatsappOption.GetOption().(type) {
	case *CcEkycDropOffWhatsappOption_CcEkycDropOffWhatsappOptionV1:
		return w.CcEkycDropOffWhatsappOption.GetCcEkycDropOffWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_CcEkycDropOffWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.CcEkycDropOffWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_CcLivnessDropOffWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.CcLivnessDropOffWhatsappOption.GetOption().(type) {
	case *CcLivnessDropOffWhatsappOption_CcLivenessDropOffWhatsappOptionV1:
		return map[string]string{}
	}
	return nil
}

func (w *WhatsappOption_CcLivnessDropOffWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.CcLivnessDropOffWhatsappOption.GetOption().(type) {
	case *CcLivnessDropOffWhatsappOption_CcLivenessDropOffWhatsappOptionV1:
		return w.CcLivnessDropOffWhatsappOption.GetCcLivenessDropOffWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_CcLivnessDropOffWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.CcLivnessDropOffWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_CcVkycDropOff_2HrWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.CcVkycDropOff_2HrWhatsappOption.GetOption().(type) {
	case *CcVkycDropOff2HrWhatsappOption_CcVkyc_2HrDropOffWhatsappOptionV1:
		return map[string]string{
			"1": w.CcVkycDropOff_2HrWhatsappOption.GetCcVkyc_2HrDropOffWhatsappOptionV1().GetName(),
			"2": w.CcVkycDropOff_2HrWhatsappOption.GetCcVkyc_2HrDropOffWhatsappOptionV1().GetRedirectLink(),
			"3": w.CcVkycDropOff_2HrWhatsappOption.GetCcVkyc_2HrDropOffWhatsappOptionV1().GetFeedbackLink(),
		}
	}
	return nil
}

func (w *WhatsappOption_CcVkycDropOff_2HrWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.CcVkycDropOff_2HrWhatsappOption.GetOption().(type) {
	case *CcVkycDropOff2HrWhatsappOption_CcVkyc_2HrDropOffWhatsappOptionV1:
		return w.CcVkycDropOff_2HrWhatsappOption.GetCcVkyc_2HrDropOffWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_CcVkycDropOff_2HrWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.CcVkycDropOff_2HrWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_CcFiLiteCardCreationSuccessWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}

	switch w.CcFiLiteCardCreationSuccessWhatsappOption.GetOption().(type) {
	case *CCFiLiteCardCreationSuccessWhatsappOption_CcFiLiteCardCreationSuccessWhatsappOptionV1:
		return map[string]string{
			"1": w.CcFiLiteCardCreationSuccessWhatsappOption.GetCcFiLiteCardCreationSuccessWhatsappOptionV1().GetRedirectionUrl(),
		}
	}

	return nil
}

func (w *WhatsappOption_CcFiLiteCardCreationSuccessWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}

	switch w.CcFiLiteCardCreationSuccessWhatsappOption.GetOption().(type) {
	case *CCFiLiteCardCreationSuccessWhatsappOption_CcFiLiteCardCreationSuccessWhatsappOptionV1:
		return w.CcFiLiteCardCreationSuccessWhatsappOption.GetCcFiLiteCardCreationSuccessWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_CcFiLiteCardCreationSuccessWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.CcFiLiteCardCreationSuccessWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_RiskOutcallWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.RiskOutcallWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_RiskOutcallWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.RiskOutcallWhatsappOption.GetOption().(type) {
	case *RiskOutcallWhatsappOption_RiskOutcallWhatsappOptionV1:
		return map[string]string{
			"1": w.RiskOutcallWhatsappOption.GetRiskOutcallWhatsappOptionV1().GetName(),
			"2": w.RiskOutcallWhatsappOption.GetRiskOutcallWhatsappOptionV1().GetFormExpiry(),
			"3": w.RiskOutcallWhatsappOption.GetRiskOutcallWhatsappOptionV1().GetFormUrl(),
		}
	}
	return nil
}

func (w *WhatsappOption_RiskOutcallWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.RiskOutcallWhatsappOption.GetOption().(type) {
	case *RiskOutcallWhatsappOption_RiskOutcallWhatsappOptionV1:
		return w.RiskOutcallWhatsappOption.GetRiskOutcallWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_RiskOutcallWhatsappOption) SetFormAttributes(formUrl, formExpiry string) error {
	if w == nil {
		return nil
	}
	switch w.RiskOutcallWhatsappOption.GetOption().(type) {
	case *RiskOutcallWhatsappOption_RiskOutcallWhatsappOptionV1:
		w.RiskOutcallWhatsappOption.GetRiskOutcallWhatsappOptionV1().FormUrl = formUrl
		w.RiskOutcallWhatsappOption.GetRiskOutcallWhatsappOptionV1().FormExpiry = formExpiry
		return nil
	}
	return nil
}

func (w *WhatsappOption_RiskOpsCfWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.RiskOpsCfWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_RiskOpsCfWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}
	switch w.RiskOpsCfWhatsappOption.GetOption().(type) {
	case *RiskOpsCFWhatsappOption_RiskOpsWhatsappOptionV1:
		return map[string]string{
			"1": w.RiskOpsCfWhatsappOption.GetRiskOpsWhatsappOptionV1().GetName(),
			"2": w.RiskOpsCfWhatsappOption.GetRiskOpsWhatsappOptionV1().GetFormExpiry(),
			"3": w.RiskOpsCfWhatsappOption.GetRiskOpsWhatsappOptionV1().GetFormUrl(),
		}
	}
	return nil
}

func (w *WhatsappOption_RiskOpsCfWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch w.RiskOpsCfWhatsappOption.GetOption().(type) {
	case *RiskOpsCFWhatsappOption_RiskOpsWhatsappOptionV1:
		return w.RiskOpsCfWhatsappOption.GetRiskOpsWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_RiskOpsCfWhatsappOption) SetFormAttributes(formUrl, formExpiry string) error {
	if w == nil {
		return nil
	}
	switch w.RiskOpsCfWhatsappOption.GetOption().(type) {
	case *RiskOpsCFWhatsappOption_RiskOpsWhatsappOptionV1:
		w.RiskOpsCfWhatsappOption.GetRiskOpsWhatsappOptionV1().FormUrl = formUrl
		w.RiskOpsCfWhatsappOption.GetRiskOpsWhatsappOptionV1().FormExpiry = formExpiry
		return nil
	}
	return nil
}

func (w *WhatsappOption_RiskUnifiedLeaDebitFreezeWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}

	switch w.RiskUnifiedLeaDebitFreezeWhatsappOption.GetOption().(type) {
	case *RiskUnifiedLeaDebitFreezeWhatsappOption_RiskUnifiedLeaDebitFreezeWhatsappOptionV1:
		return map[string]string{
			"1": w.RiskUnifiedLeaDebitFreezeWhatsappOption.GetRiskUnifiedLeaDebitFreezeWhatsappOptionV1().GetReminderText(),
			"2": w.RiskUnifiedLeaDebitFreezeWhatsappOption.GetRiskUnifiedLeaDebitFreezeWhatsappOptionV1().GetFirstName(),
			"3": w.RiskUnifiedLeaDebitFreezeWhatsappOption.GetRiskUnifiedLeaDebitFreezeWhatsappOptionV1().GetIgnoreMessageText(),
		}
	}

	return nil
}

func (w *WhatsappOption_RiskUnifiedLeaDebitFreezeWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}

	switch w.RiskUnifiedLeaDebitFreezeWhatsappOption.GetOption().(type) {
	case *RiskUnifiedLeaDebitFreezeWhatsappOption_RiskUnifiedLeaDebitFreezeWhatsappOptionV1:
		return w.RiskUnifiedLeaDebitFreezeWhatsappOption.GetRiskUnifiedLeaDebitFreezeWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_RiskUnifiedLeaDebitFreezeWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.RiskUnifiedLeaDebitFreezeWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_RiskUnifiedLeaCreditFreezeWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}

	switch w.RiskUnifiedLeaCreditFreezeWhatsappOption.GetOption().(type) {
	case *RiskUnifiedLeaCreditFreezeWhatsappOption_RiskUnifiedLeaCreditFreezeWhatsappOptionV1:
		return map[string]string{
			"1": w.RiskUnifiedLeaCreditFreezeWhatsappOption.GetRiskUnifiedLeaCreditFreezeWhatsappOptionV1().GetReminderText(),
			"2": w.RiskUnifiedLeaCreditFreezeWhatsappOption.GetRiskUnifiedLeaCreditFreezeWhatsappOptionV1().GetFirstName(),
			"3": w.RiskUnifiedLeaCreditFreezeWhatsappOption.GetRiskUnifiedLeaCreditFreezeWhatsappOptionV1().GetIgnoreMessageText(),
		}
	}

	return nil
}

func (w *WhatsappOption_RiskUnifiedLeaCreditFreezeWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}

	switch w.RiskUnifiedLeaCreditFreezeWhatsappOption.GetOption().(type) {
	case *RiskUnifiedLeaCreditFreezeWhatsappOption_RiskUnifiedLeaCreditFreezeWhatsappOptionV1:
		return w.RiskUnifiedLeaCreditFreezeWhatsappOption.GetRiskUnifiedLeaCreditFreezeWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_RiskUnifiedLeaCreditFreezeWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.RiskUnifiedLeaCreditFreezeWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_RiskUnifiedLeaTotalFreezeWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}

	switch w.RiskUnifiedLeaTotalFreezeWhatsappOption.GetOption().(type) {
	case *RiskUnifiedLeaTotalFreezeWhatsappOption_RiskUnifiedLeaTotalFreezeWhatsappOptionV1:
		return map[string]string{
			"1": w.RiskUnifiedLeaTotalFreezeWhatsappOption.GetRiskUnifiedLeaTotalFreezeWhatsappOptionV1().GetReminderText(),
			"2": w.RiskUnifiedLeaTotalFreezeWhatsappOption.GetRiskUnifiedLeaTotalFreezeWhatsappOptionV1().GetFirstName(),
			"3": w.RiskUnifiedLeaTotalFreezeWhatsappOption.GetRiskUnifiedLeaTotalFreezeWhatsappOptionV1().GetIgnoreMessageText(),
		}
	}

	return nil
}

func (w *WhatsappOption_RiskUnifiedLeaTotalFreezeWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}

	switch w.RiskUnifiedLeaTotalFreezeWhatsappOption.GetOption().(type) {
	case *RiskUnifiedLeaTotalFreezeWhatsappOption_RiskUnifiedLeaTotalFreezeWhatsappOptionV1:
		return w.RiskUnifiedLeaTotalFreezeWhatsappOption.GetRiskUnifiedLeaTotalFreezeWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_RiskUnifiedLeaTotalFreezeWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.RiskUnifiedLeaTotalFreezeWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_RiskUnifiedLeaLienWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}

	switch w.RiskUnifiedLeaLienWhatsappOption.GetOption().(type) {
	case *RiskUnifiedLeaLienWhatsappOption_RiskUnifiedLeaLienWhatsappOptionV1:
		return map[string]string{
			"1": w.RiskUnifiedLeaLienWhatsappOption.GetRiskUnifiedLeaLienWhatsappOptionV1().GetAmount(),
			"2": w.RiskUnifiedLeaLienWhatsappOption.GetRiskUnifiedLeaLienWhatsappOptionV1().GetFirstName(),
			"3": w.RiskUnifiedLeaLienWhatsappOption.GetRiskUnifiedLeaLienWhatsappOptionV1().GetAmount(),
			"4": w.RiskUnifiedLeaLienWhatsappOption.GetRiskUnifiedLeaLienWhatsappOptionV1().GetAmount(),
			"5": w.RiskUnifiedLeaLienWhatsappOption.GetRiskUnifiedLeaLienWhatsappOptionV1().GetIgnoreMessageText(),
			"6": w.RiskUnifiedLeaLienWhatsappOption.GetRiskUnifiedLeaLienWhatsappOptionV1().GetReminderText(),
		}
	}

	return nil
}

func (w *WhatsappOption_RiskUnifiedLeaLienWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}

	switch w.RiskUnifiedLeaLienWhatsappOption.GetOption().(type) {
	case *RiskUnifiedLeaLienWhatsappOption_RiskUnifiedLeaLienWhatsappOptionV1:
		return w.RiskUnifiedLeaLienWhatsappOption.GetRiskUnifiedLeaLienWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_RiskUnifiedLeaLienWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.RiskUnifiedLeaLienWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_RiskCreditFreezeWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}

	switch w.RiskCreditFreezeWhatsappOption.GetOption().(type) {
	case *RiskCreditFreezeWhatsappOption_RiskOpsCreditFreezeWhatsappOptionV1:
		return map[string]string{
			"1": w.RiskCreditFreezeWhatsappOption.GetRiskOpsCreditFreezeWhatsappOptionV1().GetReminderHeading(),
			"2": w.RiskCreditFreezeWhatsappOption.GetRiskOpsCreditFreezeWhatsappOptionV1().GetName(),
			"3": w.RiskCreditFreezeWhatsappOption.GetRiskOpsCreditFreezeWhatsappOptionV1().GetFormUrl(),
			"4": w.RiskCreditFreezeWhatsappOption.GetRiskOpsCreditFreezeWhatsappOptionV1().GetReminderText(),
		}
	}

	return nil
}

func (w *WhatsappOption_RiskCreditFreezeWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}

	switch w.RiskCreditFreezeWhatsappOption.GetOption().(type) {
	case *RiskCreditFreezeWhatsappOption_RiskOpsCreditFreezeWhatsappOptionV1:
		return w.RiskCreditFreezeWhatsappOption.GetRiskOpsCreditFreezeWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_RiskCreditFreezeWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.RiskCreditFreezeWhatsappOption.GetWhatsappType()
}

func (w *WhatsappOption_CxTicketResolutionCsatWhatsappOption) GetParameterMap() map[string]string {
	if w == nil {
		return nil
	}

	switch w.CxTicketResolutionCsatWhatsappOption.GetOption().(type) {
	case *CxTicketResolutionCsatWhatsappOption_CxTicketResolutionCsatWhatsappOptionV1:
		return map[string]string{
			"1": w.CxTicketResolutionCsatWhatsappOption.GetCxTicketResolutionCsatWhatsappOptionV1().GetFirstName(),
			"2": w.CxTicketResolutionCsatWhatsappOption.GetCxTicketResolutionCsatWhatsappOptionV1().GetCsatLink(),
		}
	}

	return nil
}

func (w *WhatsappOption_CxTicketResolutionCsatWhatsappOption) GetTemplateVersion() TemplateVersion {
	if w == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}

	switch w.CxTicketResolutionCsatWhatsappOption.GetOption().(type) {
	case *CxTicketResolutionCsatWhatsappOption_CxTicketResolutionCsatWhatsappOptionV1:
		return w.CxTicketResolutionCsatWhatsappOption.GetCxTicketResolutionCsatWhatsappOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (w *WhatsappOption_CxTicketResolutionCsatWhatsappOption) GetType() WhatsappType {
	if w == nil {
		return WhatsappType_WHATSAPP_TYPE_UNSPECIFIED
	}
	return w.CxTicketResolutionCsatWhatsappOption.GetWhatsappType()
}
