// Code generated by MockGen. DO NOT EDIT.
// Source: api/comms/comms_consumer_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	comms "github.com/epifi/gamma/api/comms"
	email "github.com/epifi/gamma/api/vendornotification/email"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCommsConsumerClient is a mock of CommsConsumerClient interface.
type MockCommsConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockCommsConsumerClientMockRecorder
}

// MockCommsConsumerClientMockRecorder is the mock recorder for MockCommsConsumerClient.
type MockCommsConsumerClientMockRecorder struct {
	mock *MockCommsConsumerClient
}

// NewMockCommsConsumerClient creates a new mock instance.
func NewMockCommsConsumerClient(ctrl *gomock.Controller) *MockCommsConsumerClient {
	mock := &MockCommsConsumerClient{ctrl: ctrl}
	mock.recorder = &MockCommsConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCommsConsumerClient) EXPECT() *MockCommsConsumerClientMockRecorder {
	return m.recorder
}

// ProcessAclSmsCallBack mocks base method.
func (m *MockCommsConsumerClient) ProcessAclSmsCallBack(ctx context.Context, in *comms.ProcessAclSmsCallBackRequest, opts ...grpc.CallOption) (*comms.ProcessAclSmsCallBackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessAclSmsCallBack", varargs...)
	ret0, _ := ret[0].(*comms.ProcessAclSmsCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAclSmsCallBack indicates an expected call of ProcessAclSmsCallBack.
func (mr *MockCommsConsumerClientMockRecorder) ProcessAclSmsCallBack(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAclSmsCallBack", reflect.TypeOf((*MockCommsConsumerClient)(nil).ProcessAclSmsCallBack), varargs...)
}

// ProcessAclWhatsappCallBack mocks base method.
func (m *MockCommsConsumerClient) ProcessAclWhatsappCallBack(ctx context.Context, in *comms.ProcessAclWhatsappCallBackRequest, opts ...grpc.CallOption) (*comms.ProcessAclWhatsappCallBackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessAclWhatsappCallBack", varargs...)
	ret0, _ := ret[0].(*comms.ProcessAclWhatsappCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAclWhatsappCallBack indicates an expected call of ProcessAclWhatsappCallBack.
func (mr *MockCommsConsumerClientMockRecorder) ProcessAclWhatsappCallBack(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAclWhatsappCallBack", reflect.TypeOf((*MockCommsConsumerClient)(nil).ProcessAclWhatsappCallBack), varargs...)
}

// ProcessAclWhatsappReply mocks base method.
func (m *MockCommsConsumerClient) ProcessAclWhatsappReply(ctx context.Context, in *comms.ProcessAclWhatsappReplyRequest, opts ...grpc.CallOption) (*comms.ProcessAclWhatsappReplyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessAclWhatsappReply", varargs...)
	ret0, _ := ret[0].(*comms.ProcessAclWhatsappReplyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAclWhatsappReply indicates an expected call of ProcessAclWhatsappReply.
func (mr *MockCommsConsumerClientMockRecorder) ProcessAclWhatsappReply(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAclWhatsappReply", reflect.TypeOf((*MockCommsConsumerClient)(nil).ProcessAclWhatsappReply), varargs...)
}

// ProcessAirtelSmsCallback mocks base method.
func (m *MockCommsConsumerClient) ProcessAirtelSmsCallback(ctx context.Context, in *comms.ProcessAirtelSmsCallbackRequest, opts ...grpc.CallOption) (*comms.ProcessAirtelSmsCallbackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessAirtelSmsCallback", varargs...)
	ret0, _ := ret[0].(*comms.ProcessAirtelSmsCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAirtelSmsCallback indicates an expected call of ProcessAirtelSmsCallback.
func (mr *MockCommsConsumerClientMockRecorder) ProcessAirtelSmsCallback(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAirtelSmsCallback", reflect.TypeOf((*MockCommsConsumerClient)(nil).ProcessAirtelSmsCallback), varargs...)
}

// ProcessAirtelWhatsappCallback mocks base method.
func (m *MockCommsConsumerClient) ProcessAirtelWhatsappCallback(ctx context.Context, in *comms.ProcessAirtelWhatsappCallbackRequest, opts ...grpc.CallOption) (*comms.ProcessAirtelWhatsappCallbackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessAirtelWhatsappCallback", varargs...)
	ret0, _ := ret[0].(*comms.ProcessAirtelWhatsappCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAirtelWhatsappCallback indicates an expected call of ProcessAirtelWhatsappCallback.
func (mr *MockCommsConsumerClientMockRecorder) ProcessAirtelWhatsappCallback(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAirtelWhatsappCallback", reflect.TypeOf((*MockCommsConsumerClient)(nil).ProcessAirtelWhatsappCallback), varargs...)
}

// ProcessEmail mocks base method.
func (m *MockCommsConsumerClient) ProcessEmail(ctx context.Context, in *comms.ProcessEmailRequest, opts ...grpc.CallOption) (*comms.ProcessMessageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessEmail", varargs...)
	ret0, _ := ret[0].(*comms.ProcessMessageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessEmail indicates an expected call of ProcessEmail.
func (mr *MockCommsConsumerClientMockRecorder) ProcessEmail(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessEmail", reflect.TypeOf((*MockCommsConsumerClient)(nil).ProcessEmail), varargs...)
}

// ProcessEmailCallBack mocks base method.
func (m *MockCommsConsumerClient) ProcessEmailCallBack(ctx context.Context, in *email.EmailCallbackEvent, opts ...grpc.CallOption) (*comms.ProcessEmailCallBackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessEmailCallBack", varargs...)
	ret0, _ := ret[0].(*comms.ProcessEmailCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessEmailCallBack indicates an expected call of ProcessEmailCallBack.
func (mr *MockCommsConsumerClientMockRecorder) ProcessEmailCallBack(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessEmailCallBack", reflect.TypeOf((*MockCommsConsumerClient)(nil).ProcessEmailCallBack), varargs...)
}

// ProcessGupshupRcsCallback mocks base method.
func (m *MockCommsConsumerClient) ProcessGupshupRcsCallback(ctx context.Context, in *comms.ProcessGupshupRcsCallbackRequest, opts ...grpc.CallOption) (*comms.ProcessGupshupRcsCallbackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessGupshupRcsCallback", varargs...)
	ret0, _ := ret[0].(*comms.ProcessGupshupRcsCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessGupshupRcsCallback indicates an expected call of ProcessGupshupRcsCallback.
func (mr *MockCommsConsumerClientMockRecorder) ProcessGupshupRcsCallback(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessGupshupRcsCallback", reflect.TypeOf((*MockCommsConsumerClient)(nil).ProcessGupshupRcsCallback), varargs...)
}

// ProcessGupshupWhatsappCallBack mocks base method.
func (m *MockCommsConsumerClient) ProcessGupshupWhatsappCallBack(ctx context.Context, in *comms.ProcessGupshupWhatsappCallBackRequest, opts ...grpc.CallOption) (*comms.ProcessGupshupWhatsappCallBackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessGupshupWhatsappCallBack", varargs...)
	ret0, _ := ret[0].(*comms.ProcessGupshupWhatsappCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessGupshupWhatsappCallBack indicates an expected call of ProcessGupshupWhatsappCallBack.
func (mr *MockCommsConsumerClientMockRecorder) ProcessGupshupWhatsappCallBack(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessGupshupWhatsappCallBack", reflect.TypeOf((*MockCommsConsumerClient)(nil).ProcessGupshupWhatsappCallBack), varargs...)
}

// ProcessKaleyraSmsCallBack mocks base method.
func (m *MockCommsConsumerClient) ProcessKaleyraSmsCallBack(ctx context.Context, in *comms.ProcessKaleyraSmsCallBackRequest, opts ...grpc.CallOption) (*comms.ProcessKaleyraSmsCallBackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessKaleyraSmsCallBack", varargs...)
	ret0, _ := ret[0].(*comms.ProcessKaleyraSmsCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessKaleyraSmsCallBack indicates an expected call of ProcessKaleyraSmsCallBack.
func (mr *MockCommsConsumerClientMockRecorder) ProcessKaleyraSmsCallBack(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessKaleyraSmsCallBack", reflect.TypeOf((*MockCommsConsumerClient)(nil).ProcessKaleyraSmsCallBack), varargs...)
}

// ProcessNetCoreSmsCallback mocks base method.
func (m *MockCommsConsumerClient) ProcessNetCoreSmsCallback(ctx context.Context, in *comms.ProcessNetCoreSmsCallbackRequest, opts ...grpc.CallOption) (*comms.ProcessNetCoreSmsCallbackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessNetCoreSmsCallback", varargs...)
	ret0, _ := ret[0].(*comms.ProcessNetCoreSmsCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessNetCoreSmsCallback indicates an expected call of ProcessNetCoreSmsCallback.
func (mr *MockCommsConsumerClientMockRecorder) ProcessNetCoreSmsCallback(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessNetCoreSmsCallback", reflect.TypeOf((*MockCommsConsumerClient)(nil).ProcessNetCoreSmsCallback), varargs...)
}

// ProcessNotification mocks base method.
func (m *MockCommsConsumerClient) ProcessNotification(ctx context.Context, in *comms.ProcessNotificationRequest, opts ...grpc.CallOption) (*comms.ProcessMessageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessNotification", varargs...)
	ret0, _ := ret[0].(*comms.ProcessMessageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessNotification indicates an expected call of ProcessNotification.
func (mr *MockCommsConsumerClientMockRecorder) ProcessNotification(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessNotification", reflect.TypeOf((*MockCommsConsumerClient)(nil).ProcessNotification), varargs...)
}

// ProcessPinpointEvent mocks base method.
func (m *MockCommsConsumerClient) ProcessPinpointEvent(ctx context.Context, in *comms.ProcessPinpointEventRequest, opts ...grpc.CallOption) (*comms.ProcessPinpointEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessPinpointEvent", varargs...)
	ret0, _ := ret[0].(*comms.ProcessPinpointEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessPinpointEvent indicates an expected call of ProcessPinpointEvent.
func (mr *MockCommsConsumerClientMockRecorder) ProcessPinpointEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessPinpointEvent", reflect.TypeOf((*MockCommsConsumerClient)(nil).ProcessPinpointEvent), varargs...)
}

// ProcessSMS mocks base method.
func (m *MockCommsConsumerClient) ProcessSMS(ctx context.Context, in *comms.ProcessSMSRequest, opts ...grpc.CallOption) (*comms.ProcessMessageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessSMS", varargs...)
	ret0, _ := ret[0].(*comms.ProcessMessageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessSMS indicates an expected call of ProcessSMS.
func (mr *MockCommsConsumerClientMockRecorder) ProcessSMS(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessSMS", reflect.TypeOf((*MockCommsConsumerClient)(nil).ProcessSMS), varargs...)
}

// ProcessSentSMS mocks base method.
func (m *MockCommsConsumerClient) ProcessSentSMS(ctx context.Context, in *comms.ProcessSentSMSRequest, opts ...grpc.CallOption) (*comms.ProcessSentSMSResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessSentSMS", varargs...)
	ret0, _ := ret[0].(*comms.ProcessSentSMSResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessSentSMS indicates an expected call of ProcessSentSMS.
func (mr *MockCommsConsumerClientMockRecorder) ProcessSentSMS(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessSentSMS", reflect.TypeOf((*MockCommsConsumerClient)(nil).ProcessSentSMS), varargs...)
}

// ProcessWhatsapp mocks base method.
func (m *MockCommsConsumerClient) ProcessWhatsapp(ctx context.Context, in *comms.ProcessWhatsappRequest, opts ...grpc.CallOption) (*comms.ProcessWhatsappResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessWhatsapp", varargs...)
	ret0, _ := ret[0].(*comms.ProcessWhatsappResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessWhatsapp indicates an expected call of ProcessWhatsapp.
func (mr *MockCommsConsumerClientMockRecorder) ProcessWhatsapp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessWhatsapp", reflect.TypeOf((*MockCommsConsumerClient)(nil).ProcessWhatsapp), varargs...)
}

// UpdateNotification mocks base method.
func (m *MockCommsConsumerClient) UpdateNotification(ctx context.Context, in *comms.UpdateNotificationRequest, opts ...grpc.CallOption) (*comms.UpdateNotificationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateNotification", varargs...)
	ret0, _ := ret[0].(*comms.UpdateNotificationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateNotification indicates an expected call of UpdateNotification.
func (mr *MockCommsConsumerClientMockRecorder) UpdateNotification(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNotification", reflect.TypeOf((*MockCommsConsumerClient)(nil).UpdateNotification), varargs...)
}

// UpdateSmsStatus mocks base method.
func (m *MockCommsConsumerClient) UpdateSmsStatus(ctx context.Context, in *comms.UpdateSmsStatusRequest, opts ...grpc.CallOption) (*comms.UpdateSmsStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateSmsStatus", varargs...)
	ret0, _ := ret[0].(*comms.UpdateSmsStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSmsStatus indicates an expected call of UpdateSmsStatus.
func (mr *MockCommsConsumerClientMockRecorder) UpdateSmsStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSmsStatus", reflect.TypeOf((*MockCommsConsumerClient)(nil).UpdateSmsStatus), varargs...)
}

// MockCommsConsumerServer is a mock of CommsConsumerServer interface.
type MockCommsConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockCommsConsumerServerMockRecorder
}

// MockCommsConsumerServerMockRecorder is the mock recorder for MockCommsConsumerServer.
type MockCommsConsumerServerMockRecorder struct {
	mock *MockCommsConsumerServer
}

// NewMockCommsConsumerServer creates a new mock instance.
func NewMockCommsConsumerServer(ctrl *gomock.Controller) *MockCommsConsumerServer {
	mock := &MockCommsConsumerServer{ctrl: ctrl}
	mock.recorder = &MockCommsConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCommsConsumerServer) EXPECT() *MockCommsConsumerServerMockRecorder {
	return m.recorder
}

// ProcessAclSmsCallBack mocks base method.
func (m *MockCommsConsumerServer) ProcessAclSmsCallBack(arg0 context.Context, arg1 *comms.ProcessAclSmsCallBackRequest) (*comms.ProcessAclSmsCallBackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessAclSmsCallBack", arg0, arg1)
	ret0, _ := ret[0].(*comms.ProcessAclSmsCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAclSmsCallBack indicates an expected call of ProcessAclSmsCallBack.
func (mr *MockCommsConsumerServerMockRecorder) ProcessAclSmsCallBack(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAclSmsCallBack", reflect.TypeOf((*MockCommsConsumerServer)(nil).ProcessAclSmsCallBack), arg0, arg1)
}

// ProcessAclWhatsappCallBack mocks base method.
func (m *MockCommsConsumerServer) ProcessAclWhatsappCallBack(arg0 context.Context, arg1 *comms.ProcessAclWhatsappCallBackRequest) (*comms.ProcessAclWhatsappCallBackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessAclWhatsappCallBack", arg0, arg1)
	ret0, _ := ret[0].(*comms.ProcessAclWhatsappCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAclWhatsappCallBack indicates an expected call of ProcessAclWhatsappCallBack.
func (mr *MockCommsConsumerServerMockRecorder) ProcessAclWhatsappCallBack(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAclWhatsappCallBack", reflect.TypeOf((*MockCommsConsumerServer)(nil).ProcessAclWhatsappCallBack), arg0, arg1)
}

// ProcessAclWhatsappReply mocks base method.
func (m *MockCommsConsumerServer) ProcessAclWhatsappReply(arg0 context.Context, arg1 *comms.ProcessAclWhatsappReplyRequest) (*comms.ProcessAclWhatsappReplyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessAclWhatsappReply", arg0, arg1)
	ret0, _ := ret[0].(*comms.ProcessAclWhatsappReplyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAclWhatsappReply indicates an expected call of ProcessAclWhatsappReply.
func (mr *MockCommsConsumerServerMockRecorder) ProcessAclWhatsappReply(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAclWhatsappReply", reflect.TypeOf((*MockCommsConsumerServer)(nil).ProcessAclWhatsappReply), arg0, arg1)
}

// ProcessAirtelSmsCallback mocks base method.
func (m *MockCommsConsumerServer) ProcessAirtelSmsCallback(arg0 context.Context, arg1 *comms.ProcessAirtelSmsCallbackRequest) (*comms.ProcessAirtelSmsCallbackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessAirtelSmsCallback", arg0, arg1)
	ret0, _ := ret[0].(*comms.ProcessAirtelSmsCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAirtelSmsCallback indicates an expected call of ProcessAirtelSmsCallback.
func (mr *MockCommsConsumerServerMockRecorder) ProcessAirtelSmsCallback(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAirtelSmsCallback", reflect.TypeOf((*MockCommsConsumerServer)(nil).ProcessAirtelSmsCallback), arg0, arg1)
}

// ProcessAirtelWhatsappCallback mocks base method.
func (m *MockCommsConsumerServer) ProcessAirtelWhatsappCallback(arg0 context.Context, arg1 *comms.ProcessAirtelWhatsappCallbackRequest) (*comms.ProcessAirtelWhatsappCallbackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessAirtelWhatsappCallback", arg0, arg1)
	ret0, _ := ret[0].(*comms.ProcessAirtelWhatsappCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAirtelWhatsappCallback indicates an expected call of ProcessAirtelWhatsappCallback.
func (mr *MockCommsConsumerServerMockRecorder) ProcessAirtelWhatsappCallback(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAirtelWhatsappCallback", reflect.TypeOf((*MockCommsConsumerServer)(nil).ProcessAirtelWhatsappCallback), arg0, arg1)
}

// ProcessEmail mocks base method.
func (m *MockCommsConsumerServer) ProcessEmail(arg0 context.Context, arg1 *comms.ProcessEmailRequest) (*comms.ProcessMessageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessEmail", arg0, arg1)
	ret0, _ := ret[0].(*comms.ProcessMessageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessEmail indicates an expected call of ProcessEmail.
func (mr *MockCommsConsumerServerMockRecorder) ProcessEmail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessEmail", reflect.TypeOf((*MockCommsConsumerServer)(nil).ProcessEmail), arg0, arg1)
}

// ProcessEmailCallBack mocks base method.
func (m *MockCommsConsumerServer) ProcessEmailCallBack(arg0 context.Context, arg1 *email.EmailCallbackEvent) (*comms.ProcessEmailCallBackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessEmailCallBack", arg0, arg1)
	ret0, _ := ret[0].(*comms.ProcessEmailCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessEmailCallBack indicates an expected call of ProcessEmailCallBack.
func (mr *MockCommsConsumerServerMockRecorder) ProcessEmailCallBack(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessEmailCallBack", reflect.TypeOf((*MockCommsConsumerServer)(nil).ProcessEmailCallBack), arg0, arg1)
}

// ProcessGupshupRcsCallback mocks base method.
func (m *MockCommsConsumerServer) ProcessGupshupRcsCallback(arg0 context.Context, arg1 *comms.ProcessGupshupRcsCallbackRequest) (*comms.ProcessGupshupRcsCallbackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessGupshupRcsCallback", arg0, arg1)
	ret0, _ := ret[0].(*comms.ProcessGupshupRcsCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessGupshupRcsCallback indicates an expected call of ProcessGupshupRcsCallback.
func (mr *MockCommsConsumerServerMockRecorder) ProcessGupshupRcsCallback(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessGupshupRcsCallback", reflect.TypeOf((*MockCommsConsumerServer)(nil).ProcessGupshupRcsCallback), arg0, arg1)
}

// ProcessGupshupWhatsappCallBack mocks base method.
func (m *MockCommsConsumerServer) ProcessGupshupWhatsappCallBack(arg0 context.Context, arg1 *comms.ProcessGupshupWhatsappCallBackRequest) (*comms.ProcessGupshupWhatsappCallBackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessGupshupWhatsappCallBack", arg0, arg1)
	ret0, _ := ret[0].(*comms.ProcessGupshupWhatsappCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessGupshupWhatsappCallBack indicates an expected call of ProcessGupshupWhatsappCallBack.
func (mr *MockCommsConsumerServerMockRecorder) ProcessGupshupWhatsappCallBack(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessGupshupWhatsappCallBack", reflect.TypeOf((*MockCommsConsumerServer)(nil).ProcessGupshupWhatsappCallBack), arg0, arg1)
}

// ProcessKaleyraSmsCallBack mocks base method.
func (m *MockCommsConsumerServer) ProcessKaleyraSmsCallBack(arg0 context.Context, arg1 *comms.ProcessKaleyraSmsCallBackRequest) (*comms.ProcessKaleyraSmsCallBackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessKaleyraSmsCallBack", arg0, arg1)
	ret0, _ := ret[0].(*comms.ProcessKaleyraSmsCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessKaleyraSmsCallBack indicates an expected call of ProcessKaleyraSmsCallBack.
func (mr *MockCommsConsumerServerMockRecorder) ProcessKaleyraSmsCallBack(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessKaleyraSmsCallBack", reflect.TypeOf((*MockCommsConsumerServer)(nil).ProcessKaleyraSmsCallBack), arg0, arg1)
}

// ProcessNetCoreSmsCallback mocks base method.
func (m *MockCommsConsumerServer) ProcessNetCoreSmsCallback(arg0 context.Context, arg1 *comms.ProcessNetCoreSmsCallbackRequest) (*comms.ProcessNetCoreSmsCallbackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessNetCoreSmsCallback", arg0, arg1)
	ret0, _ := ret[0].(*comms.ProcessNetCoreSmsCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessNetCoreSmsCallback indicates an expected call of ProcessNetCoreSmsCallback.
func (mr *MockCommsConsumerServerMockRecorder) ProcessNetCoreSmsCallback(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessNetCoreSmsCallback", reflect.TypeOf((*MockCommsConsumerServer)(nil).ProcessNetCoreSmsCallback), arg0, arg1)
}

// ProcessNotification mocks base method.
func (m *MockCommsConsumerServer) ProcessNotification(arg0 context.Context, arg1 *comms.ProcessNotificationRequest) (*comms.ProcessMessageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessNotification", arg0, arg1)
	ret0, _ := ret[0].(*comms.ProcessMessageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessNotification indicates an expected call of ProcessNotification.
func (mr *MockCommsConsumerServerMockRecorder) ProcessNotification(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessNotification", reflect.TypeOf((*MockCommsConsumerServer)(nil).ProcessNotification), arg0, arg1)
}

// ProcessPinpointEvent mocks base method.
func (m *MockCommsConsumerServer) ProcessPinpointEvent(arg0 context.Context, arg1 *comms.ProcessPinpointEventRequest) (*comms.ProcessPinpointEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessPinpointEvent", arg0, arg1)
	ret0, _ := ret[0].(*comms.ProcessPinpointEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessPinpointEvent indicates an expected call of ProcessPinpointEvent.
func (mr *MockCommsConsumerServerMockRecorder) ProcessPinpointEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessPinpointEvent", reflect.TypeOf((*MockCommsConsumerServer)(nil).ProcessPinpointEvent), arg0, arg1)
}

// ProcessSMS mocks base method.
func (m *MockCommsConsumerServer) ProcessSMS(arg0 context.Context, arg1 *comms.ProcessSMSRequest) (*comms.ProcessMessageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessSMS", arg0, arg1)
	ret0, _ := ret[0].(*comms.ProcessMessageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessSMS indicates an expected call of ProcessSMS.
func (mr *MockCommsConsumerServerMockRecorder) ProcessSMS(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessSMS", reflect.TypeOf((*MockCommsConsumerServer)(nil).ProcessSMS), arg0, arg1)
}

// ProcessSentSMS mocks base method.
func (m *MockCommsConsumerServer) ProcessSentSMS(arg0 context.Context, arg1 *comms.ProcessSentSMSRequest) (*comms.ProcessSentSMSResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessSentSMS", arg0, arg1)
	ret0, _ := ret[0].(*comms.ProcessSentSMSResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessSentSMS indicates an expected call of ProcessSentSMS.
func (mr *MockCommsConsumerServerMockRecorder) ProcessSentSMS(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessSentSMS", reflect.TypeOf((*MockCommsConsumerServer)(nil).ProcessSentSMS), arg0, arg1)
}

// ProcessWhatsapp mocks base method.
func (m *MockCommsConsumerServer) ProcessWhatsapp(arg0 context.Context, arg1 *comms.ProcessWhatsappRequest) (*comms.ProcessWhatsappResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessWhatsapp", arg0, arg1)
	ret0, _ := ret[0].(*comms.ProcessWhatsappResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessWhatsapp indicates an expected call of ProcessWhatsapp.
func (mr *MockCommsConsumerServerMockRecorder) ProcessWhatsapp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessWhatsapp", reflect.TypeOf((*MockCommsConsumerServer)(nil).ProcessWhatsapp), arg0, arg1)
}

// UpdateNotification mocks base method.
func (m *MockCommsConsumerServer) UpdateNotification(arg0 context.Context, arg1 *comms.UpdateNotificationRequest) (*comms.UpdateNotificationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateNotification", arg0, arg1)
	ret0, _ := ret[0].(*comms.UpdateNotificationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateNotification indicates an expected call of UpdateNotification.
func (mr *MockCommsConsumerServerMockRecorder) UpdateNotification(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNotification", reflect.TypeOf((*MockCommsConsumerServer)(nil).UpdateNotification), arg0, arg1)
}

// UpdateSmsStatus mocks base method.
func (m *MockCommsConsumerServer) UpdateSmsStatus(arg0 context.Context, arg1 *comms.UpdateSmsStatusRequest) (*comms.UpdateSmsStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSmsStatus", arg0, arg1)
	ret0, _ := ret[0].(*comms.UpdateSmsStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSmsStatus indicates an expected call of UpdateSmsStatus.
func (mr *MockCommsConsumerServerMockRecorder) UpdateSmsStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSmsStatus", reflect.TypeOf((*MockCommsConsumerServer)(nil).UpdateSmsStatus), arg0, arg1)
}

// MockUnsafeCommsConsumerServer is a mock of UnsafeCommsConsumerServer interface.
type MockUnsafeCommsConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCommsConsumerServerMockRecorder
}

// MockUnsafeCommsConsumerServerMockRecorder is the mock recorder for MockUnsafeCommsConsumerServer.
type MockUnsafeCommsConsumerServerMockRecorder struct {
	mock *MockUnsafeCommsConsumerServer
}

// NewMockUnsafeCommsConsumerServer creates a new mock instance.
func NewMockUnsafeCommsConsumerServer(ctrl *gomock.Controller) *MockUnsafeCommsConsumerServer {
	mock := &MockUnsafeCommsConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCommsConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCommsConsumerServer) EXPECT() *MockUnsafeCommsConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCommsConsumerServer mocks base method.
func (m *MockUnsafeCommsConsumerServer) mustEmbedUnimplementedCommsConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCommsConsumerServer")
}

// mustEmbedUnimplementedCommsConsumerServer indicates an expected call of mustEmbedUnimplementedCommsConsumerServer.
func (mr *MockUnsafeCommsConsumerServerMockRecorder) mustEmbedUnimplementedCommsConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCommsConsumerServer", reflect.TypeOf((*MockUnsafeCommsConsumerServer)(nil).mustEmbedUnimplementedCommsConsumerServer))
}
