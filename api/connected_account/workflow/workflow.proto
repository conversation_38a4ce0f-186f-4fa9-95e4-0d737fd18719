syntax = "proto3";

package connected_account.analytics.workflow;

import "api/connected_account/analytics/analysis_request.proto";
import "api/connected_account/analytics/data_exchange_record.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/connected_account/workflow";
option java_package = "com.github.epifi.gamma.api.connected_account.workflow";

message RefreshAnalysisWorkflowRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  analytics.DataExchangeRecord data_exchange_record = 2;
  AnalysisRequest analysis_request = 3;
  string orch_id = 4;
}

message ProcessTechAnalysisAttemptWorkflowRequest {
  string attempt_id = 1 [(validate.rules).string.min_len = 1];
}
