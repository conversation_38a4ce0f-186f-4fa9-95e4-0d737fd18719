// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/recurringpayment/activity/get_recurring_payment.proto

package activity

import (
	activity "github.com/epifi/be-common/api/celestial/activity"
	recurringpayment "github.com/epifi/gamma/api/recurringpayment"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetRecurringPaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Id for the recurring payment to fetch
	RecurringPaymentId string `protobuf:"bytes,2,opt,name=recurring_payment_id,json=recurringPaymentId,proto3" json:"recurring_payment_id,omitempty"`
}

func (x *GetRecurringPaymentRequest) Reset() {
	*x = GetRecurringPaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_recurringpayment_activity_get_recurring_payment_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecurringPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecurringPaymentRequest) ProtoMessage() {}

func (x *GetRecurringPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_recurringpayment_activity_get_recurring_payment_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecurringPaymentRequest.ProtoReflect.Descriptor instead.
func (*GetRecurringPaymentRequest) Descriptor() ([]byte, []int) {
	return file_api_recurringpayment_activity_get_recurring_payment_proto_rawDescGZIP(), []int{0}
}

func (x *GetRecurringPaymentRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *GetRecurringPaymentRequest) GetRecurringPaymentId() string {
	if x != nil {
		return x.RecurringPaymentId
	}
	return ""
}

type GetRecurringPaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// Fetched Recurring payment
	RecurringPayment *recurringpayment.RecurringPayment `protobuf:"bytes,2,opt,name=recurring_payment,json=recurringPayment,proto3" json:"recurring_payment,omitempty"`
}

func (x *GetRecurringPaymentResponse) Reset() {
	*x = GetRecurringPaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_recurringpayment_activity_get_recurring_payment_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecurringPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecurringPaymentResponse) ProtoMessage() {}

func (x *GetRecurringPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_recurringpayment_activity_get_recurring_payment_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecurringPaymentResponse.ProtoReflect.Descriptor instead.
func (*GetRecurringPaymentResponse) Descriptor() ([]byte, []int) {
	return file_api_recurringpayment_activity_get_recurring_payment_proto_rawDescGZIP(), []int{1}
}

func (x *GetRecurringPaymentResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *GetRecurringPaymentResponse) GetRecurringPayment() *recurringpayment.RecurringPayment {
	if x != nil {
		return x.RecurringPayment
	}
	return nil
}

var File_api_recurringpayment_activity_get_recurring_payment_proto protoreflect.FileDescriptor

var file_api_recurringpayment_activity_get_recurring_payment_proto_rawDesc = []byte{
	0x0a, 0x39, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f,
	0x67, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x72, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x98, 0x01, 0x0a, 0x1a, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x22, 0xbb, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x75,
	0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x4f, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x72,
	0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x10, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x42, 0x6c, 0x0a, 0x34, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_recurringpayment_activity_get_recurring_payment_proto_rawDescOnce sync.Once
	file_api_recurringpayment_activity_get_recurring_payment_proto_rawDescData = file_api_recurringpayment_activity_get_recurring_payment_proto_rawDesc
)

func file_api_recurringpayment_activity_get_recurring_payment_proto_rawDescGZIP() []byte {
	file_api_recurringpayment_activity_get_recurring_payment_proto_rawDescOnce.Do(func() {
		file_api_recurringpayment_activity_get_recurring_payment_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_recurringpayment_activity_get_recurring_payment_proto_rawDescData)
	})
	return file_api_recurringpayment_activity_get_recurring_payment_proto_rawDescData
}

var file_api_recurringpayment_activity_get_recurring_payment_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_recurringpayment_activity_get_recurring_payment_proto_goTypes = []interface{}{
	(*GetRecurringPaymentRequest)(nil),        // 0: recurringpayment.activity.GetRecurringPaymentRequest
	(*GetRecurringPaymentResponse)(nil),       // 1: recurringpayment.activity.GetRecurringPaymentResponse
	(*activity.RequestHeader)(nil),            // 2: celestial.activity.RequestHeader
	(*activity.ResponseHeader)(nil),           // 3: celestial.activity.ResponseHeader
	(*recurringpayment.RecurringPayment)(nil), // 4: recurringpayment.RecurringPayment
}
var file_api_recurringpayment_activity_get_recurring_payment_proto_depIdxs = []int32{
	2, // 0: recurringpayment.activity.GetRecurringPaymentRequest.request_header:type_name -> celestial.activity.RequestHeader
	3, // 1: recurringpayment.activity.GetRecurringPaymentResponse.response_header:type_name -> celestial.activity.ResponseHeader
	4, // 2: recurringpayment.activity.GetRecurringPaymentResponse.recurring_payment:type_name -> recurringpayment.RecurringPayment
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_recurringpayment_activity_get_recurring_payment_proto_init() }
func file_api_recurringpayment_activity_get_recurring_payment_proto_init() {
	if File_api_recurringpayment_activity_get_recurring_payment_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_recurringpayment_activity_get_recurring_payment_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecurringPaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_recurringpayment_activity_get_recurring_payment_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecurringPaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_recurringpayment_activity_get_recurring_payment_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_recurringpayment_activity_get_recurring_payment_proto_goTypes,
		DependencyIndexes: file_api_recurringpayment_activity_get_recurring_payment_proto_depIdxs,
		MessageInfos:      file_api_recurringpayment_activity_get_recurring_payment_proto_msgTypes,
	}.Build()
	File_api_recurringpayment_activity_get_recurring_payment_proto = out.File
	file_api_recurringpayment_activity_get_recurring_payment_proto_rawDesc = nil
	file_api_recurringpayment_activity_get_recurring_payment_proto_goTypes = nil
	file_api_recurringpayment_activity_get_recurring_payment_proto_depIdxs = nil
}
