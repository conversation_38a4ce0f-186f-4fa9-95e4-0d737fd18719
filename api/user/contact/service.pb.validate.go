// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/user/contact/service.proto

package contact

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RecordHashedContactsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecordHashedContactsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordHashedContactsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecordHashedContactsRequestMultiError, or nil if none found.
func (m *RecordHashedContactsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordHashedContactsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetContact() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RecordHashedContactsRequestValidationError{
						field:  fmt.Sprintf("Contact[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RecordHashedContactsRequestValidationError{
						field:  fmt.Sprintf("Contact[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RecordHashedContactsRequestValidationError{
					field:  fmt.Sprintf("Contact[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RecordHashedContactsRequestMultiError(errors)
	}

	return nil
}

// RecordHashedContactsRequestMultiError is an error wrapping multiple
// validation errors returned by RecordHashedContactsRequest.ValidateAll() if
// the designated constraints aren't met.
type RecordHashedContactsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordHashedContactsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordHashedContactsRequestMultiError) AllErrors() []error { return m }

// RecordHashedContactsRequestValidationError is the validation error returned
// by RecordHashedContactsRequest.Validate if the designated constraints
// aren't met.
type RecordHashedContactsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordHashedContactsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordHashedContactsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordHashedContactsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordHashedContactsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordHashedContactsRequestValidationError) ErrorName() string {
	return "RecordHashedContactsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecordHashedContactsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordHashedContactsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordHashedContactsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordHashedContactsRequestValidationError{}

// Validate checks the field values on RecordHashedContactsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecordHashedContactsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordHashedContactsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecordHashedContactsResponseMultiError, or nil if none found.
func (m *RecordHashedContactsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordHashedContactsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordHashedContactsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordHashedContactsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordHashedContactsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecordHashedContactsResponseMultiError(errors)
	}

	return nil
}

// RecordHashedContactsResponseMultiError is an error wrapping multiple
// validation errors returned by RecordHashedContactsResponse.ValidateAll() if
// the designated constraints aren't met.
type RecordHashedContactsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordHashedContactsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordHashedContactsResponseMultiError) AllErrors() []error { return m }

// RecordHashedContactsResponseValidationError is the validation error returned
// by RecordHashedContactsResponse.Validate if the designated constraints
// aren't met.
type RecordHashedContactsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordHashedContactsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordHashedContactsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordHashedContactsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordHashedContactsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordHashedContactsResponseValidationError) ErrorName() string {
	return "RecordHashedContactsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RecordHashedContactsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordHashedContactsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordHashedContactsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordHashedContactsResponseValidationError{}

// Validate checks the field values on SyncContactDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncContactDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncContactDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncContactDetailsRequestMultiError, or nil if none found.
func (m *SyncContactDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncContactDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Cursor

	if len(errors) > 0 {
		return SyncContactDetailsRequestMultiError(errors)
	}

	return nil
}

// SyncContactDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by SyncContactDetailsRequest.ValidateAll() if the
// designated constraints aren't met.
type SyncContactDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncContactDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncContactDetailsRequestMultiError) AllErrors() []error { return m }

// SyncContactDetailsRequestValidationError is the validation error returned by
// SyncContactDetailsRequest.Validate if the designated constraints aren't met.
type SyncContactDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncContactDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncContactDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncContactDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncContactDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncContactDetailsRequestValidationError) ErrorName() string {
	return "SyncContactDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SyncContactDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncContactDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncContactDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncContactDetailsRequestValidationError{}

// Validate checks the field values on SyncContactDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncContactDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncContactDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncContactDetailsResponseMultiError, or nil if none found.
func (m *SyncContactDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncContactDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SyncContactDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SyncContactDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SyncContactDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetContactDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncContactDetailsResponseValidationError{
						field:  fmt.Sprintf("ContactDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncContactDetailsResponseValidationError{
						field:  fmt.Sprintf("ContactDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncContactDetailsResponseValidationError{
					field:  fmt.Sprintf("ContactDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Cursor

	if len(errors) > 0 {
		return SyncContactDetailsResponseMultiError(errors)
	}

	return nil
}

// SyncContactDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by SyncContactDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type SyncContactDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncContactDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncContactDetailsResponseMultiError) AllErrors() []error { return m }

// SyncContactDetailsResponseValidationError is the validation error returned
// by SyncContactDetailsResponse.Validate if the designated constraints aren't met.
type SyncContactDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncContactDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncContactDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncContactDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncContactDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncContactDetailsResponseValidationError) ErrorName() string {
	return "SyncContactDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SyncContactDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncContactDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncContactDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncContactDetailsResponseValidationError{}

// Validate checks the field values on GetRiskyContactsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRiskyContactsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRiskyContactsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRiskyContactsRequestMultiError, or nil if none found.
func (m *GetRiskyContactsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRiskyContactsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetRiskyContactsRequestMultiError(errors)
	}

	return nil
}

// GetRiskyContactsRequestMultiError is an error wrapping multiple validation
// errors returned by GetRiskyContactsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRiskyContactsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRiskyContactsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRiskyContactsRequestMultiError) AllErrors() []error { return m }

// GetRiskyContactsRequestValidationError is the validation error returned by
// GetRiskyContactsRequest.Validate if the designated constraints aren't met.
type GetRiskyContactsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRiskyContactsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRiskyContactsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRiskyContactsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRiskyContactsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRiskyContactsRequestValidationError) ErrorName() string {
	return "GetRiskyContactsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRiskyContactsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRiskyContactsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRiskyContactsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRiskyContactsRequestValidationError{}

// Validate checks the field values on GetRiskyContactsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRiskyContactsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRiskyContactsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRiskyContactsResponseMultiError, or nil if none found.
func (m *GetRiskyContactsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRiskyContactsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRiskyContactsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRiskyContactsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRiskyContactsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TotalContacts

	for idx, item := range m.GetContactsAccessRevokeDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRiskyContactsResponseValidationError{
						field:  fmt.Sprintf("ContactsAccessRevokeDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRiskyContactsResponseValidationError{
						field:  fmt.Sprintf("ContactsAccessRevokeDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRiskyContactsResponseValidationError{
					field:  fmt.Sprintf("ContactsAccessRevokeDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRiskyContactsResponseMultiError(errors)
	}

	return nil
}

// GetRiskyContactsResponseMultiError is an error wrapping multiple validation
// errors returned by GetRiskyContactsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRiskyContactsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRiskyContactsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRiskyContactsResponseMultiError) AllErrors() []error { return m }

// GetRiskyContactsResponseValidationError is the validation error returned by
// GetRiskyContactsResponse.Validate if the designated constraints aren't met.
type GetRiskyContactsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRiskyContactsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRiskyContactsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRiskyContactsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRiskyContactsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRiskyContactsResponseValidationError) ErrorName() string {
	return "GetRiskyContactsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRiskyContactsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRiskyContactsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRiskyContactsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRiskyContactsResponseValidationError{}

// Validate checks the field values on GetContactsByBatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetContactsByBatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetContactsByBatchRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetContactsByBatchRequestMultiError, or nil if none found.
func (m *GetContactsByBatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetContactsByBatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetContactsByBatchRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetContactsByBatchRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetContactsByBatchRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetPageContextRequest() == nil {
		err := GetContactsByBatchRequestValidationError{
			field:  "PageContextRequest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPageContextRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetContactsByBatchRequestValidationError{
					field:  "PageContextRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetContactsByBatchRequestValidationError{
					field:  "PageContextRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetContactsByBatchRequestValidationError{
				field:  "PageContextRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetContactsByBatchRequestMultiError(errors)
	}

	return nil
}

// GetContactsByBatchRequestMultiError is an error wrapping multiple validation
// errors returned by GetContactsByBatchRequest.ValidateAll() if the
// designated constraints aren't met.
type GetContactsByBatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetContactsByBatchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetContactsByBatchRequestMultiError) AllErrors() []error { return m }

// GetContactsByBatchRequestValidationError is the validation error returned by
// GetContactsByBatchRequest.Validate if the designated constraints aren't met.
type GetContactsByBatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetContactsByBatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetContactsByBatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetContactsByBatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetContactsByBatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetContactsByBatchRequestValidationError) ErrorName() string {
	return "GetContactsByBatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetContactsByBatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetContactsByBatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetContactsByBatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetContactsByBatchRequestValidationError{}

// Validate checks the field values on GetContactsByBatchResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetContactsByBatchResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetContactsByBatchResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetContactsByBatchResponseMultiError, or nil if none found.
func (m *GetContactsByBatchResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetContactsByBatchResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetContactsByBatchResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetContactsByBatchResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetContactsByBatchResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetContacts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetContactsByBatchResponseValidationError{
						field:  fmt.Sprintf("Contacts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetContactsByBatchResponseValidationError{
						field:  fmt.Sprintf("Contacts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetContactsByBatchResponseValidationError{
					field:  fmt.Sprintf("Contacts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContextResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetContactsByBatchResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetContactsByBatchResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetContactsByBatchResponseValidationError{
				field:  "PageContextResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetContactsByBatchResponseMultiError(errors)
	}

	return nil
}

// GetContactsByBatchResponseMultiError is an error wrapping multiple
// validation errors returned by GetContactsByBatchResponse.ValidateAll() if
// the designated constraints aren't met.
type GetContactsByBatchResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetContactsByBatchResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetContactsByBatchResponseMultiError) AllErrors() []error { return m }

// GetContactsByBatchResponseValidationError is the validation error returned
// by GetContactsByBatchResponse.Validate if the designated constraints aren't met.
type GetContactsByBatchResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetContactsByBatchResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetContactsByBatchResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetContactsByBatchResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetContactsByBatchResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetContactsByBatchResponseValidationError) ErrorName() string {
	return "GetContactsByBatchResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetContactsByBatchResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetContactsByBatchResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetContactsByBatchResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetContactsByBatchResponseValidationError{}

// Validate checks the field values on ContactsQueryIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ContactsQueryIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContactsQueryIdentifier with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ContactsQueryIdentifierMultiError, or nil if none found.
func (m *ContactsQueryIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *ContactsQueryIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *ContactsQueryIdentifier_ActorId:
		if v == nil {
			err := ContactsQueryIdentifierValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	case *ContactsQueryIdentifier_HashedPhoneNumber:
		if v == nil {
			err := ContactsQueryIdentifierValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for HashedPhoneNumber
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ContactsQueryIdentifierMultiError(errors)
	}

	return nil
}

// ContactsQueryIdentifierMultiError is an error wrapping multiple validation
// errors returned by ContactsQueryIdentifier.ValidateAll() if the designated
// constraints aren't met.
type ContactsQueryIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContactsQueryIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContactsQueryIdentifierMultiError) AllErrors() []error { return m }

// ContactsQueryIdentifierValidationError is the validation error returned by
// ContactsQueryIdentifier.Validate if the designated constraints aren't met.
type ContactsQueryIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContactsQueryIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContactsQueryIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContactsQueryIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContactsQueryIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContactsQueryIdentifierValidationError) ErrorName() string {
	return "ContactsQueryIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e ContactsQueryIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContactsQueryIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContactsQueryIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContactsQueryIdentifierValidationError{}

// Validate checks the field values on RecordHashedContactsRequest_Contact with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecordHashedContactsRequest_Contact) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordHashedContactsRequest_Contact
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecordHashedContactsRequest_ContactMultiError, or nil if none found.
func (m *RecordHashedContactsRequest_Contact) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordHashedContactsRequest_Contact) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PhoneNumberHash

	// no validation rules for IsDeleted

	if len(errors) > 0 {
		return RecordHashedContactsRequest_ContactMultiError(errors)
	}

	return nil
}

// RecordHashedContactsRequest_ContactMultiError is an error wrapping multiple
// validation errors returned by
// RecordHashedContactsRequest_Contact.ValidateAll() if the designated
// constraints aren't met.
type RecordHashedContactsRequest_ContactMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordHashedContactsRequest_ContactMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordHashedContactsRequest_ContactMultiError) AllErrors() []error { return m }

// RecordHashedContactsRequest_ContactValidationError is the validation error
// returned by RecordHashedContactsRequest_Contact.Validate if the designated
// constraints aren't met.
type RecordHashedContactsRequest_ContactValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordHashedContactsRequest_ContactValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordHashedContactsRequest_ContactValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordHashedContactsRequest_ContactValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordHashedContactsRequest_ContactValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordHashedContactsRequest_ContactValidationError) ErrorName() string {
	return "RecordHashedContactsRequest_ContactValidationError"
}

// Error satisfies the builtin error interface
func (e RecordHashedContactsRequest_ContactValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordHashedContactsRequest_Contact.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordHashedContactsRequest_ContactValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordHashedContactsRequest_ContactValidationError{}

// Validate checks the field values on
// SyncContactDetailsResponse_ContactDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SyncContactDetailsResponse_ContactDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SyncContactDetailsResponse_ContactDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// SyncContactDetailsResponse_ContactDetailsMultiError, or nil if none found.
func (m *SyncContactDetailsResponse_ContactDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncContactDetailsResponse_ContactDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PhoneNumberHash

	if all {
		switch v := interface{}(m.GetVerifiedName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SyncContactDetailsResponse_ContactDetailsValidationError{
					field:  "VerifiedName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SyncContactDetailsResponse_ContactDetailsValidationError{
					field:  "VerifiedName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVerifiedName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SyncContactDetailsResponse_ContactDetailsValidationError{
				field:  "VerifiedName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IconUrl

	// no validation rules for IsNewOnFi

	// no validation rules for ColourCode

	if len(errors) > 0 {
		return SyncContactDetailsResponse_ContactDetailsMultiError(errors)
	}

	return nil
}

// SyncContactDetailsResponse_ContactDetailsMultiError is an error wrapping
// multiple validation errors returned by
// SyncContactDetailsResponse_ContactDetails.ValidateAll() if the designated
// constraints aren't met.
type SyncContactDetailsResponse_ContactDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncContactDetailsResponse_ContactDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncContactDetailsResponse_ContactDetailsMultiError) AllErrors() []error { return m }

// SyncContactDetailsResponse_ContactDetailsValidationError is the validation
// error returned by SyncContactDetailsResponse_ContactDetails.Validate if the
// designated constraints aren't met.
type SyncContactDetailsResponse_ContactDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncContactDetailsResponse_ContactDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncContactDetailsResponse_ContactDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncContactDetailsResponse_ContactDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncContactDetailsResponse_ContactDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncContactDetailsResponse_ContactDetailsValidationError) ErrorName() string {
	return "SyncContactDetailsResponse_ContactDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e SyncContactDetailsResponse_ContactDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncContactDetailsResponse_ContactDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncContactDetailsResponse_ContactDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncContactDetailsResponse_ContactDetailsValidationError{}
