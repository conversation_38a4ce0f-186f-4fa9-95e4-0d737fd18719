// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/user/group/agent_details.proto

package group

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AgentDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AgentDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AgentDetailsMultiError, or
// nil if none found.
func (m *AgentDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetEntryCreatedAgent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AgentDetailsValidationError{
					field:  "EntryCreatedAgent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AgentDetailsValidationError{
					field:  "EntryCreatedAgent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEntryCreatedAgent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AgentDetailsValidationError{
				field:  "EntryCreatedAgent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEntryDeletedAgent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AgentDetailsValidationError{
					field:  "EntryDeletedAgent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AgentDetailsValidationError{
					field:  "EntryDeletedAgent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEntryDeletedAgent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AgentDetailsValidationError{
				field:  "EntryDeletedAgent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AgentDetailsMultiError(errors)
	}

	return nil
}

// AgentDetailsMultiError is an error wrapping multiple validation errors
// returned by AgentDetails.ValidateAll() if the designated constraints aren't met.
type AgentDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentDetailsMultiError) AllErrors() []error { return m }

// AgentDetailsValidationError is the validation error returned by
// AgentDetails.Validate if the designated constraints aren't met.
type AgentDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentDetailsValidationError) ErrorName() string { return "AgentDetailsValidationError" }

// Error satisfies the builtin error interface
func (e AgentDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentDetailsValidationError{}

// Validate checks the field values on Agent with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Agent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Agent with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AgentMultiError, or nil if none found.
func (m *Agent) ValidateAll() error {
	return m.validate(true)
}

func (m *Agent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Email

	// no validation rules for Reason

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AgentValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AgentValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AgentValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AgentMultiError(errors)
	}

	return nil
}

// AgentMultiError is an error wrapping multiple validation errors returned by
// Agent.ValidateAll() if the designated constraints aren't met.
type AgentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentMultiError) AllErrors() []error { return m }

// AgentValidationError is the validation error returned by Agent.Validate if
// the designated constraints aren't met.
type AgentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentValidationError) ErrorName() string { return "AgentValidationError" }

// Error satisfies the builtin error interface
func (e AgentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentValidationError{}
