// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/user/credit_report/service.proto

package credit_report

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	consent "github.com/epifi/gamma/api/consent"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = consent.ConsentType(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on GetCreditReportRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCreditReportRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditReportRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCreditReportRequestMultiError, or nil if none found.
func (m *GetCreditReportRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditReportRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetCreditReportRequestMultiError(errors)
	}

	return nil
}

// GetCreditReportRequestMultiError is an error wrapping multiple validation
// errors returned by GetCreditReportRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCreditReportRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditReportRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditReportRequestMultiError) AllErrors() []error { return m }

// GetCreditReportRequestValidationError is the validation error returned by
// GetCreditReportRequest.Validate if the designated constraints aren't met.
type GetCreditReportRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditReportRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditReportRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditReportRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditReportRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditReportRequestValidationError) ErrorName() string {
	return "GetCreditReportRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditReportRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditReportRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditReportRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditReportRequestValidationError{}

// Validate checks the field values on GetCreditReportResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCreditReportResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditReportResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCreditReportResponseMultiError, or nil if none found.
func (m *GetCreditReportResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditReportResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditReportResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditReportResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditReportResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PresenceStatus

	// no validation rules for VerificationStatus

	if all {
		switch v := interface{}(m.GetCreditReportData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditReportResponseValidationError{
					field:  "CreditReportData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditReportResponseValidationError{
					field:  "CreditReportData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreditReportData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditReportResponseValidationError{
				field:  "CreditReportData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DownloadConsent

	if all {
		switch v := interface{}(m.GetCreditReport()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditReportResponseValidationError{
					field:  "CreditReport",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditReportResponseValidationError{
					field:  "CreditReport",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreditReport()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditReportResponseValidationError{
				field:  "CreditReport",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCreditReportResponseMultiError(errors)
	}

	return nil
}

// GetCreditReportResponseMultiError is an error wrapping multiple validation
// errors returned by GetCreditReportResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCreditReportResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditReportResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditReportResponseMultiError) AllErrors() []error { return m }

// GetCreditReportResponseValidationError is the validation error returned by
// GetCreditReportResponse.Validate if the designated constraints aren't met.
type GetCreditReportResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditReportResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditReportResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditReportResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditReportResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditReportResponseValidationError) ErrorName() string {
	return "GetCreditReportResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditReportResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditReportResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditReportResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditReportResponseValidationError{}

// Validate checks the field values on GetCreditReportsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCreditReportsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditReportsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCreditReportsRequestMultiError, or nil if none found.
func (m *GetCreditReportsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditReportsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetCreditReportsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetLimit() > 10 {
		err := GetCreditReportsRequestValidationError{
			field:  "Limit",
			reason: "value must be less than or equal to 10",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCreditReportsRequestMultiError(errors)
	}

	return nil
}

// GetCreditReportsRequestMultiError is an error wrapping multiple validation
// errors returned by GetCreditReportsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCreditReportsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditReportsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditReportsRequestMultiError) AllErrors() []error { return m }

// GetCreditReportsRequestValidationError is the validation error returned by
// GetCreditReportsRequest.Validate if the designated constraints aren't met.
type GetCreditReportsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditReportsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditReportsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditReportsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditReportsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditReportsRequestValidationError) ErrorName() string {
	return "GetCreditReportsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditReportsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditReportsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditReportsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditReportsRequestValidationError{}

// Validate checks the field values on GetCreditReportsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCreditReportsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditReportsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCreditReportsResponseMultiError, or nil if none found.
func (m *GetCreditReportsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditReportsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditReportsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditReportsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditReportsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCreditReports() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCreditReportsResponseValidationError{
						field:  fmt.Sprintf("CreditReports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCreditReportsResponseValidationError{
						field:  fmt.Sprintf("CreditReports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCreditReportsResponseValidationError{
					field:  fmt.Sprintf("CreditReports[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCreditReportsResponseMultiError(errors)
	}

	return nil
}

// GetCreditReportsResponseMultiError is an error wrapping multiple validation
// errors returned by GetCreditReportsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCreditReportsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditReportsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditReportsResponseMultiError) AllErrors() []error { return m }

// GetCreditReportsResponseValidationError is the validation error returned by
// GetCreditReportsResponse.Validate if the designated constraints aren't met.
type GetCreditReportsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditReportsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditReportsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditReportsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditReportsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditReportsResponseValidationError) ErrorName() string {
	return "GetCreditReportsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditReportsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditReportsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditReportsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditReportsResponseValidationError{}

// Validate checks the field values on CreditReportDownloadDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreditReportDownloadDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreditReportDownloadDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreditReportDownloadDetailsMultiError, or nil if none found.
func (m *CreditReportDownloadDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditReportDownloadDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCreditReportData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditReportDownloadDetailsValidationError{
					field:  "CreditReportData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditReportDownloadDetailsValidationError{
					field:  "CreditReportData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreditReportData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditReportDownloadDetailsValidationError{
				field:  "CreditReportData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditReportDownloadDetailsValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditReportDownloadDetailsValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditReportDownloadDetailsValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreditReportDownloadDetailsMultiError(errors)
	}

	return nil
}

// CreditReportDownloadDetailsMultiError is an error wrapping multiple
// validation errors returned by CreditReportDownloadDetails.ValidateAll() if
// the designated constraints aren't met.
type CreditReportDownloadDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditReportDownloadDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditReportDownloadDetailsMultiError) AllErrors() []error { return m }

// CreditReportDownloadDetailsValidationError is the validation error returned
// by CreditReportDownloadDetails.Validate if the designated constraints
// aren't met.
type CreditReportDownloadDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditReportDownloadDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditReportDownloadDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditReportDownloadDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditReportDownloadDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditReportDownloadDetailsValidationError) ErrorName() string {
	return "CreditReportDownloadDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e CreditReportDownloadDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditReportDownloadDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditReportDownloadDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditReportDownloadDetailsValidationError{}

// Validate checks the field values on InitiateCreditReportVerificationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *InitiateCreditReportVerificationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitiateCreditReportVerificationRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// InitiateCreditReportVerificationRequestMultiError, or nil if none found.
func (m *InitiateCreditReportVerificationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateCreditReportVerificationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return InitiateCreditReportVerificationRequestMultiError(errors)
	}

	return nil
}

// InitiateCreditReportVerificationRequestMultiError is an error wrapping
// multiple validation errors returned by
// InitiateCreditReportVerificationRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateCreditReportVerificationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateCreditReportVerificationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateCreditReportVerificationRequestMultiError) AllErrors() []error { return m }

// InitiateCreditReportVerificationRequestValidationError is the validation
// error returned by InitiateCreditReportVerificationRequest.Validate if the
// designated constraints aren't met.
type InitiateCreditReportVerificationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateCreditReportVerificationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateCreditReportVerificationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateCreditReportVerificationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateCreditReportVerificationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateCreditReportVerificationRequestValidationError) ErrorName() string {
	return "InitiateCreditReportVerificationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateCreditReportVerificationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateCreditReportVerificationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateCreditReportVerificationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateCreditReportVerificationRequestValidationError{}

// Validate checks the field values on InitiateCreditReportVerificationResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *InitiateCreditReportVerificationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitiateCreditReportVerificationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// InitiateCreditReportVerificationResponseMultiError, or nil if none found.
func (m *InitiateCreditReportVerificationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateCreditReportVerificationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCreditReportVerificationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCreditReportVerificationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCreditReportVerificationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateCreditReportVerificationResponseMultiError(errors)
	}

	return nil
}

// InitiateCreditReportVerificationResponseMultiError is an error wrapping
// multiple validation errors returned by
// InitiateCreditReportVerificationResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateCreditReportVerificationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateCreditReportVerificationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateCreditReportVerificationResponseMultiError) AllErrors() []error { return m }

// InitiateCreditReportVerificationResponseValidationError is the validation
// error returned by InitiateCreditReportVerificationResponse.Validate if the
// designated constraints aren't met.
type InitiateCreditReportVerificationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateCreditReportVerificationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateCreditReportVerificationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateCreditReportVerificationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateCreditReportVerificationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateCreditReportVerificationResponseValidationError) ErrorName() string {
	return "InitiateCreditReportVerificationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateCreditReportVerificationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateCreditReportVerificationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateCreditReportVerificationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateCreditReportVerificationResponseValidationError{}

// Validate checks the field values on InitiateCreditReportPresenceCheckRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *InitiateCreditReportPresenceCheckRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitiateCreditReportPresenceCheckRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// InitiateCreditReportPresenceCheckRequestMultiError, or nil if none found.
func (m *InitiateCreditReportPresenceCheckRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateCreditReportPresenceCheckRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return InitiateCreditReportPresenceCheckRequestMultiError(errors)
	}

	return nil
}

// InitiateCreditReportPresenceCheckRequestMultiError is an error wrapping
// multiple validation errors returned by
// InitiateCreditReportPresenceCheckRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateCreditReportPresenceCheckRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateCreditReportPresenceCheckRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateCreditReportPresenceCheckRequestMultiError) AllErrors() []error { return m }

// InitiateCreditReportPresenceCheckRequestValidationError is the validation
// error returned by InitiateCreditReportPresenceCheckRequest.Validate if the
// designated constraints aren't met.
type InitiateCreditReportPresenceCheckRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateCreditReportPresenceCheckRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateCreditReportPresenceCheckRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateCreditReportPresenceCheckRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateCreditReportPresenceCheckRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateCreditReportPresenceCheckRequestValidationError) ErrorName() string {
	return "InitiateCreditReportPresenceCheckRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateCreditReportPresenceCheckRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateCreditReportPresenceCheckRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateCreditReportPresenceCheckRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateCreditReportPresenceCheckRequestValidationError{}

// Validate checks the field values on
// InitiateCreditReportPresenceCheckResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InitiateCreditReportPresenceCheckResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitiateCreditReportPresenceCheckResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// InitiateCreditReportPresenceCheckResponseMultiError, or nil if none found.
func (m *InitiateCreditReportPresenceCheckResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateCreditReportPresenceCheckResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCreditReportPresenceCheckResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCreditReportPresenceCheckResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCreditReportPresenceCheckResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateCreditReportPresenceCheckResponseMultiError(errors)
	}

	return nil
}

// InitiateCreditReportPresenceCheckResponseMultiError is an error wrapping
// multiple validation errors returned by
// InitiateCreditReportPresenceCheckResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateCreditReportPresenceCheckResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateCreditReportPresenceCheckResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateCreditReportPresenceCheckResponseMultiError) AllErrors() []error { return m }

// InitiateCreditReportPresenceCheckResponseValidationError is the validation
// error returned by InitiateCreditReportPresenceCheckResponse.Validate if the
// designated constraints aren't met.
type InitiateCreditReportPresenceCheckResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateCreditReportPresenceCheckResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateCreditReportPresenceCheckResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateCreditReportPresenceCheckResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateCreditReportPresenceCheckResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateCreditReportPresenceCheckResponseValidationError) ErrorName() string {
	return "InitiateCreditReportPresenceCheckResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateCreditReportPresenceCheckResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateCreditReportPresenceCheckResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateCreditReportPresenceCheckResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateCreditReportPresenceCheckResponseValidationError{}

// Validate checks the field values on RecordReportDownloadConsentRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecordReportDownloadConsentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordReportDownloadConsentRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecordReportDownloadConsentRequestMultiError, or nil if none found.
func (m *RecordReportDownloadConsentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordReportDownloadConsentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Consent

	if len(errors) > 0 {
		return RecordReportDownloadConsentRequestMultiError(errors)
	}

	return nil
}

// RecordReportDownloadConsentRequestMultiError is an error wrapping multiple
// validation errors returned by
// RecordReportDownloadConsentRequest.ValidateAll() if the designated
// constraints aren't met.
type RecordReportDownloadConsentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordReportDownloadConsentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordReportDownloadConsentRequestMultiError) AllErrors() []error { return m }

// RecordReportDownloadConsentRequestValidationError is the validation error
// returned by RecordReportDownloadConsentRequest.Validate if the designated
// constraints aren't met.
type RecordReportDownloadConsentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordReportDownloadConsentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordReportDownloadConsentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordReportDownloadConsentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordReportDownloadConsentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordReportDownloadConsentRequestValidationError) ErrorName() string {
	return "RecordReportDownloadConsentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecordReportDownloadConsentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordReportDownloadConsentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordReportDownloadConsentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordReportDownloadConsentRequestValidationError{}

// Validate checks the field values on RecordReportDownloadConsentResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecordReportDownloadConsentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordReportDownloadConsentResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecordReportDownloadConsentResponseMultiError, or nil if none found.
func (m *RecordReportDownloadConsentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordReportDownloadConsentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordReportDownloadConsentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordReportDownloadConsentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordReportDownloadConsentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecordReportDownloadConsentResponseMultiError(errors)
	}

	return nil
}

// RecordReportDownloadConsentResponseMultiError is an error wrapping multiple
// validation errors returned by
// RecordReportDownloadConsentResponse.ValidateAll() if the designated
// constraints aren't met.
type RecordReportDownloadConsentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordReportDownloadConsentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordReportDownloadConsentResponseMultiError) AllErrors() []error { return m }

// RecordReportDownloadConsentResponseValidationError is the validation error
// returned by RecordReportDownloadConsentResponse.Validate if the designated
// constraints aren't met.
type RecordReportDownloadConsentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordReportDownloadConsentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordReportDownloadConsentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordReportDownloadConsentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordReportDownloadConsentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordReportDownloadConsentResponseValidationError) ErrorName() string {
	return "RecordReportDownloadConsentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RecordReportDownloadConsentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordReportDownloadConsentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordReportDownloadConsentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordReportDownloadConsentResponseValidationError{}

// Validate checks the field values on PurgeExperianDataForOldUsersRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PurgeExperianDataForOldUsersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PurgeExperianDataForOldUsersRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PurgeExperianDataForOldUsersRequestMultiError, or nil if none found.
func (m *PurgeExperianDataForOldUsersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PurgeExperianDataForOldUsersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return PurgeExperianDataForOldUsersRequestMultiError(errors)
	}

	return nil
}

// PurgeExperianDataForOldUsersRequestMultiError is an error wrapping multiple
// validation errors returned by
// PurgeExperianDataForOldUsersRequest.ValidateAll() if the designated
// constraints aren't met.
type PurgeExperianDataForOldUsersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PurgeExperianDataForOldUsersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PurgeExperianDataForOldUsersRequestMultiError) AllErrors() []error { return m }

// PurgeExperianDataForOldUsersRequestValidationError is the validation error
// returned by PurgeExperianDataForOldUsersRequest.Validate if the designated
// constraints aren't met.
type PurgeExperianDataForOldUsersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PurgeExperianDataForOldUsersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PurgeExperianDataForOldUsersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PurgeExperianDataForOldUsersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PurgeExperianDataForOldUsersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PurgeExperianDataForOldUsersRequestValidationError) ErrorName() string {
	return "PurgeExperianDataForOldUsersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PurgeExperianDataForOldUsersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPurgeExperianDataForOldUsersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PurgeExperianDataForOldUsersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PurgeExperianDataForOldUsersRequestValidationError{}

// Validate checks the field values on PurgeExperianDataForOldUsersResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *PurgeExperianDataForOldUsersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PurgeExperianDataForOldUsersResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PurgeExperianDataForOldUsersResponseMultiError, or nil if none found.
func (m *PurgeExperianDataForOldUsersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PurgeExperianDataForOldUsersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PurgeExperianDataForOldUsersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PurgeExperianDataForOldUsersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PurgeExperianDataForOldUsersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PurgeExperianDataForOldUsersResponseMultiError(errors)
	}

	return nil
}

// PurgeExperianDataForOldUsersResponseMultiError is an error wrapping multiple
// validation errors returned by
// PurgeExperianDataForOldUsersResponse.ValidateAll() if the designated
// constraints aren't met.
type PurgeExperianDataForOldUsersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PurgeExperianDataForOldUsersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PurgeExperianDataForOldUsersResponseMultiError) AllErrors() []error { return m }

// PurgeExperianDataForOldUsersResponseValidationError is the validation error
// returned by PurgeExperianDataForOldUsersResponse.Validate if the designated
// constraints aren't met.
type PurgeExperianDataForOldUsersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PurgeExperianDataForOldUsersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PurgeExperianDataForOldUsersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PurgeExperianDataForOldUsersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PurgeExperianDataForOldUsersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PurgeExperianDataForOldUsersResponseValidationError) ErrorName() string {
	return "PurgeExperianDataForOldUsersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PurgeExperianDataForOldUsersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPurgeExperianDataForOldUsersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PurgeExperianDataForOldUsersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PurgeExperianDataForOldUsersResponseValidationError{}

// Validate checks the field values on StartDownloadProcessRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StartDownloadProcessRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartDownloadProcessRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StartDownloadProcessRequestMultiError, or nil if none found.
func (m *StartDownloadProcessRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StartDownloadProcessRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for RequestId

	// no validation rules for Provenance

	// no validation rules for Vendor

	if all {
		switch v := interface{}(m.GetRedirectDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartDownloadProcessRequestValidationError{
					field:  "RedirectDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartDownloadProcessRequestValidationError{
					field:  "RedirectDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedirectDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartDownloadProcessRequestValidationError{
				field:  "RedirectDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EmailId

	if all {
		switch v := interface{}(m.GetAuthInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartDownloadProcessRequestValidationError{
					field:  "AuthInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartDownloadProcessRequestValidationError{
					field:  "AuthInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartDownloadProcessRequestValidationError{
				field:  "AuthInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Pan

	if all {
		switch v := interface{}(m.GetConsentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartDownloadProcessRequestValidationError{
					field:  "ConsentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartDownloadProcessRequestValidationError{
					field:  "ConsentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartDownloadProcessRequestValidationError{
				field:  "ConsentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartDownloadProcessRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartDownloadProcessRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartDownloadProcessRequestValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StartDownloadProcessRequestMultiError(errors)
	}

	return nil
}

// StartDownloadProcessRequestMultiError is an error wrapping multiple
// validation errors returned by StartDownloadProcessRequest.ValidateAll() if
// the designated constraints aren't met.
type StartDownloadProcessRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartDownloadProcessRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartDownloadProcessRequestMultiError) AllErrors() []error { return m }

// StartDownloadProcessRequestValidationError is the validation error returned
// by StartDownloadProcessRequest.Validate if the designated constraints
// aren't met.
type StartDownloadProcessRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartDownloadProcessRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartDownloadProcessRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartDownloadProcessRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartDownloadProcessRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartDownloadProcessRequestValidationError) ErrorName() string {
	return "StartDownloadProcessRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StartDownloadProcessRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartDownloadProcessRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartDownloadProcessRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartDownloadProcessRequestValidationError{}

// Validate checks the field values on AuthInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AuthInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AuthInfoMultiError, or nil
// if none found.
func (m *AuthInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AuthRequestId

	if len(errors) > 0 {
		return AuthInfoMultiError(errors)
	}

	return nil
}

// AuthInfoMultiError is an error wrapping multiple validation errors returned
// by AuthInfo.ValidateAll() if the designated constraints aren't met.
type AuthInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthInfoMultiError) AllErrors() []error { return m }

// AuthInfoValidationError is the validation error returned by
// AuthInfo.Validate if the designated constraints aren't met.
type AuthInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthInfoValidationError) ErrorName() string { return "AuthInfoValidationError" }

// Error satisfies the builtin error interface
func (e AuthInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthInfoValidationError{}

// Validate checks the field values on ConsentDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ConsentDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsentDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ConsentDetailsMultiError,
// or nil if none found.
func (m *ConsentDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsentDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConsentType

	if len(errors) > 0 {
		return ConsentDetailsMultiError(errors)
	}

	return nil
}

// ConsentDetailsMultiError is an error wrapping multiple validation errors
// returned by ConsentDetails.ValidateAll() if the designated constraints
// aren't met.
type ConsentDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsentDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsentDetailsMultiError) AllErrors() []error { return m }

// ConsentDetailsValidationError is the validation error returned by
// ConsentDetails.Validate if the designated constraints aren't met.
type ConsentDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsentDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsentDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsentDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsentDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsentDetailsValidationError) ErrorName() string { return "ConsentDetailsValidationError" }

// Error satisfies the builtin error interface
func (e ConsentDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsentDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsentDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsentDetailsValidationError{}

// Validate checks the field values on StartDownloadProcessResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StartDownloadProcessResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartDownloadProcessResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StartDownloadProcessResponseMultiError, or nil if none found.
func (m *StartDownloadProcessResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StartDownloadProcessResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartDownloadProcessResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartDownloadProcessResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartDownloadProcessResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProcessStatus

	// no validation rules for ProcessSubStatus

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartDownloadProcessResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartDownloadProcessResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartDownloadProcessResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StartDownloadProcessResponseMultiError(errors)
	}

	return nil
}

// StartDownloadProcessResponseMultiError is an error wrapping multiple
// validation errors returned by StartDownloadProcessResponse.ValidateAll() if
// the designated constraints aren't met.
type StartDownloadProcessResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartDownloadProcessResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartDownloadProcessResponseMultiError) AllErrors() []error { return m }

// StartDownloadProcessResponseValidationError is the validation error returned
// by StartDownloadProcessResponse.Validate if the designated constraints
// aren't met.
type StartDownloadProcessResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartDownloadProcessResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartDownloadProcessResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartDownloadProcessResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartDownloadProcessResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartDownloadProcessResponseValidationError) ErrorName() string {
	return "StartDownloadProcessResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StartDownloadProcessResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartDownloadProcessResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartDownloadProcessResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartDownloadProcessResponseValidationError{}

// Validate checks the field values on RecordUserConsentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecordUserConsentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordUserConsentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecordUserConsentRequestMultiError, or nil if none found.
func (m *RecordUserConsentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordUserConsentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for RequestId

	// no validation rules for ConsentAction

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordUserConsentRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordUserConsentRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordUserConsentRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecordUserConsentRequestMultiError(errors)
	}

	return nil
}

// RecordUserConsentRequestMultiError is an error wrapping multiple validation
// errors returned by RecordUserConsentRequest.ValidateAll() if the designated
// constraints aren't met.
type RecordUserConsentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordUserConsentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordUserConsentRequestMultiError) AllErrors() []error { return m }

// RecordUserConsentRequestValidationError is the validation error returned by
// RecordUserConsentRequest.Validate if the designated constraints aren't met.
type RecordUserConsentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordUserConsentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordUserConsentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordUserConsentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordUserConsentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordUserConsentRequestValidationError) ErrorName() string {
	return "RecordUserConsentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecordUserConsentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordUserConsentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordUserConsentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordUserConsentRequestValidationError{}

// Validate checks the field values on RecordUserConsentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecordUserConsentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordUserConsentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecordUserConsentResponseMultiError, or nil if none found.
func (m *RecordUserConsentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordUserConsentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordUserConsentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordUserConsentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordUserConsentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecordUserConsentResponseMultiError(errors)
	}

	return nil
}

// RecordUserConsentResponseMultiError is an error wrapping multiple validation
// errors returned by RecordUserConsentResponse.ValidateAll() if the
// designated constraints aren't met.
type RecordUserConsentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordUserConsentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordUserConsentResponseMultiError) AllErrors() []error { return m }

// RecordUserConsentResponseValidationError is the validation error returned by
// RecordUserConsentResponse.Validate if the designated constraints aren't met.
type RecordUserConsentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordUserConsentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordUserConsentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordUserConsentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordUserConsentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordUserConsentResponseValidationError) ErrorName() string {
	return "RecordUserConsentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RecordUserConsentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordUserConsentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordUserConsentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordUserConsentResponseValidationError{}

// Validate checks the field values on
// GetNextActionForCreditReportDownloadRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetNextActionForCreditReportDownloadRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetNextActionForCreditReportDownloadRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetNextActionForCreditReportDownloadRequestMultiError, or nil if none found.
func (m *GetNextActionForCreditReportDownloadRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNextActionForCreditReportDownloadRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetClientRequestId()) < 1 {
		err := GetNextActionForCreditReportDownloadRequestValidationError{
			field:  "ClientRequestId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetNextActionForCreditReportDownloadRequestMultiError(errors)
	}

	return nil
}

// GetNextActionForCreditReportDownloadRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetNextActionForCreditReportDownloadRequest.ValidateAll() if the designated
// constraints aren't met.
type GetNextActionForCreditReportDownloadRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNextActionForCreditReportDownloadRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNextActionForCreditReportDownloadRequestMultiError) AllErrors() []error { return m }

// GetNextActionForCreditReportDownloadRequestValidationError is the validation
// error returned by GetNextActionForCreditReportDownloadRequest.Validate if
// the designated constraints aren't met.
type GetNextActionForCreditReportDownloadRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNextActionForCreditReportDownloadRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNextActionForCreditReportDownloadRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNextActionForCreditReportDownloadRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNextActionForCreditReportDownloadRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNextActionForCreditReportDownloadRequestValidationError) ErrorName() string {
	return "GetNextActionForCreditReportDownloadRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNextActionForCreditReportDownloadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNextActionForCreditReportDownloadRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNextActionForCreditReportDownloadRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNextActionForCreditReportDownloadRequestValidationError{}

// Validate checks the field values on
// GetNextActionForCreditReportDownloadResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetNextActionForCreditReportDownloadResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetNextActionForCreditReportDownloadResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetNextActionForCreditReportDownloadResponseMultiError, or nil if none found.
func (m *GetNextActionForCreditReportDownloadResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNextActionForCreditReportDownloadResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNextActionForCreditReportDownloadResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNextActionForCreditReportDownloadResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNextActionForCreditReportDownloadResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNextActionForCreditReportDownloadResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNextActionForCreditReportDownloadResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNextActionForCreditReportDownloadResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNextActionForCreditReportDownloadResponseMultiError(errors)
	}

	return nil
}

// GetNextActionForCreditReportDownloadResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetNextActionForCreditReportDownloadResponse.ValidateAll() if the
// designated constraints aren't met.
type GetNextActionForCreditReportDownloadResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNextActionForCreditReportDownloadResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNextActionForCreditReportDownloadResponseMultiError) AllErrors() []error { return m }

// GetNextActionForCreditReportDownloadResponseValidationError is the
// validation error returned by
// GetNextActionForCreditReportDownloadResponse.Validate if the designated
// constraints aren't met.
type GetNextActionForCreditReportDownloadResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNextActionForCreditReportDownloadResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNextActionForCreditReportDownloadResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNextActionForCreditReportDownloadResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNextActionForCreditReportDownloadResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNextActionForCreditReportDownloadResponseValidationError) ErrorName() string {
	return "GetNextActionForCreditReportDownloadResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNextActionForCreditReportDownloadResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNextActionForCreditReportDownloadResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNextActionForCreditReportDownloadResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNextActionForCreditReportDownloadResponseValidationError{}

// Validate checks the field values on GetLatestDownloadProcessDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetLatestDownloadProcessDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLatestDownloadProcessDetailsRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetLatestDownloadProcessDetailsRequestMultiError, or nil if none found.
func (m *GetLatestDownloadProcessDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLatestDownloadProcessDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetLatestDownloadProcessDetailsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Provenance

	// no validation rules for Vendor

	if len(errors) > 0 {
		return GetLatestDownloadProcessDetailsRequestMultiError(errors)
	}

	return nil
}

// GetLatestDownloadProcessDetailsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetLatestDownloadProcessDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLatestDownloadProcessDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLatestDownloadProcessDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLatestDownloadProcessDetailsRequestMultiError) AllErrors() []error { return m }

// GetLatestDownloadProcessDetailsRequestValidationError is the validation
// error returned by GetLatestDownloadProcessDetailsRequest.Validate if the
// designated constraints aren't met.
type GetLatestDownloadProcessDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLatestDownloadProcessDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLatestDownloadProcessDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLatestDownloadProcessDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLatestDownloadProcessDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLatestDownloadProcessDetailsRequestValidationError) ErrorName() string {
	return "GetLatestDownloadProcessDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLatestDownloadProcessDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLatestDownloadProcessDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLatestDownloadProcessDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLatestDownloadProcessDetailsRequestValidationError{}

// Validate checks the field values on GetLatestDownloadProcessDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetLatestDownloadProcessDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLatestDownloadProcessDetailsResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetLatestDownloadProcessDetailsResponseMultiError, or nil if none found.
func (m *GetLatestDownloadProcessDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLatestDownloadProcessDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLatestDownloadProcessDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLatestDownloadProcessDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLatestDownloadProcessDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for ProcessStatus

	// no validation rules for ProcessSubStatus

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLatestDownloadProcessDetailsResponseValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLatestDownloadProcessDetailsResponseValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLatestDownloadProcessDetailsResponseValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLatestDownloadProcessDetailsResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLatestDownloadProcessDetailsResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLatestDownloadProcessDetailsResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLatestDownloadProcessDetailsResponseMultiError(errors)
	}

	return nil
}

// GetLatestDownloadProcessDetailsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetLatestDownloadProcessDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLatestDownloadProcessDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLatestDownloadProcessDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLatestDownloadProcessDetailsResponseMultiError) AllErrors() []error { return m }

// GetLatestDownloadProcessDetailsResponseValidationError is the validation
// error returned by GetLatestDownloadProcessDetailsResponse.Validate if the
// designated constraints aren't met.
type GetLatestDownloadProcessDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLatestDownloadProcessDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLatestDownloadProcessDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLatestDownloadProcessDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLatestDownloadProcessDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLatestDownloadProcessDetailsResponseValidationError) ErrorName() string {
	return "GetLatestDownloadProcessDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLatestDownloadProcessDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLatestDownloadProcessDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLatestDownloadProcessDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLatestDownloadProcessDetailsResponseValidationError{}

// Validate checks the field values on RecordAuthFlowCompletionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecordAuthFlowCompletionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordAuthFlowCompletionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RecordAuthFlowCompletionRequestMultiError, or nil if none found.
func (m *RecordAuthFlowCompletionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordAuthFlowCompletionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetClientReqId()) < 1 {
		err := RecordAuthFlowCompletionRequestValidationError{
			field:  "ClientReqId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RecordAuthFlowCompletionRequestMultiError(errors)
	}

	return nil
}

// RecordAuthFlowCompletionRequestMultiError is an error wrapping multiple
// validation errors returned by RecordAuthFlowCompletionRequest.ValidateAll()
// if the designated constraints aren't met.
type RecordAuthFlowCompletionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordAuthFlowCompletionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordAuthFlowCompletionRequestMultiError) AllErrors() []error { return m }

// RecordAuthFlowCompletionRequestValidationError is the validation error
// returned by RecordAuthFlowCompletionRequest.Validate if the designated
// constraints aren't met.
type RecordAuthFlowCompletionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordAuthFlowCompletionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordAuthFlowCompletionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordAuthFlowCompletionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordAuthFlowCompletionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordAuthFlowCompletionRequestValidationError) ErrorName() string {
	return "RecordAuthFlowCompletionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecordAuthFlowCompletionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordAuthFlowCompletionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordAuthFlowCompletionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordAuthFlowCompletionRequestValidationError{}

// Validate checks the field values on RecordAuthFlowCompletionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecordAuthFlowCompletionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordAuthFlowCompletionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RecordAuthFlowCompletionResponseMultiError, or nil if none found.
func (m *RecordAuthFlowCompletionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordAuthFlowCompletionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordAuthFlowCompletionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordAuthFlowCompletionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordAuthFlowCompletionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordAuthFlowCompletionResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordAuthFlowCompletionResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordAuthFlowCompletionResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecordAuthFlowCompletionResponseMultiError(errors)
	}

	return nil
}

// RecordAuthFlowCompletionResponseMultiError is an error wrapping multiple
// validation errors returned by
// RecordAuthFlowCompletionResponse.ValidateAll() if the designated
// constraints aren't met.
type RecordAuthFlowCompletionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordAuthFlowCompletionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordAuthFlowCompletionResponseMultiError) AllErrors() []error { return m }

// RecordAuthFlowCompletionResponseValidationError is the validation error
// returned by RecordAuthFlowCompletionResponse.Validate if the designated
// constraints aren't met.
type RecordAuthFlowCompletionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordAuthFlowCompletionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordAuthFlowCompletionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordAuthFlowCompletionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordAuthFlowCompletionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordAuthFlowCompletionResponseValidationError) ErrorName() string {
	return "RecordAuthFlowCompletionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RecordAuthFlowCompletionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordAuthFlowCompletionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordAuthFlowCompletionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordAuthFlowCompletionResponseValidationError{}

// Validate checks the field values on GetCreditReportConsentStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCreditReportConsentStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditReportConsentStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCreditReportConsentStatusRequestMultiError, or nil if none found.
func (m *GetCreditReportConsentStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditReportConsentStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Vendor

	if len(errors) > 0 {
		return GetCreditReportConsentStatusRequestMultiError(errors)
	}

	return nil
}

// GetCreditReportConsentStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetCreditReportConsentStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCreditReportConsentStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditReportConsentStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditReportConsentStatusRequestMultiError) AllErrors() []error { return m }

// GetCreditReportConsentStatusRequestValidationError is the validation error
// returned by GetCreditReportConsentStatusRequest.Validate if the designated
// constraints aren't met.
type GetCreditReportConsentStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditReportConsentStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditReportConsentStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditReportConsentStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditReportConsentStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditReportConsentStatusRequestValidationError) ErrorName() string {
	return "GetCreditReportConsentStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditReportConsentStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditReportConsentStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditReportConsentStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditReportConsentStatusRequestValidationError{}

// Validate checks the field values on GetCreditReportConsentStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCreditReportConsentStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditReportConsentStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCreditReportConsentStatusResponseMultiError, or nil if none found.
func (m *GetCreditReportConsentStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditReportConsentStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditReportConsentStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditReportConsentStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditReportConsentStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CreditReportConsentStatus

	if len(errors) > 0 {
		return GetCreditReportConsentStatusResponseMultiError(errors)
	}

	return nil
}

// GetCreditReportConsentStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetCreditReportConsentStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCreditReportConsentStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditReportConsentStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditReportConsentStatusResponseMultiError) AllErrors() []error { return m }

// GetCreditReportConsentStatusResponseValidationError is the validation error
// returned by GetCreditReportConsentStatusResponse.Validate if the designated
// constraints aren't met.
type GetCreditReportConsentStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditReportConsentStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditReportConsentStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditReportConsentStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditReportConsentStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditReportConsentStatusResponseValidationError) ErrorName() string {
	return "GetCreditReportConsentStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditReportConsentStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditReportConsentStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditReportConsentStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditReportConsentStatusResponseValidationError{}
