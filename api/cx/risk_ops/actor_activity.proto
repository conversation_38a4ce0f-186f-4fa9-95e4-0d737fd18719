syntax = "proto3";

package cx.risk_ops;

import "api/cx/header.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/typesv2/webui/table.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/risk_ops";
option java_package = "com.github.epifi.gamma.api.cx.risk_ops";

// Below are the areas where we can search for actor activities
enum ActorActivityArea {
  ACTOR_ACTIVITY_AREA_UNSPECIFIED = 0;
  ACTOR_ACTIVITY_AREA_ALL = 1;
  ACTOR_ACTIVITY_AREA_PAY = 2;
  ACTOR_ACTIVITY_AREA_CREDIT_CARD = 3;
  ACTOR_ACTIVITY_AREA_ACCOUNTS = 4;
  ACTOR_ACTIVITY_AREA_REWARDS = 5;
}

// This message defines the request for dropdown options in the
// "Search by Areas" section to fetch available areas for activities.
message GetActorActivityAreasRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
}

// GetActorActivityAreasResponse defines the response contract
// for the activity dropdown options to be shown on the client side
message GetActorActivityAreasResponse {
  rpc.Status status = 1;
  repeated ActorActivityArea areas = 2;
}


// GetActorActivitiesRequest defines the contract for sherlock
// client to fetch actor related activities
message GetActorActivitiesRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  // case management case id
  string case_id = 2 [(validate.rules).string.min_len = 1];
  // areas to fetch activities from
  repeated ActorActivityArea areas = 3 [(validate.rules).repeated = {min_items: 1}];
  // start_time defines the start time for the activities to be fetched
  google.protobuf.Timestamp start_time = 4 [(validate.rules).timestamp.required = true];
  // page_context helps client to paginate across responses
  rpc.PageContextRequest page_context = 5;
  // end_time defines the end time for the activities to be fetched
  google.protobuf.Timestamp end_time = 6 [(validate.rules).timestamp.required = true];
}

// GetActorActivitiesResponse defines the contract for response to be sent to sherlock
// all the data will be sent in table format
message GetActorActivitiesResponse {
  enum Status {
    OK = 0;
    // essential params missing like area list not valid
    INVALID_ARGUMENT = 3;
    // No activities found for actor
    NOT_FOUND = 5;
    // user not able to access page as permission denied
    PERMISSION_DENIED = 7;
    // ISE response due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 13;
  }
  rpc.Status status = 1;

  api.typesv2.webui.Table activity_details = 2;
  rpc.PageContextResponse page_context = 3;
}
