//go:generate gen_queue_pb

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/cx/watson/consumer/service.proto

package consumer

import (
	context "context"
	ticket "github.com/epifi/gamma/api/cx/ticket"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	WatsonConsumerService_ProcessReportIncidentEvent_FullMethodName  = "/cx.watson.consumer.WatsonConsumerService/ProcessReportIncidentEvent"
	WatsonConsumerService_ProcessResolveIncidentEvent_FullMethodName = "/cx.watson.consumer.WatsonConsumerService/ProcessResolveIncidentEvent"
	WatsonConsumerService_ProcessTicketEventForWatson_FullMethodName = "/cx.watson.consumer.WatsonConsumerService/ProcessTicketEventForWatson"
	WatsonConsumerService_ProcessCreateTicketEvent_FullMethodName    = "/cx.watson.consumer.WatsonConsumerService/ProcessCreateTicketEvent"
)

// WatsonConsumerServiceClient is the client API for WatsonConsumerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WatsonConsumerServiceClient interface {
	// Consumer RPC for the queue: cx-watson-incident-reporting-queue
	// Processing the incident event of type report which was published into queue by IngestEvent RPC.
	ProcessReportIncidentEvent(ctx context.Context, in *ProcessReportIncidentEventRequest, opts ...grpc.CallOption) (*ProcessReportIncidentEventResponse, error)
	// Consumer RPC for the queue: cx-watson-incident-resolution-queue
	// Processing the incident event of type resolve which was published into queue by IngestEvent RPC.
	ProcessResolveIncidentEvent(ctx context.Context, in *ProcessResolveIncidentEventRequest, opts ...grpc.CallOption) (*ProcessResolveIncidentEventResponse, error)
	// Consumer RPC for the queue: cx-watson-ticket-event-queue
	// Processes the ticket events published into queue for ingestion into Watson. eg: signaling the corresponding workflow etc.
	ProcessTicketEventForWatson(ctx context.Context, in *ProcessTicketEventForWatsonRequest, opts ...grpc.CallOption) (*ProcessTicketEventForWatsonResponse, error)
	// Consumer RPC for the queue: cx-watson-create-ticket-event-queue
	// Processes the event published into the queue after successful ticket creation
	// This consumer helps Watson update its internal mapping with newly created ticket
	ProcessCreateTicketEvent(ctx context.Context, in *ticket.CreateTicketEvent, opts ...grpc.CallOption) (*ProcessCreateTicketEventResponse, error)
}

type watsonConsumerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWatsonConsumerServiceClient(cc grpc.ClientConnInterface) WatsonConsumerServiceClient {
	return &watsonConsumerServiceClient{cc}
}

func (c *watsonConsumerServiceClient) ProcessReportIncidentEvent(ctx context.Context, in *ProcessReportIncidentEventRequest, opts ...grpc.CallOption) (*ProcessReportIncidentEventResponse, error) {
	out := new(ProcessReportIncidentEventResponse)
	err := c.cc.Invoke(ctx, WatsonConsumerService_ProcessReportIncidentEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *watsonConsumerServiceClient) ProcessResolveIncidentEvent(ctx context.Context, in *ProcessResolveIncidentEventRequest, opts ...grpc.CallOption) (*ProcessResolveIncidentEventResponse, error) {
	out := new(ProcessResolveIncidentEventResponse)
	err := c.cc.Invoke(ctx, WatsonConsumerService_ProcessResolveIncidentEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *watsonConsumerServiceClient) ProcessTicketEventForWatson(ctx context.Context, in *ProcessTicketEventForWatsonRequest, opts ...grpc.CallOption) (*ProcessTicketEventForWatsonResponse, error) {
	out := new(ProcessTicketEventForWatsonResponse)
	err := c.cc.Invoke(ctx, WatsonConsumerService_ProcessTicketEventForWatson_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *watsonConsumerServiceClient) ProcessCreateTicketEvent(ctx context.Context, in *ticket.CreateTicketEvent, opts ...grpc.CallOption) (*ProcessCreateTicketEventResponse, error) {
	out := new(ProcessCreateTicketEventResponse)
	err := c.cc.Invoke(ctx, WatsonConsumerService_ProcessCreateTicketEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WatsonConsumerServiceServer is the server API for WatsonConsumerService service.
// All implementations should embed UnimplementedWatsonConsumerServiceServer
// for forward compatibility
type WatsonConsumerServiceServer interface {
	// Consumer RPC for the queue: cx-watson-incident-reporting-queue
	// Processing the incident event of type report which was published into queue by IngestEvent RPC.
	ProcessReportIncidentEvent(context.Context, *ProcessReportIncidentEventRequest) (*ProcessReportIncidentEventResponse, error)
	// Consumer RPC for the queue: cx-watson-incident-resolution-queue
	// Processing the incident event of type resolve which was published into queue by IngestEvent RPC.
	ProcessResolveIncidentEvent(context.Context, *ProcessResolveIncidentEventRequest) (*ProcessResolveIncidentEventResponse, error)
	// Consumer RPC for the queue: cx-watson-ticket-event-queue
	// Processes the ticket events published into queue for ingestion into Watson. eg: signaling the corresponding workflow etc.
	ProcessTicketEventForWatson(context.Context, *ProcessTicketEventForWatsonRequest) (*ProcessTicketEventForWatsonResponse, error)
	// Consumer RPC for the queue: cx-watson-create-ticket-event-queue
	// Processes the event published into the queue after successful ticket creation
	// This consumer helps Watson update its internal mapping with newly created ticket
	ProcessCreateTicketEvent(context.Context, *ticket.CreateTicketEvent) (*ProcessCreateTicketEventResponse, error)
}

// UnimplementedWatsonConsumerServiceServer should be embedded to have forward compatible implementations.
type UnimplementedWatsonConsumerServiceServer struct {
}

func (UnimplementedWatsonConsumerServiceServer) ProcessReportIncidentEvent(context.Context, *ProcessReportIncidentEventRequest) (*ProcessReportIncidentEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessReportIncidentEvent not implemented")
}
func (UnimplementedWatsonConsumerServiceServer) ProcessResolveIncidentEvent(context.Context, *ProcessResolveIncidentEventRequest) (*ProcessResolveIncidentEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessResolveIncidentEvent not implemented")
}
func (UnimplementedWatsonConsumerServiceServer) ProcessTicketEventForWatson(context.Context, *ProcessTicketEventForWatsonRequest) (*ProcessTicketEventForWatsonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessTicketEventForWatson not implemented")
}
func (UnimplementedWatsonConsumerServiceServer) ProcessCreateTicketEvent(context.Context, *ticket.CreateTicketEvent) (*ProcessCreateTicketEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCreateTicketEvent not implemented")
}

// UnsafeWatsonConsumerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WatsonConsumerServiceServer will
// result in compilation errors.
type UnsafeWatsonConsumerServiceServer interface {
	mustEmbedUnimplementedWatsonConsumerServiceServer()
}

func RegisterWatsonConsumerServiceServer(s grpc.ServiceRegistrar, srv WatsonConsumerServiceServer) {
	s.RegisterService(&WatsonConsumerService_ServiceDesc, srv)
}

func _WatsonConsumerService_ProcessReportIncidentEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessReportIncidentEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WatsonConsumerServiceServer).ProcessReportIncidentEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WatsonConsumerService_ProcessReportIncidentEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WatsonConsumerServiceServer).ProcessReportIncidentEvent(ctx, req.(*ProcessReportIncidentEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WatsonConsumerService_ProcessResolveIncidentEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessResolveIncidentEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WatsonConsumerServiceServer).ProcessResolveIncidentEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WatsonConsumerService_ProcessResolveIncidentEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WatsonConsumerServiceServer).ProcessResolveIncidentEvent(ctx, req.(*ProcessResolveIncidentEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WatsonConsumerService_ProcessTicketEventForWatson_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessTicketEventForWatsonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WatsonConsumerServiceServer).ProcessTicketEventForWatson(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WatsonConsumerService_ProcessTicketEventForWatson_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WatsonConsumerServiceServer).ProcessTicketEventForWatson(ctx, req.(*ProcessTicketEventForWatsonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WatsonConsumerService_ProcessCreateTicketEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ticket.CreateTicketEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WatsonConsumerServiceServer).ProcessCreateTicketEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WatsonConsumerService_ProcessCreateTicketEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WatsonConsumerServiceServer).ProcessCreateTicketEvent(ctx, req.(*ticket.CreateTicketEvent))
	}
	return interceptor(ctx, in, info, handler)
}

// WatsonConsumerService_ServiceDesc is the grpc.ServiceDesc for WatsonConsumerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WatsonConsumerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cx.watson.consumer.WatsonConsumerService",
	HandlerType: (*WatsonConsumerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessReportIncidentEvent",
			Handler:    _WatsonConsumerService_ProcessReportIncidentEvent_Handler,
		},
		{
			MethodName: "ProcessResolveIncidentEvent",
			Handler:    _WatsonConsumerService_ProcessResolveIncidentEvent_Handler,
		},
		{
			MethodName: "ProcessTicketEventForWatson",
			Handler:    _WatsonConsumerService_ProcessTicketEventForWatson_Handler,
		},
		{
			MethodName: "ProcessCreateTicketEvent",
			Handler:    _WatsonConsumerService_ProcessCreateTicketEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/cx/watson/consumer/service.proto",
}
