// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/cx/data_collector/investment/usstocks/service.proto

package usstocks

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UsStocksInvestment_GetPortfolioDetails_FullMethodName       = "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetPortfolioDetails"
	UsStocksInvestment_GetUsStockActivities_FullMethodName      = "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetUsStockActivities"
	UsStocksInvestment_GetActivityDetails_FullMethodName        = "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetActivityDetails"
	UsStocksInvestment_GetRemittances_FullMethodName            = "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetRemittances"
	UsStocksInvestment_GetRemittanceOrderDetails_FullMethodName = "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetRemittanceOrderDetails"
	UsStocksInvestment_GetAccountBasicDetails_FullMethodName    = "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetAccountBasicDetails"
	UsStocksInvestment_GetAccountStagesDetails_FullMethodName   = "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetAccountStagesDetails"
	UsStocksInvestment_GetReviewItems_FullMethodName            = "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetReviewItems"
	UsStocksInvestment_GetReviewItemDetails_FullMethodName      = "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetReviewItemDetails"
	UsStocksInvestment_MarkReviewAsDone_FullMethodName          = "/cx.data_collector.investment.usstocks.UsStocksInvestment/MarkReviewAsDone"
)

// UsStocksInvestmentClient is the client API for UsStocksInvestment service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UsStocksInvestmentClient interface {
	// To get list of  distinct stocks/etfs asset name invested by a user
	// Actor information will be enriched in header via interceptor
	// Returns empty list in case no investments found
	// enrichment and ticket validation method options are set because this is part of user information flow
	// this method is behind moderately sensitive information level because it will show what all auto invest rules are active for user
	// will return status ok with sherlock deeplink if customer auth is not satisfied
	GetPortfolioDetails(ctx context.Context, in *GetPortfolioDetailsRequest, opts ...grpc.CallOption) (*GetPortfolioDetailsResponse, error)
	// To get list of stock/etf orders placed by the actor along with the details
	// in input request it takes at least one of these following values-
	//
	//	1.order_id
	//	2.from date, to date, activity_type(optional)
	//	3.asset name
	//
	// Actor information will be enriched in header via interceptor
	// Returns empty list in case no investment activities are found
	// enrichment and ticket validation method options are set because this is part of user information flow
	// this method is behind moderately sensitive information level because it will show the orders by a user and number of units in an order
	// will return status ok with sherlock deeplink if customer auth is not satisfied
	// Note: this is a paginated api. Pagination is controlled by page context definition in request and response.
	// If Order Id is sent in a request then this rpc will return that particular order
	GetUsStockActivities(ctx context.Context, in *GetUsStockActivitiesRequest, opts ...grpc.CallOption) (*GetUsStockActivitiesResponse, error)
	// To get timeline of an order by order_id(external_order_id)
	// Actor information will be enriched in header via interceptor
	// enrichment and ticket validation method options are set because this is part of user information flow
	// this method is behind less sensitive information level because it will show the order's status
	// and timestamp when order reached that status
	// will return status ok with sherlock deeplink if customer auth is not satisfied
	GetActivityDetails(ctx context.Context, in *GetActivityDetailsRequest, opts ...grpc.CallOption) (*GetActivityDetailsResponse, error)
	// To get list of remittance placed by the actor along with the details
	// in input request it takes at least one of these following values-
	//
	//	1.remittance_id
	//	2.from date, to date
	//
	// Actor information will be enriched in header via interceptor
	// Returns empty list in case no investment remittances are found
	// enrichment and ticket validation method options are set because this is part of user information flow
	// this method is behind moderately sensitive information level because it will show the orders by a user and number of units in an order
	// will return status ok with sherlock deeplink if customer auth is not satisfied
	// Note: this is a paginated api. Pagination is controlled by page context definition in request and response.
	// If Remittance Id is sent in a request then this rpc will return that particular Remittance order
	GetRemittances(ctx context.Context, in *GetRemittancesRequest, opts ...grpc.CallOption) (*GetRemittancesResponse, error)
	// To get the timeline of a remittance order by ID (i.e., external ID of US stocks wallet load/withdrawal order)
	// Actor information will be enriched in header via interceptor
	// enrichment and ticket validation method options are set because this is part of user information flow
	// this method is behind less sensitive information level because it will show the remittance's status
	// and timestamp when order reached that status
	// will return status ok with sherlock deeplink if customer auth is not satisfied
	GetRemittanceOrderDetails(ctx context.Context, in *GetRemittanceOrderDetailsRequest, opts ...grpc.CallOption) (*GetRemittanceOrderDetailsResponse, error)
	// to get user's account id, current stage and status info
	// Actor information will be enriched in header via interceptor
	// enrichment and ticket validation method options are set because this is part of user information flow
	// this method is behind less sensitive information level because it will show the order's account stage and status
	// will return status ok with sherlock deeplink if customer auth is not satisfied
	GetAccountBasicDetails(ctx context.Context, in *GetAccountBasicDetailsRequest, opts ...grpc.CallOption) (*GetAccountBasicDetailsResponse, error)
	// to get user's all account stage details along with troubleshoot and description
	// Actor information will be enriched in header via interceptor
	// enrichment and ticket validation method options are set because this is part of user information flow
	// this method is behind less sensitive information level because it will show the order's account stage and status
	// will return status ok with sherlock deeplink if customer auth is not satisfied
	GetAccountStagesDetails(ctx context.Context, in *GetAccountStagesDetailsRequest, opts ...grpc.CallOption) (*GetAccountStagesDetailsResponse, error)
	// to get pending items for manual review
	// Actor information will be enriched in header via interceptor
	// ticket validation, enrichment options are set to false and information level is set to insensitive
	// since this is not part of user information flow
	GetReviewItems(ctx context.Context, in *GetReviewItemsRequest, opts ...grpc.CallOption) (*GetReviewItemsResponse, error)
	// to get details of an individual review item (e.g. pan image, liveness image etc)
	// Actor information will be enriched in header via interceptor
	// ticket validation, enrichment options are set to false and information level is set to insensitive
	// since this is not part of user information flow
	GetReviewItemDetails(ctx context.Context, in *GetReviewItemDetailsRequest, opts ...grpc.CallOption) (*GetReviewItemDetailsResponse, error)
	// to get details of an individual review item (e.g. pan image, liveness image etc)
	// Actor information will be enriched in header via interceptor
	// ticket validation, enrichment options are set to false and information level is set to insensitive
	// since this is not part of user information flow
	MarkReviewAsDone(ctx context.Context, in *MarkReviewAsDoneRequest, opts ...grpc.CallOption) (*MarkReviewAsDoneResponse, error)
}

type usStocksInvestmentClient struct {
	cc grpc.ClientConnInterface
}

func NewUsStocksInvestmentClient(cc grpc.ClientConnInterface) UsStocksInvestmentClient {
	return &usStocksInvestmentClient{cc}
}

func (c *usStocksInvestmentClient) GetPortfolioDetails(ctx context.Context, in *GetPortfolioDetailsRequest, opts ...grpc.CallOption) (*GetPortfolioDetailsResponse, error) {
	out := new(GetPortfolioDetailsResponse)
	err := c.cc.Invoke(ctx, UsStocksInvestment_GetPortfolioDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usStocksInvestmentClient) GetUsStockActivities(ctx context.Context, in *GetUsStockActivitiesRequest, opts ...grpc.CallOption) (*GetUsStockActivitiesResponse, error) {
	out := new(GetUsStockActivitiesResponse)
	err := c.cc.Invoke(ctx, UsStocksInvestment_GetUsStockActivities_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usStocksInvestmentClient) GetActivityDetails(ctx context.Context, in *GetActivityDetailsRequest, opts ...grpc.CallOption) (*GetActivityDetailsResponse, error) {
	out := new(GetActivityDetailsResponse)
	err := c.cc.Invoke(ctx, UsStocksInvestment_GetActivityDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usStocksInvestmentClient) GetRemittances(ctx context.Context, in *GetRemittancesRequest, opts ...grpc.CallOption) (*GetRemittancesResponse, error) {
	out := new(GetRemittancesResponse)
	err := c.cc.Invoke(ctx, UsStocksInvestment_GetRemittances_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usStocksInvestmentClient) GetRemittanceOrderDetails(ctx context.Context, in *GetRemittanceOrderDetailsRequest, opts ...grpc.CallOption) (*GetRemittanceOrderDetailsResponse, error) {
	out := new(GetRemittanceOrderDetailsResponse)
	err := c.cc.Invoke(ctx, UsStocksInvestment_GetRemittanceOrderDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usStocksInvestmentClient) GetAccountBasicDetails(ctx context.Context, in *GetAccountBasicDetailsRequest, opts ...grpc.CallOption) (*GetAccountBasicDetailsResponse, error) {
	out := new(GetAccountBasicDetailsResponse)
	err := c.cc.Invoke(ctx, UsStocksInvestment_GetAccountBasicDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usStocksInvestmentClient) GetAccountStagesDetails(ctx context.Context, in *GetAccountStagesDetailsRequest, opts ...grpc.CallOption) (*GetAccountStagesDetailsResponse, error) {
	out := new(GetAccountStagesDetailsResponse)
	err := c.cc.Invoke(ctx, UsStocksInvestment_GetAccountStagesDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usStocksInvestmentClient) GetReviewItems(ctx context.Context, in *GetReviewItemsRequest, opts ...grpc.CallOption) (*GetReviewItemsResponse, error) {
	out := new(GetReviewItemsResponse)
	err := c.cc.Invoke(ctx, UsStocksInvestment_GetReviewItems_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usStocksInvestmentClient) GetReviewItemDetails(ctx context.Context, in *GetReviewItemDetailsRequest, opts ...grpc.CallOption) (*GetReviewItemDetailsResponse, error) {
	out := new(GetReviewItemDetailsResponse)
	err := c.cc.Invoke(ctx, UsStocksInvestment_GetReviewItemDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usStocksInvestmentClient) MarkReviewAsDone(ctx context.Context, in *MarkReviewAsDoneRequest, opts ...grpc.CallOption) (*MarkReviewAsDoneResponse, error) {
	out := new(MarkReviewAsDoneResponse)
	err := c.cc.Invoke(ctx, UsStocksInvestment_MarkReviewAsDone_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UsStocksInvestmentServer is the server API for UsStocksInvestment service.
// All implementations should embed UnimplementedUsStocksInvestmentServer
// for forward compatibility
type UsStocksInvestmentServer interface {
	// To get list of  distinct stocks/etfs asset name invested by a user
	// Actor information will be enriched in header via interceptor
	// Returns empty list in case no investments found
	// enrichment and ticket validation method options are set because this is part of user information flow
	// this method is behind moderately sensitive information level because it will show what all auto invest rules are active for user
	// will return status ok with sherlock deeplink if customer auth is not satisfied
	GetPortfolioDetails(context.Context, *GetPortfolioDetailsRequest) (*GetPortfolioDetailsResponse, error)
	// To get list of stock/etf orders placed by the actor along with the details
	// in input request it takes at least one of these following values-
	//
	//	1.order_id
	//	2.from date, to date, activity_type(optional)
	//	3.asset name
	//
	// Actor information will be enriched in header via interceptor
	// Returns empty list in case no investment activities are found
	// enrichment and ticket validation method options are set because this is part of user information flow
	// this method is behind moderately sensitive information level because it will show the orders by a user and number of units in an order
	// will return status ok with sherlock deeplink if customer auth is not satisfied
	// Note: this is a paginated api. Pagination is controlled by page context definition in request and response.
	// If Order Id is sent in a request then this rpc will return that particular order
	GetUsStockActivities(context.Context, *GetUsStockActivitiesRequest) (*GetUsStockActivitiesResponse, error)
	// To get timeline of an order by order_id(external_order_id)
	// Actor information will be enriched in header via interceptor
	// enrichment and ticket validation method options are set because this is part of user information flow
	// this method is behind less sensitive information level because it will show the order's status
	// and timestamp when order reached that status
	// will return status ok with sherlock deeplink if customer auth is not satisfied
	GetActivityDetails(context.Context, *GetActivityDetailsRequest) (*GetActivityDetailsResponse, error)
	// To get list of remittance placed by the actor along with the details
	// in input request it takes at least one of these following values-
	//
	//	1.remittance_id
	//	2.from date, to date
	//
	// Actor information will be enriched in header via interceptor
	// Returns empty list in case no investment remittances are found
	// enrichment and ticket validation method options are set because this is part of user information flow
	// this method is behind moderately sensitive information level because it will show the orders by a user and number of units in an order
	// will return status ok with sherlock deeplink if customer auth is not satisfied
	// Note: this is a paginated api. Pagination is controlled by page context definition in request and response.
	// If Remittance Id is sent in a request then this rpc will return that particular Remittance order
	GetRemittances(context.Context, *GetRemittancesRequest) (*GetRemittancesResponse, error)
	// To get the timeline of a remittance order by ID (i.e., external ID of US stocks wallet load/withdrawal order)
	// Actor information will be enriched in header via interceptor
	// enrichment and ticket validation method options are set because this is part of user information flow
	// this method is behind less sensitive information level because it will show the remittance's status
	// and timestamp when order reached that status
	// will return status ok with sherlock deeplink if customer auth is not satisfied
	GetRemittanceOrderDetails(context.Context, *GetRemittanceOrderDetailsRequest) (*GetRemittanceOrderDetailsResponse, error)
	// to get user's account id, current stage and status info
	// Actor information will be enriched in header via interceptor
	// enrichment and ticket validation method options are set because this is part of user information flow
	// this method is behind less sensitive information level because it will show the order's account stage and status
	// will return status ok with sherlock deeplink if customer auth is not satisfied
	GetAccountBasicDetails(context.Context, *GetAccountBasicDetailsRequest) (*GetAccountBasicDetailsResponse, error)
	// to get user's all account stage details along with troubleshoot and description
	// Actor information will be enriched in header via interceptor
	// enrichment and ticket validation method options are set because this is part of user information flow
	// this method is behind less sensitive information level because it will show the order's account stage and status
	// will return status ok with sherlock deeplink if customer auth is not satisfied
	GetAccountStagesDetails(context.Context, *GetAccountStagesDetailsRequest) (*GetAccountStagesDetailsResponse, error)
	// to get pending items for manual review
	// Actor information will be enriched in header via interceptor
	// ticket validation, enrichment options are set to false and information level is set to insensitive
	// since this is not part of user information flow
	GetReviewItems(context.Context, *GetReviewItemsRequest) (*GetReviewItemsResponse, error)
	// to get details of an individual review item (e.g. pan image, liveness image etc)
	// Actor information will be enriched in header via interceptor
	// ticket validation, enrichment options are set to false and information level is set to insensitive
	// since this is not part of user information flow
	GetReviewItemDetails(context.Context, *GetReviewItemDetailsRequest) (*GetReviewItemDetailsResponse, error)
	// to get details of an individual review item (e.g. pan image, liveness image etc)
	// Actor information will be enriched in header via interceptor
	// ticket validation, enrichment options are set to false and information level is set to insensitive
	// since this is not part of user information flow
	MarkReviewAsDone(context.Context, *MarkReviewAsDoneRequest) (*MarkReviewAsDoneResponse, error)
}

// UnimplementedUsStocksInvestmentServer should be embedded to have forward compatible implementations.
type UnimplementedUsStocksInvestmentServer struct {
}

func (UnimplementedUsStocksInvestmentServer) GetPortfolioDetails(context.Context, *GetPortfolioDetailsRequest) (*GetPortfolioDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPortfolioDetails not implemented")
}
func (UnimplementedUsStocksInvestmentServer) GetUsStockActivities(context.Context, *GetUsStockActivitiesRequest) (*GetUsStockActivitiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUsStockActivities not implemented")
}
func (UnimplementedUsStocksInvestmentServer) GetActivityDetails(context.Context, *GetActivityDetailsRequest) (*GetActivityDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActivityDetails not implemented")
}
func (UnimplementedUsStocksInvestmentServer) GetRemittances(context.Context, *GetRemittancesRequest) (*GetRemittancesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRemittances not implemented")
}
func (UnimplementedUsStocksInvestmentServer) GetRemittanceOrderDetails(context.Context, *GetRemittanceOrderDetailsRequest) (*GetRemittanceOrderDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRemittanceOrderDetails not implemented")
}
func (UnimplementedUsStocksInvestmentServer) GetAccountBasicDetails(context.Context, *GetAccountBasicDetailsRequest) (*GetAccountBasicDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountBasicDetails not implemented")
}
func (UnimplementedUsStocksInvestmentServer) GetAccountStagesDetails(context.Context, *GetAccountStagesDetailsRequest) (*GetAccountStagesDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountStagesDetails not implemented")
}
func (UnimplementedUsStocksInvestmentServer) GetReviewItems(context.Context, *GetReviewItemsRequest) (*GetReviewItemsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReviewItems not implemented")
}
func (UnimplementedUsStocksInvestmentServer) GetReviewItemDetails(context.Context, *GetReviewItemDetailsRequest) (*GetReviewItemDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReviewItemDetails not implemented")
}
func (UnimplementedUsStocksInvestmentServer) MarkReviewAsDone(context.Context, *MarkReviewAsDoneRequest) (*MarkReviewAsDoneResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkReviewAsDone not implemented")
}

// UnsafeUsStocksInvestmentServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UsStocksInvestmentServer will
// result in compilation errors.
type UnsafeUsStocksInvestmentServer interface {
	mustEmbedUnimplementedUsStocksInvestmentServer()
}

func RegisterUsStocksInvestmentServer(s grpc.ServiceRegistrar, srv UsStocksInvestmentServer) {
	s.RegisterService(&UsStocksInvestment_ServiceDesc, srv)
}

func _UsStocksInvestment_GetPortfolioDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPortfolioDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsStocksInvestmentServer).GetPortfolioDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UsStocksInvestment_GetPortfolioDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsStocksInvestmentServer).GetPortfolioDetails(ctx, req.(*GetPortfolioDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsStocksInvestment_GetUsStockActivities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUsStockActivitiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsStocksInvestmentServer).GetUsStockActivities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UsStocksInvestment_GetUsStockActivities_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsStocksInvestmentServer).GetUsStockActivities(ctx, req.(*GetUsStockActivitiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsStocksInvestment_GetActivityDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActivityDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsStocksInvestmentServer).GetActivityDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UsStocksInvestment_GetActivityDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsStocksInvestmentServer).GetActivityDetails(ctx, req.(*GetActivityDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsStocksInvestment_GetRemittances_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRemittancesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsStocksInvestmentServer).GetRemittances(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UsStocksInvestment_GetRemittances_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsStocksInvestmentServer).GetRemittances(ctx, req.(*GetRemittancesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsStocksInvestment_GetRemittanceOrderDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRemittanceOrderDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsStocksInvestmentServer).GetRemittanceOrderDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UsStocksInvestment_GetRemittanceOrderDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsStocksInvestmentServer).GetRemittanceOrderDetails(ctx, req.(*GetRemittanceOrderDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsStocksInvestment_GetAccountBasicDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountBasicDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsStocksInvestmentServer).GetAccountBasicDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UsStocksInvestment_GetAccountBasicDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsStocksInvestmentServer).GetAccountBasicDetails(ctx, req.(*GetAccountBasicDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsStocksInvestment_GetAccountStagesDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountStagesDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsStocksInvestmentServer).GetAccountStagesDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UsStocksInvestment_GetAccountStagesDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsStocksInvestmentServer).GetAccountStagesDetails(ctx, req.(*GetAccountStagesDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsStocksInvestment_GetReviewItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReviewItemsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsStocksInvestmentServer).GetReviewItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UsStocksInvestment_GetReviewItems_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsStocksInvestmentServer).GetReviewItems(ctx, req.(*GetReviewItemsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsStocksInvestment_GetReviewItemDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReviewItemDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsStocksInvestmentServer).GetReviewItemDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UsStocksInvestment_GetReviewItemDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsStocksInvestmentServer).GetReviewItemDetails(ctx, req.(*GetReviewItemDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsStocksInvestment_MarkReviewAsDone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkReviewAsDoneRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsStocksInvestmentServer).MarkReviewAsDone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UsStocksInvestment_MarkReviewAsDone_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsStocksInvestmentServer).MarkReviewAsDone(ctx, req.(*MarkReviewAsDoneRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UsStocksInvestment_ServiceDesc is the grpc.ServiceDesc for UsStocksInvestment service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UsStocksInvestment_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cx.data_collector.investment.usstocks.UsStocksInvestment",
	HandlerType: (*UsStocksInvestmentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPortfolioDetails",
			Handler:    _UsStocksInvestment_GetPortfolioDetails_Handler,
		},
		{
			MethodName: "GetUsStockActivities",
			Handler:    _UsStocksInvestment_GetUsStockActivities_Handler,
		},
		{
			MethodName: "GetActivityDetails",
			Handler:    _UsStocksInvestment_GetActivityDetails_Handler,
		},
		{
			MethodName: "GetRemittances",
			Handler:    _UsStocksInvestment_GetRemittances_Handler,
		},
		{
			MethodName: "GetRemittanceOrderDetails",
			Handler:    _UsStocksInvestment_GetRemittanceOrderDetails_Handler,
		},
		{
			MethodName: "GetAccountBasicDetails",
			Handler:    _UsStocksInvestment_GetAccountBasicDetails_Handler,
		},
		{
			MethodName: "GetAccountStagesDetails",
			Handler:    _UsStocksInvestment_GetAccountStagesDetails_Handler,
		},
		{
			MethodName: "GetReviewItems",
			Handler:    _UsStocksInvestment_GetReviewItems_Handler,
		},
		{
			MethodName: "GetReviewItemDetails",
			Handler:    _UsStocksInvestment_GetReviewItemDetails_Handler,
		},
		{
			MethodName: "MarkReviewAsDone",
			Handler:    _UsStocksInvestment_MarkReviewAsDone_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/cx/data_collector/investment/usstocks/service.proto",
}
