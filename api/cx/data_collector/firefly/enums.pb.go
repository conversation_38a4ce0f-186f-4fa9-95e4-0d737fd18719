// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/data_collector/firefly/enums.proto

package firefly

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CardActionType int32

const (
	CardActionType_CARD_ACTION_TYPE_UNSPECIFIED          CardActionType = 0
	CardActionType_CARD_ACTION_TYPE_FREEZE_UNFREEZE_CARD CardActionType = 1
	CardActionType_CARD_ACTION_TYPE_REISSUE_CARD         CardActionType = 2
	CardActionType_CARD_ACTION_TYPE_PROCESS_DISPUTE      CardActionType = 3
)

// Enum value maps for CardActionType.
var (
	CardActionType_name = map[int32]string{
		0: "CARD_ACTION_TYPE_UNSPECIFIED",
		1: "CARD_ACTION_TYPE_FREEZE_UNFREEZE_CARD",
		2: "CARD_ACTION_TYPE_REISSUE_CARD",
		3: "CARD_ACTION_TYPE_PROCESS_DISPUTE",
	}
	CardActionType_value = map[string]int32{
		"CARD_ACTION_TYPE_UNSPECIFIED":          0,
		"CARD_ACTION_TYPE_FREEZE_UNFREEZE_CARD": 1,
		"CARD_ACTION_TYPE_REISSUE_CARD":         2,
		"CARD_ACTION_TYPE_PROCESS_DISPUTE":      3,
	}
)

func (x CardActionType) Enum() *CardActionType {
	p := new(CardActionType)
	*p = x
	return p
}

func (x CardActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_data_collector_firefly_enums_proto_enumTypes[0].Descriptor()
}

func (CardActionType) Type() protoreflect.EnumType {
	return &file_api_cx_data_collector_firefly_enums_proto_enumTypes[0]
}

func (x CardActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardActionType.Descriptor instead.
func (CardActionType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_data_collector_firefly_enums_proto_rawDescGZIP(), []int{0}
}

type CardRequestType int32

const (
	CardRequestType_CARD_REQUEST_TYPE_UNSPECIFIED   CardRequestType = 0
	CardRequestType_CARD_REQUEST_TYPE_FREEZE_CARD   CardRequestType = 1
	CardRequestType_CARD_REQUEST_TYPE_UNFREEZE_CARD CardRequestType = 2
)

// Enum value maps for CardRequestType.
var (
	CardRequestType_name = map[int32]string{
		0: "CARD_REQUEST_TYPE_UNSPECIFIED",
		1: "CARD_REQUEST_TYPE_FREEZE_CARD",
		2: "CARD_REQUEST_TYPE_UNFREEZE_CARD",
	}
	CardRequestType_value = map[string]int32{
		"CARD_REQUEST_TYPE_UNSPECIFIED":   0,
		"CARD_REQUEST_TYPE_FREEZE_CARD":   1,
		"CARD_REQUEST_TYPE_UNFREEZE_CARD": 2,
	}
)

func (x CardRequestType) Enum() *CardRequestType {
	p := new(CardRequestType)
	*p = x
	return p
}

func (x CardRequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardRequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_data_collector_firefly_enums_proto_enumTypes[1].Descriptor()
}

func (CardRequestType) Type() protoreflect.EnumType {
	return &file_api_cx_data_collector_firefly_enums_proto_enumTypes[1]
}

func (x CardRequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardRequestType.Descriptor instead.
func (CardRequestType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_data_collector_firefly_enums_proto_rawDescGZIP(), []int{1}
}

type DisputeType int32

const (
	DisputeType_DISPUTE_TYPE_UNSPECIFIED DisputeType = 0
	DisputeType_DISPUTE_TYPE_TRANSACTION DisputeType = 1
)

// Enum value maps for DisputeType.
var (
	DisputeType_name = map[int32]string{
		0: "DISPUTE_TYPE_UNSPECIFIED",
		1: "DISPUTE_TYPE_TRANSACTION",
	}
	DisputeType_value = map[string]int32{
		"DISPUTE_TYPE_UNSPECIFIED": 0,
		"DISPUTE_TYPE_TRANSACTION": 1,
	}
)

func (x DisputeType) Enum() *DisputeType {
	p := new(DisputeType)
	*p = x
	return p
}

func (x DisputeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisputeType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_data_collector_firefly_enums_proto_enumTypes[2].Descriptor()
}

func (DisputeType) Type() protoreflect.EnumType {
	return &file_api_cx_data_collector_firefly_enums_proto_enumTypes[2]
}

func (x DisputeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisputeType.Descriptor instead.
func (DisputeType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_data_collector_firefly_enums_proto_rawDescGZIP(), []int{2}
}

type CardControlType int32

const (
	CardControlType_CARD_CONTROL_TYPE_UNSPECIFIED CardControlType = 0
	// control type for ATM transactions
	CardControlType_CARD_CONTROL_TYPE_ATM CardControlType = 1
	// control type for e-commerce transactions
	CardControlType_CARD_CONTROL_TYPE_ECOM CardControlType = 2
	// control type for POS transactions
	CardControlType_CARD_CONTROL_TYPE_POS CardControlType = 3
	// control type for NFC/Contactless transactions
	CardControlType_CARD_CONTROL_TYPE_CONTACTLESS CardControlType = 4
	// control type for International transactions
	CardControlType_CARD_CONTROL_TYPE_INTERNATIONAL CardControlType = 5
)

// Enum value maps for CardControlType.
var (
	CardControlType_name = map[int32]string{
		0: "CARD_CONTROL_TYPE_UNSPECIFIED",
		1: "CARD_CONTROL_TYPE_ATM",
		2: "CARD_CONTROL_TYPE_ECOM",
		3: "CARD_CONTROL_TYPE_POS",
		4: "CARD_CONTROL_TYPE_CONTACTLESS",
		5: "CARD_CONTROL_TYPE_INTERNATIONAL",
	}
	CardControlType_value = map[string]int32{
		"CARD_CONTROL_TYPE_UNSPECIFIED":   0,
		"CARD_CONTROL_TYPE_ATM":           1,
		"CARD_CONTROL_TYPE_ECOM":          2,
		"CARD_CONTROL_TYPE_POS":           3,
		"CARD_CONTROL_TYPE_CONTACTLESS":   4,
		"CARD_CONTROL_TYPE_INTERNATIONAL": 5,
	}
)

func (x CardControlType) Enum() *CardControlType {
	p := new(CardControlType)
	*p = x
	return p
}

func (x CardControlType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardControlType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_data_collector_firefly_enums_proto_enumTypes[3].Descriptor()
}

func (CardControlType) Type() protoreflect.EnumType {
	return &file_api_cx_data_collector_firefly_enums_proto_enumTypes[3]
}

func (x CardControlType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardControlType.Descriptor instead.
func (CardControlType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_data_collector_firefly_enums_proto_rawDescGZIP(), []int{3}
}

type TransactionRequestType int32

const (
	TransactionRequestType_TRANSACTION_REQUEST_TYPE_UNSPECIFIED TransactionRequestType = 0
	TransactionRequestType_TRANSACTION_REQUEST_TYPE_FIRST_TEN   TransactionRequestType = 1
	TransactionRequestType_TRANSACTION_REQUEST_TYPE_LAST_TEN    TransactionRequestType = 2
)

// Enum value maps for TransactionRequestType.
var (
	TransactionRequestType_name = map[int32]string{
		0: "TRANSACTION_REQUEST_TYPE_UNSPECIFIED",
		1: "TRANSACTION_REQUEST_TYPE_FIRST_TEN",
		2: "TRANSACTION_REQUEST_TYPE_LAST_TEN",
	}
	TransactionRequestType_value = map[string]int32{
		"TRANSACTION_REQUEST_TYPE_UNSPECIFIED": 0,
		"TRANSACTION_REQUEST_TYPE_FIRST_TEN":   1,
		"TRANSACTION_REQUEST_TYPE_LAST_TEN":    2,
	}
)

func (x TransactionRequestType) Enum() *TransactionRequestType {
	p := new(TransactionRequestType)
	*p = x
	return p
}

func (x TransactionRequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionRequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_data_collector_firefly_enums_proto_enumTypes[4].Descriptor()
}

func (TransactionRequestType) Type() protoreflect.EnumType {
	return &file_api_cx_data_collector_firefly_enums_proto_enumTypes[4]
}

func (x TransactionRequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionRequestType.Descriptor instead.
func (TransactionRequestType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_data_collector_firefly_enums_proto_rawDescGZIP(), []int{4}
}

type OnboardingStageName int32

const (
	OnboardingStageName_ONBOARDING_STAGE_NAME_UNSPECIFIED                    OnboardingStageName = 0
	OnboardingStageName_ONBOARDING_STAGE_NAME_APPLICATION_INITIATION         OnboardingStageName = 1
	OnboardingStageName_ONBOARDING_STAGE_NAME_PROFILE_VALIDATION             OnboardingStageName = 2
	OnboardingStageName_ONBOARDING_STAGE_NAME_SHIPPING_ADDRESS_SELECTION     OnboardingStageName = 3
	OnboardingStageName_ONBOARDING_STAGE_NAME_BILL_GENERATION_DATE_SELECTION OnboardingStageName = 4
	OnboardingStageName_ONBOARDING_STAGE_NAME_VKYC_COMPLETION                OnboardingStageName = 5
	OnboardingStageName_ONBOARDING_STAGE_NAME_LIVENESS_FACEMATCH_CHECK       OnboardingStageName = 6
	OnboardingStageName_ONBOARDING_STAGE_NAME_CARD_CREATION                  OnboardingStageName = 7
)

// Enum value maps for OnboardingStageName.
var (
	OnboardingStageName_name = map[int32]string{
		0: "ONBOARDING_STAGE_NAME_UNSPECIFIED",
		1: "ONBOARDING_STAGE_NAME_APPLICATION_INITIATION",
		2: "ONBOARDING_STAGE_NAME_PROFILE_VALIDATION",
		3: "ONBOARDING_STAGE_NAME_SHIPPING_ADDRESS_SELECTION",
		4: "ONBOARDING_STAGE_NAME_BILL_GENERATION_DATE_SELECTION",
		5: "ONBOARDING_STAGE_NAME_VKYC_COMPLETION",
		6: "ONBOARDING_STAGE_NAME_LIVENESS_FACEMATCH_CHECK",
		7: "ONBOARDING_STAGE_NAME_CARD_CREATION",
	}
	OnboardingStageName_value = map[string]int32{
		"ONBOARDING_STAGE_NAME_UNSPECIFIED":                    0,
		"ONBOARDING_STAGE_NAME_APPLICATION_INITIATION":         1,
		"ONBOARDING_STAGE_NAME_PROFILE_VALIDATION":             2,
		"ONBOARDING_STAGE_NAME_SHIPPING_ADDRESS_SELECTION":     3,
		"ONBOARDING_STAGE_NAME_BILL_GENERATION_DATE_SELECTION": 4,
		"ONBOARDING_STAGE_NAME_VKYC_COMPLETION":                5,
		"ONBOARDING_STAGE_NAME_LIVENESS_FACEMATCH_CHECK":       6,
		"ONBOARDING_STAGE_NAME_CARD_CREATION":                  7,
	}
)

func (x OnboardingStageName) Enum() *OnboardingStageName {
	p := new(OnboardingStageName)
	*p = x
	return p
}

func (x OnboardingStageName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OnboardingStageName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_data_collector_firefly_enums_proto_enumTypes[5].Descriptor()
}

func (OnboardingStageName) Type() protoreflect.EnumType {
	return &file_api_cx_data_collector_firefly_enums_proto_enumTypes[5]
}

func (x OnboardingStageName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OnboardingStageName.Descriptor instead.
func (OnboardingStageName) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_data_collector_firefly_enums_proto_rawDescGZIP(), []int{5}
}

type CardProgramType int32

const (
	CardProgramType_CARD_PROGRAM_TYPE_UNSPECIFIED CardProgramType = 0
	CardProgramType_CARD_PROGRAM_TYPE_UNSECURED   CardProgramType = 1
	CardProgramType_CARD_PROGRAM_TYPE_SECURED     CardProgramType = 2
)

// Enum value maps for CardProgramType.
var (
	CardProgramType_name = map[int32]string{
		0: "CARD_PROGRAM_TYPE_UNSPECIFIED",
		1: "CARD_PROGRAM_TYPE_UNSECURED",
		2: "CARD_PROGRAM_TYPE_SECURED",
	}
	CardProgramType_value = map[string]int32{
		"CARD_PROGRAM_TYPE_UNSPECIFIED": 0,
		"CARD_PROGRAM_TYPE_UNSECURED":   1,
		"CARD_PROGRAM_TYPE_SECURED":     2,
	}
)

func (x CardProgramType) Enum() *CardProgramType {
	p := new(CardProgramType)
	*p = x
	return p
}

func (x CardProgramType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardProgramType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_data_collector_firefly_enums_proto_enumTypes[6].Descriptor()
}

func (CardProgramType) Type() protoreflect.EnumType {
	return &file_api_cx_data_collector_firefly_enums_proto_enumTypes[6]
}

func (x CardProgramType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardProgramType.Descriptor instead.
func (CardProgramType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_data_collector_firefly_enums_proto_rawDescGZIP(), []int{6}
}

var File_api_cx_data_collector_firefly_enums_proto protoreflect.FileDescriptor

var file_api_cx_data_collector_firefly_enums_proto_rawDesc = []byte{
	0x0a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x63, 0x78, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2a, 0xa6, 0x01, 0x0a, 0x0e, 0x43, 0x61, 0x72, 0x64, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x55, 0x4e, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x49, 0x53, 0x53,
	0x55, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52,
	0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x10, 0x03, 0x2a,
	0x7c, 0x0a, 0x0f, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a,
	0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x02, 0x2a, 0x49, 0x0a,
	0x0b, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18,
	0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x49,
	0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x2a, 0xce, 0x01, 0x0a, 0x0f, 0x43, 0x61, 0x72,
	0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x19, 0x0a, 0x15, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x54, 0x4d, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x45, 0x43, 0x4f, 0x4d, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43,
	0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4f, 0x53, 0x10,
	0x03, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f,
	0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x4c, 0x45,
	0x53, 0x53, 0x10, 0x04, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4f, 0x4e,
	0x54, 0x52, 0x4f, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x10, 0x05, 0x2a, 0x91, 0x01, 0x0a, 0x16, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x24, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x26,
	0x0a, 0x22, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x52, 0x53, 0x54,
	0x5f, 0x54, 0x45, 0x4e, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x54, 0x45, 0x4e, 0x10, 0x02, 0x2a, 0x94, 0x03,
	0x0a, 0x13, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44,
	0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x30, 0x0a, 0x2c,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x2c,
	0x0a, 0x28, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x34, 0x0a, 0x30,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x53, 0x48, 0x49, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x41,
	0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x03, 0x12, 0x38, 0x0a, 0x34, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x42, 0x49, 0x4c, 0x4c,
	0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x29, 0x0a, 0x25,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c,
	0x45, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x32, 0x0a, 0x2e, 0x4f, 0x4e, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x4d, 0x41,
	0x54, 0x43, 0x48, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x06, 0x12, 0x27, 0x0a, 0x23, 0x4f,
	0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x07, 0x2a, 0x74, 0x0a, 0x0f, 0x43, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x10, 0x02, 0x42, 0x6c, 0x0a, 0x34, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x78, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_data_collector_firefly_enums_proto_rawDescOnce sync.Once
	file_api_cx_data_collector_firefly_enums_proto_rawDescData = file_api_cx_data_collector_firefly_enums_proto_rawDesc
)

func file_api_cx_data_collector_firefly_enums_proto_rawDescGZIP() []byte {
	file_api_cx_data_collector_firefly_enums_proto_rawDescOnce.Do(func() {
		file_api_cx_data_collector_firefly_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_data_collector_firefly_enums_proto_rawDescData)
	})
	return file_api_cx_data_collector_firefly_enums_proto_rawDescData
}

var file_api_cx_data_collector_firefly_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_api_cx_data_collector_firefly_enums_proto_goTypes = []interface{}{
	(CardActionType)(0),         // 0: cx.data_collector.firefly.CardActionType
	(CardRequestType)(0),        // 1: cx.data_collector.firefly.CardRequestType
	(DisputeType)(0),            // 2: cx.data_collector.firefly.DisputeType
	(CardControlType)(0),        // 3: cx.data_collector.firefly.CardControlType
	(TransactionRequestType)(0), // 4: cx.data_collector.firefly.TransactionRequestType
	(OnboardingStageName)(0),    // 5: cx.data_collector.firefly.OnboardingStageName
	(CardProgramType)(0),        // 6: cx.data_collector.firefly.CardProgramType
}
var file_api_cx_data_collector_firefly_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_cx_data_collector_firefly_enums_proto_init() }
func file_api_cx_data_collector_firefly_enums_proto_init() {
	if File_api_cx_data_collector_firefly_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_data_collector_firefly_enums_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_cx_data_collector_firefly_enums_proto_goTypes,
		DependencyIndexes: file_api_cx_data_collector_firefly_enums_proto_depIdxs,
		EnumInfos:         file_api_cx_data_collector_firefly_enums_proto_enumTypes,
	}.Build()
	File_api_cx_data_collector_firefly_enums_proto = out.File
	file_api_cx_data_collector_firefly_enums_proto_rawDesc = nil
	file_api_cx_data_collector_firefly_enums_proto_goTypes = nil
	file_api_cx_data_collector_firefly_enums_proto_depIdxs = nil
}
