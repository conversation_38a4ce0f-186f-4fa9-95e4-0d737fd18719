syntax = "proto3";

package cx.call_ivr;

import "api/cx/call_ivr/enums.proto";
import "api/cx/call_routing/enums.proto";
import "api/typesv2/user.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/cx/call_ivr";
option java_package = "com.github.epifi.gamma.api.cx.call_ivr";

message IvrStateInfo {
  // Type of IVR that the user is currently responding to
  IvrType ivr_type = 1;
  // Id of the question that will be served next to the user
  int32 ivr_question_id = 2;
  // Current state of the IVR based on the user action
  // Example : In case of a successful auth, the IVR can transition to 'completed' state
  IvrState ivr_state = 3;
  // recording id (if any) that needs to be played to the user
  call_routing.RecordingIdentifier recording_id = 4;
  // wait duration needed in seconds (if any) post which the vendor will start polling again from GetIVRState RPC
  int32 wait_duration = 5;
  // preferred language of the user
  api.typesv2.Language preferred_language = 6;
}

// proto msg that will be used to store call ivr related details
message CallIvrDetail {
  // identifier of the row
  string id = 1;
  // ivr type that the user is currently answering
  IvrType current_ivr_type = 2;
  // ivr type that the user previously answered
  IvrType previous_ivr_type = 3;
  // id of the question being currently answered
  int32 current_question_id = 4;
  // id of the question that was previously answered
  int32 previous_question_id = 5;
  // wait duration after which the vendor starts polling
  int32 wait_duration = 6;
  // identifier of the call on which ivr was answered
  string monitor_ucid = 7;
  // actor id of the user who answered the call
  // can be unavailable for unregistered users
  string actor_id = 8;
  // current state of the ivr
  IvrState current_ivr_state = 9;
  // previous state of the ivr
  IvrState previous_ivr_state = 10;
  // timestamp at which this row was created in DB
  google.protobuf.Timestamp created_at = 11;
  // timestamp at which this row was last updated
  google.protobuf.Timestamp updated_at = 12;
  // previous question response
  string previous_question_response = 13;
  // preferred language of the user
  api.typesv2.Language preferred_language = 14;
}

// CacheIvrDetails will be stored in cache to make decisions like which language question to be asked,
// when to drop off if user is trying to abuse the ivr etc.
message CacheIvrDetails {
  api.typesv2.Language preferred_language = 1;
  int32 invalid_input_count = 2;
  int32 repeat_input_count = 3;
}
