// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/sherlock_feedback/sherlock_feedback.proto

package sherlock_feedback

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// proto msg describing exhaustive set of details of feedback submitted by agent
type SherlockFeedbackDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// db record id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// describe the type of feedback
	FeedbackCategory FeedbackCategory `protobuf:"varint,2,opt,name=feedback_category,json=feedbackCategory,proto3,enum=cx.sherlock_feedback.FeedbackCategory" json:"feedback_category,omitempty"`
	// actual feedback
	FeedbackMessage string `protobuf:"bytes,3,opt,name=feedback_message,json=feedbackMessage,proto3" json:"feedback_message,omitempty"`
	// feedback meta data has one to one mapping with feedback category.
	FeedbackMetaData *FeedbackMetaData `protobuf:"bytes,4,opt,name=feedback_meta_data,json=feedbackMetaData,proto3" json:"feedback_meta_data,omitempty"`
	// do not have to populate this in request
	// agent who submits the feedback
	AgentEmail string `protobuf:"bytes,5,opt,name=agent_email,json=agentEmail,proto3" json:"agent_email,omitempty"`
	// what type of feedback identifier it is: ticket_id
	FeedbackIdentifierType FeedbackIdentifierType `protobuf:"varint,6,opt,name=feedback_identifier_type,json=feedbackIdentifierType,proto3,enum=cx.sherlock_feedback.FeedbackIdentifierType" json:"feedback_identifier_type,omitempty"`
	// have one to one mapping with feedback_identifier_type
	// contain the value of a particular identifier type
	FeedbackIdentifierValue string `protobuf:"bytes,7,opt,name=feedback_identifier_value,json=feedbackIdentifierValue,proto3" json:"feedback_identifier_value,omitempty"`
	// the time at which we got feedback and recorded into the db
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// last time when we updated this feedback in db
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *SherlockFeedbackDetails) Reset() {
	*x = SherlockFeedbackDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_sherlock_feedback_sherlock_feedback_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SherlockFeedbackDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SherlockFeedbackDetails) ProtoMessage() {}

func (x *SherlockFeedbackDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_sherlock_feedback_sherlock_feedback_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SherlockFeedbackDetails.ProtoReflect.Descriptor instead.
func (*SherlockFeedbackDetails) Descriptor() ([]byte, []int) {
	return file_api_cx_sherlock_feedback_sherlock_feedback_proto_rawDescGZIP(), []int{0}
}

func (x *SherlockFeedbackDetails) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SherlockFeedbackDetails) GetFeedbackCategory() FeedbackCategory {
	if x != nil {
		return x.FeedbackCategory
	}
	return FeedbackCategory_FEEDBACK_CATEGORY_UNSPECIFIED
}

func (x *SherlockFeedbackDetails) GetFeedbackMessage() string {
	if x != nil {
		return x.FeedbackMessage
	}
	return ""
}

func (x *SherlockFeedbackDetails) GetFeedbackMetaData() *FeedbackMetaData {
	if x != nil {
		return x.FeedbackMetaData
	}
	return nil
}

func (x *SherlockFeedbackDetails) GetAgentEmail() string {
	if x != nil {
		return x.AgentEmail
	}
	return ""
}

func (x *SherlockFeedbackDetails) GetFeedbackIdentifierType() FeedbackIdentifierType {
	if x != nil {
		return x.FeedbackIdentifierType
	}
	return FeedbackIdentifierType_FEEDBACK_IDENTIFIER_TYPE_UNSPECIFIED
}

func (x *SherlockFeedbackDetails) GetFeedbackIdentifierValue() string {
	if x != nil {
		return x.FeedbackIdentifierValue
	}
	return ""
}

func (x *SherlockFeedbackDetails) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SherlockFeedbackDetails) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// proto msg describing exhaustive set of secondary details of feedback submitted by agent
type FeedbackMetaData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// describes the frequency of issue reported by agent
	Frequency FeedbackFrequency `protobuf:"varint,3,opt,name=frequency,proto3,enum=cx.sherlock_feedback.FeedbackFrequency" json:"frequency,omitempty"`
	// describes if agent highlighted current feedback/issue before
	IsHighlightedBefore common.BooleanEnum `protobuf:"varint,4,opt,name=is_highlighted_before,json=isHighlightedBefore,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_highlighted_before,omitempty"`
	// tell if the current feedback/issue is urgent or not
	IsUrgent common.BooleanEnum `protobuf:"varint,5,opt,name=is_urgent,json=isUrgent,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_urgent,omitempty"`
}

func (x *FeedbackMetaData) Reset() {
	*x = FeedbackMetaData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_sherlock_feedback_sherlock_feedback_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeedbackMetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedbackMetaData) ProtoMessage() {}

func (x *FeedbackMetaData) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_sherlock_feedback_sherlock_feedback_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedbackMetaData.ProtoReflect.Descriptor instead.
func (*FeedbackMetaData) Descriptor() ([]byte, []int) {
	return file_api_cx_sherlock_feedback_sherlock_feedback_proto_rawDescGZIP(), []int{1}
}

func (x *FeedbackMetaData) GetFrequency() FeedbackFrequency {
	if x != nil {
		return x.Frequency
	}
	return FeedbackFrequency_FEEDBACK_FREQUENCY_UNSPECIFIED
}

func (x *FeedbackMetaData) GetIsHighlightedBefore() common.BooleanEnum {
	if x != nil {
		return x.IsHighlightedBefore
	}
	return common.BooleanEnum(0)
}

func (x *FeedbackMetaData) GetIsUrgent() common.BooleanEnum {
	if x != nil {
		return x.IsUrgent
	}
	return common.BooleanEnum(0)
}

// Proto message for backend to not expose model in DAO methods
type SherlockFeedbackMetaData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// db record id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// feedback id
	FeedbackId string `protobuf:"bytes,2,opt,name=feedback_id,json=feedbackId,proto3" json:"feedback_id,omitempty"`
	// describes the frequency of issue reported by agent
	Frequency FeedbackFrequency `protobuf:"varint,3,opt,name=frequency,proto3,enum=cx.sherlock_feedback.FeedbackFrequency" json:"frequency,omitempty"`
	// describes if agent highlighted current feedback/issue before
	IsHighlightedBefore common.BooleanEnum `protobuf:"varint,4,opt,name=is_highlighted_before,json=isHighlightedBefore,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_highlighted_before,omitempty"`
	// tell if the current feedback/issue is urgent or not
	IsUrgent common.BooleanEnum `protobuf:"varint,5,opt,name=is_urgent,json=isUrgent,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_urgent,omitempty"`
	// the time at which we got feedback meta data and recorded into the db
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// last time when we updated this feedback meta data in db
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *SherlockFeedbackMetaData) Reset() {
	*x = SherlockFeedbackMetaData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_sherlock_feedback_sherlock_feedback_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SherlockFeedbackMetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SherlockFeedbackMetaData) ProtoMessage() {}

func (x *SherlockFeedbackMetaData) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_sherlock_feedback_sherlock_feedback_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SherlockFeedbackMetaData.ProtoReflect.Descriptor instead.
func (*SherlockFeedbackMetaData) Descriptor() ([]byte, []int) {
	return file_api_cx_sherlock_feedback_sherlock_feedback_proto_rawDescGZIP(), []int{2}
}

func (x *SherlockFeedbackMetaData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SherlockFeedbackMetaData) GetFeedbackId() string {
	if x != nil {
		return x.FeedbackId
	}
	return ""
}

func (x *SherlockFeedbackMetaData) GetFrequency() FeedbackFrequency {
	if x != nil {
		return x.Frequency
	}
	return FeedbackFrequency_FEEDBACK_FREQUENCY_UNSPECIFIED
}

func (x *SherlockFeedbackMetaData) GetIsHighlightedBefore() common.BooleanEnum {
	if x != nil {
		return x.IsHighlightedBefore
	}
	return common.BooleanEnum(0)
}

func (x *SherlockFeedbackMetaData) GetIsUrgent() common.BooleanEnum {
	if x != nil {
		return x.IsUrgent
	}
	return common.BooleanEnum(0)
}

func (x *SherlockFeedbackMetaData) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SherlockFeedbackMetaData) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type SherlockFeedbackFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Mandatory: describe the type of feedback given by agent
	FeedbackCategory FeedbackCategory `protobuf:"varint,1,opt,name=feedback_category,json=feedbackCategory,proto3,enum=cx.sherlock_feedback.FeedbackCategory" json:"feedback_category,omitempty"`
	// Mandatory: will get all feedbacks who,s createdAt >= from_date
	FromDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=from_date,json=fromDate,proto3" json:"from_date,omitempty"`
	// Mandatory: will get all feedbacks who,s createdAt <= to_date
	ToDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=to_date,json=toDate,proto3" json:"to_date,omitempty"`
	// agent mail who submits the feedback
	AgentEmail string `protobuf:"bytes,4,opt,name=agent_email,json=agentEmail,proto3" json:"agent_email,omitempty"`
	// feedback meta data has one to one mapping with feedback category.
	FeedbackMetaData *FeedbackMetaData `protobuf:"bytes,5,opt,name=feedback_meta_data,json=feedbackMetaData,proto3" json:"feedback_meta_data,omitempty"`
	// what type of feedback identifier it is: ticket_id
	FeedbackIdentifierType FeedbackIdentifierType `protobuf:"varint,6,opt,name=feedback_identifier_type,json=feedbackIdentifierType,proto3,enum=cx.sherlock_feedback.FeedbackIdentifierType" json:"feedback_identifier_type,omitempty"`
	// have one to one mapping with feedback_identifier_type
	// contain the value of a particular identifier type
	FeedbackIdentifierValue string `protobuf:"bytes,7,opt,name=feedback_identifier_value,json=feedbackIdentifierValue,proto3" json:"feedback_identifier_value,omitempty"`
}

func (x *SherlockFeedbackFilters) Reset() {
	*x = SherlockFeedbackFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_sherlock_feedback_sherlock_feedback_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SherlockFeedbackFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SherlockFeedbackFilters) ProtoMessage() {}

func (x *SherlockFeedbackFilters) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_sherlock_feedback_sherlock_feedback_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SherlockFeedbackFilters.ProtoReflect.Descriptor instead.
func (*SherlockFeedbackFilters) Descriptor() ([]byte, []int) {
	return file_api_cx_sherlock_feedback_sherlock_feedback_proto_rawDescGZIP(), []int{3}
}

func (x *SherlockFeedbackFilters) GetFeedbackCategory() FeedbackCategory {
	if x != nil {
		return x.FeedbackCategory
	}
	return FeedbackCategory_FEEDBACK_CATEGORY_UNSPECIFIED
}

func (x *SherlockFeedbackFilters) GetFromDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FromDate
	}
	return nil
}

func (x *SherlockFeedbackFilters) GetToDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ToDate
	}
	return nil
}

func (x *SherlockFeedbackFilters) GetAgentEmail() string {
	if x != nil {
		return x.AgentEmail
	}
	return ""
}

func (x *SherlockFeedbackFilters) GetFeedbackMetaData() *FeedbackMetaData {
	if x != nil {
		return x.FeedbackMetaData
	}
	return nil
}

func (x *SherlockFeedbackFilters) GetFeedbackIdentifierType() FeedbackIdentifierType {
	if x != nil {
		return x.FeedbackIdentifierType
	}
	return FeedbackIdentifierType_FEEDBACK_IDENTIFIER_TYPE_UNSPECIFIED
}

func (x *SherlockFeedbackFilters) GetFeedbackIdentifierValue() string {
	if x != nil {
		return x.FeedbackIdentifierValue
	}
	return ""
}

var File_api_cx_sherlock_feedback_sherlock_feedback_proto protoreflect.FileDescriptor

var file_api_cx_sherlock_feedback_sherlock_feedback_proto_rawDesc = []byte{
	0x0a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63,
	0x6b, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x2f, 0x73, 0x68, 0x65, 0x72, 0x6c,
	0x6f, 0x63, 0x6b, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x14, 0x63, 0x78, 0x2e, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x5f,
	0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f,
	0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x78, 0x2f, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x66, 0x65, 0x65,
	0x64, 0x62, 0x61, 0x63, 0x6b, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xba, 0x04, 0x0a, 0x17, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x46, 0x65,
	0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x53, 0x0a,
	0x11, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x63, 0x78, 0x2e, 0x73, 0x68,
	0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x2e,
	0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x52, 0x10, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x66, 0x65,
	0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x54, 0x0a,
	0x12, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x78, 0x2e, 0x73,
	0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b,
	0x2e, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x10, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x66, 0x0a, 0x18, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x63, 0x78, 0x2e, 0x73, 0x68, 0x65, 0x72,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x46, 0x65,
	0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x16, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x19,
	0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x17, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xec,
	0x01, 0x0a, 0x10, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x45, 0x0a, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x63, 0x78, 0x2e, 0x73, 0x68, 0x65, 0x72,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x46, 0x65,
	0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52,
	0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x53, 0x0a, 0x15, 0x69, 0x73,
	0x5f, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x65, 0x66,
	0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42,
	0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x13, 0x69, 0x73, 0x48, 0x69,
	0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x12,
	0x3c, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x75, 0x72, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x08, 0x69, 0x73, 0x55, 0x72, 0x67, 0x65, 0x6e, 0x74, 0x22, 0x9b, 0x03,
	0x0a, 0x18, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61,
	0x63, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x65,
	0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x09, 0x66,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x63, 0x78, 0x2e, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x66, 0x65, 0x65,
	0x64, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x46, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x79, 0x12, 0x53, 0x0a, 0x15, 0x69, 0x73, 0x5f, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67,
	0x68, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x13, 0x69, 0x73, 0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x65,
	0x64, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x12, 0x3c, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x75, 0x72,
	0x67, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x69, 0x73, 0x55,
	0x72, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xf7, 0x03, 0x0a, 0x17,
	0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x53, 0x0a, 0x11, 0x66, 0x65, 0x65, 0x64, 0x62,
	0x61, 0x63, 0x6b, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x63, 0x78, 0x2e, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61,
	0x63, 0x6b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x10, 0x66, 0x65, 0x65, 0x64,
	0x62, 0x61, 0x63, 0x6b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x37, 0x0a, 0x09,
	0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x66, 0x72, 0x6f,
	0x6d, 0x44, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x6f, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x06, 0x74, 0x6f, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x54, 0x0a, 0x12, 0x66,
	0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x78, 0x2e, 0x73, 0x68, 0x65,
	0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x46,
	0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x10, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x66, 0x0a, 0x18, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x63, 0x78, 0x2e, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63,
	0x6b, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x62,
	0x61, 0x63, 0x6b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x16, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x19, 0x66, 0x65, 0x65,
	0x64, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x66, 0x65,
	0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x62, 0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x78, 0x2e, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x5f,
	0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_cx_sherlock_feedback_sherlock_feedback_proto_rawDescOnce sync.Once
	file_api_cx_sherlock_feedback_sherlock_feedback_proto_rawDescData = file_api_cx_sherlock_feedback_sherlock_feedback_proto_rawDesc
)

func file_api_cx_sherlock_feedback_sherlock_feedback_proto_rawDescGZIP() []byte {
	file_api_cx_sherlock_feedback_sherlock_feedback_proto_rawDescOnce.Do(func() {
		file_api_cx_sherlock_feedback_sherlock_feedback_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_sherlock_feedback_sherlock_feedback_proto_rawDescData)
	})
	return file_api_cx_sherlock_feedback_sherlock_feedback_proto_rawDescData
}

var file_api_cx_sherlock_feedback_sherlock_feedback_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_cx_sherlock_feedback_sherlock_feedback_proto_goTypes = []interface{}{
	(*SherlockFeedbackDetails)(nil),  // 0: cx.sherlock_feedback.SherlockFeedbackDetails
	(*FeedbackMetaData)(nil),         // 1: cx.sherlock_feedback.FeedbackMetaData
	(*SherlockFeedbackMetaData)(nil), // 2: cx.sherlock_feedback.SherlockFeedbackMetaData
	(*SherlockFeedbackFilters)(nil),  // 3: cx.sherlock_feedback.SherlockFeedbackFilters
	(FeedbackCategory)(0),            // 4: cx.sherlock_feedback.FeedbackCategory
	(FeedbackIdentifierType)(0),      // 5: cx.sherlock_feedback.FeedbackIdentifierType
	(*timestamppb.Timestamp)(nil),    // 6: google.protobuf.Timestamp
	(FeedbackFrequency)(0),           // 7: cx.sherlock_feedback.FeedbackFrequency
	(common.BooleanEnum)(0),          // 8: api.typesv2.common.BooleanEnum
}
var file_api_cx_sherlock_feedback_sherlock_feedback_proto_depIdxs = []int32{
	4,  // 0: cx.sherlock_feedback.SherlockFeedbackDetails.feedback_category:type_name -> cx.sherlock_feedback.FeedbackCategory
	1,  // 1: cx.sherlock_feedback.SherlockFeedbackDetails.feedback_meta_data:type_name -> cx.sherlock_feedback.FeedbackMetaData
	5,  // 2: cx.sherlock_feedback.SherlockFeedbackDetails.feedback_identifier_type:type_name -> cx.sherlock_feedback.FeedbackIdentifierType
	6,  // 3: cx.sherlock_feedback.SherlockFeedbackDetails.created_at:type_name -> google.protobuf.Timestamp
	6,  // 4: cx.sherlock_feedback.SherlockFeedbackDetails.updated_at:type_name -> google.protobuf.Timestamp
	7,  // 5: cx.sherlock_feedback.FeedbackMetaData.frequency:type_name -> cx.sherlock_feedback.FeedbackFrequency
	8,  // 6: cx.sherlock_feedback.FeedbackMetaData.is_highlighted_before:type_name -> api.typesv2.common.BooleanEnum
	8,  // 7: cx.sherlock_feedback.FeedbackMetaData.is_urgent:type_name -> api.typesv2.common.BooleanEnum
	7,  // 8: cx.sherlock_feedback.SherlockFeedbackMetaData.frequency:type_name -> cx.sherlock_feedback.FeedbackFrequency
	8,  // 9: cx.sherlock_feedback.SherlockFeedbackMetaData.is_highlighted_before:type_name -> api.typesv2.common.BooleanEnum
	8,  // 10: cx.sherlock_feedback.SherlockFeedbackMetaData.is_urgent:type_name -> api.typesv2.common.BooleanEnum
	6,  // 11: cx.sherlock_feedback.SherlockFeedbackMetaData.created_at:type_name -> google.protobuf.Timestamp
	6,  // 12: cx.sherlock_feedback.SherlockFeedbackMetaData.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 13: cx.sherlock_feedback.SherlockFeedbackFilters.feedback_category:type_name -> cx.sherlock_feedback.FeedbackCategory
	6,  // 14: cx.sherlock_feedback.SherlockFeedbackFilters.from_date:type_name -> google.protobuf.Timestamp
	6,  // 15: cx.sherlock_feedback.SherlockFeedbackFilters.to_date:type_name -> google.protobuf.Timestamp
	1,  // 16: cx.sherlock_feedback.SherlockFeedbackFilters.feedback_meta_data:type_name -> cx.sherlock_feedback.FeedbackMetaData
	5,  // 17: cx.sherlock_feedback.SherlockFeedbackFilters.feedback_identifier_type:type_name -> cx.sherlock_feedback.FeedbackIdentifierType
	18, // [18:18] is the sub-list for method output_type
	18, // [18:18] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_api_cx_sherlock_feedback_sherlock_feedback_proto_init() }
func file_api_cx_sherlock_feedback_sherlock_feedback_proto_init() {
	if File_api_cx_sherlock_feedback_sherlock_feedback_proto != nil {
		return
	}
	file_api_cx_sherlock_feedback_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_cx_sherlock_feedback_sherlock_feedback_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SherlockFeedbackDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_sherlock_feedback_sherlock_feedback_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeedbackMetaData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_sherlock_feedback_sherlock_feedback_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SherlockFeedbackMetaData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_sherlock_feedback_sherlock_feedback_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SherlockFeedbackFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_sherlock_feedback_sherlock_feedback_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_cx_sherlock_feedback_sherlock_feedback_proto_goTypes,
		DependencyIndexes: file_api_cx_sherlock_feedback_sherlock_feedback_proto_depIdxs,
		MessageInfos:      file_api_cx_sherlock_feedback_sherlock_feedback_proto_msgTypes,
	}.Build()
	File_api_cx_sherlock_feedback_sherlock_feedback_proto = out.File
	file_api_cx_sherlock_feedback_sherlock_feedback_proto_rawDesc = nil
	file_api_cx_sherlock_feedback_sherlock_feedback_proto_goTypes = nil
	file_api_cx_sherlock_feedback_sherlock_feedback_proto_depIdxs = nil
}
