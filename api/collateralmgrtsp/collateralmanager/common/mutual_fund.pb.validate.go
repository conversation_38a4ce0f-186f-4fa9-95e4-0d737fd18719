// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/collateralmgrtsp/collateralmanager/common/mutual_fund.proto

package common

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MfAsset with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MfAsset) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MfAsset with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in MfAssetMultiError, or nil if none found.
func (m *MfAsset) ValidateAll() error {
	return m.validate(true)
}

func (m *MfAsset) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MfAssetValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MfAssetValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MfAssetValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetHoldings() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MfAssetValidationError{
						field:  fmt.Sprintf("Holdings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MfAssetValidationError{
						field:  fmt.Sprintf("Holdings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MfAssetValidationError{
					field:  fmt.Sprintf("Holdings[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MfAssetMultiError(errors)
	}

	return nil
}

// MfAssetMultiError is an error wrapping multiple validation errors returned
// by MfAsset.ValidateAll() if the designated constraints aren't met.
type MfAssetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MfAssetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MfAssetMultiError) AllErrors() []error { return m }

// MfAssetValidationError is the validation error returned by MfAsset.Validate
// if the designated constraints aren't met.
type MfAssetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MfAssetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MfAssetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MfAssetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MfAssetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MfAssetValidationError) ErrorName() string { return "MfAssetValidationError" }

// Error satisfies the builtin error interface
func (e MfAssetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMfAsset.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MfAssetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MfAssetValidationError{}

// Validate checks the field values on MfHolding with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MfHolding) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MfHolding with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MfHoldingMultiError, or nil
// if none found.
func (m *MfHolding) ValidateAll() error {
	return m.validate(true)
}

func (m *MfHolding) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MfHoldingValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MfHoldingValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MfHoldingValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AvailableUnits

	// no validation rules for IsDemat

	// no validation rules for LienUnitsFlag

	if all {
		switch v := interface{}(m.GetSchemeDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MfHoldingValidationError{
					field:  "SchemeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MfHoldingValidationError{
					field:  "SchemeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSchemeDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MfHoldingValidationError{
				field:  "SchemeDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOtherDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MfHoldingValidationError{
					field:  "OtherDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MfHoldingValidationError{
					field:  "OtherDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOtherDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MfHoldingValidationError{
				field:  "OtherDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MfHoldingMultiError(errors)
	}

	return nil
}

// MfHoldingMultiError is an error wrapping multiple validation errors returned
// by MfHolding.ValidateAll() if the designated constraints aren't met.
type MfHoldingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MfHoldingMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MfHoldingMultiError) AllErrors() []error { return m }

// MfHoldingValidationError is the validation error returned by
// MfHolding.Validate if the designated constraints aren't met.
type MfHoldingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MfHoldingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MfHoldingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MfHoldingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MfHoldingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MfHoldingValidationError) ErrorName() string { return "MfHoldingValidationError" }

// Error satisfies the builtin error interface
func (e MfHoldingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMfHolding.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MfHoldingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MfHoldingValidationError{}

// Validate checks the field values on MfHoldingIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MfHoldingIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MfHoldingIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MfHoldingIdentifierMultiError, or nil if none found.
func (m *MfHoldingIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *MfHoldingIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Folio

	// no validation rules for Isin

	// no validation rules for RtaName

	if len(errors) > 0 {
		return MfHoldingIdentifierMultiError(errors)
	}

	return nil
}

// MfHoldingIdentifierMultiError is an error wrapping multiple validation
// errors returned by MfHoldingIdentifier.ValidateAll() if the designated
// constraints aren't met.
type MfHoldingIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MfHoldingIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MfHoldingIdentifierMultiError) AllErrors() []error { return m }

// MfHoldingIdentifierValidationError is the validation error returned by
// MfHoldingIdentifier.Validate if the designated constraints aren't met.
type MfHoldingIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MfHoldingIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MfHoldingIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MfHoldingIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MfHoldingIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MfHoldingIdentifierValidationError) ErrorName() string {
	return "MfHoldingIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e MfHoldingIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMfHoldingIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MfHoldingIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MfHoldingIdentifierValidationError{}

// Validate checks the field values on MfHoldingSchemeDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MfHoldingSchemeDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MfHoldingSchemeDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MfHoldingSchemeDetailsMultiError, or nil if none found.
func (m *MfHoldingSchemeDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *MfHoldingSchemeDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Amc

	// no validation rules for AmcName

	// no validation rules for SchemeName

	// no validation rules for SchemeCode

	// no validation rules for SchemeOption

	// no validation rules for AssetType

	// no validation rules for SchemeType

	// no validation rules for IcdwChangeAllowed

	if len(errors) > 0 {
		return MfHoldingSchemeDetailsMultiError(errors)
	}

	return nil
}

// MfHoldingSchemeDetailsMultiError is an error wrapping multiple validation
// errors returned by MfHoldingSchemeDetails.ValidateAll() if the designated
// constraints aren't met.
type MfHoldingSchemeDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MfHoldingSchemeDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MfHoldingSchemeDetailsMultiError) AllErrors() []error { return m }

// MfHoldingSchemeDetailsValidationError is the validation error returned by
// MfHoldingSchemeDetails.Validate if the designated constraints aren't met.
type MfHoldingSchemeDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MfHoldingSchemeDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MfHoldingSchemeDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MfHoldingSchemeDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MfHoldingSchemeDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MfHoldingSchemeDetailsValidationError) ErrorName() string {
	return "MfHoldingSchemeDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e MfHoldingSchemeDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMfHoldingSchemeDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MfHoldingSchemeDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MfHoldingSchemeDetailsValidationError{}

// Validate checks the field values on MfHoldingOtherDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MfHoldingOtherDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MfHoldingOtherDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MfHoldingOtherDetailsMultiError, or nil if none found.
func (m *MfHoldingOtherDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *MfHoldingOtherDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModeOfHolding

	// no validation rules for TaxStatus

	// no validation rules for TransactionSource

	// no validation rules for BrokerCode

	// no validation rules for BrokerName

	// no validation rules for PurAllow

	// no validation rules for RedAllow

	// no validation rules for SwtAllow

	// no validation rules for SipAllow

	// no validation rules for StpAllow

	// no validation rules for SwpAllow

	// no validation rules for PlanMode

	// no validation rules for NomineeStatus

	// no validation rules for Bank

	if len(errors) > 0 {
		return MfHoldingOtherDetailsMultiError(errors)
	}

	return nil
}

// MfHoldingOtherDetailsMultiError is an error wrapping multiple validation
// errors returned by MfHoldingOtherDetails.ValidateAll() if the designated
// constraints aren't met.
type MfHoldingOtherDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MfHoldingOtherDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MfHoldingOtherDetailsMultiError) AllErrors() []error { return m }

// MfHoldingOtherDetailsValidationError is the validation error returned by
// MfHoldingOtherDetails.Validate if the designated constraints aren't met.
type MfHoldingOtherDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MfHoldingOtherDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MfHoldingOtherDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MfHoldingOtherDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MfHoldingOtherDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MfHoldingOtherDetailsValidationError) ErrorName() string {
	return "MfHoldingOtherDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e MfHoldingOtherDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMfHoldingOtherDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MfHoldingOtherDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MfHoldingOtherDetailsValidationError{}

// Validate checks the field values on MfAsset_Identifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MfAsset_Identifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MfAsset_Identifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MfAsset_IdentifierMultiError, or nil if none found.
func (m *MfAsset_Identifier) ValidateAll() error {
	return m.validate(true)
}

func (m *MfAsset_Identifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Pan

	// no validation rules for Pekrn

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetPhone()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MfAsset_IdentifierValidationError{
					field:  "Phone",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MfAsset_IdentifierValidationError{
					field:  "Phone",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhone()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MfAsset_IdentifierValidationError{
				field:  "Phone",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MfAsset_IdentifierMultiError(errors)
	}

	return nil
}

// MfAsset_IdentifierMultiError is an error wrapping multiple validation errors
// returned by MfAsset_Identifier.ValidateAll() if the designated constraints
// aren't met.
type MfAsset_IdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MfAsset_IdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MfAsset_IdentifierMultiError) AllErrors() []error { return m }

// MfAsset_IdentifierValidationError is the validation error returned by
// MfAsset_Identifier.Validate if the designated constraints aren't met.
type MfAsset_IdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MfAsset_IdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MfAsset_IdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MfAsset_IdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MfAsset_IdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MfAsset_IdentifierValidationError) ErrorName() string {
	return "MfAsset_IdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e MfAsset_IdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMfAsset_Identifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MfAsset_IdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MfAsset_IdentifierValidationError{}
