// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/celestial/activity/notification/notification.proto

package notification

import (
	comms "github.com/epifi/gamma/api/comms"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Notification consists meta data needed to notify the end user.
type Notification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to UserIdentifier:
	//
	//	*Notification_UserId
	//	*Notification_PhoneNumber
	//	*Notification_EmailId
	UserIdentifier isNotification_UserIdentifier `protobuf_oneof:"user_identifier"`
	// List containing templates for notifications in their respective formats.
	CommunicationList []*comms.Communication `protobuf:"bytes,4,rep,name=communication_list,json=communicationList,proto3" json:"communication_list,omitempty"`
	// Specifies how the request should be processed (Quality of Service)
	QualityOfService comms.QoS `protobuf:"varint,5,opt,name=quality_of_service,json=qualityOfService,proto3,enum=comms.QoS" json:"quality_of_service,omitempty"`
	// client from where the call is initiated, It will be the server name which will be set by the client interceptor
	// calling service do not need to set this field
	ClientId string `protobuf:"bytes,6,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *Notification) Reset() {
	*x = Notification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_celestial_activity_notification_notification_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Notification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Notification) ProtoMessage() {}

func (x *Notification) ProtoReflect() protoreflect.Message {
	mi := &file_api_celestial_activity_notification_notification_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Notification.ProtoReflect.Descriptor instead.
func (*Notification) Descriptor() ([]byte, []int) {
	return file_api_celestial_activity_notification_notification_proto_rawDescGZIP(), []int{0}
}

func (m *Notification) GetUserIdentifier() isNotification_UserIdentifier {
	if m != nil {
		return m.UserIdentifier
	}
	return nil
}

func (x *Notification) GetUserId() string {
	if x, ok := x.GetUserIdentifier().(*Notification_UserId); ok {
		return x.UserId
	}
	return ""
}

func (x *Notification) GetPhoneNumber() string {
	if x, ok := x.GetUserIdentifier().(*Notification_PhoneNumber); ok {
		return x.PhoneNumber
	}
	return ""
}

func (x *Notification) GetEmailId() string {
	if x, ok := x.GetUserIdentifier().(*Notification_EmailId); ok {
		return x.EmailId
	}
	return ""
}

func (x *Notification) GetCommunicationList() []*comms.Communication {
	if x != nil {
		return x.CommunicationList
	}
	return nil
}

func (x *Notification) GetQualityOfService() comms.QoS {
	if x != nil {
		return x.QualityOfService
	}
	return comms.QoS(0)
}

func (x *Notification) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

type isNotification_UserIdentifier interface {
	isNotification_UserIdentifier()
}

type Notification_UserId struct {
	// The user id of the user to send the message to; If this is specified the service looks
	// up the phone number/email of the user based on the medium from a database to send the message
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3,oneof"`
}

type Notification_PhoneNumber struct {
	// The phone number to which the sms has to be sent. Does not include country code
	PhoneNumber string `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3,oneof"`
}

type Notification_EmailId struct {
	// The email id to which the email has to be sent
	EmailId string `protobuf:"bytes,3,opt,name=email_id,json=emailId,proto3,oneof"`
}

func (*Notification_UserId) isNotification_UserIdentifier() {}

func (*Notification_PhoneNumber) isNotification_UserIdentifier() {}

func (*Notification_EmailId) isNotification_UserIdentifier() {}

var File_api_celestial_activity_notification_notification_proto protoreflect.FileDescriptor

var file_api_celestial_activity_notification_notification_proto_rawDesc = []byte{
	0x0a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74,
	0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9a, 0x02, 0x0a, 0x0c, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x08, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x6d, 0x75,
	0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x75,
	0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x12,
	0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73,
	0x2e, 0x51, 0x6f, 0x53, 0x52, 0x10, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x66, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x42, 0x11, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x42, 0x78, 0x0a, 0x3a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x3a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_celestial_activity_notification_notification_proto_rawDescOnce sync.Once
	file_api_celestial_activity_notification_notification_proto_rawDescData = file_api_celestial_activity_notification_notification_proto_rawDesc
)

func file_api_celestial_activity_notification_notification_proto_rawDescGZIP() []byte {
	file_api_celestial_activity_notification_notification_proto_rawDescOnce.Do(func() {
		file_api_celestial_activity_notification_notification_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_celestial_activity_notification_notification_proto_rawDescData)
	})
	return file_api_celestial_activity_notification_notification_proto_rawDescData
}

var file_api_celestial_activity_notification_notification_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_celestial_activity_notification_notification_proto_goTypes = []interface{}{
	(*Notification)(nil),        // 0: celestial.activity.notification.Notification
	(*comms.Communication)(nil), // 1: comms.Communication
	(comms.QoS)(0),              // 2: comms.QoS
}
var file_api_celestial_activity_notification_notification_proto_depIdxs = []int32{
	1, // 0: celestial.activity.notification.Notification.communication_list:type_name -> comms.Communication
	2, // 1: celestial.activity.notification.Notification.quality_of_service:type_name -> comms.QoS
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_celestial_activity_notification_notification_proto_init() }
func file_api_celestial_activity_notification_notification_proto_init() {
	if File_api_celestial_activity_notification_notification_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_celestial_activity_notification_notification_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Notification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_celestial_activity_notification_notification_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Notification_UserId)(nil),
		(*Notification_PhoneNumber)(nil),
		(*Notification_EmailId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_celestial_activity_notification_notification_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_celestial_activity_notification_notification_proto_goTypes,
		DependencyIndexes: file_api_celestial_activity_notification_notification_proto_depIdxs,
		MessageInfos:      file_api_celestial_activity_notification_notification_proto_msgTypes,
	}.Build()
	File_api_celestial_activity_notification_notification_proto = out.File
	file_api_celestial_activity_notification_notification_proto_rawDesc = nil
	file_api_celestial_activity_notification_notification_proto_goTypes = nil
	file_api_celestial_activity_notification_notification_proto_depIdxs = nil
}
