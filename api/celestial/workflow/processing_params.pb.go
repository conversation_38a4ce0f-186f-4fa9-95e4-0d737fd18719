// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/celestial/workflow/processing_params.proto

package workflow

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Workflow processing params - all the params required to process a workflow
type ProcessingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// An opaque blob containing the data needed for processing activity for a workflow.
	// This might vary based on the type of workflow. The data inside the blob will
	// depend on underlying domain service.
	Payload []byte `protobuf:"bytes,1,opt,name=payload,proto3" json:"payload,omitempty"`
	// Client details corresponding to the service initiating workflow request. The combination of client and client_req_id must be unique.
	ClientReqId *ClientReqId `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
}

func (x *ProcessingParams) Reset() {
	*x = ProcessingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_celestial_workflow_processing_params_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessingParams) ProtoMessage() {}

func (x *ProcessingParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_celestial_workflow_processing_params_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessingParams.ProtoReflect.Descriptor instead.
func (*ProcessingParams) Descriptor() ([]byte, []int) {
	return file_api_celestial_workflow_processing_params_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessingParams) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *ProcessingParams) GetClientReqId() *ClientReqId {
	if x != nil {
		return x.ClientReqId
	}
	return nil
}

var File_api_celestial_workflow_processing_params_proto protoreflect.FileDescriptor

var file_api_celestial_workflow_processing_params_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x12, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74,
	0x69, 0x61, 0x6c, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x71, 0x0a, 0x10, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07,
	0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x43, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x52,
	0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x42, 0x66, 0x0a, 0x31,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x62, 0x65, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63,
	0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x5a, 0x31, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x62, 0x65, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_celestial_workflow_processing_params_proto_rawDescOnce sync.Once
	file_api_celestial_workflow_processing_params_proto_rawDescData = file_api_celestial_workflow_processing_params_proto_rawDesc
)

func file_api_celestial_workflow_processing_params_proto_rawDescGZIP() []byte {
	file_api_celestial_workflow_processing_params_proto_rawDescOnce.Do(func() {
		file_api_celestial_workflow_processing_params_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_celestial_workflow_processing_params_proto_rawDescData)
	})
	return file_api_celestial_workflow_processing_params_proto_rawDescData
}

var file_api_celestial_workflow_processing_params_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_celestial_workflow_processing_params_proto_goTypes = []interface{}{
	(*ProcessingParams)(nil), // 0: celestial.workflow.ProcessingParams
	(*ClientReqId)(nil),      // 1: celestial.workflow.ClientReqId
}
var file_api_celestial_workflow_processing_params_proto_depIdxs = []int32{
	1, // 0: celestial.workflow.ProcessingParams.client_req_id:type_name -> celestial.workflow.ClientReqId
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_celestial_workflow_processing_params_proto_init() }
func file_api_celestial_workflow_processing_params_proto_init() {
	if File_api_celestial_workflow_processing_params_proto != nil {
		return
	}
	file_api_celestial_workflow_client_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_celestial_workflow_processing_params_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_celestial_workflow_processing_params_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_celestial_workflow_processing_params_proto_goTypes,
		DependencyIndexes: file_api_celestial_workflow_processing_params_proto_depIdxs,
		MessageInfos:      file_api_celestial_workflow_processing_params_proto_msgTypes,
	}.Build()
	File_api_celestial_workflow_processing_params_proto = out.File
	file_api_celestial_workflow_processing_params_proto_rawDesc = nil
	file_api_celestial_workflow_processing_params_proto_goTypes = nil
	file_api_celestial_workflow_processing_params_proto_depIdxs = nil
}
