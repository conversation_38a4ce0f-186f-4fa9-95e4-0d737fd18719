syntax = "proto3";

package search.cards;

option go_package = "github.com/epifi/gamma/api/search/cards";
option java_package = "com.github.epifi.gamma.api.search.cards";

// cardtype for the client to show widget of that type
// currently 3 types of cards are shown at client, can be added more
enum CardType {
  // unspecified type
  CARD_TYPE_UNSPECIFIED = 0;
  // time identifier card to show "Monday, 19 Jan"
  TIME_IDENTIFIER = 1;
  // separator line widget
  SEPARATOR = 2;
  // all transactions card for the filtered search
  TRANSACTION = 3;
}

// a card will indicate to client what type of widget to be shown,
// card will contain all the data for the widget
message Card {
  CardType card_type = 1;
  // data for a card can be one of
  // TimeIdentifierCard, separator card or transaction card
  oneof data {
    // time identifier card
    TimeIdentifierCard time_identifier_card = 2;
    // separator card
    SeparatorCard separator_card = 3;
    // transaction card
    TransactionCard transaction_card = 4;
  }
}

// this card is of type `TIME_IDENTIFIER`
// TODO(shubhra): add more field after client side structure is defined
message TimeIdentifierCard {
  string title = 1;
}

// TODO(shubhra): follow up with client if this widget is needed, and what are arguments
message SeparatorCard {
}

message TransactionCard {
  string img_url = 1;
  // short description for the transaction
  string short_desc = 2;
  // amount of the transaction. Should use api.typesv2.money ?
  float amount = 3;
}
