// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/wealthonboarding/digilocker_data.proto

package wealthonboarding

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = typesv2.Gender(0)
)

// Validate checks the field values on DigilockerData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DigilockerData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DigilockerData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DigilockerDataMultiError,
// or nil if none found.
func (m *DigilockerData) ValidateAll() error {
	return m.validate(true)
}

func (m *DigilockerData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPersonalData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerDataValidationError{
					field:  "PersonalData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerDataValidationError{
					field:  "PersonalData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPersonalData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerDataValidationError{
				field:  "PersonalData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPan()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerDataValidationError{
					field:  "Pan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerDataValidationError{
					field:  "Pan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPan()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerDataValidationError{
				field:  "Pan",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDrivingLicense()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerDataValidationError{
					field:  "DrivingLicense",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerDataValidationError{
					field:  "DrivingLicense",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDrivingLicense()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerDataValidationError{
				field:  "DrivingLicense",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDigilockerAadhaarData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerDataValidationError{
					field:  "DigilockerAadhaarData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerDataValidationError{
					field:  "DigilockerAadhaarData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDigilockerAadhaarData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerDataValidationError{
				field:  "DigilockerAadhaarData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAadhaarPdf()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerDataValidationError{
					field:  "AadhaarPdf",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerDataValidationError{
					field:  "AadhaarPdf",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAadhaarPdf()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerDataValidationError{
				field:  "AadhaarPdf",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPanDocument()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerDataValidationError{
					field:  "PanDocument",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerDataValidationError{
					field:  "PanDocument",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPanDocument()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerDataValidationError{
				field:  "PanDocument",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDrivingLicenseDocument()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerDataValidationError{
					field:  "DrivingLicenseDocument",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerDataValidationError{
					field:  "DrivingLicenseDocument",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDrivingLicenseDocument()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerDataValidationError{
				field:  "DrivingLicenseDocument",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RefreshToken

	if len(errors) > 0 {
		return DigilockerDataMultiError(errors)
	}

	return nil
}

// DigilockerDataMultiError is an error wrapping multiple validation errors
// returned by DigilockerData.ValidateAll() if the designated constraints
// aren't met.
type DigilockerDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DigilockerDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DigilockerDataMultiError) AllErrors() []error { return m }

// DigilockerDataValidationError is the validation error returned by
// DigilockerData.Validate if the designated constraints aren't met.
type DigilockerDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DigilockerDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DigilockerDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DigilockerDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DigilockerDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DigilockerDataValidationError) ErrorName() string { return "DigilockerDataValidationError" }

// Error satisfies the builtin error interface
func (e DigilockerDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDigilockerData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DigilockerDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DigilockerDataValidationError{}

// Validate checks the field values on DigilockerPersonalData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DigilockerPersonalData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DigilockerPersonalData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DigilockerPersonalDataMultiError, or nil if none found.
func (m *DigilockerPersonalData) ValidateAll() error {
	return m.validate(true)
}

func (m *DigilockerPersonalData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerPersonalDataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerPersonalDataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerPersonalDataValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerPersonalDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerPersonalDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerPersonalDataValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	// no validation rules for EAadhaar

	if all {
		switch v := interface{}(m.GetMobile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerPersonalDataValidationError{
					field:  "Mobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerPersonalDataValidationError{
					field:  "Mobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMobile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerPersonalDataValidationError{
				field:  "Mobile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DigilockerPersonalDataMultiError(errors)
	}

	return nil
}

// DigilockerPersonalDataMultiError is an error wrapping multiple validation
// errors returned by DigilockerPersonalData.ValidateAll() if the designated
// constraints aren't met.
type DigilockerPersonalDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DigilockerPersonalDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DigilockerPersonalDataMultiError) AllErrors() []error { return m }

// DigilockerPersonalDataValidationError is the validation error returned by
// DigilockerPersonalData.Validate if the designated constraints aren't met.
type DigilockerPersonalDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DigilockerPersonalDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DigilockerPersonalDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DigilockerPersonalDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DigilockerPersonalDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DigilockerPersonalDataValidationError) ErrorName() string {
	return "DigilockerPersonalDataValidationError"
}

// Error satisfies the builtin error interface
func (e DigilockerPersonalDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDigilockerPersonalData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DigilockerPersonalDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DigilockerPersonalDataValidationError{}

// Validate checks the field values on DigilockerAadhaarData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DigilockerAadhaarData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DigilockerAadhaarData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DigilockerAadhaarDataMultiError, or nil if none found.
func (m *DigilockerAadhaarData) ValidateAll() error {
	return m.validate(true)
}

func (m *DigilockerAadhaarData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUserImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerAadhaarDataValidationError{
					field:  "UserImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerAadhaarDataValidationError{
					field:  "UserImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerAadhaarDataValidationError{
				field:  "UserImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerAadhaarDataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerAadhaarDataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerAadhaarDataValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerAadhaarDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerAadhaarDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerAadhaarDataValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerAadhaarDataValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerAadhaarDataValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerAadhaarDataValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTtl()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerAadhaarDataValidationError{
					field:  "Ttl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerAadhaarDataValidationError{
					field:  "Ttl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTtl()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerAadhaarDataValidationError{
				field:  "Ttl",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaskedAadhaarNumber

	if all {
		switch v := interface{}(m.GetTs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerAadhaarDataValidationError{
					field:  "Ts",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerAadhaarDataValidationError{
					field:  "Ts",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerAadhaarDataValidationError{
				field:  "Ts",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserImageDocument()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigilockerAadhaarDataValidationError{
					field:  "UserImageDocument",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigilockerAadhaarDataValidationError{
					field:  "UserImageDocument",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserImageDocument()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigilockerAadhaarDataValidationError{
				field:  "UserImageDocument",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AadhaarXmlFilePath

	// no validation rules for CareOf

	// no validation rules for Landmark

	if len(errors) > 0 {
		return DigilockerAadhaarDataMultiError(errors)
	}

	return nil
}

// DigilockerAadhaarDataMultiError is an error wrapping multiple validation
// errors returned by DigilockerAadhaarData.ValidateAll() if the designated
// constraints aren't met.
type DigilockerAadhaarDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DigilockerAadhaarDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DigilockerAadhaarDataMultiError) AllErrors() []error { return m }

// DigilockerAadhaarDataValidationError is the validation error returned by
// DigilockerAadhaarData.Validate if the designated constraints aren't met.
type DigilockerAadhaarDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DigilockerAadhaarDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DigilockerAadhaarDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DigilockerAadhaarDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DigilockerAadhaarDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DigilockerAadhaarDataValidationError) ErrorName() string {
	return "DigilockerAadhaarDataValidationError"
}

// Error satisfies the builtin error interface
func (e DigilockerAadhaarDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDigilockerAadhaarData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DigilockerAadhaarDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DigilockerAadhaarDataValidationError{}
