// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/tiering/external/external.proto

//go:generate gen_sql -types=Tier,TierMovementType

package external

import (
	criteria "github.com/epifi/gamma/api/tiering/criteria"
	enums "github.com/epifi/gamma/api/tiering/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Tier int32

const (
	Tier_TIER_UNSPECIFIED Tier = 0
	// Basic tier
	Tier_TIER_FI_BASIC Tier = 1
	// Plus tier
	Tier_TIER_FI_PLUS Tier = 2
	// Infinite tier
	Tier_TIER_FI_INFINITE Tier = 3
	// Salary tier
	Tier_TIER_FI_SALARY Tier = 4
	// Salary Lite tier
	Tier_TIER_FI_SALARY_LITE Tier = 5
	// AA Salary tier
	// Deprecated: Use tiers with explicit bands
	//
	// Deprecated: Marked as deprecated in api/tiering/external/external.proto.
	Tier_TIER_FI_AA_SALARY Tier = 6
	// AA Salary tier band 1
	Tier_TIER_FI_AA_SALARY_BAND_1 Tier = 7
	// AA Salary tier band 2
	Tier_TIER_FI_AA_SALARY_BAND_2 Tier = 8
	// AA Salary tier band 3
	Tier_TIER_FI_AA_SALARY_BAND_3 Tier = 9
	// Regular tier
	Tier_TIER_FI_REGULAR Tier = 10
	// Salary Basic tier
	Tier_TIER_FI_SALARY_BASIC Tier = 11
)

// Enum value maps for Tier.
var (
	Tier_name = map[int32]string{
		0:  "TIER_UNSPECIFIED",
		1:  "TIER_FI_BASIC",
		2:  "TIER_FI_PLUS",
		3:  "TIER_FI_INFINITE",
		4:  "TIER_FI_SALARY",
		5:  "TIER_FI_SALARY_LITE",
		6:  "TIER_FI_AA_SALARY",
		7:  "TIER_FI_AA_SALARY_BAND_1",
		8:  "TIER_FI_AA_SALARY_BAND_2",
		9:  "TIER_FI_AA_SALARY_BAND_3",
		10: "TIER_FI_REGULAR",
		11: "TIER_FI_SALARY_BASIC",
	}
	Tier_value = map[string]int32{
		"TIER_UNSPECIFIED":         0,
		"TIER_FI_BASIC":            1,
		"TIER_FI_PLUS":             2,
		"TIER_FI_INFINITE":         3,
		"TIER_FI_SALARY":           4,
		"TIER_FI_SALARY_LITE":      5,
		"TIER_FI_AA_SALARY":        6,
		"TIER_FI_AA_SALARY_BAND_1": 7,
		"TIER_FI_AA_SALARY_BAND_2": 8,
		"TIER_FI_AA_SALARY_BAND_3": 9,
		"TIER_FI_REGULAR":          10,
		"TIER_FI_SALARY_BASIC":     11,
	}
)

func (x Tier) Enum() *Tier {
	p := new(Tier)
	*p = x
	return p
}

func (x Tier) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Tier) Descriptor() protoreflect.EnumDescriptor {
	return file_api_tiering_external_external_proto_enumTypes[0].Descriptor()
}

func (Tier) Type() protoreflect.EnumType {
	return &file_api_tiering_external_external_proto_enumTypes[0]
}

func (x Tier) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Tier.Descriptor instead.
func (Tier) EnumDescriptor() ([]byte, []int) {
	return file_api_tiering_external_external_proto_rawDescGZIP(), []int{0}
}

type TierMovementType int32

const (
	TierMovementType_TIER_MOVEMENT_TYPE_UNSPECIFIED TierMovementType = 0
	TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE     TierMovementType = 1
	TierMovementType_TIER_MOVEMENT_TYPE_DOWNGRADE   TierMovementType = 2
)

// Enum value maps for TierMovementType.
var (
	TierMovementType_name = map[int32]string{
		0: "TIER_MOVEMENT_TYPE_UNSPECIFIED",
		1: "TIER_MOVEMENT_TYPE_UPGRADE",
		2: "TIER_MOVEMENT_TYPE_DOWNGRADE",
	}
	TierMovementType_value = map[string]int32{
		"TIER_MOVEMENT_TYPE_UNSPECIFIED": 0,
		"TIER_MOVEMENT_TYPE_UPGRADE":     1,
		"TIER_MOVEMENT_TYPE_DOWNGRADE":   2,
	}
)

func (x TierMovementType) Enum() *TierMovementType {
	p := new(TierMovementType)
	*p = x
	return p
}

func (x TierMovementType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TierMovementType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_tiering_external_external_proto_enumTypes[1].Descriptor()
}

func (TierMovementType) Type() protoreflect.EnumType {
	return &file_api_tiering_external_external_proto_enumTypes[1]
}

func (x TierMovementType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TierMovementType.Descriptor instead.
func (TierMovementType) EnumDescriptor() ([]byte, []int) {
	return file_api_tiering_external_external_proto_rawDescGZIP(), []int{1}
}

type TierInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tier of the user
	Tier Tier `protobuf:"varint,1,opt,name=tier,proto3,enum=tiering.external.Tier" json:"tier,omitempty"`
	// Timestamp at which this user moved to this tier
	LastUpdatedAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=last_updated_at,json=lastUpdatedAt,proto3" json:"last_updated_at,omitempty"`
}

func (x *TierInfo) Reset() {
	*x = TierInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_external_external_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TierInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TierInfo) ProtoMessage() {}

func (x *TierInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_external_external_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TierInfo.ProtoReflect.Descriptor instead.
func (*TierInfo) Descriptor() ([]byte, []int) {
	return file_api_tiering_external_external_proto_rawDescGZIP(), []int{0}
}

func (x *TierInfo) GetTier() Tier {
	if x != nil {
		return x.Tier
	}
	return Tier_TIER_UNSPECIFIED
}

func (x *TierInfo) GetLastUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdatedAt
	}
	return nil
}

// External tier representation
type TierExternalCriteria struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Enum to represent tier name
	TierName Tier `protobuf:"varint,1,opt,name=tier_name,json=tierName,proto3,enum=tiering.external.Tier" json:"tier_name,omitempty"`
	// list of qualifying criteria for a tier to satisfy
	QualifyingCriteriaList []*criteria.QualifyingCriteria `protobuf:"bytes,2,rep,name=qualifying_criteria_list,json=qualifyingCriteriaList,proto3" json:"qualifying_criteria_list,omitempty"`
}

func (x *TierExternalCriteria) Reset() {
	*x = TierExternalCriteria{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_external_external_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TierExternalCriteria) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TierExternalCriteria) ProtoMessage() {}

func (x *TierExternalCriteria) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_external_external_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TierExternalCriteria.ProtoReflect.Descriptor instead.
func (*TierExternalCriteria) Descriptor() ([]byte, []int) {
	return file_api_tiering_external_external_proto_rawDescGZIP(), []int{1}
}

func (x *TierExternalCriteria) GetTierName() Tier {
	if x != nil {
		return x.TierName
	}
	return Tier_TIER_UNSPECIFIED
}

func (x *TierExternalCriteria) GetQualifyingCriteriaList() []*criteria.QualifyingCriteria {
	if x != nil {
		return x.QualifyingCriteriaList
	}
	return nil
}

type MovementExternalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Tier to which movement is being considered
	// In case this is same as current tier, it contains retention details
	TierName Tier `protobuf:"varint,1,opt,name=tier_name,json=tierName,proto3,enum=tiering.external.Tier" json:"tier_name,omitempty"`
	// Specifies whether movement is allowed to the above tier or not
	// If lower tier is_movement_allowed is always false
	// For same tier if is_movement_allowed is false then that implies user is in grace period
	// For higher tier if is_movement_allowed is false then that implies user is in cool off period
	IsMovementAllowed bool `protobuf:"varint,2,opt,name=is_movement_allowed,json=isMovementAllowed,proto3" json:"is_movement_allowed,omitempty"`
	// Time at which movement will be possible in case it is not allowed now
	MovementTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=movement_timestamp,json=movementTimestamp,proto3" json:"movement_timestamp,omitempty"`
	// Set of options - on meeting even one of which, movement(/retention) will occur
	Options []*criteria.Option `protobuf:"bytes,4,rep,name=options,proto3" json:"options,omitempty"`
	// grace params is populated only for current tier movement when is_movement_allowed is false
	GraceParams *GraceParams `protobuf:"bytes,6,opt,name=grace_params,json=graceParams,proto3" json:"grace_params,omitempty"`
}

func (x *MovementExternalDetails) Reset() {
	*x = MovementExternalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_external_external_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MovementExternalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MovementExternalDetails) ProtoMessage() {}

func (x *MovementExternalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_external_external_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MovementExternalDetails.ProtoReflect.Descriptor instead.
func (*MovementExternalDetails) Descriptor() ([]byte, []int) {
	return file_api_tiering_external_external_proto_rawDescGZIP(), []int{2}
}

func (x *MovementExternalDetails) GetTierName() Tier {
	if x != nil {
		return x.TierName
	}
	return Tier_TIER_UNSPECIFIED
}

func (x *MovementExternalDetails) GetIsMovementAllowed() bool {
	if x != nil {
		return x.IsMovementAllowed
	}
	return false
}

func (x *MovementExternalDetails) GetMovementTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.MovementTimestamp
	}
	return nil
}

func (x *MovementExternalDetails) GetOptions() []*criteria.Option {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *MovementExternalDetails) GetGraceParams() *GraceParams {
	if x != nil {
		return x.GraceParams
	}
	return nil
}

type LatestMovementDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Tier from which movement happened
	FromTier Tier `protobuf:"varint,1,opt,name=from_tier,json=fromTier,proto3,enum=tiering.external.Tier" json:"from_tier,omitempty"`
	// Tier to which movement happened
	ToTier Tier `protobuf:"varint,2,opt,name=to_tier,json=toTier,proto3,enum=tiering.external.Tier" json:"to_tier,omitempty"`
	// Timestamp at which movement happened
	MovementTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=movement_timestamp,json=movementTimestamp,proto3" json:"movement_timestamp,omitempty"`
	// Entry and Current criteria option type
	EntryCriteriaOptionType  enums.CriteriaOptionType `protobuf:"varint,4,opt,name=entry_criteria_option_type,json=entryCriteriaOptionType,proto3,enum=tiering.enums.CriteriaOptionType" json:"entry_criteria_option_type,omitempty"`
	LatestCriteriaOptionType enums.CriteriaOptionType `protobuf:"varint,5,opt,name=latest_criteria_option_type,json=latestCriteriaOptionType,proto3,enum=tiering.enums.CriteriaOptionType" json:"latest_criteria_option_type,omitempty"`
}

func (x *LatestMovementDetails) Reset() {
	*x = LatestMovementDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_external_external_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LatestMovementDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LatestMovementDetails) ProtoMessage() {}

func (x *LatestMovementDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_external_external_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LatestMovementDetails.ProtoReflect.Descriptor instead.
func (*LatestMovementDetails) Descriptor() ([]byte, []int) {
	return file_api_tiering_external_external_proto_rawDescGZIP(), []int{3}
}

func (x *LatestMovementDetails) GetFromTier() Tier {
	if x != nil {
		return x.FromTier
	}
	return Tier_TIER_UNSPECIFIED
}

func (x *LatestMovementDetails) GetToTier() Tier {
	if x != nil {
		return x.ToTier
	}
	return Tier_TIER_UNSPECIFIED
}

func (x *LatestMovementDetails) GetMovementTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.MovementTimestamp
	}
	return nil
}

func (x *LatestMovementDetails) GetEntryCriteriaOptionType() enums.CriteriaOptionType {
	if x != nil {
		return x.EntryCriteriaOptionType
	}
	return enums.CriteriaOptionType(0)
}

func (x *LatestMovementDetails) GetLatestCriteriaOptionType() enums.CriteriaOptionType {
	if x != nil {
		return x.LatestCriteriaOptionType
	}
	return enums.CriteriaOptionType(0)
}

type TierMovementHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Primary id of the TMH entry
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// fromTier of the movement in history
	FromTier Tier `protobuf:"varint,2,opt,name=from_tier,json=fromTier,proto3,enum=tiering.external.Tier" json:"from_tier,omitempty"`
	// toTier of the movement in history
	ToTier Tier `protobuf:"varint,3,opt,name=to_tier,json=toTier,proto3,enum=tiering.external.Tier" json:"to_tier,omitempty"`
	// Timestamp at which movement created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// String variant of tiering.enums.TierMovementType
	MovementType string `protobuf:"bytes,5,opt,name=movement_type,json=movementType,proto3" json:"movement_type,omitempty"`
}

func (x *TierMovementHistory) Reset() {
	*x = TierMovementHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_external_external_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TierMovementHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TierMovementHistory) ProtoMessage() {}

func (x *TierMovementHistory) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_external_external_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TierMovementHistory.ProtoReflect.Descriptor instead.
func (*TierMovementHistory) Descriptor() ([]byte, []int) {
	return file_api_tiering_external_external_proto_rawDescGZIP(), []int{4}
}

func (x *TierMovementHistory) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TierMovementHistory) GetFromTier() Tier {
	if x != nil {
		return x.FromTier
	}
	return Tier_TIER_UNSPECIFIED
}

func (x *TierMovementHistory) GetToTier() Tier {
	if x != nil {
		return x.ToTier
	}
	return Tier_TIER_UNSPECIFIED
}

func (x *TierMovementHistory) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *TierMovementHistory) GetMovementType() string {
	if x != nil {
		return x.MovementType
	}
	return ""
}

type GraceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Period float64 `protobuf:"fixed64,1,opt,name=period,proto3" json:"period,omitempty"`
}

func (x *GraceParams) Reset() {
	*x = GraceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_external_external_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GraceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GraceParams) ProtoMessage() {}

func (x *GraceParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_external_external_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GraceParams.ProtoReflect.Descriptor instead.
func (*GraceParams) Descriptor() ([]byte, []int) {
	return file_api_tiering_external_external_proto_rawDescGZIP(), []int{5}
}

func (x *GraceParams) GetPeriod() float64 {
	if x != nil {
		return x.Period
	}
	return 0
}

type TierMovementCriterias struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// criterias user is satisfying to be on current tier
	CurrentCriteriaOptionTypes []enums.CriteriaOptionType `protobuf:"varint,1,rep,packed,name=current_criteria_option_types,json=currentCriteriaOptionTypes,proto3,enum=tiering.enums.CriteriaOptionType" json:"current_criteria_option_types,omitempty"`
	// criteria through which user entered current tier
	EntryCriteriaOptionType enums.CriteriaOptionType `protobuf:"varint,2,opt,name=entry_criteria_option_type,json=entryCriteriaOptionType,proto3,enum=tiering.enums.CriteriaOptionType" json:"entry_criteria_option_type,omitempty"`
}

func (x *TierMovementCriterias) Reset() {
	*x = TierMovementCriterias{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_external_external_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TierMovementCriterias) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TierMovementCriterias) ProtoMessage() {}

func (x *TierMovementCriterias) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_external_external_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TierMovementCriterias.ProtoReflect.Descriptor instead.
func (*TierMovementCriterias) Descriptor() ([]byte, []int) {
	return file_api_tiering_external_external_proto_rawDescGZIP(), []int{6}
}

func (x *TierMovementCriterias) GetCurrentCriteriaOptionTypes() []enums.CriteriaOptionType {
	if x != nil {
		return x.CurrentCriteriaOptionTypes
	}
	return nil
}

func (x *TierMovementCriterias) GetEntryCriteriaOptionType() enums.CriteriaOptionType {
	if x != nil {
		return x.EntryCriteriaOptionType
	}
	return enums.CriteriaOptionType(0)
}

type TrialDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tier for which user opted for Trial period
	Tier Tier `protobuf:"varint,1,opt,name=tier,proto3,enum=tiering.external.Tier" json:"tier,omitempty"`
	// timestamp at which user opted for trial
	OptedAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=opted_at,json=optedAt,proto3" json:"opted_at,omitempty"`
	// Timestamp at which trial starts
	TrialStartTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=trial_start_time,json=trialStartTime,proto3" json:"trial_start_time,omitempty"`
	// Timestamp at which trial ends
	TrialEndTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=trial_end_time,json=trialEndTime,proto3" json:"trial_end_time,omitempty"`
}

func (x *TrialDetails) Reset() {
	*x = TrialDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_external_external_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrialDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrialDetails) ProtoMessage() {}

func (x *TrialDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_external_external_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrialDetails.ProtoReflect.Descriptor instead.
func (*TrialDetails) Descriptor() ([]byte, []int) {
	return file_api_tiering_external_external_proto_rawDescGZIP(), []int{7}
}

func (x *TrialDetails) GetTier() Tier {
	if x != nil {
		return x.Tier
	}
	return Tier_TIER_UNSPECIFIED
}

func (x *TrialDetails) GetOptedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.OptedAt
	}
	return nil
}

func (x *TrialDetails) GetTrialStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.TrialStartTime
	}
	return nil
}

func (x *TrialDetails) GetTrialEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.TrialEndTime
	}
	return nil
}

var File_api_tiering_external_external_proto protoreflect.FileDescriptor

var file_api_tiering_external_external_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x2f, 0x63, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7a, 0x0a, 0x08,
	0x54, 0x69, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x69, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x04,
	0x74, 0x69, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xab, 0x01, 0x0a, 0x14, 0x54, 0x69, 0x65,
	0x72, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x12, 0x33, 0x0a, 0x09, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x08, 0x74, 0x69,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x5e, 0x0a, 0x18, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66,
	0x79, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x2e, 0x51, 0x75, 0x61, 0x6c,
	0x69, 0x66, 0x79, 0x69, 0x6e, 0x67, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x52, 0x16,
	0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x79, 0x69, 0x6e, 0x67, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xbf, 0x02, 0x0a, 0x17, 0x4d, 0x6f, 0x76, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x08, 0x74,
	0x69, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x6d, 0x6f,
	0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x73, 0x4d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x49, 0x0a, 0x12, 0x6d, 0x6f, 0x76, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x11, 0x6d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x32, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x40, 0x0a, 0x0c, 0x67, 0x72, 0x61, 0x63, 0x65, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e,
	0x47, 0x72, 0x61, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0b, 0x67, 0x72, 0x61,
	0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x8a, 0x03, 0x0a, 0x15, 0x4c, 0x61, 0x74,
	0x65, 0x73, 0x74, 0x4d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x08, 0x66,
	0x72, 0x6f, 0x6d, 0x54, 0x69, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x07, 0x74, 0x6f, 0x5f, 0x74, 0x69,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72,
	0x52, 0x06, 0x74, 0x6f, 0x54, 0x69, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x12, 0x6d, 0x6f, 0x76, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x11, 0x6d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x12, 0x5e, 0x0a, 0x1a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x72, 0x69,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x17, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x60, 0x0a, 0x1b, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x18, 0x6c, 0x61, 0x74,
	0x65, 0x73, 0x74, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0xeb, 0x01, 0x0a, 0x13, 0x54, 0x69, 0x65, 0x72, 0x4d, 0x6f,
	0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x33, 0x0a,
	0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69,
	0x65, 0x72, 0x12, 0x2f, 0x0a, 0x07, 0x74, 0x6f, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x06, 0x74, 0x6f, 0x54,
	0x69, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x23,
	0x0a, 0x0d, 0x6d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x25, 0x0a, 0x0b, 0x47, 0x72, 0x61, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x06, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0xdd, 0x01, 0x0a, 0x15, 0x54,
	0x69, 0x65, 0x72, 0x4d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x73, 0x12, 0x64, 0x0a, 0x1d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x69,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x72, 0x69, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x1a,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x5e, 0x0a, 0x1a, 0x65, 0x6e,
	0x74, 0x72, 0x79, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21,
	0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43,
	0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x17, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0xf9, 0x01, 0x0a, 0x0c, 0x54,
	0x72, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x04, 0x74,
	0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65,
	0x72, 0x52, 0x04, 0x74, 0x69, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x44,
	0x0a, 0x10, 0x74, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x74, 0x72, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0e, 0x74, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x74, 0x72, 0x69, 0x61, 0x6c, 0x45,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x2a, 0xa8, 0x02, 0x0a, 0x04, 0x54, 0x69, 0x65, 0x72, 0x12,
	0x14, 0x0a, 0x10, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49,
	0x5f, 0x42, 0x41, 0x53, 0x49, 0x43, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x49, 0x45, 0x52,
	0x5f, 0x46, 0x49, 0x5f, 0x50, 0x4c, 0x55, 0x53, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x49,
	0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x49, 0x4e, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x10, 0x03,
	0x12, 0x12, 0x0a, 0x0e, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x53, 0x41, 0x4c, 0x41,
	0x52, 0x59, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f,
	0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x10, 0x05, 0x12, 0x19, 0x0a,
	0x11, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41,
	0x52, 0x59, 0x10, 0x06, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x49, 0x45, 0x52,
	0x5f, 0x46, 0x49, 0x5f, 0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x41,
	0x4e, 0x44, 0x5f, 0x31, 0x10, 0x07, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46,
	0x49, 0x5f, 0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x41, 0x4e, 0x44,
	0x5f, 0x32, 0x10, 0x08, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f,
	0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x41, 0x4e, 0x44, 0x5f, 0x33,
	0x10, 0x09, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x52, 0x45,
	0x47, 0x55, 0x4c, 0x41, 0x52, 0x10, 0x0a, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x49, 0x45, 0x52, 0x5f,
	0x46, 0x49, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x41, 0x53, 0x49, 0x43, 0x10,
	0x0b, 0x2a, 0x78, 0x0a, 0x10, 0x54, 0x69, 0x65, 0x72, 0x4d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x4d, 0x4f,
	0x56, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x49, 0x45,
	0x52, 0x5f, 0x4d, 0x4f, 0x56, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x54, 0x49, 0x45,
	0x52, 0x5f, 0x4d, 0x4f, 0x56, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x44, 0x4f, 0x57, 0x4e, 0x47, 0x52, 0x41, 0x44, 0x45, 0x10, 0x02, 0x42, 0x5a, 0x0a, 0x2b, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_tiering_external_external_proto_rawDescOnce sync.Once
	file_api_tiering_external_external_proto_rawDescData = file_api_tiering_external_external_proto_rawDesc
)

func file_api_tiering_external_external_proto_rawDescGZIP() []byte {
	file_api_tiering_external_external_proto_rawDescOnce.Do(func() {
		file_api_tiering_external_external_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_tiering_external_external_proto_rawDescData)
	})
	return file_api_tiering_external_external_proto_rawDescData
}

var file_api_tiering_external_external_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_tiering_external_external_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_tiering_external_external_proto_goTypes = []interface{}{
	(Tier)(0),                           // 0: tiering.external.Tier
	(TierMovementType)(0),               // 1: tiering.external.TierMovementType
	(*TierInfo)(nil),                    // 2: tiering.external.TierInfo
	(*TierExternalCriteria)(nil),        // 3: tiering.external.TierExternalCriteria
	(*MovementExternalDetails)(nil),     // 4: tiering.external.MovementExternalDetails
	(*LatestMovementDetails)(nil),       // 5: tiering.external.LatestMovementDetails
	(*TierMovementHistory)(nil),         // 6: tiering.external.TierMovementHistory
	(*GraceParams)(nil),                 // 7: tiering.external.GraceParams
	(*TierMovementCriterias)(nil),       // 8: tiering.external.TierMovementCriterias
	(*TrialDetails)(nil),                // 9: tiering.external.TrialDetails
	(*timestamppb.Timestamp)(nil),       // 10: google.protobuf.Timestamp
	(*criteria.QualifyingCriteria)(nil), // 11: tiering.criteria.QualifyingCriteria
	(*criteria.Option)(nil),             // 12: tiering.criteria.Option
	(enums.CriteriaOptionType)(0),       // 13: tiering.enums.CriteriaOptionType
}
var file_api_tiering_external_external_proto_depIdxs = []int32{
	0,  // 0: tiering.external.TierInfo.tier:type_name -> tiering.external.Tier
	10, // 1: tiering.external.TierInfo.last_updated_at:type_name -> google.protobuf.Timestamp
	0,  // 2: tiering.external.TierExternalCriteria.tier_name:type_name -> tiering.external.Tier
	11, // 3: tiering.external.TierExternalCriteria.qualifying_criteria_list:type_name -> tiering.criteria.QualifyingCriteria
	0,  // 4: tiering.external.MovementExternalDetails.tier_name:type_name -> tiering.external.Tier
	10, // 5: tiering.external.MovementExternalDetails.movement_timestamp:type_name -> google.protobuf.Timestamp
	12, // 6: tiering.external.MovementExternalDetails.options:type_name -> tiering.criteria.Option
	7,  // 7: tiering.external.MovementExternalDetails.grace_params:type_name -> tiering.external.GraceParams
	0,  // 8: tiering.external.LatestMovementDetails.from_tier:type_name -> tiering.external.Tier
	0,  // 9: tiering.external.LatestMovementDetails.to_tier:type_name -> tiering.external.Tier
	10, // 10: tiering.external.LatestMovementDetails.movement_timestamp:type_name -> google.protobuf.Timestamp
	13, // 11: tiering.external.LatestMovementDetails.entry_criteria_option_type:type_name -> tiering.enums.CriteriaOptionType
	13, // 12: tiering.external.LatestMovementDetails.latest_criteria_option_type:type_name -> tiering.enums.CriteriaOptionType
	0,  // 13: tiering.external.TierMovementHistory.from_tier:type_name -> tiering.external.Tier
	0,  // 14: tiering.external.TierMovementHistory.to_tier:type_name -> tiering.external.Tier
	10, // 15: tiering.external.TierMovementHistory.created_at:type_name -> google.protobuf.Timestamp
	13, // 16: tiering.external.TierMovementCriterias.current_criteria_option_types:type_name -> tiering.enums.CriteriaOptionType
	13, // 17: tiering.external.TierMovementCriterias.entry_criteria_option_type:type_name -> tiering.enums.CriteriaOptionType
	0,  // 18: tiering.external.TrialDetails.tier:type_name -> tiering.external.Tier
	10, // 19: tiering.external.TrialDetails.opted_at:type_name -> google.protobuf.Timestamp
	10, // 20: tiering.external.TrialDetails.trial_start_time:type_name -> google.protobuf.Timestamp
	10, // 21: tiering.external.TrialDetails.trial_end_time:type_name -> google.protobuf.Timestamp
	22, // [22:22] is the sub-list for method output_type
	22, // [22:22] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_api_tiering_external_external_proto_init() }
func file_api_tiering_external_external_proto_init() {
	if File_api_tiering_external_external_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_tiering_external_external_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TierInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_external_external_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TierExternalCriteria); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_external_external_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MovementExternalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_external_external_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LatestMovementDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_external_external_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TierMovementHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_external_external_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GraceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_external_external_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TierMovementCriterias); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_external_external_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrialDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_tiering_external_external_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_tiering_external_external_proto_goTypes,
		DependencyIndexes: file_api_tiering_external_external_proto_depIdxs,
		EnumInfos:         file_api_tiering_external_external_proto_enumTypes,
		MessageInfos:      file_api_tiering_external_external_proto_msgTypes,
	}.Build()
	File_api_tiering_external_external_proto = out.File
	file_api_tiering_external_external_proto_rawDesc = nil
	file_api_tiering_external_external_proto_goTypes = nil
	file_api_tiering_external_external_proto_depIdxs = nil
}
