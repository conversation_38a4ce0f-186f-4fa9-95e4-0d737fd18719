// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/merchantresolution/service.proto

package merchantresolution

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MerchantResolutionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MerchantResolutionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MerchantResolutionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MerchantResolutionRequestMultiError, or nil if none found.
func (m *MerchantResolutionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MerchantResolutionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MerchantResolutionRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MerchantResolutionRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MerchantResolutionRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawMerchantName

	// no validation rules for IsFiTxn

	// no validation rules for IsMerchantTxn

	if len(errors) > 0 {
		return MerchantResolutionRequestMultiError(errors)
	}

	return nil
}

// MerchantResolutionRequestMultiError is an error wrapping multiple validation
// errors returned by MerchantResolutionRequest.ValidateAll() if the
// designated constraints aren't met.
type MerchantResolutionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MerchantResolutionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MerchantResolutionRequestMultiError) AllErrors() []error { return m }

// MerchantResolutionRequestValidationError is the validation error returned by
// MerchantResolutionRequest.Validate if the designated constraints aren't met.
type MerchantResolutionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MerchantResolutionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MerchantResolutionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MerchantResolutionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MerchantResolutionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MerchantResolutionRequestValidationError) ErrorName() string {
	return "MerchantResolutionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MerchantResolutionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMerchantResolutionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MerchantResolutionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MerchantResolutionRequestValidationError{}

// Validate checks the field values on MerchantResolutionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MerchantResolutionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MerchantResolutionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MerchantResolutionResponseMultiError, or nil if none found.
func (m *MerchantResolutionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *MerchantResolutionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MerchantResolutionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MerchantResolutionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MerchantResolutionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DsMerchantId

	// no validation rules for MerchantName

	if len(errors) > 0 {
		return MerchantResolutionResponseMultiError(errors)
	}

	return nil
}

// MerchantResolutionResponseMultiError is an error wrapping multiple
// validation errors returned by MerchantResolutionResponse.ValidateAll() if
// the designated constraints aren't met.
type MerchantResolutionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MerchantResolutionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MerchantResolutionResponseMultiError) AllErrors() []error { return m }

// MerchantResolutionResponseValidationError is the validation error returned
// by MerchantResolutionResponse.Validate if the designated constraints aren't met.
type MerchantResolutionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MerchantResolutionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MerchantResolutionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MerchantResolutionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MerchantResolutionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MerchantResolutionResponseValidationError) ErrorName() string {
	return "MerchantResolutionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e MerchantResolutionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMerchantResolutionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MerchantResolutionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MerchantResolutionResponseValidationError{}
