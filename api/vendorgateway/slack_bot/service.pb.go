// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/slack_bot/service.proto

package slack_bot

import (
	rpc "github.com/epifi/be-common/api/rpc"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	types "github.com/epifi/gamma/api/vendorgateway/slack_bot/types"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListenSlackEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *ListenSlackEventRequest) Reset() {
	*x = ListenSlackEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListenSlackEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListenSlackEventRequest) ProtoMessage() {}

func (x *ListenSlackEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListenSlackEventRequest.ProtoReflect.Descriptor instead.
func (*ListenSlackEventRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListenSlackEventRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

type ListenSlackEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Event  *types.Event `protobuf:"bytes,2,opt,name=event,proto3" json:"event,omitempty"`
}

func (x *ListenSlackEventResponse) Reset() {
	*x = ListenSlackEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListenSlackEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListenSlackEventResponse) ProtoMessage() {}

func (x *ListenSlackEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListenSlackEventResponse.ProtoReflect.Descriptor instead.
func (*ListenSlackEventResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListenSlackEventResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ListenSlackEventResponse) GetEvent() *types.Event {
	if x != nil {
		return x.Event
	}
	return nil
}

type PostSlackMsgRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Msg:
	//
	//	*PostSlackMsgRequest_TextMsg
	//	*PostSlackMsgRequest_Attachment
	Msg  isPostSlackMsgRequest_Msg `protobuf_oneof:"msg"`
	User *types.User               `protobuf:"bytes,4,opt,name=user,proto3" json:"user,omitempty"`
	// Slack channel id
	Channel string `protobuf:"bytes,5,opt,name=channel,proto3" json:"channel,omitempty"`
}

func (x *PostSlackMsgRequest) Reset() {
	*x = PostSlackMsgRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostSlackMsgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostSlackMsgRequest) ProtoMessage() {}

func (x *PostSlackMsgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostSlackMsgRequest.ProtoReflect.Descriptor instead.
func (*PostSlackMsgRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{2}
}

func (x *PostSlackMsgRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *PostSlackMsgRequest) GetMsg() isPostSlackMsgRequest_Msg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (x *PostSlackMsgRequest) GetTextMsg() string {
	if x, ok := x.GetMsg().(*PostSlackMsgRequest_TextMsg); ok {
		return x.TextMsg
	}
	return ""
}

func (x *PostSlackMsgRequest) GetAttachment() *types.Attachment {
	if x, ok := x.GetMsg().(*PostSlackMsgRequest_Attachment); ok {
		return x.Attachment
	}
	return nil
}

func (x *PostSlackMsgRequest) GetUser() *types.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *PostSlackMsgRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

type isPostSlackMsgRequest_Msg interface {
	isPostSlackMsgRequest_Msg()
}

type PostSlackMsgRequest_TextMsg struct {
	TextMsg string `protobuf:"bytes,2,opt,name=text_msg,json=textMsg,proto3,oneof"`
}

type PostSlackMsgRequest_Attachment struct {
	Attachment *types.Attachment `protobuf:"bytes,3,opt,name=attachment,proto3,oneof"`
}

func (*PostSlackMsgRequest_TextMsg) isPostSlackMsgRequest_Msg() {}

func (*PostSlackMsgRequest_Attachment) isPostSlackMsgRequest_Msg() {}

type PostSlackMsgResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Ts     string      `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *PostSlackMsgResponse) Reset() {
	*x = PostSlackMsgResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostSlackMsgResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostSlackMsgResponse) ProtoMessage() {}

func (x *PostSlackMsgResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostSlackMsgResponse.ProtoReflect.Descriptor instead.
func (*PostSlackMsgResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{3}
}

func (x *PostSlackMsgResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *PostSlackMsgResponse) GetTs() string {
	if x != nil {
		return x.Ts
	}
	return ""
}

type PostSlackMsgInThreadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Msg to be posted in thread
	//
	// Types that are assignable to Msg:
	//
	//	*PostSlackMsgInThreadRequest_TextMsg
	//	*PostSlackMsgInThreadRequest_Attachment
	Msg  isPostSlackMsgInThreadRequest_Msg `protobuf_oneof:"msg"`
	User *types.User                       `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	// Ts of the parent msg
	Ts      string `protobuf:"bytes,4,opt,name=ts,proto3" json:"ts,omitempty"`
	Channel string `protobuf:"bytes,5,opt,name=channel,proto3" json:"channel,omitempty"`
}

func (x *PostSlackMsgInThreadRequest) Reset() {
	*x = PostSlackMsgInThreadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostSlackMsgInThreadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostSlackMsgInThreadRequest) ProtoMessage() {}

func (x *PostSlackMsgInThreadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostSlackMsgInThreadRequest.ProtoReflect.Descriptor instead.
func (*PostSlackMsgInThreadRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{4}
}

func (x *PostSlackMsgInThreadRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *PostSlackMsgInThreadRequest) GetMsg() isPostSlackMsgInThreadRequest_Msg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (x *PostSlackMsgInThreadRequest) GetTextMsg() string {
	if x, ok := x.GetMsg().(*PostSlackMsgInThreadRequest_TextMsg); ok {
		return x.TextMsg
	}
	return ""
}

func (x *PostSlackMsgInThreadRequest) GetAttachment() *types.Attachment {
	if x, ok := x.GetMsg().(*PostSlackMsgInThreadRequest_Attachment); ok {
		return x.Attachment
	}
	return nil
}

func (x *PostSlackMsgInThreadRequest) GetUser() *types.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *PostSlackMsgInThreadRequest) GetTs() string {
	if x != nil {
		return x.Ts
	}
	return ""
}

func (x *PostSlackMsgInThreadRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

type isPostSlackMsgInThreadRequest_Msg interface {
	isPostSlackMsgInThreadRequest_Msg()
}

type PostSlackMsgInThreadRequest_TextMsg struct {
	TextMsg string `protobuf:"bytes,2,opt,name=text_msg,json=textMsg,proto3,oneof"`
}

type PostSlackMsgInThreadRequest_Attachment struct {
	Attachment *types.Attachment `protobuf:"bytes,6,opt,name=attachment,proto3,oneof"`
}

func (*PostSlackMsgInThreadRequest_TextMsg) isPostSlackMsgInThreadRequest_Msg() {}

func (*PostSlackMsgInThreadRequest_Attachment) isPostSlackMsgInThreadRequest_Msg() {}

type PostSlackMsgInThreadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *PostSlackMsgInThreadResponse) Reset() {
	*x = PostSlackMsgInThreadResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostSlackMsgInThreadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostSlackMsgInThreadResponse) ProtoMessage() {}

func (x *PostSlackMsgInThreadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostSlackMsgInThreadResponse.ProtoReflect.Descriptor instead.
func (*PostSlackMsgInThreadResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{5}
}

func (x *PostSlackMsgInThreadResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type OpenViewModalRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Trigger-id of the request event to open view-modal
	TriggerId string           `protobuf:"bytes,2,opt,name=trigger_id,json=triggerId,proto3" json:"trigger_id,omitempty"`
	ViewModal *types.ViewModal `protobuf:"bytes,3,opt,name=view_modal,json=viewModal,proto3" json:"view_modal,omitempty"`
}

func (x *OpenViewModalRequest) Reset() {
	*x = OpenViewModalRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenViewModalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenViewModalRequest) ProtoMessage() {}

func (x *OpenViewModalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenViewModalRequest.ProtoReflect.Descriptor instead.
func (*OpenViewModalRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{6}
}

func (x *OpenViewModalRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *OpenViewModalRequest) GetTriggerId() string {
	if x != nil {
		return x.TriggerId
	}
	return ""
}

func (x *OpenViewModalRequest) GetViewModal() *types.ViewModal {
	if x != nil {
		return x.ViewModal
	}
	return nil
}

type OpenViewModalResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ViewId string      `protobuf:"bytes,2,opt,name=view_id,json=viewId,proto3" json:"view_id,omitempty"`
}

func (x *OpenViewModalResponse) Reset() {
	*x = OpenViewModalResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenViewModalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenViewModalResponse) ProtoMessage() {}

func (x *OpenViewModalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenViewModalResponse.ProtoReflect.Descriptor instead.
func (*OpenViewModalResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{7}
}

func (x *OpenViewModalResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *OpenViewModalResponse) GetViewId() string {
	if x != nil {
		return x.ViewId
	}
	return ""
}

type GetSlackMsgWithTsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header  *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Channel string                       `protobuf:"bytes,2,opt,name=channel,proto3" json:"channel,omitempty"`
	// filter slack msg search with Ts
	Ts string `protobuf:"bytes,3,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetSlackMsgWithTsRequest) Reset() {
	*x = GetSlackMsgWithTsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSlackMsgWithTsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSlackMsgWithTsRequest) ProtoMessage() {}

func (x *GetSlackMsgWithTsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSlackMsgWithTsRequest.ProtoReflect.Descriptor instead.
func (*GetSlackMsgWithTsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetSlackMsgWithTsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetSlackMsgWithTsRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *GetSlackMsgWithTsRequest) GetTs() string {
	if x != nil {
		return x.Ts
	}
	return ""
}

type GetSlackMsgWithTsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Fields  []*types.AttachmentField `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
	TextMsg string                   `protobuf:"bytes,3,opt,name=text_msg,json=textMsg,proto3" json:"text_msg,omitempty"`
	UserId  string                   `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *GetSlackMsgWithTsResponse) Reset() {
	*x = GetSlackMsgWithTsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSlackMsgWithTsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSlackMsgWithTsResponse) ProtoMessage() {}

func (x *GetSlackMsgWithTsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSlackMsgWithTsResponse.ProtoReflect.Descriptor instead.
func (*GetSlackMsgWithTsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetSlackMsgWithTsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSlackMsgWithTsResponse) GetFields() []*types.AttachmentField {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *GetSlackMsgWithTsResponse) GetTextMsg() string {
	if x != nil {
		return x.TextMsg
	}
	return ""
}

func (x *GetSlackMsgWithTsResponse) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type UpdateSlackMsgRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header     *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Channel    string                       `protobuf:"bytes,2,opt,name=channel,proto3" json:"channel,omitempty"`
	Ts         string                       `protobuf:"bytes,3,opt,name=ts,proto3" json:"ts,omitempty"`
	Attachment *types.Attachment            `protobuf:"bytes,4,opt,name=attachment,proto3" json:"attachment,omitempty"`
}

func (x *UpdateSlackMsgRequest) Reset() {
	*x = UpdateSlackMsgRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSlackMsgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSlackMsgRequest) ProtoMessage() {}

func (x *UpdateSlackMsgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSlackMsgRequest.ProtoReflect.Descriptor instead.
func (*UpdateSlackMsgRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateSlackMsgRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *UpdateSlackMsgRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *UpdateSlackMsgRequest) GetTs() string {
	if x != nil {
		return x.Ts
	}
	return ""
}

func (x *UpdateSlackMsgRequest) GetAttachment() *types.Attachment {
	if x != nil {
		return x.Attachment
	}
	return nil
}

type UpdateSlackMsgResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateSlackMsgResponse) Reset() {
	*x = UpdateSlackMsgResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSlackMsgResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSlackMsgResponse) ProtoMessage() {}

func (x *UpdateSlackMsgResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSlackMsgResponse.ProtoReflect.Descriptor instead.
func (*UpdateSlackMsgResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateSlackMsgResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetUserInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Member id or email of the slack user
	UserIdsOrEmails []string `protobuf:"bytes,2,rep,name=userIdsOrEmails,proto3" json:"userIdsOrEmails,omitempty"`
}

func (x *GetUserInfoRequest) Reset() {
	*x = GetUserInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoRequest) ProtoMessage() {}

func (x *GetUserInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoRequest.ProtoReflect.Descriptor instead.
func (*GetUserInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetUserInfoRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetUserInfoRequest) GetUserIdsOrEmails() []string {
	if x != nil {
		return x.UserIdsOrEmails
	}
	return nil
}

type GetUserInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Users  []*types.User `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *GetUserInfoResponse) Reset() {
	*x = GetUserInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoResponse) ProtoMessage() {}

func (x *GetUserInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoResponse.ProtoReflect.Descriptor instead.
func (*GetUserInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetUserInfoResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetUserInfoResponse) GetUsers() []*types.User {
	if x != nil {
		return x.Users
	}
	return nil
}

type GetMsgLinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header  *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Channel string                       `protobuf:"bytes,2,opt,name=channel,proto3" json:"channel,omitempty"`
	Ts      string                       `protobuf:"bytes,3,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetMsgLinkRequest) Reset() {
	*x = GetMsgLinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMsgLinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMsgLinkRequest) ProtoMessage() {}

func (x *GetMsgLinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMsgLinkRequest.ProtoReflect.Descriptor instead.
func (*GetMsgLinkRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetMsgLinkRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetMsgLinkRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *GetMsgLinkRequest) GetTs() string {
	if x != nil {
		return x.Ts
	}
	return ""
}

type GetMsgLinkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Url    string      `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *GetMsgLinkResponse) Reset() {
	*x = GetMsgLinkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMsgLinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMsgLinkResponse) ProtoMessage() {}

func (x *GetMsgLinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMsgLinkResponse.ProtoReflect.Descriptor instead.
func (*GetMsgLinkResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetMsgLinkResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetMsgLinkResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type UpdateViewModalRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header    *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ViewId    string                       `protobuf:"bytes,2,opt,name=view_id,json=viewId,proto3" json:"view_id,omitempty"`
	ViewModal *types.ViewModal             `protobuf:"bytes,3,opt,name=view_modal,json=viewModal,proto3" json:"view_modal,omitempty"`
}

func (x *UpdateViewModalRequest) Reset() {
	*x = UpdateViewModalRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateViewModalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateViewModalRequest) ProtoMessage() {}

func (x *UpdateViewModalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateViewModalRequest.ProtoReflect.Descriptor instead.
func (*UpdateViewModalRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateViewModalRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *UpdateViewModalRequest) GetViewId() string {
	if x != nil {
		return x.ViewId
	}
	return ""
}

func (x *UpdateViewModalRequest) GetViewModal() *types.ViewModal {
	if x != nil {
		return x.ViewModal
	}
	return nil
}

type UpdateViewModalResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateViewModalResponse) Reset() {
	*x = UpdateViewModalResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateViewModalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateViewModalResponse) ProtoMessage() {}

func (x *UpdateViewModalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateViewModalResponse.ProtoReflect.Descriptor instead.
func (*UpdateViewModalResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{17}
}

func (x *UpdateViewModalResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetUserGroupsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IncludeDisable bool `protobuf:"varint,1,opt,name=include_disable,json=includeDisable,proto3" json:"include_disable,omitempty"`
}

func (x *GetUserGroupsRequest) Reset() {
	*x = GetUserGroupsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserGroupsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserGroupsRequest) ProtoMessage() {}

func (x *GetUserGroupsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserGroupsRequest.ProtoReflect.Descriptor instead.
func (*GetUserGroupsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetUserGroupsRequest) GetIncludeDisable() bool {
	if x != nil {
		return x.IncludeDisable
	}
	return false
}

type GetUserGroupsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status    *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	UserGroup []*types.UserGroup `protobuf:"bytes,2,rep,name=user_group,json=userGroup,proto3" json:"user_group,omitempty"`
}

func (x *GetUserGroupsResponse) Reset() {
	*x = GetUserGroupsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserGroupsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserGroupsResponse) ProtoMessage() {}

func (x *GetUserGroupsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserGroupsResponse.ProtoReflect.Descriptor instead.
func (*GetUserGroupsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetUserGroupsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetUserGroupsResponse) GetUserGroup() []*types.UserGroup {
	if x != nil {
		return x.UserGroup
	}
	return nil
}

type GetThreadMsgWithTsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header  *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Channel string                       `protobuf:"bytes,2,opt,name=channel,proto3" json:"channel,omitempty"`
	// filter slack msg search with Ts
	Ts string `protobuf:"bytes,3,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetThreadMsgWithTsRequest) Reset() {
	*x = GetThreadMsgWithTsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetThreadMsgWithTsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetThreadMsgWithTsRequest) ProtoMessage() {}

func (x *GetThreadMsgWithTsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetThreadMsgWithTsRequest.ProtoReflect.Descriptor instead.
func (*GetThreadMsgWithTsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetThreadMsgWithTsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetThreadMsgWithTsRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *GetThreadMsgWithTsRequest) GetTs() string {
	if x != nil {
		return x.Ts
	}
	return ""
}

type GetThreadMsgWithTsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	TextMsg string      `protobuf:"bytes,2,opt,name=text_msg,json=textMsg,proto3" json:"text_msg,omitempty"`
	UserId  string      `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *GetThreadMsgWithTsResponse) Reset() {
	*x = GetThreadMsgWithTsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetThreadMsgWithTsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetThreadMsgWithTsResponse) ProtoMessage() {}

func (x *GetThreadMsgWithTsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetThreadMsgWithTsResponse.ProtoReflect.Descriptor instead.
func (*GetThreadMsgWithTsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetThreadMsgWithTsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetThreadMsgWithTsResponse) GetTextMsg() string {
	if x != nil {
		return x.TextMsg
	}
	return ""
}

func (x *GetThreadMsgWithTsResponse) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserGroupMembersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserGroupId string `protobuf:"bytes,1,opt,name=user_group_id,json=userGroupId,proto3" json:"user_group_id,omitempty"`
}

func (x *GetUserGroupMembersRequest) Reset() {
	*x = GetUserGroupMembersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserGroupMembersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserGroupMembersRequest) ProtoMessage() {}

func (x *GetUserGroupMembersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserGroupMembersRequest.ProtoReflect.Descriptor instead.
func (*GetUserGroupMembersRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{22}
}

func (x *GetUserGroupMembersRequest) GetUserGroupId() string {
	if x != nil {
		return x.UserGroupId
	}
	return ""
}

type GetUserGroupMembersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Users  []*types.User `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *GetUserGroupMembersResponse) Reset() {
	*x = GetUserGroupMembersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserGroupMembersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserGroupMembersResponse) ProtoMessage() {}

func (x *GetUserGroupMembersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserGroupMembersResponse.ProtoReflect.Descriptor instead.
func (*GetUserGroupMembersResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetUserGroupMembersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetUserGroupMembersResponse) GetUsers() []*types.User {
	if x != nil {
		return x.Users
	}
	return nil
}

type GetFileInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	FileId string                       `protobuf:"bytes,2,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
}

func (x *GetFileInfoRequest) Reset() {
	*x = GetFileInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFileInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileInfoRequest) ProtoMessage() {}

func (x *GetFileInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileInfoRequest.ProtoReflect.Descriptor instead.
func (*GetFileInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetFileInfoRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetFileInfoRequest) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

type GetFileInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	FileInfo *FileInfo   `protobuf:"bytes,2,opt,name=file_info,json=fileInfo,proto3" json:"file_info,omitempty"`
}

func (x *GetFileInfoResponse) Reset() {
	*x = GetFileInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFileInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileInfoResponse) ProtoMessage() {}

func (x *GetFileInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileInfoResponse.ProtoReflect.Descriptor instead.
func (*GetFileInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetFileInfoResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetFileInfoResponse) GetFileInfo() *FileInfo {
	if x != nil {
		return x.FileInfo
	}
	return nil
}

type DownloadFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	FileId string                       `protobuf:"bytes,2,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
}

func (x *DownloadFileRequest) Reset() {
	*x = DownloadFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadFileRequest) ProtoMessage() {}

func (x *DownloadFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadFileRequest.ProtoReflect.Descriptor instead.
func (*DownloadFileRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{26}
}

func (x *DownloadFileRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *DownloadFileRequest) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

type DownloadFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Content []byte      `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *DownloadFileResponse) Reset() {
	*x = DownloadFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadFileResponse) ProtoMessage() {}

func (x *DownloadFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadFileResponse.ProtoReflect.Descriptor instead.
func (*DownloadFileResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{27}
}

func (x *DownloadFileResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *DownloadFileResponse) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

type FileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name               string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	UrlPrivate         string `protobuf:"bytes,3,opt,name=url_private,json=urlPrivate,proto3" json:"url_private,omitempty"`
	Mimetype           string `protobuf:"bytes,4,opt,name=mimetype,proto3" json:"mimetype,omitempty"`
	Size               int64  `protobuf:"varint,5,opt,name=size,proto3" json:"size,omitempty"`
	UrlPrivateDownload string `protobuf:"bytes,6,opt,name=url_private_download,json=urlPrivateDownload,proto3" json:"url_private_download,omitempty"`
}

func (x *FileInfo) Reset() {
	*x = FileInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileInfo) ProtoMessage() {}

func (x *FileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileInfo.ProtoReflect.Descriptor instead.
func (*FileInfo) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{28}
}

func (x *FileInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *FileInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FileInfo) GetUrlPrivate() string {
	if x != nil {
		return x.UrlPrivate
	}
	return ""
}

func (x *FileInfo) GetMimetype() string {
	if x != nil {
		return x.Mimetype
	}
	return ""
}

func (x *FileInfo) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *FileInfo) GetUrlPrivateDownload() string {
	if x != nil {
		return x.UrlPrivateDownload
	}
	return ""
}

type DownloadFileFromPrivateURLRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header             *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	PrivateDownloadUrl string                       `protobuf:"bytes,2,opt,name=private_download_url,json=privateDownloadUrl,proto3" json:"private_download_url,omitempty"`
}

func (x *DownloadFileFromPrivateURLRequest) Reset() {
	*x = DownloadFileFromPrivateURLRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadFileFromPrivateURLRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadFileFromPrivateURLRequest) ProtoMessage() {}

func (x *DownloadFileFromPrivateURLRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadFileFromPrivateURLRequest.ProtoReflect.Descriptor instead.
func (*DownloadFileFromPrivateURLRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{29}
}

func (x *DownloadFileFromPrivateURLRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *DownloadFileFromPrivateURLRequest) GetPrivateDownloadUrl() string {
	if x != nil {
		return x.PrivateDownloadUrl
	}
	return ""
}

type DownloadFileFromPrivateURLResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Content []byte      `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *DownloadFileFromPrivateURLResponse) Reset() {
	*x = DownloadFileFromPrivateURLResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadFileFromPrivateURLResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadFileFromPrivateURLResponse) ProtoMessage() {}

func (x *DownloadFileFromPrivateURLResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadFileFromPrivateURLResponse.ProtoReflect.Descriptor instead.
func (*DownloadFileFromPrivateURLResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP(), []int{30}
}

func (x *DownloadFileFromPrivateURLResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *DownloadFileFromPrivateURLResponse) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

var File_api_vendorgateway_slack_bot_service_proto protoreflect.FileDescriptor

var file_api_vendorgateway_slack_bot_service_proto_rawDesc = []byte{
	0x0a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b,
	0x5f, 0x62, 0x6f, 0x74, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2f, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x32, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2f, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x73, 0x6c, 0x61, 0x63, 0x6b,
	0x5f, 0x62, 0x6f, 0x74, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4f, 0x0a, 0x17, 0x4c,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x7b, 0x0a, 0x18,
	0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3a, 0x0a,
	0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61,
	0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x22, 0x8f, 0x02, 0x0a, 0x13, 0x50, 0x6f,
	0x73, 0x74, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x08, 0x74, 0x65, 0x78, 0x74, 0x5f,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x74, 0x65, 0x78,
	0x74, 0x4d, 0x73, 0x67, 0x12, 0x4b, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62,
	0x6f, 0x74, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d,
	0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x37, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x42, 0x05, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x4b, 0x0a, 0x14, 0x50,
	0x6f, 0x73, 0x74, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74, 0x73, 0x22, 0xa7, 0x02, 0x0a, 0x1b, 0x50, 0x6f, 0x73,
	0x74, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x49, 0x6e, 0x54, 0x68, 0x72, 0x65, 0x61,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1b,
	0x0a, 0x08, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x07, 0x74, 0x65, 0x78, 0x74, 0x4d, 0x73, 0x67, 0x12, 0x4b, 0x0a, 0x0a, 0x61,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x42, 0x05, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x22, 0x43, 0x0a, 0x1c, 0x50, 0x6f, 0x73, 0x74, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x4d,
	0x73, 0x67, 0x49, 0x6e, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xb4, 0x01, 0x0a, 0x14, 0x4f, 0x70, 0x65, 0x6e,
	0x56, 0x69, 0x65, 0x77, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0a, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x6d, 0x6f,
	0x64, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f,
	0x62, 0x6f, 0x74, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x56, 0x69, 0x65, 0x77, 0x4d, 0x6f,
	0x64, 0x61, 0x6c, 0x52, 0x09, 0x76, 0x69, 0x65, 0x77, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x22, 0x55,
	0x0a, 0x15, 0x4f, 0x70, 0x65, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x07,
	0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x76,
	0x69, 0x65, 0x77, 0x49, 0x64, 0x22, 0x7a, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x6c, 0x61, 0x63,
	0x6b, 0x4d, 0x73, 0x67, 0x57, 0x69, 0x74, 0x68, 0x54, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74,
	0x73, 0x22, 0xbc, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x73,
	0x67, 0x57, 0x69, 0x74, 0x68, 0x54, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x19, 0x0a, 0x08,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x74, 0x65, 0x78, 0x74, 0x4d, 0x73, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x22, 0xc2, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x61, 0x63, 0x6b,
	0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74, 0x73, 0x12, 0x49, 0x0a, 0x0a, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73,
	0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63,
	0x68, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x3d, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x74, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x28, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x4f, 0x72, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x73, 0x4f, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x75, 0x0a, 0x13, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72,
	0x73, 0x22, 0x73, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4d, 0x73, 0x67, 0x4c, 0x69, 0x6e, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x74, 0x73, 0x22, 0x4b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4d, 0x73, 0x67,
	0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x22, 0xb0, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x69,
	0x65, 0x77, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x76, 0x69, 0x65, 0x77, 0x49, 0x64, 0x12, 0x47, 0x0a,
	0x0a, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x56, 0x69, 0x65, 0x77, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x52, 0x09, 0x76, 0x69, 0x65,
	0x77, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x22, 0x3e, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x56, 0x69, 0x65, 0x77, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x3f, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27,
	0x0a, 0x0f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x85, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x47, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b,
	0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22,
	0x7b, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x73, 0x67, 0x57,
	0x69, 0x74, 0x68, 0x54, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74, 0x73, 0x22, 0x75, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x73, 0x67, 0x57, 0x69, 0x74, 0x68,
	0x54, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x74, 0x65, 0x78, 0x74, 0x4d, 0x73, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x22, 0x40, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x22, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x22, 0x7d, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x05, 0x75, 0x73, 0x65,
	0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62,
	0x6f, 0x74, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x75,
	0x73, 0x65, 0x72, 0x73, 0x22, 0x63, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x7a, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62,
	0x6f, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x64, 0x0a, 0x13, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x55, 0x0a, 0x14, 0x44,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x22, 0xb1, 0x01, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x72, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x72, 0x6c, 0x50, 0x72, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x75, 0x72, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x75, 0x72, 0x6c, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0x8b, 0x01, 0x0a, 0x21, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x50, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x55, 0x52, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x55, 0x72, 0x6c, 0x22, 0x63, 0x0a, 0x22, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x46, 0x69, 0x6c, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x55,
	0x52, 0x4c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x32, 0x80, 0x0e, 0x0a, 0x08, 0x53, 0x6c,
	0x61, 0x63, 0x6b, 0x42, 0x6f, 0x74, 0x12, 0x7a, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e,
	0x53, 0x6c, 0x61, 0x63, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x30, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63,
	0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x53, 0x6c, 0x61, 0x63,
	0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c,
	0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x53, 0x6c,
	0x61, 0x63, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x30, 0x01, 0x12, 0x6b, 0x0a, 0x0c, 0x50, 0x6f, 0x73, 0x74, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x4d,
	0x73, 0x67, 0x12, 0x2c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x50, 0x6f, 0x73,
	0x74, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x53,
	0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x83, 0x01, 0x0a, 0x14, 0x50, 0x6f, 0x73, 0x74, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67,
	0x49, 0x6e, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x12, 0x34, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62,
	0x6f, 0x74, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x49,
	0x6e, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73,
	0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x53, 0x6c, 0x61,
	0x63, 0x6b, 0x4d, 0x73, 0x67, 0x49, 0x6e, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x0d, 0x4f, 0x70, 0x65, 0x6e, 0x56, 0x69, 0x65,
	0x77, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x12, 0x2d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74,
	0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e,
	0x4f, 0x70, 0x65, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7a, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x6c, 0x61, 0x63,
	0x6b, 0x4d, 0x73, 0x67, 0x57, 0x69, 0x74, 0x68, 0x54, 0x73, 0x12, 0x31, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b,
	0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67,
	0x57, 0x69, 0x74, 0x68, 0x54, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c,
	0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x6c, 0x61, 0x63, 0x6b,
	0x4d, 0x73, 0x67, 0x57, 0x69, 0x74, 0x68, 0x54, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x71, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x61, 0x63, 0x6b,
	0x4d, 0x73, 0x67, 0x12, 0x2e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x2b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65,
	0x0a, 0x0a, 0x47, 0x65, 0x74, 0x4d, 0x73, 0x67, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x2a, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61,
	0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x73, 0x67, 0x4c, 0x69, 0x6e,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62,
	0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x73, 0x67, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x74, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56,
	0x69, 0x65, 0x77, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x12, 0x2f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62,
	0x6f, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x4d, 0x6f, 0x64,
	0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f,
	0x62, 0x6f, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x4d, 0x6f,
	0x64, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x0d, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x2d, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61,
	0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63,
	0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7d, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x73, 0x67, 0x57, 0x69, 0x74, 0x68, 0x54,
	0x73, 0x12, 0x32, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x68, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x73, 0x67, 0x57, 0x69, 0x74, 0x68, 0x54, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x73, 0x67, 0x57, 0x69, 0x74, 0x68,
	0x54, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x80, 0x01, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x12, 0x33, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a,
	0x0b, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2b, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61,
	0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f,
	0x62, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6b, 0x0a, 0x0c, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x2c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f,
	0x74, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x95, 0x01, 0x0a, 0x1a, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x46, 0x69, 0x6c, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x55, 0x52, 0x4c, 0x12, 0x3a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x50, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x55, 0x52, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x55, 0x52, 0x4c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x68, 0x0a, 0x32,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62,
	0x6f, 0x74, 0x5a, 0x32, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x73, 0x6c, 0x61,
	0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_slack_bot_service_proto_rawDescOnce sync.Once
	file_api_vendorgateway_slack_bot_service_proto_rawDescData = file_api_vendorgateway_slack_bot_service_proto_rawDesc
)

func file_api_vendorgateway_slack_bot_service_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_slack_bot_service_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_slack_bot_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_slack_bot_service_proto_rawDescData)
	})
	return file_api_vendorgateway_slack_bot_service_proto_rawDescData
}

var file_api_vendorgateway_slack_bot_service_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_api_vendorgateway_slack_bot_service_proto_goTypes = []interface{}{
	(*ListenSlackEventRequest)(nil),            // 0: vendorgateway.slack_bot.ListenSlackEventRequest
	(*ListenSlackEventResponse)(nil),           // 1: vendorgateway.slack_bot.ListenSlackEventResponse
	(*PostSlackMsgRequest)(nil),                // 2: vendorgateway.slack_bot.PostSlackMsgRequest
	(*PostSlackMsgResponse)(nil),               // 3: vendorgateway.slack_bot.PostSlackMsgResponse
	(*PostSlackMsgInThreadRequest)(nil),        // 4: vendorgateway.slack_bot.PostSlackMsgInThreadRequest
	(*PostSlackMsgInThreadResponse)(nil),       // 5: vendorgateway.slack_bot.PostSlackMsgInThreadResponse
	(*OpenViewModalRequest)(nil),               // 6: vendorgateway.slack_bot.OpenViewModalRequest
	(*OpenViewModalResponse)(nil),              // 7: vendorgateway.slack_bot.OpenViewModalResponse
	(*GetSlackMsgWithTsRequest)(nil),           // 8: vendorgateway.slack_bot.GetSlackMsgWithTsRequest
	(*GetSlackMsgWithTsResponse)(nil),          // 9: vendorgateway.slack_bot.GetSlackMsgWithTsResponse
	(*UpdateSlackMsgRequest)(nil),              // 10: vendorgateway.slack_bot.UpdateSlackMsgRequest
	(*UpdateSlackMsgResponse)(nil),             // 11: vendorgateway.slack_bot.UpdateSlackMsgResponse
	(*GetUserInfoRequest)(nil),                 // 12: vendorgateway.slack_bot.GetUserInfoRequest
	(*GetUserInfoResponse)(nil),                // 13: vendorgateway.slack_bot.GetUserInfoResponse
	(*GetMsgLinkRequest)(nil),                  // 14: vendorgateway.slack_bot.GetMsgLinkRequest
	(*GetMsgLinkResponse)(nil),                 // 15: vendorgateway.slack_bot.GetMsgLinkResponse
	(*UpdateViewModalRequest)(nil),             // 16: vendorgateway.slack_bot.UpdateViewModalRequest
	(*UpdateViewModalResponse)(nil),            // 17: vendorgateway.slack_bot.UpdateViewModalResponse
	(*GetUserGroupsRequest)(nil),               // 18: vendorgateway.slack_bot.GetUserGroupsRequest
	(*GetUserGroupsResponse)(nil),              // 19: vendorgateway.slack_bot.GetUserGroupsResponse
	(*GetThreadMsgWithTsRequest)(nil),          // 20: vendorgateway.slack_bot.GetThreadMsgWithTsRequest
	(*GetThreadMsgWithTsResponse)(nil),         // 21: vendorgateway.slack_bot.GetThreadMsgWithTsResponse
	(*GetUserGroupMembersRequest)(nil),         // 22: vendorgateway.slack_bot.GetUserGroupMembersRequest
	(*GetUserGroupMembersResponse)(nil),        // 23: vendorgateway.slack_bot.GetUserGroupMembersResponse
	(*GetFileInfoRequest)(nil),                 // 24: vendorgateway.slack_bot.GetFileInfoRequest
	(*GetFileInfoResponse)(nil),                // 25: vendorgateway.slack_bot.GetFileInfoResponse
	(*DownloadFileRequest)(nil),                // 26: vendorgateway.slack_bot.DownloadFileRequest
	(*DownloadFileResponse)(nil),               // 27: vendorgateway.slack_bot.DownloadFileResponse
	(*FileInfo)(nil),                           // 28: vendorgateway.slack_bot.FileInfo
	(*DownloadFileFromPrivateURLRequest)(nil),  // 29: vendorgateway.slack_bot.DownloadFileFromPrivateURLRequest
	(*DownloadFileFromPrivateURLResponse)(nil), // 30: vendorgateway.slack_bot.DownloadFileFromPrivateURLResponse
	(*vendorgateway.RequestHeader)(nil),        // 31: vendorgateway.RequestHeader
	(*rpc.Status)(nil),                         // 32: rpc.Status
	(*types.Event)(nil),                        // 33: vendorgateway.slack_bot.types.Event
	(*types.Attachment)(nil),                   // 34: vendorgateway.slack_bot.types.Attachment
	(*types.User)(nil),                         // 35: vendorgateway.slack_bot.types.User
	(*types.ViewModal)(nil),                    // 36: vendorgateway.slack_bot.types.ViewModal
	(*types.AttachmentField)(nil),              // 37: vendorgateway.slack_bot.types.AttachmentField
	(*types.UserGroup)(nil),                    // 38: vendorgateway.slack_bot.types.UserGroup
}
var file_api_vendorgateway_slack_bot_service_proto_depIdxs = []int32{
	31, // 0: vendorgateway.slack_bot.ListenSlackEventRequest.header:type_name -> vendorgateway.RequestHeader
	32, // 1: vendorgateway.slack_bot.ListenSlackEventResponse.status:type_name -> rpc.Status
	33, // 2: vendorgateway.slack_bot.ListenSlackEventResponse.event:type_name -> vendorgateway.slack_bot.types.Event
	31, // 3: vendorgateway.slack_bot.PostSlackMsgRequest.header:type_name -> vendorgateway.RequestHeader
	34, // 4: vendorgateway.slack_bot.PostSlackMsgRequest.attachment:type_name -> vendorgateway.slack_bot.types.Attachment
	35, // 5: vendorgateway.slack_bot.PostSlackMsgRequest.user:type_name -> vendorgateway.slack_bot.types.User
	32, // 6: vendorgateway.slack_bot.PostSlackMsgResponse.status:type_name -> rpc.Status
	31, // 7: vendorgateway.slack_bot.PostSlackMsgInThreadRequest.header:type_name -> vendorgateway.RequestHeader
	34, // 8: vendorgateway.slack_bot.PostSlackMsgInThreadRequest.attachment:type_name -> vendorgateway.slack_bot.types.Attachment
	35, // 9: vendorgateway.slack_bot.PostSlackMsgInThreadRequest.user:type_name -> vendorgateway.slack_bot.types.User
	32, // 10: vendorgateway.slack_bot.PostSlackMsgInThreadResponse.status:type_name -> rpc.Status
	31, // 11: vendorgateway.slack_bot.OpenViewModalRequest.header:type_name -> vendorgateway.RequestHeader
	36, // 12: vendorgateway.slack_bot.OpenViewModalRequest.view_modal:type_name -> vendorgateway.slack_bot.types.ViewModal
	32, // 13: vendorgateway.slack_bot.OpenViewModalResponse.status:type_name -> rpc.Status
	31, // 14: vendorgateway.slack_bot.GetSlackMsgWithTsRequest.header:type_name -> vendorgateway.RequestHeader
	32, // 15: vendorgateway.slack_bot.GetSlackMsgWithTsResponse.status:type_name -> rpc.Status
	37, // 16: vendorgateway.slack_bot.GetSlackMsgWithTsResponse.fields:type_name -> vendorgateway.slack_bot.types.AttachmentField
	31, // 17: vendorgateway.slack_bot.UpdateSlackMsgRequest.header:type_name -> vendorgateway.RequestHeader
	34, // 18: vendorgateway.slack_bot.UpdateSlackMsgRequest.attachment:type_name -> vendorgateway.slack_bot.types.Attachment
	32, // 19: vendorgateway.slack_bot.UpdateSlackMsgResponse.status:type_name -> rpc.Status
	31, // 20: vendorgateway.slack_bot.GetUserInfoRequest.header:type_name -> vendorgateway.RequestHeader
	32, // 21: vendorgateway.slack_bot.GetUserInfoResponse.status:type_name -> rpc.Status
	35, // 22: vendorgateway.slack_bot.GetUserInfoResponse.users:type_name -> vendorgateway.slack_bot.types.User
	31, // 23: vendorgateway.slack_bot.GetMsgLinkRequest.header:type_name -> vendorgateway.RequestHeader
	32, // 24: vendorgateway.slack_bot.GetMsgLinkResponse.status:type_name -> rpc.Status
	31, // 25: vendorgateway.slack_bot.UpdateViewModalRequest.header:type_name -> vendorgateway.RequestHeader
	36, // 26: vendorgateway.slack_bot.UpdateViewModalRequest.view_modal:type_name -> vendorgateway.slack_bot.types.ViewModal
	32, // 27: vendorgateway.slack_bot.UpdateViewModalResponse.status:type_name -> rpc.Status
	32, // 28: vendorgateway.slack_bot.GetUserGroupsResponse.status:type_name -> rpc.Status
	38, // 29: vendorgateway.slack_bot.GetUserGroupsResponse.user_group:type_name -> vendorgateway.slack_bot.types.UserGroup
	31, // 30: vendorgateway.slack_bot.GetThreadMsgWithTsRequest.header:type_name -> vendorgateway.RequestHeader
	32, // 31: vendorgateway.slack_bot.GetThreadMsgWithTsResponse.status:type_name -> rpc.Status
	32, // 32: vendorgateway.slack_bot.GetUserGroupMembersResponse.status:type_name -> rpc.Status
	35, // 33: vendorgateway.slack_bot.GetUserGroupMembersResponse.users:type_name -> vendorgateway.slack_bot.types.User
	31, // 34: vendorgateway.slack_bot.GetFileInfoRequest.header:type_name -> vendorgateway.RequestHeader
	32, // 35: vendorgateway.slack_bot.GetFileInfoResponse.status:type_name -> rpc.Status
	28, // 36: vendorgateway.slack_bot.GetFileInfoResponse.file_info:type_name -> vendorgateway.slack_bot.FileInfo
	31, // 37: vendorgateway.slack_bot.DownloadFileRequest.header:type_name -> vendorgateway.RequestHeader
	32, // 38: vendorgateway.slack_bot.DownloadFileResponse.status:type_name -> rpc.Status
	31, // 39: vendorgateway.slack_bot.DownloadFileFromPrivateURLRequest.header:type_name -> vendorgateway.RequestHeader
	32, // 40: vendorgateway.slack_bot.DownloadFileFromPrivateURLResponse.status:type_name -> rpc.Status
	0,  // 41: vendorgateway.slack_bot.SlackBot.ListenSlackEvents:input_type -> vendorgateway.slack_bot.ListenSlackEventRequest
	2,  // 42: vendorgateway.slack_bot.SlackBot.PostSlackMsg:input_type -> vendorgateway.slack_bot.PostSlackMsgRequest
	4,  // 43: vendorgateway.slack_bot.SlackBot.PostSlackMsgInThread:input_type -> vendorgateway.slack_bot.PostSlackMsgInThreadRequest
	6,  // 44: vendorgateway.slack_bot.SlackBot.OpenViewModal:input_type -> vendorgateway.slack_bot.OpenViewModalRequest
	8,  // 45: vendorgateway.slack_bot.SlackBot.GetSlackMsgWithTs:input_type -> vendorgateway.slack_bot.GetSlackMsgWithTsRequest
	10, // 46: vendorgateway.slack_bot.SlackBot.UpdateSlackMsg:input_type -> vendorgateway.slack_bot.UpdateSlackMsgRequest
	12, // 47: vendorgateway.slack_bot.SlackBot.GetUserInfo:input_type -> vendorgateway.slack_bot.GetUserInfoRequest
	14, // 48: vendorgateway.slack_bot.SlackBot.GetMsgLink:input_type -> vendorgateway.slack_bot.GetMsgLinkRequest
	16, // 49: vendorgateway.slack_bot.SlackBot.UpdateViewModal:input_type -> vendorgateway.slack_bot.UpdateViewModalRequest
	18, // 50: vendorgateway.slack_bot.SlackBot.GetUserGroups:input_type -> vendorgateway.slack_bot.GetUserGroupsRequest
	20, // 51: vendorgateway.slack_bot.SlackBot.GetThreadMsgWithTs:input_type -> vendorgateway.slack_bot.GetThreadMsgWithTsRequest
	22, // 52: vendorgateway.slack_bot.SlackBot.GetUserGroupMembers:input_type -> vendorgateway.slack_bot.GetUserGroupMembersRequest
	24, // 53: vendorgateway.slack_bot.SlackBot.GetFileInfo:input_type -> vendorgateway.slack_bot.GetFileInfoRequest
	26, // 54: vendorgateway.slack_bot.SlackBot.DownloadFile:input_type -> vendorgateway.slack_bot.DownloadFileRequest
	29, // 55: vendorgateway.slack_bot.SlackBot.DownloadFileFromPrivateURL:input_type -> vendorgateway.slack_bot.DownloadFileFromPrivateURLRequest
	1,  // 56: vendorgateway.slack_bot.SlackBot.ListenSlackEvents:output_type -> vendorgateway.slack_bot.ListenSlackEventResponse
	3,  // 57: vendorgateway.slack_bot.SlackBot.PostSlackMsg:output_type -> vendorgateway.slack_bot.PostSlackMsgResponse
	5,  // 58: vendorgateway.slack_bot.SlackBot.PostSlackMsgInThread:output_type -> vendorgateway.slack_bot.PostSlackMsgInThreadResponse
	7,  // 59: vendorgateway.slack_bot.SlackBot.OpenViewModal:output_type -> vendorgateway.slack_bot.OpenViewModalResponse
	9,  // 60: vendorgateway.slack_bot.SlackBot.GetSlackMsgWithTs:output_type -> vendorgateway.slack_bot.GetSlackMsgWithTsResponse
	11, // 61: vendorgateway.slack_bot.SlackBot.UpdateSlackMsg:output_type -> vendorgateway.slack_bot.UpdateSlackMsgResponse
	13, // 62: vendorgateway.slack_bot.SlackBot.GetUserInfo:output_type -> vendorgateway.slack_bot.GetUserInfoResponse
	15, // 63: vendorgateway.slack_bot.SlackBot.GetMsgLink:output_type -> vendorgateway.slack_bot.GetMsgLinkResponse
	17, // 64: vendorgateway.slack_bot.SlackBot.UpdateViewModal:output_type -> vendorgateway.slack_bot.UpdateViewModalResponse
	19, // 65: vendorgateway.slack_bot.SlackBot.GetUserGroups:output_type -> vendorgateway.slack_bot.GetUserGroupsResponse
	21, // 66: vendorgateway.slack_bot.SlackBot.GetThreadMsgWithTs:output_type -> vendorgateway.slack_bot.GetThreadMsgWithTsResponse
	23, // 67: vendorgateway.slack_bot.SlackBot.GetUserGroupMembers:output_type -> vendorgateway.slack_bot.GetUserGroupMembersResponse
	25, // 68: vendorgateway.slack_bot.SlackBot.GetFileInfo:output_type -> vendorgateway.slack_bot.GetFileInfoResponse
	27, // 69: vendorgateway.slack_bot.SlackBot.DownloadFile:output_type -> vendorgateway.slack_bot.DownloadFileResponse
	30, // 70: vendorgateway.slack_bot.SlackBot.DownloadFileFromPrivateURL:output_type -> vendorgateway.slack_bot.DownloadFileFromPrivateURLResponse
	56, // [56:71] is the sub-list for method output_type
	41, // [41:56] is the sub-list for method input_type
	41, // [41:41] is the sub-list for extension type_name
	41, // [41:41] is the sub-list for extension extendee
	0,  // [0:41] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_slack_bot_service_proto_init() }
func file_api_vendorgateway_slack_bot_service_proto_init() {
	if File_api_vendorgateway_slack_bot_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListenSlackEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListenSlackEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostSlackMsgRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostSlackMsgResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostSlackMsgInThreadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostSlackMsgInThreadResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenViewModalRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenViewModalResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSlackMsgWithTsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSlackMsgWithTsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSlackMsgRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSlackMsgResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMsgLinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMsgLinkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateViewModalRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateViewModalResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserGroupsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserGroupsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetThreadMsgWithTsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetThreadMsgWithTsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserGroupMembersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserGroupMembersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFileInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFileInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FileInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadFileFromPrivateURLRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_slack_bot_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadFileFromPrivateURLResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_vendorgateway_slack_bot_service_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*PostSlackMsgRequest_TextMsg)(nil),
		(*PostSlackMsgRequest_Attachment)(nil),
	}
	file_api_vendorgateway_slack_bot_service_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*PostSlackMsgInThreadRequest_TextMsg)(nil),
		(*PostSlackMsgInThreadRequest_Attachment)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_slack_bot_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendorgateway_slack_bot_service_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_slack_bot_service_proto_depIdxs,
		MessageInfos:      file_api_vendorgateway_slack_bot_service_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_slack_bot_service_proto = out.File
	file_api_vendorgateway_slack_bot_service_proto_rawDesc = nil
	file_api_vendorgateway_slack_bot_service_proto_goTypes = nil
	file_api_vendorgateway_slack_bot_service_proto_depIdxs = nil
}
