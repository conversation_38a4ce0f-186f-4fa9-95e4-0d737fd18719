// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendorgateway/wealth/cvl/service.proto

package cvl

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Cvl_GetPassword_FullMethodName            = "/vendorgateway.wealth.cvl.Cvl/GetPassword"
	Cvl_GetPanStatus_FullMethodName           = "/vendorgateway.wealth.cvl.Cvl/GetPanStatus"
	Cvl_PanDetailsFetch_FullMethodName        = "/vendorgateway.wealth.cvl.Cvl/PanDetailsFetch"
	Cvl_InsertUpdateKycRecord_FullMethodName  = "/vendorgateway.wealth.cvl.Cvl/InsertUpdateKycRecord"
	Cvl_UploadFile_FullMethodName             = "/vendorgateway.wealth.cvl.Cvl/UploadFile"
	Cvl_DownloadDir_FullMethodName            = "/vendorgateway.wealth.cvl.Cvl/DownloadDir"
	Cvl_DownloadFile_FullMethodName           = "/vendorgateway.wealth.cvl.Cvl/DownloadFile"
	Cvl_ListDirFiles_FullMethodName           = "/vendorgateway.wealth.cvl.Cvl/ListDirFiles"
	Cvl_DownloadFileWithStream_FullMethodName = "/vendorgateway.wealth.cvl.Cvl/DownloadFileWithStream"
)

// CvlClient is the client API for Cvl service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CvlClient interface {
	// GetPassword was created to get the latest generated password from CVL, but since CVL doesn't change it, the vendor call isn't made anymore
	// Currently, it retrieves the password required to make CVL API calls from AWS secret manager and caches it for a specific duration
	GetPassword(ctx context.Context, in *GetPasswordRequest, opts ...grpc.CallOption) (*GetPasswordResponse, error)
	// GenPanStatus RPC is used to fetch pan status of a customer
	GetPanStatus(ctx context.Context, in *GetPanStatusRequest, opts ...grpc.CallOption) (*GetPanStatusResponse, error)
	// PanDetailsFetch is used to fetch PAN data of a customer
	// To get the PAN details of a user, their date of birth (DoB) must be sent in the request along with PAN
	// In case DoB sent in request doesn't match with the DoB that CVL has in their KYC records, CVL might respond with an error code or with missing fields in response
	// Some of these error scenarios have been handled specially in this RPC to return InvalidArgument as the status code
	// All other error scenarios result in Internal status code as default
	PanDetailsFetch(ctx context.Context, in *PanDetailsFetchRequest, opts ...grpc.CallOption) (*PanDetailsFetchResponse, error)
	// InsertUpdateKycRecord is used to insert or update kyc record of customer
	InsertUpdateKycRecord(ctx context.Context, in *InsertUpdateKycRecordRequest, opts ...grpc.CallOption) (*InsertUpdateKycRecordResponse, error)
	// UploadFile RPC is used for uploading a file on the sftp server remote path
	UploadFile(ctx context.Context, in *UploadFileRequest, opts ...grpc.CallOption) (*UploadFileResponse, error)
	// DownloadDir RPC is used for downloading file contents of dir from sftp server
	DownloadDir(ctx context.Context, in *DownloadDirRequest, opts ...grpc.CallOption) (*DownloadDirResponse, error)
	// DownloadFile RPC is used for downloading a single file from sftp server
	DownloadFile(ctx context.Context, in *DownloadFileRequest, opts ...grpc.CallOption) (*DownloadFileResponse, error)
	// ListDirFiles RPC is used for listing file names of a dir from sftp server
	ListDirFiles(ctx context.Context, in *ListDirFilesRequest, opts ...grpc.CallOption) (*ListDirFilesResponse, error)
	// DownloadFileWithStream RPC is used for downloading a single file from sftp server using grpc streams
	DownloadFileWithStream(ctx context.Context, in *DownloadFileWithStreamRequest, opts ...grpc.CallOption) (Cvl_DownloadFileWithStreamClient, error)
}

type cvlClient struct {
	cc grpc.ClientConnInterface
}

func NewCvlClient(cc grpc.ClientConnInterface) CvlClient {
	return &cvlClient{cc}
}

func (c *cvlClient) GetPassword(ctx context.Context, in *GetPasswordRequest, opts ...grpc.CallOption) (*GetPasswordResponse, error) {
	out := new(GetPasswordResponse)
	err := c.cc.Invoke(ctx, Cvl_GetPassword_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cvlClient) GetPanStatus(ctx context.Context, in *GetPanStatusRequest, opts ...grpc.CallOption) (*GetPanStatusResponse, error) {
	out := new(GetPanStatusResponse)
	err := c.cc.Invoke(ctx, Cvl_GetPanStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cvlClient) PanDetailsFetch(ctx context.Context, in *PanDetailsFetchRequest, opts ...grpc.CallOption) (*PanDetailsFetchResponse, error) {
	out := new(PanDetailsFetchResponse)
	err := c.cc.Invoke(ctx, Cvl_PanDetailsFetch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cvlClient) InsertUpdateKycRecord(ctx context.Context, in *InsertUpdateKycRecordRequest, opts ...grpc.CallOption) (*InsertUpdateKycRecordResponse, error) {
	out := new(InsertUpdateKycRecordResponse)
	err := c.cc.Invoke(ctx, Cvl_InsertUpdateKycRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cvlClient) UploadFile(ctx context.Context, in *UploadFileRequest, opts ...grpc.CallOption) (*UploadFileResponse, error) {
	out := new(UploadFileResponse)
	err := c.cc.Invoke(ctx, Cvl_UploadFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cvlClient) DownloadDir(ctx context.Context, in *DownloadDirRequest, opts ...grpc.CallOption) (*DownloadDirResponse, error) {
	out := new(DownloadDirResponse)
	err := c.cc.Invoke(ctx, Cvl_DownloadDir_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cvlClient) DownloadFile(ctx context.Context, in *DownloadFileRequest, opts ...grpc.CallOption) (*DownloadFileResponse, error) {
	out := new(DownloadFileResponse)
	err := c.cc.Invoke(ctx, Cvl_DownloadFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cvlClient) ListDirFiles(ctx context.Context, in *ListDirFilesRequest, opts ...grpc.CallOption) (*ListDirFilesResponse, error) {
	out := new(ListDirFilesResponse)
	err := c.cc.Invoke(ctx, Cvl_ListDirFiles_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cvlClient) DownloadFileWithStream(ctx context.Context, in *DownloadFileWithStreamRequest, opts ...grpc.CallOption) (Cvl_DownloadFileWithStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &Cvl_ServiceDesc.Streams[0], Cvl_DownloadFileWithStream_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &cvlDownloadFileWithStreamClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Cvl_DownloadFileWithStreamClient interface {
	Recv() (*DownloadFileWithStreamResponse, error)
	grpc.ClientStream
}

type cvlDownloadFileWithStreamClient struct {
	grpc.ClientStream
}

func (x *cvlDownloadFileWithStreamClient) Recv() (*DownloadFileWithStreamResponse, error) {
	m := new(DownloadFileWithStreamResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// CvlServer is the server API for Cvl service.
// All implementations should embed UnimplementedCvlServer
// for forward compatibility
type CvlServer interface {
	// GetPassword was created to get the latest generated password from CVL, but since CVL doesn't change it, the vendor call isn't made anymore
	// Currently, it retrieves the password required to make CVL API calls from AWS secret manager and caches it for a specific duration
	GetPassword(context.Context, *GetPasswordRequest) (*GetPasswordResponse, error)
	// GenPanStatus RPC is used to fetch pan status of a customer
	GetPanStatus(context.Context, *GetPanStatusRequest) (*GetPanStatusResponse, error)
	// PanDetailsFetch is used to fetch PAN data of a customer
	// To get the PAN details of a user, their date of birth (DoB) must be sent in the request along with PAN
	// In case DoB sent in request doesn't match with the DoB that CVL has in their KYC records, CVL might respond with an error code or with missing fields in response
	// Some of these error scenarios have been handled specially in this RPC to return InvalidArgument as the status code
	// All other error scenarios result in Internal status code as default
	PanDetailsFetch(context.Context, *PanDetailsFetchRequest) (*PanDetailsFetchResponse, error)
	// InsertUpdateKycRecord is used to insert or update kyc record of customer
	InsertUpdateKycRecord(context.Context, *InsertUpdateKycRecordRequest) (*InsertUpdateKycRecordResponse, error)
	// UploadFile RPC is used for uploading a file on the sftp server remote path
	UploadFile(context.Context, *UploadFileRequest) (*UploadFileResponse, error)
	// DownloadDir RPC is used for downloading file contents of dir from sftp server
	DownloadDir(context.Context, *DownloadDirRequest) (*DownloadDirResponse, error)
	// DownloadFile RPC is used for downloading a single file from sftp server
	DownloadFile(context.Context, *DownloadFileRequest) (*DownloadFileResponse, error)
	// ListDirFiles RPC is used for listing file names of a dir from sftp server
	ListDirFiles(context.Context, *ListDirFilesRequest) (*ListDirFilesResponse, error)
	// DownloadFileWithStream RPC is used for downloading a single file from sftp server using grpc streams
	DownloadFileWithStream(*DownloadFileWithStreamRequest, Cvl_DownloadFileWithStreamServer) error
}

// UnimplementedCvlServer should be embedded to have forward compatible implementations.
type UnimplementedCvlServer struct {
}

func (UnimplementedCvlServer) GetPassword(context.Context, *GetPasswordRequest) (*GetPasswordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPassword not implemented")
}
func (UnimplementedCvlServer) GetPanStatus(context.Context, *GetPanStatusRequest) (*GetPanStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPanStatus not implemented")
}
func (UnimplementedCvlServer) PanDetailsFetch(context.Context, *PanDetailsFetchRequest) (*PanDetailsFetchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PanDetailsFetch not implemented")
}
func (UnimplementedCvlServer) InsertUpdateKycRecord(context.Context, *InsertUpdateKycRecordRequest) (*InsertUpdateKycRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InsertUpdateKycRecord not implemented")
}
func (UnimplementedCvlServer) UploadFile(context.Context, *UploadFileRequest) (*UploadFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadFile not implemented")
}
func (UnimplementedCvlServer) DownloadDir(context.Context, *DownloadDirRequest) (*DownloadDirResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadDir not implemented")
}
func (UnimplementedCvlServer) DownloadFile(context.Context, *DownloadFileRequest) (*DownloadFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadFile not implemented")
}
func (UnimplementedCvlServer) ListDirFiles(context.Context, *ListDirFilesRequest) (*ListDirFilesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDirFiles not implemented")
}
func (UnimplementedCvlServer) DownloadFileWithStream(*DownloadFileWithStreamRequest, Cvl_DownloadFileWithStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method DownloadFileWithStream not implemented")
}

// UnsafeCvlServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CvlServer will
// result in compilation errors.
type UnsafeCvlServer interface {
	mustEmbedUnimplementedCvlServer()
}

func RegisterCvlServer(s grpc.ServiceRegistrar, srv CvlServer) {
	s.RegisterService(&Cvl_ServiceDesc, srv)
}

func _Cvl_GetPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPasswordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CvlServer).GetPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cvl_GetPassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CvlServer).GetPassword(ctx, req.(*GetPasswordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cvl_GetPanStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPanStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CvlServer).GetPanStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cvl_GetPanStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CvlServer).GetPanStatus(ctx, req.(*GetPanStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cvl_PanDetailsFetch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PanDetailsFetchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CvlServer).PanDetailsFetch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cvl_PanDetailsFetch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CvlServer).PanDetailsFetch(ctx, req.(*PanDetailsFetchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cvl_InsertUpdateKycRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsertUpdateKycRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CvlServer).InsertUpdateKycRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cvl_InsertUpdateKycRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CvlServer).InsertUpdateKycRecord(ctx, req.(*InsertUpdateKycRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cvl_UploadFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CvlServer).UploadFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cvl_UploadFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CvlServer).UploadFile(ctx, req.(*UploadFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cvl_DownloadDir_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadDirRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CvlServer).DownloadDir(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cvl_DownloadDir_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CvlServer).DownloadDir(ctx, req.(*DownloadDirRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cvl_DownloadFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CvlServer).DownloadFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cvl_DownloadFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CvlServer).DownloadFile(ctx, req.(*DownloadFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cvl_ListDirFiles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDirFilesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CvlServer).ListDirFiles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cvl_ListDirFiles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CvlServer).ListDirFiles(ctx, req.(*ListDirFilesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cvl_DownloadFileWithStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(DownloadFileWithStreamRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(CvlServer).DownloadFileWithStream(m, &cvlDownloadFileWithStreamServer{stream})
}

type Cvl_DownloadFileWithStreamServer interface {
	Send(*DownloadFileWithStreamResponse) error
	grpc.ServerStream
}

type cvlDownloadFileWithStreamServer struct {
	grpc.ServerStream
}

func (x *cvlDownloadFileWithStreamServer) Send(m *DownloadFileWithStreamResponse) error {
	return x.ServerStream.SendMsg(m)
}

// Cvl_ServiceDesc is the grpc.ServiceDesc for Cvl service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Cvl_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendorgateway.wealth.cvl.Cvl",
	HandlerType: (*CvlServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPassword",
			Handler:    _Cvl_GetPassword_Handler,
		},
		{
			MethodName: "GetPanStatus",
			Handler:    _Cvl_GetPanStatus_Handler,
		},
		{
			MethodName: "PanDetailsFetch",
			Handler:    _Cvl_PanDetailsFetch_Handler,
		},
		{
			MethodName: "InsertUpdateKycRecord",
			Handler:    _Cvl_InsertUpdateKycRecord_Handler,
		},
		{
			MethodName: "UploadFile",
			Handler:    _Cvl_UploadFile_Handler,
		},
		{
			MethodName: "DownloadDir",
			Handler:    _Cvl_DownloadDir_Handler,
		},
		{
			MethodName: "DownloadFile",
			Handler:    _Cvl_DownloadFile_Handler,
		},
		{
			MethodName: "ListDirFiles",
			Handler:    _Cvl_ListDirFiles_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "DownloadFileWithStream",
			Handler:       _Cvl_DownloadFileWithStream_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "api/vendorgateway/wealth/cvl/service.proto",
}
