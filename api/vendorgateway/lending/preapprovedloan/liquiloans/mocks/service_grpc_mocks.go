// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendorgateway/lending/preapprovedloan/liquiloans/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	liquiloans "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockLiquiloansClient is a mock of LiquiloansClient interface.
type MockLiquiloansClient struct {
	ctrl     *gomock.Controller
	recorder *MockLiquiloansClientMockRecorder
}

// MockLiquiloansClientMockRecorder is the mock recorder for MockLiquiloansClient.
type MockLiquiloansClientMockRecorder struct {
	mock *MockLiquiloansClient
}

// NewMockLiquiloansClient creates a new mock instance.
func NewMockLiquiloansClient(ctrl *gomock.Controller) *MockLiquiloansClient {
	mock := &MockLiquiloansClient{ctrl: ctrl}
	mock.recorder = &MockLiquiloansClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLiquiloansClient) EXPECT() *MockLiquiloansClientMockRecorder {
	return m.recorder
}

// AddAddressDetails mocks base method.
func (m *MockLiquiloansClient) AddAddressDetails(ctx context.Context, in *liquiloans.AddAddressDetailsRequest, opts ...grpc.CallOption) (*liquiloans.AddDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddAddressDetails", varargs...)
	ret0, _ := ret[0].(*liquiloans.AddDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAddressDetails indicates an expected call of AddAddressDetails.
func (mr *MockLiquiloansClientMockRecorder) AddAddressDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAddressDetails", reflect.TypeOf((*MockLiquiloansClient)(nil).AddAddressDetails), varargs...)
}

// AddBankingDetails mocks base method.
func (m *MockLiquiloansClient) AddBankingDetails(ctx context.Context, in *liquiloans.AddBankingDetailsRequest, opts ...grpc.CallOption) (*liquiloans.AddDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddBankingDetails", varargs...)
	ret0, _ := ret[0].(*liquiloans.AddDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddBankingDetails indicates an expected call of AddBankingDetails.
func (mr *MockLiquiloansClientMockRecorder) AddBankingDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBankingDetails", reflect.TypeOf((*MockLiquiloansClient)(nil).AddBankingDetails), varargs...)
}

// AddEmploymentDetails mocks base method.
func (m *MockLiquiloansClient) AddEmploymentDetails(ctx context.Context, in *liquiloans.AddEmploymentDetailsRequest, opts ...grpc.CallOption) (*liquiloans.AddDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddEmploymentDetails", varargs...)
	ret0, _ := ret[0].(*liquiloans.AddDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddEmploymentDetails indicates an expected call of AddEmploymentDetails.
func (mr *MockLiquiloansClientMockRecorder) AddEmploymentDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddEmploymentDetails", reflect.TypeOf((*MockLiquiloansClient)(nil).AddEmploymentDetails), varargs...)
}

// AddPersonalDetails mocks base method.
func (m *MockLiquiloansClient) AddPersonalDetails(ctx context.Context, in *liquiloans.AddPersonalDetailsRequest, opts ...grpc.CallOption) (*liquiloans.AddPersonalDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPersonalDetails", varargs...)
	ret0, _ := ret[0].(*liquiloans.AddPersonalDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPersonalDetails indicates an expected call of AddPersonalDetails.
func (mr *MockLiquiloansClientMockRecorder) AddPersonalDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPersonalDetails", reflect.TypeOf((*MockLiquiloansClient)(nil).AddPersonalDetails), varargs...)
}

// ApplicantLookup mocks base method.
func (m *MockLiquiloansClient) ApplicantLookup(ctx context.Context, in *liquiloans.ApplicantLookupRequest, opts ...grpc.CallOption) (*liquiloans.ApplicantLookupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplicantLookup", varargs...)
	ret0, _ := ret[0].(*liquiloans.ApplicantLookupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplicantLookup indicates an expected call of ApplicantLookup.
func (mr *MockLiquiloansClientMockRecorder) ApplicantLookup(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplicantLookup", reflect.TypeOf((*MockLiquiloansClient)(nil).ApplicantLookup), varargs...)
}

// CancelLead mocks base method.
func (m *MockLiquiloansClient) CancelLead(ctx context.Context, in *liquiloans.CancelLeadRequest, opts ...grpc.CallOption) (*liquiloans.CancelLeadResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelLead", varargs...)
	ret0, _ := ret[0].(*liquiloans.CancelLeadResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelLead indicates an expected call of CancelLead.
func (mr *MockLiquiloansClientMockRecorder) CancelLead(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelLead", reflect.TypeOf((*MockLiquiloansClient)(nil).CancelLead), varargs...)
}

// CaptchaGenerationForOkyc mocks base method.
func (m *MockLiquiloansClient) CaptchaGenerationForOkyc(ctx context.Context, in *liquiloans.CaptchaGenerationForOkycRequest, opts ...grpc.CallOption) (*liquiloans.CaptchaGenerationForOkycResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CaptchaGenerationForOkyc", varargs...)
	ret0, _ := ret[0].(*liquiloans.CaptchaGenerationForOkycResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CaptchaGenerationForOkyc indicates an expected call of CaptchaGenerationForOkyc.
func (mr *MockLiquiloansClientMockRecorder) CaptchaGenerationForOkyc(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CaptchaGenerationForOkyc", reflect.TypeOf((*MockLiquiloansClient)(nil).CaptchaGenerationForOkyc), varargs...)
}

// CreateRepaymentSchedule mocks base method.
func (m *MockLiquiloansClient) CreateRepaymentSchedule(ctx context.Context, in *liquiloans.CreateRepaymentScheduleRequest, opts ...grpc.CallOption) (*liquiloans.CreateRepaymentScheduleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateRepaymentSchedule", varargs...)
	ret0, _ := ret[0].(*liquiloans.CreateRepaymentScheduleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRepaymentSchedule indicates an expected call of CreateRepaymentSchedule.
func (mr *MockLiquiloansClientMockRecorder) CreateRepaymentSchedule(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRepaymentSchedule", reflect.TypeOf((*MockLiquiloansClient)(nil).CreateRepaymentSchedule), varargs...)
}

// ForeClosureDetails mocks base method.
func (m *MockLiquiloansClient) ForeClosureDetails(ctx context.Context, in *liquiloans.ForeClosureDetailsRequest, opts ...grpc.CallOption) (*liquiloans.ForeClosureDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ForeClosureDetails", varargs...)
	ret0, _ := ret[0].(*liquiloans.ForeClosureDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ForeClosureDetails indicates an expected call of ForeClosureDetails.
func (mr *MockLiquiloansClientMockRecorder) ForeClosureDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ForeClosureDetails", reflect.TypeOf((*MockLiquiloansClient)(nil).ForeClosureDetails), varargs...)
}

// GenerateOtpForOkyc mocks base method.
func (m *MockLiquiloansClient) GenerateOtpForOkyc(ctx context.Context, in *liquiloans.GenerateOtpForOkycRequest, opts ...grpc.CallOption) (*liquiloans.GenerateOtpForOkycResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateOtpForOkyc", varargs...)
	ret0, _ := ret[0].(*liquiloans.GenerateOtpForOkycResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateOtpForOkyc indicates an expected call of GenerateOtpForOkyc.
func (mr *MockLiquiloansClientMockRecorder) GenerateOtpForOkyc(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateOtpForOkyc", reflect.TypeOf((*MockLiquiloansClient)(nil).GenerateOtpForOkyc), varargs...)
}

// GetApplicantStatus mocks base method.
func (m *MockLiquiloansClient) GetApplicantStatus(ctx context.Context, in *liquiloans.GetApplicantStatusRequest, opts ...grpc.CallOption) (*liquiloans.GetApplicantStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetApplicantStatus", varargs...)
	ret0, _ := ret[0].(*liquiloans.GetApplicantStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplicantStatus indicates an expected call of GetApplicantStatus.
func (mr *MockLiquiloansClientMockRecorder) GetApplicantStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplicantStatus", reflect.TypeOf((*MockLiquiloansClient)(nil).GetApplicantStatus), varargs...)
}

// GetApplicationSoa mocks base method.
func (m *MockLiquiloansClient) GetApplicationSoa(ctx context.Context, in *liquiloans.GetApplicationSoaRequest, opts ...grpc.CallOption) (*liquiloans.GetApplicationSoaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetApplicationSoa", varargs...)
	ret0, _ := ret[0].(*liquiloans.GetApplicationSoaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplicationSoa indicates an expected call of GetApplicationSoa.
func (mr *MockLiquiloansClientMockRecorder) GetApplicationSoa(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplicationSoa", reflect.TypeOf((*MockLiquiloansClient)(nil).GetApplicationSoa), varargs...)
}

// GetCreditLineSchemes mocks base method.
func (m *MockLiquiloansClient) GetCreditLineSchemes(ctx context.Context, in *liquiloans.GetCreditLineSchemesRequest, opts ...grpc.CallOption) (*liquiloans.GetCreditLineSchemesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCreditLineSchemes", varargs...)
	ret0, _ := ret[0].(*liquiloans.GetCreditLineSchemesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCreditLineSchemes indicates an expected call of GetCreditLineSchemes.
func (mr *MockLiquiloansClientMockRecorder) GetCreditLineSchemes(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCreditLineSchemes", reflect.TypeOf((*MockLiquiloansClient)(nil).GetCreditLineSchemes), varargs...)
}

// GetLoanStatus mocks base method.
func (m *MockLiquiloansClient) GetLoanStatus(ctx context.Context, in *liquiloans.GetLoanStatusRequest, opts ...grpc.CallOption) (*liquiloans.GetLoanStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoanStatus", varargs...)
	ret0, _ := ret[0].(*liquiloans.GetLoanStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanStatus indicates an expected call of GetLoanStatus.
func (mr *MockLiquiloansClientMockRecorder) GetLoanStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanStatus", reflect.TypeOf((*MockLiquiloansClient)(nil).GetLoanStatus), varargs...)
}

// GetMandateLink mocks base method.
func (m *MockLiquiloansClient) GetMandateLink(ctx context.Context, in *liquiloans.GetMandateLinkRequest, opts ...grpc.CallOption) (*liquiloans.GetMandateLinkResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMandateLink", varargs...)
	ret0, _ := ret[0].(*liquiloans.GetMandateLinkResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMandateLink indicates an expected call of GetMandateLink.
func (mr *MockLiquiloansClientMockRecorder) GetMandateLink(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMandateLink", reflect.TypeOf((*MockLiquiloansClient)(nil).GetMandateLink), varargs...)
}

// GetMandateStatus mocks base method.
func (m *MockLiquiloansClient) GetMandateStatus(ctx context.Context, in *liquiloans.GetMandateStatusRequest, opts ...grpc.CallOption) (*liquiloans.GetMandateStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMandateStatus", varargs...)
	ret0, _ := ret[0].(*liquiloans.GetMandateStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMandateStatus indicates an expected call of GetMandateStatus.
func (mr *MockLiquiloansClientMockRecorder) GetMandateStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMandateStatus", reflect.TypeOf((*MockLiquiloansClient)(nil).GetMandateStatus), varargs...)
}

// GetPdfAgreement mocks base method.
func (m *MockLiquiloansClient) GetPdfAgreement(ctx context.Context, in *liquiloans.GetPdfAgreementRequest, opts ...grpc.CallOption) (*liquiloans.GetPdfAgreementResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPdfAgreement", varargs...)
	ret0, _ := ret[0].(*liquiloans.GetPdfAgreementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPdfAgreement indicates an expected call of GetPdfAgreement.
func (mr *MockLiquiloansClientMockRecorder) GetPdfAgreement(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPdfAgreement", reflect.TypeOf((*MockLiquiloansClient)(nil).GetPdfAgreement), varargs...)
}

// GetRepaymentSchedule mocks base method.
func (m *MockLiquiloansClient) GetRepaymentSchedule(ctx context.Context, in *liquiloans.GetRepaymentScheduleRequest, opts ...grpc.CallOption) (*liquiloans.GetRepaymentScheduleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRepaymentSchedule", varargs...)
	ret0, _ := ret[0].(*liquiloans.GetRepaymentScheduleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRepaymentSchedule indicates an expected call of GetRepaymentSchedule.
func (mr *MockLiquiloansClientMockRecorder) GetRepaymentSchedule(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRepaymentSchedule", reflect.TypeOf((*MockLiquiloansClient)(nil).GetRepaymentSchedule), varargs...)
}

// HashGenerationForOkyc mocks base method.
func (m *MockLiquiloansClient) HashGenerationForOkyc(ctx context.Context, in *liquiloans.HashGenerationForOkycRequest, opts ...grpc.CallOption) (*liquiloans.HashGenerationForOkycResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HashGenerationForOkyc", varargs...)
	ret0, _ := ret[0].(*liquiloans.HashGenerationForOkycResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HashGenerationForOkyc indicates an expected call of HashGenerationForOkyc.
func (mr *MockLiquiloansClientMockRecorder) HashGenerationForOkyc(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HashGenerationForOkyc", reflect.TypeOf((*MockLiquiloansClient)(nil).HashGenerationForOkyc), varargs...)
}

// MakeDrawdown mocks base method.
func (m *MockLiquiloansClient) MakeDrawdown(ctx context.Context, in *liquiloans.MakeDrawdownRequest, opts ...grpc.CallOption) (*liquiloans.MakeDrawdownResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MakeDrawdown", varargs...)
	ret0, _ := ret[0].(*liquiloans.MakeDrawdownResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeDrawdown indicates an expected call of MakeDrawdown.
func (mr *MockLiquiloansClientMockRecorder) MakeDrawdown(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeDrawdown", reflect.TypeOf((*MockLiquiloansClient)(nil).MakeDrawdown), varargs...)
}

// SaveCharges mocks base method.
func (m *MockLiquiloansClient) SaveCharges(ctx context.Context, in *liquiloans.SaveChargesRequest, opts ...grpc.CallOption) (*liquiloans.SaveChargesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveCharges", varargs...)
	ret0, _ := ret[0].(*liquiloans.SaveChargesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveCharges indicates an expected call of SaveCharges.
func (mr *MockLiquiloansClientMockRecorder) SaveCharges(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveCharges", reflect.TypeOf((*MockLiquiloansClient)(nil).SaveCharges), varargs...)
}

// SaveCollection mocks base method.
func (m *MockLiquiloansClient) SaveCollection(ctx context.Context, in *liquiloans.SaveCollectionRequest, opts ...grpc.CallOption) (*liquiloans.SaveCollectionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveCollection", varargs...)
	ret0, _ := ret[0].(*liquiloans.SaveCollectionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveCollection indicates an expected call of SaveCollection.
func (mr *MockLiquiloansClientMockRecorder) SaveCollection(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveCollection", reflect.TypeOf((*MockLiquiloansClient)(nil).SaveCollection), varargs...)
}

// SendBorrowerAgreementOtp mocks base method.
func (m *MockLiquiloansClient) SendBorrowerAgreementOtp(ctx context.Context, in *liquiloans.SendBorrowerAgreementOtpRequest, opts ...grpc.CallOption) (*liquiloans.SendBorrowerAgreementOtpResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendBorrowerAgreementOtp", varargs...)
	ret0, _ := ret[0].(*liquiloans.SendBorrowerAgreementOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendBorrowerAgreementOtp indicates an expected call of SendBorrowerAgreementOtp.
func (mr *MockLiquiloansClientMockRecorder) SendBorrowerAgreementOtp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendBorrowerAgreementOtp", reflect.TypeOf((*MockLiquiloansClient)(nil).SendBorrowerAgreementOtp), varargs...)
}

// UpdateApplicantUdf mocks base method.
func (m *MockLiquiloansClient) UpdateApplicantUdf(ctx context.Context, in *liquiloans.UpdateApplicantUdfRequest, opts ...grpc.CallOption) (*liquiloans.UpdateApplicantUdfResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateApplicantUdf", varargs...)
	ret0, _ := ret[0].(*liquiloans.UpdateApplicantUdfResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateApplicantUdf indicates an expected call of UpdateApplicantUdf.
func (mr *MockLiquiloansClientMockRecorder) UpdateApplicantUdf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateApplicantUdf", reflect.TypeOf((*MockLiquiloansClient)(nil).UpdateApplicantUdf), varargs...)
}

// UpdateLead mocks base method.
func (m *MockLiquiloansClient) UpdateLead(ctx context.Context, in *liquiloans.UpdateLeadRequest, opts ...grpc.CallOption) (*liquiloans.UpdateLeadResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateLead", varargs...)
	ret0, _ := ret[0].(*liquiloans.UpdateLeadResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateLead indicates an expected call of UpdateLead.
func (mr *MockLiquiloansClientMockRecorder) UpdateLead(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLead", reflect.TypeOf((*MockLiquiloansClient)(nil).UpdateLead), varargs...)
}

// UploadDocument mocks base method.
func (m *MockLiquiloansClient) UploadDocument(ctx context.Context, in *liquiloans.UploadDocumentRequest, opts ...grpc.CallOption) (*liquiloans.UploadDocumentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UploadDocument", varargs...)
	ret0, _ := ret[0].(*liquiloans.UploadDocumentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadDocument indicates an expected call of UploadDocument.
func (mr *MockLiquiloansClientMockRecorder) UploadDocument(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadDocument", reflect.TypeOf((*MockLiquiloansClient)(nil).UploadDocument), varargs...)
}

// ValidateOtpForOkyc mocks base method.
func (m *MockLiquiloansClient) ValidateOtpForOkyc(ctx context.Context, in *liquiloans.ValidateOtpForOkycRequest, opts ...grpc.CallOption) (*liquiloans.ValidateOtpForOkycResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ValidateOtpForOkyc", varargs...)
	ret0, _ := ret[0].(*liquiloans.ValidateOtpForOkycResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateOtpForOkyc indicates an expected call of ValidateOtpForOkyc.
func (mr *MockLiquiloansClientMockRecorder) ValidateOtpForOkyc(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateOtpForOkyc", reflect.TypeOf((*MockLiquiloansClient)(nil).ValidateOtpForOkyc), varargs...)
}

// VerifyAndDownloadCkyc mocks base method.
func (m *MockLiquiloansClient) VerifyAndDownloadCkyc(ctx context.Context, in *liquiloans.VerifyAndDownloadCkycRequest, opts ...grpc.CallOption) (*liquiloans.VerifyAndDownloadCkycResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyAndDownloadCkyc", varargs...)
	ret0, _ := ret[0].(*liquiloans.VerifyAndDownloadCkycResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyAndDownloadCkyc indicates an expected call of VerifyAndDownloadCkyc.
func (mr *MockLiquiloansClientMockRecorder) VerifyAndDownloadCkyc(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyAndDownloadCkyc", reflect.TypeOf((*MockLiquiloansClient)(nil).VerifyAndDownloadCkyc), varargs...)
}

// VerifyBorrowerAgreementOtp mocks base method.
func (m *MockLiquiloansClient) VerifyBorrowerAgreementOtp(ctx context.Context, in *liquiloans.VerifyBorrowerAgreementOtpRequest, opts ...grpc.CallOption) (*liquiloans.VerifyBorrowerAgreementOtpResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyBorrowerAgreementOtp", varargs...)
	ret0, _ := ret[0].(*liquiloans.VerifyBorrowerAgreementOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyBorrowerAgreementOtp indicates an expected call of VerifyBorrowerAgreementOtp.
func (mr *MockLiquiloansClientMockRecorder) VerifyBorrowerAgreementOtp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyBorrowerAgreementOtp", reflect.TypeOf((*MockLiquiloansClient)(nil).VerifyBorrowerAgreementOtp), varargs...)
}

// MockLiquiloansServer is a mock of LiquiloansServer interface.
type MockLiquiloansServer struct {
	ctrl     *gomock.Controller
	recorder *MockLiquiloansServerMockRecorder
}

// MockLiquiloansServerMockRecorder is the mock recorder for MockLiquiloansServer.
type MockLiquiloansServerMockRecorder struct {
	mock *MockLiquiloansServer
}

// NewMockLiquiloansServer creates a new mock instance.
func NewMockLiquiloansServer(ctrl *gomock.Controller) *MockLiquiloansServer {
	mock := &MockLiquiloansServer{ctrl: ctrl}
	mock.recorder = &MockLiquiloansServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLiquiloansServer) EXPECT() *MockLiquiloansServerMockRecorder {
	return m.recorder
}

// AddAddressDetails mocks base method.
func (m *MockLiquiloansServer) AddAddressDetails(arg0 context.Context, arg1 *liquiloans.AddAddressDetailsRequest) (*liquiloans.AddDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAddressDetails", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.AddDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAddressDetails indicates an expected call of AddAddressDetails.
func (mr *MockLiquiloansServerMockRecorder) AddAddressDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAddressDetails", reflect.TypeOf((*MockLiquiloansServer)(nil).AddAddressDetails), arg0, arg1)
}

// AddBankingDetails mocks base method.
func (m *MockLiquiloansServer) AddBankingDetails(arg0 context.Context, arg1 *liquiloans.AddBankingDetailsRequest) (*liquiloans.AddDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddBankingDetails", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.AddDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddBankingDetails indicates an expected call of AddBankingDetails.
func (mr *MockLiquiloansServerMockRecorder) AddBankingDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBankingDetails", reflect.TypeOf((*MockLiquiloansServer)(nil).AddBankingDetails), arg0, arg1)
}

// AddEmploymentDetails mocks base method.
func (m *MockLiquiloansServer) AddEmploymentDetails(arg0 context.Context, arg1 *liquiloans.AddEmploymentDetailsRequest) (*liquiloans.AddDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddEmploymentDetails", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.AddDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddEmploymentDetails indicates an expected call of AddEmploymentDetails.
func (mr *MockLiquiloansServerMockRecorder) AddEmploymentDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddEmploymentDetails", reflect.TypeOf((*MockLiquiloansServer)(nil).AddEmploymentDetails), arg0, arg1)
}

// AddPersonalDetails mocks base method.
func (m *MockLiquiloansServer) AddPersonalDetails(arg0 context.Context, arg1 *liquiloans.AddPersonalDetailsRequest) (*liquiloans.AddPersonalDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPersonalDetails", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.AddPersonalDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPersonalDetails indicates an expected call of AddPersonalDetails.
func (mr *MockLiquiloansServerMockRecorder) AddPersonalDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPersonalDetails", reflect.TypeOf((*MockLiquiloansServer)(nil).AddPersonalDetails), arg0, arg1)
}

// ApplicantLookup mocks base method.
func (m *MockLiquiloansServer) ApplicantLookup(arg0 context.Context, arg1 *liquiloans.ApplicantLookupRequest) (*liquiloans.ApplicantLookupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplicantLookup", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.ApplicantLookupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplicantLookup indicates an expected call of ApplicantLookup.
func (mr *MockLiquiloansServerMockRecorder) ApplicantLookup(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplicantLookup", reflect.TypeOf((*MockLiquiloansServer)(nil).ApplicantLookup), arg0, arg1)
}

// CancelLead mocks base method.
func (m *MockLiquiloansServer) CancelLead(arg0 context.Context, arg1 *liquiloans.CancelLeadRequest) (*liquiloans.CancelLeadResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelLead", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.CancelLeadResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelLead indicates an expected call of CancelLead.
func (mr *MockLiquiloansServerMockRecorder) CancelLead(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelLead", reflect.TypeOf((*MockLiquiloansServer)(nil).CancelLead), arg0, arg1)
}

// CaptchaGenerationForOkyc mocks base method.
func (m *MockLiquiloansServer) CaptchaGenerationForOkyc(arg0 context.Context, arg1 *liquiloans.CaptchaGenerationForOkycRequest) (*liquiloans.CaptchaGenerationForOkycResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CaptchaGenerationForOkyc", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.CaptchaGenerationForOkycResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CaptchaGenerationForOkyc indicates an expected call of CaptchaGenerationForOkyc.
func (mr *MockLiquiloansServerMockRecorder) CaptchaGenerationForOkyc(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CaptchaGenerationForOkyc", reflect.TypeOf((*MockLiquiloansServer)(nil).CaptchaGenerationForOkyc), arg0, arg1)
}

// CreateRepaymentSchedule mocks base method.
func (m *MockLiquiloansServer) CreateRepaymentSchedule(arg0 context.Context, arg1 *liquiloans.CreateRepaymentScheduleRequest) (*liquiloans.CreateRepaymentScheduleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRepaymentSchedule", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.CreateRepaymentScheduleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRepaymentSchedule indicates an expected call of CreateRepaymentSchedule.
func (mr *MockLiquiloansServerMockRecorder) CreateRepaymentSchedule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRepaymentSchedule", reflect.TypeOf((*MockLiquiloansServer)(nil).CreateRepaymentSchedule), arg0, arg1)
}

// ForeClosureDetails mocks base method.
func (m *MockLiquiloansServer) ForeClosureDetails(arg0 context.Context, arg1 *liquiloans.ForeClosureDetailsRequest) (*liquiloans.ForeClosureDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ForeClosureDetails", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.ForeClosureDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ForeClosureDetails indicates an expected call of ForeClosureDetails.
func (mr *MockLiquiloansServerMockRecorder) ForeClosureDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ForeClosureDetails", reflect.TypeOf((*MockLiquiloansServer)(nil).ForeClosureDetails), arg0, arg1)
}

// GenerateOtpForOkyc mocks base method.
func (m *MockLiquiloansServer) GenerateOtpForOkyc(arg0 context.Context, arg1 *liquiloans.GenerateOtpForOkycRequest) (*liquiloans.GenerateOtpForOkycResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateOtpForOkyc", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.GenerateOtpForOkycResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateOtpForOkyc indicates an expected call of GenerateOtpForOkyc.
func (mr *MockLiquiloansServerMockRecorder) GenerateOtpForOkyc(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateOtpForOkyc", reflect.TypeOf((*MockLiquiloansServer)(nil).GenerateOtpForOkyc), arg0, arg1)
}

// GetApplicantStatus mocks base method.
func (m *MockLiquiloansServer) GetApplicantStatus(arg0 context.Context, arg1 *liquiloans.GetApplicantStatusRequest) (*liquiloans.GetApplicantStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApplicantStatus", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.GetApplicantStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplicantStatus indicates an expected call of GetApplicantStatus.
func (mr *MockLiquiloansServerMockRecorder) GetApplicantStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplicantStatus", reflect.TypeOf((*MockLiquiloansServer)(nil).GetApplicantStatus), arg0, arg1)
}

// GetApplicationSoa mocks base method.
func (m *MockLiquiloansServer) GetApplicationSoa(arg0 context.Context, arg1 *liquiloans.GetApplicationSoaRequest) (*liquiloans.GetApplicationSoaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApplicationSoa", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.GetApplicationSoaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplicationSoa indicates an expected call of GetApplicationSoa.
func (mr *MockLiquiloansServerMockRecorder) GetApplicationSoa(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplicationSoa", reflect.TypeOf((*MockLiquiloansServer)(nil).GetApplicationSoa), arg0, arg1)
}

// GetCreditLineSchemes mocks base method.
func (m *MockLiquiloansServer) GetCreditLineSchemes(arg0 context.Context, arg1 *liquiloans.GetCreditLineSchemesRequest) (*liquiloans.GetCreditLineSchemesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCreditLineSchemes", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.GetCreditLineSchemesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCreditLineSchemes indicates an expected call of GetCreditLineSchemes.
func (mr *MockLiquiloansServerMockRecorder) GetCreditLineSchemes(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCreditLineSchemes", reflect.TypeOf((*MockLiquiloansServer)(nil).GetCreditLineSchemes), arg0, arg1)
}

// GetLoanStatus mocks base method.
func (m *MockLiquiloansServer) GetLoanStatus(arg0 context.Context, arg1 *liquiloans.GetLoanStatusRequest) (*liquiloans.GetLoanStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoanStatus", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.GetLoanStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanStatus indicates an expected call of GetLoanStatus.
func (mr *MockLiquiloansServerMockRecorder) GetLoanStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanStatus", reflect.TypeOf((*MockLiquiloansServer)(nil).GetLoanStatus), arg0, arg1)
}

// GetMandateLink mocks base method.
func (m *MockLiquiloansServer) GetMandateLink(arg0 context.Context, arg1 *liquiloans.GetMandateLinkRequest) (*liquiloans.GetMandateLinkResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMandateLink", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.GetMandateLinkResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMandateLink indicates an expected call of GetMandateLink.
func (mr *MockLiquiloansServerMockRecorder) GetMandateLink(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMandateLink", reflect.TypeOf((*MockLiquiloansServer)(nil).GetMandateLink), arg0, arg1)
}

// GetMandateStatus mocks base method.
func (m *MockLiquiloansServer) GetMandateStatus(arg0 context.Context, arg1 *liquiloans.GetMandateStatusRequest) (*liquiloans.GetMandateStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMandateStatus", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.GetMandateStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMandateStatus indicates an expected call of GetMandateStatus.
func (mr *MockLiquiloansServerMockRecorder) GetMandateStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMandateStatus", reflect.TypeOf((*MockLiquiloansServer)(nil).GetMandateStatus), arg0, arg1)
}

// GetPdfAgreement mocks base method.
func (m *MockLiquiloansServer) GetPdfAgreement(arg0 context.Context, arg1 *liquiloans.GetPdfAgreementRequest) (*liquiloans.GetPdfAgreementResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPdfAgreement", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.GetPdfAgreementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPdfAgreement indicates an expected call of GetPdfAgreement.
func (mr *MockLiquiloansServerMockRecorder) GetPdfAgreement(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPdfAgreement", reflect.TypeOf((*MockLiquiloansServer)(nil).GetPdfAgreement), arg0, arg1)
}

// GetRepaymentSchedule mocks base method.
func (m *MockLiquiloansServer) GetRepaymentSchedule(arg0 context.Context, arg1 *liquiloans.GetRepaymentScheduleRequest) (*liquiloans.GetRepaymentScheduleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRepaymentSchedule", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.GetRepaymentScheduleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRepaymentSchedule indicates an expected call of GetRepaymentSchedule.
func (mr *MockLiquiloansServerMockRecorder) GetRepaymentSchedule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRepaymentSchedule", reflect.TypeOf((*MockLiquiloansServer)(nil).GetRepaymentSchedule), arg0, arg1)
}

// HashGenerationForOkyc mocks base method.
func (m *MockLiquiloansServer) HashGenerationForOkyc(arg0 context.Context, arg1 *liquiloans.HashGenerationForOkycRequest) (*liquiloans.HashGenerationForOkycResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HashGenerationForOkyc", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.HashGenerationForOkycResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HashGenerationForOkyc indicates an expected call of HashGenerationForOkyc.
func (mr *MockLiquiloansServerMockRecorder) HashGenerationForOkyc(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HashGenerationForOkyc", reflect.TypeOf((*MockLiquiloansServer)(nil).HashGenerationForOkyc), arg0, arg1)
}

// MakeDrawdown mocks base method.
func (m *MockLiquiloansServer) MakeDrawdown(arg0 context.Context, arg1 *liquiloans.MakeDrawdownRequest) (*liquiloans.MakeDrawdownResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeDrawdown", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.MakeDrawdownResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeDrawdown indicates an expected call of MakeDrawdown.
func (mr *MockLiquiloansServerMockRecorder) MakeDrawdown(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeDrawdown", reflect.TypeOf((*MockLiquiloansServer)(nil).MakeDrawdown), arg0, arg1)
}

// SaveCharges mocks base method.
func (m *MockLiquiloansServer) SaveCharges(arg0 context.Context, arg1 *liquiloans.SaveChargesRequest) (*liquiloans.SaveChargesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveCharges", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.SaveChargesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveCharges indicates an expected call of SaveCharges.
func (mr *MockLiquiloansServerMockRecorder) SaveCharges(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveCharges", reflect.TypeOf((*MockLiquiloansServer)(nil).SaveCharges), arg0, arg1)
}

// SaveCollection mocks base method.
func (m *MockLiquiloansServer) SaveCollection(arg0 context.Context, arg1 *liquiloans.SaveCollectionRequest) (*liquiloans.SaveCollectionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveCollection", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.SaveCollectionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveCollection indicates an expected call of SaveCollection.
func (mr *MockLiquiloansServerMockRecorder) SaveCollection(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveCollection", reflect.TypeOf((*MockLiquiloansServer)(nil).SaveCollection), arg0, arg1)
}

// SendBorrowerAgreementOtp mocks base method.
func (m *MockLiquiloansServer) SendBorrowerAgreementOtp(arg0 context.Context, arg1 *liquiloans.SendBorrowerAgreementOtpRequest) (*liquiloans.SendBorrowerAgreementOtpResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendBorrowerAgreementOtp", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.SendBorrowerAgreementOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendBorrowerAgreementOtp indicates an expected call of SendBorrowerAgreementOtp.
func (mr *MockLiquiloansServerMockRecorder) SendBorrowerAgreementOtp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendBorrowerAgreementOtp", reflect.TypeOf((*MockLiquiloansServer)(nil).SendBorrowerAgreementOtp), arg0, arg1)
}

// UpdateApplicantUdf mocks base method.
func (m *MockLiquiloansServer) UpdateApplicantUdf(arg0 context.Context, arg1 *liquiloans.UpdateApplicantUdfRequest) (*liquiloans.UpdateApplicantUdfResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateApplicantUdf", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.UpdateApplicantUdfResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateApplicantUdf indicates an expected call of UpdateApplicantUdf.
func (mr *MockLiquiloansServerMockRecorder) UpdateApplicantUdf(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateApplicantUdf", reflect.TypeOf((*MockLiquiloansServer)(nil).UpdateApplicantUdf), arg0, arg1)
}

// UpdateLead mocks base method.
func (m *MockLiquiloansServer) UpdateLead(arg0 context.Context, arg1 *liquiloans.UpdateLeadRequest) (*liquiloans.UpdateLeadResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLead", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.UpdateLeadResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateLead indicates an expected call of UpdateLead.
func (mr *MockLiquiloansServerMockRecorder) UpdateLead(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLead", reflect.TypeOf((*MockLiquiloansServer)(nil).UpdateLead), arg0, arg1)
}

// UploadDocument mocks base method.
func (m *MockLiquiloansServer) UploadDocument(arg0 context.Context, arg1 *liquiloans.UploadDocumentRequest) (*liquiloans.UploadDocumentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadDocument", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.UploadDocumentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadDocument indicates an expected call of UploadDocument.
func (mr *MockLiquiloansServerMockRecorder) UploadDocument(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadDocument", reflect.TypeOf((*MockLiquiloansServer)(nil).UploadDocument), arg0, arg1)
}

// ValidateOtpForOkyc mocks base method.
func (m *MockLiquiloansServer) ValidateOtpForOkyc(arg0 context.Context, arg1 *liquiloans.ValidateOtpForOkycRequest) (*liquiloans.ValidateOtpForOkycResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateOtpForOkyc", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.ValidateOtpForOkycResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateOtpForOkyc indicates an expected call of ValidateOtpForOkyc.
func (mr *MockLiquiloansServerMockRecorder) ValidateOtpForOkyc(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateOtpForOkyc", reflect.TypeOf((*MockLiquiloansServer)(nil).ValidateOtpForOkyc), arg0, arg1)
}

// VerifyAndDownloadCkyc mocks base method.
func (m *MockLiquiloansServer) VerifyAndDownloadCkyc(arg0 context.Context, arg1 *liquiloans.VerifyAndDownloadCkycRequest) (*liquiloans.VerifyAndDownloadCkycResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyAndDownloadCkyc", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.VerifyAndDownloadCkycResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyAndDownloadCkyc indicates an expected call of VerifyAndDownloadCkyc.
func (mr *MockLiquiloansServerMockRecorder) VerifyAndDownloadCkyc(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyAndDownloadCkyc", reflect.TypeOf((*MockLiquiloansServer)(nil).VerifyAndDownloadCkyc), arg0, arg1)
}

// VerifyBorrowerAgreementOtp mocks base method.
func (m *MockLiquiloansServer) VerifyBorrowerAgreementOtp(arg0 context.Context, arg1 *liquiloans.VerifyBorrowerAgreementOtpRequest) (*liquiloans.VerifyBorrowerAgreementOtpResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyBorrowerAgreementOtp", arg0, arg1)
	ret0, _ := ret[0].(*liquiloans.VerifyBorrowerAgreementOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyBorrowerAgreementOtp indicates an expected call of VerifyBorrowerAgreementOtp.
func (mr *MockLiquiloansServerMockRecorder) VerifyBorrowerAgreementOtp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyBorrowerAgreementOtp", reflect.TypeOf((*MockLiquiloansServer)(nil).VerifyBorrowerAgreementOtp), arg0, arg1)
}

// MockUnsafeLiquiloansServer is a mock of UnsafeLiquiloansServer interface.
type MockUnsafeLiquiloansServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeLiquiloansServerMockRecorder
}

// MockUnsafeLiquiloansServerMockRecorder is the mock recorder for MockUnsafeLiquiloansServer.
type MockUnsafeLiquiloansServerMockRecorder struct {
	mock *MockUnsafeLiquiloansServer
}

// NewMockUnsafeLiquiloansServer creates a new mock instance.
func NewMockUnsafeLiquiloansServer(ctrl *gomock.Controller) *MockUnsafeLiquiloansServer {
	mock := &MockUnsafeLiquiloansServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeLiquiloansServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeLiquiloansServer) EXPECT() *MockUnsafeLiquiloansServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedLiquiloansServer mocks base method.
func (m *MockUnsafeLiquiloansServer) mustEmbedUnimplementedLiquiloansServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedLiquiloansServer")
}

// mustEmbedUnimplementedLiquiloansServer indicates an expected call of mustEmbedUnimplementedLiquiloansServer.
func (mr *MockUnsafeLiquiloansServerMockRecorder) mustEmbedUnimplementedLiquiloansServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedLiquiloansServer", reflect.TypeOf((*MockUnsafeLiquiloansServer)(nil).mustEmbedUnimplementedLiquiloansServer))
}
