// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendorgateway/lending/securedloans/fiftyfin/service.proto

package fiftyfin

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	FiftyFin_UserSignup_FullMethodName                      = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/UserSignup"
	FiftyFin_FetchUser_FullMethodName                       = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/FetchUser"
	FiftyFin_UpdateUser_FullMethodName                      = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/UpdateUser"
	FiftyFin_CheckPan_FullMethodName                        = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/CheckPan"
	FiftyFin_LinkPan_FullMethodName                         = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/LinkPan"
	FiftyFin_GenerateKarvyOtp_FullMethodName                = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/GenerateKarvyOtp"
	FiftyFin_ValidateKarvyOtp_FullMethodName                = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/ValidateKarvyOtp"
	FiftyFin_GenerateCamsOtp_FullMethodName                 = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/GenerateCamsOtp"
	FiftyFin_ValidateCamsOtp_FullMethodName                 = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/ValidateCamsOtp"
	FiftyFin_FetchMfPortfolio_FullMethodName                = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/FetchMfPortfolio"
	FiftyFin_CreateLienKarvy_FullMethodName                 = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/CreateLienKarvy"
	FiftyFin_ConfirmLienKarvy_FullMethodName                = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/ConfirmLienKarvy"
	FiftyFin_CreateLienCams_FullMethodName                  = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/CreateLienCams"
	FiftyFin_ConfirmLienCams_FullMethodName                 = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/ConfirmLienCams"
	FiftyFin_FetchLoanPortfolio_FullMethodName              = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/FetchLoanPortfolio"
	FiftyFin_InitiateKycProcess_FullMethodName              = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/InitiateKycProcess"
	FiftyFin_FetchTentativeRepaymentTimeline_FullMethodName = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/FetchTentativeRepaymentTimeline"
	FiftyFin_FetchBankAccountDetails_FullMethodName         = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/FetchBankAccountDetails"
	FiftyFin_LinkBankAccount_FullMethodName                 = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/LinkBankAccount"
	FiftyFin_InitiateLoan_FullMethodName                    = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/InitiateLoan"
	FiftyFin_FetchExistingLoanDetails_FullMethodName        = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/FetchExistingLoanDetails"
	FiftyFin_FetchIndividualLoanDetails_FullMethodName      = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/FetchIndividualLoanDetails"
	FiftyFin_CreateLoan_FullMethodName                      = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/CreateLoan"
	FiftyFin_GetBankDetailsForRepayment_FullMethodName      = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/GetBankDetailsForRepayment"
	FiftyFin_CloseLoan_FullMethodName                       = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/CloseLoan"
	FiftyFin_InitiateLoanPartPayment_FullMethodName         = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/InitiateLoanPartPayment"
	FiftyFin_GetLoanHoldingStatement_FullMethodName         = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/GetLoanHoldingStatement"
	FiftyFin_GetLoanForeclosureStatement_FullMethodName     = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/GetLoanForeclosureStatement"
	FiftyFin_GetLoanSoaStatement_FullMethodName             = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/GetLoanSoaStatement"
	FiftyFin_GetLoanSoaStatementV2_FullMethodName           = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/GetLoanSoaStatementV2"
	FiftyFin_AddDeviceDetails_FullMethodName                = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/AddDeviceDetails"
	FiftyFin_AddAdditionalKycDetails_FullMethodName         = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/AddAdditionalKycDetails"
	FiftyFin_FetchLoansNotifications_FullMethodName         = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/FetchLoansNotifications"
	FiftyFin_VoidLoan_FullMethodName                        = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/VoidLoan"
	FiftyFin_UpdateUserBulk_FullMethodName                  = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/UpdateUserBulk"
	FiftyFin_LoanEligibilityCheck_FullMethodName            = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/LoanEligibilityCheck"
	FiftyFin_RegenerateLoanDocumentLink_FullMethodName      = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/RegenerateLoanDocumentLink"
	FiftyFin_DedupeCheck_FullMethodName                     = "/vendorgateway.lending.securedloans.fiftyfin.FiftyFin/DedupeCheck"
)

// FiftyFinClient is the client API for FiftyFin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FiftyFinClient interface {
	UserSignup(ctx context.Context, in *UserSignupRequest, opts ...grpc.CallOption) (*UserSignupResponse, error)
	FetchUser(ctx context.Context, in *FetchUserRequest, opts ...grpc.CallOption) (*FetchUserResponse, error)
	UpdateUser(ctx context.Context, in *UpdateUserRequest, opts ...grpc.CallOption) (*UpdateUserResponse, error)
	CheckPan(ctx context.Context, in *CheckPanRequest, opts ...grpc.CallOption) (*CheckPanResponse, error)
	LinkPan(ctx context.Context, in *LinkPanRequest, opts ...grpc.CallOption) (*LinkPanResponse, error)
	GenerateKarvyOtp(ctx context.Context, in *GenerateKarvyOtpRequest, opts ...grpc.CallOption) (*GenerateKarvyOtpResponse, error)
	ValidateKarvyOtp(ctx context.Context, in *ValidateKarvyOtpRequest, opts ...grpc.CallOption) (*ValidateKarvyOtpResponse, error)
	GenerateCamsOtp(ctx context.Context, in *GenerateCamsOtpRequest, opts ...grpc.CallOption) (*GenerateCamsOtpResponse, error)
	ValidateCamsOtp(ctx context.Context, in *ValidateCamsOtpRequest, opts ...grpc.CallOption) (*ValidateCamsOtpResponse, error)
	FetchMfPortfolio(ctx context.Context, in *FetchMfPortfolioRequest, opts ...grpc.CallOption) (*FetchMfPortfolioResponse, error)
	CreateLienKarvy(ctx context.Context, in *CreateLienKarvyRequest, opts ...grpc.CallOption) (*CreateLienKarvyResponse, error)
	ConfirmLienKarvy(ctx context.Context, in *ConfirmLienKarvyRequest, opts ...grpc.CallOption) (*ConfirmLienKarvyResponse, error)
	CreateLienCams(ctx context.Context, in *CreateLienCamsRequest, opts ...grpc.CallOption) (*CreateLienCamsResponse, error)
	ConfirmLienCams(ctx context.Context, in *ConfirmLienCamsRequest, opts ...grpc.CallOption) (*ConfirmLienCamsResponse, error)
	FetchLoanPortfolio(ctx context.Context, in *FetchLoanPortfolioRequest, opts ...grpc.CallOption) (*FetchLoanPortfolioResponse, error)
	InitiateKycProcess(ctx context.Context, in *InitiateKycProcessRequest, opts ...grpc.CallOption) (*InitiateKycProcessResponse, error)
	FetchTentativeRepaymentTimeline(ctx context.Context, in *FetchTentativeRepaymentTimelineRequest, opts ...grpc.CallOption) (*FetchTentativeRepaymentTimelineResponse, error)
	FetchBankAccountDetails(ctx context.Context, in *FetchBankAccountDetailsRequest, opts ...grpc.CallOption) (*FetchBankAccountDetailsResponse, error)
	LinkBankAccount(ctx context.Context, in *LinkBankAccountRequest, opts ...grpc.CallOption) (*LinkBankAccountResponse, error)
	InitiateLoan(ctx context.Context, in *InitiateLoanRequest, opts ...grpc.CallOption) (*InitiateLoanResponse, error)
	FetchExistingLoanDetails(ctx context.Context, in *FetchExistingLoanDetailsRequest, opts ...grpc.CallOption) (*FetchExistingLoanDetailsResponse, error)
	FetchIndividualLoanDetails(ctx context.Context, in *FetchIndividualLoanDetailsRequest, opts ...grpc.CallOption) (*FetchIndividualLoanDetailsResponse, error)
	CreateLoan(ctx context.Context, in *CreateLoanRequest, opts ...grpc.CallOption) (*CreateLoanResponse, error)
	GetBankDetailsForRepayment(ctx context.Context, in *GetBankDetailsForRepaymentRequest, opts ...grpc.CallOption) (*GetBankDetailsForRepaymentResponse, error)
	CloseLoan(ctx context.Context, in *CloseLoanRequest, opts ...grpc.CallOption) (*CloseLoanResponse, error)
	InitiateLoanPartPayment(ctx context.Context, in *InitiateLoanPartPaymentRequest, opts ...grpc.CallOption) (*InitiateLoanPartPaymentResponse, error)
	GetLoanHoldingStatement(ctx context.Context, in *GetLoanHoldingStatementRequest, opts ...grpc.CallOption) (*GetLoanHoldingStatementResponse, error)
	GetLoanForeclosureStatement(ctx context.Context, in *GetLoanForeclosureStatementRequest, opts ...grpc.CallOption) (*GetLoanForeclosureStatementResponse, error)
	GetLoanSoaStatement(ctx context.Context, in *GetLoanSoaStatementRequest, opts ...grpc.CallOption) (*GetLoanSoaStatementResponse, error)
	// GetLoanSoaStatementV2 rpc is upgraded version of GetLoanSoaStatement rpc having additional fields in response of transaction details in form of map to categorize different types of transactions: disbursement_data, part_payment and payments_and_charges which itself is a map of list of amount and type.
	// In GetLoanSoaStatement rpc, Transaction details uses list of objects , each representing a single transaction.
	GetLoanSoaStatementV2(ctx context.Context, in *GetLoanSoaStatementRequest, opts ...grpc.CallOption) (*GetLoanSoaStatementResponseV2, error)
	AddDeviceDetails(ctx context.Context, in *AddDeviceDetailsRequest, opts ...grpc.CallOption) (*AddDeviceDetailsResponse, error)
	AddAdditionalKycDetails(ctx context.Context, in *AddAdditionalKycDetailsRequest, opts ...grpc.CallOption) (*AddAdditionalKycDetailsResponse, error)
	FetchLoansNotifications(ctx context.Context, in *FetchLoansNotificationsRequest, opts ...grpc.CallOption) (*FetchLoansNotificationsResponse, error)
	// VoidLoan rpc can used to close an active loan process and place request to unpledge funds
	//
	// case 1: Unpledge funds and loan process not initiated:
	// unpledge_funds = true, loan_id = 0, restart_loan = false
	//
	// case 2: Unpledge funds and close an active loan:
	// loan_id = active_loan_id, restart_loan = false
	//
	// case 3: close an active loan but don't place unpledge funds request:
	// loan_id = active_loan_id, restart_loan = true
	VoidLoan(ctx context.Context, in *VoidLoanRequest, opts ...grpc.CallOption) (*VoidLoanResponse, error)
	UpdateUserBulk(ctx context.Context, in *UpdateUserBulkRequest, opts ...grpc.CallOption) (*UpdateUserBulkResponse, error)
	LoanEligibilityCheck(ctx context.Context, in *LoanEligibilityCheckRequest, opts ...grpc.CallOption) (*LoanEligibilityCheckResponse, error)
	RegenerateLoanDocumentLink(ctx context.Context, in *RegenerateLoanDocumentLinkRequest, opts ...grpc.CallOption) (*RegenerateLoanDocumentLinkResponse, error)
	DedupeCheck(ctx context.Context, in *DedupeCheckRequest, opts ...grpc.CallOption) (*DedupeCheckResponse, error)
}

type fiftyFinClient struct {
	cc grpc.ClientConnInterface
}

func NewFiftyFinClient(cc grpc.ClientConnInterface) FiftyFinClient {
	return &fiftyFinClient{cc}
}

func (c *fiftyFinClient) UserSignup(ctx context.Context, in *UserSignupRequest, opts ...grpc.CallOption) (*UserSignupResponse, error) {
	out := new(UserSignupResponse)
	err := c.cc.Invoke(ctx, FiftyFin_UserSignup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) FetchUser(ctx context.Context, in *FetchUserRequest, opts ...grpc.CallOption) (*FetchUserResponse, error) {
	out := new(FetchUserResponse)
	err := c.cc.Invoke(ctx, FiftyFin_FetchUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) UpdateUser(ctx context.Context, in *UpdateUserRequest, opts ...grpc.CallOption) (*UpdateUserResponse, error) {
	out := new(UpdateUserResponse)
	err := c.cc.Invoke(ctx, FiftyFin_UpdateUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) CheckPan(ctx context.Context, in *CheckPanRequest, opts ...grpc.CallOption) (*CheckPanResponse, error) {
	out := new(CheckPanResponse)
	err := c.cc.Invoke(ctx, FiftyFin_CheckPan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) LinkPan(ctx context.Context, in *LinkPanRequest, opts ...grpc.CallOption) (*LinkPanResponse, error) {
	out := new(LinkPanResponse)
	err := c.cc.Invoke(ctx, FiftyFin_LinkPan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) GenerateKarvyOtp(ctx context.Context, in *GenerateKarvyOtpRequest, opts ...grpc.CallOption) (*GenerateKarvyOtpResponse, error) {
	out := new(GenerateKarvyOtpResponse)
	err := c.cc.Invoke(ctx, FiftyFin_GenerateKarvyOtp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) ValidateKarvyOtp(ctx context.Context, in *ValidateKarvyOtpRequest, opts ...grpc.CallOption) (*ValidateKarvyOtpResponse, error) {
	out := new(ValidateKarvyOtpResponse)
	err := c.cc.Invoke(ctx, FiftyFin_ValidateKarvyOtp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) GenerateCamsOtp(ctx context.Context, in *GenerateCamsOtpRequest, opts ...grpc.CallOption) (*GenerateCamsOtpResponse, error) {
	out := new(GenerateCamsOtpResponse)
	err := c.cc.Invoke(ctx, FiftyFin_GenerateCamsOtp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) ValidateCamsOtp(ctx context.Context, in *ValidateCamsOtpRequest, opts ...grpc.CallOption) (*ValidateCamsOtpResponse, error) {
	out := new(ValidateCamsOtpResponse)
	err := c.cc.Invoke(ctx, FiftyFin_ValidateCamsOtp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) FetchMfPortfolio(ctx context.Context, in *FetchMfPortfolioRequest, opts ...grpc.CallOption) (*FetchMfPortfolioResponse, error) {
	out := new(FetchMfPortfolioResponse)
	err := c.cc.Invoke(ctx, FiftyFin_FetchMfPortfolio_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) CreateLienKarvy(ctx context.Context, in *CreateLienKarvyRequest, opts ...grpc.CallOption) (*CreateLienKarvyResponse, error) {
	out := new(CreateLienKarvyResponse)
	err := c.cc.Invoke(ctx, FiftyFin_CreateLienKarvy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) ConfirmLienKarvy(ctx context.Context, in *ConfirmLienKarvyRequest, opts ...grpc.CallOption) (*ConfirmLienKarvyResponse, error) {
	out := new(ConfirmLienKarvyResponse)
	err := c.cc.Invoke(ctx, FiftyFin_ConfirmLienKarvy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) CreateLienCams(ctx context.Context, in *CreateLienCamsRequest, opts ...grpc.CallOption) (*CreateLienCamsResponse, error) {
	out := new(CreateLienCamsResponse)
	err := c.cc.Invoke(ctx, FiftyFin_CreateLienCams_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) ConfirmLienCams(ctx context.Context, in *ConfirmLienCamsRequest, opts ...grpc.CallOption) (*ConfirmLienCamsResponse, error) {
	out := new(ConfirmLienCamsResponse)
	err := c.cc.Invoke(ctx, FiftyFin_ConfirmLienCams_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) FetchLoanPortfolio(ctx context.Context, in *FetchLoanPortfolioRequest, opts ...grpc.CallOption) (*FetchLoanPortfolioResponse, error) {
	out := new(FetchLoanPortfolioResponse)
	err := c.cc.Invoke(ctx, FiftyFin_FetchLoanPortfolio_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) InitiateKycProcess(ctx context.Context, in *InitiateKycProcessRequest, opts ...grpc.CallOption) (*InitiateKycProcessResponse, error) {
	out := new(InitiateKycProcessResponse)
	err := c.cc.Invoke(ctx, FiftyFin_InitiateKycProcess_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) FetchTentativeRepaymentTimeline(ctx context.Context, in *FetchTentativeRepaymentTimelineRequest, opts ...grpc.CallOption) (*FetchTentativeRepaymentTimelineResponse, error) {
	out := new(FetchTentativeRepaymentTimelineResponse)
	err := c.cc.Invoke(ctx, FiftyFin_FetchTentativeRepaymentTimeline_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) FetchBankAccountDetails(ctx context.Context, in *FetchBankAccountDetailsRequest, opts ...grpc.CallOption) (*FetchBankAccountDetailsResponse, error) {
	out := new(FetchBankAccountDetailsResponse)
	err := c.cc.Invoke(ctx, FiftyFin_FetchBankAccountDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) LinkBankAccount(ctx context.Context, in *LinkBankAccountRequest, opts ...grpc.CallOption) (*LinkBankAccountResponse, error) {
	out := new(LinkBankAccountResponse)
	err := c.cc.Invoke(ctx, FiftyFin_LinkBankAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) InitiateLoan(ctx context.Context, in *InitiateLoanRequest, opts ...grpc.CallOption) (*InitiateLoanResponse, error) {
	out := new(InitiateLoanResponse)
	err := c.cc.Invoke(ctx, FiftyFin_InitiateLoan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) FetchExistingLoanDetails(ctx context.Context, in *FetchExistingLoanDetailsRequest, opts ...grpc.CallOption) (*FetchExistingLoanDetailsResponse, error) {
	out := new(FetchExistingLoanDetailsResponse)
	err := c.cc.Invoke(ctx, FiftyFin_FetchExistingLoanDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) FetchIndividualLoanDetails(ctx context.Context, in *FetchIndividualLoanDetailsRequest, opts ...grpc.CallOption) (*FetchIndividualLoanDetailsResponse, error) {
	out := new(FetchIndividualLoanDetailsResponse)
	err := c.cc.Invoke(ctx, FiftyFin_FetchIndividualLoanDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) CreateLoan(ctx context.Context, in *CreateLoanRequest, opts ...grpc.CallOption) (*CreateLoanResponse, error) {
	out := new(CreateLoanResponse)
	err := c.cc.Invoke(ctx, FiftyFin_CreateLoan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) GetBankDetailsForRepayment(ctx context.Context, in *GetBankDetailsForRepaymentRequest, opts ...grpc.CallOption) (*GetBankDetailsForRepaymentResponse, error) {
	out := new(GetBankDetailsForRepaymentResponse)
	err := c.cc.Invoke(ctx, FiftyFin_GetBankDetailsForRepayment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) CloseLoan(ctx context.Context, in *CloseLoanRequest, opts ...grpc.CallOption) (*CloseLoanResponse, error) {
	out := new(CloseLoanResponse)
	err := c.cc.Invoke(ctx, FiftyFin_CloseLoan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) InitiateLoanPartPayment(ctx context.Context, in *InitiateLoanPartPaymentRequest, opts ...grpc.CallOption) (*InitiateLoanPartPaymentResponse, error) {
	out := new(InitiateLoanPartPaymentResponse)
	err := c.cc.Invoke(ctx, FiftyFin_InitiateLoanPartPayment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) GetLoanHoldingStatement(ctx context.Context, in *GetLoanHoldingStatementRequest, opts ...grpc.CallOption) (*GetLoanHoldingStatementResponse, error) {
	out := new(GetLoanHoldingStatementResponse)
	err := c.cc.Invoke(ctx, FiftyFin_GetLoanHoldingStatement_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) GetLoanForeclosureStatement(ctx context.Context, in *GetLoanForeclosureStatementRequest, opts ...grpc.CallOption) (*GetLoanForeclosureStatementResponse, error) {
	out := new(GetLoanForeclosureStatementResponse)
	err := c.cc.Invoke(ctx, FiftyFin_GetLoanForeclosureStatement_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) GetLoanSoaStatement(ctx context.Context, in *GetLoanSoaStatementRequest, opts ...grpc.CallOption) (*GetLoanSoaStatementResponse, error) {
	out := new(GetLoanSoaStatementResponse)
	err := c.cc.Invoke(ctx, FiftyFin_GetLoanSoaStatement_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) GetLoanSoaStatementV2(ctx context.Context, in *GetLoanSoaStatementRequest, opts ...grpc.CallOption) (*GetLoanSoaStatementResponseV2, error) {
	out := new(GetLoanSoaStatementResponseV2)
	err := c.cc.Invoke(ctx, FiftyFin_GetLoanSoaStatementV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) AddDeviceDetails(ctx context.Context, in *AddDeviceDetailsRequest, opts ...grpc.CallOption) (*AddDeviceDetailsResponse, error) {
	out := new(AddDeviceDetailsResponse)
	err := c.cc.Invoke(ctx, FiftyFin_AddDeviceDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) AddAdditionalKycDetails(ctx context.Context, in *AddAdditionalKycDetailsRequest, opts ...grpc.CallOption) (*AddAdditionalKycDetailsResponse, error) {
	out := new(AddAdditionalKycDetailsResponse)
	err := c.cc.Invoke(ctx, FiftyFin_AddAdditionalKycDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) FetchLoansNotifications(ctx context.Context, in *FetchLoansNotificationsRequest, opts ...grpc.CallOption) (*FetchLoansNotificationsResponse, error) {
	out := new(FetchLoansNotificationsResponse)
	err := c.cc.Invoke(ctx, FiftyFin_FetchLoansNotifications_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) VoidLoan(ctx context.Context, in *VoidLoanRequest, opts ...grpc.CallOption) (*VoidLoanResponse, error) {
	out := new(VoidLoanResponse)
	err := c.cc.Invoke(ctx, FiftyFin_VoidLoan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) UpdateUserBulk(ctx context.Context, in *UpdateUserBulkRequest, opts ...grpc.CallOption) (*UpdateUserBulkResponse, error) {
	out := new(UpdateUserBulkResponse)
	err := c.cc.Invoke(ctx, FiftyFin_UpdateUserBulk_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) LoanEligibilityCheck(ctx context.Context, in *LoanEligibilityCheckRequest, opts ...grpc.CallOption) (*LoanEligibilityCheckResponse, error) {
	out := new(LoanEligibilityCheckResponse)
	err := c.cc.Invoke(ctx, FiftyFin_LoanEligibilityCheck_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) RegenerateLoanDocumentLink(ctx context.Context, in *RegenerateLoanDocumentLinkRequest, opts ...grpc.CallOption) (*RegenerateLoanDocumentLinkResponse, error) {
	out := new(RegenerateLoanDocumentLinkResponse)
	err := c.cc.Invoke(ctx, FiftyFin_RegenerateLoanDocumentLink_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiftyFinClient) DedupeCheck(ctx context.Context, in *DedupeCheckRequest, opts ...grpc.CallOption) (*DedupeCheckResponse, error) {
	out := new(DedupeCheckResponse)
	err := c.cc.Invoke(ctx, FiftyFin_DedupeCheck_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FiftyFinServer is the server API for FiftyFin service.
// All implementations should embed UnimplementedFiftyFinServer
// for forward compatibility
type FiftyFinServer interface {
	UserSignup(context.Context, *UserSignupRequest) (*UserSignupResponse, error)
	FetchUser(context.Context, *FetchUserRequest) (*FetchUserResponse, error)
	UpdateUser(context.Context, *UpdateUserRequest) (*UpdateUserResponse, error)
	CheckPan(context.Context, *CheckPanRequest) (*CheckPanResponse, error)
	LinkPan(context.Context, *LinkPanRequest) (*LinkPanResponse, error)
	GenerateKarvyOtp(context.Context, *GenerateKarvyOtpRequest) (*GenerateKarvyOtpResponse, error)
	ValidateKarvyOtp(context.Context, *ValidateKarvyOtpRequest) (*ValidateKarvyOtpResponse, error)
	GenerateCamsOtp(context.Context, *GenerateCamsOtpRequest) (*GenerateCamsOtpResponse, error)
	ValidateCamsOtp(context.Context, *ValidateCamsOtpRequest) (*ValidateCamsOtpResponse, error)
	FetchMfPortfolio(context.Context, *FetchMfPortfolioRequest) (*FetchMfPortfolioResponse, error)
	CreateLienKarvy(context.Context, *CreateLienKarvyRequest) (*CreateLienKarvyResponse, error)
	ConfirmLienKarvy(context.Context, *ConfirmLienKarvyRequest) (*ConfirmLienKarvyResponse, error)
	CreateLienCams(context.Context, *CreateLienCamsRequest) (*CreateLienCamsResponse, error)
	ConfirmLienCams(context.Context, *ConfirmLienCamsRequest) (*ConfirmLienCamsResponse, error)
	FetchLoanPortfolio(context.Context, *FetchLoanPortfolioRequest) (*FetchLoanPortfolioResponse, error)
	InitiateKycProcess(context.Context, *InitiateKycProcessRequest) (*InitiateKycProcessResponse, error)
	FetchTentativeRepaymentTimeline(context.Context, *FetchTentativeRepaymentTimelineRequest) (*FetchTentativeRepaymentTimelineResponse, error)
	FetchBankAccountDetails(context.Context, *FetchBankAccountDetailsRequest) (*FetchBankAccountDetailsResponse, error)
	LinkBankAccount(context.Context, *LinkBankAccountRequest) (*LinkBankAccountResponse, error)
	InitiateLoan(context.Context, *InitiateLoanRequest) (*InitiateLoanResponse, error)
	FetchExistingLoanDetails(context.Context, *FetchExistingLoanDetailsRequest) (*FetchExistingLoanDetailsResponse, error)
	FetchIndividualLoanDetails(context.Context, *FetchIndividualLoanDetailsRequest) (*FetchIndividualLoanDetailsResponse, error)
	CreateLoan(context.Context, *CreateLoanRequest) (*CreateLoanResponse, error)
	GetBankDetailsForRepayment(context.Context, *GetBankDetailsForRepaymentRequest) (*GetBankDetailsForRepaymentResponse, error)
	CloseLoan(context.Context, *CloseLoanRequest) (*CloseLoanResponse, error)
	InitiateLoanPartPayment(context.Context, *InitiateLoanPartPaymentRequest) (*InitiateLoanPartPaymentResponse, error)
	GetLoanHoldingStatement(context.Context, *GetLoanHoldingStatementRequest) (*GetLoanHoldingStatementResponse, error)
	GetLoanForeclosureStatement(context.Context, *GetLoanForeclosureStatementRequest) (*GetLoanForeclosureStatementResponse, error)
	GetLoanSoaStatement(context.Context, *GetLoanSoaStatementRequest) (*GetLoanSoaStatementResponse, error)
	// GetLoanSoaStatementV2 rpc is upgraded version of GetLoanSoaStatement rpc having additional fields in response of transaction details in form of map to categorize different types of transactions: disbursement_data, part_payment and payments_and_charges which itself is a map of list of amount and type.
	// In GetLoanSoaStatement rpc, Transaction details uses list of objects , each representing a single transaction.
	GetLoanSoaStatementV2(context.Context, *GetLoanSoaStatementRequest) (*GetLoanSoaStatementResponseV2, error)
	AddDeviceDetails(context.Context, *AddDeviceDetailsRequest) (*AddDeviceDetailsResponse, error)
	AddAdditionalKycDetails(context.Context, *AddAdditionalKycDetailsRequest) (*AddAdditionalKycDetailsResponse, error)
	FetchLoansNotifications(context.Context, *FetchLoansNotificationsRequest) (*FetchLoansNotificationsResponse, error)
	// VoidLoan rpc can used to close an active loan process and place request to unpledge funds
	//
	// case 1: Unpledge funds and loan process not initiated:
	// unpledge_funds = true, loan_id = 0, restart_loan = false
	//
	// case 2: Unpledge funds and close an active loan:
	// loan_id = active_loan_id, restart_loan = false
	//
	// case 3: close an active loan but don't place unpledge funds request:
	// loan_id = active_loan_id, restart_loan = true
	VoidLoan(context.Context, *VoidLoanRequest) (*VoidLoanResponse, error)
	UpdateUserBulk(context.Context, *UpdateUserBulkRequest) (*UpdateUserBulkResponse, error)
	LoanEligibilityCheck(context.Context, *LoanEligibilityCheckRequest) (*LoanEligibilityCheckResponse, error)
	RegenerateLoanDocumentLink(context.Context, *RegenerateLoanDocumentLinkRequest) (*RegenerateLoanDocumentLinkResponse, error)
	DedupeCheck(context.Context, *DedupeCheckRequest) (*DedupeCheckResponse, error)
}

// UnimplementedFiftyFinServer should be embedded to have forward compatible implementations.
type UnimplementedFiftyFinServer struct {
}

func (UnimplementedFiftyFinServer) UserSignup(context.Context, *UserSignupRequest) (*UserSignupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserSignup not implemented")
}
func (UnimplementedFiftyFinServer) FetchUser(context.Context, *FetchUserRequest) (*FetchUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchUser not implemented")
}
func (UnimplementedFiftyFinServer) UpdateUser(context.Context, *UpdateUserRequest) (*UpdateUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUser not implemented")
}
func (UnimplementedFiftyFinServer) CheckPan(context.Context, *CheckPanRequest) (*CheckPanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPan not implemented")
}
func (UnimplementedFiftyFinServer) LinkPan(context.Context, *LinkPanRequest) (*LinkPanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkPan not implemented")
}
func (UnimplementedFiftyFinServer) GenerateKarvyOtp(context.Context, *GenerateKarvyOtpRequest) (*GenerateKarvyOtpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateKarvyOtp not implemented")
}
func (UnimplementedFiftyFinServer) ValidateKarvyOtp(context.Context, *ValidateKarvyOtpRequest) (*ValidateKarvyOtpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateKarvyOtp not implemented")
}
func (UnimplementedFiftyFinServer) GenerateCamsOtp(context.Context, *GenerateCamsOtpRequest) (*GenerateCamsOtpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateCamsOtp not implemented")
}
func (UnimplementedFiftyFinServer) ValidateCamsOtp(context.Context, *ValidateCamsOtpRequest) (*ValidateCamsOtpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateCamsOtp not implemented")
}
func (UnimplementedFiftyFinServer) FetchMfPortfolio(context.Context, *FetchMfPortfolioRequest) (*FetchMfPortfolioResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchMfPortfolio not implemented")
}
func (UnimplementedFiftyFinServer) CreateLienKarvy(context.Context, *CreateLienKarvyRequest) (*CreateLienKarvyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLienKarvy not implemented")
}
func (UnimplementedFiftyFinServer) ConfirmLienKarvy(context.Context, *ConfirmLienKarvyRequest) (*ConfirmLienKarvyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmLienKarvy not implemented")
}
func (UnimplementedFiftyFinServer) CreateLienCams(context.Context, *CreateLienCamsRequest) (*CreateLienCamsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLienCams not implemented")
}
func (UnimplementedFiftyFinServer) ConfirmLienCams(context.Context, *ConfirmLienCamsRequest) (*ConfirmLienCamsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmLienCams not implemented")
}
func (UnimplementedFiftyFinServer) FetchLoanPortfolio(context.Context, *FetchLoanPortfolioRequest) (*FetchLoanPortfolioResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchLoanPortfolio not implemented")
}
func (UnimplementedFiftyFinServer) InitiateKycProcess(context.Context, *InitiateKycProcessRequest) (*InitiateKycProcessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateKycProcess not implemented")
}
func (UnimplementedFiftyFinServer) FetchTentativeRepaymentTimeline(context.Context, *FetchTentativeRepaymentTimelineRequest) (*FetchTentativeRepaymentTimelineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchTentativeRepaymentTimeline not implemented")
}
func (UnimplementedFiftyFinServer) FetchBankAccountDetails(context.Context, *FetchBankAccountDetailsRequest) (*FetchBankAccountDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchBankAccountDetails not implemented")
}
func (UnimplementedFiftyFinServer) LinkBankAccount(context.Context, *LinkBankAccountRequest) (*LinkBankAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkBankAccount not implemented")
}
func (UnimplementedFiftyFinServer) InitiateLoan(context.Context, *InitiateLoanRequest) (*InitiateLoanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateLoan not implemented")
}
func (UnimplementedFiftyFinServer) FetchExistingLoanDetails(context.Context, *FetchExistingLoanDetailsRequest) (*FetchExistingLoanDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchExistingLoanDetails not implemented")
}
func (UnimplementedFiftyFinServer) FetchIndividualLoanDetails(context.Context, *FetchIndividualLoanDetailsRequest) (*FetchIndividualLoanDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchIndividualLoanDetails not implemented")
}
func (UnimplementedFiftyFinServer) CreateLoan(context.Context, *CreateLoanRequest) (*CreateLoanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLoan not implemented")
}
func (UnimplementedFiftyFinServer) GetBankDetailsForRepayment(context.Context, *GetBankDetailsForRepaymentRequest) (*GetBankDetailsForRepaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBankDetailsForRepayment not implemented")
}
func (UnimplementedFiftyFinServer) CloseLoan(context.Context, *CloseLoanRequest) (*CloseLoanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloseLoan not implemented")
}
func (UnimplementedFiftyFinServer) InitiateLoanPartPayment(context.Context, *InitiateLoanPartPaymentRequest) (*InitiateLoanPartPaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateLoanPartPayment not implemented")
}
func (UnimplementedFiftyFinServer) GetLoanHoldingStatement(context.Context, *GetLoanHoldingStatementRequest) (*GetLoanHoldingStatementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanHoldingStatement not implemented")
}
func (UnimplementedFiftyFinServer) GetLoanForeclosureStatement(context.Context, *GetLoanForeclosureStatementRequest) (*GetLoanForeclosureStatementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanForeclosureStatement not implemented")
}
func (UnimplementedFiftyFinServer) GetLoanSoaStatement(context.Context, *GetLoanSoaStatementRequest) (*GetLoanSoaStatementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanSoaStatement not implemented")
}
func (UnimplementedFiftyFinServer) GetLoanSoaStatementV2(context.Context, *GetLoanSoaStatementRequest) (*GetLoanSoaStatementResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanSoaStatementV2 not implemented")
}
func (UnimplementedFiftyFinServer) AddDeviceDetails(context.Context, *AddDeviceDetailsRequest) (*AddDeviceDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddDeviceDetails not implemented")
}
func (UnimplementedFiftyFinServer) AddAdditionalKycDetails(context.Context, *AddAdditionalKycDetailsRequest) (*AddAdditionalKycDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAdditionalKycDetails not implemented")
}
func (UnimplementedFiftyFinServer) FetchLoansNotifications(context.Context, *FetchLoansNotificationsRequest) (*FetchLoansNotificationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchLoansNotifications not implemented")
}
func (UnimplementedFiftyFinServer) VoidLoan(context.Context, *VoidLoanRequest) (*VoidLoanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VoidLoan not implemented")
}
func (UnimplementedFiftyFinServer) UpdateUserBulk(context.Context, *UpdateUserBulkRequest) (*UpdateUserBulkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserBulk not implemented")
}
func (UnimplementedFiftyFinServer) LoanEligibilityCheck(context.Context, *LoanEligibilityCheckRequest) (*LoanEligibilityCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoanEligibilityCheck not implemented")
}
func (UnimplementedFiftyFinServer) RegenerateLoanDocumentLink(context.Context, *RegenerateLoanDocumentLinkRequest) (*RegenerateLoanDocumentLinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegenerateLoanDocumentLink not implemented")
}
func (UnimplementedFiftyFinServer) DedupeCheck(context.Context, *DedupeCheckRequest) (*DedupeCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DedupeCheck not implemented")
}

// UnsafeFiftyFinServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FiftyFinServer will
// result in compilation errors.
type UnsafeFiftyFinServer interface {
	mustEmbedUnimplementedFiftyFinServer()
}

func RegisterFiftyFinServer(s grpc.ServiceRegistrar, srv FiftyFinServer) {
	s.RegisterService(&FiftyFin_ServiceDesc, srv)
}

func _FiftyFin_UserSignup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserSignupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).UserSignup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_UserSignup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).UserSignup(ctx, req.(*UserSignupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_FetchUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).FetchUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_FetchUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).FetchUser(ctx, req.(*FetchUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_UpdateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).UpdateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_UpdateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).UpdateUser(ctx, req.(*UpdateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_CheckPan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).CheckPan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_CheckPan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).CheckPan(ctx, req.(*CheckPanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_LinkPan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LinkPanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).LinkPan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_LinkPan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).LinkPan(ctx, req.(*LinkPanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_GenerateKarvyOtp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateKarvyOtpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).GenerateKarvyOtp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_GenerateKarvyOtp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).GenerateKarvyOtp(ctx, req.(*GenerateKarvyOtpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_ValidateKarvyOtp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateKarvyOtpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).ValidateKarvyOtp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_ValidateKarvyOtp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).ValidateKarvyOtp(ctx, req.(*ValidateKarvyOtpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_GenerateCamsOtp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateCamsOtpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).GenerateCamsOtp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_GenerateCamsOtp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).GenerateCamsOtp(ctx, req.(*GenerateCamsOtpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_ValidateCamsOtp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateCamsOtpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).ValidateCamsOtp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_ValidateCamsOtp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).ValidateCamsOtp(ctx, req.(*ValidateCamsOtpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_FetchMfPortfolio_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchMfPortfolioRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).FetchMfPortfolio(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_FetchMfPortfolio_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).FetchMfPortfolio(ctx, req.(*FetchMfPortfolioRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_CreateLienKarvy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLienKarvyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).CreateLienKarvy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_CreateLienKarvy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).CreateLienKarvy(ctx, req.(*CreateLienKarvyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_ConfirmLienKarvy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmLienKarvyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).ConfirmLienKarvy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_ConfirmLienKarvy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).ConfirmLienKarvy(ctx, req.(*ConfirmLienKarvyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_CreateLienCams_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLienCamsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).CreateLienCams(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_CreateLienCams_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).CreateLienCams(ctx, req.(*CreateLienCamsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_ConfirmLienCams_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmLienCamsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).ConfirmLienCams(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_ConfirmLienCams_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).ConfirmLienCams(ctx, req.(*ConfirmLienCamsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_FetchLoanPortfolio_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchLoanPortfolioRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).FetchLoanPortfolio(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_FetchLoanPortfolio_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).FetchLoanPortfolio(ctx, req.(*FetchLoanPortfolioRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_InitiateKycProcess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateKycProcessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).InitiateKycProcess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_InitiateKycProcess_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).InitiateKycProcess(ctx, req.(*InitiateKycProcessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_FetchTentativeRepaymentTimeline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchTentativeRepaymentTimelineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).FetchTentativeRepaymentTimeline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_FetchTentativeRepaymentTimeline_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).FetchTentativeRepaymentTimeline(ctx, req.(*FetchTentativeRepaymentTimelineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_FetchBankAccountDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchBankAccountDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).FetchBankAccountDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_FetchBankAccountDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).FetchBankAccountDetails(ctx, req.(*FetchBankAccountDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_LinkBankAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LinkBankAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).LinkBankAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_LinkBankAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).LinkBankAccount(ctx, req.(*LinkBankAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_InitiateLoan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateLoanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).InitiateLoan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_InitiateLoan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).InitiateLoan(ctx, req.(*InitiateLoanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_FetchExistingLoanDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchExistingLoanDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).FetchExistingLoanDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_FetchExistingLoanDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).FetchExistingLoanDetails(ctx, req.(*FetchExistingLoanDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_FetchIndividualLoanDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchIndividualLoanDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).FetchIndividualLoanDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_FetchIndividualLoanDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).FetchIndividualLoanDetails(ctx, req.(*FetchIndividualLoanDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_CreateLoan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLoanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).CreateLoan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_CreateLoan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).CreateLoan(ctx, req.(*CreateLoanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_GetBankDetailsForRepayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBankDetailsForRepaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).GetBankDetailsForRepayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_GetBankDetailsForRepayment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).GetBankDetailsForRepayment(ctx, req.(*GetBankDetailsForRepaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_CloseLoan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloseLoanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).CloseLoan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_CloseLoan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).CloseLoan(ctx, req.(*CloseLoanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_InitiateLoanPartPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateLoanPartPaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).InitiateLoanPartPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_InitiateLoanPartPayment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).InitiateLoanPartPayment(ctx, req.(*InitiateLoanPartPaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_GetLoanHoldingStatement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanHoldingStatementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).GetLoanHoldingStatement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_GetLoanHoldingStatement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).GetLoanHoldingStatement(ctx, req.(*GetLoanHoldingStatementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_GetLoanForeclosureStatement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanForeclosureStatementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).GetLoanForeclosureStatement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_GetLoanForeclosureStatement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).GetLoanForeclosureStatement(ctx, req.(*GetLoanForeclosureStatementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_GetLoanSoaStatement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanSoaStatementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).GetLoanSoaStatement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_GetLoanSoaStatement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).GetLoanSoaStatement(ctx, req.(*GetLoanSoaStatementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_GetLoanSoaStatementV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanSoaStatementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).GetLoanSoaStatementV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_GetLoanSoaStatementV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).GetLoanSoaStatementV2(ctx, req.(*GetLoanSoaStatementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_AddDeviceDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddDeviceDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).AddDeviceDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_AddDeviceDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).AddDeviceDetails(ctx, req.(*AddDeviceDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_AddAdditionalKycDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAdditionalKycDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).AddAdditionalKycDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_AddAdditionalKycDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).AddAdditionalKycDetails(ctx, req.(*AddAdditionalKycDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_FetchLoansNotifications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchLoansNotificationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).FetchLoansNotifications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_FetchLoansNotifications_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).FetchLoansNotifications(ctx, req.(*FetchLoansNotificationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_VoidLoan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VoidLoanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).VoidLoan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_VoidLoan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).VoidLoan(ctx, req.(*VoidLoanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_UpdateUserBulk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserBulkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).UpdateUserBulk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_UpdateUserBulk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).UpdateUserBulk(ctx, req.(*UpdateUserBulkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_LoanEligibilityCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoanEligibilityCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).LoanEligibilityCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_LoanEligibilityCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).LoanEligibilityCheck(ctx, req.(*LoanEligibilityCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_RegenerateLoanDocumentLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegenerateLoanDocumentLinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).RegenerateLoanDocumentLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_RegenerateLoanDocumentLink_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).RegenerateLoanDocumentLink(ctx, req.(*RegenerateLoanDocumentLinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FiftyFin_DedupeCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DedupeCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiftyFinServer).DedupeCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FiftyFin_DedupeCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiftyFinServer).DedupeCheck(ctx, req.(*DedupeCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FiftyFin_ServiceDesc is the grpc.ServiceDesc for FiftyFin service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FiftyFin_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendorgateway.lending.securedloans.fiftyfin.FiftyFin",
	HandlerType: (*FiftyFinServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UserSignup",
			Handler:    _FiftyFin_UserSignup_Handler,
		},
		{
			MethodName: "FetchUser",
			Handler:    _FiftyFin_FetchUser_Handler,
		},
		{
			MethodName: "UpdateUser",
			Handler:    _FiftyFin_UpdateUser_Handler,
		},
		{
			MethodName: "CheckPan",
			Handler:    _FiftyFin_CheckPan_Handler,
		},
		{
			MethodName: "LinkPan",
			Handler:    _FiftyFin_LinkPan_Handler,
		},
		{
			MethodName: "GenerateKarvyOtp",
			Handler:    _FiftyFin_GenerateKarvyOtp_Handler,
		},
		{
			MethodName: "ValidateKarvyOtp",
			Handler:    _FiftyFin_ValidateKarvyOtp_Handler,
		},
		{
			MethodName: "GenerateCamsOtp",
			Handler:    _FiftyFin_GenerateCamsOtp_Handler,
		},
		{
			MethodName: "ValidateCamsOtp",
			Handler:    _FiftyFin_ValidateCamsOtp_Handler,
		},
		{
			MethodName: "FetchMfPortfolio",
			Handler:    _FiftyFin_FetchMfPortfolio_Handler,
		},
		{
			MethodName: "CreateLienKarvy",
			Handler:    _FiftyFin_CreateLienKarvy_Handler,
		},
		{
			MethodName: "ConfirmLienKarvy",
			Handler:    _FiftyFin_ConfirmLienKarvy_Handler,
		},
		{
			MethodName: "CreateLienCams",
			Handler:    _FiftyFin_CreateLienCams_Handler,
		},
		{
			MethodName: "ConfirmLienCams",
			Handler:    _FiftyFin_ConfirmLienCams_Handler,
		},
		{
			MethodName: "FetchLoanPortfolio",
			Handler:    _FiftyFin_FetchLoanPortfolio_Handler,
		},
		{
			MethodName: "InitiateKycProcess",
			Handler:    _FiftyFin_InitiateKycProcess_Handler,
		},
		{
			MethodName: "FetchTentativeRepaymentTimeline",
			Handler:    _FiftyFin_FetchTentativeRepaymentTimeline_Handler,
		},
		{
			MethodName: "FetchBankAccountDetails",
			Handler:    _FiftyFin_FetchBankAccountDetails_Handler,
		},
		{
			MethodName: "LinkBankAccount",
			Handler:    _FiftyFin_LinkBankAccount_Handler,
		},
		{
			MethodName: "InitiateLoan",
			Handler:    _FiftyFin_InitiateLoan_Handler,
		},
		{
			MethodName: "FetchExistingLoanDetails",
			Handler:    _FiftyFin_FetchExistingLoanDetails_Handler,
		},
		{
			MethodName: "FetchIndividualLoanDetails",
			Handler:    _FiftyFin_FetchIndividualLoanDetails_Handler,
		},
		{
			MethodName: "CreateLoan",
			Handler:    _FiftyFin_CreateLoan_Handler,
		},
		{
			MethodName: "GetBankDetailsForRepayment",
			Handler:    _FiftyFin_GetBankDetailsForRepayment_Handler,
		},
		{
			MethodName: "CloseLoan",
			Handler:    _FiftyFin_CloseLoan_Handler,
		},
		{
			MethodName: "InitiateLoanPartPayment",
			Handler:    _FiftyFin_InitiateLoanPartPayment_Handler,
		},
		{
			MethodName: "GetLoanHoldingStatement",
			Handler:    _FiftyFin_GetLoanHoldingStatement_Handler,
		},
		{
			MethodName: "GetLoanForeclosureStatement",
			Handler:    _FiftyFin_GetLoanForeclosureStatement_Handler,
		},
		{
			MethodName: "GetLoanSoaStatement",
			Handler:    _FiftyFin_GetLoanSoaStatement_Handler,
		},
		{
			MethodName: "GetLoanSoaStatementV2",
			Handler:    _FiftyFin_GetLoanSoaStatementV2_Handler,
		},
		{
			MethodName: "AddDeviceDetails",
			Handler:    _FiftyFin_AddDeviceDetails_Handler,
		},
		{
			MethodName: "AddAdditionalKycDetails",
			Handler:    _FiftyFin_AddAdditionalKycDetails_Handler,
		},
		{
			MethodName: "FetchLoansNotifications",
			Handler:    _FiftyFin_FetchLoansNotifications_Handler,
		},
		{
			MethodName: "VoidLoan",
			Handler:    _FiftyFin_VoidLoan_Handler,
		},
		{
			MethodName: "UpdateUserBulk",
			Handler:    _FiftyFin_UpdateUserBulk_Handler,
		},
		{
			MethodName: "LoanEligibilityCheck",
			Handler:    _FiftyFin_LoanEligibilityCheck_Handler,
		},
		{
			MethodName: "RegenerateLoanDocumentLink",
			Handler:    _FiftyFin_RegenerateLoanDocumentLink_Handler,
		},
		{
			MethodName: "DedupeCheck",
			Handler:    _FiftyFin_DedupeCheck_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendorgateway/lending/securedloans/fiftyfin/service.proto",
}
