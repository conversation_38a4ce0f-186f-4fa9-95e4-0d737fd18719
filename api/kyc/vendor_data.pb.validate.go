// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/kyc/internal/vendor_data.proto

package kyc

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on KYCVendorData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *KYCVendorData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KYCVendorData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in KYCVendorDataMultiError, or
// nil if none found.
func (m *KYCVendorData) ValidateAll() error {
	return m.validate(true)
}

func (m *KYCVendorData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for KycAttemptId

	// no validation rules for PayloadType

	if all {
		switch v := interface{}(m.GetPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KYCVendorDataValidationError{
					field:  "Payload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KYCVendorDataValidationError{
					field:  "Payload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KYCVendorDataValidationError{
				field:  "Payload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KYCVendorDataValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KYCVendorDataValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KYCVendorDataValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeleteBy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KYCVendorDataValidationError{
					field:  "DeleteBy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KYCVendorDataValidationError{
					field:  "DeleteBy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeleteBy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KYCVendorDataValidationError{
				field:  "DeleteBy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KYCVendorDataValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KYCVendorDataValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KYCVendorDataValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return KYCVendorDataMultiError(errors)
	}

	return nil
}

// KYCVendorDataMultiError is an error wrapping multiple validation errors
// returned by KYCVendorData.ValidateAll() if the designated constraints
// aren't met.
type KYCVendorDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KYCVendorDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KYCVendorDataMultiError) AllErrors() []error { return m }

// KYCVendorDataValidationError is the validation error returned by
// KYCVendorData.Validate if the designated constraints aren't met.
type KYCVendorDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KYCVendorDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KYCVendorDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KYCVendorDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KYCVendorDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KYCVendorDataValidationError) ErrorName() string { return "KYCVendorDataValidationError" }

// Error satisfies the builtin error interface
func (e KYCVendorDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKYCVendorData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KYCVendorDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KYCVendorDataValidationError{}

// Validate checks the field values on KYCVendorDataPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KYCVendorDataPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KYCVendorDataPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KYCVendorDataPayloadMultiError, or nil if none found.
func (m *KYCVendorDataPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *KYCVendorDataPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCkycDownloads()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KYCVendorDataPayloadValidationError{
					field:  "CkycDownloads",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KYCVendorDataPayloadValidationError{
					field:  "CkycDownloads",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCkycDownloads()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KYCVendorDataPayloadValidationError{
				field:  "CkycDownloads",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEkycRecord()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KYCVendorDataPayloadValidationError{
					field:  "EkycRecord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KYCVendorDataPayloadValidationError{
					field:  "EkycRecord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEkycRecord()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KYCVendorDataPayloadValidationError{
				field:  "EkycRecord",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBkycRecord()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KYCVendorDataPayloadValidationError{
					field:  "BkycRecord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KYCVendorDataPayloadValidationError{
					field:  "BkycRecord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBkycRecord()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KYCVendorDataPayloadValidationError{
				field:  "BkycRecord",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return KYCVendorDataPayloadMultiError(errors)
	}

	return nil
}

// KYCVendorDataPayloadMultiError is an error wrapping multiple validation
// errors returned by KYCVendorDataPayload.ValidateAll() if the designated
// constraints aren't met.
type KYCVendorDataPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KYCVendorDataPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KYCVendorDataPayloadMultiError) AllErrors() []error { return m }

// KYCVendorDataPayloadValidationError is the validation error returned by
// KYCVendorDataPayload.Validate if the designated constraints aren't met.
type KYCVendorDataPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KYCVendorDataPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KYCVendorDataPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KYCVendorDataPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KYCVendorDataPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KYCVendorDataPayloadValidationError) ErrorName() string {
	return "KYCVendorDataPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e KYCVendorDataPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKYCVendorDataPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KYCVendorDataPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KYCVendorDataPayloadValidationError{}

// Validate checks the field values on CKYCDownloadVendorData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CKYCDownloadVendorData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CKYCDownloadVendorData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CKYCDownloadVendorDataMultiError, or nil if none found.
func (m *CKYCDownloadVendorData) ValidateAll() error {
	return m.validate(true)
}

func (m *CKYCDownloadVendorData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPayloads() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CKYCDownloadVendorDataValidationError{
						field:  fmt.Sprintf("Payloads[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CKYCDownloadVendorDataValidationError{
						field:  fmt.Sprintf("Payloads[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CKYCDownloadVendorDataValidationError{
					field:  fmt.Sprintf("Payloads[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CKYCDownloadVendorDataMultiError(errors)
	}

	return nil
}

// CKYCDownloadVendorDataMultiError is an error wrapping multiple validation
// errors returned by CKYCDownloadVendorData.ValidateAll() if the designated
// constraints aren't met.
type CKYCDownloadVendorDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CKYCDownloadVendorDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CKYCDownloadVendorDataMultiError) AllErrors() []error { return m }

// CKYCDownloadVendorDataValidationError is the validation error returned by
// CKYCDownloadVendorData.Validate if the designated constraints aren't met.
type CKYCDownloadVendorDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CKYCDownloadVendorDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CKYCDownloadVendorDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CKYCDownloadVendorDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CKYCDownloadVendorDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CKYCDownloadVendorDataValidationError) ErrorName() string {
	return "CKYCDownloadVendorDataValidationError"
}

// Error satisfies the builtin error interface
func (e CKYCDownloadVendorDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCKYCDownloadVendorData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CKYCDownloadVendorDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CKYCDownloadVendorDataValidationError{}

// Validate checks the field values on KYCVendorDataMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KYCVendorDataMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KYCVendorDataMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KYCVendorDataMetadataMultiError, or nil if none found.
func (m *KYCVendorDataMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *KYCVendorDataMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	if len(errors) > 0 {
		return KYCVendorDataMetadataMultiError(errors)
	}

	return nil
}

// KYCVendorDataMetadataMultiError is an error wrapping multiple validation
// errors returned by KYCVendorDataMetadata.ValidateAll() if the designated
// constraints aren't met.
type KYCVendorDataMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KYCVendorDataMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KYCVendorDataMetadataMultiError) AllErrors() []error { return m }

// KYCVendorDataMetadataValidationError is the validation error returned by
// KYCVendorDataMetadata.Validate if the designated constraints aren't met.
type KYCVendorDataMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KYCVendorDataMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KYCVendorDataMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KYCVendorDataMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KYCVendorDataMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KYCVendorDataMetadataValidationError) ErrorName() string {
	return "KYCVendorDataMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e KYCVendorDataMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKYCVendorDataMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KYCVendorDataMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KYCVendorDataMetadataValidationError{}
