// Code generated by MockGen. DO NOT EDIT.
// Source: api/casper/exchanger/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	exchanger "github.com/epifi/gamma/api/casper/exchanger"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockExchangerOfferServiceClient is a mock of ExchangerOfferServiceClient interface.
type MockExchangerOfferServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockExchangerOfferServiceClientMockRecorder
}

// MockExchangerOfferServiceClientMockRecorder is the mock recorder for MockExchangerOfferServiceClient.
type MockExchangerOfferServiceClientMockRecorder struct {
	mock *MockExchangerOfferServiceClient
}

// NewMockExchangerOfferServiceClient creates a new mock instance.
func NewMockExchangerOfferServiceClient(ctrl *gomock.Controller) *MockExchangerOfferServiceClient {
	mock := &MockExchangerOfferServiceClient{ctrl: ctrl}
	mock.recorder = &MockExchangerOfferServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockExchangerOfferServiceClient) EXPECT() *MockExchangerOfferServiceClientMockRecorder {
	return m.recorder
}

// ChooseExchangerOrderOption mocks base method.
func (m *MockExchangerOfferServiceClient) ChooseExchangerOrderOption(ctx context.Context, in *exchanger.ChooseExchangerOrderOptionRequest, opts ...grpc.CallOption) (*exchanger.ChooseExchangerOrderOptionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ChooseExchangerOrderOption", varargs...)
	ret0, _ := ret[0].(*exchanger.ChooseExchangerOrderOptionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChooseExchangerOrderOption indicates an expected call of ChooseExchangerOrderOption.
func (mr *MockExchangerOfferServiceClientMockRecorder) ChooseExchangerOrderOption(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChooseExchangerOrderOption", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).ChooseExchangerOrderOption), varargs...)
}

// CreateExchangerOffer mocks base method.
func (m *MockExchangerOfferServiceClient) CreateExchangerOffer(ctx context.Context, in *exchanger.CreateExchangerOfferRequest, opts ...grpc.CallOption) (*exchanger.CreateExchangerOfferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateExchangerOffer", varargs...)
	ret0, _ := ret[0].(*exchanger.CreateExchangerOfferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateExchangerOffer indicates an expected call of CreateExchangerOffer.
func (mr *MockExchangerOfferServiceClientMockRecorder) CreateExchangerOffer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateExchangerOffer", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).CreateExchangerOffer), varargs...)
}

// CreateExchangerOfferGroup mocks base method.
func (m *MockExchangerOfferServiceClient) CreateExchangerOfferGroup(ctx context.Context, in *exchanger.CreateExchangerOfferGroupRequest, opts ...grpc.CallOption) (*exchanger.CreateExchangerOfferGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateExchangerOfferGroup", varargs...)
	ret0, _ := ret[0].(*exchanger.CreateExchangerOfferGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateExchangerOfferGroup indicates an expected call of CreateExchangerOfferGroup.
func (mr *MockExchangerOfferServiceClientMockRecorder) CreateExchangerOfferGroup(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateExchangerOfferGroup", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).CreateExchangerOfferGroup), varargs...)
}

// CreateExchangerOfferInventory mocks base method.
func (m *MockExchangerOfferServiceClient) CreateExchangerOfferInventory(ctx context.Context, in *exchanger.CreateExchangerOfferInventoryRequest, opts ...grpc.CallOption) (*exchanger.CreateExchangerOfferInventoryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateExchangerOfferInventory", varargs...)
	ret0, _ := ret[0].(*exchanger.CreateExchangerOfferInventoryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateExchangerOfferInventory indicates an expected call of CreateExchangerOfferInventory.
func (mr *MockExchangerOfferServiceClientMockRecorder) CreateExchangerOfferInventory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateExchangerOfferInventory", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).CreateExchangerOfferInventory), varargs...)
}

// CreateExchangerOfferListing mocks base method.
func (m *MockExchangerOfferServiceClient) CreateExchangerOfferListing(ctx context.Context, in *exchanger.CreateExchangerOfferListingRequest, opts ...grpc.CallOption) (*exchanger.CreateExchangerOfferListingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateExchangerOfferListing", varargs...)
	ret0, _ := ret[0].(*exchanger.CreateExchangerOfferListingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateExchangerOfferListing indicates an expected call of CreateExchangerOfferListing.
func (mr *MockExchangerOfferServiceClientMockRecorder) CreateExchangerOfferListing(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateExchangerOfferListing", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).CreateExchangerOfferListing), varargs...)
}

// DecryptExchangerOfferOrdersDetails mocks base method.
func (m *MockExchangerOfferServiceClient) DecryptExchangerOfferOrdersDetails(ctx context.Context, in *exchanger.DecryptExchangerOfferOrdersDetailsRequest, opts ...grpc.CallOption) (*exchanger.DecryptExchangerOfferOrdersDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DecryptExchangerOfferOrdersDetails", varargs...)
	ret0, _ := ret[0].(*exchanger.DecryptExchangerOfferOrdersDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DecryptExchangerOfferOrdersDetails indicates an expected call of DecryptExchangerOfferOrdersDetails.
func (mr *MockExchangerOfferServiceClientMockRecorder) DecryptExchangerOfferOrdersDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecryptExchangerOfferOrdersDetails", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).DecryptExchangerOfferOrdersDetails), varargs...)
}

// DeleteExchangerOfferListing mocks base method.
func (m *MockExchangerOfferServiceClient) DeleteExchangerOfferListing(ctx context.Context, in *exchanger.DeleteExchangerOfferListingRequest, opts ...grpc.CallOption) (*exchanger.DeleteExchangerOfferListingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteExchangerOfferListing", varargs...)
	ret0, _ := ret[0].(*exchanger.DeleteExchangerOfferListingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteExchangerOfferListing indicates an expected call of DeleteExchangerOfferListing.
func (mr *MockExchangerOfferServiceClientMockRecorder) DeleteExchangerOfferListing(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteExchangerOfferListing", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).DeleteExchangerOfferListing), varargs...)
}

// GetEOGroupsRewardUnitsActorUtilisation mocks base method.
func (m *MockExchangerOfferServiceClient) GetEOGroupsRewardUnitsActorUtilisation(ctx context.Context, in *exchanger.GetEOGroupsRewardUnitsActorUtilisationRequest, opts ...grpc.CallOption) (*exchanger.GetEOGroupsRewardUnitsActorUtilisationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEOGroupsRewardUnitsActorUtilisation", varargs...)
	ret0, _ := ret[0].(*exchanger.GetEOGroupsRewardUnitsActorUtilisationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEOGroupsRewardUnitsActorUtilisation indicates an expected call of GetEOGroupsRewardUnitsActorUtilisation.
func (mr *MockExchangerOfferServiceClientMockRecorder) GetEOGroupsRewardUnitsActorUtilisation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEOGroupsRewardUnitsActorUtilisation", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).GetEOGroupsRewardUnitsActorUtilisation), varargs...)
}

// GetExchangerOfferActorAttempts mocks base method.
func (m *MockExchangerOfferServiceClient) GetExchangerOfferActorAttempts(ctx context.Context, in *exchanger.GetExchangerOfferActorAttemptsRequest, opts ...grpc.CallOption) (*exchanger.GetExchangerOfferActorAttemptsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangerOfferActorAttempts", varargs...)
	ret0, _ := ret[0].(*exchanger.GetExchangerOfferActorAttemptsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOfferActorAttempts indicates an expected call of GetExchangerOfferActorAttempts.
func (mr *MockExchangerOfferServiceClientMockRecorder) GetExchangerOfferActorAttempts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOfferActorAttempts", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).GetExchangerOfferActorAttempts), varargs...)
}

// GetExchangerOfferGroupsByIds mocks base method.
func (m *MockExchangerOfferServiceClient) GetExchangerOfferGroupsByIds(ctx context.Context, in *exchanger.GetExchangerOfferGroupsByIdsRequest, opts ...grpc.CallOption) (*exchanger.GetExchangerOfferGroupsByIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangerOfferGroupsByIds", varargs...)
	ret0, _ := ret[0].(*exchanger.GetExchangerOfferGroupsByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOfferGroupsByIds indicates an expected call of GetExchangerOfferGroupsByIds.
func (mr *MockExchangerOfferServiceClientMockRecorder) GetExchangerOfferGroupsByIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOfferGroupsByIds", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).GetExchangerOfferGroupsByIds), varargs...)
}

// GetExchangerOfferOrders mocks base method.
func (m *MockExchangerOfferServiceClient) GetExchangerOfferOrders(ctx context.Context, in *exchanger.GetExchangerOfferOrdersRequest, opts ...grpc.CallOption) (*exchanger.GetExchangerOfferOrdersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangerOfferOrders", varargs...)
	ret0, _ := ret[0].(*exchanger.GetExchangerOfferOrdersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOfferOrders indicates an expected call of GetExchangerOfferOrders.
func (mr *MockExchangerOfferServiceClientMockRecorder) GetExchangerOfferOrders(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOfferOrders", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).GetExchangerOfferOrders), varargs...)
}

// GetExchangerOfferOrdersForActor mocks base method.
func (m *MockExchangerOfferServiceClient) GetExchangerOfferOrdersForActor(ctx context.Context, in *exchanger.GetExchangerOfferOrdersForActorRequest, opts ...grpc.CallOption) (*exchanger.GetExchangerOfferOrdersForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangerOfferOrdersForActor", varargs...)
	ret0, _ := ret[0].(*exchanger.GetExchangerOfferOrdersForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOfferOrdersForActor indicates an expected call of GetExchangerOfferOrdersForActor.
func (mr *MockExchangerOfferServiceClientMockRecorder) GetExchangerOfferOrdersForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOfferOrdersForActor", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).GetExchangerOfferOrdersForActor), varargs...)
}

// GetExchangerOffers mocks base method.
func (m *MockExchangerOfferServiceClient) GetExchangerOffers(ctx context.Context, in *exchanger.GetExchangerOffersRequest, opts ...grpc.CallOption) (*exchanger.GetExchangerOffersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangerOffers", varargs...)
	ret0, _ := ret[0].(*exchanger.GetExchangerOffersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOffers indicates an expected call of GetExchangerOffers.
func (mr *MockExchangerOfferServiceClientMockRecorder) GetExchangerOffers(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOffers", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).GetExchangerOffers), varargs...)
}

// GetExchangerOffersActorAttemptsCount mocks base method.
func (m *MockExchangerOfferServiceClient) GetExchangerOffersActorAttemptsCount(ctx context.Context, in *exchanger.GetExchangerOffersActorAttemptsCountRequest, opts ...grpc.CallOption) (*exchanger.GetExchangerOffersActorAttemptsCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangerOffersActorAttemptsCount", varargs...)
	ret0, _ := ret[0].(*exchanger.GetExchangerOffersActorAttemptsCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOffersActorAttemptsCount indicates an expected call of GetExchangerOffersActorAttemptsCount.
func (mr *MockExchangerOfferServiceClientMockRecorder) GetExchangerOffersActorAttemptsCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOffersActorAttemptsCount", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).GetExchangerOffersActorAttemptsCount), varargs...)
}

// GetExchangerOffersByFilters mocks base method.
func (m *MockExchangerOfferServiceClient) GetExchangerOffersByFilters(ctx context.Context, in *exchanger.GetExchangerOffersByFiltersRequest, opts ...grpc.CallOption) (*exchanger.GetExchangerOffersByFiltersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangerOffersByFilters", varargs...)
	ret0, _ := ret[0].(*exchanger.GetExchangerOffersByFiltersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOffersByFilters indicates an expected call of GetExchangerOffersByFilters.
func (mr *MockExchangerOfferServiceClientMockRecorder) GetExchangerOffersByFilters(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOffersByFilters", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).GetExchangerOffersByFilters), varargs...)
}

// GetExchangerOffersByIds mocks base method.
func (m *MockExchangerOfferServiceClient) GetExchangerOffersByIds(ctx context.Context, in *exchanger.GetExchangerOffersByIdsRequest, opts ...grpc.CallOption) (*exchanger.GetExchangerOffersByIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangerOffersByIds", varargs...)
	ret0, _ := ret[0].(*exchanger.GetExchangerOffersByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOffersByIds indicates an expected call of GetExchangerOffersByIds.
func (mr *MockExchangerOfferServiceClientMockRecorder) GetExchangerOffersByIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOffersByIds", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).GetExchangerOffersByIds), varargs...)
}

// GetExchangerOffersOrdersSummary mocks base method.
func (m *MockExchangerOfferServiceClient) GetExchangerOffersOrdersSummary(ctx context.Context, in *exchanger.GetExchangerOffersOrdersSummaryRequest, opts ...grpc.CallOption) (*exchanger.GetExchangerOffersOrdersSummaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangerOffersOrdersSummary", varargs...)
	ret0, _ := ret[0].(*exchanger.GetExchangerOffersOrdersSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOffersOrdersSummary indicates an expected call of GetExchangerOffersOrdersSummary.
func (mr *MockExchangerOfferServiceClientMockRecorder) GetExchangerOffersOrdersSummary(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOffersOrdersSummary", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).GetExchangerOffersOrdersSummary), varargs...)
}

// GetExchangerOrderById mocks base method.
func (m *MockExchangerOfferServiceClient) GetExchangerOrderById(ctx context.Context, in *exchanger.GetExchangerOrderByIdRequest, opts ...grpc.CallOption) (*exchanger.GetExchangerOrderByIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangerOrderById", varargs...)
	ret0, _ := ret[0].(*exchanger.GetExchangerOrderByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOrderById indicates an expected call of GetExchangerOrderById.
func (mr *MockExchangerOfferServiceClientMockRecorder) GetExchangerOrderById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOrderById", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).GetExchangerOrderById), varargs...)
}

// GetRedemptionCountsForActorOfferIdsInMonth mocks base method.
func (m *MockExchangerOfferServiceClient) GetRedemptionCountsForActorOfferIdsInMonth(ctx context.Context, in *exchanger.GetRedemptionCountsForActorOfferIdsInMonthRequest, opts ...grpc.CallOption) (*exchanger.GetRedemptionCountsForActorOfferIdsInMonthResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRedemptionCountsForActorOfferIdsInMonth", varargs...)
	ret0, _ := ret[0].(*exchanger.GetRedemptionCountsForActorOfferIdsInMonthResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRedemptionCountsForActorOfferIdsInMonth indicates an expected call of GetRedemptionCountsForActorOfferIdsInMonth.
func (mr *MockExchangerOfferServiceClientMockRecorder) GetRedemptionCountsForActorOfferIdsInMonth(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedemptionCountsForActorOfferIdsInMonth", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).GetRedemptionCountsForActorOfferIdsInMonth), varargs...)
}

// IncrementExchangerOfferInventory mocks base method.
func (m *MockExchangerOfferServiceClient) IncrementExchangerOfferInventory(ctx context.Context, in *exchanger.IncrementExchangerOfferInventoryRequest, opts ...grpc.CallOption) (*exchanger.IncrementExchangerOfferInventoryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IncrementExchangerOfferInventory", varargs...)
	ret0, _ := ret[0].(*exchanger.IncrementExchangerOfferInventoryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrementExchangerOfferInventory indicates an expected call of IncrementExchangerOfferInventory.
func (mr *MockExchangerOfferServiceClientMockRecorder) IncrementExchangerOfferInventory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrementExchangerOfferInventory", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).IncrementExchangerOfferInventory), varargs...)
}

// RedeemExchangerOffer mocks base method.
func (m *MockExchangerOfferServiceClient) RedeemExchangerOffer(ctx context.Context, in *exchanger.RedeemExchangerOfferRequest, opts ...grpc.CallOption) (*exchanger.RedeemExchangerOfferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RedeemExchangerOffer", varargs...)
	ret0, _ := ret[0].(*exchanger.RedeemExchangerOfferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RedeemExchangerOffer indicates an expected call of RedeemExchangerOffer.
func (mr *MockExchangerOfferServiceClientMockRecorder) RedeemExchangerOffer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RedeemExchangerOffer", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).RedeemExchangerOffer), varargs...)
}

// SubmitUserInputForChosenOption mocks base method.
func (m *MockExchangerOfferServiceClient) SubmitUserInputForChosenOption(ctx context.Context, in *exchanger.SubmitUserInputForChosenOptionRequest, opts ...grpc.CallOption) (*exchanger.SubmitUserInputForChosenOptionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitUserInputForChosenOption", varargs...)
	ret0, _ := ret[0].(*exchanger.SubmitUserInputForChosenOptionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitUserInputForChosenOption indicates an expected call of SubmitUserInputForChosenOption.
func (mr *MockExchangerOfferServiceClientMockRecorder) SubmitUserInputForChosenOption(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitUserInputForChosenOption", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).SubmitUserInputForChosenOption), varargs...)
}

// UpdateExchangerOfferDisplay mocks base method.
func (m *MockExchangerOfferServiceClient) UpdateExchangerOfferDisplay(ctx context.Context, in *exchanger.UpdateExchangerOfferDisplayRequest, opts ...grpc.CallOption) (*exchanger.UpdateExchangerOfferDisplayResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateExchangerOfferDisplay", varargs...)
	ret0, _ := ret[0].(*exchanger.UpdateExchangerOfferDisplayResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateExchangerOfferDisplay indicates an expected call of UpdateExchangerOfferDisplay.
func (mr *MockExchangerOfferServiceClientMockRecorder) UpdateExchangerOfferDisplay(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateExchangerOfferDisplay", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).UpdateExchangerOfferDisplay), varargs...)
}

// UpdateExchangerOfferListing mocks base method.
func (m *MockExchangerOfferServiceClient) UpdateExchangerOfferListing(ctx context.Context, in *exchanger.UpdateExchangerOfferListingRequest, opts ...grpc.CallOption) (*exchanger.UpdateExchangerOfferListingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateExchangerOfferListing", varargs...)
	ret0, _ := ret[0].(*exchanger.UpdateExchangerOfferListingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateExchangerOfferListing indicates an expected call of UpdateExchangerOfferListing.
func (mr *MockExchangerOfferServiceClientMockRecorder) UpdateExchangerOfferListing(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateExchangerOfferListing", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).UpdateExchangerOfferListing), varargs...)
}

// UpdateExchangerOfferStatus mocks base method.
func (m *MockExchangerOfferServiceClient) UpdateExchangerOfferStatus(ctx context.Context, in *exchanger.UpdateExchangerOfferStatusRequest, opts ...grpc.CallOption) (*exchanger.UpdateExchangerOfferStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateExchangerOfferStatus", varargs...)
	ret0, _ := ret[0].(*exchanger.UpdateExchangerOfferStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateExchangerOfferStatus indicates an expected call of UpdateExchangerOfferStatus.
func (mr *MockExchangerOfferServiceClientMockRecorder) UpdateExchangerOfferStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateExchangerOfferStatus", reflect.TypeOf((*MockExchangerOfferServiceClient)(nil).UpdateExchangerOfferStatus), varargs...)
}

// MockExchangerOfferServiceServer is a mock of ExchangerOfferServiceServer interface.
type MockExchangerOfferServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockExchangerOfferServiceServerMockRecorder
}

// MockExchangerOfferServiceServerMockRecorder is the mock recorder for MockExchangerOfferServiceServer.
type MockExchangerOfferServiceServerMockRecorder struct {
	mock *MockExchangerOfferServiceServer
}

// NewMockExchangerOfferServiceServer creates a new mock instance.
func NewMockExchangerOfferServiceServer(ctrl *gomock.Controller) *MockExchangerOfferServiceServer {
	mock := &MockExchangerOfferServiceServer{ctrl: ctrl}
	mock.recorder = &MockExchangerOfferServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockExchangerOfferServiceServer) EXPECT() *MockExchangerOfferServiceServerMockRecorder {
	return m.recorder
}

// ChooseExchangerOrderOption mocks base method.
func (m *MockExchangerOfferServiceServer) ChooseExchangerOrderOption(arg0 context.Context, arg1 *exchanger.ChooseExchangerOrderOptionRequest) (*exchanger.ChooseExchangerOrderOptionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChooseExchangerOrderOption", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.ChooseExchangerOrderOptionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChooseExchangerOrderOption indicates an expected call of ChooseExchangerOrderOption.
func (mr *MockExchangerOfferServiceServerMockRecorder) ChooseExchangerOrderOption(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChooseExchangerOrderOption", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).ChooseExchangerOrderOption), arg0, arg1)
}

// CreateExchangerOffer mocks base method.
func (m *MockExchangerOfferServiceServer) CreateExchangerOffer(arg0 context.Context, arg1 *exchanger.CreateExchangerOfferRequest) (*exchanger.CreateExchangerOfferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateExchangerOffer", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.CreateExchangerOfferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateExchangerOffer indicates an expected call of CreateExchangerOffer.
func (mr *MockExchangerOfferServiceServerMockRecorder) CreateExchangerOffer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateExchangerOffer", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).CreateExchangerOffer), arg0, arg1)
}

// CreateExchangerOfferGroup mocks base method.
func (m *MockExchangerOfferServiceServer) CreateExchangerOfferGroup(arg0 context.Context, arg1 *exchanger.CreateExchangerOfferGroupRequest) (*exchanger.CreateExchangerOfferGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateExchangerOfferGroup", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.CreateExchangerOfferGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateExchangerOfferGroup indicates an expected call of CreateExchangerOfferGroup.
func (mr *MockExchangerOfferServiceServerMockRecorder) CreateExchangerOfferGroup(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateExchangerOfferGroup", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).CreateExchangerOfferGroup), arg0, arg1)
}

// CreateExchangerOfferInventory mocks base method.
func (m *MockExchangerOfferServiceServer) CreateExchangerOfferInventory(arg0 context.Context, arg1 *exchanger.CreateExchangerOfferInventoryRequest) (*exchanger.CreateExchangerOfferInventoryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateExchangerOfferInventory", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.CreateExchangerOfferInventoryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateExchangerOfferInventory indicates an expected call of CreateExchangerOfferInventory.
func (mr *MockExchangerOfferServiceServerMockRecorder) CreateExchangerOfferInventory(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateExchangerOfferInventory", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).CreateExchangerOfferInventory), arg0, arg1)
}

// CreateExchangerOfferListing mocks base method.
func (m *MockExchangerOfferServiceServer) CreateExchangerOfferListing(arg0 context.Context, arg1 *exchanger.CreateExchangerOfferListingRequest) (*exchanger.CreateExchangerOfferListingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateExchangerOfferListing", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.CreateExchangerOfferListingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateExchangerOfferListing indicates an expected call of CreateExchangerOfferListing.
func (mr *MockExchangerOfferServiceServerMockRecorder) CreateExchangerOfferListing(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateExchangerOfferListing", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).CreateExchangerOfferListing), arg0, arg1)
}

// DecryptExchangerOfferOrdersDetails mocks base method.
func (m *MockExchangerOfferServiceServer) DecryptExchangerOfferOrdersDetails(arg0 context.Context, arg1 *exchanger.DecryptExchangerOfferOrdersDetailsRequest) (*exchanger.DecryptExchangerOfferOrdersDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecryptExchangerOfferOrdersDetails", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.DecryptExchangerOfferOrdersDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DecryptExchangerOfferOrdersDetails indicates an expected call of DecryptExchangerOfferOrdersDetails.
func (mr *MockExchangerOfferServiceServerMockRecorder) DecryptExchangerOfferOrdersDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecryptExchangerOfferOrdersDetails", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).DecryptExchangerOfferOrdersDetails), arg0, arg1)
}

// DeleteExchangerOfferListing mocks base method.
func (m *MockExchangerOfferServiceServer) DeleteExchangerOfferListing(arg0 context.Context, arg1 *exchanger.DeleteExchangerOfferListingRequest) (*exchanger.DeleteExchangerOfferListingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteExchangerOfferListing", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.DeleteExchangerOfferListingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteExchangerOfferListing indicates an expected call of DeleteExchangerOfferListing.
func (mr *MockExchangerOfferServiceServerMockRecorder) DeleteExchangerOfferListing(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteExchangerOfferListing", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).DeleteExchangerOfferListing), arg0, arg1)
}

// GetEOGroupsRewardUnitsActorUtilisation mocks base method.
func (m *MockExchangerOfferServiceServer) GetEOGroupsRewardUnitsActorUtilisation(arg0 context.Context, arg1 *exchanger.GetEOGroupsRewardUnitsActorUtilisationRequest) (*exchanger.GetEOGroupsRewardUnitsActorUtilisationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEOGroupsRewardUnitsActorUtilisation", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.GetEOGroupsRewardUnitsActorUtilisationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEOGroupsRewardUnitsActorUtilisation indicates an expected call of GetEOGroupsRewardUnitsActorUtilisation.
func (mr *MockExchangerOfferServiceServerMockRecorder) GetEOGroupsRewardUnitsActorUtilisation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEOGroupsRewardUnitsActorUtilisation", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).GetEOGroupsRewardUnitsActorUtilisation), arg0, arg1)
}

// GetExchangerOfferActorAttempts mocks base method.
func (m *MockExchangerOfferServiceServer) GetExchangerOfferActorAttempts(arg0 context.Context, arg1 *exchanger.GetExchangerOfferActorAttemptsRequest) (*exchanger.GetExchangerOfferActorAttemptsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangerOfferActorAttempts", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.GetExchangerOfferActorAttemptsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOfferActorAttempts indicates an expected call of GetExchangerOfferActorAttempts.
func (mr *MockExchangerOfferServiceServerMockRecorder) GetExchangerOfferActorAttempts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOfferActorAttempts", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).GetExchangerOfferActorAttempts), arg0, arg1)
}

// GetExchangerOfferGroupsByIds mocks base method.
func (m *MockExchangerOfferServiceServer) GetExchangerOfferGroupsByIds(arg0 context.Context, arg1 *exchanger.GetExchangerOfferGroupsByIdsRequest) (*exchanger.GetExchangerOfferGroupsByIdsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangerOfferGroupsByIds", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.GetExchangerOfferGroupsByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOfferGroupsByIds indicates an expected call of GetExchangerOfferGroupsByIds.
func (mr *MockExchangerOfferServiceServerMockRecorder) GetExchangerOfferGroupsByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOfferGroupsByIds", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).GetExchangerOfferGroupsByIds), arg0, arg1)
}

// GetExchangerOfferOrders mocks base method.
func (m *MockExchangerOfferServiceServer) GetExchangerOfferOrders(arg0 context.Context, arg1 *exchanger.GetExchangerOfferOrdersRequest) (*exchanger.GetExchangerOfferOrdersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangerOfferOrders", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.GetExchangerOfferOrdersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOfferOrders indicates an expected call of GetExchangerOfferOrders.
func (mr *MockExchangerOfferServiceServerMockRecorder) GetExchangerOfferOrders(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOfferOrders", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).GetExchangerOfferOrders), arg0, arg1)
}

// GetExchangerOfferOrdersForActor mocks base method.
func (m *MockExchangerOfferServiceServer) GetExchangerOfferOrdersForActor(arg0 context.Context, arg1 *exchanger.GetExchangerOfferOrdersForActorRequest) (*exchanger.GetExchangerOfferOrdersForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangerOfferOrdersForActor", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.GetExchangerOfferOrdersForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOfferOrdersForActor indicates an expected call of GetExchangerOfferOrdersForActor.
func (mr *MockExchangerOfferServiceServerMockRecorder) GetExchangerOfferOrdersForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOfferOrdersForActor", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).GetExchangerOfferOrdersForActor), arg0, arg1)
}

// GetExchangerOffers mocks base method.
func (m *MockExchangerOfferServiceServer) GetExchangerOffers(arg0 context.Context, arg1 *exchanger.GetExchangerOffersRequest) (*exchanger.GetExchangerOffersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangerOffers", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.GetExchangerOffersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOffers indicates an expected call of GetExchangerOffers.
func (mr *MockExchangerOfferServiceServerMockRecorder) GetExchangerOffers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOffers", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).GetExchangerOffers), arg0, arg1)
}

// GetExchangerOffersActorAttemptsCount mocks base method.
func (m *MockExchangerOfferServiceServer) GetExchangerOffersActorAttemptsCount(arg0 context.Context, arg1 *exchanger.GetExchangerOffersActorAttemptsCountRequest) (*exchanger.GetExchangerOffersActorAttemptsCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangerOffersActorAttemptsCount", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.GetExchangerOffersActorAttemptsCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOffersActorAttemptsCount indicates an expected call of GetExchangerOffersActorAttemptsCount.
func (mr *MockExchangerOfferServiceServerMockRecorder) GetExchangerOffersActorAttemptsCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOffersActorAttemptsCount", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).GetExchangerOffersActorAttemptsCount), arg0, arg1)
}

// GetExchangerOffersByFilters mocks base method.
func (m *MockExchangerOfferServiceServer) GetExchangerOffersByFilters(arg0 context.Context, arg1 *exchanger.GetExchangerOffersByFiltersRequest) (*exchanger.GetExchangerOffersByFiltersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangerOffersByFilters", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.GetExchangerOffersByFiltersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOffersByFilters indicates an expected call of GetExchangerOffersByFilters.
func (mr *MockExchangerOfferServiceServerMockRecorder) GetExchangerOffersByFilters(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOffersByFilters", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).GetExchangerOffersByFilters), arg0, arg1)
}

// GetExchangerOffersByIds mocks base method.
func (m *MockExchangerOfferServiceServer) GetExchangerOffersByIds(arg0 context.Context, arg1 *exchanger.GetExchangerOffersByIdsRequest) (*exchanger.GetExchangerOffersByIdsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangerOffersByIds", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.GetExchangerOffersByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOffersByIds indicates an expected call of GetExchangerOffersByIds.
func (mr *MockExchangerOfferServiceServerMockRecorder) GetExchangerOffersByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOffersByIds", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).GetExchangerOffersByIds), arg0, arg1)
}

// GetExchangerOffersOrdersSummary mocks base method.
func (m *MockExchangerOfferServiceServer) GetExchangerOffersOrdersSummary(arg0 context.Context, arg1 *exchanger.GetExchangerOffersOrdersSummaryRequest) (*exchanger.GetExchangerOffersOrdersSummaryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangerOffersOrdersSummary", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.GetExchangerOffersOrdersSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOffersOrdersSummary indicates an expected call of GetExchangerOffersOrdersSummary.
func (mr *MockExchangerOfferServiceServerMockRecorder) GetExchangerOffersOrdersSummary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOffersOrdersSummary", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).GetExchangerOffersOrdersSummary), arg0, arg1)
}

// GetExchangerOrderById mocks base method.
func (m *MockExchangerOfferServiceServer) GetExchangerOrderById(arg0 context.Context, arg1 *exchanger.GetExchangerOrderByIdRequest) (*exchanger.GetExchangerOrderByIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangerOrderById", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.GetExchangerOrderByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOrderById indicates an expected call of GetExchangerOrderById.
func (mr *MockExchangerOfferServiceServerMockRecorder) GetExchangerOrderById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOrderById", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).GetExchangerOrderById), arg0, arg1)
}

// GetRedemptionCountsForActorOfferIdsInMonth mocks base method.
func (m *MockExchangerOfferServiceServer) GetRedemptionCountsForActorOfferIdsInMonth(arg0 context.Context, arg1 *exchanger.GetRedemptionCountsForActorOfferIdsInMonthRequest) (*exchanger.GetRedemptionCountsForActorOfferIdsInMonthResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedemptionCountsForActorOfferIdsInMonth", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.GetRedemptionCountsForActorOfferIdsInMonthResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRedemptionCountsForActorOfferIdsInMonth indicates an expected call of GetRedemptionCountsForActorOfferIdsInMonth.
func (mr *MockExchangerOfferServiceServerMockRecorder) GetRedemptionCountsForActorOfferIdsInMonth(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedemptionCountsForActorOfferIdsInMonth", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).GetRedemptionCountsForActorOfferIdsInMonth), arg0, arg1)
}

// IncrementExchangerOfferInventory mocks base method.
func (m *MockExchangerOfferServiceServer) IncrementExchangerOfferInventory(arg0 context.Context, arg1 *exchanger.IncrementExchangerOfferInventoryRequest) (*exchanger.IncrementExchangerOfferInventoryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrementExchangerOfferInventory", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.IncrementExchangerOfferInventoryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrementExchangerOfferInventory indicates an expected call of IncrementExchangerOfferInventory.
func (mr *MockExchangerOfferServiceServerMockRecorder) IncrementExchangerOfferInventory(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrementExchangerOfferInventory", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).IncrementExchangerOfferInventory), arg0, arg1)
}

// RedeemExchangerOffer mocks base method.
func (m *MockExchangerOfferServiceServer) RedeemExchangerOffer(arg0 context.Context, arg1 *exchanger.RedeemExchangerOfferRequest) (*exchanger.RedeemExchangerOfferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RedeemExchangerOffer", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.RedeemExchangerOfferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RedeemExchangerOffer indicates an expected call of RedeemExchangerOffer.
func (mr *MockExchangerOfferServiceServerMockRecorder) RedeemExchangerOffer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RedeemExchangerOffer", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).RedeemExchangerOffer), arg0, arg1)
}

// SubmitUserInputForChosenOption mocks base method.
func (m *MockExchangerOfferServiceServer) SubmitUserInputForChosenOption(arg0 context.Context, arg1 *exchanger.SubmitUserInputForChosenOptionRequest) (*exchanger.SubmitUserInputForChosenOptionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitUserInputForChosenOption", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.SubmitUserInputForChosenOptionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitUserInputForChosenOption indicates an expected call of SubmitUserInputForChosenOption.
func (mr *MockExchangerOfferServiceServerMockRecorder) SubmitUserInputForChosenOption(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitUserInputForChosenOption", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).SubmitUserInputForChosenOption), arg0, arg1)
}

// UpdateExchangerOfferDisplay mocks base method.
func (m *MockExchangerOfferServiceServer) UpdateExchangerOfferDisplay(arg0 context.Context, arg1 *exchanger.UpdateExchangerOfferDisplayRequest) (*exchanger.UpdateExchangerOfferDisplayResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateExchangerOfferDisplay", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.UpdateExchangerOfferDisplayResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateExchangerOfferDisplay indicates an expected call of UpdateExchangerOfferDisplay.
func (mr *MockExchangerOfferServiceServerMockRecorder) UpdateExchangerOfferDisplay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateExchangerOfferDisplay", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).UpdateExchangerOfferDisplay), arg0, arg1)
}

// UpdateExchangerOfferListing mocks base method.
func (m *MockExchangerOfferServiceServer) UpdateExchangerOfferListing(arg0 context.Context, arg1 *exchanger.UpdateExchangerOfferListingRequest) (*exchanger.UpdateExchangerOfferListingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateExchangerOfferListing", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.UpdateExchangerOfferListingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateExchangerOfferListing indicates an expected call of UpdateExchangerOfferListing.
func (mr *MockExchangerOfferServiceServerMockRecorder) UpdateExchangerOfferListing(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateExchangerOfferListing", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).UpdateExchangerOfferListing), arg0, arg1)
}

// UpdateExchangerOfferStatus mocks base method.
func (m *MockExchangerOfferServiceServer) UpdateExchangerOfferStatus(arg0 context.Context, arg1 *exchanger.UpdateExchangerOfferStatusRequest) (*exchanger.UpdateExchangerOfferStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateExchangerOfferStatus", arg0, arg1)
	ret0, _ := ret[0].(*exchanger.UpdateExchangerOfferStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateExchangerOfferStatus indicates an expected call of UpdateExchangerOfferStatus.
func (mr *MockExchangerOfferServiceServerMockRecorder) UpdateExchangerOfferStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateExchangerOfferStatus", reflect.TypeOf((*MockExchangerOfferServiceServer)(nil).UpdateExchangerOfferStatus), arg0, arg1)
}

// MockUnsafeExchangerOfferServiceServer is a mock of UnsafeExchangerOfferServiceServer interface.
type MockUnsafeExchangerOfferServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeExchangerOfferServiceServerMockRecorder
}

// MockUnsafeExchangerOfferServiceServerMockRecorder is the mock recorder for MockUnsafeExchangerOfferServiceServer.
type MockUnsafeExchangerOfferServiceServerMockRecorder struct {
	mock *MockUnsafeExchangerOfferServiceServer
}

// NewMockUnsafeExchangerOfferServiceServer creates a new mock instance.
func NewMockUnsafeExchangerOfferServiceServer(ctrl *gomock.Controller) *MockUnsafeExchangerOfferServiceServer {
	mock := &MockUnsafeExchangerOfferServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeExchangerOfferServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeExchangerOfferServiceServer) EXPECT() *MockUnsafeExchangerOfferServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedExchangerOfferServiceServer mocks base method.
func (m *MockUnsafeExchangerOfferServiceServer) mustEmbedUnimplementedExchangerOfferServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedExchangerOfferServiceServer")
}

// mustEmbedUnimplementedExchangerOfferServiceServer indicates an expected call of mustEmbedUnimplementedExchangerOfferServiceServer.
func (mr *MockUnsafeExchangerOfferServiceServerMockRecorder) mustEmbedUnimplementedExchangerOfferServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedExchangerOfferServiceServer", reflect.TypeOf((*MockUnsafeExchangerOfferServiceServer)(nil).mustEmbedUnimplementedExchangerOfferServiceServer))
}
