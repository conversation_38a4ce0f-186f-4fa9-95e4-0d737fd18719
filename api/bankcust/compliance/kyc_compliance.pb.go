//go:generate gen_sql -types=ProfileUpdateDetails,ComplianceMetadata

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/bankcust/compliance/kyc_compliance.proto

package compliance

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AadhaarUpdateStatus int32

const (
	AadhaarUpdateStatus_AADHAAR_UPDATE_STATUS_UNSPECIFIED AadhaarUpdateStatus = 0
	AadhaarUpdateStatus_AADHAAR_UPDATE_STATUS_IN_PROGRESS AadhaarUpdateStatus = 1
	AadhaarUpdateStatus_AADHAAR_UPDATE_STATUS_COMPLETED   AadhaarUpdateStatus = 2
	AadhaarUpdateStatus_AADHAAR_UPDATE_STATUS_FAILED      AadhaarUpdateStatus = 3
)

// Enum value maps for AadhaarUpdateStatus.
var (
	AadhaarUpdateStatus_name = map[int32]string{
		0: "AADHAAR_UPDATE_STATUS_UNSPECIFIED",
		1: "AADHAAR_UPDATE_STATUS_IN_PROGRESS",
		2: "AADHAAR_UPDATE_STATUS_COMPLETED",
		3: "AADHAAR_UPDATE_STATUS_FAILED",
	}
	AadhaarUpdateStatus_value = map[string]int32{
		"AADHAAR_UPDATE_STATUS_UNSPECIFIED": 0,
		"AADHAAR_UPDATE_STATUS_IN_PROGRESS": 1,
		"AADHAAR_UPDATE_STATUS_COMPLETED":   2,
		"AADHAAR_UPDATE_STATUS_FAILED":      3,
	}
)

func (x AadhaarUpdateStatus) Enum() *AadhaarUpdateStatus {
	p := new(AadhaarUpdateStatus)
	*p = x
	return p
}

func (x AadhaarUpdateStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AadhaarUpdateStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_bankcust_compliance_kyc_compliance_proto_enumTypes[0].Descriptor()
}

func (AadhaarUpdateStatus) Type() protoreflect.EnumType {
	return &file_api_bankcust_compliance_kyc_compliance_proto_enumTypes[0]
}

func (x AadhaarUpdateStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AadhaarUpdateStatus.Descriptor instead.
func (AadhaarUpdateStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_bankcust_compliance_kyc_compliance_proto_rawDescGZIP(), []int{0}
}

type AadhaarUpdateFailureReason int32

const (
	AadhaarUpdateFailureReason_AADHAAR_UPDATE_FAILURE_REASON_UNSPECIFIED                      AadhaarUpdateFailureReason = 0
	AadhaarUpdateFailureReason_AADHAAR_UPDATE_FAILURE_REASON_AADHAAR_MOBILE_VALIDATION_FAILED AadhaarUpdateFailureReason = 1
	AadhaarUpdateFailureReason_AADHAAR_UPDATE_FAILURE_REASON_NSDL_PAN_VALIDATION_FAILED       AadhaarUpdateFailureReason = 2
	AadhaarUpdateFailureReason_AADHAAR_UPDATE_FAILURE_REASON_IDFY_PAN_VALIDATION_FAILED       AadhaarUpdateFailureReason = 3
	AadhaarUpdateFailureReason_AADHAAR_UPDATE_FAILURE_REASON_PROFILE_UPDATE_FAILED            AadhaarUpdateFailureReason = 4
)

// Enum value maps for AadhaarUpdateFailureReason.
var (
	AadhaarUpdateFailureReason_name = map[int32]string{
		0: "AADHAAR_UPDATE_FAILURE_REASON_UNSPECIFIED",
		1: "AADHAAR_UPDATE_FAILURE_REASON_AADHAAR_MOBILE_VALIDATION_FAILED",
		2: "AADHAAR_UPDATE_FAILURE_REASON_NSDL_PAN_VALIDATION_FAILED",
		3: "AADHAAR_UPDATE_FAILURE_REASON_IDFY_PAN_VALIDATION_FAILED",
		4: "AADHAAR_UPDATE_FAILURE_REASON_PROFILE_UPDATE_FAILED",
	}
	AadhaarUpdateFailureReason_value = map[string]int32{
		"AADHAAR_UPDATE_FAILURE_REASON_UNSPECIFIED":                      0,
		"AADHAAR_UPDATE_FAILURE_REASON_AADHAAR_MOBILE_VALIDATION_FAILED": 1,
		"AADHAAR_UPDATE_FAILURE_REASON_NSDL_PAN_VALIDATION_FAILED":       2,
		"AADHAAR_UPDATE_FAILURE_REASON_IDFY_PAN_VALIDATION_FAILED":       3,
		"AADHAAR_UPDATE_FAILURE_REASON_PROFILE_UPDATE_FAILED":            4,
	}
)

func (x AadhaarUpdateFailureReason) Enum() *AadhaarUpdateFailureReason {
	p := new(AadhaarUpdateFailureReason)
	*p = x
	return p
}

func (x AadhaarUpdateFailureReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AadhaarUpdateFailureReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_bankcust_compliance_kyc_compliance_proto_enumTypes[1].Descriptor()
}

func (AadhaarUpdateFailureReason) Type() protoreflect.EnumType {
	return &file_api_bankcust_compliance_kyc_compliance_proto_enumTypes[1]
}

func (x AadhaarUpdateFailureReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AadhaarUpdateFailureReason.Descriptor instead.
func (AadhaarUpdateFailureReason) EnumDescriptor() ([]byte, []int) {
	return file_api_bankcust_compliance_kyc_compliance_proto_rawDescGZIP(), []int{1}
}

type KYCCompliance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string               `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ActorId string               `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Vendor  vendorgateway.Vendor `protobuf:"varint,3,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	// customer id of user received in csv(as of now) for whom kyc is pending
	VendorCustomerId string `protobuf:"bytes,4,opt,name=vendor_customer_id,json=vendorCustomerId,proto3" json:"vendor_customer_id,omitempty"`
	// KYCComplianceStatus indicate whether the user kyc is complied/verified or due
	KYCComplianceStatus KYCComplianceStatus `protobuf:"varint,5,opt,name=k_y_c_compliance_status,json=kYCComplianceStatus,proto3,enum=compliance.KYCComplianceStatus" json:"k_y_c_compliance_status,omitempty"`
	// AttemptSource indicate source which created the entry
	AttemptSource AttemptSource `protobuf:"varint,6,opt,name=attempt_source,json=attemptSource,proto3,enum=compliance.AttemptSource" json:"attempt_source,omitempty"`
	// kyc_complied_at indicate timestamp when the users kyc was verified
	KYCCompliedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=k_y_c_complied_at,json=kYCCompliedAt,proto3" json:"k_y_c_complied_at,omitempty"`
	// kyc_due_at indicate timestamp before kyc need to be done, this timestamp is without grace period
	// if delayed beyond grace period account is put on debit freeze.
	KYCDueAt      *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=k_y_c_due_at,json=kYCDueAt,proto3" json:"k_y_c_due_at,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAtUnix int64                  `protobuf:"varint,11,opt,name=deleted_at_unix,json=deletedAtUnix,proto3" json:"deleted_at_unix,omitempty"`
	// user_sms_triggered_at indicate timestamp at which sms was sent by client for reKYC
	UserSmsTriggeredAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=user_sms_triggered_at,json=userSmsTriggeredAt,proto3" json:"user_sms_triggered_at,omitempty"`
	// kyc_cta_clicked_at indicate timestamp at which kyc cta was clicked
	KYCCtaClickedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=k_y_c_cta_clicked_at,json=kYCCtaClickedAt,proto3" json:"k_y_c_cta_clicked_at,omitempty"`
	// kyc_grace_period_timestamp indicate timestamp at grace period after which account may freeze
	KYCGracePeriodTimestamp *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=k_y_c_grace_period_timestamp,json=kYCGracePeriodTimestamp,proto3" json:"k_y_c_grace_period_timestamp,omitempty"`
	// ProfileUpdateDetails holds data regarding ProfileUpdate for periodic KYC process.
	ProfileUpdateDetails *ProfileUpdateDetails `protobuf:"bytes,15,opt,name=profile_update_details,json=profileUpdateDetails,proto3" json:"profile_update_details,omitempty"`
	// ComplianceMetadata holds data regarding compliance metadata.
	ComplianceMetadata *ComplianceMetadata `protobuf:"bytes,16,opt,name=compliance_metadata,json=complianceMetadata,proto3" json:"compliance_metadata,omitempty"`
}

func (x *KYCCompliance) Reset() {
	*x = KYCCompliance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_bankcust_compliance_kyc_compliance_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KYCCompliance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KYCCompliance) ProtoMessage() {}

func (x *KYCCompliance) ProtoReflect() protoreflect.Message {
	mi := &file_api_bankcust_compliance_kyc_compliance_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KYCCompliance.ProtoReflect.Descriptor instead.
func (*KYCCompliance) Descriptor() ([]byte, []int) {
	return file_api_bankcust_compliance_kyc_compliance_proto_rawDescGZIP(), []int{0}
}

func (x *KYCCompliance) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *KYCCompliance) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *KYCCompliance) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *KYCCompliance) GetVendorCustomerId() string {
	if x != nil {
		return x.VendorCustomerId
	}
	return ""
}

func (x *KYCCompliance) GetKYCComplianceStatus() KYCComplianceStatus {
	if x != nil {
		return x.KYCComplianceStatus
	}
	return KYCComplianceStatus_KYC_COMPLIANCE_STATUS_UNSPECIFIED
}

func (x *KYCCompliance) GetAttemptSource() AttemptSource {
	if x != nil {
		return x.AttemptSource
	}
	return AttemptSource_ATTEMPT_SOURCE_UNSPECIFIED
}

func (x *KYCCompliance) GetKYCCompliedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.KYCCompliedAt
	}
	return nil
}

func (x *KYCCompliance) GetKYCDueAt() *timestamppb.Timestamp {
	if x != nil {
		return x.KYCDueAt
	}
	return nil
}

func (x *KYCCompliance) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *KYCCompliance) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *KYCCompliance) GetDeletedAtUnix() int64 {
	if x != nil {
		return x.DeletedAtUnix
	}
	return 0
}

func (x *KYCCompliance) GetUserSmsTriggeredAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UserSmsTriggeredAt
	}
	return nil
}

func (x *KYCCompliance) GetKYCCtaClickedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.KYCCtaClickedAt
	}
	return nil
}

func (x *KYCCompliance) GetKYCGracePeriodTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.KYCGracePeriodTimestamp
	}
	return nil
}

func (x *KYCCompliance) GetProfileUpdateDetails() *ProfileUpdateDetails {
	if x != nil {
		return x.ProfileUpdateDetails
	}
	return nil
}

func (x *KYCCompliance) GetComplianceMetadata() *ComplianceMetadata {
	if x != nil {
		return x.ComplianceMetadata
	}
	return nil
}

type ProfileUpdateDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ProfileUpdateRequestId indicates the request ID that was used to trigger the profile update at the bank.
	ProfileUpdateRequestId string `protobuf:"bytes,1,opt,name=profile_update_request_id,json=profileUpdateRequestId,proto3" json:"profile_update_request_id,omitempty"`
	// EKYCClientRequestId indicates the request id that is used for EKYC process for the purpose of ReKYC.
	EKYCClientRequestId string `protobuf:"bytes,2,opt,name=e_k_y_c_client_request_id,json=eKYCClientRequestId,proto3" json:"e_k_y_c_client_request_id,omitempty"`
	// EKYCCompletedAt indicates the timestamp when EKYC was attempted for the process of ReKYC.
	EKYCCompletedAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=e_k_y_c_completed_at,json=eKYCCompletedAt,proto3" json:"e_k_y_c_completed_at,omitempty"`
	// ProfileUpdateStartedAt indicates the timestamp when Profile Update for triggered for ReKYC purposes.
	ProfileUpdateStartedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=profile_update_started_at,json=profileUpdateStartedAt,proto3" json:"profile_update_started_at,omitempty"`
	// AadhaarUpdateDetails holds data regarding ProfileUpdate for Aadhaar update details
	AadhaarUpdateDetails *AadhaarUpdateDetails `protobuf:"bytes,5,opt,name=aadhaar_update_details,json=aadhaarUpdateDetails,proto3" json:"aadhaar_update_details,omitempty"`
}

func (x *ProfileUpdateDetails) Reset() {
	*x = ProfileUpdateDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_bankcust_compliance_kyc_compliance_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProfileUpdateDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileUpdateDetails) ProtoMessage() {}

func (x *ProfileUpdateDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_bankcust_compliance_kyc_compliance_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileUpdateDetails.ProtoReflect.Descriptor instead.
func (*ProfileUpdateDetails) Descriptor() ([]byte, []int) {
	return file_api_bankcust_compliance_kyc_compliance_proto_rawDescGZIP(), []int{1}
}

func (x *ProfileUpdateDetails) GetProfileUpdateRequestId() string {
	if x != nil {
		return x.ProfileUpdateRequestId
	}
	return ""
}

func (x *ProfileUpdateDetails) GetEKYCClientRequestId() string {
	if x != nil {
		return x.EKYCClientRequestId
	}
	return ""
}

func (x *ProfileUpdateDetails) GetEKYCCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EKYCCompletedAt
	}
	return nil
}

func (x *ProfileUpdateDetails) GetProfileUpdateStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ProfileUpdateStartedAt
	}
	return nil
}

func (x *ProfileUpdateDetails) GetAadhaarUpdateDetails() *AadhaarUpdateDetails {
	if x != nil {
		return x.AadhaarUpdateDetails
	}
	return nil
}

type AadhaarUpdateDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ProfileUpdateRequestId indicates the request ID that was used to trigger the profile update at the bank.
	ProfileUpdateRequestId string `protobuf:"bytes,1,opt,name=profile_update_request_id,json=profileUpdateRequestId,proto3" json:"profile_update_request_id,omitempty"`
	// EKYCClientRequestId indicates the request id that is used for EKYC process for the purpose of Aadhaar update.
	EKYCClientRequestId string `protobuf:"bytes,2,opt,name=e_k_y_c_client_request_id,json=eKYCClientRequestId,proto3" json:"e_k_y_c_client_request_id,omitempty"`
	// EKYCCompletedAt indicates the timestamp when EKYC was attempted for the process of Aadhaar update.
	EKYCCompletedAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=e_k_y_c_completed_at,json=eKYCCompletedAt,proto3" json:"e_k_y_c_completed_at,omitempty"`
	// ProfileUpdateStartedAt indicates the timestamp when Profile Update triggered for Aadhaar update purposes.
	ProfileUpdateStartedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=profile_update_started_at,json=profileUpdateStartedAt,proto3" json:"profile_update_started_at,omitempty"`
	// AadhaarUpdateStatus states the status of aadhaar update request
	AadhaarUpdateStatus AadhaarUpdateStatus `protobuf:"varint,5,opt,name=aadhaar_update_status,json=aadhaarUpdateStatus,proto3,enum=compliance.AadhaarUpdateStatus" json:"aadhaar_update_status,omitempty"`
	// AadhaarUpdateFailureReason contains the failure reason for aadhaar update
	AadhaarUpdateFailureReason AadhaarUpdateFailureReason `protobuf:"varint,6,opt,name=aadhaar_update_failure_reason,json=aadhaarUpdateFailureReason,proto3,enum=compliance.AadhaarUpdateFailureReason" json:"aadhaar_update_failure_reason,omitempty"`
}

func (x *AadhaarUpdateDetails) Reset() {
	*x = AadhaarUpdateDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_bankcust_compliance_kyc_compliance_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AadhaarUpdateDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AadhaarUpdateDetails) ProtoMessage() {}

func (x *AadhaarUpdateDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_bankcust_compliance_kyc_compliance_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AadhaarUpdateDetails.ProtoReflect.Descriptor instead.
func (*AadhaarUpdateDetails) Descriptor() ([]byte, []int) {
	return file_api_bankcust_compliance_kyc_compliance_proto_rawDescGZIP(), []int{2}
}

func (x *AadhaarUpdateDetails) GetProfileUpdateRequestId() string {
	if x != nil {
		return x.ProfileUpdateRequestId
	}
	return ""
}

func (x *AadhaarUpdateDetails) GetEKYCClientRequestId() string {
	if x != nil {
		return x.EKYCClientRequestId
	}
	return ""
}

func (x *AadhaarUpdateDetails) GetEKYCCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EKYCCompletedAt
	}
	return nil
}

func (x *AadhaarUpdateDetails) GetProfileUpdateStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ProfileUpdateStartedAt
	}
	return nil
}

func (x *AadhaarUpdateDetails) GetAadhaarUpdateStatus() AadhaarUpdateStatus {
	if x != nil {
		return x.AadhaarUpdateStatus
	}
	return AadhaarUpdateStatus_AADHAAR_UPDATE_STATUS_UNSPECIFIED
}

func (x *AadhaarUpdateDetails) GetAadhaarUpdateFailureReason() AadhaarUpdateFailureReason {
	if x != nil {
		return x.AadhaarUpdateFailureReason
	}
	return AadhaarUpdateFailureReason_AADHAAR_UPDATE_FAILURE_REASON_UNSPECIFIED
}

type ComplianceMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// should_show_re_kyc_success_banner indicates whether a success banner should be shown to the user
	// upon the completion of a re-KYC
	// BOOLEAN_ENUM_UNSPECIFIED: Default value, don't show the banner
	// BOOLEAN_ENUM_FALSE: This means user has acknowledged the banner, so don't show it again
	// BOOLEAN_ENUM_TRUE: Show the banner
	ShouldShowReKycSuccessBanner common.BooleanEnum `protobuf:"varint,1,opt,name=should_show_re_kyc_success_banner,json=shouldShowReKycSuccessBanner,proto3,enum=api.typesv2.common.BooleanEnum" json:"should_show_re_kyc_success_banner,omitempty"`
}

func (x *ComplianceMetadata) Reset() {
	*x = ComplianceMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_bankcust_compliance_kyc_compliance_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComplianceMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComplianceMetadata) ProtoMessage() {}

func (x *ComplianceMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_bankcust_compliance_kyc_compliance_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComplianceMetadata.ProtoReflect.Descriptor instead.
func (*ComplianceMetadata) Descriptor() ([]byte, []int) {
	return file_api_bankcust_compliance_kyc_compliance_proto_rawDescGZIP(), []int{3}
}

func (x *ComplianceMetadata) GetShouldShowReKycSuccessBanner() common.BooleanEnum {
	if x != nil {
		return x.ShouldShowReKycSuccessBanner
	}
	return common.BooleanEnum(0)
}

var File_api_bankcust_compliance_kyc_compliance_proto protoreflect.FileDescriptor

var file_api_bankcust_compliance_kyc_compliance_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x61, 0x6e, 0x6b, 0x63, 0x75, 0x73, 0x74, 0x2f, 0x63,
	0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x6b, 0x79, 0x63, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f,
	0x62, 0x61, 0x6e, 0x6b, 0x63, 0x75, 0x73, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61,
	0x6e, 0x63, 0x65, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xee, 0x07, 0x0a, 0x0d, 0x4b, 0x59, 0x43, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2d,
	0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x2c, 0x0a,
	0x12, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x55, 0x0a, 0x17, 0x6b,
	0x5f, 0x79, 0x5f, 0x63, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x4b, 0x59, 0x43, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x13, 0x6b,
	0x59, 0x43, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x40, 0x0a, 0x0e, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0d, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x44, 0x0a, 0x11, 0x6b, 0x5f, 0x79, 0x5f, 0x63, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x6b, 0x59, 0x43,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3a, 0x0a, 0x0c, 0x6b, 0x5f,
	0x79, 0x5f, 0x63, 0x5f, 0x64, 0x75, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x6b, 0x59,
	0x43, 0x44, 0x75, 0x65, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x26, 0x0a, 0x0f,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x5f, 0x75, 0x6e, 0x69, 0x78, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x55, 0x6e, 0x69, 0x78, 0x12, 0x4d, 0x0a, 0x15, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x6d, 0x73,
	0x5f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x12, 0x75, 0x73, 0x65, 0x72, 0x53, 0x6d, 0x73, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x49, 0x0a, 0x14, 0x6b, 0x5f, 0x79, 0x5f, 0x63, 0x5f, 0x63, 0x74, 0x61,
	0x5f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x6b,
	0x59, 0x43, 0x43, 0x74, 0x61, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x41, 0x74, 0x12, 0x59,
	0x0a, 0x1c, 0x6b, 0x5f, 0x79, 0x5f, 0x63, 0x5f, 0x67, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x70, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x17, 0x6b, 0x59, 0x43, 0x47, 0x72, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x56, 0x0a, 0x16, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x14, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x4f, 0x0a, 0x13, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70,
	0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x12,
	0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x83, 0x03, 0x0a, 0x14, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x39, 0x0a, 0x19, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x19, 0x65, 0x5f, 0x6b, 0x5f, 0x79, 0x5f,
	0x63, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x65, 0x4b, 0x59, 0x43, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x49,
	0x0a, 0x14, 0x65, 0x5f, 0x6b, 0x5f, 0x79, 0x5f, 0x63, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x65, 0x4b, 0x59, 0x43, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x55, 0x0a, 0x19, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x16, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x56, 0x0a, 0x16, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x41, 0x61,
	0x64, 0x68, 0x61, 0x61, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x14, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xeb, 0x03, 0x0a, 0x14, 0x41, 0x61, 0x64,
	0x68, 0x61, 0x61, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x39, 0x0a, 0x19, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x19,
	0x65, 0x5f, 0x6b, 0x5f, 0x79, 0x5f, 0x63, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x65, 0x4b, 0x59, 0x43, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x14, 0x65, 0x5f, 0x6b, 0x5f, 0x79, 0x5f, 0x63, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f,
	0x65, 0x4b, 0x59, 0x43, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x55, 0x0a, 0x19, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x16,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x53, 0x0a, 0x15, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61,
	0x72, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e,
	0x63, 0x65, 0x2e, 0x41, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x13, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x69, 0x0a, 0x1d, 0x61,
	0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e,
	0x41, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x1a, 0x61, 0x61, 0x64, 0x68,
	0x61, 0x61, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x7e, 0x0a, 0x12, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x69,
	0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x68, 0x0a, 0x21,
	0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x72, 0x65, 0x5f, 0x6b,
	0x79, 0x63, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f,
	0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x1c, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64,
	0x53, 0x68, 0x6f, 0x77, 0x52, 0x65, 0x4b, 0x79, 0x63, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2a, 0xaa, 0x01, 0x0a, 0x13, 0x41, 0x61, 0x64, 0x68, 0x61,
	0x61, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25,
	0x0a, 0x21, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49,
	0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f,
	0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10,
	0x02, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x03, 0x2a, 0xc4, 0x02, 0x0a, 0x1a, 0x41, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x29, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x42, 0x0a, 0x3e, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c,
	0x45, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x3c, 0x0a, 0x38, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4e, 0x53, 0x44, 0x4c, 0x5f, 0x50, 0x41, 0x4e, 0x5f,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x02, 0x12, 0x3c, 0x0a, 0x38, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45,
	0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x49, 0x44, 0x46, 0x59, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x03, 0x12, 0x37, 0x0a, 0x33, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x42, 0x60, 0x0a, 0x2e, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x61, 0x6e, 0x6b, 0x63, 0x75, 0x73,
	0x74, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5a, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x61, 0x6e, 0x6b, 0x63, 0x75, 0x73,
	0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_bankcust_compliance_kyc_compliance_proto_rawDescOnce sync.Once
	file_api_bankcust_compliance_kyc_compliance_proto_rawDescData = file_api_bankcust_compliance_kyc_compliance_proto_rawDesc
)

func file_api_bankcust_compliance_kyc_compliance_proto_rawDescGZIP() []byte {
	file_api_bankcust_compliance_kyc_compliance_proto_rawDescOnce.Do(func() {
		file_api_bankcust_compliance_kyc_compliance_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_bankcust_compliance_kyc_compliance_proto_rawDescData)
	})
	return file_api_bankcust_compliance_kyc_compliance_proto_rawDescData
}

var file_api_bankcust_compliance_kyc_compliance_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_bankcust_compliance_kyc_compliance_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_bankcust_compliance_kyc_compliance_proto_goTypes = []interface{}{
	(AadhaarUpdateStatus)(0),        // 0: compliance.AadhaarUpdateStatus
	(AadhaarUpdateFailureReason)(0), // 1: compliance.AadhaarUpdateFailureReason
	(*KYCCompliance)(nil),           // 2: compliance.KYCCompliance
	(*ProfileUpdateDetails)(nil),    // 3: compliance.ProfileUpdateDetails
	(*AadhaarUpdateDetails)(nil),    // 4: compliance.AadhaarUpdateDetails
	(*ComplianceMetadata)(nil),      // 5: compliance.ComplianceMetadata
	(vendorgateway.Vendor)(0),       // 6: vendorgateway.Vendor
	(KYCComplianceStatus)(0),        // 7: compliance.KYCComplianceStatus
	(AttemptSource)(0),              // 8: compliance.AttemptSource
	(*timestamppb.Timestamp)(nil),   // 9: google.protobuf.Timestamp
	(common.BooleanEnum)(0),         // 10: api.typesv2.common.BooleanEnum
}
var file_api_bankcust_compliance_kyc_compliance_proto_depIdxs = []int32{
	6,  // 0: compliance.KYCCompliance.vendor:type_name -> vendorgateway.Vendor
	7,  // 1: compliance.KYCCompliance.k_y_c_compliance_status:type_name -> compliance.KYCComplianceStatus
	8,  // 2: compliance.KYCCompliance.attempt_source:type_name -> compliance.AttemptSource
	9,  // 3: compliance.KYCCompliance.k_y_c_complied_at:type_name -> google.protobuf.Timestamp
	9,  // 4: compliance.KYCCompliance.k_y_c_due_at:type_name -> google.protobuf.Timestamp
	9,  // 5: compliance.KYCCompliance.created_at:type_name -> google.protobuf.Timestamp
	9,  // 6: compliance.KYCCompliance.updated_at:type_name -> google.protobuf.Timestamp
	9,  // 7: compliance.KYCCompliance.user_sms_triggered_at:type_name -> google.protobuf.Timestamp
	9,  // 8: compliance.KYCCompliance.k_y_c_cta_clicked_at:type_name -> google.protobuf.Timestamp
	9,  // 9: compliance.KYCCompliance.k_y_c_grace_period_timestamp:type_name -> google.protobuf.Timestamp
	3,  // 10: compliance.KYCCompliance.profile_update_details:type_name -> compliance.ProfileUpdateDetails
	5,  // 11: compliance.KYCCompliance.compliance_metadata:type_name -> compliance.ComplianceMetadata
	9,  // 12: compliance.ProfileUpdateDetails.e_k_y_c_completed_at:type_name -> google.protobuf.Timestamp
	9,  // 13: compliance.ProfileUpdateDetails.profile_update_started_at:type_name -> google.protobuf.Timestamp
	4,  // 14: compliance.ProfileUpdateDetails.aadhaar_update_details:type_name -> compliance.AadhaarUpdateDetails
	9,  // 15: compliance.AadhaarUpdateDetails.e_k_y_c_completed_at:type_name -> google.protobuf.Timestamp
	9,  // 16: compliance.AadhaarUpdateDetails.profile_update_started_at:type_name -> google.protobuf.Timestamp
	0,  // 17: compliance.AadhaarUpdateDetails.aadhaar_update_status:type_name -> compliance.AadhaarUpdateStatus
	1,  // 18: compliance.AadhaarUpdateDetails.aadhaar_update_failure_reason:type_name -> compliance.AadhaarUpdateFailureReason
	10, // 19: compliance.ComplianceMetadata.should_show_re_kyc_success_banner:type_name -> api.typesv2.common.BooleanEnum
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_api_bankcust_compliance_kyc_compliance_proto_init() }
func file_api_bankcust_compliance_kyc_compliance_proto_init() {
	if File_api_bankcust_compliance_kyc_compliance_proto != nil {
		return
	}
	file_api_bankcust_compliance_enum_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_bankcust_compliance_kyc_compliance_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KYCCompliance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_bankcust_compliance_kyc_compliance_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProfileUpdateDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_bankcust_compliance_kyc_compliance_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AadhaarUpdateDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_bankcust_compliance_kyc_compliance_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComplianceMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_bankcust_compliance_kyc_compliance_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_bankcust_compliance_kyc_compliance_proto_goTypes,
		DependencyIndexes: file_api_bankcust_compliance_kyc_compliance_proto_depIdxs,
		EnumInfos:         file_api_bankcust_compliance_kyc_compliance_proto_enumTypes,
		MessageInfos:      file_api_bankcust_compliance_kyc_compliance_proto_msgTypes,
	}.Build()
	File_api_bankcust_compliance_kyc_compliance_proto = out.File
	file_api_bankcust_compliance_kyc_compliance_proto_rawDesc = nil
	file_api_bankcust_compliance_kyc_compliance_proto_goTypes = nil
	file_api_bankcust_compliance_kyc_compliance_proto_depIdxs = nil
}
