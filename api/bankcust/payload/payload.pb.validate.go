// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/bankcust/payload/payload.proto

package payload

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	bankcust "github.com/epifi/gamma/api/bankcust"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = bankcust.ChequebookStatus(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on ProvisionChequebookPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProvisionChequebookPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProvisionChequebookPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProvisionChequebookPayloadMultiError, or nil if none found.
func (m *ProvisionChequebookPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *ProvisionChequebookPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetTrackDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProvisionChequebookPayloadValidationError{
					field:  "TrackDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProvisionChequebookPayloadValidationError{
					field:  "TrackDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTrackDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProvisionChequebookPayloadValidationError{
				field:  "TrackDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProvisionedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProvisionChequebookPayloadValidationError{
					field:  "ProvisionedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProvisionChequebookPayloadValidationError{
					field:  "ProvisionedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProvisionedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProvisionChequebookPayloadValidationError{
				field:  "ProvisionedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Charges

	if len(errors) > 0 {
		return ProvisionChequebookPayloadMultiError(errors)
	}

	return nil
}

// ProvisionChequebookPayloadMultiError is an error wrapping multiple
// validation errors returned by ProvisionChequebookPayload.ValidateAll() if
// the designated constraints aren't met.
type ProvisionChequebookPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProvisionChequebookPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProvisionChequebookPayloadMultiError) AllErrors() []error { return m }

// ProvisionChequebookPayloadValidationError is the validation error returned
// by ProvisionChequebookPayload.Validate if the designated constraints aren't met.
type ProvisionChequebookPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProvisionChequebookPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProvisionChequebookPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProvisionChequebookPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProvisionChequebookPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProvisionChequebookPayloadValidationError) ErrorName() string {
	return "ProvisionChequebookPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e ProvisionChequebookPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProvisionChequebookPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProvisionChequebookPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProvisionChequebookPayloadValidationError{}

// Validate checks the field values on ProvisionChequebookSignalPayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProvisionChequebookSignalPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProvisionChequebookSignalPayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProvisionChequebookSignalPayloadMultiError, or nil if none found.
func (m *ProvisionChequebookSignalPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *ProvisionChequebookSignalPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChequebookStatus

	if len(errors) > 0 {
		return ProvisionChequebookSignalPayloadMultiError(errors)
	}

	return nil
}

// ProvisionChequebookSignalPayloadMultiError is an error wrapping multiple
// validation errors returned by
// ProvisionChequebookSignalPayload.ValidateAll() if the designated
// constraints aren't met.
type ProvisionChequebookSignalPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProvisionChequebookSignalPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProvisionChequebookSignalPayloadMultiError) AllErrors() []error { return m }

// ProvisionChequebookSignalPayloadValidationError is the validation error
// returned by ProvisionChequebookSignalPayload.Validate if the designated
// constraints aren't met.
type ProvisionChequebookSignalPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProvisionChequebookSignalPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProvisionChequebookSignalPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProvisionChequebookSignalPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProvisionChequebookSignalPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProvisionChequebookSignalPayloadValidationError) ErrorName() string {
	return "ProvisionChequebookSignalPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e ProvisionChequebookSignalPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProvisionChequebookSignalPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProvisionChequebookSignalPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProvisionChequebookSignalPayloadValidationError{}

// Validate checks the field values on ProfileUpdateWorkflowPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProfileUpdateWorkflowPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProfileUpdateWorkflowPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProfileUpdateWorkflowPayloadMultiError, or nil if none found.
func (m *ProfileUpdateWorkflowPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *ProfileUpdateWorkflowPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for ActorId

	// no validation rules for Vendor

	if all {
		switch v := interface{}(m.GetInputDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileUpdateWorkflowPayloadValidationError{
					field:  "InputDob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileUpdateWorkflowPayloadValidationError{
					field:  "InputDob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInputDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileUpdateWorkflowPayloadValidationError{
				field:  "InputDob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmploymentData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileUpdateWorkflowPayloadValidationError{
					field:  "EmploymentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileUpdateWorkflowPayloadValidationError{
					field:  "EmploymentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmploymentData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileUpdateWorkflowPayloadValidationError{
				field:  "EmploymentData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVendorRequestStartedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileUpdateWorkflowPayloadValidationError{
					field:  "VendorRequestStartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileUpdateWorkflowPayloadValidationError{
					field:  "VendorRequestStartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorRequestStartedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileUpdateWorkflowPayloadValidationError{
				field:  "VendorRequestStartedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InputPan

	// no validation rules for EkycRrn

	if len(errors) > 0 {
		return ProfileUpdateWorkflowPayloadMultiError(errors)
	}

	return nil
}

// ProfileUpdateWorkflowPayloadMultiError is an error wrapping multiple
// validation errors returned by ProfileUpdateWorkflowPayload.ValidateAll() if
// the designated constraints aren't met.
type ProfileUpdateWorkflowPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProfileUpdateWorkflowPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProfileUpdateWorkflowPayloadMultiError) AllErrors() []error { return m }

// ProfileUpdateWorkflowPayloadValidationError is the validation error returned
// by ProfileUpdateWorkflowPayload.Validate if the designated constraints
// aren't met.
type ProfileUpdateWorkflowPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProfileUpdateWorkflowPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProfileUpdateWorkflowPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProfileUpdateWorkflowPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProfileUpdateWorkflowPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProfileUpdateWorkflowPayloadValidationError) ErrorName() string {
	return "ProfileUpdateWorkflowPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e ProfileUpdateWorkflowPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProfileUpdateWorkflowPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProfileUpdateWorkflowPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProfileUpdateWorkflowPayloadValidationError{}
