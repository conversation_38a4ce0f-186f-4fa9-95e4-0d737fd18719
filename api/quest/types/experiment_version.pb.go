// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/quest/types/experiment_version.proto

package types

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ExperimentVersionStatus int32

const (
	ExperimentVersionStatus_EXPERIMENT_VERSION_STATUS_UNSPECIFIED          ExperimentVersionStatus = 0
	ExperimentVersionStatus_EXPERIMENT_VERSION_STATUS_DRAFT                ExperimentVersionStatus = 1
	ExperimentVersionStatus_EXPERIMENT_VERSION_STATUS_WAITING_FOR_APPROVAL ExperimentVersionStatus = 2
	ExperimentVersionStatus_EXPERIMENT_VERSION_STATUS_APPROVED             ExperimentVersionStatus = 3
	ExperimentVersionStatus_EXPERIMENT_VERSION_STATUS_DECLINED             ExperimentVersionStatus = 4
)

// Enum value maps for ExperimentVersionStatus.
var (
	ExperimentVersionStatus_name = map[int32]string{
		0: "EXPERIMENT_VERSION_STATUS_UNSPECIFIED",
		1: "EXPERIMENT_VERSION_STATUS_DRAFT",
		2: "EXPERIMENT_VERSION_STATUS_WAITING_FOR_APPROVAL",
		3: "EXPERIMENT_VERSION_STATUS_APPROVED",
		4: "EXPERIMENT_VERSION_STATUS_DECLINED",
	}
	ExperimentVersionStatus_value = map[string]int32{
		"EXPERIMENT_VERSION_STATUS_UNSPECIFIED":          0,
		"EXPERIMENT_VERSION_STATUS_DRAFT":                1,
		"EXPERIMENT_VERSION_STATUS_WAITING_FOR_APPROVAL": 2,
		"EXPERIMENT_VERSION_STATUS_APPROVED":             3,
		"EXPERIMENT_VERSION_STATUS_DECLINED":             4,
	}
)

func (x ExperimentVersionStatus) Enum() *ExperimentVersionStatus {
	p := new(ExperimentVersionStatus)
	*p = x
	return p
}

func (x ExperimentVersionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExperimentVersionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_quest_types_experiment_version_proto_enumTypes[0].Descriptor()
}

func (ExperimentVersionStatus) Type() protoreflect.EnumType {
	return &file_api_quest_types_experiment_version_proto_enumTypes[0]
}

func (x ExperimentVersionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExperimentVersionStatus.Descriptor instead.
func (ExperimentVersionStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_quest_types_experiment_version_proto_rawDescGZIP(), []int{0}
}

type ExperimentVersion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 string                  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Description        string                  `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Status             ExperimentVersionStatus `protobuf:"varint,3,opt,name=status,proto3,enum=api.quest.types.ExperimentVersionStatus" json:"status,omitempty"`
	ExpData            *Experiment             `protobuf:"bytes,4,opt,name=exp_data,json=expData,proto3" json:"exp_data,omitempty"`
	RequestedReviewers []string                `protobuf:"bytes,5,rep,name=requested_reviewers,json=requestedReviewers,proto3" json:"requested_reviewers,omitempty"`
	CreatedBy          string                  `protobuf:"bytes,6,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	ProcessedBy        string                  `protobuf:"bytes,7,opt,name=processed_by,json=processedBy,proto3" json:"processed_by,omitempty"`
	CreatedAt          *timestamppb.Timestamp  `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt          *timestamppb.Timestamp  `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt          *timestamppb.Timestamp  `protobuf:"bytes,10,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *ExperimentVersion) Reset() {
	*x = ExperimentVersion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_quest_types_experiment_version_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExperimentVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExperimentVersion) ProtoMessage() {}

func (x *ExperimentVersion) ProtoReflect() protoreflect.Message {
	mi := &file_api_quest_types_experiment_version_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExperimentVersion.ProtoReflect.Descriptor instead.
func (*ExperimentVersion) Descriptor() ([]byte, []int) {
	return file_api_quest_types_experiment_version_proto_rawDescGZIP(), []int{0}
}

func (x *ExperimentVersion) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ExperimentVersion) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ExperimentVersion) GetStatus() ExperimentVersionStatus {
	if x != nil {
		return x.Status
	}
	return ExperimentVersionStatus_EXPERIMENT_VERSION_STATUS_UNSPECIFIED
}

func (x *ExperimentVersion) GetExpData() *Experiment {
	if x != nil {
		return x.ExpData
	}
	return nil
}

func (x *ExperimentVersion) GetRequestedReviewers() []string {
	if x != nil {
		return x.RequestedReviewers
	}
	return nil
}

func (x *ExperimentVersion) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *ExperimentVersion) GetProcessedBy() string {
	if x != nil {
		return x.ProcessedBy
	}
	return ""
}

func (x *ExperimentVersion) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ExperimentVersion) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *ExperimentVersion) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_quest_types_experiment_version_proto protoreflect.FileDescriptor

var file_api_quest_types_experiment_version_proto_rawDesc = []byte{
	0x0a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2f, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x61, 0x70, 0x69, 0x2e,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x1a, 0x20, 0x61, 0x70, 0x69,
	0x2f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x65, 0x78, 0x70,
	0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe3,
	0x03, 0x0a, 0x11, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d,
	0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x45, 0x78, 0x70,
	0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x65, 0x78, 0x70, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x2f, 0x0a, 0x13, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65,
	0x64, 0x42, 0x79, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x2a, 0xed, 0x01, 0x0a, 0x17, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d,
	0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x29, 0x0a, 0x25, 0x45, 0x58, 0x50, 0x45, 0x52, 0x49, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x56,
	0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x45,
	0x58, 0x50, 0x45, 0x52, 0x49, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x52, 0x41, 0x46, 0x54, 0x10, 0x01,
	0x12, 0x32, 0x0a, 0x2e, 0x45, 0x58, 0x50, 0x45, 0x52, 0x49, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x56,
	0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x57, 0x41,
	0x49, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56,
	0x41, 0x4c, 0x10, 0x02, 0x12, 0x26, 0x0a, 0x22, 0x45, 0x58, 0x50, 0x45, 0x52, 0x49, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x03, 0x12, 0x26, 0x0a, 0x22,
	0x45, 0x58, 0x50, 0x45, 0x52, 0x49, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x49, 0x4e,
	0x45, 0x44, 0x10, 0x04, 0x42, 0x50, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x5a, 0x26,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_quest_types_experiment_version_proto_rawDescOnce sync.Once
	file_api_quest_types_experiment_version_proto_rawDescData = file_api_quest_types_experiment_version_proto_rawDesc
)

func file_api_quest_types_experiment_version_proto_rawDescGZIP() []byte {
	file_api_quest_types_experiment_version_proto_rawDescOnce.Do(func() {
		file_api_quest_types_experiment_version_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_quest_types_experiment_version_proto_rawDescData)
	})
	return file_api_quest_types_experiment_version_proto_rawDescData
}

var file_api_quest_types_experiment_version_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_quest_types_experiment_version_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_quest_types_experiment_version_proto_goTypes = []interface{}{
	(ExperimentVersionStatus)(0),  // 0: api.quest.types.ExperimentVersionStatus
	(*ExperimentVersion)(nil),     // 1: api.quest.types.ExperimentVersion
	(*Experiment)(nil),            // 2: api.quest.types.Experiment
	(*timestamppb.Timestamp)(nil), // 3: google.protobuf.Timestamp
}
var file_api_quest_types_experiment_version_proto_depIdxs = []int32{
	0, // 0: api.quest.types.ExperimentVersion.status:type_name -> api.quest.types.ExperimentVersionStatus
	2, // 1: api.quest.types.ExperimentVersion.exp_data:type_name -> api.quest.types.Experiment
	3, // 2: api.quest.types.ExperimentVersion.created_at:type_name -> google.protobuf.Timestamp
	3, // 3: api.quest.types.ExperimentVersion.updated_at:type_name -> google.protobuf.Timestamp
	3, // 4: api.quest.types.ExperimentVersion.deleted_at:type_name -> google.protobuf.Timestamp
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_quest_types_experiment_version_proto_init() }
func file_api_quest_types_experiment_version_proto_init() {
	if File_api_quest_types_experiment_version_proto != nil {
		return
	}
	file_api_quest_types_experiment_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_quest_types_experiment_version_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExperimentVersion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_quest_types_experiment_version_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_quest_types_experiment_version_proto_goTypes,
		DependencyIndexes: file_api_quest_types_experiment_version_proto_depIdxs,
		EnumInfos:         file_api_quest_types_experiment_version_proto_enumTypes,
		MessageInfos:      file_api_quest_types_experiment_version_proto_msgTypes,
	}.Build()
	File_api_quest_types_experiment_version_proto = out.File
	file_api_quest_types_experiment_version_proto_rawDesc = nil
	file_api_quest_types_experiment_version_proto_goTypes = nil
	file_api_quest_types_experiment_version_proto_depIdxs = nil
}
