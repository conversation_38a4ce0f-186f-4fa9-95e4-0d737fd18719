syntax = "proto3";

package api.quest.types;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/be-common/api/quest/types";
option java_package = "com.github.epifi.be-common.api.quest.types";

// Layer model holds all the information to serve layer
message Layer {
  // unique id
  string id = 1;
  // unique name
  string name = 2;
  // parent layer
  string parent_layer = 3;
  // layer can reuse buckets from sibling experiments if overlapping is allowed
  bool is_overlapping_allowed = 4;
  // list of variable expressions for the layer
  repeated string variable_expressions = 5;
  // status of layer
  // 1. ACTIVE
  // 2. ARCHIVED
  LayerStatus status = 6;
  // maximum number of buckets that the experiments in this layer and its successor can have.
  uint32 max_user_segment_bucket = 10;
  // maximum number of buckets that an experiment can have in this layer and its successor.
  uint32 max_user_segment_bucket_for_exp = 11;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
  google.protobuf.Timestamp deleted_at = 9;
}

enum LayerStatus {
  LAYER_STATUS_UNSPECIFIED = 0;
  // Layer is live and being evaluated
  LAYER_STATUS_ACTIVE = 1;
  // Layer is deleted and archived
  LAYER_STATUS_ARCHIVED = 2;
}
