syntax = "proto3";

package insights.model;

import "api/insights/model/insight_variable_value_pair.proto";
import "api/typesv2/common/user_group.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/insights/model";
option java_package = "com.github.epifi.gamma.api.insights.model";

// One framework can have multiple Segment
// ex. "Hurray, you spend <percentage>% less last month on <merchant> compared to previous month" is a framework
// Now based on value of merchant, we can divide this framework into different segments
// ex. "Hurray, you spend <percentage>% less last month on amazon compared to previous month" is a segment
// ex. "Hurray, you spend <percentage>% less last month on flipkart compared to previous month" is a segment

message InsightSegment {
  // Unique Identifier for each record (primary key)
  string id = 1;

  // Foreign key of framework table
  string framework_id = 2;

  // segment_details defines values of all the segment_variables of insight framework to which this segment belongs
  // {merchant: amazon}
  repeated InsightVariableValuePair segment_details = 3;

  // Defines default values for variables (present in framework)
  // if generation script doesn't create values for any variable, their default value will be picked from here.
  // ex. {icon_url: "www.iconurl.in/abcd"}
  repeated InsightVariableValuePair default_values = 8;

  // defines the user group who is eligible to see this insight e.g. Internal / fnf / All users
  api.typesv2.common.UserGroup release_group = 4;

  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  google.protobuf.Timestamp deleted_at = 7;

  // expression that will be evaluated before an insight of this segment is selected to be shown to the user
  // NOTE: framework also has a validation_expression expression which if exists will also be evaluated along with this expression
  string validation_expression = 9;
}

// InsightSegmentFieldMask is the enum representation of all the InsightSegment fields.
// Meant to be used as field mask to help with database updates
enum InsightSegmentFieldMask {
  INSIGHT_SEGMENT_FIELD_MASK_UNSPECIFIED = 0;
  INSIGHT_SEGMENT_FIELD_MASK_ID = 1;
  INSIGHT_SEGMENT_FIELD_MASK_FRAMEWORK_ID = 2;
  INSIGHT_SEGMENT_FIELD_MASK_SEGMENT_DETAILS = 3;
  INSIGHT_SEGMENT_FIELD_MASK_RELEASE_GROUP = 4;
  INSIGHT_SEGMENT_FIELD_MASK_CREATED_AT = 5;
  INSIGHT_SEGMENT_FIELD_MASK_UPDATED_AT = 6;
  INSIGHT_SEGMENT_FIELD_MASK_DELETED_AT = 7;
  INSIGHT_SEGMENT_FIELD_MASK_DEFAULT_VALUES = 8;
  INSIGHT_SEGMENT_FIELD_MASK_VALIDATION_EXPRESSION = 9;
}
