// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/insights/story/model/story_group.proto

package model

import (
	story "github.com/epifi/gamma/api/webfe/story"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// A story gorup is created based on some theme, topic etc.
// Each story group will consist a set of stories that are shown together to the user.
type StoryGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name StoryGroupName `protobuf:"varint,2,opt,name=name,proto3,enum=insights.story.model.StoryGroupName" json:"name,omitempty"`
	// time after which story group becomes eligible to be shown to the user
	ValidFrom *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=valid_from,json=validFrom,proto3" json:"valid_from,omitempty"`
	// time before which story is eligible to be shown to the user.
	ValidTill *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=valid_till,json=validTill,proto3" json:"valid_till,omitempty"`
	// display_details will contain config/details related to how
	// a story group will visually appear to the user.
	DisplayDetails *StoryGroupDisplayDetails `protobuf:"bytes,5,opt,name=display_details,json=displayDetails,proto3" json:"display_details,omitempty"`
	// Share related details e.g. story share text
	ShareDetails *ShareDetails `protobuf:"bytes,6,opt,name=share_details,json=shareDetails,proto3" json:"share_details,omitempty"`
}

func (x *StoryGroup) Reset() {
	*x = StoryGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_story_model_story_group_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoryGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryGroup) ProtoMessage() {}

func (x *StoryGroup) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_story_model_story_group_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryGroup.ProtoReflect.Descriptor instead.
func (*StoryGroup) Descriptor() ([]byte, []int) {
	return file_api_insights_story_model_story_group_proto_rawDescGZIP(), []int{0}
}

func (x *StoryGroup) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StoryGroup) GetName() StoryGroupName {
	if x != nil {
		return x.Name
	}
	return StoryGroupName_STORY_GROUP_NAME_UNSPECIFIED
}

func (x *StoryGroup) GetValidFrom() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidFrom
	}
	return nil
}

func (x *StoryGroup) GetValidTill() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidTill
	}
	return nil
}

func (x *StoryGroup) GetDisplayDetails() *StoryGroupDisplayDetails {
	if x != nil {
		return x.DisplayDetails
	}
	return nil
}

func (x *StoryGroup) GetShareDetails() *ShareDetails {
	if x != nil {
		return x.ShareDetails
	}
	return nil
}

type StoryGroupDisplayDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *StoryGroupHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *StoryGroupDisplayDetails) Reset() {
	*x = StoryGroupDisplayDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_story_model_story_group_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoryGroupDisplayDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryGroupDisplayDetails) ProtoMessage() {}

func (x *StoryGroupDisplayDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_story_model_story_group_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryGroupDisplayDetails.ProtoReflect.Descriptor instead.
func (*StoryGroupDisplayDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_story_model_story_group_proto_rawDescGZIP(), []int{1}
}

func (x *StoryGroupDisplayDetails) GetHeader() *StoryGroupHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

type StoryGroupHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Heading *story.StoryGroupHeading `protobuf:"bytes,1,opt,name=heading,proto3" json:"heading,omitempty"`
}

func (x *StoryGroupHeader) Reset() {
	*x = StoryGroupHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_story_model_story_group_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoryGroupHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryGroupHeader) ProtoMessage() {}

func (x *StoryGroupHeader) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_story_model_story_group_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryGroupHeader.ProtoReflect.Descriptor instead.
func (*StoryGroupHeader) Descriptor() ([]byte, []int) {
	return file_api_insights_story_model_story_group_proto_rawDescGZIP(), []int{2}
}

func (x *StoryGroupHeader) GetHeading() *story.StoryGroupHeading {
	if x != nil {
		return x.Heading
	}
	return nil
}

type ShareDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// share text required to share with a story
	StoryShareText string `protobuf:"bytes,1,opt,name=story_share_text,json=storyShareText,proto3" json:"story_share_text,omitempty"`
}

func (x *ShareDetails) Reset() {
	*x = ShareDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_story_model_story_group_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShareDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShareDetails) ProtoMessage() {}

func (x *ShareDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_story_model_story_group_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShareDetails.ProtoReflect.Descriptor instead.
func (*ShareDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_story_model_story_group_proto_rawDescGZIP(), []int{3}
}

func (x *ShareDetails) GetStoryShareText() string {
	if x != nil {
		return x.StoryShareText
	}
	return ""
}

var File_api_insights_story_model_story_group_proto protoreflect.FileDescriptor

var file_api_insights_story_model_story_group_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x77,
	0x65, 0x62, 0x66, 0x65, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xee, 0x02, 0x0a, 0x0a,
	0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x38, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x66, 0x72,
	0x6f, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x12,
	0x39, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x54, 0x69, 0x6c, 0x6c, 0x12, 0x57, 0x0a, 0x0f, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x79,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x47, 0x0a, 0x0d, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x53, 0x68, 0x61, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c,
	0x73, 0x68, 0x61, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x5a, 0x0a, 0x18,
	0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x4c, 0x0a, 0x10, 0x53, 0x74, 0x6f, 0x72,
	0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x07,
	0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x53, 0x74, 0x6f, 0x72,
	0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x48, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x68,
	0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x22, 0x38, 0x0a, 0x0c, 0x53, 0x68, 0x61, 0x72, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f,
	0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x68, 0x61, 0x72, 0x65, 0x54, 0x65, 0x78, 0x74,
	0x42, 0x62, 0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_insights_story_model_story_group_proto_rawDescOnce sync.Once
	file_api_insights_story_model_story_group_proto_rawDescData = file_api_insights_story_model_story_group_proto_rawDesc
)

func file_api_insights_story_model_story_group_proto_rawDescGZIP() []byte {
	file_api_insights_story_model_story_group_proto_rawDescOnce.Do(func() {
		file_api_insights_story_model_story_group_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_insights_story_model_story_group_proto_rawDescData)
	})
	return file_api_insights_story_model_story_group_proto_rawDescData
}

var file_api_insights_story_model_story_group_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_insights_story_model_story_group_proto_goTypes = []interface{}{
	(*StoryGroup)(nil),               // 0: insights.story.model.StoryGroup
	(*StoryGroupDisplayDetails)(nil), // 1: insights.story.model.StoryGroupDisplayDetails
	(*StoryGroupHeader)(nil),         // 2: insights.story.model.StoryGroupHeader
	(*ShareDetails)(nil),             // 3: insights.story.model.ShareDetails
	(StoryGroupName)(0),              // 4: insights.story.model.StoryGroupName
	(*timestamppb.Timestamp)(nil),    // 5: google.protobuf.Timestamp
	(*story.StoryGroupHeading)(nil),  // 6: webfe.story.StoryGroupHeading
}
var file_api_insights_story_model_story_group_proto_depIdxs = []int32{
	4, // 0: insights.story.model.StoryGroup.name:type_name -> insights.story.model.StoryGroupName
	5, // 1: insights.story.model.StoryGroup.valid_from:type_name -> google.protobuf.Timestamp
	5, // 2: insights.story.model.StoryGroup.valid_till:type_name -> google.protobuf.Timestamp
	1, // 3: insights.story.model.StoryGroup.display_details:type_name -> insights.story.model.StoryGroupDisplayDetails
	3, // 4: insights.story.model.StoryGroup.share_details:type_name -> insights.story.model.ShareDetails
	2, // 5: insights.story.model.StoryGroupDisplayDetails.header:type_name -> insights.story.model.StoryGroupHeader
	6, // 6: insights.story.model.StoryGroupHeader.heading:type_name -> webfe.story.StoryGroupHeading
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_insights_story_model_story_group_proto_init() }
func file_api_insights_story_model_story_group_proto_init() {
	if File_api_insights_story_model_story_group_proto != nil {
		return
	}
	file_api_insights_story_model_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_insights_story_model_story_group_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoryGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_story_model_story_group_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoryGroupDisplayDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_story_model_story_group_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoryGroupHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_story_model_story_group_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShareDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_insights_story_model_story_group_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_insights_story_model_story_group_proto_goTypes,
		DependencyIndexes: file_api_insights_story_model_story_group_proto_depIdxs,
		MessageInfos:      file_api_insights_story_model_story_group_proto_msgTypes,
	}.Build()
	File_api_insights_story_model_story_group_proto = out.File
	file_api_insights_story_model_story_group_proto_rawDesc = nil
	file_api_insights_story_model_story_group_proto_goTypes = nil
	file_api_insights_story_model_story_group_proto_depIdxs = nil
}
