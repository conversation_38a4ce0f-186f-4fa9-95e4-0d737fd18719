// Code generated by MockGen. DO NOT EDIT.
// Source: api/preapprovedloan/lendability/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	lendability "github.com/epifi/gamma/api/preapprovedloan/lendability"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockLendabilityClient is a mock of LendabilityClient interface.
type MockLendabilityClient struct {
	ctrl     *gomock.Controller
	recorder *MockLendabilityClientMockRecorder
}

// MockLendabilityClientMockRecorder is the mock recorder for MockLendabilityClient.
type MockLendabilityClientMockRecorder struct {
	mock *MockLendabilityClient
}

// NewMockLendabilityClient creates a new mock instance.
func NewMockLendabilityClient(ctrl *gomock.Controller) *MockLendabilityClient {
	mock := &MockLendabilityClient{ctrl: ctrl}
	mock.recorder = &MockLendabilityClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLendabilityClient) EXPECT() *MockLendabilityClientMockRecorder {
	return m.recorder
}

// GetActorLendability mocks base method.
func (m *MockLendabilityClient) GetActorLendability(ctx context.Context, in *lendability.GetActorLendabilityRequest, opts ...grpc.CallOption) (*lendability.GetActorLendabilityResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetActorLendability", varargs...)
	ret0, _ := ret[0].(*lendability.GetActorLendabilityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActorLendability indicates an expected call of GetActorLendability.
func (mr *MockLendabilityClientMockRecorder) GetActorLendability(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActorLendability", reflect.TypeOf((*MockLendabilityClient)(nil).GetActorLendability), varargs...)
}

// MockLendabilityServer is a mock of LendabilityServer interface.
type MockLendabilityServer struct {
	ctrl     *gomock.Controller
	recorder *MockLendabilityServerMockRecorder
}

// MockLendabilityServerMockRecorder is the mock recorder for MockLendabilityServer.
type MockLendabilityServerMockRecorder struct {
	mock *MockLendabilityServer
}

// NewMockLendabilityServer creates a new mock instance.
func NewMockLendabilityServer(ctrl *gomock.Controller) *MockLendabilityServer {
	mock := &MockLendabilityServer{ctrl: ctrl}
	mock.recorder = &MockLendabilityServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLendabilityServer) EXPECT() *MockLendabilityServerMockRecorder {
	return m.recorder
}

// GetActorLendability mocks base method.
func (m *MockLendabilityServer) GetActorLendability(arg0 context.Context, arg1 *lendability.GetActorLendabilityRequest) (*lendability.GetActorLendabilityResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActorLendability", arg0, arg1)
	ret0, _ := ret[0].(*lendability.GetActorLendabilityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActorLendability indicates an expected call of GetActorLendability.
func (mr *MockLendabilityServerMockRecorder) GetActorLendability(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActorLendability", reflect.TypeOf((*MockLendabilityServer)(nil).GetActorLendability), arg0, arg1)
}

// MockUnsafeLendabilityServer is a mock of UnsafeLendabilityServer interface.
type MockUnsafeLendabilityServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeLendabilityServerMockRecorder
}

// MockUnsafeLendabilityServerMockRecorder is the mock recorder for MockUnsafeLendabilityServer.
type MockUnsafeLendabilityServerMockRecorder struct {
	mock *MockUnsafeLendabilityServer
}

// NewMockUnsafeLendabilityServer creates a new mock instance.
func NewMockUnsafeLendabilityServer(ctrl *gomock.Controller) *MockUnsafeLendabilityServer {
	mock := &MockUnsafeLendabilityServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeLendabilityServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeLendabilityServer) EXPECT() *MockUnsafeLendabilityServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedLendabilityServer mocks base method.
func (m *MockUnsafeLendabilityServer) mustEmbedUnimplementedLendabilityServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedLendabilityServer")
}

// mustEmbedUnimplementedLendabilityServer indicates an expected call of mustEmbedUnimplementedLendabilityServer.
func (mr *MockUnsafeLendabilityServerMockRecorder) mustEmbedUnimplementedLendabilityServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedLendabilityServer", reflect.TypeOf((*MockUnsafeLendabilityServer)(nil).mustEmbedUnimplementedLendabilityServer))
}
