//go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/preapprovedloan/consumer/service.proto

package consumer

import (
	queue "github.com/epifi/be-common/api/queue"
	s3 "github.com/epifi/gamma/api/aws/s3"
	order "github.com/epifi/gamma/api/order"
	fiftyfin "github.com/epifi/gamma/api/vendornotification/lending/loans/fiftyfin"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProcessEligibleUsersFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// The notification message that Amazon S3 sends to publish an event is in the JSON format
	Records []*s3.Record `protobuf:"bytes,2,rep,name=records,json=Records,proto3" json:"records,omitempty"`
}

func (x *ProcessEligibleUsersFileRequest) Reset() {
	*x = ProcessEligibleUsersFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_consumer_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessEligibleUsersFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessEligibleUsersFileRequest) ProtoMessage() {}

func (x *ProcessEligibleUsersFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_consumer_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessEligibleUsersFileRequest.ProtoReflect.Descriptor instead.
func (*ProcessEligibleUsersFileRequest) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_consumer_service_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessEligibleUsersFileRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessEligibleUsersFileRequest) GetRecords() []*s3.Record {
	if x != nil {
		return x.Records
	}
	return nil
}

type ProcessEligibleUsersFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessEligibleUsersFileResponse) Reset() {
	*x = ProcessEligibleUsersFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_consumer_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessEligibleUsersFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessEligibleUsersFileResponse) ProtoMessage() {}

func (x *ProcessEligibleUsersFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_consumer_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessEligibleUsersFileResponse.ProtoReflect.Descriptor instead.
func (*ProcessEligibleUsersFileResponse) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_consumer_service_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessEligibleUsersFileResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessLoanOfferCsvFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// The notification message that Amazon S3 sends to publish an event is in the JSON format
	Records []*s3.Record `protobuf:"bytes,2,rep,name=records,json=Records,proto3" json:"records,omitempty"`
}

func (x *ProcessLoanOfferCsvFileRequest) Reset() {
	*x = ProcessLoanOfferCsvFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_consumer_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessLoanOfferCsvFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessLoanOfferCsvFileRequest) ProtoMessage() {}

func (x *ProcessLoanOfferCsvFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_consumer_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessLoanOfferCsvFileRequest.ProtoReflect.Descriptor instead.
func (*ProcessLoanOfferCsvFileRequest) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_consumer_service_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessLoanOfferCsvFileRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessLoanOfferCsvFileRequest) GetRecords() []*s3.Record {
	if x != nil {
		return x.Records
	}
	return nil
}

type ProcessLoanOfferCsvFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessLoanOfferCsvFileResponse) Reset() {
	*x = ProcessLoanOfferCsvFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_consumer_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessLoanOfferCsvFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessLoanOfferCsvFileResponse) ProtoMessage() {}

func (x *ProcessLoanOfferCsvFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_consumer_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessLoanOfferCsvFileResponse.ProtoReflect.Descriptor instead.
func (*ProcessLoanOfferCsvFileResponse) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_consumer_service_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessLoanOfferCsvFileResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessOrderUpdatePacketResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessOrderUpdatePacketResponse) Reset() {
	*x = ProcessOrderUpdatePacketResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_consumer_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessOrderUpdatePacketResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessOrderUpdatePacketResponse) ProtoMessage() {}

func (x *ProcessOrderUpdatePacketResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_consumer_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessOrderUpdatePacketResponse.ProtoReflect.Descriptor instead.
func (*ProcessOrderUpdatePacketResponse) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_consumer_service_proto_rawDescGZIP(), []int{4}
}

func (x *ProcessOrderUpdatePacketResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessLoansFiftyfinCallbackEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessLoansFiftyfinCallbackEventResponse) Reset() {
	*x = ProcessLoansFiftyfinCallbackEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_consumer_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessLoansFiftyfinCallbackEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessLoansFiftyfinCallbackEventResponse) ProtoMessage() {}

func (x *ProcessLoansFiftyfinCallbackEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_consumer_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessLoansFiftyfinCallbackEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessLoansFiftyfinCallbackEventResponse) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_consumer_service_proto_rawDescGZIP(), []int{5}
}

func (x *ProcessLoansFiftyfinCallbackEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_preapprovedloan_consumer_service_proto protoreflect.FileDescriptor

var file_api_preapprovedloan_consumer_service_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x77, 0x73, 0x2f,
	0x73, 0x33, 0x2f, 0x73, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x61, 0x70, 0x69,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x39, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2f, 0x66, 0x69, 0x66, 0x74, 0x79,
	0x66, 0x69, 0x6e, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x90, 0x01, 0x0a, 0x1f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x77, 0x73, 0x2e,
	0x73, 0x33, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x07, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x73, 0x22, 0x6a, 0x0a, 0x20, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6c, 0x69,
	0x67, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x8f,
	0x01, 0x0a, 0x1e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x73, 0x33,
	0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x07, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x22, 0x69, 0x0a, 0x1f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x61, 0x6e, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x6a, 0x0a, 0x20, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x73, 0x0a, 0x29, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x4c, 0x6f, 0x61, 0x6e, 0x73, 0x46, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x32, 0xe4, 0x04, 0x0a,
	0x17, 0x50, 0x72, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x4c, 0x6f, 0x61, 0x6e,
	0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x12, 0x93, 0x01, 0x0a, 0x18, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x39, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3a, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x90,
	0x01, 0x0a, 0x17, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x38, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x61,
	0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x6c, 0x0a, 0x18, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x12, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x1a, 0x3a, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x61, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0xb1, 0x01, 0x0a, 0x21, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x61, 0x6e, 0x73,
	0x46, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x45, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x66, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69,
	0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x73, 0x46, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x43, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c,
	0x6f, 0x61, 0x6e, 0x73, 0x46, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x42, 0x6a, 0x0a, 0x33, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5a, 0x33, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_preapprovedloan_consumer_service_proto_rawDescOnce sync.Once
	file_api_preapprovedloan_consumer_service_proto_rawDescData = file_api_preapprovedloan_consumer_service_proto_rawDesc
)

func file_api_preapprovedloan_consumer_service_proto_rawDescGZIP() []byte {
	file_api_preapprovedloan_consumer_service_proto_rawDescOnce.Do(func() {
		file_api_preapprovedloan_consumer_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_preapprovedloan_consumer_service_proto_rawDescData)
	})
	return file_api_preapprovedloan_consumer_service_proto_rawDescData
}

var file_api_preapprovedloan_consumer_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_preapprovedloan_consumer_service_proto_goTypes = []interface{}{
	(*ProcessEligibleUsersFileRequest)(nil),           // 0: preapprovedloan.consumer.ProcessEligibleUsersFileRequest
	(*ProcessEligibleUsersFileResponse)(nil),          // 1: preapprovedloan.consumer.ProcessEligibleUsersFileResponse
	(*ProcessLoanOfferCsvFileRequest)(nil),            // 2: preapprovedloan.consumer.ProcessLoanOfferCsvFileRequest
	(*ProcessLoanOfferCsvFileResponse)(nil),           // 3: preapprovedloan.consumer.ProcessLoanOfferCsvFileResponse
	(*ProcessOrderUpdatePacketResponse)(nil),          // 4: preapprovedloan.consumer.ProcessOrderUpdatePacketResponse
	(*ProcessLoansFiftyfinCallbackEventResponse)(nil), // 5: preapprovedloan.consumer.ProcessLoansFiftyfinCallbackEventResponse
	(*queue.ConsumerRequestHeader)(nil),               // 6: queue.ConsumerRequestHeader
	(*s3.Record)(nil),                                 // 7: aws.s3.Record
	(*queue.ConsumerResponseHeader)(nil),              // 8: queue.ConsumerResponseHeader
	(*order.OrderUpdate)(nil),                         // 9: order.OrderUpdate
	(*fiftyfin.LoansFiftyfinCallbackEvent)(nil),       // 10: vendornotification.lending.loans.fiftyfin.LoansFiftyfinCallbackEvent
}
var file_api_preapprovedloan_consumer_service_proto_depIdxs = []int32{
	6,  // 0: preapprovedloan.consumer.ProcessEligibleUsersFileRequest.request_header:type_name -> queue.ConsumerRequestHeader
	7,  // 1: preapprovedloan.consumer.ProcessEligibleUsersFileRequest.records:type_name -> aws.s3.Record
	8,  // 2: preapprovedloan.consumer.ProcessEligibleUsersFileResponse.response_header:type_name -> queue.ConsumerResponseHeader
	6,  // 3: preapprovedloan.consumer.ProcessLoanOfferCsvFileRequest.request_header:type_name -> queue.ConsumerRequestHeader
	7,  // 4: preapprovedloan.consumer.ProcessLoanOfferCsvFileRequest.records:type_name -> aws.s3.Record
	8,  // 5: preapprovedloan.consumer.ProcessLoanOfferCsvFileResponse.response_header:type_name -> queue.ConsumerResponseHeader
	8,  // 6: preapprovedloan.consumer.ProcessOrderUpdatePacketResponse.response_header:type_name -> queue.ConsumerResponseHeader
	8,  // 7: preapprovedloan.consumer.ProcessLoansFiftyfinCallbackEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	0,  // 8: preapprovedloan.consumer.PreApprovedLoanConsumer.ProcessEligibleUsersFile:input_type -> preapprovedloan.consumer.ProcessEligibleUsersFileRequest
	2,  // 9: preapprovedloan.consumer.PreApprovedLoanConsumer.ProcessLoanOfferCsvFile:input_type -> preapprovedloan.consumer.ProcessLoanOfferCsvFileRequest
	9,  // 10: preapprovedloan.consumer.PreApprovedLoanConsumer.ProcessOrderUpdatePacket:input_type -> order.OrderUpdate
	10, // 11: preapprovedloan.consumer.PreApprovedLoanConsumer.ProcessLoansFiftyfinCallbackEvent:input_type -> vendornotification.lending.loans.fiftyfin.LoansFiftyfinCallbackEvent
	1,  // 12: preapprovedloan.consumer.PreApprovedLoanConsumer.ProcessEligibleUsersFile:output_type -> preapprovedloan.consumer.ProcessEligibleUsersFileResponse
	3,  // 13: preapprovedloan.consumer.PreApprovedLoanConsumer.ProcessLoanOfferCsvFile:output_type -> preapprovedloan.consumer.ProcessLoanOfferCsvFileResponse
	4,  // 14: preapprovedloan.consumer.PreApprovedLoanConsumer.ProcessOrderUpdatePacket:output_type -> preapprovedloan.consumer.ProcessOrderUpdatePacketResponse
	5,  // 15: preapprovedloan.consumer.PreApprovedLoanConsumer.ProcessLoansFiftyfinCallbackEvent:output_type -> preapprovedloan.consumer.ProcessLoansFiftyfinCallbackEventResponse
	12, // [12:16] is the sub-list for method output_type
	8,  // [8:12] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_api_preapprovedloan_consumer_service_proto_init() }
func file_api_preapprovedloan_consumer_service_proto_init() {
	if File_api_preapprovedloan_consumer_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_preapprovedloan_consumer_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessEligibleUsersFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_consumer_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessEligibleUsersFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_consumer_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessLoanOfferCsvFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_consumer_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessLoanOfferCsvFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_consumer_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessOrderUpdatePacketResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_consumer_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessLoansFiftyfinCallbackEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_preapprovedloan_consumer_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_preapprovedloan_consumer_service_proto_goTypes,
		DependencyIndexes: file_api_preapprovedloan_consumer_service_proto_depIdxs,
		MessageInfos:      file_api_preapprovedloan_consumer_service_proto_msgTypes,
	}.Build()
	File_api_preapprovedloan_consumer_service_proto = out.File
	file_api_preapprovedloan_consumer_service_proto_rawDesc = nil
	file_api_preapprovedloan_consumer_service_proto_goTypes = nil
	file_api_preapprovedloan_consumer_service_proto_depIdxs = nil
}
