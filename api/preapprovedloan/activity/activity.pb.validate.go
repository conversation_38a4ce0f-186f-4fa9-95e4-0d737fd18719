// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/preapprovedloan/activity/activity.proto

package activity

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	preapprovedloan "github.com/epifi/gamma/api/preapprovedloan"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = preapprovedloan.LoanRequestStatus(0)

	_ = typesv2.Feature(0)
)

// Validate checks the field values on CheckEligibilityActivityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckEligibilityActivityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckEligibilityActivityRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CheckEligibilityActivityRequestMultiError, or nil if none found.
func (m *CheckEligibilityActivityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckEligibilityActivityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientReqId

	// no validation rules for EligibilityType

	if len(errors) > 0 {
		return CheckEligibilityActivityRequestMultiError(errors)
	}

	return nil
}

// CheckEligibilityActivityRequestMultiError is an error wrapping multiple
// validation errors returned by CheckEligibilityActivityRequest.ValidateAll()
// if the designated constraints aren't met.
type CheckEligibilityActivityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckEligibilityActivityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckEligibilityActivityRequestMultiError) AllErrors() []error { return m }

// CheckEligibilityActivityRequestValidationError is the validation error
// returned by CheckEligibilityActivityRequest.Validate if the designated
// constraints aren't met.
type CheckEligibilityActivityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckEligibilityActivityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckEligibilityActivityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckEligibilityActivityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckEligibilityActivityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckEligibilityActivityRequestValidationError) ErrorName() string {
	return "CheckEligibilityActivityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckEligibilityActivityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckEligibilityActivityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckEligibilityActivityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckEligibilityActivityRequestValidationError{}

// Validate checks the field values on CheckEligibilityActivityResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CheckEligibilityActivityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckEligibilityActivityResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CheckEligibilityActivityResponseMultiError, or nil if none found.
func (m *CheckEligibilityActivityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckEligibilityActivityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanStepExecutionOrchId

	if len(errors) > 0 {
		return CheckEligibilityActivityResponseMultiError(errors)
	}

	return nil
}

// CheckEligibilityActivityResponseMultiError is an error wrapping multiple
// validation errors returned by
// CheckEligibilityActivityResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckEligibilityActivityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckEligibilityActivityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckEligibilityActivityResponseMultiError) AllErrors() []error { return m }

// CheckEligibilityActivityResponseValidationError is the validation error
// returned by CheckEligibilityActivityResponse.Validate if the designated
// constraints aren't met.
type CheckEligibilityActivityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckEligibilityActivityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckEligibilityActivityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckEligibilityActivityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckEligibilityActivityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckEligibilityActivityResponseValidationError) ErrorName() string {
	return "CheckEligibilityActivityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckEligibilityActivityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckEligibilityActivityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckEligibilityActivityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckEligibilityActivityResponseValidationError{}

// Validate checks the field values on UpdateLoanRequestActivityRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateLoanRequestActivityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLoanRequestActivityRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateLoanRequestActivityRequestMultiError, or nil if none found.
func (m *UpdateLoanRequestActivityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLoanRequestActivityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanRequestStatus

	// no validation rules for LoanRequestSubStatus

	if len(errors) > 0 {
		return UpdateLoanRequestActivityRequestMultiError(errors)
	}

	return nil
}

// UpdateLoanRequestActivityRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateLoanRequestActivityRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateLoanRequestActivityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLoanRequestActivityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLoanRequestActivityRequestMultiError) AllErrors() []error { return m }

// UpdateLoanRequestActivityRequestValidationError is the validation error
// returned by UpdateLoanRequestActivityRequest.Validate if the designated
// constraints aren't met.
type UpdateLoanRequestActivityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLoanRequestActivityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLoanRequestActivityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLoanRequestActivityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLoanRequestActivityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLoanRequestActivityRequestValidationError) ErrorName() string {
	return "UpdateLoanRequestActivityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLoanRequestActivityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLoanRequestActivityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLoanRequestActivityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLoanRequestActivityRequestValidationError{}

// Validate checks the field values on UpdateLoanPaymentRequestActivityRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateLoanPaymentRequestActivityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateLoanPaymentRequestActivityRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// UpdateLoanPaymentRequestActivityRequestMultiError, or nil if none found.
func (m *UpdateLoanPaymentRequestActivityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLoanPaymentRequestActivityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLoanPaymentRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLoanPaymentRequestActivityRequestValidationError{
					field:  "LoanPaymentRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLoanPaymentRequestActivityRequestValidationError{
					field:  "LoanPaymentRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanPaymentRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLoanPaymentRequestActivityRequestValidationError{
				field:  "LoanPaymentRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateLoanPaymentRequestActivityRequestMultiError(errors)
	}

	return nil
}

// UpdateLoanPaymentRequestActivityRequestMultiError is an error wrapping
// multiple validation errors returned by
// UpdateLoanPaymentRequestActivityRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateLoanPaymentRequestActivityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLoanPaymentRequestActivityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLoanPaymentRequestActivityRequestMultiError) AllErrors() []error { return m }

// UpdateLoanPaymentRequestActivityRequestValidationError is the validation
// error returned by UpdateLoanPaymentRequestActivityRequest.Validate if the
// designated constraints aren't met.
type UpdateLoanPaymentRequestActivityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLoanPaymentRequestActivityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLoanPaymentRequestActivityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLoanPaymentRequestActivityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLoanPaymentRequestActivityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLoanPaymentRequestActivityRequestValidationError) ErrorName() string {
	return "UpdateLoanPaymentRequestActivityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLoanPaymentRequestActivityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLoanPaymentRequestActivityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLoanPaymentRequestActivityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLoanPaymentRequestActivityRequestValidationError{}

// Validate checks the field values on CheckManualReviewStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckManualReviewStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckManualReviewStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CheckManualReviewStatusRequestMultiError, or nil if none found.
func (m *CheckManualReviewStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckManualReviewStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StepExecutionLivenessOrchId

	// no validation rules for StepExecutionFaceMatchOrchId

	if len(errors) > 0 {
		return CheckManualReviewStatusRequestMultiError(errors)
	}

	return nil
}

// CheckManualReviewStatusRequestMultiError is an error wrapping multiple
// validation errors returned by CheckManualReviewStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type CheckManualReviewStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckManualReviewStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckManualReviewStatusRequestMultiError) AllErrors() []error { return m }

// CheckManualReviewStatusRequestValidationError is the validation error
// returned by CheckManualReviewStatusRequest.Validate if the designated
// constraints aren't met.
type CheckManualReviewStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckManualReviewStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckManualReviewStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckManualReviewStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckManualReviewStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckManualReviewStatusRequestValidationError) ErrorName() string {
	return "CheckManualReviewStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckManualReviewStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckManualReviewStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckManualReviewStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckManualReviewStatusRequestValidationError{}

// Validate checks the field values on
// CheckManualReviewForLivenessAndFaceMatchResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckManualReviewForLivenessAndFaceMatchResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckManualReviewForLivenessAndFaceMatchResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// CheckManualReviewForLivenessAndFaceMatchResponseMultiError, or nil if none found.
func (m *CheckManualReviewForLivenessAndFaceMatchResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckManualReviewForLivenessAndFaceMatchResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StepExecutionLivenessMrOrchId

	// no validation rules for StepExecutionFaceMatchMrOrchId

	if len(errors) > 0 {
		return CheckManualReviewForLivenessAndFaceMatchResponseMultiError(errors)
	}

	return nil
}

// CheckManualReviewForLivenessAndFaceMatchResponseMultiError is an error
// wrapping multiple validation errors returned by
// CheckManualReviewForLivenessAndFaceMatchResponse.ValidateAll() if the
// designated constraints aren't met.
type CheckManualReviewForLivenessAndFaceMatchResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckManualReviewForLivenessAndFaceMatchResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckManualReviewForLivenessAndFaceMatchResponseMultiError) AllErrors() []error { return m }

// CheckManualReviewForLivenessAndFaceMatchResponseValidationError is the
// validation error returned by
// CheckManualReviewForLivenessAndFaceMatchResponse.Validate if the designated
// constraints aren't met.
type CheckManualReviewForLivenessAndFaceMatchResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckManualReviewForLivenessAndFaceMatchResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CheckManualReviewForLivenessAndFaceMatchResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CheckManualReviewForLivenessAndFaceMatchResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e CheckManualReviewForLivenessAndFaceMatchResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckManualReviewForLivenessAndFaceMatchResponseValidationError) ErrorName() string {
	return "CheckManualReviewForLivenessAndFaceMatchResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckManualReviewForLivenessAndFaceMatchResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckManualReviewForLivenessAndFaceMatchResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckManualReviewForLivenessAndFaceMatchResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckManualReviewForLivenessAndFaceMatchResponseValidationError{}

// Validate checks the field values on PalActivityRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PalActivityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PalActivityRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PalActivityRequestMultiError, or nil if none found.
func (m *PalActivityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PalActivityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PalActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PalActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PalActivityRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PalActivityRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PalActivityRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PalActivityRequestValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	// no validation rules for LoanProgram

	if all {
		switch v := interface{}(m.GetPayloadData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PalActivityRequestValidationError{
					field:  "PayloadData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PalActivityRequestValidationError{
					field:  "PayloadData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayloadData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PalActivityRequestValidationError{
				field:  "PayloadData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SyncActivityName

	if len(errors) > 0 {
		return PalActivityRequestMultiError(errors)
	}

	return nil
}

// PalActivityRequestMultiError is an error wrapping multiple validation errors
// returned by PalActivityRequest.ValidateAll() if the designated constraints
// aren't met.
type PalActivityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PalActivityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PalActivityRequestMultiError) AllErrors() []error { return m }

// PalActivityRequestValidationError is the validation error returned by
// PalActivityRequest.Validate if the designated constraints aren't met.
type PalActivityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PalActivityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PalActivityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PalActivityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PalActivityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PalActivityRequestValidationError) ErrorName() string {
	return "PalActivityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PalActivityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPalActivityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PalActivityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PalActivityRequestValidationError{}

// Validate checks the field values on PalActivityResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PalActivityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PalActivityResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PalActivityResponseMultiError, or nil if none found.
func (m *PalActivityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PalActivityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PalActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PalActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PalActivityResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PalActivityResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PalActivityResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PalActivityResponseValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PalActivityResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PalActivityResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PalActivityResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsSyncTaskDone

	if len(errors) > 0 {
		return PalActivityResponseMultiError(errors)
	}

	return nil
}

// PalActivityResponseMultiError is an error wrapping multiple validation
// errors returned by PalActivityResponse.ValidateAll() if the designated
// constraints aren't met.
type PalActivityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PalActivityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PalActivityResponseMultiError) AllErrors() []error { return m }

// PalActivityResponseValidationError is the validation error returned by
// PalActivityResponse.Validate if the designated constraints aren't met.
type PalActivityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PalActivityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PalActivityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PalActivityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PalActivityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PalActivityResponseValidationError) ErrorName() string {
	return "PalActivityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PalActivityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPalActivityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PalActivityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PalActivityResponseValidationError{}

// Validate checks the field values on CreateLoanStepExecutionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLoanStepExecutionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLoanStepExecutionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateLoanStepExecutionRequestMultiError, or nil if none found.
func (m *CreateLoanStepExecutionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLoanStepExecutionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanStepExecutionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanStepExecutionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanStepExecutionRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StepName

	// no validation rules for Flow

	// no validation rules for GroupStage

	if len(errors) > 0 {
		return CreateLoanStepExecutionRequestMultiError(errors)
	}

	return nil
}

// CreateLoanStepExecutionRequestMultiError is an error wrapping multiple
// validation errors returned by CreateLoanStepExecutionRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateLoanStepExecutionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLoanStepExecutionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLoanStepExecutionRequestMultiError) AllErrors() []error { return m }

// CreateLoanStepExecutionRequestValidationError is the validation error
// returned by CreateLoanStepExecutionRequest.Validate if the designated
// constraints aren't met.
type CreateLoanStepExecutionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLoanStepExecutionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLoanStepExecutionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLoanStepExecutionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLoanStepExecutionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLoanStepExecutionRequestValidationError) ErrorName() string {
	return "CreateLoanStepExecutionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLoanStepExecutionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLoanStepExecutionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLoanStepExecutionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLoanStepExecutionRequestValidationError{}

// Validate checks the field values on CreateLoanStepExecutionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLoanStepExecutionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLoanStepExecutionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateLoanStepExecutionResponseMultiError, or nil if none found.
func (m *CreateLoanStepExecutionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLoanStepExecutionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanStepExecutionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanStepExecutionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanStepExecutionResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanStepExecutionResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanStepExecutionResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanStepExecutionResponseValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateLoanStepExecutionResponseMultiError(errors)
	}

	return nil
}

// CreateLoanStepExecutionResponseMultiError is an error wrapping multiple
// validation errors returned by CreateLoanStepExecutionResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateLoanStepExecutionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLoanStepExecutionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLoanStepExecutionResponseMultiError) AllErrors() []error { return m }

// CreateLoanStepExecutionResponseValidationError is the validation error
// returned by CreateLoanStepExecutionResponse.Validate if the designated
// constraints aren't met.
type CreateLoanStepExecutionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLoanStepExecutionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLoanStepExecutionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLoanStepExecutionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLoanStepExecutionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLoanStepExecutionResponseValidationError) ErrorName() string {
	return "CreateLoanStepExecutionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLoanStepExecutionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLoanStepExecutionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLoanStepExecutionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLoanStepExecutionResponseValidationError{}

// Validate checks the field values on UpdateLoanStepExecutionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateLoanStepExecutionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLoanStepExecutionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateLoanStepExecutionRequestMultiError, or nil if none found.
func (m *UpdateLoanStepExecutionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLoanStepExecutionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLoanStepExecutionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLoanStepExecutionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLoanStepExecutionRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLoanStepExecutionRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLoanStepExecutionRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLoanStepExecutionRequestValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateLoanStepExecutionRequestMultiError(errors)
	}

	return nil
}

// UpdateLoanStepExecutionRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateLoanStepExecutionRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateLoanStepExecutionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLoanStepExecutionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLoanStepExecutionRequestMultiError) AllErrors() []error { return m }

// UpdateLoanStepExecutionRequestValidationError is the validation error
// returned by UpdateLoanStepExecutionRequest.Validate if the designated
// constraints aren't met.
type UpdateLoanStepExecutionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLoanStepExecutionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLoanStepExecutionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLoanStepExecutionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLoanStepExecutionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLoanStepExecutionRequestValidationError) ErrorName() string {
	return "UpdateLoanStepExecutionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLoanStepExecutionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLoanStepExecutionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLoanStepExecutionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLoanStepExecutionRequestValidationError{}

// Validate checks the field values on UpdateLRNextActionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateLRNextActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLRNextActionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLRNextActionRequestMultiError, or nil if none found.
func (m *UpdateLRNextActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLRNextActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLRNextActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLRNextActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLRNextActionRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanRequestId

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLRNextActionRequestValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLRNextActionRequestValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLRNextActionRequestValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLRNextActionRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLRNextActionRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLRNextActionRequestValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoecOwner

	if len(errors) > 0 {
		return UpdateLRNextActionRequestMultiError(errors)
	}

	return nil
}

// UpdateLRNextActionRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateLRNextActionRequest.ValidateAll() if the
// designated constraints aren't met.
type UpdateLRNextActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLRNextActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLRNextActionRequestMultiError) AllErrors() []error { return m }

// UpdateLRNextActionRequestValidationError is the validation error returned by
// UpdateLRNextActionRequest.Validate if the designated constraints aren't met.
type UpdateLRNextActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLRNextActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLRNextActionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLRNextActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLRNextActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLRNextActionRequestValidationError) ErrorName() string {
	return "UpdateLRNextActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLRNextActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLRNextActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLRNextActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLRNextActionRequestValidationError{}

// Validate checks the field values on
// LoanApplicationESignVerificationSignalPayload with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoanApplicationESignVerificationSignalPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanApplicationESignVerificationSignalPayload with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// LoanApplicationESignVerificationSignalPayloadMultiError, or nil if none found.
func (m *LoanApplicationESignVerificationSignalPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanApplicationESignVerificationSignalPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanApplicationESignVerificationSignalPayloadValidationError{
					field:  "Lse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanApplicationESignVerificationSignalPayloadValidationError{
					field:  "Lse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanApplicationESignVerificationSignalPayloadValidationError{
				field:  "Lse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanApplicationESignVerificationSignalPayloadMultiError(errors)
	}

	return nil
}

// LoanApplicationESignVerificationSignalPayloadMultiError is an error wrapping
// multiple validation errors returned by
// LoanApplicationESignVerificationSignalPayload.ValidateAll() if the
// designated constraints aren't met.
type LoanApplicationESignVerificationSignalPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanApplicationESignVerificationSignalPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanApplicationESignVerificationSignalPayloadMultiError) AllErrors() []error { return m }

// LoanApplicationESignVerificationSignalPayloadValidationError is the
// validation error returned by
// LoanApplicationESignVerificationSignalPayload.Validate if the designated
// constraints aren't met.
type LoanApplicationESignVerificationSignalPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanApplicationESignVerificationSignalPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanApplicationESignVerificationSignalPayloadValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanApplicationESignVerificationSignalPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanApplicationESignVerificationSignalPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanApplicationESignVerificationSignalPayloadValidationError) ErrorName() string {
	return "LoanApplicationESignVerificationSignalPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e LoanApplicationESignVerificationSignalPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanApplicationESignVerificationSignalPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanApplicationESignVerificationSignalPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanApplicationESignVerificationSignalPayloadValidationError{}

// Validate checks the field values on GetLoanRequestActivityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanRequestActivityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanRequestActivityRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLoanRequestActivityRequestMultiError, or nil if none found.
func (m *GetLoanRequestActivityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanRequestActivityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanRequestActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanRequestActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanRequestActivityRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanRequestId

	// no validation rules for OrchId

	if len(errors) > 0 {
		return GetLoanRequestActivityRequestMultiError(errors)
	}

	return nil
}

// GetLoanRequestActivityRequestMultiError is an error wrapping multiple
// validation errors returned by GetLoanRequestActivityRequest.ValidateAll()
// if the designated constraints aren't met.
type GetLoanRequestActivityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanRequestActivityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanRequestActivityRequestMultiError) AllErrors() []error { return m }

// GetLoanRequestActivityRequestValidationError is the validation error
// returned by GetLoanRequestActivityRequest.Validate if the designated
// constraints aren't met.
type GetLoanRequestActivityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanRequestActivityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanRequestActivityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanRequestActivityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanRequestActivityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanRequestActivityRequestValidationError) ErrorName() string {
	return "GetLoanRequestActivityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanRequestActivityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanRequestActivityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanRequestActivityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanRequestActivityRequestValidationError{}

// Validate checks the field values on GetLoanRequestActivityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanRequestActivityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanRequestActivityResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLoanRequestActivityResponseMultiError, or nil if none found.
func (m *GetLoanRequestActivityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanRequestActivityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanRequestActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanRequestActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanRequestActivityResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanRequestActivityResponseValidationError{
					field:  "LoanRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanRequestActivityResponseValidationError{
					field:  "LoanRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanRequestActivityResponseValidationError{
				field:  "LoanRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanRequestActivityResponseMultiError(errors)
	}

	return nil
}

// GetLoanRequestActivityResponseMultiError is an error wrapping multiple
// validation errors returned by GetLoanRequestActivityResponse.ValidateAll()
// if the designated constraints aren't met.
type GetLoanRequestActivityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanRequestActivityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanRequestActivityResponseMultiError) AllErrors() []error { return m }

// GetLoanRequestActivityResponseValidationError is the validation error
// returned by GetLoanRequestActivityResponse.Validate if the designated
// constraints aren't met.
type GetLoanRequestActivityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanRequestActivityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanRequestActivityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanRequestActivityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanRequestActivityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanRequestActivityResponseValidationError) ErrorName() string {
	return "GetLoanRequestActivityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanRequestActivityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanRequestActivityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanRequestActivityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanRequestActivityResponseValidationError{}

// Validate checks the field values on FetchAvailableLimitRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchAvailableLimitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchAvailableLimitRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchAvailableLimitRequestMultiError, or nil if none found.
func (m *FetchAvailableLimitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchAvailableLimitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchAvailableLimitRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchAvailableLimitRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchAvailableLimitRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchAvailableLimitRequestMultiError(errors)
	}

	return nil
}

// FetchAvailableLimitRequestMultiError is an error wrapping multiple
// validation errors returned by FetchAvailableLimitRequest.ValidateAll() if
// the designated constraints aren't met.
type FetchAvailableLimitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchAvailableLimitRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchAvailableLimitRequestMultiError) AllErrors() []error { return m }

// FetchAvailableLimitRequestValidationError is the validation error returned
// by FetchAvailableLimitRequest.Validate if the designated constraints aren't met.
type FetchAvailableLimitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchAvailableLimitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchAvailableLimitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchAvailableLimitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchAvailableLimitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchAvailableLimitRequestValidationError) ErrorName() string {
	return "FetchAvailableLimitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchAvailableLimitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchAvailableLimitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchAvailableLimitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchAvailableLimitRequestValidationError{}

// Validate checks the field values on FetchAvailableLimitResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchAvailableLimitResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchAvailableLimitResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchAvailableLimitResponseMultiError, or nil if none found.
func (m *FetchAvailableLimitResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchAvailableLimitResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchAvailableLimitResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchAvailableLimitResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchAvailableLimitResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAvailableLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchAvailableLimitResponseValidationError{
					field:  "AvailableLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchAvailableLimitResponseValidationError{
					field:  "AvailableLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAvailableLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchAvailableLimitResponseValidationError{
				field:  "AvailableLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchAvailableLimitResponseMultiError(errors)
	}

	return nil
}

// FetchAvailableLimitResponseMultiError is an error wrapping multiple
// validation errors returned by FetchAvailableLimitResponse.ValidateAll() if
// the designated constraints aren't met.
type FetchAvailableLimitResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchAvailableLimitResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchAvailableLimitResponseMultiError) AllErrors() []error { return m }

// FetchAvailableLimitResponseValidationError is the validation error returned
// by FetchAvailableLimitResponse.Validate if the designated constraints
// aren't met.
type FetchAvailableLimitResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchAvailableLimitResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchAvailableLimitResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchAvailableLimitResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchAvailableLimitResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchAvailableLimitResponseValidationError) ErrorName() string {
	return "FetchAvailableLimitResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchAvailableLimitResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchAvailableLimitResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchAvailableLimitResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchAvailableLimitResponseValidationError{}

// Validate checks the field values on EmitEventRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EmitEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmitEventRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EmitEventRequestMultiError, or nil if none found.
func (m *EmitEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EmitEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmitEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmitEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmitEventRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmitEventRequestValidationError{
					field:  "Lse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmitEventRequestValidationError{
					field:  "Lse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmitEventRequestValidationError{
				field:  "Lse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EventName

	// no validation rules for Vendor

	// no validation rules for LoanProgram

	// no validation rules for EventParams

	if len(errors) > 0 {
		return EmitEventRequestMultiError(errors)
	}

	return nil
}

// EmitEventRequestMultiError is an error wrapping multiple validation errors
// returned by EmitEventRequest.ValidateAll() if the designated constraints
// aren't met.
type EmitEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmitEventRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmitEventRequestMultiError) AllErrors() []error { return m }

// EmitEventRequestValidationError is the validation error returned by
// EmitEventRequest.Validate if the designated constraints aren't met.
type EmitEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmitEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmitEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmitEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmitEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmitEventRequestValidationError) ErrorName() string { return "EmitEventRequestValidationError" }

// Error satisfies the builtin error interface
func (e EmitEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmitEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmitEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmitEventRequestValidationError{}

// Validate checks the field values on EmitEventResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EmitEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmitEventResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EmitEventResponseMultiError, or nil if none found.
func (m *EmitEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EmitEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmitEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmitEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmitEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EmitEventResponseMultiError(errors)
	}

	return nil
}

// EmitEventResponseMultiError is an error wrapping multiple validation errors
// returned by EmitEventResponse.ValidateAll() if the designated constraints
// aren't met.
type EmitEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmitEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmitEventResponseMultiError) AllErrors() []error { return m }

// EmitEventResponseValidationError is the validation error returned by
// EmitEventResponse.Validate if the designated constraints aren't met.
type EmitEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmitEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmitEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmitEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmitEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmitEventResponseValidationError) ErrorName() string {
	return "EmitEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EmitEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmitEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmitEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmitEventResponseValidationError{}

// Validate checks the field values on GetBatchLimitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBatchLimitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBatchLimitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBatchLimitRequestMultiError, or nil if none found.
func (m *GetBatchLimitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBatchLimitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBatchLimitRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBatchLimitRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBatchLimitRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Offset

	// no validation rules for BatchSize

	// no validation rules for S3Path

	if len(errors) > 0 {
		return GetBatchLimitRequestMultiError(errors)
	}

	return nil
}

// GetBatchLimitRequestMultiError is an error wrapping multiple validation
// errors returned by GetBatchLimitRequest.ValidateAll() if the designated
// constraints aren't met.
type GetBatchLimitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBatchLimitRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBatchLimitRequestMultiError) AllErrors() []error { return m }

// GetBatchLimitRequestValidationError is the validation error returned by
// GetBatchLimitRequest.Validate if the designated constraints aren't met.
type GetBatchLimitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBatchLimitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBatchLimitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBatchLimitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBatchLimitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBatchLimitRequestValidationError) ErrorName() string {
	return "GetBatchLimitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBatchLimitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBatchLimitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBatchLimitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBatchLimitRequestValidationError{}

// Validate checks the field values on GetBatchLimitResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBatchLimitResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBatchLimitResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBatchLimitResponseMultiError, or nil if none found.
func (m *GetBatchLimitResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBatchLimitResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBatchLimitResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBatchLimitResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBatchLimitResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCsvRecord() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBatchLimitResponseValidationError{
						field:  fmt.Sprintf("CsvRecord[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBatchLimitResponseValidationError{
						field:  fmt.Sprintf("CsvRecord[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBatchLimitResponseValidationError{
					field:  fmt.Sprintf("CsvRecord[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetBatchLimitResponseMultiError(errors)
	}

	return nil
}

// GetBatchLimitResponseMultiError is an error wrapping multiple validation
// errors returned by GetBatchLimitResponse.ValidateAll() if the designated
// constraints aren't met.
type GetBatchLimitResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBatchLimitResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBatchLimitResponseMultiError) AllErrors() []error { return m }

// GetBatchLimitResponseValidationError is the validation error returned by
// GetBatchLimitResponse.Validate if the designated constraints aren't met.
type GetBatchLimitResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBatchLimitResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBatchLimitResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBatchLimitResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBatchLimitResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBatchLimitResponseValidationError) ErrorName() string {
	return "GetBatchLimitResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetBatchLimitResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBatchLimitResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBatchLimitResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBatchLimitResponseValidationError{}

// Validate checks the field values on ProcessChildBatchActivityRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessChildBatchActivityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessChildBatchActivityRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessChildBatchActivityRequestMultiError, or nil if none found.
func (m *ProcessChildBatchActivityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessChildBatchActivityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessChildBatchActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessChildBatchActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessChildBatchActivityRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	// no validation rules for Program

	// no validation rules for BatchId

	for idx, item := range m.GetCsvRecord() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessChildBatchActivityRequestValidationError{
						field:  fmt.Sprintf("CsvRecord[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessChildBatchActivityRequestValidationError{
						field:  fmt.Sprintf("CsvRecord[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessChildBatchActivityRequestValidationError{
					field:  fmt.Sprintf("CsvRecord[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProcessChildBatchActivityRequestMultiError(errors)
	}

	return nil
}

// ProcessChildBatchActivityRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessChildBatchActivityRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessChildBatchActivityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessChildBatchActivityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessChildBatchActivityRequestMultiError) AllErrors() []error { return m }

// ProcessChildBatchActivityRequestValidationError is the validation error
// returned by ProcessChildBatchActivityRequest.Validate if the designated
// constraints aren't met.
type ProcessChildBatchActivityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessChildBatchActivityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessChildBatchActivityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessChildBatchActivityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessChildBatchActivityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessChildBatchActivityRequestValidationError) ErrorName() string {
	return "ProcessChildBatchActivityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessChildBatchActivityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessChildBatchActivityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessChildBatchActivityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessChildBatchActivityRequestValidationError{}

// Validate checks the field values on ProcessChildBatchActivityResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessChildBatchActivityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessChildBatchActivityResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessChildBatchActivityResponseMultiError, or nil if none found.
func (m *ProcessChildBatchActivityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessChildBatchActivityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessChildBatchActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessChildBatchActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessChildBatchActivityResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessChildBatchActivityResponseMultiError(errors)
	}

	return nil
}

// ProcessChildBatchActivityResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessChildBatchActivityResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessChildBatchActivityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessChildBatchActivityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessChildBatchActivityResponseMultiError) AllErrors() []error { return m }

// ProcessChildBatchActivityResponseValidationError is the validation error
// returned by ProcessChildBatchActivityResponse.Validate if the designated
// constraints aren't met.
type ProcessChildBatchActivityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessChildBatchActivityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessChildBatchActivityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessChildBatchActivityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessChildBatchActivityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessChildBatchActivityResponseValidationError) ErrorName() string {
	return "ProcessChildBatchActivityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessChildBatchActivityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessChildBatchActivityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessChildBatchActivityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessChildBatchActivityResponseValidationError{}

// Validate checks the field values on ExecuteSiRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ExecuteSiRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteSiRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExecuteSiRequestMultiError, or nil if none found.
func (m *ExecuteSiRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteSiRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteSiRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteSiRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteSiRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountId

	if all {
		switch v := interface{}(m.GetSiAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteSiRequestValidationError{
					field:  "SiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteSiRequestValidationError{
					field:  "SiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSiAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteSiRequestValidationError{
				field:  "SiAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteSiRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteSiRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteSiRequestValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExecuteSiRequestMultiError(errors)
	}

	return nil
}

// ExecuteSiRequestMultiError is an error wrapping multiple validation errors
// returned by ExecuteSiRequest.ValidateAll() if the designated constraints
// aren't met.
type ExecuteSiRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteSiRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteSiRequestMultiError) AllErrors() []error { return m }

// ExecuteSiRequestValidationError is the validation error returned by
// ExecuteSiRequest.Validate if the designated constraints aren't met.
type ExecuteSiRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteSiRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteSiRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteSiRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteSiRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteSiRequestValidationError) ErrorName() string { return "ExecuteSiRequestValidationError" }

// Error satisfies the builtin error interface
func (e ExecuteSiRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteSiRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteSiRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteSiRequestValidationError{}

// Validate checks the field values on ExecuteSiResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ExecuteSiResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteSiResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExecuteSiResponseMultiError, or nil if none found.
func (m *ExecuteSiResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteSiResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteSiResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteSiResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteSiResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteSiResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteSiResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteSiResponseValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExecutedSiAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteSiResponseValidationError{
					field:  "ExecutedSiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteSiResponseValidationError{
					field:  "ExecutedSiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExecutedSiAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteSiResponseValidationError{
				field:  "ExecutedSiAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExecuteSiResponseMultiError(errors)
	}

	return nil
}

// ExecuteSiResponseMultiError is an error wrapping multiple validation errors
// returned by ExecuteSiResponse.ValidateAll() if the designated constraints
// aren't met.
type ExecuteSiResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteSiResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteSiResponseMultiError) AllErrors() []error { return m }

// ExecuteSiResponseValidationError is the validation error returned by
// ExecuteSiResponse.Validate if the designated constraints aren't met.
type ExecuteSiResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteSiResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteSiResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteSiResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteSiResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteSiResponseValidationError) ErrorName() string {
	return "ExecuteSiResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteSiResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteSiResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteSiResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteSiResponseValidationError{}

// Validate checks the field values on CreateLoanPaymentRequestRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLoanPaymentRequestRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLoanPaymentRequestRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateLoanPaymentRequestRequestMultiError, or nil if none found.
func (m *CreateLoanPaymentRequestRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLoanPaymentRequestRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanPaymentRequestRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanPaymentRequestRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanPaymentRequestRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	// no validation rules for AccountId

	// no validation rules for OrchId

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanPaymentRequestRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanPaymentRequestRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanPaymentRequestRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateLoanPaymentRequestRequestMultiError(errors)
	}

	return nil
}

// CreateLoanPaymentRequestRequestMultiError is an error wrapping multiple
// validation errors returned by CreateLoanPaymentRequestRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateLoanPaymentRequestRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLoanPaymentRequestRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLoanPaymentRequestRequestMultiError) AllErrors() []error { return m }

// CreateLoanPaymentRequestRequestValidationError is the validation error
// returned by CreateLoanPaymentRequestRequest.Validate if the designated
// constraints aren't met.
type CreateLoanPaymentRequestRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLoanPaymentRequestRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLoanPaymentRequestRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLoanPaymentRequestRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLoanPaymentRequestRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLoanPaymentRequestRequestValidationError) ErrorName() string {
	return "CreateLoanPaymentRequestRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLoanPaymentRequestRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLoanPaymentRequestRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLoanPaymentRequestRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLoanPaymentRequestRequestValidationError{}

// Validate checks the field values on CreateLoanPaymentRequestResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateLoanPaymentRequestResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLoanPaymentRequestResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateLoanPaymentRequestResponseMultiError, or nil if none found.
func (m *CreateLoanPaymentRequestResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLoanPaymentRequestResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanPaymentRequestResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanPaymentRequestResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanPaymentRequestResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanPaymentRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanPaymentRequestResponseValidationError{
					field:  "LoanPaymentRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanPaymentRequestResponseValidationError{
					field:  "LoanPaymentRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanPaymentRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanPaymentRequestResponseValidationError{
				field:  "LoanPaymentRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateLoanPaymentRequestResponseMultiError(errors)
	}

	return nil
}

// CreateLoanPaymentRequestResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateLoanPaymentRequestResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateLoanPaymentRequestResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLoanPaymentRequestResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLoanPaymentRequestResponseMultiError) AllErrors() []error { return m }

// CreateLoanPaymentRequestResponseValidationError is the validation error
// returned by CreateLoanPaymentRequestResponse.Validate if the designated
// constraints aren't met.
type CreateLoanPaymentRequestResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLoanPaymentRequestResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLoanPaymentRequestResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLoanPaymentRequestResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLoanPaymentRequestResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLoanPaymentRequestResponseValidationError) ErrorName() string {
	return "CreateLoanPaymentRequestResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLoanPaymentRequestResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLoanPaymentRequestResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLoanPaymentRequestResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLoanPaymentRequestResponseValidationError{}

// Validate checks the field values on GetRepaymentBreakupRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRepaymentBreakupRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRepaymentBreakupRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRepaymentBreakupRequestMultiError, or nil if none found.
func (m *GetRepaymentBreakupRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRepaymentBreakupRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentBreakupRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentBreakupRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentBreakupRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentBreakupRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentBreakupRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentBreakupRequestValidationError{
				field:  "LoanHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanAccountId

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetRepaymentBreakupRequestMultiError(errors)
	}

	return nil
}

// GetRepaymentBreakupRequestMultiError is an error wrapping multiple
// validation errors returned by GetRepaymentBreakupRequest.ValidateAll() if
// the designated constraints aren't met.
type GetRepaymentBreakupRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRepaymentBreakupRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRepaymentBreakupRequestMultiError) AllErrors() []error { return m }

// GetRepaymentBreakupRequestValidationError is the validation error returned
// by GetRepaymentBreakupRequest.Validate if the designated constraints aren't met.
type GetRepaymentBreakupRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRepaymentBreakupRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRepaymentBreakupRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRepaymentBreakupRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRepaymentBreakupRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRepaymentBreakupRequestValidationError) ErrorName() string {
	return "GetRepaymentBreakupRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRepaymentBreakupRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRepaymentBreakupRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRepaymentBreakupRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRepaymentBreakupRequestValidationError{}

// Validate checks the field values on GetRepaymentBreakupResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRepaymentBreakupResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRepaymentBreakupResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRepaymentBreakupResponseMultiError, or nil if none found.
func (m *GetRepaymentBreakupResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRepaymentBreakupResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentBreakupResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentBreakupResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentBreakupResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRepaymentBreakup() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRepaymentBreakupResponseValidationError{
						field:  fmt.Sprintf("RepaymentBreakup[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRepaymentBreakupResponseValidationError{
						field:  fmt.Sprintf("RepaymentBreakup[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRepaymentBreakupResponseValidationError{
					field:  fmt.Sprintf("RepaymentBreakup[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRepaymentBreakupResponseMultiError(errors)
	}

	return nil
}

// GetRepaymentBreakupResponseMultiError is an error wrapping multiple
// validation errors returned by GetRepaymentBreakupResponse.ValidateAll() if
// the designated constraints aren't met.
type GetRepaymentBreakupResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRepaymentBreakupResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRepaymentBreakupResponseMultiError) AllErrors() []error { return m }

// GetRepaymentBreakupResponseValidationError is the validation error returned
// by GetRepaymentBreakupResponse.Validate if the designated constraints
// aren't met.
type GetRepaymentBreakupResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRepaymentBreakupResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRepaymentBreakupResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRepaymentBreakupResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRepaymentBreakupResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRepaymentBreakupResponseValidationError) ErrorName() string {
	return "GetRepaymentBreakupResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRepaymentBreakupResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRepaymentBreakupResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRepaymentBreakupResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRepaymentBreakupResponseValidationError{}

// Validate checks the field values on CreateLoanActivityRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLoanActivityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLoanActivityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLoanActivityRequestMultiError, or nil if none found.
func (m *CreateLoanActivityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLoanActivityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanActivityRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanActivityRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanActivityRequestValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanActivityRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanActivityType

	if all {
		switch v := interface{}(m.GetPaymentLoanStepExecution()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanActivityRequestValidationError{
					field:  "PaymentLoanStepExecution",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanActivityRequestValidationError{
					field:  "PaymentLoanStepExecution",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentLoanStepExecution()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanActivityRequestValidationError{
				field:  "PaymentLoanStepExecution",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateLoanActivityRequestMultiError(errors)
	}

	return nil
}

// CreateLoanActivityRequestMultiError is an error wrapping multiple validation
// errors returned by CreateLoanActivityRequest.ValidateAll() if the
// designated constraints aren't met.
type CreateLoanActivityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLoanActivityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLoanActivityRequestMultiError) AllErrors() []error { return m }

// CreateLoanActivityRequestValidationError is the validation error returned by
// CreateLoanActivityRequest.Validate if the designated constraints aren't met.
type CreateLoanActivityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLoanActivityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLoanActivityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLoanActivityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLoanActivityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLoanActivityRequestValidationError) ErrorName() string {
	return "CreateLoanActivityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLoanActivityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLoanActivityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLoanActivityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLoanActivityRequestValidationError{}

// Validate checks the field values on CreateLoanActivityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLoanActivityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLoanActivityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLoanActivityResponseMultiError, or nil if none found.
func (m *CreateLoanActivityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLoanActivityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanActivityResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanActivityResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanActivityResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanActivityResponseValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateLoanActivityResponseMultiError(errors)
	}

	return nil
}

// CreateLoanActivityResponseMultiError is an error wrapping multiple
// validation errors returned by CreateLoanActivityResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateLoanActivityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLoanActivityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLoanActivityResponseMultiError) AllErrors() []error { return m }

// CreateLoanActivityResponseValidationError is the validation error returned
// by CreateLoanActivityResponse.Validate if the designated constraints aren't met.
type CreateLoanActivityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLoanActivityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLoanActivityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLoanActivityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLoanActivityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLoanActivityResponseValidationError) ErrorName() string {
	return "CreateLoanActivityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLoanActivityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLoanActivityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLoanActivityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLoanActivityResponseValidationError{}

// Validate checks the field values on LmsRefreshAndSaveApiReconcileRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LmsRefreshAndSaveApiReconcileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LmsRefreshAndSaveApiReconcileRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LmsRefreshAndSaveApiReconcileRequestMultiError, or nil if none found.
func (m *LmsRefreshAndSaveApiReconcileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LmsRefreshAndSaveApiReconcileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LmsRefreshAndSaveApiReconcileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LmsRefreshAndSaveApiReconcileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LmsRefreshAndSaveApiReconcileRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExecutedSiAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LmsRefreshAndSaveApiReconcileRequestValidationError{
					field:  "ExecutedSiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LmsRefreshAndSaveApiReconcileRequestValidationError{
					field:  "ExecutedSiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExecutedSiAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LmsRefreshAndSaveApiReconcileRequestValidationError{
				field:  "ExecutedSiAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LmsRefreshAndSaveApiReconcileRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LmsRefreshAndSaveApiReconcileRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LmsRefreshAndSaveApiReconcileRequestValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanActivityType

	// no validation rules for LoanAccountId

	if all {
		switch v := interface{}(m.GetPaymentLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LmsRefreshAndSaveApiReconcileRequestValidationError{
					field:  "PaymentLoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LmsRefreshAndSaveApiReconcileRequestValidationError{
					field:  "PaymentLoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LmsRefreshAndSaveApiReconcileRequestValidationError{
				field:  "PaymentLoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LmsRefreshAndSaveApiReconcileRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LmsRefreshAndSaveApiReconcileRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LmsRefreshAndSaveApiReconcileRequestValidationError{
				field:  "LoanHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LmsRefreshAndSaveApiReconcileRequestMultiError(errors)
	}

	return nil
}

// LmsRefreshAndSaveApiReconcileRequestMultiError is an error wrapping multiple
// validation errors returned by
// LmsRefreshAndSaveApiReconcileRequest.ValidateAll() if the designated
// constraints aren't met.
type LmsRefreshAndSaveApiReconcileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LmsRefreshAndSaveApiReconcileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LmsRefreshAndSaveApiReconcileRequestMultiError) AllErrors() []error { return m }

// LmsRefreshAndSaveApiReconcileRequestValidationError is the validation error
// returned by LmsRefreshAndSaveApiReconcileRequest.Validate if the designated
// constraints aren't met.
type LmsRefreshAndSaveApiReconcileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LmsRefreshAndSaveApiReconcileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LmsRefreshAndSaveApiReconcileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LmsRefreshAndSaveApiReconcileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LmsRefreshAndSaveApiReconcileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LmsRefreshAndSaveApiReconcileRequestValidationError) ErrorName() string {
	return "LmsRefreshAndSaveApiReconcileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LmsRefreshAndSaveApiReconcileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLmsRefreshAndSaveApiReconcileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LmsRefreshAndSaveApiReconcileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LmsRefreshAndSaveApiReconcileRequestValidationError{}

// Validate checks the field values on LmsRefreshAndSaveApiReconcileResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LmsRefreshAndSaveApiReconcileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LmsRefreshAndSaveApiReconcileResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LmsRefreshAndSaveApiReconcileResponseMultiError, or nil if none found.
func (m *LmsRefreshAndSaveApiReconcileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LmsRefreshAndSaveApiReconcileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LmsRefreshAndSaveApiReconcileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LmsRefreshAndSaveApiReconcileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LmsRefreshAndSaveApiReconcileResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOutstandingAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LmsRefreshAndSaveApiReconcileResponseValidationError{
					field:  "OutstandingAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LmsRefreshAndSaveApiReconcileResponseValidationError{
					field:  "OutstandingAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOutstandingAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LmsRefreshAndSaveApiReconcileResponseValidationError{
				field:  "OutstandingAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LmsRefreshAndSaveApiReconcileResponseMultiError(errors)
	}

	return nil
}

// LmsRefreshAndSaveApiReconcileResponseMultiError is an error wrapping
// multiple validation errors returned by
// LmsRefreshAndSaveApiReconcileResponse.ValidateAll() if the designated
// constraints aren't met.
type LmsRefreshAndSaveApiReconcileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LmsRefreshAndSaveApiReconcileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LmsRefreshAndSaveApiReconcileResponseMultiError) AllErrors() []error { return m }

// LmsRefreshAndSaveApiReconcileResponseValidationError is the validation error
// returned by LmsRefreshAndSaveApiReconcileResponse.Validate if the
// designated constraints aren't met.
type LmsRefreshAndSaveApiReconcileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LmsRefreshAndSaveApiReconcileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LmsRefreshAndSaveApiReconcileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LmsRefreshAndSaveApiReconcileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LmsRefreshAndSaveApiReconcileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LmsRefreshAndSaveApiReconcileResponseValidationError) ErrorName() string {
	return "LmsRefreshAndSaveApiReconcileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LmsRefreshAndSaveApiReconcileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLmsRefreshAndSaveApiReconcileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LmsRefreshAndSaveApiReconcileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LmsRefreshAndSaveApiReconcileResponseValidationError{}

// Validate checks the field values on
// RefreshLmsFetchAndEvaluationReconcileRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RefreshLmsFetchAndEvaluationReconcileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RefreshLmsFetchAndEvaluationReconcileRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// RefreshLmsFetchAndEvaluationReconcileRequestMultiError, or nil if none found.
func (m *RefreshLmsFetchAndEvaluationReconcileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RefreshLmsFetchAndEvaluationReconcileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefreshLmsFetchAndEvaluationReconcileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefreshLmsFetchAndEvaluationReconcileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefreshLmsFetchAndEvaluationReconcileRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefreshLmsFetchAndEvaluationReconcileRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefreshLmsFetchAndEvaluationReconcileRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefreshLmsFetchAndEvaluationReconcileRequestValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanActivityType

	if all {
		switch v := interface{}(m.GetPaymentLoanStepExecution()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefreshLmsFetchAndEvaluationReconcileRequestValidationError{
					field:  "PaymentLoanStepExecution",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefreshLmsFetchAndEvaluationReconcileRequestValidationError{
					field:  "PaymentLoanStepExecution",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentLoanStepExecution()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefreshLmsFetchAndEvaluationReconcileRequestValidationError{
				field:  "PaymentLoanStepExecution",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanAccountId

	if all {
		switch v := interface{}(m.GetPreviousOutstandingAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefreshLmsFetchAndEvaluationReconcileRequestValidationError{
					field:  "PreviousOutstandingAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefreshLmsFetchAndEvaluationReconcileRequestValidationError{
					field:  "PreviousOutstandingAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreviousOutstandingAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefreshLmsFetchAndEvaluationReconcileRequestValidationError{
				field:  "PreviousOutstandingAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefreshLmsFetchAndEvaluationReconcileRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefreshLmsFetchAndEvaluationReconcileRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefreshLmsFetchAndEvaluationReconcileRequestValidationError{
				field:  "LoanHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RefreshLmsFetchAndEvaluationReconcileRequestMultiError(errors)
	}

	return nil
}

// RefreshLmsFetchAndEvaluationReconcileRequestMultiError is an error wrapping
// multiple validation errors returned by
// RefreshLmsFetchAndEvaluationReconcileRequest.ValidateAll() if the
// designated constraints aren't met.
type RefreshLmsFetchAndEvaluationReconcileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefreshLmsFetchAndEvaluationReconcileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefreshLmsFetchAndEvaluationReconcileRequestMultiError) AllErrors() []error { return m }

// RefreshLmsFetchAndEvaluationReconcileRequestValidationError is the
// validation error returned by
// RefreshLmsFetchAndEvaluationReconcileRequest.Validate if the designated
// constraints aren't met.
type RefreshLmsFetchAndEvaluationReconcileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefreshLmsFetchAndEvaluationReconcileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefreshLmsFetchAndEvaluationReconcileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefreshLmsFetchAndEvaluationReconcileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefreshLmsFetchAndEvaluationReconcileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefreshLmsFetchAndEvaluationReconcileRequestValidationError) ErrorName() string {
	return "RefreshLmsFetchAndEvaluationReconcileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RefreshLmsFetchAndEvaluationReconcileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefreshLmsFetchAndEvaluationReconcileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefreshLmsFetchAndEvaluationReconcileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefreshLmsFetchAndEvaluationReconcileRequestValidationError{}

// Validate checks the field values on
// RefreshLmsFetchAndEvaluationReconcileResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RefreshLmsFetchAndEvaluationReconcileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RefreshLmsFetchAndEvaluationReconcileResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// RefreshLmsFetchAndEvaluationReconcileResponseMultiError, or nil if none found.
func (m *RefreshLmsFetchAndEvaluationReconcileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RefreshLmsFetchAndEvaluationReconcileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefreshLmsFetchAndEvaluationReconcileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefreshLmsFetchAndEvaluationReconcileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefreshLmsFetchAndEvaluationReconcileResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefreshLmsFetchAndEvaluationReconcileResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefreshLmsFetchAndEvaluationReconcileResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefreshLmsFetchAndEvaluationReconcileResponseValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RefreshLmsFetchAndEvaluationReconcileResponseMultiError(errors)
	}

	return nil
}

// RefreshLmsFetchAndEvaluationReconcileResponseMultiError is an error wrapping
// multiple validation errors returned by
// RefreshLmsFetchAndEvaluationReconcileResponse.ValidateAll() if the
// designated constraints aren't met.
type RefreshLmsFetchAndEvaluationReconcileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefreshLmsFetchAndEvaluationReconcileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefreshLmsFetchAndEvaluationReconcileResponseMultiError) AllErrors() []error { return m }

// RefreshLmsFetchAndEvaluationReconcileResponseValidationError is the
// validation error returned by
// RefreshLmsFetchAndEvaluationReconcileResponse.Validate if the designated
// constraints aren't met.
type RefreshLmsFetchAndEvaluationReconcileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefreshLmsFetchAndEvaluationReconcileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefreshLmsFetchAndEvaluationReconcileResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e RefreshLmsFetchAndEvaluationReconcileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefreshLmsFetchAndEvaluationReconcileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefreshLmsFetchAndEvaluationReconcileResponseValidationError) ErrorName() string {
	return "RefreshLmsFetchAndEvaluationReconcileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RefreshLmsFetchAndEvaluationReconcileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefreshLmsFetchAndEvaluationReconcileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefreshLmsFetchAndEvaluationReconcileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefreshLmsFetchAndEvaluationReconcileResponseValidationError{}

// Validate checks the field values on CreateLoecRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateLoecRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLoecRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLoecRequestMultiError, or nil if none found.
func (m *CreateLoecRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLoecRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoecRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoecRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoecRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoecRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoecRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoecRequestValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoec()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoecRequestValidationError{
					field:  "Loec",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoecRequestValidationError{
					field:  "Loec",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoec()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoecRequestValidationError{
				field:  "Loec",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateLoecRequestMultiError(errors)
	}

	return nil
}

// CreateLoecRequestMultiError is an error wrapping multiple validation errors
// returned by CreateLoecRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateLoecRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLoecRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLoecRequestMultiError) AllErrors() []error { return m }

// CreateLoecRequestValidationError is the validation error returned by
// CreateLoecRequest.Validate if the designated constraints aren't met.
type CreateLoecRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLoecRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLoecRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLoecRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLoecRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLoecRequestValidationError) ErrorName() string {
	return "CreateLoecRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLoecRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLoecRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLoecRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLoecRequestValidationError{}

// Validate checks the field values on CheckPrePayStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckPrePayStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckPrePayStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckPrePayStatusRequestMultiError, or nil if none found.
func (m *CheckPrePayStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckPrePayStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckPrePayStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckPrePayStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckPrePayStatusRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PrePayLprId

	// no validation rules for Vendor

	// no validation rules for LoanProgram

	if len(errors) > 0 {
		return CheckPrePayStatusRequestMultiError(errors)
	}

	return nil
}

// CheckPrePayStatusRequestMultiError is an error wrapping multiple validation
// errors returned by CheckPrePayStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckPrePayStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckPrePayStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckPrePayStatusRequestMultiError) AllErrors() []error { return m }

// CheckPrePayStatusRequestValidationError is the validation error returned by
// CheckPrePayStatusRequest.Validate if the designated constraints aren't met.
type CheckPrePayStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckPrePayStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckPrePayStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckPrePayStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckPrePayStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckPrePayStatusRequestValidationError) ErrorName() string {
	return "CheckPrePayStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckPrePayStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckPrePayStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckPrePayStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckPrePayStatusRequestValidationError{}

// Validate checks the field values on RefreshLoanScheduleFromVendorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *RefreshLoanScheduleFromVendorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefreshLoanScheduleFromVendorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RefreshLoanScheduleFromVendorRequestMultiError, or nil if none found.
func (m *RefreshLoanScheduleFromVendorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RefreshLoanScheduleFromVendorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefreshLoanScheduleFromVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefreshLoanScheduleFromVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefreshLoanScheduleFromVendorRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefreshLoanScheduleFromVendorRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefreshLoanScheduleFromVendorRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefreshLoanScheduleFromVendorRequestValidationError{
				field:  "LoanHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Identifier.(type) {
	case *RefreshLoanScheduleFromVendorRequest_LoanRequestId:
		if v == nil {
			err := RefreshLoanScheduleFromVendorRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for LoanRequestId
	case *RefreshLoanScheduleFromVendorRequest_LoanAccountId:
		if v == nil {
			err := RefreshLoanScheduleFromVendorRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for LoanAccountId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RefreshLoanScheduleFromVendorRequestMultiError(errors)
	}

	return nil
}

// RefreshLoanScheduleFromVendorRequestMultiError is an error wrapping multiple
// validation errors returned by
// RefreshLoanScheduleFromVendorRequest.ValidateAll() if the designated
// constraints aren't met.
type RefreshLoanScheduleFromVendorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefreshLoanScheduleFromVendorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefreshLoanScheduleFromVendorRequestMultiError) AllErrors() []error { return m }

// RefreshLoanScheduleFromVendorRequestValidationError is the validation error
// returned by RefreshLoanScheduleFromVendorRequest.Validate if the designated
// constraints aren't met.
type RefreshLoanScheduleFromVendorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefreshLoanScheduleFromVendorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefreshLoanScheduleFromVendorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefreshLoanScheduleFromVendorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefreshLoanScheduleFromVendorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefreshLoanScheduleFromVendorRequestValidationError) ErrorName() string {
	return "RefreshLoanScheduleFromVendorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RefreshLoanScheduleFromVendorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefreshLoanScheduleFromVendorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefreshLoanScheduleFromVendorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefreshLoanScheduleFromVendorRequestValidationError{}

// Validate checks the field values on RefreshLoanScheduleFromVendorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *RefreshLoanScheduleFromVendorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefreshLoanScheduleFromVendorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RefreshLoanScheduleFromVendorResponseMultiError, or nil if none found.
func (m *RefreshLoanScheduleFromVendorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RefreshLoanScheduleFromVendorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefreshLoanScheduleFromVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefreshLoanScheduleFromVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefreshLoanScheduleFromVendorResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RefreshLoanScheduleFromVendorResponseMultiError(errors)
	}

	return nil
}

// RefreshLoanScheduleFromVendorResponseMultiError is an error wrapping
// multiple validation errors returned by
// RefreshLoanScheduleFromVendorResponse.ValidateAll() if the designated
// constraints aren't met.
type RefreshLoanScheduleFromVendorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefreshLoanScheduleFromVendorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefreshLoanScheduleFromVendorResponseMultiError) AllErrors() []error { return m }

// RefreshLoanScheduleFromVendorResponseValidationError is the validation error
// returned by RefreshLoanScheduleFromVendorResponse.Validate if the
// designated constraints aren't met.
type RefreshLoanScheduleFromVendorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefreshLoanScheduleFromVendorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefreshLoanScheduleFromVendorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefreshLoanScheduleFromVendorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefreshLoanScheduleFromVendorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefreshLoanScheduleFromVendorResponseValidationError) ErrorName() string {
	return "RefreshLoanScheduleFromVendorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RefreshLoanScheduleFromVendorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefreshLoanScheduleFromVendorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefreshLoanScheduleFromVendorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefreshLoanScheduleFromVendorResponseValidationError{}

// Validate checks the field values on UpdateLoanRequestActivityV2Request with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateLoanRequestActivityV2Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLoanRequestActivityV2Request
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateLoanRequestActivityV2RequestMultiError, or nil if none found.
func (m *UpdateLoanRequestActivityV2Request) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLoanRequestActivityV2Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLoanRequestActivityV2RequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLoanRequestActivityV2RequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLoanRequestActivityV2RequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanRequestStatus

	// no validation rules for LoanRequestSubStatus

	// no validation rules for OrchId

	if len(errors) > 0 {
		return UpdateLoanRequestActivityV2RequestMultiError(errors)
	}

	return nil
}

// UpdateLoanRequestActivityV2RequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateLoanRequestActivityV2Request.ValidateAll() if the designated
// constraints aren't met.
type UpdateLoanRequestActivityV2RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLoanRequestActivityV2RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLoanRequestActivityV2RequestMultiError) AllErrors() []error { return m }

// UpdateLoanRequestActivityV2RequestValidationError is the validation error
// returned by UpdateLoanRequestActivityV2Request.Validate if the designated
// constraints aren't met.
type UpdateLoanRequestActivityV2RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLoanRequestActivityV2RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLoanRequestActivityV2RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLoanRequestActivityV2RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLoanRequestActivityV2RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLoanRequestActivityV2RequestValidationError) ErrorName() string {
	return "UpdateLoanRequestActivityV2RequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLoanRequestActivityV2RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLoanRequestActivityV2Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLoanRequestActivityV2RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLoanRequestActivityV2RequestValidationError{}

// Validate checks the field values on UpdateLoanRequestActivityV2Response with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateLoanRequestActivityV2Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLoanRequestActivityV2Response
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateLoanRequestActivityV2ResponseMultiError, or nil if none found.
func (m *UpdateLoanRequestActivityV2Response) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLoanRequestActivityV2Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLoanRequestActivityV2ResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLoanRequestActivityV2ResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLoanRequestActivityV2ResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateLoanRequestActivityV2ResponseMultiError(errors)
	}

	return nil
}

// UpdateLoanRequestActivityV2ResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateLoanRequestActivityV2Response.ValidateAll() if the designated
// constraints aren't met.
type UpdateLoanRequestActivityV2ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLoanRequestActivityV2ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLoanRequestActivityV2ResponseMultiError) AllErrors() []error { return m }

// UpdateLoanRequestActivityV2ResponseValidationError is the validation error
// returned by UpdateLoanRequestActivityV2Response.Validate if the designated
// constraints aren't met.
type UpdateLoanRequestActivityV2ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLoanRequestActivityV2ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLoanRequestActivityV2ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLoanRequestActivityV2ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLoanRequestActivityV2ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLoanRequestActivityV2ResponseValidationError) ErrorName() string {
	return "UpdateLoanRequestActivityV2ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLoanRequestActivityV2ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLoanRequestActivityV2Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLoanRequestActivityV2ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLoanRequestActivityV2ResponseValidationError{}

// Validate checks the field values on AbflPollingActivityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AbflPollingActivityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AbflPollingActivityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AbflPollingActivityRequestMultiError, or nil if none found.
func (m *AbflPollingActivityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AbflPollingActivityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AbflPollingActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AbflPollingActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AbflPollingActivityRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AbflPollingActivityRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AbflPollingActivityRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AbflPollingActivityRequestValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	// no validation rules for LoanProgram

	// no validation rules for CallbackData

	if len(errors) > 0 {
		return AbflPollingActivityRequestMultiError(errors)
	}

	return nil
}

// AbflPollingActivityRequestMultiError is an error wrapping multiple
// validation errors returned by AbflPollingActivityRequest.ValidateAll() if
// the designated constraints aren't met.
type AbflPollingActivityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AbflPollingActivityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AbflPollingActivityRequestMultiError) AllErrors() []error { return m }

// AbflPollingActivityRequestValidationError is the validation error returned
// by AbflPollingActivityRequest.Validate if the designated constraints aren't met.
type AbflPollingActivityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AbflPollingActivityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AbflPollingActivityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AbflPollingActivityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AbflPollingActivityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AbflPollingActivityRequestValidationError) ErrorName() string {
	return "AbflPollingActivityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AbflPollingActivityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAbflPollingActivityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AbflPollingActivityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AbflPollingActivityRequestValidationError{}

// Validate checks the field values on ProcessCallbackNotificationsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessCallbackNotificationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCallbackNotificationsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCallbackNotificationsRequestMultiError, or nil if none found.
func (m *ProcessCallbackNotificationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCallbackNotificationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCallbackNotificationsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCallbackNotificationsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCallbackNotificationsRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCallbackNotificationsRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCallbackNotificationsRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCallbackNotificationsRequestValidationError{
				field:  "LoanHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoansNotification()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCallbackNotificationsRequestValidationError{
					field:  "LoansNotification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCallbackNotificationsRequestValidationError{
					field:  "LoansNotification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoansNotification()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCallbackNotificationsRequestValidationError{
				field:  "LoansNotification",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCallbackNotificationsRequestMultiError(errors)
	}

	return nil
}

// ProcessCallbackNotificationsRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessCallbackNotificationsRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessCallbackNotificationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCallbackNotificationsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCallbackNotificationsRequestMultiError) AllErrors() []error { return m }

// ProcessCallbackNotificationsRequestValidationError is the validation error
// returned by ProcessCallbackNotificationsRequest.Validate if the designated
// constraints aren't met.
type ProcessCallbackNotificationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCallbackNotificationsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCallbackNotificationsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCallbackNotificationsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCallbackNotificationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCallbackNotificationsRequestValidationError) ErrorName() string {
	return "ProcessCallbackNotificationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCallbackNotificationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCallbackNotificationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCallbackNotificationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCallbackNotificationsRequestValidationError{}

// Validate checks the field values on ProcessCallbackNotificationsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessCallbackNotificationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCallbackNotificationsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCallbackNotificationsResponseMultiError, or nil if none found.
func (m *ProcessCallbackNotificationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCallbackNotificationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCallbackNotificationsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCallbackNotificationsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCallbackNotificationsResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCallbackNotificationsResponseMultiError(errors)
	}

	return nil
}

// ProcessCallbackNotificationsResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessCallbackNotificationsResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessCallbackNotificationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCallbackNotificationsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCallbackNotificationsResponseMultiError) AllErrors() []error { return m }

// ProcessCallbackNotificationsResponseValidationError is the validation error
// returned by ProcessCallbackNotificationsResponse.Validate if the designated
// constraints aren't met.
type ProcessCallbackNotificationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCallbackNotificationsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCallbackNotificationsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCallbackNotificationsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCallbackNotificationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCallbackNotificationsResponseValidationError) ErrorName() string {
	return "ProcessCallbackNotificationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCallbackNotificationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCallbackNotificationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCallbackNotificationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCallbackNotificationsResponseValidationError{}

// Validate checks the field values on LamfValidateLoanAmountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LamfValidateLoanAmountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LamfValidateLoanAmountResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LamfValidateLoanAmountResponseMultiError, or nil if none found.
func (m *LamfValidateLoanAmountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LamfValidateLoanAmountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LamfValidateLoanAmountResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LamfValidateLoanAmountResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LamfValidateLoanAmountResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LamfValidateLoanAmountResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LamfValidateLoanAmountResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LamfValidateLoanAmountResponseValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LamfValidateLoanAmountResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LamfValidateLoanAmountResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LamfValidateLoanAmountResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PartialLienApproved

	if len(errors) > 0 {
		return LamfValidateLoanAmountResponseMultiError(errors)
	}

	return nil
}

// LamfValidateLoanAmountResponseMultiError is an error wrapping multiple
// validation errors returned by LamfValidateLoanAmountResponse.ValidateAll()
// if the designated constraints aren't met.
type LamfValidateLoanAmountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LamfValidateLoanAmountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LamfValidateLoanAmountResponseMultiError) AllErrors() []error { return m }

// LamfValidateLoanAmountResponseValidationError is the validation error
// returned by LamfValidateLoanAmountResponse.Validate if the designated
// constraints aren't met.
type LamfValidateLoanAmountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LamfValidateLoanAmountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LamfValidateLoanAmountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LamfValidateLoanAmountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LamfValidateLoanAmountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LamfValidateLoanAmountResponseValidationError) ErrorName() string {
	return "LamfValidateLoanAmountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LamfValidateLoanAmountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLamfValidateLoanAmountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LamfValidateLoanAmountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LamfValidateLoanAmountResponseValidationError{}

// Validate checks the field values on LamfInitKycActivityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LamfInitKycActivityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LamfInitKycActivityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LamfInitKycActivityResponseMultiError, or nil if none found.
func (m *LamfInitKycActivityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LamfInitKycActivityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LamfInitKycActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LamfInitKycActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LamfInitKycActivityResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LamfInitKycActivityResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LamfInitKycActivityResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LamfInitKycActivityResponseValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LamfInitKycActivityResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LamfInitKycActivityResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LamfInitKycActivityResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsKycApproved

	if len(errors) > 0 {
		return LamfInitKycActivityResponseMultiError(errors)
	}

	return nil
}

// LamfInitKycActivityResponseMultiError is an error wrapping multiple
// validation errors returned by LamfInitKycActivityResponse.ValidateAll() if
// the designated constraints aren't met.
type LamfInitKycActivityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LamfInitKycActivityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LamfInitKycActivityResponseMultiError) AllErrors() []error { return m }

// LamfInitKycActivityResponseValidationError is the validation error returned
// by LamfInitKycActivityResponse.Validate if the designated constraints
// aren't met.
type LamfInitKycActivityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LamfInitKycActivityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LamfInitKycActivityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LamfInitKycActivityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LamfInitKycActivityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LamfInitKycActivityResponseValidationError) ErrorName() string {
	return "LamfInitKycActivityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LamfInitKycActivityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLamfInitKycActivityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LamfInitKycActivityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LamfInitKycActivityResponseValidationError{}

// Validate checks the field values on FiftyfinFetchMfPortfolioRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FiftyfinFetchMfPortfolioRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiftyfinFetchMfPortfolioRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FiftyfinFetchMfPortfolioRequestMultiError, or nil if none found.
func (m *FiftyfinFetchMfPortfolioRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FiftyfinFetchMfPortfolioRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinFetchMfPortfolioRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinFetchMfPortfolioRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinFetchMfPortfolioRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinFetchMfPortfolioRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinFetchMfPortfolioRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinFetchMfPortfolioRequestValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	// no validation rules for LoanProgram

	// no validation rules for SkipAccountDetailUpdate

	// no validation rules for FetchMfcPf

	// no validation rules for FetchFiftyfinPf

	// no validation rules for SkipApplicationProgressScreen

	if len(errors) > 0 {
		return FiftyfinFetchMfPortfolioRequestMultiError(errors)
	}

	return nil
}

// FiftyfinFetchMfPortfolioRequestMultiError is an error wrapping multiple
// validation errors returned by FiftyfinFetchMfPortfolioRequest.ValidateAll()
// if the designated constraints aren't met.
type FiftyfinFetchMfPortfolioRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiftyfinFetchMfPortfolioRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiftyfinFetchMfPortfolioRequestMultiError) AllErrors() []error { return m }

// FiftyfinFetchMfPortfolioRequestValidationError is the validation error
// returned by FiftyfinFetchMfPortfolioRequest.Validate if the designated
// constraints aren't met.
type FiftyfinFetchMfPortfolioRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiftyfinFetchMfPortfolioRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiftyfinFetchMfPortfolioRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiftyfinFetchMfPortfolioRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiftyfinFetchMfPortfolioRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiftyfinFetchMfPortfolioRequestValidationError) ErrorName() string {
	return "FiftyfinFetchMfPortfolioRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FiftyfinFetchMfPortfolioRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiftyfinFetchMfPortfolioRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiftyfinFetchMfPortfolioRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiftyfinFetchMfPortfolioRequestValidationError{}

// Validate checks the field values on FiftyfinFetchMfPortfolioResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FiftyfinFetchMfPortfolioResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiftyfinFetchMfPortfolioResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FiftyfinFetchMfPortfolioResponseMultiError, or nil if none found.
func (m *FiftyfinFetchMfPortfolioResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FiftyfinFetchMfPortfolioResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinFetchMfPortfolioResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinFetchMfPortfolioResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinFetchMfPortfolioResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinFetchMfPortfolioResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinFetchMfPortfolioResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinFetchMfPortfolioResponseValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinFetchMfPortfolioResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinFetchMfPortfolioResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinFetchMfPortfolioResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PfFetchStatus

	// no validation rules for PfFetchSubStatus

	if len(errors) > 0 {
		return FiftyfinFetchMfPortfolioResponseMultiError(errors)
	}

	return nil
}

// FiftyfinFetchMfPortfolioResponseMultiError is an error wrapping multiple
// validation errors returned by
// FiftyfinFetchMfPortfolioResponse.ValidateAll() if the designated
// constraints aren't met.
type FiftyfinFetchMfPortfolioResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiftyfinFetchMfPortfolioResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiftyfinFetchMfPortfolioResponseMultiError) AllErrors() []error { return m }

// FiftyfinFetchMfPortfolioResponseValidationError is the validation error
// returned by FiftyfinFetchMfPortfolioResponse.Validate if the designated
// constraints aren't met.
type FiftyfinFetchMfPortfolioResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiftyfinFetchMfPortfolioResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiftyfinFetchMfPortfolioResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiftyfinFetchMfPortfolioResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiftyfinFetchMfPortfolioResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiftyfinFetchMfPortfolioResponseValidationError) ErrorName() string {
	return "FiftyfinFetchMfPortfolioResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FiftyfinFetchMfPortfolioResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiftyfinFetchMfPortfolioResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiftyfinFetchMfPortfolioResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiftyfinFetchMfPortfolioResponseValidationError{}

// Validate checks the field values on FiftyfinFundVerificationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FiftyfinFundVerificationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiftyfinFundVerificationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FiftyfinFundVerificationRequestMultiError, or nil if none found.
func (m *FiftyfinFundVerificationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FiftyfinFundVerificationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinFundVerificationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinFundVerificationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinFundVerificationRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinFundVerificationRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinFundVerificationRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinFundVerificationRequestValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	// no validation rules for LoanProgram

	if len(errors) > 0 {
		return FiftyfinFundVerificationRequestMultiError(errors)
	}

	return nil
}

// FiftyfinFundVerificationRequestMultiError is an error wrapping multiple
// validation errors returned by FiftyfinFundVerificationRequest.ValidateAll()
// if the designated constraints aren't met.
type FiftyfinFundVerificationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiftyfinFundVerificationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiftyfinFundVerificationRequestMultiError) AllErrors() []error { return m }

// FiftyfinFundVerificationRequestValidationError is the validation error
// returned by FiftyfinFundVerificationRequest.Validate if the designated
// constraints aren't met.
type FiftyfinFundVerificationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiftyfinFundVerificationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiftyfinFundVerificationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiftyfinFundVerificationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiftyfinFundVerificationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiftyfinFundVerificationRequestValidationError) ErrorName() string {
	return "FiftyfinFundVerificationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FiftyfinFundVerificationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiftyfinFundVerificationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiftyfinFundVerificationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiftyfinFundVerificationRequestValidationError{}

// Validate checks the field values on FiftyfinFundVerificationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FiftyfinFundVerificationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiftyfinFundVerificationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FiftyfinFundVerificationResponseMultiError, or nil if none found.
func (m *FiftyfinFundVerificationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FiftyfinFundVerificationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinFundVerificationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinFundVerificationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinFundVerificationResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinFundVerificationResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinFundVerificationResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinFundVerificationResponseValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinFundVerificationResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinFundVerificationResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinFundVerificationResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FundVerificationSuccessful

	if len(errors) > 0 {
		return FiftyfinFundVerificationResponseMultiError(errors)
	}

	return nil
}

// FiftyfinFundVerificationResponseMultiError is an error wrapping multiple
// validation errors returned by
// FiftyfinFundVerificationResponse.ValidateAll() if the designated
// constraints aren't met.
type FiftyfinFundVerificationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiftyfinFundVerificationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiftyfinFundVerificationResponseMultiError) AllErrors() []error { return m }

// FiftyfinFundVerificationResponseValidationError is the validation error
// returned by FiftyfinFundVerificationResponse.Validate if the designated
// constraints aren't met.
type FiftyfinFundVerificationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiftyfinFundVerificationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiftyfinFundVerificationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiftyfinFundVerificationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiftyfinFundVerificationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiftyfinFundVerificationResponseValidationError) ErrorName() string {
	return "FiftyfinFundVerificationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FiftyfinFundVerificationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiftyfinFundVerificationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiftyfinFundVerificationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiftyfinFundVerificationResponseValidationError{}

// Validate checks the field values on FiftyfinWaitForUserActionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FiftyfinWaitForUserActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiftyfinWaitForUserActionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FiftyfinWaitForUserActionRequestMultiError, or nil if none found.
func (m *FiftyfinWaitForUserActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FiftyfinWaitForUserActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinWaitForUserActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinWaitForUserActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinWaitForUserActionRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinWaitForUserActionRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinWaitForUserActionRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinWaitForUserActionRequestValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	// no validation rules for LoanProgram

	if len(errors) > 0 {
		return FiftyfinWaitForUserActionRequestMultiError(errors)
	}

	return nil
}

// FiftyfinWaitForUserActionRequestMultiError is an error wrapping multiple
// validation errors returned by
// FiftyfinWaitForUserActionRequest.ValidateAll() if the designated
// constraints aren't met.
type FiftyfinWaitForUserActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiftyfinWaitForUserActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiftyfinWaitForUserActionRequestMultiError) AllErrors() []error { return m }

// FiftyfinWaitForUserActionRequestValidationError is the validation error
// returned by FiftyfinWaitForUserActionRequest.Validate if the designated
// constraints aren't met.
type FiftyfinWaitForUserActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiftyfinWaitForUserActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiftyfinWaitForUserActionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiftyfinWaitForUserActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiftyfinWaitForUserActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiftyfinWaitForUserActionRequestValidationError) ErrorName() string {
	return "FiftyfinWaitForUserActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FiftyfinWaitForUserActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiftyfinWaitForUserActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiftyfinWaitForUserActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiftyfinWaitForUserActionRequestValidationError{}

// Validate checks the field values on FiftyfinWaitForUserActionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FiftyfinWaitForUserActionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiftyfinWaitForUserActionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FiftyfinWaitForUserActionResponseMultiError, or nil if none found.
func (m *FiftyfinWaitForUserActionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FiftyfinWaitForUserActionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinWaitForUserActionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinWaitForUserActionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinWaitForUserActionResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinWaitForUserActionResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinWaitForUserActionResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinWaitForUserActionResponseValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinWaitForUserActionResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinWaitForUserActionResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinWaitForUserActionResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FiftyfinWaitForUserActionResponseMultiError(errors)
	}

	return nil
}

// FiftyfinWaitForUserActionResponseMultiError is an error wrapping multiple
// validation errors returned by
// FiftyfinWaitForUserActionResponse.ValidateAll() if the designated
// constraints aren't met.
type FiftyfinWaitForUserActionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiftyfinWaitForUserActionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiftyfinWaitForUserActionResponseMultiError) AllErrors() []error { return m }

// FiftyfinWaitForUserActionResponseValidationError is the validation error
// returned by FiftyfinWaitForUserActionResponse.Validate if the designated
// constraints aren't met.
type FiftyfinWaitForUserActionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiftyfinWaitForUserActionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiftyfinWaitForUserActionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiftyfinWaitForUserActionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiftyfinWaitForUserActionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiftyfinWaitForUserActionResponseValidationError) ErrorName() string {
	return "FiftyfinWaitForUserActionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FiftyfinWaitForUserActionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiftyfinWaitForUserActionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiftyfinWaitForUserActionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiftyfinWaitForUserActionResponseValidationError{}

// Validate checks the field values on AsyncLoanStepStateCheckRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AsyncLoanStepStateCheckRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AsyncLoanStepStateCheckRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AsyncLoanStepStateCheckRequestMultiError, or nil if none found.
func (m *AsyncLoanStepStateCheckRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AsyncLoanStepStateCheckRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AsyncLoanStepStateCheckRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AsyncLoanStepStateCheckRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AsyncLoanStepStateCheckRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LseStatus

	// no validation rules for LseSubStatus

	// no validation rules for LseId

	if len(errors) > 0 {
		return AsyncLoanStepStateCheckRequestMultiError(errors)
	}

	return nil
}

// AsyncLoanStepStateCheckRequestMultiError is an error wrapping multiple
// validation errors returned by AsyncLoanStepStateCheckRequest.ValidateAll()
// if the designated constraints aren't met.
type AsyncLoanStepStateCheckRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AsyncLoanStepStateCheckRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AsyncLoanStepStateCheckRequestMultiError) AllErrors() []error { return m }

// AsyncLoanStepStateCheckRequestValidationError is the validation error
// returned by AsyncLoanStepStateCheckRequest.Validate if the designated
// constraints aren't met.
type AsyncLoanStepStateCheckRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AsyncLoanStepStateCheckRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AsyncLoanStepStateCheckRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AsyncLoanStepStateCheckRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AsyncLoanStepStateCheckRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AsyncLoanStepStateCheckRequestValidationError) ErrorName() string {
	return "AsyncLoanStepStateCheckRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AsyncLoanStepStateCheckRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAsyncLoanStepStateCheckRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AsyncLoanStepStateCheckRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AsyncLoanStepStateCheckRequestValidationError{}

// Validate checks the field values on AsyncLoanStepStateCheckResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AsyncLoanStepStateCheckResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AsyncLoanStepStateCheckResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AsyncLoanStepStateCheckResponseMultiError, or nil if none found.
func (m *AsyncLoanStepStateCheckResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AsyncLoanStepStateCheckResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AsyncLoanStepStateCheckResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AsyncLoanStepStateCheckResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AsyncLoanStepStateCheckResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AsyncLoanStepStateCheckResponseMultiError(errors)
	}

	return nil
}

// AsyncLoanStepStateCheckResponseMultiError is an error wrapping multiple
// validation errors returned by AsyncLoanStepStateCheckResponse.ValidateAll()
// if the designated constraints aren't met.
type AsyncLoanStepStateCheckResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AsyncLoanStepStateCheckResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AsyncLoanStepStateCheckResponseMultiError) AllErrors() []error { return m }

// AsyncLoanStepStateCheckResponseValidationError is the validation error
// returned by AsyncLoanStepStateCheckResponse.Validate if the designated
// constraints aren't met.
type AsyncLoanStepStateCheckResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AsyncLoanStepStateCheckResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AsyncLoanStepStateCheckResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AsyncLoanStepStateCheckResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AsyncLoanStepStateCheckResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AsyncLoanStepStateCheckResponseValidationError) ErrorName() string {
	return "AsyncLoanStepStateCheckResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AsyncLoanStepStateCheckResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAsyncLoanStepStateCheckResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AsyncLoanStepStateCheckResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AsyncLoanStepStateCheckResponseValidationError{}

// Validate checks the field values on DeactivateLoanOfferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeactivateLoanOfferRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeactivateLoanOfferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeactivateLoanOfferRequestMultiError, or nil if none found.
func (m *DeactivateLoanOfferRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeactivateLoanOfferRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeactivateLoanOfferRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeactivateLoanOfferRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeactivateLoanOfferRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeactivateLoanOfferRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeactivateLoanOfferRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeactivateLoanOfferRequestValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeactivateLoanOfferRequestMultiError(errors)
	}

	return nil
}

// DeactivateLoanOfferRequestMultiError is an error wrapping multiple
// validation errors returned by DeactivateLoanOfferRequest.ValidateAll() if
// the designated constraints aren't met.
type DeactivateLoanOfferRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeactivateLoanOfferRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeactivateLoanOfferRequestMultiError) AllErrors() []error { return m }

// DeactivateLoanOfferRequestValidationError is the validation error returned
// by DeactivateLoanOfferRequest.Validate if the designated constraints aren't met.
type DeactivateLoanOfferRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeactivateLoanOfferRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeactivateLoanOfferRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeactivateLoanOfferRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeactivateLoanOfferRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeactivateLoanOfferRequestValidationError) ErrorName() string {
	return "DeactivateLoanOfferRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeactivateLoanOfferRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeactivateLoanOfferRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeactivateLoanOfferRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeactivateLoanOfferRequestValidationError{}

// Validate checks the field values on DeactivateLoanOfferResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeactivateLoanOfferResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeactivateLoanOfferResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeactivateLoanOfferResponseMultiError, or nil if none found.
func (m *DeactivateLoanOfferResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeactivateLoanOfferResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeactivateLoanOfferResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeactivateLoanOfferResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeactivateLoanOfferResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeactivateLoanOfferResponseMultiError(errors)
	}

	return nil
}

// DeactivateLoanOfferResponseMultiError is an error wrapping multiple
// validation errors returned by DeactivateLoanOfferResponse.ValidateAll() if
// the designated constraints aren't met.
type DeactivateLoanOfferResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeactivateLoanOfferResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeactivateLoanOfferResponseMultiError) AllErrors() []error { return m }

// DeactivateLoanOfferResponseValidationError is the validation error returned
// by DeactivateLoanOfferResponse.Validate if the designated constraints
// aren't met.
type DeactivateLoanOfferResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeactivateLoanOfferResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeactivateLoanOfferResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeactivateLoanOfferResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeactivateLoanOfferResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeactivateLoanOfferResponseValidationError) ErrorName() string {
	return "DeactivateLoanOfferResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeactivateLoanOfferResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeactivateLoanOfferResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeactivateLoanOfferResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeactivateLoanOfferResponseValidationError{}

// Validate checks the field values on CreateLoanActivityEntryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLoanActivityEntryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLoanActivityEntryRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateLoanActivityEntryRequestMultiError, or nil if none found.
func (m *CreateLoanActivityEntryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLoanActivityEntryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLoanActivityEntry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanActivityEntryRequestValidationError{
					field:  "LoanActivityEntry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanActivityEntryRequestValidationError{
					field:  "LoanActivityEntry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanActivityEntry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanActivityEntryRequestValidationError{
				field:  "LoanActivityEntry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MarkNonRetryableAsExpired

	if len(errors) > 0 {
		return CreateLoanActivityEntryRequestMultiError(errors)
	}

	return nil
}

// CreateLoanActivityEntryRequestMultiError is an error wrapping multiple
// validation errors returned by CreateLoanActivityEntryRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateLoanActivityEntryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLoanActivityEntryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLoanActivityEntryRequestMultiError) AllErrors() []error { return m }

// CreateLoanActivityEntryRequestValidationError is the validation error
// returned by CreateLoanActivityEntryRequest.Validate if the designated
// constraints aren't met.
type CreateLoanActivityEntryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLoanActivityEntryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLoanActivityEntryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLoanActivityEntryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLoanActivityEntryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLoanActivityEntryRequestValidationError) ErrorName() string {
	return "CreateLoanActivityEntryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLoanActivityEntryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLoanActivityEntryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLoanActivityEntryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLoanActivityEntryRequestValidationError{}

// Validate checks the field values on GetExpiredPropertyForStageRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetExpiredPropertyForStageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExpiredPropertyForStageRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetExpiredPropertyForStageRequestMultiError, or nil if none found.
func (m *GetExpiredPropertyForStageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExpiredPropertyForStageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExpiredPropertyForStageRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExpiredPropertyForStageRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExpiredPropertyForStageRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LseStatus

	// no validation rules for LseId

	if len(errors) > 0 {
		return GetExpiredPropertyForStageRequestMultiError(errors)
	}

	return nil
}

// GetExpiredPropertyForStageRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetExpiredPropertyForStageRequest.ValidateAll() if the designated
// constraints aren't met.
type GetExpiredPropertyForStageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExpiredPropertyForStageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExpiredPropertyForStageRequestMultiError) AllErrors() []error { return m }

// GetExpiredPropertyForStageRequestValidationError is the validation error
// returned by GetExpiredPropertyForStageRequest.Validate if the designated
// constraints aren't met.
type GetExpiredPropertyForStageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExpiredPropertyForStageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExpiredPropertyForStageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExpiredPropertyForStageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExpiredPropertyForStageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExpiredPropertyForStageRequestValidationError) ErrorName() string {
	return "GetExpiredPropertyForStageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExpiredPropertyForStageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExpiredPropertyForStageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExpiredPropertyForStageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExpiredPropertyForStageRequestValidationError{}

// Validate checks the field values on GetExpiredPropertyForStageResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetExpiredPropertyForStageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExpiredPropertyForStageResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetExpiredPropertyForStageResponseMultiError, or nil if none found.
func (m *GetExpiredPropertyForStageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExpiredPropertyForStageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExpiredPropertyForStageResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExpiredPropertyForStageResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExpiredPropertyForStageResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MarkNonRetryableAsExpired

	if len(errors) > 0 {
		return GetExpiredPropertyForStageResponseMultiError(errors)
	}

	return nil
}

// GetExpiredPropertyForStageResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetExpiredPropertyForStageResponse.ValidateAll() if the designated
// constraints aren't met.
type GetExpiredPropertyForStageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExpiredPropertyForStageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExpiredPropertyForStageResponseMultiError) AllErrors() []error { return m }

// GetExpiredPropertyForStageResponseValidationError is the validation error
// returned by GetExpiredPropertyForStageResponse.Validate if the designated
// constraints aren't met.
type GetExpiredPropertyForStageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExpiredPropertyForStageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExpiredPropertyForStageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExpiredPropertyForStageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExpiredPropertyForStageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExpiredPropertyForStageResponseValidationError) ErrorName() string {
	return "GetExpiredPropertyForStageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExpiredPropertyForStageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExpiredPropertyForStageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExpiredPropertyForStageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExpiredPropertyForStageResponseValidationError{}

// Validate checks the field values on FiftyfinVoidLoanRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FiftyfinVoidLoanRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiftyfinVoidLoanRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FiftyfinVoidLoanRequestMultiError, or nil if none found.
func (m *FiftyfinVoidLoanRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FiftyfinVoidLoanRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinVoidLoanRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinVoidLoanRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinVoidLoanRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinVoidLoanRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinVoidLoanRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinVoidLoanRequestValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	// no validation rules for LoanProgram

	// no validation rules for RestartLoan

	// no validation rules for UnpledgeFunds

	if len(errors) > 0 {
		return FiftyfinVoidLoanRequestMultiError(errors)
	}

	return nil
}

// FiftyfinVoidLoanRequestMultiError is an error wrapping multiple validation
// errors returned by FiftyfinVoidLoanRequest.ValidateAll() if the designated
// constraints aren't met.
type FiftyfinVoidLoanRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiftyfinVoidLoanRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiftyfinVoidLoanRequestMultiError) AllErrors() []error { return m }

// FiftyfinVoidLoanRequestValidationError is the validation error returned by
// FiftyfinVoidLoanRequest.Validate if the designated constraints aren't met.
type FiftyfinVoidLoanRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiftyfinVoidLoanRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiftyfinVoidLoanRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiftyfinVoidLoanRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiftyfinVoidLoanRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiftyfinVoidLoanRequestValidationError) ErrorName() string {
	return "FiftyfinVoidLoanRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FiftyfinVoidLoanRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiftyfinVoidLoanRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiftyfinVoidLoanRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiftyfinVoidLoanRequestValidationError{}

// Validate checks the field values on FiftyfinVoidLoanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FiftyfinVoidLoanResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiftyfinVoidLoanResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FiftyfinVoidLoanResponseMultiError, or nil if none found.
func (m *FiftyfinVoidLoanResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FiftyfinVoidLoanResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinVoidLoanResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinVoidLoanResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinVoidLoanResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinVoidLoanResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinVoidLoanResponseValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinVoidLoanResponseValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinVoidLoanResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinVoidLoanResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinVoidLoanResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FiftyfinVoidLoanResponseMultiError(errors)
	}

	return nil
}

// FiftyfinVoidLoanResponseMultiError is an error wrapping multiple validation
// errors returned by FiftyfinVoidLoanResponse.ValidateAll() if the designated
// constraints aren't met.
type FiftyfinVoidLoanResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiftyfinVoidLoanResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiftyfinVoidLoanResponseMultiError) AllErrors() []error { return m }

// FiftyfinVoidLoanResponseValidationError is the validation error returned by
// FiftyfinVoidLoanResponse.Validate if the designated constraints aren't met.
type FiftyfinVoidLoanResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiftyfinVoidLoanResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiftyfinVoidLoanResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiftyfinVoidLoanResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiftyfinVoidLoanResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiftyfinVoidLoanResponseValidationError) ErrorName() string {
	return "FiftyfinVoidLoanResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FiftyfinVoidLoanResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiftyfinVoidLoanResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiftyfinVoidLoanResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiftyfinVoidLoanResponseValidationError{}

// Validate checks the field values on UpdateLoecRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateLoecRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLoecRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLoecRequestMultiError, or nil if none found.
func (m *UpdateLoecRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLoecRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLoecRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLoecRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLoecRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLoecRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLoecRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLoecRequestValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanProgram

	// no validation rules for LoanOfferEligibilityCriteriaStatus

	// no validation rules for LoanOfferEligibilityCriteriaSubStatus

	if len(errors) > 0 {
		return UpdateLoecRequestMultiError(errors)
	}

	return nil
}

// UpdateLoecRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateLoecRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateLoecRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLoecRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLoecRequestMultiError) AllErrors() []error { return m }

// UpdateLoecRequestValidationError is the validation error returned by
// UpdateLoecRequest.Validate if the designated constraints aren't met.
type UpdateLoecRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLoecRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLoecRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLoecRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLoecRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLoecRequestValidationError) ErrorName() string {
	return "UpdateLoecRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLoecRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLoecRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLoecRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLoecRequestValidationError{}

// Validate checks the field values on GetLoanOfferActivityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanOfferActivityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanOfferActivityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanOfferActivityRequestMultiError, or nil if none found.
func (m *GetLoanOfferActivityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanOfferActivityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanOfferActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanOfferActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanOfferActivityRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OfferId

	if len(errors) > 0 {
		return GetLoanOfferActivityRequestMultiError(errors)
	}

	return nil
}

// GetLoanOfferActivityRequestMultiError is an error wrapping multiple
// validation errors returned by GetLoanOfferActivityRequest.ValidateAll() if
// the designated constraints aren't met.
type GetLoanOfferActivityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanOfferActivityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanOfferActivityRequestMultiError) AllErrors() []error { return m }

// GetLoanOfferActivityRequestValidationError is the validation error returned
// by GetLoanOfferActivityRequest.Validate if the designated constraints
// aren't met.
type GetLoanOfferActivityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanOfferActivityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanOfferActivityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanOfferActivityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanOfferActivityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanOfferActivityRequestValidationError) ErrorName() string {
	return "GetLoanOfferActivityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanOfferActivityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanOfferActivityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanOfferActivityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanOfferActivityRequestValidationError{}

// Validate checks the field values on GetLoanOfferActivityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanOfferActivityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanOfferActivityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanOfferActivityResponseMultiError, or nil if none found.
func (m *GetLoanOfferActivityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanOfferActivityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanOfferActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanOfferActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanOfferActivityResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanOffer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanOfferActivityResponseValidationError{
					field:  "LoanOffer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanOfferActivityResponseValidationError{
					field:  "LoanOffer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanOffer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanOfferActivityResponseValidationError{
				field:  "LoanOffer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanOfferActivityResponseMultiError(errors)
	}

	return nil
}

// GetLoanOfferActivityResponseMultiError is an error wrapping multiple
// validation errors returned by GetLoanOfferActivityResponse.ValidateAll() if
// the designated constraints aren't met.
type GetLoanOfferActivityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanOfferActivityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanOfferActivityResponseMultiError) AllErrors() []error { return m }

// GetLoanOfferActivityResponseValidationError is the validation error returned
// by GetLoanOfferActivityResponse.Validate if the designated constraints
// aren't met.
type GetLoanOfferActivityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanOfferActivityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanOfferActivityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanOfferActivityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanOfferActivityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanOfferActivityResponseValidationError) ErrorName() string {
	return "GetLoanOfferActivityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanOfferActivityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanOfferActivityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanOfferActivityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanOfferActivityResponseValidationError{}

// Validate checks the field values on PalActivityWithCallbackRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PalActivityWithCallbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PalActivityWithCallbackRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PalActivityWithCallbackRequestMultiError, or nil if none found.
func (m *PalActivityWithCallbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PalActivityWithCallbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PalActivityWithCallbackRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PalActivityWithCallbackRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PalActivityWithCallbackRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PalActivityWithCallbackRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PalActivityWithCallbackRequestValidationError{
					field:  "LoanStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PalActivityWithCallbackRequestValidationError{
				field:  "LoanStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	// no validation rules for LoanProgram

	// no validation rules for Data

	if len(errors) > 0 {
		return PalActivityWithCallbackRequestMultiError(errors)
	}

	return nil
}

// PalActivityWithCallbackRequestMultiError is an error wrapping multiple
// validation errors returned by PalActivityWithCallbackRequest.ValidateAll()
// if the designated constraints aren't met.
type PalActivityWithCallbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PalActivityWithCallbackRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PalActivityWithCallbackRequestMultiError) AllErrors() []error { return m }

// PalActivityWithCallbackRequestValidationError is the validation error
// returned by PalActivityWithCallbackRequest.Validate if the designated
// constraints aren't met.
type PalActivityWithCallbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PalActivityWithCallbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PalActivityWithCallbackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PalActivityWithCallbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PalActivityWithCallbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PalActivityWithCallbackRequestValidationError) ErrorName() string {
	return "PalActivityWithCallbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PalActivityWithCallbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPalActivityWithCallbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PalActivityWithCallbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PalActivityWithCallbackRequestValidationError{}

// Validate checks the field values on PalActivityGetFeatureVersionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PalActivityGetFeatureVersionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PalActivityGetFeatureVersionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PalActivityGetFeatureVersionRequestMultiError, or nil if none found.
func (m *PalActivityGetFeatureVersionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PalActivityGetFeatureVersionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PalActivityGetFeatureVersionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PalActivityGetFeatureVersionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PalActivityGetFeatureVersionRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FeatureType

	if len(errors) > 0 {
		return PalActivityGetFeatureVersionRequestMultiError(errors)
	}

	return nil
}

// PalActivityGetFeatureVersionRequestMultiError is an error wrapping multiple
// validation errors returned by
// PalActivityGetFeatureVersionRequest.ValidateAll() if the designated
// constraints aren't met.
type PalActivityGetFeatureVersionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PalActivityGetFeatureVersionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PalActivityGetFeatureVersionRequestMultiError) AllErrors() []error { return m }

// PalActivityGetFeatureVersionRequestValidationError is the validation error
// returned by PalActivityGetFeatureVersionRequest.Validate if the designated
// constraints aren't met.
type PalActivityGetFeatureVersionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PalActivityGetFeatureVersionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PalActivityGetFeatureVersionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PalActivityGetFeatureVersionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PalActivityGetFeatureVersionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PalActivityGetFeatureVersionRequestValidationError) ErrorName() string {
	return "PalActivityGetFeatureVersionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PalActivityGetFeatureVersionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPalActivityGetFeatureVersionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PalActivityGetFeatureVersionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PalActivityGetFeatureVersionRequestValidationError{}

// Validate checks the field values on PalActivityGetFeatureVersionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *PalActivityGetFeatureVersionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PalActivityGetFeatureVersionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PalActivityGetFeatureVersionResponseMultiError, or nil if none found.
func (m *PalActivityGetFeatureVersionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PalActivityGetFeatureVersionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PalActivityGetFeatureVersionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PalActivityGetFeatureVersionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PalActivityGetFeatureVersionResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FeatureVersion

	if len(errors) > 0 {
		return PalActivityGetFeatureVersionResponseMultiError(errors)
	}

	return nil
}

// PalActivityGetFeatureVersionResponseMultiError is an error wrapping multiple
// validation errors returned by
// PalActivityGetFeatureVersionResponse.ValidateAll() if the designated
// constraints aren't met.
type PalActivityGetFeatureVersionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PalActivityGetFeatureVersionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PalActivityGetFeatureVersionResponseMultiError) AllErrors() []error { return m }

// PalActivityGetFeatureVersionResponseValidationError is the validation error
// returned by PalActivityGetFeatureVersionResponse.Validate if the designated
// constraints aren't met.
type PalActivityGetFeatureVersionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PalActivityGetFeatureVersionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PalActivityGetFeatureVersionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PalActivityGetFeatureVersionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PalActivityGetFeatureVersionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PalActivityGetFeatureVersionResponseValidationError) ErrorName() string {
	return "PalActivityGetFeatureVersionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PalActivityGetFeatureVersionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPalActivityGetFeatureVersionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PalActivityGetFeatureVersionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PalActivityGetFeatureVersionResponseValidationError{}

// Validate checks the field values on IsBasicAddressCollectedRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsBasicAddressCollectedRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsBasicAddressCollectedRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// IsBasicAddressCollectedRequestMultiError, or nil if none found.
func (m *IsBasicAddressCollectedRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IsBasicAddressCollectedRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsBasicAddressCollectedRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsBasicAddressCollectedRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsBasicAddressCollectedRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if len(errors) > 0 {
		return IsBasicAddressCollectedRequestMultiError(errors)
	}

	return nil
}

// IsBasicAddressCollectedRequestMultiError is an error wrapping multiple
// validation errors returned by IsBasicAddressCollectedRequest.ValidateAll()
// if the designated constraints aren't met.
type IsBasicAddressCollectedRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsBasicAddressCollectedRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsBasicAddressCollectedRequestMultiError) AllErrors() []error { return m }

// IsBasicAddressCollectedRequestValidationError is the validation error
// returned by IsBasicAddressCollectedRequest.Validate if the designated
// constraints aren't met.
type IsBasicAddressCollectedRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsBasicAddressCollectedRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsBasicAddressCollectedRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsBasicAddressCollectedRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsBasicAddressCollectedRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsBasicAddressCollectedRequestValidationError) ErrorName() string {
	return "IsBasicAddressCollectedRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IsBasicAddressCollectedRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsBasicAddressCollectedRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsBasicAddressCollectedRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsBasicAddressCollectedRequestValidationError{}

// Validate checks the field values on IsBasicAddressCollectedResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsBasicAddressCollectedResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsBasicAddressCollectedResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// IsBasicAddressCollectedResponseMultiError, or nil if none found.
func (m *IsBasicAddressCollectedResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IsBasicAddressCollectedResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsBasicAddressCollectedResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsBasicAddressCollectedResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsBasicAddressCollectedResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsBasicAddressCollected

	if len(errors) > 0 {
		return IsBasicAddressCollectedResponseMultiError(errors)
	}

	return nil
}

// IsBasicAddressCollectedResponseMultiError is an error wrapping multiple
// validation errors returned by IsBasicAddressCollectedResponse.ValidateAll()
// if the designated constraints aren't met.
type IsBasicAddressCollectedResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsBasicAddressCollectedResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsBasicAddressCollectedResponseMultiError) AllErrors() []error { return m }

// IsBasicAddressCollectedResponseValidationError is the validation error
// returned by IsBasicAddressCollectedResponse.Validate if the designated
// constraints aren't met.
type IsBasicAddressCollectedResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsBasicAddressCollectedResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsBasicAddressCollectedResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsBasicAddressCollectedResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsBasicAddressCollectedResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsBasicAddressCollectedResponseValidationError) ErrorName() string {
	return "IsBasicAddressCollectedResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IsBasicAddressCollectedResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsBasicAddressCollectedResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsBasicAddressCollectedResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsBasicAddressCollectedResponseValidationError{}

// Validate checks the field values on IsFeatureEnabledRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsFeatureEnabledRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsFeatureEnabledRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsFeatureEnabledRequestMultiError, or nil if none found.
func (m *IsFeatureEnabledRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IsFeatureEnabledRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsFeatureEnabledRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsFeatureEnabledRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsFeatureEnabledRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FeatureType

	// no validation rules for ActorId

	if len(errors) > 0 {
		return IsFeatureEnabledRequestMultiError(errors)
	}

	return nil
}

// IsFeatureEnabledRequestMultiError is an error wrapping multiple validation
// errors returned by IsFeatureEnabledRequest.ValidateAll() if the designated
// constraints aren't met.
type IsFeatureEnabledRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsFeatureEnabledRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsFeatureEnabledRequestMultiError) AllErrors() []error { return m }

// IsFeatureEnabledRequestValidationError is the validation error returned by
// IsFeatureEnabledRequest.Validate if the designated constraints aren't met.
type IsFeatureEnabledRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsFeatureEnabledRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsFeatureEnabledRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsFeatureEnabledRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsFeatureEnabledRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsFeatureEnabledRequestValidationError) ErrorName() string {
	return "IsFeatureEnabledRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IsFeatureEnabledRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsFeatureEnabledRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsFeatureEnabledRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsFeatureEnabledRequestValidationError{}

// Validate checks the field values on IsFeatureEnabledResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsFeatureEnabledResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsFeatureEnabledResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsFeatureEnabledResponseMultiError, or nil if none found.
func (m *IsFeatureEnabledResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IsFeatureEnabledResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsFeatureEnabledResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsFeatureEnabledResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsFeatureEnabledResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsFeatureEnabled

	if len(errors) > 0 {
		return IsFeatureEnabledResponseMultiError(errors)
	}

	return nil
}

// IsFeatureEnabledResponseMultiError is an error wrapping multiple validation
// errors returned by IsFeatureEnabledResponse.ValidateAll() if the designated
// constraints aren't met.
type IsFeatureEnabledResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsFeatureEnabledResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsFeatureEnabledResponseMultiError) AllErrors() []error { return m }

// IsFeatureEnabledResponseValidationError is the validation error returned by
// IsFeatureEnabledResponse.Validate if the designated constraints aren't met.
type IsFeatureEnabledResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsFeatureEnabledResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsFeatureEnabledResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsFeatureEnabledResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsFeatureEnabledResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsFeatureEnabledResponseValidationError) ErrorName() string {
	return "IsFeatureEnabledResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IsFeatureEnabledResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsFeatureEnabledResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsFeatureEnabledResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsFeatureEnabledResponseValidationError{}

// Validate checks the field values on IsFiCoreUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsFiCoreUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsFiCoreUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsFiCoreUserRequestMultiError, or nil if none found.
func (m *IsFiCoreUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IsFiCoreUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsFiCoreUserRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsFiCoreUserRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsFiCoreUserRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if len(errors) > 0 {
		return IsFiCoreUserRequestMultiError(errors)
	}

	return nil
}

// IsFiCoreUserRequestMultiError is an error wrapping multiple validation
// errors returned by IsFiCoreUserRequest.ValidateAll() if the designated
// constraints aren't met.
type IsFiCoreUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsFiCoreUserRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsFiCoreUserRequestMultiError) AllErrors() []error { return m }

// IsFiCoreUserRequestValidationError is the validation error returned by
// IsFiCoreUserRequest.Validate if the designated constraints aren't met.
type IsFiCoreUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsFiCoreUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsFiCoreUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsFiCoreUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsFiCoreUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsFiCoreUserRequestValidationError) ErrorName() string {
	return "IsFiCoreUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IsFiCoreUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsFiCoreUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsFiCoreUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsFiCoreUserRequestValidationError{}

// Validate checks the field values on IsFiCoreUserResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsFiCoreUserResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsFiCoreUserResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsFiCoreUserResponseMultiError, or nil if none found.
func (m *IsFiCoreUserResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IsFiCoreUserResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsFiCoreUserResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsFiCoreUserResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsFiCoreUserResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsFiCoreUser

	if len(errors) > 0 {
		return IsFiCoreUserResponseMultiError(errors)
	}

	return nil
}

// IsFiCoreUserResponseMultiError is an error wrapping multiple validation
// errors returned by IsFiCoreUserResponse.ValidateAll() if the designated
// constraints aren't met.
type IsFiCoreUserResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsFiCoreUserResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsFiCoreUserResponseMultiError) AllErrors() []error { return m }

// IsFiCoreUserResponseValidationError is the validation error returned by
// IsFiCoreUserResponse.Validate if the designated constraints aren't met.
type IsFiCoreUserResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsFiCoreUserResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsFiCoreUserResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsFiCoreUserResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsFiCoreUserResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsFiCoreUserResponseValidationError) ErrorName() string {
	return "IsFiCoreUserResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IsFiCoreUserResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsFiCoreUserResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsFiCoreUserResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsFiCoreUserResponseValidationError{}

// Validate checks the field values on UpdateSyncStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateSyncStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSyncStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateSyncStatusRequestMultiError, or nil if none found.
func (m *UpdateSyncStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSyncStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSyncStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSyncStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSyncStatusRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LrId

	// no validation rules for Vendor

	// no validation rules for IsInSyncMode

	if len(errors) > 0 {
		return UpdateSyncStatusRequestMultiError(errors)
	}

	return nil
}

// UpdateSyncStatusRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateSyncStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateSyncStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSyncStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSyncStatusRequestMultiError) AllErrors() []error { return m }

// UpdateSyncStatusRequestValidationError is the validation error returned by
// UpdateSyncStatusRequest.Validate if the designated constraints aren't met.
type UpdateSyncStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSyncStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSyncStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSyncStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSyncStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSyncStatusRequestValidationError) ErrorName() string {
	return "UpdateSyncStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSyncStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSyncStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSyncStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSyncStatusRequestValidationError{}

// Validate checks the field values on UpdateSyncStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateSyncStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSyncStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateSyncStatusResponseMultiError, or nil if none found.
func (m *UpdateSyncStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSyncStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSyncStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSyncStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSyncStatusResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateSyncStatusResponseMultiError(errors)
	}

	return nil
}

// UpdateSyncStatusResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateSyncStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateSyncStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSyncStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSyncStatusResponseMultiError) AllErrors() []error { return m }

// UpdateSyncStatusResponseValidationError is the validation error returned by
// UpdateSyncStatusResponse.Validate if the designated constraints aren't met.
type UpdateSyncStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSyncStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSyncStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSyncStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSyncStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSyncStatusResponseValidationError) ErrorName() string {
	return "UpdateSyncStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSyncStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSyncStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSyncStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSyncStatusResponseValidationError{}

// Validate checks the field values on CheckHardOffersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckHardOffersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckHardOffersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckHardOffersRequestMultiError, or nil if none found.
func (m *CheckHardOffersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckHardOffersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckHardOffersRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckHardOffersRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckHardOffersRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetLoanHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckHardOffersRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckHardOffersRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckHardOffersRequestValidationError{
				field:  "LoanHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckHardOffersRequestMultiError(errors)
	}

	return nil
}

// CheckHardOffersRequestMultiError is an error wrapping multiple validation
// errors returned by CheckHardOffersRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckHardOffersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckHardOffersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckHardOffersRequestMultiError) AllErrors() []error { return m }

// CheckHardOffersRequestValidationError is the validation error returned by
// CheckHardOffersRequest.Validate if the designated constraints aren't met.
type CheckHardOffersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckHardOffersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckHardOffersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckHardOffersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckHardOffersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckHardOffersRequestValidationError) ErrorName() string {
	return "CheckHardOffersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckHardOffersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckHardOffersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckHardOffersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckHardOffersRequestValidationError{}

// Validate checks the field values on CheckHardOffersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckHardOffersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckHardOffersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckHardOffersResponseMultiError, or nil if none found.
func (m *CheckHardOffersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckHardOffersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckHardOffersResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckHardOffersResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckHardOffersResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for HardOfferExists

	if len(errors) > 0 {
		return CheckHardOffersResponseMultiError(errors)
	}

	return nil
}

// CheckHardOffersResponseMultiError is an error wrapping multiple validation
// errors returned by CheckHardOffersResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckHardOffersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckHardOffersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckHardOffersResponseMultiError) AllErrors() []error { return m }

// CheckHardOffersResponseValidationError is the validation error returned by
// CheckHardOffersResponse.Validate if the designated constraints aren't met.
type CheckHardOffersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckHardOffersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckHardOffersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckHardOffersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckHardOffersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckHardOffersResponseValidationError) ErrorName() string {
	return "CheckHardOffersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckHardOffersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckHardOffersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckHardOffersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckHardOffersResponseValidationError{}
