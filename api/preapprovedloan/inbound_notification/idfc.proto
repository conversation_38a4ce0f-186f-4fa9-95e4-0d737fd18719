syntax = "proto3";

package preapprovedloan.inbound_notification;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/preapprovedloan/inbound_notification";
option java_package = "com.github.epifi.gamma.api.preapprovedloan.inbound_notification";

enum VkycReviewerAction {
  VKYC_REVIEWER_ACTION_UNSPECIFIED = 0;
  VKYC_REVIEWER_ACTION_APPROVED = 1;
  VKYC_REVIEWER_ACTION_REJECTED = 2;
}

enum VkycStatus {
  VKYC_STATUS_UNSPECIFIED = 0;
  // upon creation of capture link
  VKYC_STATUS_CAPTURE_PENDING = 1;
  // just after the vkyc agent makes a decision.
  VKYC_STATUS_IN_PROGRESS = 2;
  // after the vkyc video's composition completes.
  VKYC_STATUS_PROCESSED = 3;
  // when the Profile data is ready for auditor to review.
  VKYC_STATUS_REVIEW_REQUIRED = 4;
  // when the Profile review is completed by the auditor.
  VKYC_STATUS_COMPLETED = 5;
  // when the Profile is asked for a Recapture by the auditor.
  VKYC_STATUS_RECAPTURE_PENDING = 6;
  // when the Profile is unable to verify.
  VKYC_STATUS_UNABLE_TO_VERIFY = 7;
}

message VkycProfileStatusUpdatePayload {
  string req_id = 1;
  string reference_id = 2;
  VkycReviewerAction reviewer_action = 3;
  VkycStatus status = 4;
  // e.g: "Signatures do not match", "Face does not match the document"
  string status_detail = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp completed_at = 7;
}
