// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/investment/indianstocks/frontend/account_summary.proto

package frontend

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AccountSummary with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AccountSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountSummary with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AccountSummaryMultiError,
// or nil if none found.
func (m *AccountSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	// no validation rules for DematId

	if all {
		switch v := interface{}(m.GetCurrentValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountSummaryValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountSummaryValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountSummaryValidationError{
				field:  "CurrentValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountSummaryValidationError{
					field:  "AccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountSummaryValidationError{
					field:  "AccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountSummaryValidationError{
				field:  "AccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetHoldingDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AccountSummaryValidationError{
						field:  fmt.Sprintf("HoldingDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AccountSummaryValidationError{
						field:  fmt.Sprintf("HoldingDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AccountSummaryValidationError{
					field:  fmt.Sprintf("HoldingDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AccountSummaryMultiError(errors)
	}

	return nil
}

// AccountSummaryMultiError is an error wrapping multiple validation errors
// returned by AccountSummary.ValidateAll() if the designated constraints
// aren't met.
type AccountSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountSummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountSummaryMultiError) AllErrors() []error { return m }

// AccountSummaryValidationError is the validation error returned by
// AccountSummary.Validate if the designated constraints aren't met.
type AccountSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountSummaryValidationError) ErrorName() string { return "AccountSummaryValidationError" }

// Error satisfies the builtin error interface
func (e AccountSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountSummaryValidationError{}
