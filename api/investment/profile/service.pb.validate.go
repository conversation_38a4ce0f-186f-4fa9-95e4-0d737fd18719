// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/investment/profile/service.proto

package profile

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = typesv2.MultiChoiceSelectType(0)
)

// Validate checks the field values on GetInvestmentRiskSurveyStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetInvestmentRiskSurveyStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentRiskSurveyStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetInvestmentRiskSurveyStatusRequestMultiError, or nil if none found.
func (m *GetInvestmentRiskSurveyStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentRiskSurveyStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetInvestmentRiskSurveyStatusRequestMultiError(errors)
	}

	return nil
}

// GetInvestmentRiskSurveyStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetInvestmentRiskSurveyStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentRiskSurveyStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentRiskSurveyStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentRiskSurveyStatusRequestMultiError) AllErrors() []error { return m }

// GetInvestmentRiskSurveyStatusRequestValidationError is the validation error
// returned by GetInvestmentRiskSurveyStatusRequest.Validate if the designated
// constraints aren't met.
type GetInvestmentRiskSurveyStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentRiskSurveyStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentRiskSurveyStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentRiskSurveyStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentRiskSurveyStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentRiskSurveyStatusRequestValidationError) ErrorName() string {
	return "GetInvestmentRiskSurveyStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentRiskSurveyStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentRiskSurveyStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentRiskSurveyStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentRiskSurveyStatusRequestValidationError{}

// Validate checks the field values on GetInvestmentRiskSurveyStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetInvestmentRiskSurveyStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentRiskSurveyStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetInvestmentRiskSurveyStatusResponseMultiError, or nil if none found.
func (m *GetInvestmentRiskSurveyStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentRiskSurveyStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentRiskSurveyStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentRiskSurveyStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentRiskSurveyStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SurveyStatus

	if len(errors) > 0 {
		return GetInvestmentRiskSurveyStatusResponseMultiError(errors)
	}

	return nil
}

// GetInvestmentRiskSurveyStatusResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetInvestmentRiskSurveyStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentRiskSurveyStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentRiskSurveyStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentRiskSurveyStatusResponseMultiError) AllErrors() []error { return m }

// GetInvestmentRiskSurveyStatusResponseValidationError is the validation error
// returned by GetInvestmentRiskSurveyStatusResponse.Validate if the
// designated constraints aren't met.
type GetInvestmentRiskSurveyStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentRiskSurveyStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentRiskSurveyStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentRiskSurveyStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentRiskSurveyStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentRiskSurveyStatusResponseValidationError) ErrorName() string {
	return "GetInvestmentRiskSurveyStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentRiskSurveyStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentRiskSurveyStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentRiskSurveyStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentRiskSurveyStatusResponseValidationError{}

// Validate checks the field values on GetInvestmentRiskQuestionnaireRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetInvestmentRiskQuestionnaireRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentRiskQuestionnaireRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetInvestmentRiskQuestionnaireRequestMultiError, or nil if none found.
func (m *GetInvestmentRiskQuestionnaireRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentRiskQuestionnaireRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetInvestmentRiskQuestionnaireRequestMultiError(errors)
	}

	return nil
}

// GetInvestmentRiskQuestionnaireRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetInvestmentRiskQuestionnaireRequest.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentRiskQuestionnaireRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentRiskQuestionnaireRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentRiskQuestionnaireRequestMultiError) AllErrors() []error { return m }

// GetInvestmentRiskQuestionnaireRequestValidationError is the validation error
// returned by GetInvestmentRiskQuestionnaireRequest.Validate if the
// designated constraints aren't met.
type GetInvestmentRiskQuestionnaireRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentRiskQuestionnaireRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentRiskQuestionnaireRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentRiskQuestionnaireRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentRiskQuestionnaireRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentRiskQuestionnaireRequestValidationError) ErrorName() string {
	return "GetInvestmentRiskQuestionnaireRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentRiskQuestionnaireRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentRiskQuestionnaireRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentRiskQuestionnaireRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentRiskQuestionnaireRequestValidationError{}

// Validate checks the field values on GetInvestmentRiskQuestionnaireResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetInvestmentRiskQuestionnaireResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetInvestmentRiskQuestionnaireResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetInvestmentRiskQuestionnaireResponseMultiError, or nil if none found.
func (m *GetInvestmentRiskQuestionnaireResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentRiskQuestionnaireResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentRiskQuestionnaireResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentRiskQuestionnaireResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentRiskQuestionnaireResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetQuestionsAndAnswerChoices() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetInvestmentRiskQuestionnaireResponseValidationError{
						field:  fmt.Sprintf("QuestionsAndAnswerChoices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetInvestmentRiskQuestionnaireResponseValidationError{
						field:  fmt.Sprintf("QuestionsAndAnswerChoices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetInvestmentRiskQuestionnaireResponseValidationError{
					field:  fmt.Sprintf("QuestionsAndAnswerChoices[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetInvestmentRiskQuestionnaireResponseMultiError(errors)
	}

	return nil
}

// GetInvestmentRiskQuestionnaireResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetInvestmentRiskQuestionnaireResponse.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentRiskQuestionnaireResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentRiskQuestionnaireResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentRiskQuestionnaireResponseMultiError) AllErrors() []error { return m }

// GetInvestmentRiskQuestionnaireResponseValidationError is the validation
// error returned by GetInvestmentRiskQuestionnaireResponse.Validate if the
// designated constraints aren't met.
type GetInvestmentRiskQuestionnaireResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentRiskQuestionnaireResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentRiskQuestionnaireResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentRiskQuestionnaireResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentRiskQuestionnaireResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentRiskQuestionnaireResponseValidationError) ErrorName() string {
	return "GetInvestmentRiskQuestionnaireResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentRiskQuestionnaireResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentRiskQuestionnaireResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentRiskQuestionnaireResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentRiskQuestionnaireResponseValidationError{}

// Validate checks the field values on QuestionAndAnswerChoices with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuestionAndAnswerChoices) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionAndAnswerChoices with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionAndAnswerChoicesMultiError, or nil if none found.
func (m *QuestionAndAnswerChoices) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionAndAnswerChoices) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for QuestionType

	// no validation rules for MultiChoiceSelectType

	if len(errors) > 0 {
		return QuestionAndAnswerChoicesMultiError(errors)
	}

	return nil
}

// QuestionAndAnswerChoicesMultiError is an error wrapping multiple validation
// errors returned by QuestionAndAnswerChoices.ValidateAll() if the designated
// constraints aren't met.
type QuestionAndAnswerChoicesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionAndAnswerChoicesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionAndAnswerChoicesMultiError) AllErrors() []error { return m }

// QuestionAndAnswerChoicesValidationError is the validation error returned by
// QuestionAndAnswerChoices.Validate if the designated constraints aren't met.
type QuestionAndAnswerChoicesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionAndAnswerChoicesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionAndAnswerChoicesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionAndAnswerChoicesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionAndAnswerChoicesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionAndAnswerChoicesValidationError) ErrorName() string {
	return "QuestionAndAnswerChoicesValidationError"
}

// Error satisfies the builtin error interface
func (e QuestionAndAnswerChoicesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionAndAnswerChoices.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionAndAnswerChoicesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionAndAnswerChoicesValidationError{}

// Validate checks the field values on CollectRiskQuestionnaireAnswersRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CollectRiskQuestionnaireAnswersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CollectRiskQuestionnaireAnswersRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// CollectRiskQuestionnaireAnswersRequestMultiError, or nil if none found.
func (m *CollectRiskQuestionnaireAnswersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectRiskQuestionnaireAnswersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	for idx, item := range m.GetQuestionsAndSelectedAnswers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CollectRiskQuestionnaireAnswersRequestValidationError{
						field:  fmt.Sprintf("QuestionsAndSelectedAnswers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CollectRiskQuestionnaireAnswersRequestValidationError{
						field:  fmt.Sprintf("QuestionsAndSelectedAnswers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CollectRiskQuestionnaireAnswersRequestValidationError{
					field:  fmt.Sprintf("QuestionsAndSelectedAnswers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CollectRiskQuestionnaireAnswersRequestMultiError(errors)
	}

	return nil
}

// CollectRiskQuestionnaireAnswersRequestMultiError is an error wrapping
// multiple validation errors returned by
// CollectRiskQuestionnaireAnswersRequest.ValidateAll() if the designated
// constraints aren't met.
type CollectRiskQuestionnaireAnswersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectRiskQuestionnaireAnswersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectRiskQuestionnaireAnswersRequestMultiError) AllErrors() []error { return m }

// CollectRiskQuestionnaireAnswersRequestValidationError is the validation
// error returned by CollectRiskQuestionnaireAnswersRequest.Validate if the
// designated constraints aren't met.
type CollectRiskQuestionnaireAnswersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectRiskQuestionnaireAnswersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectRiskQuestionnaireAnswersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectRiskQuestionnaireAnswersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectRiskQuestionnaireAnswersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectRiskQuestionnaireAnswersRequestValidationError) ErrorName() string {
	return "CollectRiskQuestionnaireAnswersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CollectRiskQuestionnaireAnswersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectRiskQuestionnaireAnswersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectRiskQuestionnaireAnswersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectRiskQuestionnaireAnswersRequestValidationError{}

// Validate checks the field values on QuestionAndSelectedAnswers with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuestionAndSelectedAnswers) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionAndSelectedAnswers with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionAndSelectedAnswersMultiError, or nil if none found.
func (m *QuestionAndSelectedAnswers) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionAndSelectedAnswers) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for QuestionType

	if len(errors) > 0 {
		return QuestionAndSelectedAnswersMultiError(errors)
	}

	return nil
}

// QuestionAndSelectedAnswersMultiError is an error wrapping multiple
// validation errors returned by QuestionAndSelectedAnswers.ValidateAll() if
// the designated constraints aren't met.
type QuestionAndSelectedAnswersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionAndSelectedAnswersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionAndSelectedAnswersMultiError) AllErrors() []error { return m }

// QuestionAndSelectedAnswersValidationError is the validation error returned
// by QuestionAndSelectedAnswers.Validate if the designated constraints aren't met.
type QuestionAndSelectedAnswersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionAndSelectedAnswersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionAndSelectedAnswersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionAndSelectedAnswersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionAndSelectedAnswersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionAndSelectedAnswersValidationError) ErrorName() string {
	return "QuestionAndSelectedAnswersValidationError"
}

// Error satisfies the builtin error interface
func (e QuestionAndSelectedAnswersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionAndSelectedAnswers.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionAndSelectedAnswersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionAndSelectedAnswersValidationError{}

// Validate checks the field values on CollectRiskQuestionnaireAnswersResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CollectRiskQuestionnaireAnswersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CollectRiskQuestionnaireAnswersResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// CollectRiskQuestionnaireAnswersResponseMultiError, or nil if none found.
func (m *CollectRiskQuestionnaireAnswersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectRiskQuestionnaireAnswersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectRiskQuestionnaireAnswersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectRiskQuestionnaireAnswersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectRiskQuestionnaireAnswersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RiskLevel

	// no validation rules for ProfileId

	if len(errors) > 0 {
		return CollectRiskQuestionnaireAnswersResponseMultiError(errors)
	}

	return nil
}

// CollectRiskQuestionnaireAnswersResponseMultiError is an error wrapping
// multiple validation errors returned by
// CollectRiskQuestionnaireAnswersResponse.ValidateAll() if the designated
// constraints aren't met.
type CollectRiskQuestionnaireAnswersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectRiskQuestionnaireAnswersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectRiskQuestionnaireAnswersResponseMultiError) AllErrors() []error { return m }

// CollectRiskQuestionnaireAnswersResponseValidationError is the validation
// error returned by CollectRiskQuestionnaireAnswersResponse.Validate if the
// designated constraints aren't met.
type CollectRiskQuestionnaireAnswersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectRiskQuestionnaireAnswersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectRiskQuestionnaireAnswersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectRiskQuestionnaireAnswersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectRiskQuestionnaireAnswersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectRiskQuestionnaireAnswersResponseValidationError) ErrorName() string {
	return "CollectRiskQuestionnaireAnswersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CollectRiskQuestionnaireAnswersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectRiskQuestionnaireAnswersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectRiskQuestionnaireAnswersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectRiskQuestionnaireAnswersResponseValidationError{}

// Validate checks the field values on GetRiskDashboardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRiskDashboardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRiskDashboardRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRiskDashboardRequestMultiError, or nil if none found.
func (m *GetRiskDashboardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRiskDashboardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetRiskDashboardRequestMultiError(errors)
	}

	return nil
}

// GetRiskDashboardRequestMultiError is an error wrapping multiple validation
// errors returned by GetRiskDashboardRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRiskDashboardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRiskDashboardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRiskDashboardRequestMultiError) AllErrors() []error { return m }

// GetRiskDashboardRequestValidationError is the validation error returned by
// GetRiskDashboardRequest.Validate if the designated constraints aren't met.
type GetRiskDashboardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRiskDashboardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRiskDashboardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRiskDashboardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRiskDashboardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRiskDashboardRequestValidationError) ErrorName() string {
	return "GetRiskDashboardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRiskDashboardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRiskDashboardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRiskDashboardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRiskDashboardRequestValidationError{}

// Validate checks the field values on GetRiskDashboardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRiskDashboardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRiskDashboardResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRiskDashboardResponseMultiError, or nil if none found.
func (m *GetRiskDashboardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRiskDashboardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRiskDashboardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRiskDashboardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRiskDashboardResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetInvestmentTermOptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRiskDashboardResponseValidationError{
						field:  fmt.Sprintf("InvestmentTermOptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRiskDashboardResponseValidationError{
						field:  fmt.Sprintf("InvestmentTermOptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRiskDashboardResponseValidationError{
					field:  fmt.Sprintf("InvestmentTermOptions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRiskDashboardResponseMultiError(errors)
	}

	return nil
}

// GetRiskDashboardResponseMultiError is an error wrapping multiple validation
// errors returned by GetRiskDashboardResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRiskDashboardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRiskDashboardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRiskDashboardResponseMultiError) AllErrors() []error { return m }

// GetRiskDashboardResponseValidationError is the validation error returned by
// GetRiskDashboardResponse.Validate if the designated constraints aren't met.
type GetRiskDashboardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRiskDashboardResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRiskDashboardResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRiskDashboardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRiskDashboardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRiskDashboardResponseValidationError) ErrorName() string {
	return "GetRiskDashboardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRiskDashboardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRiskDashboardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRiskDashboardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRiskDashboardResponseValidationError{}

// Validate checks the field values on InvestmentTermOption with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InvestmentTermOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestmentTermOption with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InvestmentTermOptionMultiError, or nil if none found.
func (m *InvestmentTermOption) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestmentTermOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Term

	for idx, item := range m.GetInstrumentDistributions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentTermOptionValidationError{
						field:  fmt.Sprintf("InstrumentDistributions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentTermOptionValidationError{
						field:  fmt.Sprintf("InstrumentDistributions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentTermOptionValidationError{
					field:  fmt.Sprintf("InstrumentDistributions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return InvestmentTermOptionMultiError(errors)
	}

	return nil
}

// InvestmentTermOptionMultiError is an error wrapping multiple validation
// errors returned by InvestmentTermOption.ValidateAll() if the designated
// constraints aren't met.
type InvestmentTermOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestmentTermOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestmentTermOptionMultiError) AllErrors() []error { return m }

// InvestmentTermOptionValidationError is the validation error returned by
// InvestmentTermOption.Validate if the designated constraints aren't met.
type InvestmentTermOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestmentTermOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestmentTermOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestmentTermOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestmentTermOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestmentTermOptionValidationError) ErrorName() string {
	return "InvestmentTermOptionValidationError"
}

// Error satisfies the builtin error interface
func (e InvestmentTermOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestmentTermOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestmentTermOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestmentTermOptionValidationError{}

// Validate checks the field values on InstrumentDistribution with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InstrumentDistribution) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InstrumentDistribution with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InstrumentDistributionMultiError, or nil if none found.
func (m *InstrumentDistribution) ValidateAll() error {
	return m.validate(true)
}

func (m *InstrumentDistribution) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InvestmentInstrumentType

	// no validation rules for PercentageShare

	switch v := m.InstrumentLevelDistribution.(type) {
	case *InstrumentDistribution_MfDistribution:
		if v == nil {
			err := InstrumentDistributionValidationError{
				field:  "InstrumentLevelDistribution",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMfDistribution()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InstrumentDistributionValidationError{
						field:  "MfDistribution",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InstrumentDistributionValidationError{
						field:  "MfDistribution",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMfDistribution()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InstrumentDistributionValidationError{
					field:  "MfDistribution",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return InstrumentDistributionMultiError(errors)
	}

	return nil
}

// InstrumentDistributionMultiError is an error wrapping multiple validation
// errors returned by InstrumentDistribution.ValidateAll() if the designated
// constraints aren't met.
type InstrumentDistributionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InstrumentDistributionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InstrumentDistributionMultiError) AllErrors() []error { return m }

// InstrumentDistributionValidationError is the validation error returned by
// InstrumentDistribution.Validate if the designated constraints aren't met.
type InstrumentDistributionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InstrumentDistributionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InstrumentDistributionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InstrumentDistributionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InstrumentDistributionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InstrumentDistributionValidationError) ErrorName() string {
	return "InstrumentDistributionValidationError"
}

// Error satisfies the builtin error interface
func (e InstrumentDistributionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInstrumentDistribution.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InstrumentDistributionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InstrumentDistributionValidationError{}

// Validate checks the field values on MfDistribution with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MfDistribution) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MfDistribution with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MfDistributionMultiError,
// or nil if none found.
func (m *MfDistribution) ValidateAll() error {
	return m.validate(true)
}

func (m *MfDistribution) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetMfSubCategoryDistributions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MfDistributionValidationError{
						field:  fmt.Sprintf("MfSubCategoryDistributions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MfDistributionValidationError{
						field:  fmt.Sprintf("MfSubCategoryDistributions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MfDistributionValidationError{
					field:  fmt.Sprintf("MfSubCategoryDistributions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MfDistributionMultiError(errors)
	}

	return nil
}

// MfDistributionMultiError is an error wrapping multiple validation errors
// returned by MfDistribution.ValidateAll() if the designated constraints
// aren't met.
type MfDistributionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MfDistributionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MfDistributionMultiError) AllErrors() []error { return m }

// MfDistributionValidationError is the validation error returned by
// MfDistribution.Validate if the designated constraints aren't met.
type MfDistributionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MfDistributionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MfDistributionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MfDistributionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MfDistributionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MfDistributionValidationError) ErrorName() string { return "MfDistributionValidationError" }

// Error satisfies the builtin error interface
func (e MfDistributionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMfDistribution.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MfDistributionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MfDistributionValidationError{}

// Validate checks the field values on MfSubCategoryDistribution with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MfSubCategoryDistribution) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MfSubCategoryDistribution with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MfSubCategoryDistributionMultiError, or nil if none found.
func (m *MfSubCategoryDistribution) ValidateAll() error {
	return m.validate(true)
}

func (m *MfSubCategoryDistribution) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PercentageShare

	// no validation rules for DisplayLabel

	if len(errors) > 0 {
		return MfSubCategoryDistributionMultiError(errors)
	}

	return nil
}

// MfSubCategoryDistributionMultiError is an error wrapping multiple validation
// errors returned by MfSubCategoryDistribution.ValidateAll() if the
// designated constraints aren't met.
type MfSubCategoryDistributionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MfSubCategoryDistributionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MfSubCategoryDistributionMultiError) AllErrors() []error { return m }

// MfSubCategoryDistributionValidationError is the validation error returned by
// MfSubCategoryDistribution.Validate if the designated constraints aren't met.
type MfSubCategoryDistributionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MfSubCategoryDistributionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MfSubCategoryDistributionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MfSubCategoryDistributionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MfSubCategoryDistributionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MfSubCategoryDistributionValidationError) ErrorName() string {
	return "MfSubCategoryDistributionValidationError"
}

// Error satisfies the builtin error interface
func (e MfSubCategoryDistributionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMfSubCategoryDistribution.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MfSubCategoryDistributionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MfSubCategoryDistributionValidationError{}

// Validate checks the field values on GetRiskLevelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRiskLevelRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRiskLevelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRiskLevelRequestMultiError, or nil if none found.
func (m *GetRiskLevelRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRiskLevelRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetRiskLevelRequestMultiError(errors)
	}

	return nil
}

// GetRiskLevelRequestMultiError is an error wrapping multiple validation
// errors returned by GetRiskLevelRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRiskLevelRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRiskLevelRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRiskLevelRequestMultiError) AllErrors() []error { return m }

// GetRiskLevelRequestValidationError is the validation error returned by
// GetRiskLevelRequest.Validate if the designated constraints aren't met.
type GetRiskLevelRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRiskLevelRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRiskLevelRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRiskLevelRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRiskLevelRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRiskLevelRequestValidationError) ErrorName() string {
	return "GetRiskLevelRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRiskLevelRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRiskLevelRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRiskLevelRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRiskLevelRequestValidationError{}

// Validate checks the field values on GetRiskLevelResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRiskLevelResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRiskLevelResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRiskLevelResponseMultiError, or nil if none found.
func (m *GetRiskLevelResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRiskLevelResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRiskLevelResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRiskLevelResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRiskLevelResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RiskLevel

	if len(errors) > 0 {
		return GetRiskLevelResponseMultiError(errors)
	}

	return nil
}

// GetRiskLevelResponseMultiError is an error wrapping multiple validation
// errors returned by GetRiskLevelResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRiskLevelResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRiskLevelResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRiskLevelResponseMultiError) AllErrors() []error { return m }

// GetRiskLevelResponseValidationError is the validation error returned by
// GetRiskLevelResponse.Validate if the designated constraints aren't met.
type GetRiskLevelResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRiskLevelResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRiskLevelResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRiskLevelResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRiskLevelResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRiskLevelResponseValidationError) ErrorName() string {
	return "GetRiskLevelResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRiskLevelResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRiskLevelResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRiskLevelResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRiskLevelResponseValidationError{}

// Validate checks the field values on
// GetInvestmentRiskProfileForWealthOnboardingRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentRiskProfileForWealthOnboardingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetInvestmentRiskProfileForWealthOnboardingRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetInvestmentRiskProfileForWealthOnboardingRequestMultiError, or nil if
// none found.
func (m *GetInvestmentRiskProfileForWealthOnboardingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentRiskProfileForWealthOnboardingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetInvestmentRiskProfileForWealthOnboardingRequestMultiError(errors)
	}

	return nil
}

// GetInvestmentRiskProfileForWealthOnboardingRequestMultiError is an error
// wrapping multiple validation errors returned by
// GetInvestmentRiskProfileForWealthOnboardingRequest.ValidateAll() if the
// designated constraints aren't met.
type GetInvestmentRiskProfileForWealthOnboardingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentRiskProfileForWealthOnboardingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentRiskProfileForWealthOnboardingRequestMultiError) AllErrors() []error { return m }

// GetInvestmentRiskProfileForWealthOnboardingRequestValidationError is the
// validation error returned by
// GetInvestmentRiskProfileForWealthOnboardingRequest.Validate if the
// designated constraints aren't met.
type GetInvestmentRiskProfileForWealthOnboardingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentRiskProfileForWealthOnboardingRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetInvestmentRiskProfileForWealthOnboardingRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetInvestmentRiskProfileForWealthOnboardingRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetInvestmentRiskProfileForWealthOnboardingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentRiskProfileForWealthOnboardingRequestValidationError) ErrorName() string {
	return "GetInvestmentRiskProfileForWealthOnboardingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentRiskProfileForWealthOnboardingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentRiskProfileForWealthOnboardingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentRiskProfileForWealthOnboardingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentRiskProfileForWealthOnboardingRequestValidationError{}

// Validate checks the field values on
// GetInvestmentRiskProfileForWealthOnboardingResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentRiskProfileForWealthOnboardingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetInvestmentRiskProfileForWealthOnboardingResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetInvestmentRiskProfileForWealthOnboardingResponseMultiError, or nil if
// none found.
func (m *GetInvestmentRiskProfileForWealthOnboardingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentRiskProfileForWealthOnboardingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentRiskProfileForWealthOnboardingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentRiskProfileForWealthOnboardingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentRiskProfileForWealthOnboardingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RiskProfileStatus

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentRiskProfileForWealthOnboardingResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentRiskProfileForWealthOnboardingResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentRiskProfileForWealthOnboardingResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestmentRiskProfileForWealthOnboardingResponseMultiError(errors)
	}

	return nil
}

// GetInvestmentRiskProfileForWealthOnboardingResponseMultiError is an error
// wrapping multiple validation errors returned by
// GetInvestmentRiskProfileForWealthOnboardingResponse.ValidateAll() if the
// designated constraints aren't met.
type GetInvestmentRiskProfileForWealthOnboardingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentRiskProfileForWealthOnboardingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentRiskProfileForWealthOnboardingResponseMultiError) AllErrors() []error { return m }

// GetInvestmentRiskProfileForWealthOnboardingResponseValidationError is the
// validation error returned by
// GetInvestmentRiskProfileForWealthOnboardingResponse.Validate if the
// designated constraints aren't met.
type GetInvestmentRiskProfileForWealthOnboardingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentRiskProfileForWealthOnboardingResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetInvestmentRiskProfileForWealthOnboardingResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetInvestmentRiskProfileForWealthOnboardingResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetInvestmentRiskProfileForWealthOnboardingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentRiskProfileForWealthOnboardingResponseValidationError) ErrorName() string {
	return "GetInvestmentRiskProfileForWealthOnboardingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentRiskProfileForWealthOnboardingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentRiskProfileForWealthOnboardingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentRiskProfileForWealthOnboardingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentRiskProfileForWealthOnboardingResponseValidationError{}
