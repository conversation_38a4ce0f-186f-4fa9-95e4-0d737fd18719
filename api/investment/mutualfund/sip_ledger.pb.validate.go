// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/investment/mutualfund/sip_ledger.proto

package mutualfund

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SIPLedger with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SIPLedger) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SIPLedger with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SIPLedgerMultiError, or nil
// if none found.
func (m *SIPLedger) ValidateAll() error {
	return m.validate(true)
}

func (m *SIPLedger) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for MutualFundId

	// no validation rules for SipRegistrationNumber

	// no validation rules for FitSubscriptionId

	if all {
		switch v := interface{}(m.GetSipAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SIPLedgerValidationError{
					field:  "SipAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SIPLedgerValidationError{
					field:  "SipAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSipAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SIPLedgerValidationError{
				field:  "SipAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SipGranularity

	// no validation rules for SipStatus

	if all {
		switch v := interface{}(m.GetStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SIPLedgerValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SIPLedgerValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SIPLedgerValidationError{
				field:  "StartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SIPLedgerValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SIPLedgerValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SIPLedgerValidationError{
				field:  "EndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TotalInstallments

	// no validation rules for SuccessfulInstallments

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SIPLedgerValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SIPLedgerValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SIPLedgerValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SIPLedgerValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SIPLedgerValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SIPLedgerValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SIPLedgerValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SIPLedgerValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SIPLedgerValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SIPLedgerMultiError(errors)
	}

	return nil
}

// SIPLedgerMultiError is an error wrapping multiple validation errors returned
// by SIPLedger.ValidateAll() if the designated constraints aren't met.
type SIPLedgerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SIPLedgerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SIPLedgerMultiError) AllErrors() []error { return m }

// SIPLedgerValidationError is the validation error returned by
// SIPLedger.Validate if the designated constraints aren't met.
type SIPLedgerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SIPLedgerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SIPLedgerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SIPLedgerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SIPLedgerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SIPLedgerValidationError) ErrorName() string { return "SIPLedgerValidationError" }

// Error satisfies the builtin error interface
func (e SIPLedgerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSIPLedger.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SIPLedgerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SIPLedgerValidationError{}
