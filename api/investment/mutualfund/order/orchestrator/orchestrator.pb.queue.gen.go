// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/investment/mutualfund/order/orchestrator
package orchestrator

import (
	"github.com/epifi/be-common/pkg/queue"
)

const (
	ProcessOrdersMethod                     = "ProcessOrders"
	ProcessOrderFileGenerationSuccessMethod = "ProcessOrderFileGenerationSuccess"
	ProcessOrderFileGenerationMethod        = "ProcessOrderFileGeneration"
)

// RegisterProcessOrdersMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessOrdersMethodToSubscriber(subscriber queue.Subscriber, srv OrderOrchestratorConsumerServer) {
	subscriber.RegisterService(&OrderOrchestratorConsumer_ServiceDesc, srv, ProcessOrdersMethod)
}

// RegisterProcessOrderFileGenerationSuccessMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessOrderFileGenerationSuccessMethodToSubscriber(subscriber queue.Subscriber, srv OrderOrchestratorConsumerServer) {
	subscriber.RegisterService(&OrderOrchestratorConsumer_ServiceDesc, srv, ProcessOrderFileGenerationSuccessMethod)
}

// RegisterProcessOrderFileGenerationMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessOrderFileGenerationMethodToSubscriber(subscriber queue.Subscriber, srv OrderOrchestratorConsumerServer) {
	subscriber.RegisterService(&OrderOrchestratorConsumer_ServiceDesc, srv, ProcessOrderFileGenerationMethod)
}
