// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/investment/auth/service.proto

package auth

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Auth_CreateAuthAttempt_FullMethodName = "/api.investment.auth.Auth/CreateAuthAttempt"
	Auth_GetAuthAttempt_FullMethodName    = "/api.investment.auth.Auth/GetAuthAttempt"
	Auth_GetAuthAttempts_FullMethodName   = "/api.investment.auth.Auth/GetAuthAttempts"
	Auth_UpdateAuthAttempt_FullMethodName = "/api.investment.auth.Auth/UpdateAuthAttempt"
)

// AuthClient is the client API for Auth service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AuthClient interface {
	// CreateAuthAttempt rpc will create a new auth attempt entry in db
	CreateAuthAttempt(ctx context.Context, in *CreateAuthAttemptRequest, opts ...grpc.CallOption) (*CreateAuthAttemptResponse, error)
	// GetAuthAttempt rpc will return a auth_attempt entry from the db
	GetAuthAttempt(ctx context.Context, in *GetAuthAttemptRequest, opts ...grpc.CallOption) (*GetAuthAttemptResponse, error)
	// RPC returns a list of auth attempts for the given input identifiers.
	GetAuthAttempts(ctx context.Context, in *GetAuthAttemptsRequest, opts ...grpc.CallOption) (*GetAuthAttemptsResponse, error)
	// UpdateAuthAttempt rpc will update an existing auth attempt
	UpdateAuthAttempt(ctx context.Context, in *UpdateAuthAttemptRequest, opts ...grpc.CallOption) (*UpdateAuthAttemptResponse, error)
}

type authClient struct {
	cc grpc.ClientConnInterface
}

func NewAuthClient(cc grpc.ClientConnInterface) AuthClient {
	return &authClient{cc}
}

func (c *authClient) CreateAuthAttempt(ctx context.Context, in *CreateAuthAttemptRequest, opts ...grpc.CallOption) (*CreateAuthAttemptResponse, error) {
	out := new(CreateAuthAttemptResponse)
	err := c.cc.Invoke(ctx, Auth_CreateAuthAttempt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetAuthAttempt(ctx context.Context, in *GetAuthAttemptRequest, opts ...grpc.CallOption) (*GetAuthAttemptResponse, error) {
	out := new(GetAuthAttemptResponse)
	err := c.cc.Invoke(ctx, Auth_GetAuthAttempt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetAuthAttempts(ctx context.Context, in *GetAuthAttemptsRequest, opts ...grpc.CallOption) (*GetAuthAttemptsResponse, error) {
	out := new(GetAuthAttemptsResponse)
	err := c.cc.Invoke(ctx, Auth_GetAuthAttempts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) UpdateAuthAttempt(ctx context.Context, in *UpdateAuthAttemptRequest, opts ...grpc.CallOption) (*UpdateAuthAttemptResponse, error) {
	out := new(UpdateAuthAttemptResponse)
	err := c.cc.Invoke(ctx, Auth_UpdateAuthAttempt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AuthServer is the server API for Auth service.
// All implementations should embed UnimplementedAuthServer
// for forward compatibility
type AuthServer interface {
	// CreateAuthAttempt rpc will create a new auth attempt entry in db
	CreateAuthAttempt(context.Context, *CreateAuthAttemptRequest) (*CreateAuthAttemptResponse, error)
	// GetAuthAttempt rpc will return a auth_attempt entry from the db
	GetAuthAttempt(context.Context, *GetAuthAttemptRequest) (*GetAuthAttemptResponse, error)
	// RPC returns a list of auth attempts for the given input identifiers.
	GetAuthAttempts(context.Context, *GetAuthAttemptsRequest) (*GetAuthAttemptsResponse, error)
	// UpdateAuthAttempt rpc will update an existing auth attempt
	UpdateAuthAttempt(context.Context, *UpdateAuthAttemptRequest) (*UpdateAuthAttemptResponse, error)
}

// UnimplementedAuthServer should be embedded to have forward compatible implementations.
type UnimplementedAuthServer struct {
}

func (UnimplementedAuthServer) CreateAuthAttempt(context.Context, *CreateAuthAttemptRequest) (*CreateAuthAttemptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAuthAttempt not implemented")
}
func (UnimplementedAuthServer) GetAuthAttempt(context.Context, *GetAuthAttemptRequest) (*GetAuthAttemptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthAttempt not implemented")
}
func (UnimplementedAuthServer) GetAuthAttempts(context.Context, *GetAuthAttemptsRequest) (*GetAuthAttemptsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthAttempts not implemented")
}
func (UnimplementedAuthServer) UpdateAuthAttempt(context.Context, *UpdateAuthAttemptRequest) (*UpdateAuthAttemptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuthAttempt not implemented")
}

// UnsafeAuthServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AuthServer will
// result in compilation errors.
type UnsafeAuthServer interface {
	mustEmbedUnimplementedAuthServer()
}

func RegisterAuthServer(s grpc.ServiceRegistrar, srv AuthServer) {
	s.RegisterService(&Auth_ServiceDesc, srv)
}

func _Auth_CreateAuthAttempt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAuthAttemptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).CreateAuthAttempt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_CreateAuthAttempt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).CreateAuthAttempt(ctx, req.(*CreateAuthAttemptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetAuthAttempt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthAttemptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetAuthAttempt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetAuthAttempt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetAuthAttempt(ctx, req.(*GetAuthAttemptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetAuthAttempts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthAttemptsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetAuthAttempts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetAuthAttempts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetAuthAttempts(ctx, req.(*GetAuthAttemptsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_UpdateAuthAttempt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAuthAttemptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).UpdateAuthAttempt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_UpdateAuthAttempt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).UpdateAuthAttempt(ctx, req.(*UpdateAuthAttemptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Auth_ServiceDesc is the grpc.ServiceDesc for Auth service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Auth_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.investment.auth.Auth",
	HandlerType: (*AuthServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAuthAttempt",
			Handler:    _Auth_CreateAuthAttempt_Handler,
		},
		{
			MethodName: "GetAuthAttempt",
			Handler:    _Auth_GetAuthAttempt_Handler,
		},
		{
			MethodName: "GetAuthAttempts",
			Handler:    _Auth_GetAuthAttempts_Handler,
		},
		{
			MethodName: "UpdateAuthAttempt",
			Handler:    _Auth_UpdateAuthAttempt_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/investment/auth/service.proto",
}
