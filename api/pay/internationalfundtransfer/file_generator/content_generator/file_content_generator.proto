syntax = "proto3";

package api.pay.internationalfundtransfer.file_generator.content_generator;

import "api/celestial/workflow/client.proto";
import "api/pay/internationalfundtransfer/file_generator/file_generation_attempt.proto";
import "api/pay/internationalfundtransfer/service.proto";
import "api/vendorgateway/vendor.proto";

option go_package = "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator/content_generator";
option java_package = "com.github.epifi.gamma.api.pay.internationalfundtransfer.file_generator/content_generator";

message FileContentGenerationResponse {
  int32 total_records = 1;
  string file_content = 2;
  // entity IDs used to generate the file contents
  // Can be lesser than the number of entities provided in request if some entities were not used
  // and this is being used for v2 file generation file
  repeated string used_entity_ids = 3;
  pay.internationalfundtransfer.file_generator.FileProcessingInfo file_processing_info = 4;
  // this field will be passed back successful creation of file
  // and this is being used for v1 file generation file
  repeated celestial.workflow.ClientReqId used_ift_wf_client_req_ids = 5;
}

message FileContentGenerationRequest {
  vendorgateway.Vendor vendor = 1;
  pay.internationalfundtransfer.file_generator.FileType file_type = 2;
  string client_request_id = 3;
  repeated celestial.workflow.ClientReqId ift_client_request_ids = 4;

  // MT-199 message and attachment don't require parsing SWIFT transfer file anymore
  // The contents are now retrieved from relevant non-file based data sources
  // There are some files for which we might need data from some other file previously generated. The datasource path represent the s3 path from which we are fetching the previously generated file.
  // For example, MT199 file needs data from the previously generated Swift transfer file.
  string data_source_path = 5 [deprecated = true];

  // details required for batching orders to swift transfer file
  pay.internationalfundtransfer.SwiftTransferBatchDetails swift_file_batch_details = 6;

  // Entities used for generating file contents, e.g. IFT order IDs or client order IDs
  // These entity IDs identify the contents of a file uniquely from another file of the same type
  repeated string entity_ids = 7;
}
