syntax = "proto3";

package api.usstocks.catalog;

import "api/usstocks/catalog/etf.proto";
import "api/usstocks/catalog/stock.proto";
import "api/usstocks/catalog/stock_and_etf_common.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/catalog";
option java_package = "com.github.epifi.gamma.api.usstocks.catalog";

// A stock/ETF is a security that represents the ownership of a fraction of the issuing corporation.
// Units of these are called "shares" which entitles the owner to a proportion of the corporation's assets and profits equal to how much stock they own.
// Stocks and ETFs are bought and sold predominantly on stock exchanges and are the foundation of many individual investors' portfolios.
message Stock {
  // Primary identifier for a stock database model.
  string id = 1;

  // An arrangement of characters (often letters) representing a particular security listed on an exchange or otherwise traded publicly.
  // symbol for various listed entities, e.g. : 'AAPL', 'AMZN','VOO'
  // this may not be unique across exchanges
  string symbol = 2;

  // the stock exchange on which the symbol is listed, e.g: Nasdaq
  Exchange exchange = 3;

  // Represent whether data represent for Etf or stock
  StockType stock_type = 4;

  // vendor id for industry, like '31120030' for 'Consumer Electronics'
  string industry_id = 5;

  // vendor id for industry group, like '31120' for 'Hardware'
  string industry_group_id = 6;

  // vendor id for sector, like '311' for 'Technology'
  string sector_id = 7;

  // company related data for the symbol
  CompanyInfo company_info = 8;

  // estimates, predictions and recommendations on company
  EstimatesInfo estimates_info = 9;

  // financial data of the company for the previous financial quarters & years
  // Represent financial data of stock
  FinancialInfo financial_info = 10;

  // OHLC price, volume and other daily performance info for the Stock, to be updated daily
  DailyPerformance daily_performance = 11;

  // historical prices
  HistoricalPriceData historical_price_data = 12;

  // fi curated info for the symbol, e.g: highlights for the symbol
  FiContent fi_content = 13;

  // internal availability status of stock
  InternalStatus internal_status = 14;

  // min app versions, platform and user groups which can query the symbol data
  UserAndPlatformSupport user_and_platform_support = 15;

  // Standard timestamp fields
  google.protobuf.Timestamp created_at = 16;
  google.protobuf.Timestamp updated_at = 17;
  google.protobuf.Timestamp deleted_at = 18;

  // StockBasicDetails contains Company/ETF name, logo url and their investment strategies.
  StockBasicDetails stock_basic_details = 19;

  // ETFFinancialInfo contains expense ratio, market info, tracking info
  ETFFinancialInfo etf_financial_info = 20;

  // ETFPerformanceMetrics contains sharp ratio and batting average details
  ETFPerformanceMetrics etf_performance_metrics = 21;

  // Holdings contain details of holding percentage and name of different company eg. google, apple etc
  // Modifiable montlhy by AMC
  Holdings etf_holdings = 22;

  // unique identifier for the stock at broker
  string broker_stock_id = 23;

  // contains status, tradable, fractionable and other details from broker side
  BrokerStockDetails broker_stock_details = 24;
  //contains stock logo
  string logo_url = 25;
}

enum StockFieldMask {
  STOCK_FIELD_MASK_UNSPECIFIED = 0;
  STOCK_ID = 1;
  STOCK_SYMBOL = 2;
  STOCK_EXCHANGE = 3;
  STOCK_TYPE = 4;
  STOCK_INDUSTRY_ID = 5;
  STOCK_INDUSTRY_GROUP_ID = 6;
  STOCK_SECTOR_ID = 7;
  STOCK_COMPANY_INFO = 8;
  STOCK_ESTIMATES_INFO = 9;
  STOCK_FINANCIAL_INFO = 10;
  STOCK_DAILY_PERFORMANCE = 11;
  STOCK_HISTORICAL_PRICE_DATA = 12;
  STOCK_FI_CONTENT = 13;
  STOCK_INTERNAL_STATUS = 14;
  STOCK_USER_AND_PLATFORM_SUPPORT = 15;
  STOCK_CREATED_AT = 16;
  STOCK_UPDATED_AT = 17;
  STOCK_DELETED_AT = 18;
  STOCK_STOCK_BASIC_DETAILS = 19;
  STOCK_ETF_FINANCIAL_INFO = 20;
  STOCK_ETF_PERFORMANCE_METRICS = 21;
  STOCK_ETF_HOLDINGS = 22;
  STOCK_BROKER_STOCK_ID = 23;
  STOCK_BROKER_STOCK_DETAILS = 24;
  STOCK_LOGO_URL = 25;
}
