//go:generate gen_sql -types=UsStockDocumentType,SecurityType,SellType,IncomeSource,TimeRangeType,DocumentSource

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/usstocks/tax/enums.proto

package tax

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UsStockDocumentType int32

const (
	UsStockDocumentType_US_STOCK_DOCUMENT_TYPE_UNSPECIFIED  UsStockDocumentType = 0
	UsStockDocumentType_US_STOCK_DOCUMENT_TYPE_CAPITAL_GAIN UsStockDocumentType = 1
	UsStockDocumentType_US_STOCK_DOCUMENT_TYPE_SCHEDULE_FA  UsStockDocumentType = 2
	UsStockDocumentType_US_STOCK_DOCUMENT_TYPE_SCHEDULE_FSI UsStockDocumentType = 3
	UsStockDocumentType_US_STOCK_DOCUMENT_TYPE_SCHEDULE_TR  UsStockDocumentType = 4
	UsStockDocumentType_US_STOCK_DOCUMENT_TYPE_FORM_67      UsStockDocumentType = 5
	UsStockDocumentType_US_STOCK_DOCUMENT_TYPE_FORM_1042_S  UsStockDocumentType = 6
)

// Enum value maps for UsStockDocumentType.
var (
	UsStockDocumentType_name = map[int32]string{
		0: "US_STOCK_DOCUMENT_TYPE_UNSPECIFIED",
		1: "US_STOCK_DOCUMENT_TYPE_CAPITAL_GAIN",
		2: "US_STOCK_DOCUMENT_TYPE_SCHEDULE_FA",
		3: "US_STOCK_DOCUMENT_TYPE_SCHEDULE_FSI",
		4: "US_STOCK_DOCUMENT_TYPE_SCHEDULE_TR",
		5: "US_STOCK_DOCUMENT_TYPE_FORM_67",
		6: "US_STOCK_DOCUMENT_TYPE_FORM_1042_S",
	}
	UsStockDocumentType_value = map[string]int32{
		"US_STOCK_DOCUMENT_TYPE_UNSPECIFIED":  0,
		"US_STOCK_DOCUMENT_TYPE_CAPITAL_GAIN": 1,
		"US_STOCK_DOCUMENT_TYPE_SCHEDULE_FA":  2,
		"US_STOCK_DOCUMENT_TYPE_SCHEDULE_FSI": 3,
		"US_STOCK_DOCUMENT_TYPE_SCHEDULE_TR":  4,
		"US_STOCK_DOCUMENT_TYPE_FORM_67":      5,
		"US_STOCK_DOCUMENT_TYPE_FORM_1042_S":  6,
	}
)

func (x UsStockDocumentType) Enum() *UsStockDocumentType {
	p := new(UsStockDocumentType)
	*p = x
	return p
}

func (x UsStockDocumentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UsStockDocumentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_tax_enums_proto_enumTypes[0].Descriptor()
}

func (UsStockDocumentType) Type() protoreflect.EnumType {
	return &file_api_usstocks_tax_enums_proto_enumTypes[0]
}

func (x UsStockDocumentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UsStockDocumentType.Descriptor instead.
func (UsStockDocumentType) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_tax_enums_proto_rawDescGZIP(), []int{0}
}

type SecurityType int32

const (
	SecurityType_SECURITY_TYPE_UNSPECIFIED SecurityType = 0
	SecurityType_SECURITY_TYPE_EQUITY      SecurityType = 1
	SecurityType_SECURITY_TYPE_ETF         SecurityType = 2
	SecurityType_SECURITY_TYPE_RIGHTS      SecurityType = 3
)

// Enum value maps for SecurityType.
var (
	SecurityType_name = map[int32]string{
		0: "SECURITY_TYPE_UNSPECIFIED",
		1: "SECURITY_TYPE_EQUITY",
		2: "SECURITY_TYPE_ETF",
		3: "SECURITY_TYPE_RIGHTS",
	}
	SecurityType_value = map[string]int32{
		"SECURITY_TYPE_UNSPECIFIED": 0,
		"SECURITY_TYPE_EQUITY":      1,
		"SECURITY_TYPE_ETF":         2,
		"SECURITY_TYPE_RIGHTS":      3,
	}
)

func (x SecurityType) Enum() *SecurityType {
	p := new(SecurityType)
	*p = x
	return p
}

func (x SecurityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SecurityType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_tax_enums_proto_enumTypes[1].Descriptor()
}

func (SecurityType) Type() protoreflect.EnumType {
	return &file_api_usstocks_tax_enums_proto_enumTypes[1]
}

func (x SecurityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SecurityType.Descriptor instead.
func (SecurityType) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_tax_enums_proto_rawDescGZIP(), []int{1}
}

type SellType int32

const (
	SellType_SELL_TYPE_UNSPECIFIED SellType = 0
	SellType_SELL_TYPE_SHORT_TERM  SellType = 1
	SellType_SELL_TYPE_LONG_TERM   SellType = 2
)

// Enum value maps for SellType.
var (
	SellType_name = map[int32]string{
		0: "SELL_TYPE_UNSPECIFIED",
		1: "SELL_TYPE_SHORT_TERM",
		2: "SELL_TYPE_LONG_TERM",
	}
	SellType_value = map[string]int32{
		"SELL_TYPE_UNSPECIFIED": 0,
		"SELL_TYPE_SHORT_TERM":  1,
		"SELL_TYPE_LONG_TERM":   2,
	}
)

func (x SellType) Enum() *SellType {
	p := new(SellType)
	*p = x
	return p
}

func (x SellType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SellType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_tax_enums_proto_enumTypes[2].Descriptor()
}

func (SellType) Type() protoreflect.EnumType {
	return &file_api_usstocks_tax_enums_proto_enumTypes[2]
}

func (x SellType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SellType.Descriptor instead.
func (SellType) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_tax_enums_proto_rawDescGZIP(), []int{2}
}

type IncomeSource int32

const (
	IncomeSource_INCOME_SOURCE_UNSPECIFIED IncomeSource = 0
	IncomeSource_INCOME_SOURCE_DIVIDEND    IncomeSource = 1
	IncomeSource_INCOME_SOURCE_INTEREST    IncomeSource = 2
)

// Enum value maps for IncomeSource.
var (
	IncomeSource_name = map[int32]string{
		0: "INCOME_SOURCE_UNSPECIFIED",
		1: "INCOME_SOURCE_DIVIDEND",
		2: "INCOME_SOURCE_INTEREST",
	}
	IncomeSource_value = map[string]int32{
		"INCOME_SOURCE_UNSPECIFIED": 0,
		"INCOME_SOURCE_DIVIDEND":    1,
		"INCOME_SOURCE_INTEREST":    2,
	}
)

func (x IncomeSource) Enum() *IncomeSource {
	p := new(IncomeSource)
	*p = x
	return p
}

func (x IncomeSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IncomeSource) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_tax_enums_proto_enumTypes[3].Descriptor()
}

func (IncomeSource) Type() protoreflect.EnumType {
	return &file_api_usstocks_tax_enums_proto_enumTypes[3]
}

func (x IncomeSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IncomeSource.Descriptor instead.
func (IncomeSource) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_tax_enums_proto_rawDescGZIP(), []int{3}
}

type TimeRangeType int32

const (
	TimeRangeType_TIME_RANGE_TYPE_UNSPECIFIED TimeRangeType = 0
	// Apr 01 - March 31
	TimeRangeType_TIME_RANGE_TYPE_FINANCIAL_YEAR TimeRangeType = 1
	// jan 01 - dec 31
	TimeRangeType_TIME_RANGE_TYPE_CALENDER_YEAR TimeRangeType = 2
)

// Enum value maps for TimeRangeType.
var (
	TimeRangeType_name = map[int32]string{
		0: "TIME_RANGE_TYPE_UNSPECIFIED",
		1: "TIME_RANGE_TYPE_FINANCIAL_YEAR",
		2: "TIME_RANGE_TYPE_CALENDER_YEAR",
	}
	TimeRangeType_value = map[string]int32{
		"TIME_RANGE_TYPE_UNSPECIFIED":    0,
		"TIME_RANGE_TYPE_FINANCIAL_YEAR": 1,
		"TIME_RANGE_TYPE_CALENDER_YEAR":  2,
	}
)

func (x TimeRangeType) Enum() *TimeRangeType {
	p := new(TimeRangeType)
	*p = x
	return p
}

func (x TimeRangeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TimeRangeType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_tax_enums_proto_enumTypes[4].Descriptor()
}

func (TimeRangeType) Type() protoreflect.EnumType {
	return &file_api_usstocks_tax_enums_proto_enumTypes[4]
}

func (x TimeRangeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TimeRangeType.Descriptor instead.
func (TimeRangeType) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_tax_enums_proto_rawDescGZIP(), []int{4}
}

type DocumentSource int32

const (
	DocumentSource_DOCUMENT_SOURCE_UNSPECIFIED      DocumentSource = 0
	DocumentSource_DOCUMENT_SOURCE_SYSTEM_GENERATED DocumentSource = 1
	DocumentSource_DOCUMENT_SOURCE_MANUAL_UPLOAD    DocumentSource = 2
)

// Enum value maps for DocumentSource.
var (
	DocumentSource_name = map[int32]string{
		0: "DOCUMENT_SOURCE_UNSPECIFIED",
		1: "DOCUMENT_SOURCE_SYSTEM_GENERATED",
		2: "DOCUMENT_SOURCE_MANUAL_UPLOAD",
	}
	DocumentSource_value = map[string]int32{
		"DOCUMENT_SOURCE_UNSPECIFIED":      0,
		"DOCUMENT_SOURCE_SYSTEM_GENERATED": 1,
		"DOCUMENT_SOURCE_MANUAL_UPLOAD":    2,
	}
)

func (x DocumentSource) Enum() *DocumentSource {
	p := new(DocumentSource)
	*p = x
	return p
}

func (x DocumentSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DocumentSource) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_tax_enums_proto_enumTypes[5].Descriptor()
}

func (DocumentSource) Type() protoreflect.EnumType {
	return &file_api_usstocks_tax_enums_proto_enumTypes[5]
}

func (x DocumentSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DocumentSource.Descriptor instead.
func (DocumentSource) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_tax_enums_proto_rawDescGZIP(), []int{5}
}

var File_api_usstocks_tax_enums_proto protoreflect.FileDescriptor

var file_api_usstocks_tax_enums_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2f, 0x74,
	0x61, 0x78, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x74, 0x61, 0x78,
	0x2a, 0xab, 0x02, 0x0a, 0x13, 0x55, 0x73, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x55, 0x53, 0x5f, 0x53,
	0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x27, 0x0a, 0x23, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x44, 0x4f, 0x43,
	0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x50, 0x49, 0x54,
	0x41, 0x4c, 0x5f, 0x47, 0x41, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x55, 0x53, 0x5f,
	0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x46, 0x41, 0x10,
	0x02, 0x12, 0x27, 0x0a, 0x23, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x44, 0x4f,
	0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x43, 0x48, 0x45,
	0x44, 0x55, 0x4c, 0x45, 0x5f, 0x46, 0x53, 0x49, 0x10, 0x03, 0x12, 0x26, 0x0a, 0x22, 0x55, 0x53,
	0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x54, 0x52,
	0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x44,
	0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x4f, 0x52,
	0x4d, 0x5f, 0x36, 0x37, 0x10, 0x05, 0x12, 0x26, 0x0a, 0x22, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f,
	0x43, 0x4b, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x31, 0x30, 0x34, 0x32, 0x5f, 0x53, 0x10, 0x06, 0x2a, 0x78,
	0x0a, 0x0c, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d,
	0x0a, 0x19, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a,
	0x14, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45,
	0x51, 0x55, 0x49, 0x54, 0x59, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x45, 0x43, 0x55, 0x52,
	0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x54, 0x46, 0x10, 0x02, 0x12, 0x18,
	0x0a, 0x14, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x52, 0x49, 0x47, 0x48, 0x54, 0x53, 0x10, 0x03, 0x2a, 0x58, 0x0a, 0x08, 0x53, 0x65, 0x6c, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x18, 0x0a, 0x14, 0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x48, 0x4f,
	0x52, 0x54, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x45, 0x4c,
	0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x4f, 0x4e, 0x47, 0x5f, 0x54, 0x45, 0x52, 0x4d,
	0x10, 0x02, 0x2a, 0x65, 0x0a, 0x0c, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x53, 0x4f, 0x55,
	0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1a, 0x0a, 0x16, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x44, 0x49, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x1a, 0x0a,
	0x16, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x10, 0x02, 0x2a, 0x77, 0x0a, 0x0d, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x49,
	0x4d, 0x45, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x54,
	0x49, 0x4d, 0x45, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46,
	0x49, 0x4e, 0x41, 0x4e, 0x43, 0x49, 0x41, 0x4c, 0x5f, 0x59, 0x45, 0x41, 0x52, 0x10, 0x01, 0x12,
	0x21, 0x0a, 0x1d, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x43, 0x41, 0x4c, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x59, 0x45, 0x41, 0x52,
	0x10, 0x02, 0x2a, 0x7a, 0x0a, 0x0e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f,
	0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x44,
	0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x4d,
	0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x02, 0x42, 0x52,
	0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x74, 0x61, 0x78, 0x5a, 0x27, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2f, 0x74,
	0x61, 0x78, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_usstocks_tax_enums_proto_rawDescOnce sync.Once
	file_api_usstocks_tax_enums_proto_rawDescData = file_api_usstocks_tax_enums_proto_rawDesc
)

func file_api_usstocks_tax_enums_proto_rawDescGZIP() []byte {
	file_api_usstocks_tax_enums_proto_rawDescOnce.Do(func() {
		file_api_usstocks_tax_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_usstocks_tax_enums_proto_rawDescData)
	})
	return file_api_usstocks_tax_enums_proto_rawDescData
}

var file_api_usstocks_tax_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_usstocks_tax_enums_proto_goTypes = []interface{}{
	(UsStockDocumentType)(0), // 0: api.usstocks.tax.UsStockDocumentType
	(SecurityType)(0),        // 1: api.usstocks.tax.SecurityType
	(SellType)(0),            // 2: api.usstocks.tax.SellType
	(IncomeSource)(0),        // 3: api.usstocks.tax.IncomeSource
	(TimeRangeType)(0),       // 4: api.usstocks.tax.TimeRangeType
	(DocumentSource)(0),      // 5: api.usstocks.tax.DocumentSource
}
var file_api_usstocks_tax_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_usstocks_tax_enums_proto_init() }
func file_api_usstocks_tax_enums_proto_init() {
	if File_api_usstocks_tax_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_usstocks_tax_enums_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_usstocks_tax_enums_proto_goTypes,
		DependencyIndexes: file_api_usstocks_tax_enums_proto_depIdxs,
		EnumInfos:         file_api_usstocks_tax_enums_proto_enumTypes,
	}.Build()
	File_api_usstocks_tax_enums_proto = out.File
	file_api_usstocks_tax_enums_proto_rawDesc = nil
	file_api_usstocks_tax_enums_proto_goTypes = nil
	file_api_usstocks_tax_enums_proto_depIdxs = nil
}
