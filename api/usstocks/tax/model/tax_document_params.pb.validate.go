// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/usstocks/tax/model/tax_document_params.proto

package model

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	tax "github.com/epifi/gamma/api/usstocks/tax"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = tax.UsStockDocumentType(0)
)

// Validate checks the field values on TaxDocumentParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TaxDocumentParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaxDocumentParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TaxDocumentParamsMultiError, or nil if none found.
func (m *TaxDocumentParams) ValidateAll() error {
	return m.validate(true)
}

func (m *TaxDocumentParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if _, ok := _TaxDocumentParams_DocumentType_NotInLookup[m.GetDocumentType()]; ok {
		err := TaxDocumentParamsValidationError{
			field:  "DocumentType",
			reason: "value must not be in list [US_STOCK_DOCUMENT_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAccountId()) < 1 {
		err := TaxDocumentParamsValidationError{
			field:  "AccountId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetParams() == nil {
		err := TaxDocumentParamsValidationError{
			field:  "Params",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TaxDocumentParamsValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TaxDocumentParamsValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TaxDocumentParamsValidationError{
				field:  "Params",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TaxDocumentParamsValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TaxDocumentParamsValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TaxDocumentParamsValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TaxDocumentParamsValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TaxDocumentParamsValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TaxDocumentParamsValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TaxDocumentParamsValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TaxDocumentParamsValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TaxDocumentParamsValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TaxDocumentParamsMultiError(errors)
	}

	return nil
}

// TaxDocumentParamsMultiError is an error wrapping multiple validation errors
// returned by TaxDocumentParams.ValidateAll() if the designated constraints
// aren't met.
type TaxDocumentParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaxDocumentParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaxDocumentParamsMultiError) AllErrors() []error { return m }

// TaxDocumentParamsValidationError is the validation error returned by
// TaxDocumentParams.Validate if the designated constraints aren't met.
type TaxDocumentParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaxDocumentParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaxDocumentParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaxDocumentParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaxDocumentParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaxDocumentParamsValidationError) ErrorName() string {
	return "TaxDocumentParamsValidationError"
}

// Error satisfies the builtin error interface
func (e TaxDocumentParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaxDocumentParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaxDocumentParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaxDocumentParamsValidationError{}

var _TaxDocumentParams_DocumentType_NotInLookup = map[tax.UsStockDocumentType]struct{}{
	0: {},
}
