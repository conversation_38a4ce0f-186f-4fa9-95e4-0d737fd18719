// Code generated by MockGen. DO NOT EDIT.
// Source: api/varys/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	varys "github.com/epifi/gamma/api/varys"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockVarysClient is a mock of VarysClient interface.
type MockVarysClient struct {
	ctrl     *gomock.Controller
	recorder *MockVarysClientMockRecorder
}

// MockVarysClientMockRecorder is the mock recorder for MockVarysClient.
type MockVarysClientMockRecorder struct {
	mock *MockVarysClient
}

// NewMockVarysClient creates a new mock instance.
func NewMockVarysClient(ctrl *gomock.Controller) *MockVarysClient {
	mock := &MockVarysClient{ctrl: ctrl}
	mock.recorder = &MockVarysClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVarysClient) EXPECT() *MockVarysClientMockRecorder {
	return m.recorder
}

// AlertmanagerWebhook mocks base method.
func (m *MockVarysClient) AlertmanagerWebhook(ctx context.Context, in *varys.AlertmanagerWebhookRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AlertmanagerWebhook", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AlertmanagerWebhook indicates an expected call of AlertmanagerWebhook.
func (mr *MockVarysClientMockRecorder) AlertmanagerWebhook(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AlertmanagerWebhook", reflect.TypeOf((*MockVarysClient)(nil).AlertmanagerWebhook), varargs...)
}

// MockVarysServer is a mock of VarysServer interface.
type MockVarysServer struct {
	ctrl     *gomock.Controller
	recorder *MockVarysServerMockRecorder
}

// MockVarysServerMockRecorder is the mock recorder for MockVarysServer.
type MockVarysServerMockRecorder struct {
	mock *MockVarysServer
}

// NewMockVarysServer creates a new mock instance.
func NewMockVarysServer(ctrl *gomock.Controller) *MockVarysServer {
	mock := &MockVarysServer{ctrl: ctrl}
	mock.recorder = &MockVarysServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVarysServer) EXPECT() *MockVarysServerMockRecorder {
	return m.recorder
}

// AlertmanagerWebhook mocks base method.
func (m *MockVarysServer) AlertmanagerWebhook(arg0 context.Context, arg1 *varys.AlertmanagerWebhookRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AlertmanagerWebhook", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AlertmanagerWebhook indicates an expected call of AlertmanagerWebhook.
func (mr *MockVarysServerMockRecorder) AlertmanagerWebhook(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AlertmanagerWebhook", reflect.TypeOf((*MockVarysServer)(nil).AlertmanagerWebhook), arg0, arg1)
}

// MockUnsafeVarysServer is a mock of UnsafeVarysServer interface.
type MockUnsafeVarysServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeVarysServerMockRecorder
}

// MockUnsafeVarysServerMockRecorder is the mock recorder for MockUnsafeVarysServer.
type MockUnsafeVarysServerMockRecorder struct {
	mock *MockUnsafeVarysServer
}

// NewMockUnsafeVarysServer creates a new mock instance.
func NewMockUnsafeVarysServer(ctrl *gomock.Controller) *MockUnsafeVarysServer {
	mock := &MockUnsafeVarysServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeVarysServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeVarysServer) EXPECT() *MockUnsafeVarysServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedVarysServer mocks base method.
func (m *MockUnsafeVarysServer) mustEmbedUnimplementedVarysServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedVarysServer")
}

// mustEmbedUnimplementedVarysServer indicates an expected call of mustEmbedUnimplementedVarysServer.
func (mr *MockUnsafeVarysServerMockRecorder) mustEmbedUnimplementedVarysServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedVarysServer", reflect.TypeOf((*MockUnsafeVarysServer)(nil).mustEmbedUnimplementedVarysServer))
}
