package review

import (
	"testing"

	"google.golang.org/protobuf/runtime/protoimpl"

	"github.com/epifi/gamma/api/risk/case_management/enums"
)

func TestCaseReviewLevel_IsWontReviewCase(t *testing.T) {
	type fields struct {
		state           protoimpl.MessageState
		sizeCache       protoimpl.SizeCache
		unknownFields   protoimpl.UnknownFields
		IsActive        bool
		CaseReviewLevel enums.CaseReviewLevel
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "fails with wont review case",
			fields: fields{
				IsActive:        true,
				CaseReviewLevel: enums.CaseReviewLevel_CASE_ACTION_LEVEL_CREATE_WONT_REVIEW_CASE,
			},
			want: true,
		},
		{
			name: "success",
			fields: fields{
				IsActive: false,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CaseReviewLevel{
				IsActive:        tt.fields.IsActive,
				CaseReviewLevel: tt.fields.CaseReviewLevel,
			}
			if got := c.IsWontReviewCase(); got != tt.want {
				t.Errorf("IsWontReviewCase() = %v, want %v", got, tt.want)
			}
		})
	}
}
