syntax = "proto3";

package developer.devcache;

import "api/rpc/status.proto";

option go_package = "github.com/epifi/be-common/api/developer/devcache";
option java_package = "com.github.epifi.be-common.api.developer.devcache";

// This service provides one-off cache operations for debugging purposes.
// It allows developers to manually perform operations on specific keys in the cache storage
// to diagnose or resolve issues. This service should only be used during debugging
// and not in production workflows, as it bypasses regular caching mechanisms.
service DevCache {
  // Fetches list of all the supported cache storages. returns CacheStorage enums as strings.
  // This is used to populate the dropdown in the UI.
  rpc GetCacheStorages(GetCacheStoragesRequest) returns (GetCacheStoragesResponse) {}

  // Operation to get cache entry for a key in the storage.
  rpc GetCacheEntry(GetCacheEntryRequest) returns (GetCacheEntryResponse) {}

  // Operation to delete cache entry for a key in the storage.
  rpc DeleteCacheEntry(DeleteCacheEntryRequest) returns (DeleteCacheEntryResponse) {}
}

message GetCacheStoragesRequest {}

message GetCacheStoragesResponse {
  rpc.Status status = 1;
  // CacheStorage enums as strings
  repeated string cache_storages = 2;
}

message GetCacheEntryRequest {
  // Cache Storage in which key will be searched. Send CacheStorage enum as string.
  // String is used instead of enum to not require any deployment in callers if any changes in storage enum.
  // eg. "CACHE_STORAGE_ONBOARDING"
  string cache_storage = 1;

  // Key to be fetched. e.g. "minUser:919123123123"
  string key = 2;
}

message GetCacheEntryResponse {
  rpc.Status status = 1;
  // cache value of the key.
  string cache_entry = 2;
}

message DeleteCacheEntryRequest {
  // Cache Storage in which key will be searched. Send CacheStorage enum as string.
  // String is used instead of enum to not require any deployment in callers if any changes in storage enum.
  // eg. "CACHE_STORAGE_ONBOARDING"
  string cache_storage = 1;

  // Key to be deleted. e.g. "minUser:919123123123"
  string key = 2;
}

message DeleteCacheEntryResponse {
  rpc.Status status = 1;
}
