// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/webfe/travel/service.proto

package travel

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetTravelDestinationsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTravelDestinationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTravelDestinationsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTravelDestinationsRequestMultiError, or nil if none found.
func (m *GetTravelDestinationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTravelDestinationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTravelDestinationsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTravelDestinationsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTravelDestinationsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTravelDestinationsRequestMultiError(errors)
	}

	return nil
}

// GetTravelDestinationsRequestMultiError is an error wrapping multiple
// validation errors returned by GetTravelDestinationsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetTravelDestinationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTravelDestinationsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTravelDestinationsRequestMultiError) AllErrors() []error { return m }

// GetTravelDestinationsRequestValidationError is the validation error returned
// by GetTravelDestinationsRequest.Validate if the designated constraints
// aren't met.
type GetTravelDestinationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTravelDestinationsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTravelDestinationsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTravelDestinationsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTravelDestinationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTravelDestinationsRequestValidationError) ErrorName() string {
	return "GetTravelDestinationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTravelDestinationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTravelDestinationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTravelDestinationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTravelDestinationsRequestValidationError{}

// Validate checks the field values on GetTravelDestinationsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTravelDestinationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTravelDestinationsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTravelDestinationsResponseMultiError, or nil if none found.
func (m *GetTravelDestinationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTravelDestinationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTravelDestinationsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTravelDestinationsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTravelDestinationsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTravelDestinations()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTravelDestinationsResponseValidationError{
					field:  "TravelDestinations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTravelDestinationsResponseValidationError{
					field:  "TravelDestinations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTravelDestinations()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTravelDestinationsResponseValidationError{
				field:  "TravelDestinations",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTravelDestinationsResponseMultiError(errors)
	}

	return nil
}

// GetTravelDestinationsResponseMultiError is an error wrapping multiple
// validation errors returned by GetTravelDestinationsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetTravelDestinationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTravelDestinationsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTravelDestinationsResponseMultiError) AllErrors() []error { return m }

// GetTravelDestinationsResponseValidationError is the validation error
// returned by GetTravelDestinationsResponse.Validate if the designated
// constraints aren't met.
type GetTravelDestinationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTravelDestinationsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTravelDestinationsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTravelDestinationsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTravelDestinationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTravelDestinationsResponseValidationError) ErrorName() string {
	return "GetTravelDestinationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTravelDestinationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTravelDestinationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTravelDestinationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTravelDestinationsResponseValidationError{}

// Validate checks the field values on GetTravelExpenseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTravelExpenseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTravelExpenseRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTravelExpenseRequestMultiError, or nil if none found.
func (m *GetTravelExpenseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTravelExpenseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTravelExpenseRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTravelExpenseRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTravelExpenseRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpenseData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTravelExpenseRequestValidationError{
					field:  "ExpenseData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTravelExpenseRequestValidationError{
					field:  "ExpenseData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpenseData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTravelExpenseRequestValidationError{
				field:  "ExpenseData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTravelExpenseRequestMultiError(errors)
	}

	return nil
}

// GetTravelExpenseRequestMultiError is an error wrapping multiple validation
// errors returned by GetTravelExpenseRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTravelExpenseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTravelExpenseRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTravelExpenseRequestMultiError) AllErrors() []error { return m }

// GetTravelExpenseRequestValidationError is the validation error returned by
// GetTravelExpenseRequest.Validate if the designated constraints aren't met.
type GetTravelExpenseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTravelExpenseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTravelExpenseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTravelExpenseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTravelExpenseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTravelExpenseRequestValidationError) ErrorName() string {
	return "GetTravelExpenseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTravelExpenseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTravelExpenseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTravelExpenseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTravelExpenseRequestValidationError{}

// Validate checks the field values on GetTravelExpenseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTravelExpenseResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTravelExpenseResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTravelExpenseResponseMultiError, or nil if none found.
func (m *GetTravelExpenseResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTravelExpenseResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTravelExpenseResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTravelExpenseResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTravelExpenseResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpenseBudget()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTravelExpenseResponseValidationError{
					field:  "ExpenseBudget",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTravelExpenseResponseValidationError{
					field:  "ExpenseBudget",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpenseBudget()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTravelExpenseResponseValidationError{
				field:  "ExpenseBudget",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTravelExpenseResponseMultiError(errors)
	}

	return nil
}

// GetTravelExpenseResponseMultiError is an error wrapping multiple validation
// errors returned by GetTravelExpenseResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTravelExpenseResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTravelExpenseResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTravelExpenseResponseMultiError) AllErrors() []error { return m }

// GetTravelExpenseResponseValidationError is the validation error returned by
// GetTravelExpenseResponse.Validate if the designated constraints aren't met.
type GetTravelExpenseResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTravelExpenseResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTravelExpenseResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTravelExpenseResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTravelExpenseResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTravelExpenseResponseValidationError) ErrorName() string {
	return "GetTravelExpenseResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTravelExpenseResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTravelExpenseResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTravelExpenseResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTravelExpenseResponseValidationError{}

// Validate checks the field values on
// GetInternationalATMWithdrawalLimitsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetInternationalATMWithdrawalLimitsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetInternationalATMWithdrawalLimitsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetInternationalATMWithdrawalLimitsRequestMultiError, or nil if none found.
func (m *GetInternationalATMWithdrawalLimitsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInternationalATMWithdrawalLimitsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInternationalATMWithdrawalLimitsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInternationalATMWithdrawalLimitsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInternationalATMWithdrawalLimitsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInternationalATMWithdrawalLimitsRequestMultiError(errors)
	}

	return nil
}

// GetInternationalATMWithdrawalLimitsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetInternationalATMWithdrawalLimitsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetInternationalATMWithdrawalLimitsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInternationalATMWithdrawalLimitsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInternationalATMWithdrawalLimitsRequestMultiError) AllErrors() []error { return m }

// GetInternationalATMWithdrawalLimitsRequestValidationError is the validation
// error returned by GetInternationalATMWithdrawalLimitsRequest.Validate if
// the designated constraints aren't met.
type GetInternationalATMWithdrawalLimitsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInternationalATMWithdrawalLimitsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInternationalATMWithdrawalLimitsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInternationalATMWithdrawalLimitsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInternationalATMWithdrawalLimitsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInternationalATMWithdrawalLimitsRequestValidationError) ErrorName() string {
	return "GetInternationalATMWithdrawalLimitsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInternationalATMWithdrawalLimitsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInternationalATMWithdrawalLimitsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInternationalATMWithdrawalLimitsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInternationalATMWithdrawalLimitsRequestValidationError{}

// Validate checks the field values on
// GetInternationalATMWithdrawalLimitsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetInternationalATMWithdrawalLimitsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetInternationalATMWithdrawalLimitsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetInternationalATMWithdrawalLimitsResponseMultiError, or nil if none found.
func (m *GetInternationalATMWithdrawalLimitsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInternationalATMWithdrawalLimitsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInternationalATMWithdrawalLimitsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInternationalATMWithdrawalLimitsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInternationalATMWithdrawalLimitsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAtmLimits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetInternationalATMWithdrawalLimitsResponseValidationError{
						field:  fmt.Sprintf("AtmLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetInternationalATMWithdrawalLimitsResponseValidationError{
						field:  fmt.Sprintf("AtmLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetInternationalATMWithdrawalLimitsResponseValidationError{
					field:  fmt.Sprintf("AtmLimits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetInternationalATMWithdrawalLimitsResponseMultiError(errors)
	}

	return nil
}

// GetInternationalATMWithdrawalLimitsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetInternationalATMWithdrawalLimitsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetInternationalATMWithdrawalLimitsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInternationalATMWithdrawalLimitsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInternationalATMWithdrawalLimitsResponseMultiError) AllErrors() []error { return m }

// GetInternationalATMWithdrawalLimitsResponseValidationError is the validation
// error returned by GetInternationalATMWithdrawalLimitsResponse.Validate if
// the designated constraints aren't met.
type GetInternationalATMWithdrawalLimitsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInternationalATMWithdrawalLimitsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInternationalATMWithdrawalLimitsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInternationalATMWithdrawalLimitsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInternationalATMWithdrawalLimitsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInternationalATMWithdrawalLimitsResponseValidationError) ErrorName() string {
	return "GetInternationalATMWithdrawalLimitsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInternationalATMWithdrawalLimitsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInternationalATMWithdrawalLimitsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInternationalATMWithdrawalLimitsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInternationalATMWithdrawalLimitsResponseValidationError{}

// Validate checks the field values on GetForexExchangeRateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetForexExchangeRateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetForexExchangeRateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetForexExchangeRateRequestMultiError, or nil if none found.
func (m *GetForexExchangeRateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetForexExchangeRateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForexExchangeRateRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForexExchangeRateRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForexExchangeRateRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FromCountryCode

	// no validation rules for ToCountryCode

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForexExchangeRateRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForexExchangeRateRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForexExchangeRateRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetForexExchangeRateRequestMultiError(errors)
	}

	return nil
}

// GetForexExchangeRateRequestMultiError is an error wrapping multiple
// validation errors returned by GetForexExchangeRateRequest.ValidateAll() if
// the designated constraints aren't met.
type GetForexExchangeRateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetForexExchangeRateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetForexExchangeRateRequestMultiError) AllErrors() []error { return m }

// GetForexExchangeRateRequestValidationError is the validation error returned
// by GetForexExchangeRateRequest.Validate if the designated constraints
// aren't met.
type GetForexExchangeRateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetForexExchangeRateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetForexExchangeRateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetForexExchangeRateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetForexExchangeRateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetForexExchangeRateRequestValidationError) ErrorName() string {
	return "GetForexExchangeRateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetForexExchangeRateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetForexExchangeRateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetForexExchangeRateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetForexExchangeRateRequestValidationError{}

// Validate checks the field values on GetForexExchangeRateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetForexExchangeRateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetForexExchangeRateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetForexExchangeRateResponseMultiError, or nil if none found.
func (m *GetForexExchangeRateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetForexExchangeRateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForexExchangeRateResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForexExchangeRateResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForexExchangeRateResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConvertedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForexExchangeRateResponseValidationError{
					field:  "ConvertedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForexExchangeRateResponseValidationError{
					field:  "ConvertedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConvertedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForexExchangeRateResponseValidationError{
				field:  "ConvertedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetForexSavingsAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForexExchangeRateResponseValidationError{
					field:  "ForexSavingsAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForexExchangeRateResponseValidationError{
					field:  "ForexSavingsAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetForexSavingsAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForexExchangeRateResponseValidationError{
				field:  "ForexSavingsAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardOffersAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForexExchangeRateResponseValidationError{
					field:  "CardOffersAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForexExchangeRateResponseValidationError{
					field:  "CardOffersAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardOffersAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForexExchangeRateResponseValidationError{
				field:  "CardOffersAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetForexExchangeRateResponseMultiError(errors)
	}

	return nil
}

// GetForexExchangeRateResponseMultiError is an error wrapping multiple
// validation errors returned by GetForexExchangeRateResponse.ValidateAll() if
// the designated constraints aren't met.
type GetForexExchangeRateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetForexExchangeRateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetForexExchangeRateResponseMultiError) AllErrors() []error { return m }

// GetForexExchangeRateResponseValidationError is the validation error returned
// by GetForexExchangeRateResponse.Validate if the designated constraints
// aren't met.
type GetForexExchangeRateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetForexExchangeRateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetForexExchangeRateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetForexExchangeRateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetForexExchangeRateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetForexExchangeRateResponseValidationError) ErrorName() string {
	return "GetForexExchangeRateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetForexExchangeRateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetForexExchangeRateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetForexExchangeRateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetForexExchangeRateResponseValidationError{}

// Validate checks the field values on
// GetTravelDestinationsResponse_TravelDestinations with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetTravelDestinationsResponse_TravelDestinations) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetTravelDestinationsResponse_TravelDestinations with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetTravelDestinationsResponse_TravelDestinationsMultiError, or nil if none found.
func (m *GetTravelDestinationsResponse_TravelDestinations) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTravelDestinationsResponse_TravelDestinations) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDestinations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTravelDestinationsResponse_TravelDestinationsValidationError{
						field:  fmt.Sprintf("Destinations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTravelDestinationsResponse_TravelDestinationsValidationError{
						field:  fmt.Sprintf("Destinations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTravelDestinationsResponse_TravelDestinationsValidationError{
					field:  fmt.Sprintf("Destinations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetPopularDestinations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTravelDestinationsResponse_TravelDestinationsValidationError{
						field:  fmt.Sprintf("PopularDestinations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTravelDestinationsResponse_TravelDestinationsValidationError{
						field:  fmt.Sprintf("PopularDestinations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTravelDestinationsResponse_TravelDestinationsValidationError{
					field:  fmt.Sprintf("PopularDestinations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTravelDestinationsResponse_TravelDestinationsMultiError(errors)
	}

	return nil
}

// GetTravelDestinationsResponse_TravelDestinationsMultiError is an error
// wrapping multiple validation errors returned by
// GetTravelDestinationsResponse_TravelDestinations.ValidateAll() if the
// designated constraints aren't met.
type GetTravelDestinationsResponse_TravelDestinationsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTravelDestinationsResponse_TravelDestinationsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTravelDestinationsResponse_TravelDestinationsMultiError) AllErrors() []error { return m }

// GetTravelDestinationsResponse_TravelDestinationsValidationError is the
// validation error returned by
// GetTravelDestinationsResponse_TravelDestinations.Validate if the designated
// constraints aren't met.
type GetTravelDestinationsResponse_TravelDestinationsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTravelDestinationsResponse_TravelDestinationsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetTravelDestinationsResponse_TravelDestinationsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetTravelDestinationsResponse_TravelDestinationsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetTravelDestinationsResponse_TravelDestinationsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTravelDestinationsResponse_TravelDestinationsValidationError) ErrorName() string {
	return "GetTravelDestinationsResponse_TravelDestinationsValidationError"
}

// Error satisfies the builtin error interface
func (e GetTravelDestinationsResponse_TravelDestinationsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTravelDestinationsResponse_TravelDestinations.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTravelDestinationsResponse_TravelDestinationsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTravelDestinationsResponse_TravelDestinationsValidationError{}

// Validate checks the field values on GetTravelExpenseRequest_ExpenseData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTravelExpenseRequest_ExpenseData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTravelExpenseRequest_ExpenseData
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetTravelExpenseRequest_ExpenseDataMultiError, or nil if none found.
func (m *GetTravelExpenseRequest_ExpenseData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTravelExpenseRequest_ExpenseData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Country

	if all {
		switch v := interface{}(m.GetStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTravelExpenseRequest_ExpenseDataValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTravelExpenseRequest_ExpenseDataValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTravelExpenseRequest_ExpenseDataValidationError{
				field:  "StartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTravelExpenseRequest_ExpenseDataValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTravelExpenseRequest_ExpenseDataValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTravelExpenseRequest_ExpenseDataValidationError{
				field:  "EndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PeopleCount

	// no validation rules for TravelStyle

	if len(errors) > 0 {
		return GetTravelExpenseRequest_ExpenseDataMultiError(errors)
	}

	return nil
}

// GetTravelExpenseRequest_ExpenseDataMultiError is an error wrapping multiple
// validation errors returned by
// GetTravelExpenseRequest_ExpenseData.ValidateAll() if the designated
// constraints aren't met.
type GetTravelExpenseRequest_ExpenseDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTravelExpenseRequest_ExpenseDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTravelExpenseRequest_ExpenseDataMultiError) AllErrors() []error { return m }

// GetTravelExpenseRequest_ExpenseDataValidationError is the validation error
// returned by GetTravelExpenseRequest_ExpenseData.Validate if the designated
// constraints aren't met.
type GetTravelExpenseRequest_ExpenseDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTravelExpenseRequest_ExpenseDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTravelExpenseRequest_ExpenseDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTravelExpenseRequest_ExpenseDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTravelExpenseRequest_ExpenseDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTravelExpenseRequest_ExpenseDataValidationError) ErrorName() string {
	return "GetTravelExpenseRequest_ExpenseDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetTravelExpenseRequest_ExpenseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTravelExpenseRequest_ExpenseData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTravelExpenseRequest_ExpenseDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTravelExpenseRequest_ExpenseDataValidationError{}

// Validate checks the field values on
// GetTravelExpenseResponse_TravelExpenseBudget with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetTravelExpenseResponse_TravelExpenseBudget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetTravelExpenseResponse_TravelExpenseBudget with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetTravelExpenseResponse_TravelExpenseBudgetMultiError, or nil if none found.
func (m *GetTravelExpenseResponse_TravelExpenseBudget) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTravelExpenseResponse_TravelExpenseBudget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDailyExpenses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
						field:  fmt.Sprintf("DailyExpenses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
						field:  fmt.Sprintf("DailyExpenses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
					field:  fmt.Sprintf("DailyExpenses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetTravelOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
						field:  fmt.Sprintf("TravelOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
						field:  fmt.Sprintf("TravelOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
					field:  fmt.Sprintf("TravelOffers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCountry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
					field:  "Country",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
					field:  "Country",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCountry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
				field:  "Country",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UnitTotalEstimateExpenseAmount

	// no validation rules for CurrencyConversionRate

	// no validation rules for TravelDays

	// no validation rules for DestinationNativeCurrency

	if all {
		switch v := interface{}(m.GetCountryImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
					field:  "CountryImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
					field:  "CountryImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCountryImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
				field:  "CountryImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
				field:  "StartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
				field:  "EndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PeopleCount

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTravelExpenseResponse_TravelExpenseBudgetValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TravelStyle

	// no validation rules for TotalSavingsAmount

	// no validation rules for ZeroMarkupSavingsAmount

	// no validation rules for CardOffersSavingsAmount

	// no validation rules for NativeCurrencySymbol

	if len(errors) > 0 {
		return GetTravelExpenseResponse_TravelExpenseBudgetMultiError(errors)
	}

	return nil
}

// GetTravelExpenseResponse_TravelExpenseBudgetMultiError is an error wrapping
// multiple validation errors returned by
// GetTravelExpenseResponse_TravelExpenseBudget.ValidateAll() if the
// designated constraints aren't met.
type GetTravelExpenseResponse_TravelExpenseBudgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTravelExpenseResponse_TravelExpenseBudgetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTravelExpenseResponse_TravelExpenseBudgetMultiError) AllErrors() []error { return m }

// GetTravelExpenseResponse_TravelExpenseBudgetValidationError is the
// validation error returned by
// GetTravelExpenseResponse_TravelExpenseBudget.Validate if the designated
// constraints aren't met.
type GetTravelExpenseResponse_TravelExpenseBudgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTravelExpenseResponse_TravelExpenseBudgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTravelExpenseResponse_TravelExpenseBudgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTravelExpenseResponse_TravelExpenseBudgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTravelExpenseResponse_TravelExpenseBudgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTravelExpenseResponse_TravelExpenseBudgetValidationError) ErrorName() string {
	return "GetTravelExpenseResponse_TravelExpenseBudgetValidationError"
}

// Error satisfies the builtin error interface
func (e GetTravelExpenseResponse_TravelExpenseBudgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTravelExpenseResponse_TravelExpenseBudget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTravelExpenseResponse_TravelExpenseBudgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTravelExpenseResponse_TravelExpenseBudgetValidationError{}

// Validate checks the field values on
// GetInternationalATMWithdrawalLimitsResponse_ATMLimit with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetInternationalATMWithdrawalLimitsResponse_ATMLimit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetInternationalATMWithdrawalLimitsResponse_ATMLimit with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetInternationalATMWithdrawalLimitsResponse_ATMLimitMultiError, or nil if
// none found.
func (m *GetInternationalATMWithdrawalLimitsResponse_ATMLimit) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInternationalATMWithdrawalLimitsResponse_ATMLimit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CountryName

	// no validation rules for CountryFlag

	if all {
		switch v := interface{}(m.GetAtmLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInternationalATMWithdrawalLimitsResponse_ATMLimitValidationError{
					field:  "AtmLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInternationalATMWithdrawalLimitsResponse_ATMLimitValidationError{
					field:  "AtmLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAtmLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInternationalATMWithdrawalLimitsResponse_ATMLimitValidationError{
				field:  "AtmLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInternationalATMWithdrawalLimitsResponse_ATMLimitMultiError(errors)
	}

	return nil
}

// GetInternationalATMWithdrawalLimitsResponse_ATMLimitMultiError is an error
// wrapping multiple validation errors returned by
// GetInternationalATMWithdrawalLimitsResponse_ATMLimit.ValidateAll() if the
// designated constraints aren't met.
type GetInternationalATMWithdrawalLimitsResponse_ATMLimitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInternationalATMWithdrawalLimitsResponse_ATMLimitMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInternationalATMWithdrawalLimitsResponse_ATMLimitMultiError) AllErrors() []error { return m }

// GetInternationalATMWithdrawalLimitsResponse_ATMLimitValidationError is the
// validation error returned by
// GetInternationalATMWithdrawalLimitsResponse_ATMLimit.Validate if the
// designated constraints aren't met.
type GetInternationalATMWithdrawalLimitsResponse_ATMLimitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInternationalATMWithdrawalLimitsResponse_ATMLimitValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetInternationalATMWithdrawalLimitsResponse_ATMLimitValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetInternationalATMWithdrawalLimitsResponse_ATMLimitValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetInternationalATMWithdrawalLimitsResponse_ATMLimitValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInternationalATMWithdrawalLimitsResponse_ATMLimitValidationError) ErrorName() string {
	return "GetInternationalATMWithdrawalLimitsResponse_ATMLimitValidationError"
}

// Error satisfies the builtin error interface
func (e GetInternationalATMWithdrawalLimitsResponse_ATMLimitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInternationalATMWithdrawalLimitsResponse_ATMLimit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInternationalATMWithdrawalLimitsResponse_ATMLimitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInternationalATMWithdrawalLimitsResponse_ATMLimitValidationError{}
