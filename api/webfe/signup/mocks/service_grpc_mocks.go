// Code generated by MockGen. DO NOT EDIT.
// Source: api/webfe/signup/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	signup "github.com/epifi/gamma/api/webfe/signup"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockSignupClient is a mock of SignupClient interface.
type MockSignupClient struct {
	ctrl     *gomock.Controller
	recorder *MockSignupClientMockRecorder
}

// MockSignupClientMockRecorder is the mock recorder for MockSignupClient.
type MockSignupClientMockRecorder struct {
	mock *MockSignupClient
}

// NewMockSignupClient creates a new mock instance.
func NewMockSignupClient(ctrl *gomock.Controller) *MockSignupClient {
	mock := &MockSignupClient{ctrl: ctrl}
	mock.recorder = &MockSignupClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSignupClient) EXPECT() *MockSignupClientMockRecorder {
	return m.recorder
}

// GenerateOtp mocks base method.
func (m *MockSignupClient) GenerateOtp(ctx context.Context, in *signup.GenerateOtpRequest, opts ...grpc.CallOption) (*signup.GenerateOtpResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateOtp", varargs...)
	ret0, _ := ret[0].(*signup.GenerateOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateOtp indicates an expected call of GenerateOtp.
func (mr *MockSignupClientMockRecorder) GenerateOtp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateOtp", reflect.TypeOf((*MockSignupClient)(nil).GenerateOtp), varargs...)
}

// VerifyOtp mocks base method.
func (m *MockSignupClient) VerifyOtp(ctx context.Context, in *signup.VerifyOtpRequest, opts ...grpc.CallOption) (*signup.VerifyOtpResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyOtp", varargs...)
	ret0, _ := ret[0].(*signup.VerifyOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyOtp indicates an expected call of VerifyOtp.
func (mr *MockSignupClientMockRecorder) VerifyOtp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyOtp", reflect.TypeOf((*MockSignupClient)(nil).VerifyOtp), varargs...)
}

// MockSignupServer is a mock of SignupServer interface.
type MockSignupServer struct {
	ctrl     *gomock.Controller
	recorder *MockSignupServerMockRecorder
}

// MockSignupServerMockRecorder is the mock recorder for MockSignupServer.
type MockSignupServerMockRecorder struct {
	mock *MockSignupServer
}

// NewMockSignupServer creates a new mock instance.
func NewMockSignupServer(ctrl *gomock.Controller) *MockSignupServer {
	mock := &MockSignupServer{ctrl: ctrl}
	mock.recorder = &MockSignupServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSignupServer) EXPECT() *MockSignupServerMockRecorder {
	return m.recorder
}

// GenerateOtp mocks base method.
func (m *MockSignupServer) GenerateOtp(arg0 context.Context, arg1 *signup.GenerateOtpRequest) (*signup.GenerateOtpResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateOtp", arg0, arg1)
	ret0, _ := ret[0].(*signup.GenerateOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateOtp indicates an expected call of GenerateOtp.
func (mr *MockSignupServerMockRecorder) GenerateOtp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateOtp", reflect.TypeOf((*MockSignupServer)(nil).GenerateOtp), arg0, arg1)
}

// VerifyOtp mocks base method.
func (m *MockSignupServer) VerifyOtp(arg0 context.Context, arg1 *signup.VerifyOtpRequest) (*signup.VerifyOtpResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyOtp", arg0, arg1)
	ret0, _ := ret[0].(*signup.VerifyOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyOtp indicates an expected call of VerifyOtp.
func (mr *MockSignupServerMockRecorder) VerifyOtp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyOtp", reflect.TypeOf((*MockSignupServer)(nil).VerifyOtp), arg0, arg1)
}

// MockUnsafeSignupServer is a mock of UnsafeSignupServer interface.
type MockUnsafeSignupServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeSignupServerMockRecorder
}

// MockUnsafeSignupServerMockRecorder is the mock recorder for MockUnsafeSignupServer.
type MockUnsafeSignupServerMockRecorder struct {
	mock *MockUnsafeSignupServer
}

// NewMockUnsafeSignupServer creates a new mock instance.
func NewMockUnsafeSignupServer(ctrl *gomock.Controller) *MockUnsafeSignupServer {
	mock := &MockUnsafeSignupServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeSignupServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeSignupServer) EXPECT() *MockUnsafeSignupServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedSignupServer mocks base method.
func (m *MockUnsafeSignupServer) mustEmbedUnimplementedSignupServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedSignupServer")
}

// mustEmbedUnimplementedSignupServer indicates an expected call of mustEmbedUnimplementedSignupServer.
func (mr *MockUnsafeSignupServerMockRecorder) mustEmbedUnimplementedSignupServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedSignupServer", reflect.TypeOf((*MockUnsafeSignupServer)(nil).mustEmbedUnimplementedSignupServer))
}
