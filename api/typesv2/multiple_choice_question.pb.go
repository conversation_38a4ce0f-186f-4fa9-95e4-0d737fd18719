// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/multiple_choice_question.proto

package typesv2

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// MultiChoiceSelectType indicates the type of choice selection for a multiple choice question
// can be used in cases like - https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=14687-51349&t=1JDpI2k4VouE2qyL-0
type MultiChoiceSelectType int32

const (
	MultiChoiceSelectType_MULTI_CHOICE_SELECT_TYPE_UNSPECIFIED MultiChoiceSelectType = 0
	// only single answer can be selected
	MultiChoiceSelectType_MULTI_CHOICE_SELECT_TYPE_SINGLE_SELECT MultiChoiceSelectType = 1
	// multiple answers can be selected
	MultiChoiceSelectType_MULTI_CHOICE_SELECT_TYPE_MULTI_SELECT MultiChoiceSelectType = 2
)

// Enum value maps for MultiChoiceSelectType.
var (
	MultiChoiceSelectType_name = map[int32]string{
		0: "MULTI_CHOICE_SELECT_TYPE_UNSPECIFIED",
		1: "MULTI_CHOICE_SELECT_TYPE_SINGLE_SELECT",
		2: "MULTI_CHOICE_SELECT_TYPE_MULTI_SELECT",
	}
	MultiChoiceSelectType_value = map[string]int32{
		"MULTI_CHOICE_SELECT_TYPE_UNSPECIFIED":   0,
		"MULTI_CHOICE_SELECT_TYPE_SINGLE_SELECT": 1,
		"MULTI_CHOICE_SELECT_TYPE_MULTI_SELECT":  2,
	}
)

func (x MultiChoiceSelectType) Enum() *MultiChoiceSelectType {
	p := new(MultiChoiceSelectType)
	*p = x
	return p
}

func (x MultiChoiceSelectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MultiChoiceSelectType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_multiple_choice_question_proto_enumTypes[0].Descriptor()
}

func (MultiChoiceSelectType) Type() protoreflect.EnumType {
	return &file_api_typesv2_multiple_choice_question_proto_enumTypes[0]
}

func (x MultiChoiceSelectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MultiChoiceSelectType.Descriptor instead.
func (MultiChoiceSelectType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_multiple_choice_question_proto_rawDescGZIP(), []int{0}
}

var File_api_typesv2_multiple_choice_question_proto protoreflect.FileDescriptor

var file_api_typesv2_multiple_choice_question_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2a, 0x98, 0x01, 0x0a, 0x15, 0x4d, 0x75,
	0x6c, 0x74, 0x69, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x24, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f, 0x43, 0x48, 0x4f,
	0x49, 0x43, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2a, 0x0a,
	0x26, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f, 0x43, 0x48, 0x4f, 0x49, 0x43, 0x45, 0x5f, 0x53, 0x45,
	0x4c, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45,
	0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x4d, 0x55, 0x4c,
	0x54, 0x49, 0x5f, 0x43, 0x48, 0x4f, 0x49, 0x43, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f, 0x53, 0x45, 0x4c, 0x45,
	0x43, 0x54, 0x10, 0x02, 0x42, 0x48, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x5a, 0x22, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_multiple_choice_question_proto_rawDescOnce sync.Once
	file_api_typesv2_multiple_choice_question_proto_rawDescData = file_api_typesv2_multiple_choice_question_proto_rawDesc
)

func file_api_typesv2_multiple_choice_question_proto_rawDescGZIP() []byte {
	file_api_typesv2_multiple_choice_question_proto_rawDescOnce.Do(func() {
		file_api_typesv2_multiple_choice_question_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_multiple_choice_question_proto_rawDescData)
	})
	return file_api_typesv2_multiple_choice_question_proto_rawDescData
}

var file_api_typesv2_multiple_choice_question_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_multiple_choice_question_proto_goTypes = []interface{}{
	(MultiChoiceSelectType)(0), // 0: api.typesv2.MultiChoiceSelectType
}
var file_api_typesv2_multiple_choice_question_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_typesv2_multiple_choice_question_proto_init() }
func file_api_typesv2_multiple_choice_question_proto_init() {
	if File_api_typesv2_multiple_choice_question_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_multiple_choice_question_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_multiple_choice_question_proto_goTypes,
		DependencyIndexes: file_api_typesv2_multiple_choice_question_proto_depIdxs,
		EnumInfos:         file_api_typesv2_multiple_choice_question_proto_enumTypes,
	}.Build()
	File_api_typesv2_multiple_choice_question_proto = out.File
	file_api_typesv2_multiple_choice_question_proto_rawDesc = nil
	file_api_typesv2_multiple_choice_question_proto_goTypes = nil
	file_api_typesv2_multiple_choice_question_proto_depIdxs = nil
}
