// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/nr_onboarding/initiate_uqudo_sdk.proto

package nr_onboarding

import (
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// document type to be passed while initiating the sdk
// ref - https://docs.uqudo.com/docs/kyc/uqudo-sdk/integration/android/enrolment-flow#:~:text=setDocumentType(DocumentType.PASSPORT)
type InitiateUqudoSdkScreenOptions_DocType int32

const (
	InitiateUqudoSdkScreenOptions_DOC_TYPE_UNSPECIFIED InitiateUqudoSdkScreenOptions_DocType = 0
	InitiateUqudoSdkScreenOptions_DOC_TYPE_EMIRATES_ID InitiateUqudoSdkScreenOptions_DocType = 1
	InitiateUqudoSdkScreenOptions_DOC_TYPE_PASSPORT    InitiateUqudoSdkScreenOptions_DocType = 2
	InitiateUqudoSdkScreenOptions_DOC_TYPE_QATAR_ID    InitiateUqudoSdkScreenOptions_DocType = 3
)

// Enum value maps for InitiateUqudoSdkScreenOptions_DocType.
var (
	InitiateUqudoSdkScreenOptions_DocType_name = map[int32]string{
		0: "DOC_TYPE_UNSPECIFIED",
		1: "DOC_TYPE_EMIRATES_ID",
		2: "DOC_TYPE_PASSPORT",
		3: "DOC_TYPE_QATAR_ID",
	}
	InitiateUqudoSdkScreenOptions_DocType_value = map[string]int32{
		"DOC_TYPE_UNSPECIFIED": 0,
		"DOC_TYPE_EMIRATES_ID": 1,
		"DOC_TYPE_PASSPORT":    2,
		"DOC_TYPE_QATAR_ID":    3,
	}
)

func (x InitiateUqudoSdkScreenOptions_DocType) Enum() *InitiateUqudoSdkScreenOptions_DocType {
	p := new(InitiateUqudoSdkScreenOptions_DocType)
	*p = x
	return p
}

func (x InitiateUqudoSdkScreenOptions_DocType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InitiateUqudoSdkScreenOptions_DocType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_enumTypes[0].Descriptor()
}

func (InitiateUqudoSdkScreenOptions_DocType) Type() protoreflect.EnumType {
	return &file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_enumTypes[0]
}

func (x InitiateUqudoSdkScreenOptions_DocType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InitiateUqudoSdkScreenOptions_DocType.Descriptor instead.
func (InitiateUqudoSdkScreenOptions_DocType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_rawDescGZIP(), []int{0, 0}
}

// Screen options for Screen - INITIATE_UQUDO_SDK
type InitiateUqudoSdkScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// exit_deeplink to which the user is redirected after a successful completing scan.
	ExitDeeplink *deeplink.Deeplink `protobuf:"bytes,2,opt,name=exit_deeplink,json=exitDeeplink,proto3" json:"exit_deeplink,omitempty"`
	// unique identifier for sdk attempt.
	// client will pass this as session_id to sdk.
	ClientRequestId string                                `protobuf:"bytes,3,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	DocType         InitiateUqudoSdkScreenOptions_DocType `protobuf:"varint,4,opt,name=doc_type,json=docType,proto3,enum=api.typesv2.deeplink_screen_option.nr_onboarding.InitiateUqudoSdkScreenOptions_DocType" json:"doc_type,omitempty"`
	// A boolean indicating whether an NFC scan is required.
	NfcScanRequired bool `protobuf:"varint,5,opt,name=nfc_scan_required,json=nfcScanRequired,proto3" json:"nfc_scan_required,omitempty"`
}

func (x *InitiateUqudoSdkScreenOptions) Reset() {
	*x = InitiateUqudoSdkScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateUqudoSdkScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateUqudoSdkScreenOptions) ProtoMessage() {}

func (x *InitiateUqudoSdkScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateUqudoSdkScreenOptions.ProtoReflect.Descriptor instead.
func (*InitiateUqudoSdkScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_rawDescGZIP(), []int{0}
}

func (x *InitiateUqudoSdkScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *InitiateUqudoSdkScreenOptions) GetExitDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.ExitDeeplink
	}
	return nil
}

func (x *InitiateUqudoSdkScreenOptions) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *InitiateUqudoSdkScreenOptions) GetDocType() InitiateUqudoSdkScreenOptions_DocType {
	if x != nil {
		return x.DocType
	}
	return InitiateUqudoSdkScreenOptions_DOC_TYPE_UNSPECIFIED
}

func (x *InitiateUqudoSdkScreenOptions) GetNfcScanRequired() bool {
	if x != nil {
		return x.NfcScanRequired
	}
	return false
}

var File_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_rawDesc = []byte{
	0x0a, 0x49, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6e, 0x72, 0x5f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2f, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x71, 0x75, 0x64,
	0x6f, 0x5f, 0x73, 0x64, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x30, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x6e, 0x72, 0x5f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x24, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xea, 0x03, 0x0a, 0x1d, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74,
	0x65, 0x55, 0x71, 0x75, 0x64, 0x6f, 0x53, 0x64, 0x6b, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x0d, 0x65, 0x78, 0x69, 0x74, 0x5f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0c, 0x65, 0x78, 0x69, 0x74,
	0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x72, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x57, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6e, 0x72, 0x5f, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x74, 0x65, 0x55, 0x71, 0x75, 0x64, 0x6f, 0x53, 0x64, 0x6b, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x44, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6e, 0x66, 0x63, 0x5f,
	0x73, 0x63, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0f, 0x6e, 0x66, 0x63, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x22, 0x6b, 0x0a, 0x07, 0x44, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x18, 0x0a, 0x14, 0x44, 0x4f, 0x43, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x44, 0x4f, 0x43,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4d, 0x49, 0x52, 0x41, 0x54, 0x45, 0x53, 0x5f, 0x49,
	0x44, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x4f, 0x43, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x50, 0x41, 0x53, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x4f,
	0x43, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x51, 0x41, 0x54, 0x41, 0x52, 0x5f, 0x49, 0x44, 0x10,
	0x03, 0x42, 0x94, 0x01, 0x0a, 0x47, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x6e, 0x72, 0x5f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x01, 0x5a,
	0x47, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6e, 0x72, 0x5f, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_rawDescData = file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_goTypes = []interface{}{
	(InitiateUqudoSdkScreenOptions_DocType)(0),        // 0: api.typesv2.deeplink_screen_option.nr_onboarding.InitiateUqudoSdkScreenOptions.DocType
	(*InitiateUqudoSdkScreenOptions)(nil),             // 1: api.typesv2.deeplink_screen_option.nr_onboarding.InitiateUqudoSdkScreenOptions
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 2: api.typesv2.deeplink_screen_option.ScreenOptionHeader
	(*deeplink.Deeplink)(nil),                         // 3: frontend.deeplink.Deeplink
}
var file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_depIdxs = []int32{
	2, // 0: api.typesv2.deeplink_screen_option.nr_onboarding.InitiateUqudoSdkScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	3, // 1: api.typesv2.deeplink_screen_option.nr_onboarding.InitiateUqudoSdkScreenOptions.exit_deeplink:type_name -> frontend.deeplink.Deeplink
	0, // 2: api.typesv2.deeplink_screen_option.nr_onboarding.InitiateUqudoSdkScreenOptions.doc_type:type_name -> api.typesv2.deeplink_screen_option.nr_onboarding.InitiateUqudoSdkScreenOptions.DocType
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_init() }
func file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_init() {
	if File_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateUqudoSdkScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_depIdxs,
		EnumInfos:         file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_enumTypes,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto = out.File
	file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_nr_onboarding_initiate_uqudo_sdk_proto_depIdxs = nil
}
