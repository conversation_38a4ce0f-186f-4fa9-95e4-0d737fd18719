// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/ui/insights/secrets/secrets.proto

package secrets

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LegendAlignment int32

const (
	LegendAlignment_LEGEND_ALIGNMENT_UNSPECIFIED LegendAlignment = 0
	// enum value represent legend on chart to be shown on top
	LegendAlignment_LEGEND_ALIGNMENT_TOP LegendAlignment = 1
	// enum value represent legend on chart to be shown on bottom
	LegendAlignment_LEGEND_ALIGNMENT_BOTTOM LegendAlignment = 2
)

// Enum value maps for LegendAlignment.
var (
	LegendAlignment_name = map[int32]string{
		0: "LEGEND_ALIGNMENT_UNSPECIFIED",
		1: "LEGEND_ALIGNMENT_TOP",
		2: "LEGEND_ALIGNMENT_BOTTOM",
	}
	LegendAlignment_value = map[string]int32{
		"LEGEND_ALIGNMENT_UNSPECIFIED": 0,
		"LEGEND_ALIGNMENT_TOP":         1,
		"LEGEND_ALIGNMENT_BOTTOM":      2,
	}
)

func (x LegendAlignment) Enum() *LegendAlignment {
	p := new(LegendAlignment)
	*p = x
	return p
}

func (x LegendAlignment) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LegendAlignment) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_enumTypes[0].Descriptor()
}

func (LegendAlignment) Type() protoreflect.EnumType {
	return &file_api_typesv2_ui_insights_secrets_secrets_proto_enumTypes[0]
}

func (x LegendAlignment) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LegendAlignment.Descriptor instead.
func (LegendAlignment) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{0}
}

// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16276-15732&t=1gdU4DoP57yy8ZZj-4
// The smaller card shown in Home Widgets, Landing screens etc. Has only the summary of the secret without detailed 'visualisations'
// This can be configured using insights.secrets.config.SecretSummaryCardConfiguration,
// any changes done here should also include changes in insights.secrets.config.SecretSummaryCardConfiguration
type SecretSummaryCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 'Your total PF balance'
	// Client needs to add text wrapping support for this
	Title *ui.IconTextComponent `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// '₹65,999'
	Value *SecretSummaryCard_SecretValue `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// Bottom image/visualisation which can corresponds to proper visualisation corresponding to the secret
	Visualisation *common.VisualElement `protobuf:"bytes,3,opt,name=visualisation,proto3" json:"visualisation,omitempty"`
	// 'TAP TO REVEAL' or 'REFRESHES IN 5 DAYS'
	// tag to be shown at the bottom of the tag
	BottomTag   *ui.IconTextComponent `protobuf:"bytes,4,opt,name=bottom_tag,json=bottomTag,proto3" json:"bottom_tag,omitempty"`
	BgColor     string                `protobuf:"bytes,5,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	BorderColor string                `protobuf:"bytes,6,opt,name=border_color,json=borderColor,proto3" json:"border_color,omitempty"`
	// redirect to deeplink on tap on the card
	Deeplink *deeplink.Deeplink `protobuf:"bytes,7,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// this will be the name of the secret which this card represents
	// this will be needed to manage the view state of the card on client
	// the name will be the same as the name which will be used to fetch the secret when this card is tapped on
	SecretName string `protobuf:"bytes,8,opt,name=secret_name,json=secretName,proto3" json:"secret_name,omitempty"`
}

func (x *SecretSummaryCard) Reset() {
	*x = SecretSummaryCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecretSummaryCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretSummaryCard) ProtoMessage() {}

func (x *SecretSummaryCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretSummaryCard.ProtoReflect.Descriptor instead.
func (*SecretSummaryCard) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{0}
}

func (x *SecretSummaryCard) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *SecretSummaryCard) GetValue() *SecretSummaryCard_SecretValue {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *SecretSummaryCard) GetVisualisation() *common.VisualElement {
	if x != nil {
		return x.Visualisation
	}
	return nil
}

func (x *SecretSummaryCard) GetBottomTag() *ui.IconTextComponent {
	if x != nil {
		return x.BottomTag
	}
	return nil
}

func (x *SecretSummaryCard) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *SecretSummaryCard) GetBorderColor() string {
	if x != nil {
		return x.BorderColor
	}
	return ""
}

func (x *SecretSummaryCard) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *SecretSummaryCard) GetSecretName() string {
	if x != nil {
		return x.SecretName
	}
	return ""
}

// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16171-24622&t=Ha5X0yvR6oojxBKI-4
type NumberCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image *common.VisualElement `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	// Title goes here
	// '₹9999'
	TitleValuePair *ui.VerticalKeyValuePair `protobuf:"bytes,2,opt,name=title_value_pair,json=titleValuePair,proto3" json:"title_value_pair,omitempty"`
	BgColor        string                   `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *NumberCard) Reset() {
	*x = NumberCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NumberCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NumberCard) ProtoMessage() {}

func (x *NumberCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NumberCard.ProtoReflect.Descriptor instead.
func (*NumberCard) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{1}
}

func (x *NumberCard) GetImage() *common.VisualElement {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *NumberCard) GetTitleValuePair() *ui.VerticalKeyValuePair {
	if x != nil {
		return x.TitleValuePair
	}
	return nil
}

func (x *NumberCard) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16177-37087&t=EsQ3Ld0ZMnAleBrx-4
type ImageCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title *ui.IconTextComponent `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// The envelope
	BgImage *common.VisualElement `protobuf:"bytes,2,opt,name=bg_image,json=bgImage,proto3" json:"bg_image,omitempty"`
	// The letter with the values
	// The 'bg_image' and this component needs to be fixed in relation to each other
	// The width, height, positioning and margin between these would be fixed
	ValueComponent *ImageCard_ValueComponent `protobuf:"bytes,3,opt,name=value_component,json=valueComponent,proto3" json:"value_component,omitempty"`
	// bg_color for the card
	BgColor string `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *ImageCard) Reset() {
	*x = ImageCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageCard) ProtoMessage() {}

func (x *ImageCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageCard.ProtoReflect.Descriptor instead.
func (*ImageCard) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{2}
}

func (x *ImageCard) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *ImageCard) GetBgImage() *common.VisualElement {
	if x != nil {
		return x.BgImage
	}
	return nil
}

func (x *ImageCard) GetValueComponent() *ImageCard_ValueComponent {
	if x != nil {
		return x.ValueComponent
	}
	return nil
}

func (x *ImageCard) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16175-29868&t=8nGfgNz7tBRb8HFW-1
type BarChartCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 'Your top-performing Mutual Fund'
	// 'ICICI Pru Dividend fund'
	TitleValuePair *ui.VerticalKeyValuePair `protobuf:"bytes,1,opt,name=title_value_pair,json=titleValuePair,proto3" json:"title_value_pair,omitempty"`
	// '+24.5%'
	ScoreInsight *ui.IconTextComponent `protobuf:"bytes,2,opt,name=score_insight,json=scoreInsight,proto3" json:"score_insight,omitempty"`
	// [Deprecated] in favour of BarChart
	// Horizontal Bars that needs to be plotted
	//
	// Deprecated: Marked as deprecated in api/typesv2/ui/insights/secrets/secrets.proto.
	Bars []*Bar `protobuf:"bytes,3,rep,name=bars,proto3" json:"bars,omitempty"`
	// background color for the card
	BgColor string `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// [Deprecated] in favour of BarChart Default bar colour
	// color of the bars to be shown in bar chart
	// unless there is one sent in the bar itself
	//
	// Deprecated: Marked as deprecated in api/typesv2/ui/insights/secrets/secrets.proto.
	DefaultBarColor string `protobuf:"bytes,5,opt,name=default_bar_color,json=defaultBarColor,proto3" json:"default_bar_color,omitempty"`
	// this will be used to plot the bars with benchmark and the bars
	BarChart *BarChart `protobuf:"bytes,6,opt,name=bar_chart,json=barChart,proto3" json:"bar_chart,omitempty"`
}

func (x *BarChartCard) Reset() {
	*x = BarChartCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BarChartCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BarChartCard) ProtoMessage() {}

func (x *BarChartCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BarChartCard.ProtoReflect.Descriptor instead.
func (*BarChartCard) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{3}
}

func (x *BarChartCard) GetTitleValuePair() *ui.VerticalKeyValuePair {
	if x != nil {
		return x.TitleValuePair
	}
	return nil
}

func (x *BarChartCard) GetScoreInsight() *ui.IconTextComponent {
	if x != nil {
		return x.ScoreInsight
	}
	return nil
}

// Deprecated: Marked as deprecated in api/typesv2/ui/insights/secrets/secrets.proto.
func (x *BarChartCard) GetBars() []*Bar {
	if x != nil {
		return x.Bars
	}
	return nil
}

func (x *BarChartCard) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

// Deprecated: Marked as deprecated in api/typesv2/ui/insights/secrets/secrets.proto.
func (x *BarChartCard) GetDefaultBarColor() string {
	if x != nil {
		return x.DefaultBarColor
	}
	return ""
}

func (x *BarChartCard) GetBarChart() *BarChart {
	if x != nil {
		return x.BarChart
	}
	return nil
}

type BarChart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Horizontal Bars that needs to be plotted
	Bars []*Bar `protobuf:"bytes,1,rep,name=bars,proto3" json:"bars,omitempty"`
	// ReferenceLines parallel to horizontal axis
	// keeping it repeated to support multiple benchmarks if needed
	ReferenceLines []*ReferenceLine `protobuf:"bytes,2,rep,name=reference_lines,json=referenceLines,proto3" json:"reference_lines,omitempty"`
	// min and max value needed because benchmark can be above all the bar values
	// these will determine the range of bar chart (to plot bars in the range)
	// if not sent we will use maximum and minimum bar values for range
	MaxValue float64 `protobuf:"fixed64,3,opt,name=max_value,json=maxValue,proto3" json:"max_value,omitempty"`
	MinValue float64 `protobuf:"fixed64,4,opt,name=min_value,json=minValue,proto3" json:"min_value,omitempty"`
	// color of the bars to be shown in bar chart
	// unless there is one sent in the bar itself
	DefaultBarColor string `protobuf:"bytes,5,opt,name=default_bar_color,json=defaultBarColor,proto3" json:"default_bar_color,omitempty"`
	// Legend to be added for the bar chart
	Legend *Legend `protobuf:"bytes,6,opt,name=legend,proto3" json:"legend,omitempty"`
}

func (x *BarChart) Reset() {
	*x = BarChart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BarChart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BarChart) ProtoMessage() {}

func (x *BarChart) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BarChart.ProtoReflect.Descriptor instead.
func (*BarChart) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{4}
}

func (x *BarChart) GetBars() []*Bar {
	if x != nil {
		return x.Bars
	}
	return nil
}

func (x *BarChart) GetReferenceLines() []*ReferenceLine {
	if x != nil {
		return x.ReferenceLines
	}
	return nil
}

func (x *BarChart) GetMaxValue() float64 {
	if x != nil {
		return x.MaxValue
	}
	return 0
}

func (x *BarChart) GetMinValue() float64 {
	if x != nil {
		return x.MinValue
	}
	return 0
}

func (x *BarChart) GetDefaultBarColor() string {
	if x != nil {
		return x.DefaultBarColor
	}
	return ""
}

func (x *BarChart) GetLegend() *Legend {
	if x != nil {
		return x.Legend
	}
	return nil
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=539-12359&t=JL6vE1uL2qdEXM37-1
type ReferenceLine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Label *ui.IconTextComponent `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	// line needs to be shown for the divider
	LineColor string `protobuf:"bytes,2,opt,name=line_color,json=lineColor,proto3" json:"line_color,omitempty"`
	// This is the value lies between the min and max value of the bar chart to plot a reference line for the bars
	Value float64 `protobuf:"fixed64,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *ReferenceLine) Reset() {
	*x = ReferenceLine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReferenceLine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReferenceLine) ProtoMessage() {}

func (x *ReferenceLine) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReferenceLine.ProtoReflect.Descriptor instead.
func (*ReferenceLine) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{5}
}

func (x *ReferenceLine) GetLabel() *ui.IconTextComponent {
	if x != nil {
		return x.Label
	}
	return nil
}

func (x *ReferenceLine) GetLineColor() string {
	if x != nil {
		return x.LineColor
	}
	return ""
}

func (x *ReferenceLine) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/Crore-Club?node-id=355-28110&t=9PLMrfTzO3vjKyor-4
type GridVisualisationCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleValuePair *ui.VerticalKeyValuePair `protobuf:"bytes,1,opt,name=title_value_pair,json=titleValuePair,proto3" json:"title_value_pair,omitempty"`
	GridComponent  *GridComponent           `protobuf:"bytes,2,opt,name=grid_component,json=gridComponent,proto3" json:"grid_component,omitempty"`
}

func (x *GridVisualisationCard) Reset() {
	*x = GridVisualisationCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GridVisualisationCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GridVisualisationCard) ProtoMessage() {}

func (x *GridVisualisationCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GridVisualisationCard.ProtoReflect.Descriptor instead.
func (*GridVisualisationCard) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{6}
}

func (x *GridVisualisationCard) GetTitleValuePair() *ui.VerticalKeyValuePair {
	if x != nil {
		return x.TitleValuePair
	}
	return nil
}

func (x *GridVisualisationCard) GetGridComponent() *GridComponent {
	if x != nil {
		return x.GridComponent
	}
	return nil
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/Crore-Club?node-id=355-28140&t=RfHhHALM3bJeSbHF-4
type GridComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title of the component
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// background color of the component
	BgColor *widget.BackgroundColour `protobuf:"bytes,2,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// corner radius of the component
	CornerRadius int32 `protobuf:"varint,3,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
	// used for showing the tiles grid
	GridCard *ui.GridCard `protobuf:"bytes,4,opt,name=grid_card,json=gridCard,proto3" json:"grid_card,omitempty"`
}

func (x *GridComponent) Reset() {
	*x = GridComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GridComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GridComponent) ProtoMessage() {}

func (x *GridComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GridComponent.ProtoReflect.Descriptor instead.
func (*GridComponent) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{7}
}

func (x *GridComponent) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *GridComponent) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *GridComponent) GetCornerRadius() int32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

func (x *GridComponent) GetGridCard() *ui.GridCard {
	if x != nil {
		return x.GridCard
	}
	return nil
}

type Bar struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This is the value/image shown on top of bar
	// [Deprecated] since we wont be supporting the image on y-axis due to library restrictions on android.
	TopDisplayComponent *Bar_BarDisplayComponent `protobuf:"bytes,1,opt,name=top_display_component,json=topDisplayComponent,proto3" json:"top_display_component,omitempty"`
	// This is the value of the Bar which needs to be plotted on the Bar Chart
	Value float64 `protobuf:"fixed64,2,opt,name=value,proto3" json:"value,omitempty"`
	// This is the value/image shown on bottom of bar i.e x-axis
	XaxisDisplayComponent *Bar_BarDisplayComponent `protobuf:"bytes,3,opt,name=xaxis_display_component,json=xaxisDisplayComponent,proto3" json:"xaxis_display_component,omitempty"`
	// color of the bar to be shown in bar chart
	// should only be sent when need a different color else use the default_bar_color of bar chart
	BarColor string `protobuf:"bytes,4,opt,name=bar_color,json=barColor,proto3" json:"bar_color,omitempty"`
	// Deeplink which needs to be opened when tapped on a bar chart
	Deeplink *deeplink.Deeplink `protobuf:"bytes,5,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// Value (y-axis) show on top of bar to display the value of the bar i.e 12%, 12.5% etc
	YaxisDisplayValue *common.Text `protobuf:"bytes,6,opt,name=yaxis_display_value,json=yaxisDisplayValue,proto3" json:"yaxis_display_value,omitempty"`
}

func (x *Bar) Reset() {
	*x = Bar{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bar) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bar) ProtoMessage() {}

func (x *Bar) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bar.ProtoReflect.Descriptor instead.
func (*Bar) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{8}
}

func (x *Bar) GetTopDisplayComponent() *Bar_BarDisplayComponent {
	if x != nil {
		return x.TopDisplayComponent
	}
	return nil
}

func (x *Bar) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *Bar) GetXaxisDisplayComponent() *Bar_BarDisplayComponent {
	if x != nil {
		return x.XaxisDisplayComponent
	}
	return nil
}

func (x *Bar) GetBarColor() string {
	if x != nil {
		return x.BarColor
	}
	return ""
}

func (x *Bar) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *Bar) GetYaxisDisplayValue() *common.Text {
	if x != nil {
		return x.YaxisDisplayValue
	}
	return nil
}

// figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16171-24616&t=ZTOUVMjOyxeZ4LSI-1
type CardInfoComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 'Title goes here'
	// 'Top result'
	TitleValuePair *ui.VerticalKeyValuePair `protobuf:"bytes,1,opt,name=title_value_pair,json=titleValuePair,proto3" json:"title_value_pair,omitempty"`
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16171-24628&t=oa57kBmUiIJoXyzi-1
	Insight *ui.IconTextComponent `protobuf:"bytes,2,opt,name=insight,proto3" json:"insight,omitempty"`
}

func (x *CardInfoComponent) Reset() {
	*x = CardInfoComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardInfoComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardInfoComponent) ProtoMessage() {}

func (x *CardInfoComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardInfoComponent.ProtoReflect.Descriptor instead.
func (*CardInfoComponent) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{9}
}

func (x *CardInfoComponent) GetTitleValuePair() *ui.VerticalKeyValuePair {
	if x != nil {
		return x.TitleValuePair
	}
	return nil
}

func (x *CardInfoComponent) GetInsight() *ui.IconTextComponent {
	if x != nil {
		return x.Insight
	}
	return nil
}

// figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16177-37087&t=EsQ3Ld0ZMnAleBrx-4
type MultiLineChartCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// this is the component to show all the relevant information above the chart
	InfoComponent *CardInfoComponent `protobuf:"bytes,1,opt,name=info_component,json=infoComponent,proto3" json:"info_component,omitempty"`
	// multi line chart component
	Chart *MultiLineChart `protobuf:"bytes,2,opt,name=chart,proto3" json:"chart,omitempty"`
	// background color for the card
	BgColor string `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *MultiLineChartCard) Reset() {
	*x = MultiLineChartCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiLineChartCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiLineChartCard) ProtoMessage() {}

func (x *MultiLineChartCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiLineChartCard.ProtoReflect.Descriptor instead.
func (*MultiLineChartCard) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{10}
}

func (x *MultiLineChartCard) GetInfoComponent() *CardInfoComponent {
	if x != nil {
		return x.InfoComponent
	}
	return nil
}

func (x *MultiLineChartCard) GetChart() *MultiLineChart {
	if x != nil {
		return x.Chart
	}
	return nil
}

func (x *MultiLineChartCard) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

// figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16171-24616&t=ZTOUVMjOyxeZ4LSI-1
type AreaChartCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// this is the component to show all the relevant information above the chart
	InfoComponent *CardInfoComponent `protobuf:"bytes,1,opt,name=info_component,json=infoComponent,proto3" json:"info_component,omitempty"`
	// area chart component
	Chart *AreaChart `protobuf:"bytes,2,opt,name=chart,proto3" json:"chart,omitempty"`
	// background color for the card
	BgColor string `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *AreaChartCard) Reset() {
	*x = AreaChartCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AreaChartCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AreaChartCard) ProtoMessage() {}

func (x *AreaChartCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AreaChartCard.ProtoReflect.Descriptor instead.
func (*AreaChartCard) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{11}
}

func (x *AreaChartCard) GetInfoComponent() *CardInfoComponent {
	if x != nil {
		return x.InfoComponent
	}
	return nil
}

func (x *AreaChartCard) GetChart() *AreaChart {
	if x != nil {
		return x.Chart
	}
	return nil
}

func (x *AreaChartCard) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

type AreaChart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// this is the line to be shown on the area chart (only one line support for now)
	Line *AreaChartLine `protobuf:"bytes,1,opt,name=line,proto3" json:"line,omitempty"`
	// deprecated in favour of ChartDragIndicator
	//
	// Deprecated: Marked as deprecated in api/typesv2/ui/insights/secrets/secrets.proto.
	IndicatorColor string `protobuf:"bytes,2,opt,name=indicator_color,json=indicatorColor,proto3" json:"indicator_color,omitempty"`
	ShowSlider     bool   `protobuf:"varint,3,opt,name=show_slider,json=showSlider,proto3" json:"show_slider,omitempty"`
	// this represents the legends to be shown on top or bottom of the chart
	Legend *Legend `protobuf:"bytes,4,opt,name=legend,proto3" json:"legend,omitempty"`
	// this is to show the information regarding the point tapped or dragged on the chart
	DragIndicator *ChartDragIndicator `protobuf:"bytes,5,opt,name=drag_indicator,json=dragIndicator,proto3" json:"drag_indicator,omitempty"`
	// minY and maxY to specify the range of Y-Axis
	// we need this because we want to give flexibility to adjust the chart plotting
	// minY and maxY from the points would have worked because we might want to some space on top and bottom of the chart
	MinY float64 `protobuf:"fixed64,6,opt,name=minY,proto3" json:"minY,omitempty"`
	MaxY float64 `protobuf:"fixed64,7,opt,name=maxY,proto3" json:"maxY,omitempty"`
	// figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=20535-38771&t=cGjxjhKQdLP6aUc8-1
	// labels to be shown on horizontal axis
	Axis *HorizontalAxis `protobuf:"bytes,8,opt,name=axis,proto3" json:"axis,omitempty"`
}

func (x *AreaChart) Reset() {
	*x = AreaChart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AreaChart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AreaChart) ProtoMessage() {}

func (x *AreaChart) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AreaChart.ProtoReflect.Descriptor instead.
func (*AreaChart) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{12}
}

func (x *AreaChart) GetLine() *AreaChartLine {
	if x != nil {
		return x.Line
	}
	return nil
}

// Deprecated: Marked as deprecated in api/typesv2/ui/insights/secrets/secrets.proto.
func (x *AreaChart) GetIndicatorColor() string {
	if x != nil {
		return x.IndicatorColor
	}
	return ""
}

func (x *AreaChart) GetShowSlider() bool {
	if x != nil {
		return x.ShowSlider
	}
	return false
}

func (x *AreaChart) GetLegend() *Legend {
	if x != nil {
		return x.Legend
	}
	return nil
}

func (x *AreaChart) GetDragIndicator() *ChartDragIndicator {
	if x != nil {
		return x.DragIndicator
	}
	return nil
}

func (x *AreaChart) GetMinY() float64 {
	if x != nil {
		return x.MinY
	}
	return 0
}

func (x *AreaChart) GetMaxY() float64 {
	if x != nil {
		return x.MaxY
	}
	return 0
}

func (x *AreaChart) GetAxis() *HorizontalAxis {
	if x != nil {
		return x.Axis
	}
	return nil
}

// figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16177-37085&t=ZTOUVMjOyxeZ4LSI-1
type MultiLineChart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lines that needs to be plotted on the chart
	Lines []*Line `protobuf:"bytes,1,rep,name=lines,proto3" json:"lines,omitempty"`
	// figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16177-37085&t=ZTOUVMjOyxeZ4LSI-1
	// labels to be shown on horizontal axis
	Axis *HorizontalAxis `protobuf:"bytes,2,opt,name=axis,proto3" json:"axis,omitempty"`
	// minY and maxY to specify the range of Y-Axis
	// we need this because we want to give flexibility to adjust the chart plotting
	// minY and maxY from the points would have worked because we might want to some space on top and bottom of the chart
	MinY float64 `protobuf:"fixed64,3,opt,name=minY,proto3" json:"minY,omitempty"`
	MaxY float64 `protobuf:"fixed64,4,opt,name=maxY,proto3" json:"maxY,omitempty"`
	// this represents the legends to be shown on top or bottom of the chart
	Legend *Legend `protobuf:"bytes,5,opt,name=legend,proto3" json:"legend,omitempty"`
	// this is to show the information regarding the point tapped or dragged on the chart
	DragIndicator *ChartDragIndicator `protobuf:"bytes,6,opt,name=drag_indicator,json=dragIndicator,proto3" json:"drag_indicator,omitempty"`
	// Bool to tell client should we show the drag feature or not
	ShowSlider bool `protobuf:"varint,7,opt,name=show_slider,json=showSlider,proto3" json:"show_slider,omitempty"`
}

func (x *MultiLineChart) Reset() {
	*x = MultiLineChart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiLineChart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiLineChart) ProtoMessage() {}

func (x *MultiLineChart) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiLineChart.ProtoReflect.Descriptor instead.
func (*MultiLineChart) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{13}
}

func (x *MultiLineChart) GetLines() []*Line {
	if x != nil {
		return x.Lines
	}
	return nil
}

func (x *MultiLineChart) GetAxis() *HorizontalAxis {
	if x != nil {
		return x.Axis
	}
	return nil
}

func (x *MultiLineChart) GetMinY() float64 {
	if x != nil {
		return x.MinY
	}
	return 0
}

func (x *MultiLineChart) GetMaxY() float64 {
	if x != nil {
		return x.MaxY
	}
	return 0
}

func (x *MultiLineChart) GetLegend() *Legend {
	if x != nil {
		return x.Legend
	}
	return nil
}

func (x *MultiLineChart) GetDragIndicator() *ChartDragIndicator {
	if x != nil {
		return x.DragIndicator
	}
	return nil
}

func (x *MultiLineChart) GetShowSlider() bool {
	if x != nil {
		return x.ShowSlider
	}
	return false
}

// This represents the Area line that will be plotted on the chart for given points with area filled under the line
type AreaChartLine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This is the line that needs to shown on the chart
	Line *Line `protobuf:"bytes,1,opt,name=line,proto3" json:"line,omitempty"`
	// colour that needs to be filled under the line
	FillColor *widget.BackgroundColour `protobuf:"bytes,2,opt,name=fill_color,json=fillColor,proto3" json:"fill_color,omitempty"`
}

func (x *AreaChartLine) Reset() {
	*x = AreaChartLine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AreaChartLine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AreaChartLine) ProtoMessage() {}

func (x *AreaChartLine) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AreaChartLine.ProtoReflect.Descriptor instead.
func (*AreaChartLine) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{14}
}

func (x *AreaChartLine) GetLine() *Line {
	if x != nil {
		return x.Line
	}
	return nil
}

func (x *AreaChartLine) GetFillColor() *widget.BackgroundColour {
	if x != nil {
		return x.FillColor
	}
	return nil
}

// This represents the line that will be plotted on the chart for given points
type Line struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// points to plot on the chart
	Points []*Point `protobuf:"bytes,1,rep,name=points,proto3" json:"points,omitempty"`
	// colour of the line on the chart
	Color string `protobuf:"bytes,2,opt,name=color,proto3" json:"color,omitempty"`
	// width of the line on the chart
	Width int32 `protobuf:"varint,3,opt,name=width,proto3" json:"width,omitempty"`
}

func (x *Line) Reset() {
	*x = Line{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Line) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Line) ProtoMessage() {}

func (x *Line) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Line.ProtoReflect.Descriptor instead.
func (*Line) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{15}
}

func (x *Line) GetPoints() []*Point {
	if x != nil {
		return x.Points
	}
	return nil
}

func (x *Line) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *Line) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

// A point on the chart with given x and y axis value
type Point struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	XValue float64 `protobuf:"fixed64,1,opt,name=x_value,json=xValue,proto3" json:"x_value,omitempty"`
	YValue float64 `protobuf:"fixed64,2,opt,name=y_value,json=yValue,proto3" json:"y_value,omitempty"`
}

func (x *Point) Reset() {
	*x = Point{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Point) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Point) ProtoMessage() {}

func (x *Point) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Point.ProtoReflect.Descriptor instead.
func (*Point) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{16}
}

func (x *Point) GetXValue() float64 {
	if x != nil {
		return x.XValue
	}
	return 0
}

func (x *Point) GetYValue() float64 {
	if x != nil {
		return x.YValue
	}
	return 0
}

// This represents the horizontal axis needs to shown under the chart
type HorizontalAxis struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// array of labels to be shown on horizontal axis
	HorizontalAxis []*HorizontalAxisPoint `protobuf:"bytes,1,rep,name=horizontal_axis,json=horizontalAxis,proto3" json:"horizontal_axis,omitempty"`
	// Color of the Axis Line
	Color string `protobuf:"bytes,2,opt,name=color,proto3" json:"color,omitempty"`
}

func (x *HorizontalAxis) Reset() {
	*x = HorizontalAxis{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HorizontalAxis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HorizontalAxis) ProtoMessage() {}

func (x *HorizontalAxis) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HorizontalAxis.ProtoReflect.Descriptor instead.
func (*HorizontalAxis) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{17}
}

func (x *HorizontalAxis) GetHorizontalAxis() []*HorizontalAxisPoint {
	if x != nil {
		return x.HorizontalAxis
	}
	return nil
}

func (x *HorizontalAxis) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

// This represents the horizontal axis point needs to shown under the chart
type HorizontalAxisPoint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// represents the x-axis point on the axis where to show the label
	// this is define the position on the axis where the label has to be placed
	Value float64 `protobuf:"fixed64,1,opt,name=value,proto3" json:"value,omitempty"`
	// this is the label needs to be shown on the axis
	// e.g 2020, 20021, 2022, 2023, 2024
	Label *common.Text `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
}

func (x *HorizontalAxisPoint) Reset() {
	*x = HorizontalAxisPoint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HorizontalAxisPoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HorizontalAxisPoint) ProtoMessage() {}

func (x *HorizontalAxisPoint) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HorizontalAxisPoint.ProtoReflect.Descriptor instead.
func (*HorizontalAxisPoint) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{18}
}

func (x *HorizontalAxisPoint) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *HorizontalAxisPoint) GetLabel() *common.Text {
	if x != nil {
		return x.Label
	}
	return nil
}

// figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18496-13889&t=amVv7QVyZrkwkV64-1
// Component to show information about a particular point on chart e.g shown in figma
type ChartDragIndicator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// all the indicators for the chart
	Indicators []*ChartDragIndicator_Indicator `protobuf:"bytes,1,rep,name=indicators,proto3" json:"indicators,omitempty"`
	// colour of the line shown on the graph to locate a point info
	LocatorLineColour string `protobuf:"bytes,2,opt,name=locator_line_colour,json=locatorLineColour,proto3" json:"locator_line_colour,omitempty"`
}

func (x *ChartDragIndicator) Reset() {
	*x = ChartDragIndicator{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChartDragIndicator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChartDragIndicator) ProtoMessage() {}

func (x *ChartDragIndicator) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChartDragIndicator.ProtoReflect.Descriptor instead.
func (*ChartDragIndicator) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{19}
}

func (x *ChartDragIndicator) GetIndicators() []*ChartDragIndicator_Indicator {
	if x != nil {
		return x.Indicators
	}
	return nil
}

func (x *ChartDragIndicator) GetLocatorLineColour() string {
	if x != nil {
		return x.LocatorLineColour
	}
	return ""
}

// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18363-11178&t=oa57kBmUiIJoXyzi-1
type DonutCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InfoComponent *CardInfoComponent `protobuf:"bytes,1,opt,name=info_component,json=infoComponent,proto3" json:"info_component,omitempty"`
	// this represents the different slices on the donut chart
	Slices []*DonutSlice `protobuf:"bytes,2,rep,name=slices,proto3" json:"slices,omitempty"`
	// send this nil if we dont want highlight functionality
	// highlighted_index sent here will be shown highlighted on UI with wider slice than all other
	HighlightedIndex *wrapperspb.Int32Value `protobuf:"bytes,3,opt,name=highlighted_index,json=highlightedIndex,proto3" json:"highlighted_index,omitempty"`
	// background color for the card
	BgColor string `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// this represents the legends to be shown on top or bottom of the chart
	Legend *Legend `protobuf:"bytes,5,opt,name=legend,proto3" json:"legend,omitempty"`
}

func (x *DonutCard) Reset() {
	*x = DonutCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DonutCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DonutCard) ProtoMessage() {}

func (x *DonutCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DonutCard.ProtoReflect.Descriptor instead.
func (*DonutCard) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{20}
}

func (x *DonutCard) GetInfoComponent() *CardInfoComponent {
	if x != nil {
		return x.InfoComponent
	}
	return nil
}

func (x *DonutCard) GetSlices() []*DonutSlice {
	if x != nil {
		return x.Slices
	}
	return nil
}

func (x *DonutCard) GetHighlightedIndex() *wrapperspb.Int32Value {
	if x != nil {
		return x.HighlightedIndex
	}
	return nil
}

func (x *DonutCard) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *DonutCard) GetLegend() *Legend {
	if x != nil {
		return x.Legend
	}
	return nil
}

type DonutSlice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Percentage representing the slice on donut chart
	Percent float64 `protobuf:"fixed64,1,opt,name=percent,proto3" json:"percent,omitempty"`
	// Colour of a particular pie slice
	Color string `protobuf:"bytes,2,opt,name=color,proto3" json:"color,omitempty"`
	// this is the content to be shown on tooltip when tapped on the slice
	// the arrow shown we will do it on client side
	Tooltip *ui.Tooltip `protobuf:"bytes,3,opt,name=tooltip,proto3" json:"tooltip,omitempty"`
}

func (x *DonutSlice) Reset() {
	*x = DonutSlice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DonutSlice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DonutSlice) ProtoMessage() {}

func (x *DonutSlice) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DonutSlice.ProtoReflect.Descriptor instead.
func (*DonutSlice) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{21}
}

func (x *DonutSlice) GetPercent() float64 {
	if x != nil {
		return x.Percent
	}
	return 0
}

func (x *DonutSlice) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *DonutSlice) GetTooltip() *ui.Tooltip {
	if x != nil {
		return x.Tooltip
	}
	return nil
}

// represents the legend which shows the user which line or which colour pointing to what information
type Legend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// representing all the labels needs to be shown on legend
	// [Deprecated] Use the LegendView now to support multiple types
	//
	// Deprecated: Marked as deprecated in api/typesv2/ui/insights/secrets/secrets.proto.
	Labels []*ui.IconTextComponent `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty"`
	// Alignment of the legend View
	// Supported values are top and bottom right now, if unspecified client will treat it as bottom
	Alignment LegendAlignment `protobuf:"varint,2,opt,name=alignment,proto3,enum=api.typesv2.ui.insights.secrets.LegendAlignment" json:"alignment,omitempty"`
	Legends   []*LegendView   `protobuf:"bytes,3,rep,name=legends,proto3" json:"legends,omitempty"`
}

func (x *Legend) Reset() {
	*x = Legend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Legend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Legend) ProtoMessage() {}

func (x *Legend) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Legend.ProtoReflect.Descriptor instead.
func (*Legend) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{22}
}

// Deprecated: Marked as deprecated in api/typesv2/ui/insights/secrets/secrets.proto.
func (x *Legend) GetLabels() []*ui.IconTextComponent {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Legend) GetAlignment() LegendAlignment {
	if x != nil {
		return x.Alignment
	}
	return LegendAlignment_LEGEND_ALIGNMENT_UNSPECIFIED
}

func (x *Legend) GetLegends() []*LegendView {
	if x != nil {
		return x.Legends
	}
	return nil
}

type LegendView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to LegendType:
	//	*LegendView_Line
	//	*LegendView_Label
	LegendType isLegendView_LegendType `protobuf_oneof:"LegendType"`
}

func (x *LegendView) Reset() {
	*x = LegendView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LegendView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LegendView) ProtoMessage() {}

func (x *LegendView) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LegendView.ProtoReflect.Descriptor instead.
func (*LegendView) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{23}
}

func (m *LegendView) GetLegendType() isLegendView_LegendType {
	if m != nil {
		return m.LegendType
	}
	return nil
}

func (x *LegendView) GetLine() *LineLegendView {
	if x, ok := x.GetLegendType().(*LegendView_Line); ok {
		return x.Line
	}
	return nil
}

func (x *LegendView) GetLabel() *ui.IconTextComponent {
	if x, ok := x.GetLegendType().(*LegendView_Label); ok {
		return x.Label
	}
	return nil
}

type isLegendView_LegendType interface {
	isLegendView_LegendType()
}

type LegendView_Line struct {
	// This is the line type legend
	Line *LineLegendView `protobuf:"bytes,1,opt,name=line,proto3,oneof"`
}

type LegendView_Label struct {
	// This is legend where we show a circular indicator with a label
	// figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16177-35039&node-type=frame&t=ctmmwgGhVqQQop1Y-0
	// Specific View Link: https://drive.google.com/file/d/1G6aiyVrsRh_2SsAOhwuSd_rOU_jn1Y_a/view?usp=drive_link
	Label *ui.IconTextComponent `protobuf:"bytes,2,opt,name=label,proto3,oneof"`
}

func (*LegendView_Line) isLegendView_LegendType() {}

func (*LegendView_Label) isLegendView_LegendType() {}

// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=3339-39354&node-type=frame&t=zlxDZY1i00HniTZn-0
// Specific View Link: https://drive.google.com/file/d/1YqCYF3I90dtJY3jP2mwG2UKZT55fL1Od/view?usp=drive_link
type LineLegendView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This is the type of line we want to show with the Legend
	Line *ui.Line `protobuf:"bytes,1,opt,name=line,proto3" json:"line,omitempty"`
	// This is the label shown in the legend
	Label *ui.IconTextComponent `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
}

func (x *LineLegendView) Reset() {
	*x = LineLegendView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LineLegendView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LineLegendView) ProtoMessage() {}

func (x *LineLegendView) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LineLegendView.ProtoReflect.Descriptor instead.
func (*LineLegendView) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{24}
}

func (x *LineLegendView) GetLine() *ui.Line {
	if x != nil {
		return x.Line
	}
	return nil
}

func (x *LineLegendView) GetLabel() *ui.IconTextComponent {
	if x != nil {
		return x.Label
	}
	return nil
}

type SecretSummaryCard_SecretValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// '₹65,999'
	// Client needs to handle container and text wrapping
	Value *ui.IconTextComponent `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	// if value is present we would 'blur' out the value
	// else we would blur out 'N/A' txt
	IsHidden bool `protobuf:"varint,2,opt,name=is_hidden,json=isHidden,proto3" json:"is_hidden,omitempty"`
	// if true, the secret value will be locked
	// client does not need to handle this yet
	IsLocked bool `protobuf:"varint,3,opt,name=is_locked,json=isLocked,proto3" json:"is_locked,omitempty"`
}

func (x *SecretSummaryCard_SecretValue) Reset() {
	*x = SecretSummaryCard_SecretValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecretSummaryCard_SecretValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretSummaryCard_SecretValue) ProtoMessage() {}

func (x *SecretSummaryCard_SecretValue) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretSummaryCard_SecretValue.ProtoReflect.Descriptor instead.
func (*SecretSummaryCard_SecretValue) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{0, 0}
}

func (x *SecretSummaryCard_SecretValue) GetValue() *ui.IconTextComponent {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *SecretSummaryCard_SecretValue) GetIsHidden() bool {
	if x != nil {
		return x.IsHidden
	}
	return false
}

func (x *SecretSummaryCard_SecretValue) GetIsLocked() bool {
	if x != nil {
		return x.IsLocked
	}
	return false
}

type ImageCard_ValueComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image          *common.VisualElement    `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	TitleValuePair *ui.VerticalKeyValuePair `protobuf:"bytes,2,opt,name=title_value_pair,json=titleValuePair,proto3" json:"title_value_pair,omitempty"`
}

func (x *ImageCard_ValueComponent) Reset() {
	*x = ImageCard_ValueComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageCard_ValueComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageCard_ValueComponent) ProtoMessage() {}

func (x *ImageCard_ValueComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageCard_ValueComponent.ProtoReflect.Descriptor instead.
func (*ImageCard_ValueComponent) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ImageCard_ValueComponent) GetImage() *common.VisualElement {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *ImageCard_ValueComponent) GetTitleValuePair() *ui.VerticalKeyValuePair {
	if x != nil {
		return x.TitleValuePair
	}
	return nil
}

type Bar_BarDisplayComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Value:
	//	*Bar_BarDisplayComponent_Image
	//	*Bar_BarDisplayComponent_DisplayText
	Value isBar_BarDisplayComponent_Value `protobuf_oneof:"Value"`
}

func (x *Bar_BarDisplayComponent) Reset() {
	*x = Bar_BarDisplayComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bar_BarDisplayComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bar_BarDisplayComponent) ProtoMessage() {}

func (x *Bar_BarDisplayComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bar_BarDisplayComponent.ProtoReflect.Descriptor instead.
func (*Bar_BarDisplayComponent) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{8, 0}
}

func (m *Bar_BarDisplayComponent) GetValue() isBar_BarDisplayComponent_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *Bar_BarDisplayComponent) GetImage() *common.VisualElement {
	if x, ok := x.GetValue().(*Bar_BarDisplayComponent_Image); ok {
		return x.Image
	}
	return nil
}

func (x *Bar_BarDisplayComponent) GetDisplayText() *ui.IconTextComponent {
	if x, ok := x.GetValue().(*Bar_BarDisplayComponent_DisplayText); ok {
		return x.DisplayText
	}
	return nil
}

type isBar_BarDisplayComponent_Value interface {
	isBar_BarDisplayComponent_Value()
}

type Bar_BarDisplayComponent_Image struct {
	// Image width and height will be calculated on frontend with available space
	// Aspect ratio will be adjusted as per designs
	// Designs: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16328-14794&t=JCmT4FjCKl8Q1Rg9-1
	Image *common.VisualElement `protobuf:"bytes,1,opt,name=image,proto3,oneof"`
}

type Bar_BarDisplayComponent_DisplayText struct {
	DisplayText *ui.IconTextComponent `protobuf:"bytes,2,opt,name=display_text,json=displayText,proto3,oneof"`
}

func (*Bar_BarDisplayComponent_Image) isBar_BarDisplayComponent_Value() {}

func (*Bar_BarDisplayComponent_DisplayText) isBar_BarDisplayComponent_Value() {}

type ChartDragIndicator_Indicator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// x-axis value which will be used to show the locator on the basis of drag
	Value float64 `protobuf:"fixed64,1,opt,name=value,proto3" json:"value,omitempty"`
	// label shown for the locator on graph
	Label *ui.IconTextComponent `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
}

func (x *ChartDragIndicator_Indicator) Reset() {
	*x = ChartDragIndicator_Indicator{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChartDragIndicator_Indicator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChartDragIndicator_Indicator) ProtoMessage() {}

func (x *ChartDragIndicator_Indicator) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChartDragIndicator_Indicator.ProtoReflect.Descriptor instead.
func (*ChartDragIndicator_Indicator) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP(), []int{19, 0}
}

func (x *ChartDragIndicator_Indicator) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *ChartDragIndicator_Indicator) GetLabel() *ui.IconTextComponent {
	if x != nil {
		return x.Label
	}
	return nil
}

var File_api_typesv2_ui_insights_secrets_secrets_proto protoreflect.FileDescriptor

var file_api_typesv2_ui_insights_secrets_secrets_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69,
	0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1f, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69,
	0x2f, 0x67, 0x72, 0x69, 0x64, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69,
	0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x74, 0x6f, 0x6f, 0x6c, 0x74, 0x69, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x75, 0x69, 0x2f, 0x76, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x6b, 0x65, 0x79,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xc8, 0x04, 0x0a, 0x11, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x43, 0x61, 0x72, 0x64, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x54, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x43,
	0x61, 0x72, 0x64, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x47, 0x0a, 0x0d, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x0d, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x40, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x54, 0x61,
	0x67, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x21, 0x0a, 0x0c,
	0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x80, 0x01, 0x0a, 0x0b, 0x53, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x68, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x12,
	0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x22, 0xb0, 0x01, 0x0a,
	0x0a, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x43, 0x61, 0x72, 0x64, 0x12, 0x37, 0x0a, 0x05, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x12, 0x4e, 0x0a, 0x10, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x50, 0x61, 0x69, 0x72, 0x52, 0x0e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x50, 0x61, 0x69, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22,
	0x9d, 0x03, 0x0a, 0x09, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x61, 0x72, 0x64, 0x12, 0x37, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63,
	0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x62, 0x67, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x12, 0x62, 0x0a, 0x0f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0e, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f,
	0x6c, 0x6f, 0x72, 0x1a, 0x99, 0x01, 0x0a, 0x0e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x4e, 0x0a, 0x10, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70,
	0x61, 0x69, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69,
	0x63, 0x61, 0x6c, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x52,
	0x0e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x22,
	0xf7, 0x02, 0x0a, 0x0c, 0x42, 0x61, 0x72, 0x43, 0x68, 0x61, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x12, 0x4e, 0x0a, 0x10, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f,
	0x70, 0x61, 0x69, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74,
	0x69, 0x63, 0x61, 0x6c, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72,
	0x52, 0x0e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72,
	0x12, 0x46, 0x0a, 0x0d, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x12, 0x3c, 0x0a, 0x04, 0x62, 0x61, 0x72, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x42, 0x61, 0x72, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x04, 0x62, 0x61, 0x72, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x2e, 0x0a, 0x11, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x61, 0x72,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x42, 0x61, 0x72, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x46, 0x0a, 0x09, 0x62, 0x61, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x42, 0x61, 0x72, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52,
	0x08, 0x62, 0x61, 0x72, 0x43, 0x68, 0x61, 0x72, 0x74, 0x22, 0xc4, 0x02, 0x0a, 0x08, 0x42, 0x61,
	0x72, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x38, 0x0a, 0x04, 0x62, 0x61, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x42, 0x61, 0x72, 0x52, 0x04, 0x62, 0x61, 0x72, 0x73,
	0x12, 0x57, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6d, 0x61,
	0x78, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62,
	0x61, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x42, 0x61, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x3f, 0x0a, 0x06, 0x6c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x2e, 0x4c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x52, 0x06, 0x6c, 0x65, 0x67, 0x65, 0x6e, 0x64,
	0x22, 0x7d, 0x0a, 0x0d, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4c, 0x69, 0x6e,
	0x65, 0x12, 0x37, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0xbe, 0x01, 0x0a, 0x15, 0x47, 0x72, 0x69, 0x64, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x12, 0x4e, 0x0a, 0x10, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4b, 0x65, 0x79,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x52, 0x0e, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x12, 0x55, 0x0a, 0x0e, 0x67, 0x72, 0x69,
	0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2e, 0x47, 0x72, 0x69, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x52, 0x0d, 0x67, 0x72, 0x69, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x22, 0xe6, 0x01, 0x0a, 0x0d, 0x47, 0x72, 0x69, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52, 0x61, 0x64, 0x69,
	0x75, 0x73, 0x12, 0x35, 0x0a, 0x09, 0x67, 0x72, 0x69, 0x64, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x47, 0x72, 0x69, 0x64, 0x43, 0x61, 0x72, 0x64, 0x52,
	0x08, 0x67, 0x72, 0x69, 0x64, 0x43, 0x61, 0x72, 0x64, 0x22, 0xbf, 0x04, 0x0a, 0x03, 0x42, 0x61,
	0x72, 0x12, 0x6c, 0x0a, 0x15, 0x74, 0x6f, 0x70, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x73, 0x2e, 0x42, 0x61, 0x72, 0x2e, 0x42, 0x61, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x13, 0x74, 0x6f, 0x70, 0x44,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x70, 0x0a, 0x17, 0x78, 0x61, 0x78, 0x69, 0x73, 0x5f, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x42, 0x61, 0x72, 0x2e, 0x42, 0x61, 0x72,
	0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x15, 0x78, 0x61, 0x78, 0x69, 0x73, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x72, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x72, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x48, 0x0a,
	0x13, 0x79, 0x61, 0x78, 0x69, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x11, 0x79, 0x61, 0x78, 0x69, 0x73, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0xa1, 0x01, 0x0a, 0x13, 0x42, 0x61, 0x72, 0x44,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12,
	0x39, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x48, 0x00, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x46, 0x0a, 0x0c, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x65,
	0x78, 0x74, 0x42, 0x07, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xa0, 0x01, 0x0a, 0x11,
	0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x12, 0x4e, 0x0a, 0x10, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x5f, 0x70, 0x61, 0x69, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x56, 0x65, 0x72,
	0x74, 0x69, 0x63, 0x61, 0x6c, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69,
	0x72, 0x52, 0x0e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69,
	0x72, 0x12, 0x3b, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x22, 0xd1,
	0x01, 0x0a, 0x12, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x4c, 0x69, 0x6e, 0x65, 0x43, 0x68, 0x61, 0x72,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x12, 0x59, 0x0a, 0x0e, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e,
	0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x52, 0x0d, 0x69, 0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x12, 0x45, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x4c, 0x69, 0x6e, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74,
	0x52, 0x05, 0x63, 0x68, 0x61, 0x72, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x22, 0xc7, 0x01, 0x0a, 0x0d, 0x41, 0x72, 0x65, 0x61, 0x43, 0x68, 0x61, 0x72, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x12, 0x59, 0x0a, 0x0e, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x43,
	0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x0d, 0x69, 0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12,
	0x40, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x2e, 0x41, 0x72, 0x65, 0x61, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x05, 0x63, 0x68, 0x61, 0x72,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xa7, 0x03, 0x0a,
	0x09, 0x41, 0x72, 0x65, 0x61, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x42, 0x0a, 0x04, 0x6c, 0x69,
	0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x41, 0x72, 0x65, 0x61, 0x43,
	0x68, 0x61, 0x72, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x2b,
	0x0a, 0x0f, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x69, 0x6e, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x68, 0x6f, 0x77, 0x5f, 0x73, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x73, 0x68, 0x6f, 0x77, 0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x06,
	0x6c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x4c,
	0x65, 0x67, 0x65, 0x6e, 0x64, 0x52, 0x06, 0x6c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x12, 0x5a, 0x0a,
	0x0e, 0x64, 0x72, 0x61, 0x67, 0x5f, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x44, 0x72, 0x61,
	0x67, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x0d, 0x64, 0x72, 0x61, 0x67,
	0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x69, 0x6e,
	0x59, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x6d, 0x69, 0x6e, 0x59, 0x12, 0x12, 0x0a,
	0x04, 0x6d, 0x61, 0x78, 0x59, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x6d, 0x61, 0x78,
	0x59, 0x12, 0x43, 0x0a, 0x04, 0x61, 0x78, 0x69, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x2e, 0x48, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x41, 0x78, 0x69, 0x73,
	0x52, 0x04, 0x61, 0x78, 0x69, 0x73, 0x22, 0xf8, 0x02, 0x0a, 0x0e, 0x4d, 0x75, 0x6c, 0x74, 0x69,
	0x4c, 0x69, 0x6e, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x3b, 0x0a, 0x05, 0x6c, 0x69, 0x6e,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x4c, 0x69, 0x6e, 0x65, 0x52,
	0x05, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x43, 0x0a, 0x04, 0x61, 0x78, 0x69, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x48, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61,
	0x6c, 0x41, 0x78, 0x69, 0x73, 0x52, 0x04, 0x61, 0x78, 0x69, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6d,
	0x69, 0x6e, 0x59, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x6d, 0x69, 0x6e, 0x59, 0x12,
	0x12, 0x0a, 0x04, 0x6d, 0x61, 0x78, 0x59, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x6d,
	0x61, 0x78, 0x59, 0x12, 0x3f, 0x0a, 0x06, 0x6c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x4c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x52, 0x06, 0x6c, 0x65,
	0x67, 0x65, 0x6e, 0x64, 0x12, 0x5a, 0x0a, 0x0e, 0x64, 0x72, 0x61, 0x67, 0x5f, 0x69, 0x6e, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x43,
	0x68, 0x61, 0x72, 0x74, 0x44, 0x72, 0x61, 0x67, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f,
	0x72, 0x52, 0x0d, 0x64, 0x72, 0x61, 0x67, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72,
	0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x73, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x73, 0x68, 0x6f, 0x77, 0x53, 0x6c, 0x69, 0x64, 0x65,
	0x72, 0x22, 0x99, 0x01, 0x0a, 0x0d, 0x41, 0x72, 0x65, 0x61, 0x43, 0x68, 0x61, 0x72, 0x74, 0x4c,
	0x69, 0x6e, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2e, 0x4c, 0x69, 0x6e, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x4d,
	0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x72, 0x0a,
	0x04, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x3e, 0x0a, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x06, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74,
	0x68, 0x22, 0x39, 0x0a, 0x05, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x78, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x78, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x85, 0x01, 0x0a,
	0x0e, 0x48, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x41, 0x78, 0x69, 0x73, 0x12,
	0x5d, 0x0a, 0x0f, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x78,
	0x69, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x48, 0x6f, 0x72, 0x69, 0x7a,
	0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x41, 0x78, 0x69, 0x73, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x0e,
	0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x41, 0x78, 0x69, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x5b, 0x0a, 0x13, 0x48, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74,
	0x61, 0x6c, 0x41, 0x78, 0x69, 0x73, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x2e, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x22, 0xff, 0x01, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x72, 0x74, 0x44, 0x72, 0x61, 0x67, 0x49,
	0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x5d, 0x0a, 0x0a, 0x69, 0x6e, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x43,
	0x68, 0x61, 0x72, 0x74, 0x44, 0x72, 0x61, 0x67, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f,
	0x72, 0x2e, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x0a, 0x69, 0x6e, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x4c, 0x69, 0x6e,
	0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x1a, 0x5a, 0x0a, 0x09, 0x49, 0x6e, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x22, 0xd1, 0x02, 0x0a, 0x09, 0x44, 0x6f, 0x6e, 0x75, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x12, 0x59, 0x0a, 0x0e, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x69,
	0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x06,
	0x73, 0x6c, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x44,
	0x6f, 0x6e, 0x75, 0x74, 0x53, 0x6c, 0x69, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6c, 0x69, 0x63, 0x65,
	0x73, 0x12, 0x48, 0x0a, 0x11, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64,
	0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10, 0x68, 0x69, 0x67, 0x68, 0x6c,
	0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x62,
	0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62,
	0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x3f, 0x0a, 0x06, 0x6c, 0x65, 0x67, 0x65, 0x6e, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x4c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x52,
	0x06, 0x6c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x22, 0x6f, 0x0a, 0x0a, 0x44, 0x6f, 0x6e, 0x75, 0x74,
	0x53, 0x6c, 0x69, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x31, 0x0a, 0x07, 0x74, 0x6f, 0x6f, 0x6c, 0x74, 0x69, 0x70,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x74, 0x69, 0x70, 0x52,
	0x07, 0x74, 0x6f, 0x6f, 0x6c, 0x74, 0x69, 0x70, 0x22, 0xde, 0x01, 0x0a, 0x06, 0x4c, 0x65, 0x67,
	0x65, 0x6e, 0x64, 0x12, 0x3d, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x12, 0x4e, 0x0a, 0x09, 0x61, 0x6c, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x4c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x41, 0x6c,
	0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x61, 0x6c, 0x69, 0x67, 0x6e, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x45, 0x0a, 0x07, 0x6c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x4c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x07, 0x6c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x73, 0x22, 0x9c, 0x01, 0x0a, 0x0a, 0x4c, 0x65,
	0x67, 0x65, 0x6e, 0x64, 0x56, 0x69, 0x65, 0x77, 0x12, 0x45, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x4c, 0x69, 0x6e, 0x65, 0x4c, 0x65, 0x67,
	0x65, 0x6e, 0x64, 0x56, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x65, 0x12,
	0x39, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x48, 0x00, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x42, 0x0c, 0x0a, 0x0a, 0x4c, 0x65,
	0x67, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x22, 0x73, 0x0a, 0x0e, 0x4c, 0x69, 0x6e, 0x65,
	0x4c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x56, 0x69, 0x65, 0x77, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69,
	0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x4c, 0x69, 0x6e, 0x65, 0x52, 0x04,
	0x6c, 0x69, 0x6e, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x2a, 0x6a, 0x0a,
	0x0f, 0x4c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x41, 0x6c, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x45, 0x47, 0x45, 0x4e, 0x44, 0x5f, 0x41, 0x4c, 0x49, 0x47, 0x4e,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x45, 0x47, 0x45, 0x4e, 0x44, 0x5f, 0x41, 0x4c, 0x49,
	0x47, 0x4e, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x4f, 0x50, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17,
	0x4c, 0x45, 0x47, 0x45, 0x4e, 0x44, 0x5f, 0x41, 0x4c, 0x49, 0x47, 0x4e, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x10, 0x02, 0x42, 0x70, 0x0a, 0x36, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x5a, 0x36, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescOnce sync.Once
	file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescData = file_api_typesv2_ui_insights_secrets_secrets_proto_rawDesc
)

func file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescGZIP() []byte {
	file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescOnce.Do(func() {
		file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescData)
	})
	return file_api_typesv2_ui_insights_secrets_secrets_proto_rawDescData
}

var file_api_typesv2_ui_insights_secrets_secrets_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_api_typesv2_ui_insights_secrets_secrets_proto_goTypes = []interface{}{
	(LegendAlignment)(0),                  // 0: api.typesv2.ui.insights.secrets.LegendAlignment
	(*SecretSummaryCard)(nil),             // 1: api.typesv2.ui.insights.secrets.SecretSummaryCard
	(*NumberCard)(nil),                    // 2: api.typesv2.ui.insights.secrets.NumberCard
	(*ImageCard)(nil),                     // 3: api.typesv2.ui.insights.secrets.ImageCard
	(*BarChartCard)(nil),                  // 4: api.typesv2.ui.insights.secrets.BarChartCard
	(*BarChart)(nil),                      // 5: api.typesv2.ui.insights.secrets.BarChart
	(*ReferenceLine)(nil),                 // 6: api.typesv2.ui.insights.secrets.ReferenceLine
	(*GridVisualisationCard)(nil),         // 7: api.typesv2.ui.insights.secrets.GridVisualisationCard
	(*GridComponent)(nil),                 // 8: api.typesv2.ui.insights.secrets.GridComponent
	(*Bar)(nil),                           // 9: api.typesv2.ui.insights.secrets.Bar
	(*CardInfoComponent)(nil),             // 10: api.typesv2.ui.insights.secrets.CardInfoComponent
	(*MultiLineChartCard)(nil),            // 11: api.typesv2.ui.insights.secrets.MultiLineChartCard
	(*AreaChartCard)(nil),                 // 12: api.typesv2.ui.insights.secrets.AreaChartCard
	(*AreaChart)(nil),                     // 13: api.typesv2.ui.insights.secrets.AreaChart
	(*MultiLineChart)(nil),                // 14: api.typesv2.ui.insights.secrets.MultiLineChart
	(*AreaChartLine)(nil),                 // 15: api.typesv2.ui.insights.secrets.AreaChartLine
	(*Line)(nil),                          // 16: api.typesv2.ui.insights.secrets.Line
	(*Point)(nil),                         // 17: api.typesv2.ui.insights.secrets.Point
	(*HorizontalAxis)(nil),                // 18: api.typesv2.ui.insights.secrets.HorizontalAxis
	(*HorizontalAxisPoint)(nil),           // 19: api.typesv2.ui.insights.secrets.HorizontalAxisPoint
	(*ChartDragIndicator)(nil),            // 20: api.typesv2.ui.insights.secrets.ChartDragIndicator
	(*DonutCard)(nil),                     // 21: api.typesv2.ui.insights.secrets.DonutCard
	(*DonutSlice)(nil),                    // 22: api.typesv2.ui.insights.secrets.DonutSlice
	(*Legend)(nil),                        // 23: api.typesv2.ui.insights.secrets.Legend
	(*LegendView)(nil),                    // 24: api.typesv2.ui.insights.secrets.LegendView
	(*LineLegendView)(nil),                // 25: api.typesv2.ui.insights.secrets.LineLegendView
	(*SecretSummaryCard_SecretValue)(nil), // 26: api.typesv2.ui.insights.secrets.SecretSummaryCard.SecretValue
	(*ImageCard_ValueComponent)(nil),      // 27: api.typesv2.ui.insights.secrets.ImageCard.ValueComponent
	(*Bar_BarDisplayComponent)(nil),       // 28: api.typesv2.ui.insights.secrets.Bar.BarDisplayComponent
	(*ChartDragIndicator_Indicator)(nil),  // 29: api.typesv2.ui.insights.secrets.ChartDragIndicator.Indicator
	(*ui.IconTextComponent)(nil),          // 30: api.typesv2.ui.IconTextComponent
	(*common.VisualElement)(nil),          // 31: api.typesv2.common.VisualElement
	(*deeplink.Deeplink)(nil),             // 32: frontend.deeplink.Deeplink
	(*ui.VerticalKeyValuePair)(nil),       // 33: api.typesv2.ui.VerticalKeyValuePair
	(*common.Text)(nil),                   // 34: api.typesv2.common.Text
	(*widget.BackgroundColour)(nil),       // 35: api.typesv2.common.ui.widget.BackgroundColour
	(*ui.GridCard)(nil),                   // 36: api.typesv2.ui.GridCard
	(*wrapperspb.Int32Value)(nil),         // 37: google.protobuf.Int32Value
	(*ui.Tooltip)(nil),                    // 38: api.typesv2.ui.Tooltip
	(*ui.Line)(nil),                       // 39: api.typesv2.ui.Line
}
var file_api_typesv2_ui_insights_secrets_secrets_proto_depIdxs = []int32{
	30, // 0: api.typesv2.ui.insights.secrets.SecretSummaryCard.title:type_name -> api.typesv2.ui.IconTextComponent
	26, // 1: api.typesv2.ui.insights.secrets.SecretSummaryCard.value:type_name -> api.typesv2.ui.insights.secrets.SecretSummaryCard.SecretValue
	31, // 2: api.typesv2.ui.insights.secrets.SecretSummaryCard.visualisation:type_name -> api.typesv2.common.VisualElement
	30, // 3: api.typesv2.ui.insights.secrets.SecretSummaryCard.bottom_tag:type_name -> api.typesv2.ui.IconTextComponent
	32, // 4: api.typesv2.ui.insights.secrets.SecretSummaryCard.deeplink:type_name -> frontend.deeplink.Deeplink
	31, // 5: api.typesv2.ui.insights.secrets.NumberCard.image:type_name -> api.typesv2.common.VisualElement
	33, // 6: api.typesv2.ui.insights.secrets.NumberCard.title_value_pair:type_name -> api.typesv2.ui.VerticalKeyValuePair
	30, // 7: api.typesv2.ui.insights.secrets.ImageCard.title:type_name -> api.typesv2.ui.IconTextComponent
	31, // 8: api.typesv2.ui.insights.secrets.ImageCard.bg_image:type_name -> api.typesv2.common.VisualElement
	27, // 9: api.typesv2.ui.insights.secrets.ImageCard.value_component:type_name -> api.typesv2.ui.insights.secrets.ImageCard.ValueComponent
	33, // 10: api.typesv2.ui.insights.secrets.BarChartCard.title_value_pair:type_name -> api.typesv2.ui.VerticalKeyValuePair
	30, // 11: api.typesv2.ui.insights.secrets.BarChartCard.score_insight:type_name -> api.typesv2.ui.IconTextComponent
	9,  // 12: api.typesv2.ui.insights.secrets.BarChartCard.bars:type_name -> api.typesv2.ui.insights.secrets.Bar
	5,  // 13: api.typesv2.ui.insights.secrets.BarChartCard.bar_chart:type_name -> api.typesv2.ui.insights.secrets.BarChart
	9,  // 14: api.typesv2.ui.insights.secrets.BarChart.bars:type_name -> api.typesv2.ui.insights.secrets.Bar
	6,  // 15: api.typesv2.ui.insights.secrets.BarChart.reference_lines:type_name -> api.typesv2.ui.insights.secrets.ReferenceLine
	23, // 16: api.typesv2.ui.insights.secrets.BarChart.legend:type_name -> api.typesv2.ui.insights.secrets.Legend
	30, // 17: api.typesv2.ui.insights.secrets.ReferenceLine.label:type_name -> api.typesv2.ui.IconTextComponent
	33, // 18: api.typesv2.ui.insights.secrets.GridVisualisationCard.title_value_pair:type_name -> api.typesv2.ui.VerticalKeyValuePair
	8,  // 19: api.typesv2.ui.insights.secrets.GridVisualisationCard.grid_component:type_name -> api.typesv2.ui.insights.secrets.GridComponent
	34, // 20: api.typesv2.ui.insights.secrets.GridComponent.title:type_name -> api.typesv2.common.Text
	35, // 21: api.typesv2.ui.insights.secrets.GridComponent.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	36, // 22: api.typesv2.ui.insights.secrets.GridComponent.grid_card:type_name -> api.typesv2.ui.GridCard
	28, // 23: api.typesv2.ui.insights.secrets.Bar.top_display_component:type_name -> api.typesv2.ui.insights.secrets.Bar.BarDisplayComponent
	28, // 24: api.typesv2.ui.insights.secrets.Bar.xaxis_display_component:type_name -> api.typesv2.ui.insights.secrets.Bar.BarDisplayComponent
	32, // 25: api.typesv2.ui.insights.secrets.Bar.deeplink:type_name -> frontend.deeplink.Deeplink
	34, // 26: api.typesv2.ui.insights.secrets.Bar.yaxis_display_value:type_name -> api.typesv2.common.Text
	33, // 27: api.typesv2.ui.insights.secrets.CardInfoComponent.title_value_pair:type_name -> api.typesv2.ui.VerticalKeyValuePair
	30, // 28: api.typesv2.ui.insights.secrets.CardInfoComponent.insight:type_name -> api.typesv2.ui.IconTextComponent
	10, // 29: api.typesv2.ui.insights.secrets.MultiLineChartCard.info_component:type_name -> api.typesv2.ui.insights.secrets.CardInfoComponent
	14, // 30: api.typesv2.ui.insights.secrets.MultiLineChartCard.chart:type_name -> api.typesv2.ui.insights.secrets.MultiLineChart
	10, // 31: api.typesv2.ui.insights.secrets.AreaChartCard.info_component:type_name -> api.typesv2.ui.insights.secrets.CardInfoComponent
	13, // 32: api.typesv2.ui.insights.secrets.AreaChartCard.chart:type_name -> api.typesv2.ui.insights.secrets.AreaChart
	15, // 33: api.typesv2.ui.insights.secrets.AreaChart.line:type_name -> api.typesv2.ui.insights.secrets.AreaChartLine
	23, // 34: api.typesv2.ui.insights.secrets.AreaChart.legend:type_name -> api.typesv2.ui.insights.secrets.Legend
	20, // 35: api.typesv2.ui.insights.secrets.AreaChart.drag_indicator:type_name -> api.typesv2.ui.insights.secrets.ChartDragIndicator
	18, // 36: api.typesv2.ui.insights.secrets.AreaChart.axis:type_name -> api.typesv2.ui.insights.secrets.HorizontalAxis
	16, // 37: api.typesv2.ui.insights.secrets.MultiLineChart.lines:type_name -> api.typesv2.ui.insights.secrets.Line
	18, // 38: api.typesv2.ui.insights.secrets.MultiLineChart.axis:type_name -> api.typesv2.ui.insights.secrets.HorizontalAxis
	23, // 39: api.typesv2.ui.insights.secrets.MultiLineChart.legend:type_name -> api.typesv2.ui.insights.secrets.Legend
	20, // 40: api.typesv2.ui.insights.secrets.MultiLineChart.drag_indicator:type_name -> api.typesv2.ui.insights.secrets.ChartDragIndicator
	16, // 41: api.typesv2.ui.insights.secrets.AreaChartLine.line:type_name -> api.typesv2.ui.insights.secrets.Line
	35, // 42: api.typesv2.ui.insights.secrets.AreaChartLine.fill_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	17, // 43: api.typesv2.ui.insights.secrets.Line.points:type_name -> api.typesv2.ui.insights.secrets.Point
	19, // 44: api.typesv2.ui.insights.secrets.HorizontalAxis.horizontal_axis:type_name -> api.typesv2.ui.insights.secrets.HorizontalAxisPoint
	34, // 45: api.typesv2.ui.insights.secrets.HorizontalAxisPoint.label:type_name -> api.typesv2.common.Text
	29, // 46: api.typesv2.ui.insights.secrets.ChartDragIndicator.indicators:type_name -> api.typesv2.ui.insights.secrets.ChartDragIndicator.Indicator
	10, // 47: api.typesv2.ui.insights.secrets.DonutCard.info_component:type_name -> api.typesv2.ui.insights.secrets.CardInfoComponent
	22, // 48: api.typesv2.ui.insights.secrets.DonutCard.slices:type_name -> api.typesv2.ui.insights.secrets.DonutSlice
	37, // 49: api.typesv2.ui.insights.secrets.DonutCard.highlighted_index:type_name -> google.protobuf.Int32Value
	23, // 50: api.typesv2.ui.insights.secrets.DonutCard.legend:type_name -> api.typesv2.ui.insights.secrets.Legend
	38, // 51: api.typesv2.ui.insights.secrets.DonutSlice.tooltip:type_name -> api.typesv2.ui.Tooltip
	30, // 52: api.typesv2.ui.insights.secrets.Legend.labels:type_name -> api.typesv2.ui.IconTextComponent
	0,  // 53: api.typesv2.ui.insights.secrets.Legend.alignment:type_name -> api.typesv2.ui.insights.secrets.LegendAlignment
	24, // 54: api.typesv2.ui.insights.secrets.Legend.legends:type_name -> api.typesv2.ui.insights.secrets.LegendView
	25, // 55: api.typesv2.ui.insights.secrets.LegendView.line:type_name -> api.typesv2.ui.insights.secrets.LineLegendView
	30, // 56: api.typesv2.ui.insights.secrets.LegendView.label:type_name -> api.typesv2.ui.IconTextComponent
	39, // 57: api.typesv2.ui.insights.secrets.LineLegendView.line:type_name -> api.typesv2.ui.Line
	30, // 58: api.typesv2.ui.insights.secrets.LineLegendView.label:type_name -> api.typesv2.ui.IconTextComponent
	30, // 59: api.typesv2.ui.insights.secrets.SecretSummaryCard.SecretValue.value:type_name -> api.typesv2.ui.IconTextComponent
	31, // 60: api.typesv2.ui.insights.secrets.ImageCard.ValueComponent.image:type_name -> api.typesv2.common.VisualElement
	33, // 61: api.typesv2.ui.insights.secrets.ImageCard.ValueComponent.title_value_pair:type_name -> api.typesv2.ui.VerticalKeyValuePair
	31, // 62: api.typesv2.ui.insights.secrets.Bar.BarDisplayComponent.image:type_name -> api.typesv2.common.VisualElement
	30, // 63: api.typesv2.ui.insights.secrets.Bar.BarDisplayComponent.display_text:type_name -> api.typesv2.ui.IconTextComponent
	30, // 64: api.typesv2.ui.insights.secrets.ChartDragIndicator.Indicator.label:type_name -> api.typesv2.ui.IconTextComponent
	65, // [65:65] is the sub-list for method output_type
	65, // [65:65] is the sub-list for method input_type
	65, // [65:65] is the sub-list for extension type_name
	65, // [65:65] is the sub-list for extension extendee
	0,  // [0:65] is the sub-list for field type_name
}

func init() { file_api_typesv2_ui_insights_secrets_secrets_proto_init() }
func file_api_typesv2_ui_insights_secrets_secrets_proto_init() {
	if File_api_typesv2_ui_insights_secrets_secrets_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecretSummaryCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NumberCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BarChartCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BarChart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReferenceLine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GridVisualisationCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GridComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bar); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardInfoComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultiLineChartCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AreaChartCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AreaChart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultiLineChart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AreaChartLine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Line); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Point); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HorizontalAxis); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HorizontalAxisPoint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChartDragIndicator); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DonutCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DonutSlice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Legend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LegendView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LineLegendView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecretSummaryCard_SecretValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageCard_ValueComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bar_BarDisplayComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChartDragIndicator_Indicator); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[23].OneofWrappers = []interface{}{
		(*LegendView_Line)(nil),
		(*LegendView_Label)(nil),
	}
	file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes[27].OneofWrappers = []interface{}{
		(*Bar_BarDisplayComponent_Image)(nil),
		(*Bar_BarDisplayComponent_DisplayText)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_ui_insights_secrets_secrets_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_ui_insights_secrets_secrets_proto_goTypes,
		DependencyIndexes: file_api_typesv2_ui_insights_secrets_secrets_proto_depIdxs,
		EnumInfos:         file_api_typesv2_ui_insights_secrets_secrets_proto_enumTypes,
		MessageInfos:      file_api_typesv2_ui_insights_secrets_secrets_proto_msgTypes,
	}.Build()
	File_api_typesv2_ui_insights_secrets_secrets_proto = out.File
	file_api_typesv2_ui_insights_secrets_secrets_proto_rawDesc = nil
	file_api_typesv2_ui_insights_secrets_secrets_proto_goTypes = nil
	file_api_typesv2_ui_insights_secrets_secrets_proto_depIdxs = nil
}
