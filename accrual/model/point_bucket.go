package model

import (
	"time"

	"github.com/pkg/errors"
)

type PointBucketStatus string

//todo(utkarsh) : add documentation on dtos

const (
	AVAILABLE PointBucketStatus = "AVAILABLE"
	EXPIRED   PointBucketStatus = "EXPIRED"
)

type PointBucketIdentifier struct {
	AccountId  string
	ExpiryTime *time.Time
}

func (a *PointBucketIdentifier) Validate() error {
	if a.AccountId == "" {
		return errors.New("PointBucketIdentifier : account id is nil")
	}
	if a.ExpiryTime == nil {
		return errors.New("PointBucketIdentifier : expiry time is nil")
	}
	return nil
}

type PointBucket struct {
	ID string

	// AccountId denotes the account to which the point bucket belongs
	AccountId string

	// ExpiryTime denotes the time after which balance of this bucket would expire
	ExpiryTime *time.Time

	// CurrentBalance denotes the balance present in the bucket
	CurrentBalance int32

	Status PointBucketStatus
}

type AccountPointsBalance struct {
	AccountId         string
	TotalPointBalance int32
	NextExpiryTime    time.Time
}
