name: "Run LCI Validation"
description: "Action to run LCI validation for all services. This assumes that the
github.com/epifi/gringott/tools/actions/lci directory has already been checked out and the git note has been fetched."
inputs:
  workflow_token:
    description: "workflow token generated by peter-murray/workflow-application-token-action"
    required: true

runs:
  using: composite
  steps:
    - run: |
        echo "Running LCI validation"
      shell: bash

    - name: Get changed files using GitHub API
      shell: bash
      id: get_changed_files
      run: |
        PR_NUMBER=${{ github.event.pull_request.number }}
        REPO_NAME=${{ github.repository }}
        # Fetch modified files in the PR
        echo "Fetching modified files for PR $PR_NUMBER in repo $REPO_NAME"
        CHANGED_FILES=$(gh api repos/$REPO_NAME/pulls/$PR_NUMBER/files --paginate --jq '.[].filename')
        TRIMMED_CHANGED_FILES=$(echo "$CHANGED_FILES" | tr '\n' ' ')
        # Add repo name prefix to each changed file
        PREFIXED_CHANGED_FILES=$(echo "$TRIMMED_CHANGED_FILES" | sed "s|[^ ]*|gringott/&|g")
        echo "changed_files=$PREFIXED_CHANGED_FILES" >> $GITHUB_OUTPUT
        # Check if any go.* files are changed
        if echo "$TRIMMED_CHANGED_FILES" | grep -q "go\."; then
          echo "Go-related files changed: go.mod or go.sum detected"
          echo "go_mod_changed=true" >> $GITHUB_OUTPUT
        else
          echo "No go-related files changed."
          echo "go_mod_changed=false" >> $GITHUB_OUTPUT
        fi
      env:
        GITHUB_TOKEN: ${{ inputs.workflow_token }}

    # Step 3: Check if `be-common` version is upgraded
    - name: Check if  `be-common` version is upgraded
      if: ${{ steps.get_changed_files.outputs.go_mod_changed == 'true' }}
      shell: bash
      id: version_check
      run: |
        PR_NUMBER=${{ github.event.pull_request.number }}
        REPO_NAME=${{ github.repository }}
        echo "go.mod is modified, checking patch details..."
        PATCH=$(gh api repos/$REPO_NAME/pulls/$PR_NUMBER/files --paginate --jq '.[] | select(.filename == "go.mod").patch')
        BE_COMMON_CHANGED=false
        OTHER_DEPS_CHANGED=false
        OLD_COMMIT=""
        NEW_COMMIT=""
        # Loop through the patch to find be-common version upgrade and extract commit hashes
        while IFS= read -r line; do
          if [[ "$line" == -* ]] || [[ "$line" == +* ]]; then
            if echo "$line" | grep -q "be-common"; then
              BE_COMMON_CHANGED=true  # be-common version changed
              # Extract old and new commit hashes for be-common version change
              if [[ "$line" == -* ]]; then
                OLD_COMMIT=$(echo "$line" | grep -oE '[a-f0-9]+$')
                echo "Old commit hash for be-common: $OLD_COMMIT"
              elif [[ "$line" == +* ]]; then
                NEW_COMMIT=$(echo "$line" | grep -oE '[a-f0-9]+$')
                echo "New commit hash for be-common: $NEW_COMMIT"
              fi
            fi
          fi
        done <<< "$PATCH"
        if [[ "$BE_COMMON_CHANGED" == true && -n "$OLD_COMMIT" && -n "$NEW_COMMIT" ]]; then
          echo "be-common version was upgraded."
          echo "old_commit=$OLD_COMMIT" >> $GITHUB_OUTPUT
          echo "new_commit=$NEW_COMMIT" >> $GITHUB_OUTPUT
          echo "is_be_common_upgraded=true" >> $GITHUB_OUTPUT
        else
          echo "No valid be-common version upgrade or other dependencies modified."
          echo "is_be_common_upgraded=false" >> $GITHUB_OUTPUT
        fi
      env:
        GITHUB_TOKEN: ${{ inputs.workflow_token }}

    # Step 4: Find the changed files between the old and new commit in be-common
    - name: "Get changed files in be-common between commits"
      id: be-common-changed_files
      if: ${{ steps.version_check.outputs.is_be_common_upgraded == 'true' }}
      shell: bash
      run: |
        OLD_COMMIT=${{ steps.version_check.outputs.old_commit }}
        NEW_COMMIT=${{ steps.version_check.outputs.new_commit }}
        REPO_NAME="epiFi/be-common"
        echo "Fetching files changed between commits $OLD_COMMIT and $NEW_COMMIT in be-common repository..."
        CHANGED_FILES=$(gh api repos/$REPO_NAME/compare/$OLD_COMMIT...$NEW_COMMIT --paginate --jq '.files[].filename')
        TRIMMED_CHANGED_FILES=$(echo "$CHANGED_FILES" | tr '\n' ' ')
        # Add repo name prefix to each changed file
        PREFIXED_CHANGED_FILES=$(echo "$TRIMMED_CHANGED_FILES" | sed "s|[^ ]*|be-common/&|g")
        echo "changed_files=$PREFIXED_CHANGED_FILES" >> $GITHUB_OUTPUT
      env:
        GITHUB_TOKEN: ${{ inputs.workflow_token }}

    - name: "Check out be-common repo"
      uses: actions/checkout@24cb9080177205b6e8c946b17badbe402adc938f
      with:
        repository: 'epiFi/be-common'
        path: go/src/github.com/epifi/be-common
        fetch-depth: 1
        ref: "master"
        token: ${{ inputs.workflow_token }}

    - name: "Find required checks"
      id: path_filters
      shell: bash
      run: |
        set -x
        patternJson=$(yq eval '.service_file_patterns' ./go/src/github.com/epifi/gringott/tools/actions/lci/run_lci_validation/path_pattern.yml | yq eval -o=json)
        prchangedFiles="${{ steps.get_changed_files.outputs.changed_files }}"
        be_common_changedFiles="${{ steps.be-common-changed_files.outputs.changed_files }}"

        allChangedFiles="$prchangedFiles $be_common_changedFiles"
        cd "./go/src/github.com/epifi/be-common"
        go build -o ./output/required_check ./tools/actions/get_required_check/required_check.go

        output="$(./output/required_check --inputFile="$allChangedFiles" --globPattern="$patternJson")"
        echo "output $output"
        outputArray=()
        clean_output=$(echo "$output" | sed 's/"//g')
        # Use IFS (Internal Field Separator) to split by ',' and read into the array
        IFS=',' read -r -a outputArray <<< "$clean_output"

        # Iterate through the array and append results to the GitHub output
        for check in "${outputArray[@]}"; do
            echo "${check}=true" >> "$GITHUB_OUTPUT"
        done

    - name: "BatchBaseServiceChecker status"
      uses: "./go/src/github.com/epifi/gringott/tools/actions/lci/lci_checker"
      if: ${{ always() }}
      with:
        # BatchBaseServiceChecker is to ensure that there is no error in batching workflows to run services workflows
        # LCI will publish BatchBaseServiceChecker event even if there is no service workflows to run.
        services: "BatchBaseServiceChecker"
        lci_stamp_key: "BatchBaseServiceChecker"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "other"
        enabled: true
    - name: "Precommit LCI status"
      uses: "./go/src/github.com/epifi/gringott/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.precommit == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "precommit"
        lci_stamp_key: "precommit"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "other"
        enabled: true
    - name: "BuildApps LCI status"
      uses: "./go/src/github.com/epifi/gringott/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.build_apps == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "build_apps"
        lci_stamp_key: "build_apps"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "other"
        enabled: true

    - name: "Matrix LCI status"
      uses: "./go/src/github.com/epifi/gringott/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.matrix == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "matrix"
        lci_stamp_key: "matrix"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Customer LCI status"
      uses: "./go/src/github.com/epifi/gringott/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.customer == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "customer"
        lci_stamp_key: "customer"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Ckyc LCI status"
      uses: "./go/src/github.com/epifi/gringott/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.ckyc == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "ckyc"
        lci_stamp_key: "ckyc"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true
