package dao_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	authPb "github.com/epifi/gamma/api/auth"
	authTokenPb "github.com/epifi/gamma/api/auth/token"
	"github.com/epifi/gamma/auth/dao"
	mockAuthDao "github.com/epifi/gamma/auth/dao/mocks"
	"github.com/epifi/gamma/auth/dao/model"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/gamma/pkg/obfuscator"
)

func TestTokenStoresDaoV2Impl_StoreTokenById(t *testing.T) {
	var (
		ctx = context.Background()
	)

	t1Actor := newToken("1", 1231231231)
	t1Actor.SubjectValue = obfuscator.HashedPhoneNum(&commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1231231231})
	t1Actor.SubjectType = authTokenPb.SubjectType_SUBJECT_TYPE_PHONE_NUMBER
	t1Actor.CreatedAt = time.Now()
	t1Actor.UpdatedAt = t1Actor.CreatedAt
	t1Actor.TokenType = authPb.TokenType_REFRESH_TOKEN

	type args struct {
		ctx    context.Context
		token  *model.TokenStore
		params *model.StoreTokenParams
	}
	type testParams struct {
		// to avoid model asserts when there is no DB call, we assert for RNF error
		ignoreDBAssert bool
	}
	tests := []struct {
		name       string
		args       args
		wantErr    error
		wantMocks  func(args args, md *mockedDependencies)
		testParams *testParams
	}{
		{
			name: "db error (use db only)",
			args: args{
				ctx:   ctx,
				token: t1Actor,
				params: &model.StoreTokenParams{
					UseDBOnly: commontypes.BoolToBooleanEnum(true),
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().StoreToken(args.ctx, args.token, gomock.Any()).Return(fmt.Errorf("db error"))
			},
			wantErr: fmt.Errorf("db error"),
		},
		{
			name: "success (use db only)",
			args: args{
				ctx:   ctx,
				token: t1Actor,
				params: &model.StoreTokenParams{
					UseDBOnly: commontypes.BoolToBooleanEnum(true),
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().StoreToken(args.ctx, args.token, gomock.Any()).Return(nil)
			},
			wantErr: nil,
		},
		{
			name: "error in redis (use cache only)",
			args: args{
				ctx:   ctx,
				token: t1Actor,
				params: &model.StoreTokenParams{
					UseCacheOnly: commontypes.BoolToBooleanEnum(true),
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockIdCacheDao.EXPECT().StoreToken(args.ctx, args.token, gomock.Any()).Return(fmt.Errorf("cache error"))
			},
			wantErr: nil,
		},
		{
			name: "successfully stored in redis (use cache only)",
			args: args{
				ctx:   ctx,
				token: t1Actor,
				params: &model.StoreTokenParams{
					UseCacheOnly: commontypes.BoolToBooleanEnum(true),
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockIdCacheDao.EXPECT().StoreToken(args.ctx, args.token, gomock.Any()).Return(nil)
			},
			wantErr: nil,
		},
		{
			name: "successfully stored in redis and db",
			args: args{
				ctx:    ctx,
				token:  t1Actor,
				params: nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().StoreToken(args.ctx, args.token, gomock.Any()).Return(nil)
				md.mockIdCacheDao.EXPECT().StoreToken(args.ctx, args.token, gomock.Any()).Return(nil)
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()

			_ = gconf.TokenStoresCacheConfig().SetIsCachingEnabled(true, true, nil)

			mockDbDao := mockAuthDao.NewMockTokenStoresDBDaoV2(ctrl)
			mockIdCacheDao := mockAuthDao.NewMockTokenStoresIdCacheDao(ctrl)
			mockSubjectCacheDao := mockAuthDao.NewMockTokenStoresSubjectCacheDao(ctrl)

			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, &mockedDependencies{
					mockDBV2:            mockDbDao,
					mockIdCacheDao:      mockIdCacheDao,
					mockSubjectCacheDao: mockSubjectCacheDao,
				})
			}
			tokenStoresDaoV2 := dao.NewTokenStoresDaoV2Impl(gconf.TokenStoresCacheConfig(), mockDbDao, mockIdCacheDao, mockSubjectCacheDao)
			err := tokenStoresDaoV2.StoreTokenById(tt.args.ctx, tt.args.token, &model.StoreTokenParams{
				UseDBOnly:    commontypes.BoolToBooleanEnum(tt.args.params.GetUseDBOnly()),
				UseCacheOnly: commontypes.BoolToBooleanEnum(tt.args.params.GetUseCacheOnly()),
			})
			if tt.wantErr != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("unexpected error \nwant: %v\n got: %v\n", tt.wantErr, err)
			}
		})
	}
}

func TestTokenStoresDaoV2Impl_GetTokenById(t *testing.T) {
	var (
		ctx = context.Background()
	)

	t1Actor := newToken("1", 1231231231)
	t1Actor.SubjectValue = obfuscator.HashedPhoneNum(&commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1231231231})
	t1Actor.SubjectType = authTokenPb.SubjectType_SUBJECT_TYPE_PHONE_NUMBER
	t1Actor.CreatedAt = time.Now()
	t1Actor.UpdatedAt = t1Actor.CreatedAt
	t1Actor.TokenType = authPb.TokenType_REFRESH_TOKEN

	type args struct {
		ctx    context.Context
		id     string
		params *model.GetTokenByIdParams
	}
	type testParams struct {
		// to avoid model asserts when there is no DB call, we assert for RNF error
		ignoreDBAssert bool
	}
	tests := []struct {
		name       string
		args       args
		wantErr    error
		wantMocks  func(args args, md *mockedDependencies)
		want       *model.TokenStore
		testParams *testParams
	}{
		{
			name: "db error (use db only)",
			args: args{
				ctx: ctx,
				id:  t1Actor.ID,
				params: &model.GetTokenByIdParams{
					UseDBOnly: commontypes.BoolToBooleanEnum(true),
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().GetTokenById(args.ctx, args.id).Return(nil, fmt.Errorf("db error"))
			},
			want:       nil,
			wantErr:    fmt.Errorf("db error"),
			testParams: &testParams{},
		},
		{
			name: "success (use db only)",
			args: args{
				ctx: ctx,
				id:  t1Actor.ID,
				params: &model.GetTokenByIdParams{
					UseDBOnly: commontypes.BoolToBooleanEnum(true),
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().GetTokenById(args.ctx, args.id).Return(t1Actor, nil)
			},
			want:    t1Actor,
			wantErr: nil,
		},
		{
			name: "error in redis (use cache only)",
			args: args{
				ctx: ctx,
				id:  t1Actor.ID,
				params: &model.GetTokenByIdParams{
					UseCacheOnly: commontypes.BoolToBooleanEnum(true),
				},
			},
			want: nil,
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockIdCacheDao.EXPECT().GetToken(args.ctx, args.id).Return(nil, fmt.Errorf("cache error"))
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
		{
			name: "successfully got from redis (use cache only)",
			args: args{
				ctx: ctx,
				id:  t1Actor.ID,
				params: &model.GetTokenByIdParams{
					UseCacheOnly: commontypes.BoolToBooleanEnum(true),
				},
			},
			want: t1Actor,
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockIdCacheDao.EXPECT().GetToken(args.ctx, args.id).Return(t1Actor, nil)
			},
			wantErr: nil,
		},
		{
			name: "successfully got from db - cache miss",
			args: args{
				ctx:    ctx,
				id:     t1Actor.ID,
				params: &model.GetTokenByIdParams{},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockIdCacheDao.EXPECT().GetToken(args.ctx, args.id).Return(nil, epifierrors.ErrRecordNotFound)
				md.mockDBV2.EXPECT().GetTokenById(args.ctx, args.id).Return(t1Actor, nil)
				md.mockIdCacheDao.EXPECT().StoreToken(args.ctx, t1Actor).Return(nil)
			},
			want:    t1Actor,
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()

			_ = gconf.TokenStoresCacheConfig().SetIsCachingEnabled(true, true, nil)

			mockDbDao := mockAuthDao.NewMockTokenStoresDBDaoV2(ctrl)
			mockIdCacheDao := mockAuthDao.NewMockTokenStoresIdCacheDao(ctrl)
			mockSubjectCacheDao := mockAuthDao.NewMockTokenStoresSubjectCacheDao(ctrl)

			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, &mockedDependencies{
					mockDBV2:            mockDbDao,
					mockIdCacheDao:      mockIdCacheDao,
					mockSubjectCacheDao: mockSubjectCacheDao,
				})
			}
			tokenStoresDaoV2 := dao.NewTokenStoresDaoV2Impl(gconf.TokenStoresCacheConfig(), mockDbDao, mockIdCacheDao, mockSubjectCacheDao)
			got, err := tokenStoresDaoV2.GetTokenById(tt.args.ctx, tt.args.id, tt.args.params)
			if tt.wantErr != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("unexpected error \nwant: %v\n got: %v\n", tt.wantErr, err)
			}
			assertTokenStoreModel(t, got, tt.want)
		})
	}
}

func TestTokenStoresDaoV2Impl_ExpireTokenById(t *testing.T) {
	var (
		ctx = context.Background()
	)

	t1Actor := newToken("1", 1231231231)
	t1Actor.SubjectValue = obfuscator.HashedPhoneNum(&commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1231231231})
	t1Actor.SubjectType = authTokenPb.SubjectType_SUBJECT_TYPE_PHONE_NUMBER
	t1Actor.CreatedAt = time.Now()
	t1Actor.UpdatedAt = t1Actor.CreatedAt
	t1Actor.TokenType = authPb.TokenType_ACCESS_TOKEN

	type args struct {
		ctx            context.Context
		id             string
		deletionReason authPb.TokenDeletionReason
		params         []*model.ExpireTokenByIdParams
	}
	type testParams struct {
		// to avoid model asserts when there is no DB call, we assert for RNF error
		ignoreDBAssert           bool
		disableCachingByTokenId  bool
		enableCachingBySubjectId bool
	}
	tests := []struct {
		name       string
		args       args
		wantErr    error
		wantMocks  func(args args, md *mockedDependencies)
		want       *model.TokenStore
		testParams *testParams
	}{
		{
			name: "expire token : db error",
			args: args{
				ctx:            ctx,
				id:             t1Actor.ID,
				deletionReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireToken(args.ctx, args.id, args.deletionReason).Return(nil, fmt.Errorf("db error"))
			},
			want:       nil,
			wantErr:    fmt.Errorf("db error"),
			testParams: &testParams{},
		},
		{
			name: "expire token : error in expiring from cache by id",
			args: args{
				ctx:            ctx,
				id:             t1Actor.ID,
				deletionReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireToken(args.ctx, args.id, args.deletionReason).Return(t1Actor, nil)
				md.mockIdCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.ID).Return(fmt.Errorf("error in redis"))
			},
			want:       nil,
			wantErr:    fmt.Errorf("error in expiring token from cache : error in redis"),
			testParams: &testParams{},
		},
		{
			name: "expire token : error in expiring from cache by subject",
			args: args{
				ctx:            ctx,
				id:             t1Actor.ID,
				deletionReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireToken(args.ctx, args.id, args.deletionReason).Return(t1Actor, nil)
				md.mockSubjectCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.SubjectValue, t1Actor.SubjectType, t1Actor.TokenType).Return(fmt.Errorf("error in redis"))
			},
			want:    nil,
			wantErr: fmt.Errorf("error in expiring token from cache : error in redis"),
			testParams: &testParams{
				enableCachingBySubjectId: true,
				disableCachingByTokenId:  false,
			},
		},
		{
			name: "expire token : successful + disableCachingByTokenId: true",
			args: args{
				ctx:            ctx,
				id:             t1Actor.ID,
				deletionReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireToken(args.ctx, args.id, args.deletionReason).Return(t1Actor, nil)
				md.mockSubjectCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.SubjectValue, t1Actor.SubjectType, t1Actor.TokenType).Return(nil)
			},
			want:    t1Actor,
			wantErr: nil,
			testParams: &testParams{
				disableCachingByTokenId:  true,
				enableCachingBySubjectId: true,
			},
		},
		{
			name: "expire token : successful + enableCachingBySubjectId: false",
			args: args{
				ctx:            ctx,
				id:             t1Actor.ID,
				deletionReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireToken(args.ctx, args.id, args.deletionReason).Return(t1Actor, nil)
				md.mockIdCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.ID).Return(nil)
			},
			want:    t1Actor,
			wantErr: nil,
			testParams: &testParams{
				enableCachingBySubjectId: false,
			},
		},
		{
			name: "expire token : successful",
			args: args{
				ctx:            ctx,
				id:             t1Actor.ID,
				deletionReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireToken(args.ctx, args.id, args.deletionReason).Return(t1Actor, nil)
				md.mockIdCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.ID).Return(nil)
				md.mockSubjectCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.SubjectValue, t1Actor.SubjectType, t1Actor.TokenType).Return(nil)
			},
			want:    t1Actor,
			wantErr: nil,
			testParams: &testParams{
				enableCachingBySubjectId: true,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()

			_ = gconf.TokenStoresCacheConfig().SetIsCachingEnabled(true, true, nil)
			_ = gconf.TokenStoresCacheConfig().SetDisableCachingByTokenIdForAccessTokens(tt.testParams.disableCachingByTokenId, true, nil)
			_ = gconf.TokenStoresCacheConfig().SetEnableCachingBySubject(tt.testParams.enableCachingBySubjectId, true, nil)

			mockDbDao := mockAuthDao.NewMockTokenStoresDBDaoV2(ctrl)
			mockIdCacheDao := mockAuthDao.NewMockTokenStoresIdCacheDao(ctrl)
			mockSubjectCacheDao := mockAuthDao.NewMockTokenStoresSubjectCacheDao(ctrl)

			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, &mockedDependencies{
					mockDBV2:            mockDbDao,
					mockIdCacheDao:      mockIdCacheDao,
					mockSubjectCacheDao: mockSubjectCacheDao,
				})
			}
			tokenStoresDaoV2 := dao.NewTokenStoresDaoV2Impl(gconf.TokenStoresCacheConfig(), mockDbDao, mockIdCacheDao, mockSubjectCacheDao)
			_, err := tokenStoresDaoV2.ExpireTokenById(tt.args.ctx, tt.args.id, tt.args.deletionReason, tt.args.params...)
			if tt.wantErr != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("unexpected error \nwant: %v\n got: %v\n", tt.wantErr, err)
			}
		})
	}
}

func TestTokenStoresDaoV2Impl_StoreTokenBySubject(t *testing.T) {
	var (
		ctx = context.Background()
	)

	t1Actor := newToken("1", 1231231231)
	t1Actor.SubjectValue = obfuscator.HashedPhoneNum(&commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 1231231231})
	t1Actor.SubjectType = authTokenPb.SubjectType_SUBJECT_TYPE_PHONE_NUMBER
	t1Actor.CreatedAt = time.Now()
	t1Actor.UpdatedAt = t1Actor.CreatedAt
	t1Actor.TokenType = authPb.TokenType_REFRESH_TOKEN

	type args struct {
		ctx    context.Context
		token  *model.TokenStore
		params *model.StoreTokenParams
	}
	type testParams struct {
		// to avoid model asserts when there is no DB call, we assert for RNF error
		ignoreDBAssert bool
	}
	tests := []struct {
		name       string
		args       args
		wantErr    error
		wantMocks  func(args args, md *mockedDependencies)
		testParams *testParams
	}{
		{
			name: "db error (use db only)",
			args: args{
				ctx:   ctx,
				token: t1Actor,
				params: &model.StoreTokenParams{
					UseDBOnly: commontypes.BoolToBooleanEnum(true),
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().StoreToken(args.ctx, args.token, gomock.Any()).Return(fmt.Errorf("db error"))
			},
			wantErr: fmt.Errorf("db error"),
		},
		{
			name: "success (use db only)",
			args: args{
				ctx:   ctx,
				token: t1Actor,
				params: &model.StoreTokenParams{
					UseDBOnly: commontypes.BoolToBooleanEnum(true),
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().StoreToken(args.ctx, args.token, gomock.Any()).Return(nil)
			},
			wantErr: nil,
		},
		{
			name: "error in redis (use cache only)",
			args: args{
				ctx:   ctx,
				token: t1Actor,
				params: &model.StoreTokenParams{
					UseCacheOnly: commontypes.BoolToBooleanEnum(true),
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockSubjectCacheDao.EXPECT().StoreToken(args.ctx, args.token, gomock.Any()).Return(fmt.Errorf("cache error"))
			},
			wantErr: nil,
		},
		{
			name: "successfully stored in redis (use cache only)",
			args: args{
				ctx:   ctx,
				token: t1Actor,
				params: &model.StoreTokenParams{
					UseCacheOnly: commontypes.BoolToBooleanEnum(true),
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockSubjectCacheDao.EXPECT().StoreToken(args.ctx, args.token, gomock.Any()).Return(nil)
			},
			wantErr: nil,
		},
		{
			name: "successfully stored in redis and db",
			args: args{
				ctx:    ctx,
				token:  t1Actor,
				params: nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().StoreToken(args.ctx, args.token, gomock.Any()).Return(nil)
				md.mockSubjectCacheDao.EXPECT().StoreToken(args.ctx, args.token, gomock.Any()).Return(nil)
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()

			_ = gconf.TokenStoresCacheConfig().SetIsCachingEnabled(true, true, nil)

			mockDbDao := mockAuthDao.NewMockTokenStoresDBDaoV2(ctrl)
			mockIdCacheDao := mockAuthDao.NewMockTokenStoresIdCacheDao(ctrl)
			mockSubjectCacheDao := mockAuthDao.NewMockTokenStoresSubjectCacheDao(ctrl)

			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, &mockedDependencies{
					mockDBV2:            mockDbDao,
					mockIdCacheDao:      mockIdCacheDao,
					mockSubjectCacheDao: mockSubjectCacheDao,
				})
			}
			tokenStoresDaoV2 := dao.NewTokenStoresDaoV2Impl(gconf.TokenStoresCacheConfig(), mockDbDao, mockIdCacheDao, mockSubjectCacheDao)
			err := tokenStoresDaoV2.StoreTokenBySubject(tt.args.ctx, tt.args.token, &model.StoreTokenParams{
				UseDBOnly:    commontypes.BoolToBooleanEnum(tt.args.params.GetUseDBOnly()),
				UseCacheOnly: commontypes.BoolToBooleanEnum(tt.args.params.GetUseCacheOnly()),
			})
			if tt.wantErr != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("unexpected error \nwant: %v\n got: %v\n", tt.wantErr, err)
			}
		})
	}
}

func TestTokenStoresDaoV2Impl_GetTokenBySubjectAndTokenType(t *testing.T) {
	var (
		ctx = context.Background()
	)

	t1Actor := newToken("1", 1231231231)
	t1Actor.SubjectValue = "1"
	t1Actor.SubjectType = authTokenPb.SubjectType_SUBJECT_TYPE_ACTOR_ID
	t1Actor.CreatedAt = time.Now()
	t1Actor.UpdatedAt = t1Actor.CreatedAt
	t1Actor.TokenType = authPb.TokenType_ACCESS_TOKEN

	type args struct {
		ctx         context.Context
		subject     string
		subjectType authTokenPb.SubjectType
		tokenType   authPb.TokenType
		params      []*model.GetTokenBySubjectAndTokenTypeParams
	}
	tests := []struct {
		name      string
		args      args
		wantErr   error
		wantMocks func(args args, md *mockedDependencies)
		want      *model.TokenStore
	}{
		{
			name: "cache error",
			args: args{
				ctx:         ctx,
				subject:     t1Actor.SubjectValue,
				subjectType: t1Actor.SubjectType,
				tokenType:   t1Actor.TokenType,
				params:      nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockSubjectCacheDao.EXPECT().GetToken(args.ctx, args.subject, args.subjectType, args.tokenType).Return(nil, fmt.Errorf("db error"))
			},
			want:    nil,
			wantErr: fmt.Errorf("db error"),
		},
		{
			name: "success",
			args: args{
				ctx:         ctx,
				subject:     t1Actor.SubjectValue,
				subjectType: t1Actor.SubjectType,
				tokenType:   t1Actor.TokenType,
				params:      nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockSubjectCacheDao.EXPECT().GetToken(args.ctx, args.subject, args.subjectType, args.tokenType).Return(t1Actor, nil)
			},
			want:    t1Actor,
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()

			mockSubjectCacheDao := mockAuthDao.NewMockTokenStoresSubjectCacheDao(ctrl)

			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, &mockedDependencies{
					mockSubjectCacheDao: mockSubjectCacheDao,
				})
			}
			tokenStoresDaoV2 := dao.NewTokenStoresDaoV2Impl(gconf.TokenStoresCacheConfig(), nil, nil, mockSubjectCacheDao)
			got, err := tokenStoresDaoV2.GetTokenBySubjectAndTokenType(tt.args.ctx, tt.args.subject, tt.args.subjectType, tt.args.tokenType, tt.args.params...)
			if tt.wantErr != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("unexpected error \nwant: %v\n got: %v\n", tt.wantErr, err)
			}

			assertTokenStoreModel(t, got, tt.want)
		})
	}
}

func TestTokenStoresDaoV2Impl_ExpireTokenBySubject(t *testing.T) {
	var (
		ctx = context.Background()
	)

	t1Actor := newToken("1", 1231231231)
	t1Actor.SubjectValue = "1"
	t1Actor.SubjectType = authTokenPb.SubjectType_SUBJECT_TYPE_ACTOR_ID
	t1Actor.CreatedAt = time.Now()
	t1Actor.UpdatedAt = t1Actor.CreatedAt
	t1Actor.TokenType = authPb.TokenType_ACCESS_TOKEN

	type args struct {
		ctx            context.Context
		subject        string
		subjectType    authTokenPb.SubjectType
		tokenType      authPb.TokenType
		deletionReason authPb.TokenDeletionReason
		params         *model.ExpireTokensBySubjectParams
	}
	type testParams struct {
		// to avoid model asserts when there is no DB call, we assert for RNF error
		ignoreDBAssert bool
		useCacheOnly   bool
	}
	tests := []struct {
		name       string
		args       args
		wantErr    error
		wantMocks  func(args args, md *mockedDependencies)
		want       []*model.TokenStore
		testParams *testParams
	}{
		{
			name: "expire token : redis error",
			args: args{
				ctx:            ctx,
				subject:        t1Actor.SubjectValue,
				subjectType:    t1Actor.SubjectType,
				tokenType:      t1Actor.TokenType,
				deletionReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				params:         &model.ExpireTokensBySubjectParams{},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireTokenBySubject(args.ctx, args.subject, args.subjectType, args.tokenType, args.deletionReason).Return([]*model.TokenStore{t1Actor}, nil)
				md.mockSubjectCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.SubjectValue, t1Actor.SubjectType, t1Actor.TokenType).Return(fmt.Errorf("error in redis"))
			},
			want:       nil,
			wantErr:    fmt.Errorf("error in expiring token from cache : error in redis"),
			testParams: &testParams{},
		},
		{
			name: "expire token : db error",
			args: args{
				ctx:            ctx,
				subject:        t1Actor.SubjectValue,
				subjectType:    t1Actor.SubjectType,
				tokenType:      t1Actor.TokenType,
				deletionReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireTokenBySubject(args.ctx, args.subject, args.subjectType, args.tokenType, args.deletionReason).Return(nil, fmt.Errorf("db error"))
			},
			want:       nil,
			wantErr:    fmt.Errorf("db error"),
			testParams: &testParams{},
		},
		{
			name: "expire token : success",
			args: args{
				ctx:            ctx,
				subject:        t1Actor.SubjectValue,
				subjectType:    t1Actor.SubjectType,
				tokenType:      t1Actor.TokenType,
				deletionReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockSubjectCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.SubjectValue, t1Actor.SubjectType, t1Actor.TokenType).Return(nil)
				md.mockIdCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.ID).Return(nil)
				md.mockDBV2.EXPECT().ExpireTokenBySubject(args.ctx, args.subject, args.subjectType, args.tokenType, args.deletionReason).Return([]*model.TokenStore{t1Actor}, nil)
			},
			want:       []*model.TokenStore{t1Actor},
			wantErr:    nil,
			testParams: &testParams{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()

			_ = gconf.TokenStoresCacheConfig().SetIsCachingEnabled(true, true, nil)

			mockDbDao := mockAuthDao.NewMockTokenStoresDBDaoV2(ctrl)
			mockIdCacheDao := mockAuthDao.NewMockTokenStoresIdCacheDao(ctrl)
			mockSubjectCacheDao := mockAuthDao.NewMockTokenStoresSubjectCacheDao(ctrl)

			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, &mockedDependencies{
					mockDBV2:            mockDbDao,
					mockIdCacheDao:      mockIdCacheDao,
					mockSubjectCacheDao: mockSubjectCacheDao,
				})
			}
			tokenStoresDaoV2 := dao.NewTokenStoresDaoV2Impl(gconf.TokenStoresCacheConfig(), mockDbDao, mockIdCacheDao, mockSubjectCacheDao)
			got, err := tokenStoresDaoV2.ExpireTokenBySubject(tt.args.ctx, tt.args.subject, tt.args.subjectType, tt.args.tokenType, tt.args.deletionReason, tt.args.params)
			if tt.wantErr != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("unexpected error \nwant: %v\n got: %v\n", tt.wantErr, err)
			}
			assert.Equal(t, got, tt.want)
		})
	}
}

func TestTokenStoresDaoV2Impl_GetLatestTokenByActorIdAndTokenType(t *testing.T) {
	var (
		ctx = context.Background()
	)

	t1Actor := newToken("1", 1231231231)
	t1Actor.SubjectValue = "1"
	t1Actor.SubjectType = authTokenPb.SubjectType_SUBJECT_TYPE_ACTOR_ID
	t1Actor.CreatedAt = time.Now()
	t1Actor.UpdatedAt = t1Actor.CreatedAt
	t1Actor.TokenType = authPb.TokenType_ACCESS_TOKEN

	type args struct {
		ctx            context.Context
		actorId        string
		tokenType      authPb.TokenType
		includeDeleted bool
		params         *model.GetLatestTokenByActorIdAndTokenTypeParams
	}
	tests := []struct {
		name      string
		args      args
		wantErr   error
		wantMocks func(args args, md *mockedDependencies)
		want      *model.TokenStore
	}{
		{
			name: "success - found in cache",
			args: args{
				ctx:            ctx,
				actorId:        t1Actor.ActorID,
				tokenType:      t1Actor.TokenType,
				includeDeleted: false,
				params: &model.GetLatestTokenByActorIdAndTokenTypeParams{
					UseCacheOnly: commontypes.BoolToBooleanEnum(true),
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockSubjectCacheDao.EXPECT().GetToken(args.ctx, args.actorId, authTokenPb.SubjectType_SUBJECT_TYPE_ACTOR_ID, args.tokenType).Return(t1Actor, nil)
			},
			want:    t1Actor,
			wantErr: nil,
		},
		{
			name: "cache error - use cache only option",
			args: args{
				ctx:            ctx,
				actorId:        t1Actor.ActorID,
				tokenType:      t1Actor.TokenType,
				includeDeleted: false,
				params: &model.GetLatestTokenByActorIdAndTokenTypeParams{
					UseCacheOnly: commontypes.BoolToBooleanEnum(true),
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockSubjectCacheDao.EXPECT().GetToken(args.ctx, args.actorId, authTokenPb.SubjectType_SUBJECT_TYPE_ACTOR_ID, args.tokenType).Return(nil, fmt.Errorf("redis error"))
			},
			want:    nil,
			wantErr: epifierrors.ErrRecordNotFound,
		},
		{
			name: "cache error + db error",
			args: args{
				ctx:            ctx,
				actorId:        t1Actor.ActorID,
				tokenType:      t1Actor.TokenType,
				includeDeleted: false,
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockSubjectCacheDao.EXPECT().GetToken(args.ctx, args.actorId, authTokenPb.SubjectType_SUBJECT_TYPE_ACTOR_ID, args.tokenType).Return(nil, fmt.Errorf("redis error"))
				md.mockDBV2.EXPECT().GetLatestTokenByActorIdAndTokenType(ctx, args.actorId, args.tokenType, args.includeDeleted).Return(nil, fmt.Errorf("db error"))
			},
			want:    nil,
			wantErr: fmt.Errorf("db error"),
		},
		{
			name: "success - cache error + found in db",
			args: args{
				ctx:            ctx,
				actorId:        t1Actor.ActorID,
				tokenType:      t1Actor.TokenType,
				includeDeleted: false,
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockSubjectCacheDao.EXPECT().GetToken(args.ctx, args.actorId, authTokenPb.SubjectType_SUBJECT_TYPE_ACTOR_ID, args.tokenType).Return(nil, fmt.Errorf("redis error"))
				md.mockDBV2.EXPECT().GetLatestTokenByActorIdAndTokenType(ctx, args.actorId, args.tokenType, args.includeDeleted).Return(t1Actor, nil)
			},
			want:    t1Actor,
			wantErr: nil,
		},
		{
			name: "success - cache miss + found in db",
			args: args{
				ctx:            ctx,
				actorId:        t1Actor.ActorID,
				tokenType:      t1Actor.TokenType,
				includeDeleted: false,
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockSubjectCacheDao.EXPECT().GetToken(args.ctx, args.actorId, authTokenPb.SubjectType_SUBJECT_TYPE_ACTOR_ID, args.tokenType).Return(nil, gorm.ErrRecordNotFound)
				md.mockDBV2.EXPECT().GetLatestTokenByActorIdAndTokenType(ctx, args.actorId, args.tokenType, args.includeDeleted).Return(t1Actor, nil)
			},
			want:    t1Actor,
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()

			mockSubjectCacheDao := mockAuthDao.NewMockTokenStoresSubjectCacheDao(ctrl)
			mockDbDaoV2 := mockAuthDao.NewMockTokenStoresDBDaoV2(ctrl)

			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, &mockedDependencies{
					mockSubjectCacheDao: mockSubjectCacheDao,
					mockDBV2:            mockDbDaoV2,
				})
			}
			tokenStoresDaoV2 := dao.NewTokenStoresDaoV2Impl(gconf.TokenStoresCacheConfig(), mockDbDaoV2, nil, mockSubjectCacheDao)
			got, err := tokenStoresDaoV2.GetLatestTokenByActorIdAndTokenType(tt.args.ctx, tt.args.actorId, tt.args.tokenType, tt.args.includeDeleted, tt.args.params)
			if tt.wantErr != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("unexpected error \nwant: %v\n got: %v\n", tt.wantErr, err)
			}

			assertTokenStoreModel(t, got, tt.want)
		})
	}
}

func TestTokenStoresDaoV2Impl_GetTokensByPhoneNumber(t *testing.T) {
	var (
		ctx = context.Background()
	)

	t1Actor := newToken("1", 1231231231)
	t1Actor.SubjectValue = "1"
	t1Actor.SubjectType = authTokenPb.SubjectType_SUBJECT_TYPE_ACTOR_ID
	t1Actor.CreatedAt = time.Now()
	t1Actor.UpdatedAt = t1Actor.CreatedAt
	t1Actor.TokenType = authPb.TokenType_ACCESS_TOKEN

	type args struct {
		ctx            context.Context
		ph             *commontypes.PhoneNumber
		tokenType      []authPb.TokenType
		includeDeleted bool
		size           uint
		params         []*model.GetTokensByPhoneNumberParams
	}

	tests := []struct {
		name      string
		args      args
		wantErr   error
		wantMocks func(args args, md *mockedDependencies)
		want      []*model.TokenStore
	}{
		{
			name: "db error",
			args: args{
				ctx:            ctx,
				tokenType:      []authPb.TokenType{t1Actor.TokenType},
				ph:             t1Actor.PhoneNumber,
				includeDeleted: true,
				size:           50,
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().GetTokensByPhoneNumber(args.ctx, args.ph, args.tokenType, args.includeDeleted, args.size).Return(nil, fmt.Errorf("db error"))
			},
			want:    nil,
			wantErr: fmt.Errorf("db error"),
		},
		{
			name: "success",
			args: args{
				ctx:            ctx,
				tokenType:      []authPb.TokenType{t1Actor.TokenType},
				ph:             t1Actor.PhoneNumber,
				includeDeleted: true,
				size:           50,
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().GetTokensByPhoneNumber(args.ctx, args.ph, args.tokenType, args.includeDeleted, args.size).Return([]*model.TokenStore{t1Actor}, nil)
			},
			want:    []*model.TokenStore{t1Actor},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()

			mockDbDao := mockAuthDao.NewMockTokenStoresDBDaoV2(ctrl)

			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, &mockedDependencies{
					mockDBV2: mockDbDao,
				})
			}
			tokenStoresDaoV2 := dao.NewTokenStoresDaoV2Impl(gconf.TokenStoresCacheConfig(), mockDbDao, nil, nil)
			got, err := tokenStoresDaoV2.GetTokensByPhoneNumber(tt.args.ctx, tt.args.ph, tt.args.tokenType, tt.args.includeDeleted, tt.args.size, tt.args.params...)
			if tt.wantErr != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("unexpected error \nwant: %v\n got: %v\n", tt.wantErr, err)
			}
			for idx, gotToken := range got {
				if idx >= len(tt.want) {
					t.Errorf("unexpected error \nwant: %v\n got: %v\n", tt.want, got)
				}
				assertTokenStoreModel(t, gotToken, tt.want[idx])
			}
		})
	}
}

func TestTokenStoresDaoV2Impl_ExpireTokenByActorId(t *testing.T) {
	var (
		ctx = context.Background()
	)

	t1Actor := newToken("1", 1231231231)
	t1Actor.SubjectValue = "1"
	t1Actor.SubjectType = authTokenPb.SubjectType_SUBJECT_TYPE_ACTOR_ID
	t1Actor.CreatedAt = time.Now()
	t1Actor.UpdatedAt = t1Actor.CreatedAt
	t1Actor.TokenType = authPb.TokenType_ACCESS_TOKEN

	type args struct {
		ctx               context.Context
		actorId           string
		idNotToBeExpired  string
		tokenType         authPb.TokenType
		deletionReason    authPb.TokenDeletionReason
		deviceRegStatuses []authPb.DeviceRegistrationStatus
		params            []*model.ExpireTokensByActorIdParams
	}
	type testParams struct {
		// to avoid model asserts when there is no DB call, we assert for RNF error
		ignoreDBAssert           bool
		disableCachingByTokenId  bool
		enableCachingBySubjectId bool
	}
	tests := []struct {
		name       string
		args       args
		wantErr    error
		wantMocks  func(args args, md *mockedDependencies)
		want       []*model.TokenStore
		testParams *testParams
	}{
		{
			name: "expire token : db error",
			args: args{
				ctx:               ctx,
				actorId:           t1Actor.ActorID,
				tokenType:         t1Actor.TokenType,
				deletionReason:    authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				deviceRegStatuses: []authPb.DeviceRegistrationStatus{authPb.DeviceRegistrationStatus_REGISTERED},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireTokenByActorId(args.ctx, args.actorId, args.idNotToBeExpired, args.tokenType, args.deletionReason, args.deviceRegStatuses).Return(nil, fmt.Errorf("db error"))
			},
			want:       nil,
			wantErr:    fmt.Errorf("db error"),
			testParams: &testParams{},
		},
		{
			name: "expire token : error in expiring from cache by id",
			args: args{
				ctx:               ctx,
				actorId:           t1Actor.ActorID,
				tokenType:         t1Actor.TokenType,
				deletionReason:    authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				deviceRegStatuses: []authPb.DeviceRegistrationStatus{authPb.DeviceRegistrationStatus_REGISTERED},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireTokenByActorId(args.ctx, args.actorId, args.idNotToBeExpired, args.tokenType, args.deletionReason, args.deviceRegStatuses).Return([]*model.TokenStore{t1Actor}, nil)
				md.mockIdCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.ID).Return(fmt.Errorf("error in redis"))
			},
			want:       nil,
			wantErr:    fmt.Errorf("error in expiring token from cache : error in redis"),
			testParams: &testParams{},
		},
		{
			name: "expire token : error in expiring from cache by subject",
			args: args{
				ctx:               ctx,
				actorId:           t1Actor.ActorID,
				tokenType:         t1Actor.TokenType,
				deletionReason:    authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				deviceRegStatuses: []authPb.DeviceRegistrationStatus{authPb.DeviceRegistrationStatus_REGISTERED},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireTokenByActorId(args.ctx, args.actorId, args.idNotToBeExpired, args.tokenType, args.deletionReason, args.deviceRegStatuses).Return([]*model.TokenStore{t1Actor}, nil)
				md.mockSubjectCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.ActorID, authTokenPb.SubjectType_SUBJECT_TYPE_ACTOR_ID, args.tokenType).Return(fmt.Errorf("error in redis"))
			},
			want:    nil,
			wantErr: fmt.Errorf("error in expiring token from cache : error in redis"),
			testParams: &testParams{
				enableCachingBySubjectId: true,
			},
		},
		{
			name: "expire token : successful + disableCachingByTokenId: true",
			args: args{
				ctx:               ctx,
				actorId:           t1Actor.ActorID,
				tokenType:         t1Actor.TokenType,
				deletionReason:    authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				deviceRegStatuses: []authPb.DeviceRegistrationStatus{authPb.DeviceRegistrationStatus_REGISTERED},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireTokenByActorId(args.ctx, args.actorId, args.idNotToBeExpired, args.tokenType, args.deletionReason, args.deviceRegStatuses).Return([]*model.TokenStore{t1Actor}, nil)
				md.mockSubjectCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.ActorID, authTokenPb.SubjectType_SUBJECT_TYPE_ACTOR_ID, args.tokenType).Return(nil)
			},
			want:    []*model.TokenStore{t1Actor},
			wantErr: nil,
			testParams: &testParams{
				disableCachingByTokenId:  true,
				enableCachingBySubjectId: true,
			},
		},
		{
			name: "expire token : successful + enableCachingBySubjectId: false",
			args: args{
				ctx:               ctx,
				actorId:           t1Actor.ActorID,
				tokenType:         t1Actor.TokenType,
				deletionReason:    authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				deviceRegStatuses: []authPb.DeviceRegistrationStatus{authPb.DeviceRegistrationStatus_REGISTERED},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireTokenByActorId(args.ctx, args.actorId, args.idNotToBeExpired, args.tokenType, args.deletionReason, args.deviceRegStatuses).Return([]*model.TokenStore{t1Actor}, nil)
				md.mockIdCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.ID).Return(nil)
			},
			want:    []*model.TokenStore{t1Actor},
			wantErr: nil,
			testParams: &testParams{
				enableCachingBySubjectId: false,
			},
		},
		{
			name: "expire token : successful",
			args: args{
				ctx:               ctx,
				actorId:           t1Actor.ActorID,
				tokenType:         t1Actor.TokenType,
				deletionReason:    authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				deviceRegStatuses: []authPb.DeviceRegistrationStatus{authPb.DeviceRegistrationStatus_REGISTERED},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireTokenByActorId(args.ctx, args.actorId, args.idNotToBeExpired, args.tokenType, args.deletionReason, args.deviceRegStatuses).Return([]*model.TokenStore{t1Actor}, nil)
				md.mockIdCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.ID).Return(nil)
				md.mockSubjectCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.ActorID, authTokenPb.SubjectType_SUBJECT_TYPE_ACTOR_ID, args.tokenType).Return(nil)
			},
			want:    []*model.TokenStore{t1Actor},
			wantErr: nil,
			testParams: &testParams{
				enableCachingBySubjectId: true,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()

			_ = gconf.TokenStoresCacheConfig().SetIsCachingEnabled(true, true, nil)
			_ = gconf.TokenStoresCacheConfig().SetDisableCachingByTokenIdForAccessTokens(tt.testParams.disableCachingByTokenId, true, nil)
			_ = gconf.TokenStoresCacheConfig().SetEnableCachingBySubject(tt.testParams.enableCachingBySubjectId, true, nil)

			mockDbDao := mockAuthDao.NewMockTokenStoresDBDaoV2(ctrl)
			mockIdCacheDao := mockAuthDao.NewMockTokenStoresIdCacheDao(ctrl)
			mockSubjectCacheDao := mockAuthDao.NewMockTokenStoresSubjectCacheDao(ctrl)

			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, &mockedDependencies{
					mockDBV2:            mockDbDao,
					mockIdCacheDao:      mockIdCacheDao,
					mockSubjectCacheDao: mockSubjectCacheDao,
				})
			}
			tokenStoresDaoV2 := dao.NewTokenStoresDaoV2Impl(gconf.TokenStoresCacheConfig(), mockDbDao, mockIdCacheDao, mockSubjectCacheDao)
			got, err := tokenStoresDaoV2.ExpireTokenByActorId(tt.args.ctx, tt.args.actorId, tt.args.idNotToBeExpired, tt.args.tokenType, tt.args.deletionReason, tt.args.deviceRegStatuses, tt.args.params...)
			if tt.wantErr != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("unexpected error \nwant: %v\n got: %v\n", tt.wantErr, err)
			}
			assert.Equal(t, got, tt.want)
		})
	}
}

func TestTokenStoresDaoV2Impl_ExpireTokenByPhoneNumber(t *testing.T) {
	var (
		ctx = context.Background()
	)

	t1Actor := newToken("1", 1231231231)
	t1Actor.SubjectValue = "1"
	t1Actor.SubjectType = authTokenPb.SubjectType_SUBJECT_TYPE_ACTOR_ID
	t1Actor.CreatedAt = time.Now()
	t1Actor.UpdatedAt = t1Actor.CreatedAt
	t1Actor.TokenType = authPb.TokenType_ACCESS_TOKEN

	type args struct {
		ctx            context.Context
		tokenType      authPb.TokenType
		status         model.TokenStatus
		phoneNum       *commontypes.PhoneNumber
		deletionReason authPb.TokenDeletionReason
		exceptIds      []string
		params         []*model.ExpireTokensByPhoneNumberParams
	}
	type testParams struct {
		// to avoid model asserts when there is no DB call, we assert for RNF error
		ignoreDBAssert           bool
		disableCachingByTokenId  bool
		enableCachingBySubjectId bool
	}
	tests := []struct {
		name       string
		args       args
		wantErr    error
		wantMocks  func(args args, md *mockedDependencies)
		want       []*model.TokenStore
		testParams *testParams
	}{
		{
			name: "expire token : db error",
			args: args{
				ctx:            ctx,
				tokenType:      t1Actor.TokenType,
				status:         model.TokenExpired,
				phoneNum:       t1Actor.PhoneNumber,
				deletionReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				exceptIds:      []string{},
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireTokenByPhoneNumber(args.ctx, args.tokenType, args.status, args.phoneNum, args.deletionReason, args.exceptIds).Return(nil, fmt.Errorf("db error"))
			},
			want:       nil,
			wantErr:    fmt.Errorf("db error"),
			testParams: &testParams{},
		},
		{
			name: "expire token : error in expiring from cache by id",
			args: args{
				ctx:            ctx,
				tokenType:      t1Actor.TokenType,
				status:         model.TokenExpired,
				phoneNum:       t1Actor.PhoneNumber,
				deletionReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				exceptIds:      []string{},
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireTokenByPhoneNumber(args.ctx, args.tokenType, args.status, args.phoneNum, args.deletionReason, args.exceptIds).Return([]*model.TokenStore{t1Actor}, nil)
				md.mockIdCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.ID).Return(fmt.Errorf("error in redis"))
			},
			want:       nil,
			wantErr:    fmt.Errorf("error in expiring token from cache : error in redis"),
			testParams: &testParams{},
		},
		{
			name: "expire token : error in expiring from cache by subject",
			args: args{
				ctx:            ctx,
				tokenType:      t1Actor.TokenType,
				status:         model.TokenExpired,
				phoneNum:       t1Actor.PhoneNumber,
				deletionReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				exceptIds:      []string{},
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireTokenByPhoneNumber(args.ctx, args.tokenType, args.status, args.phoneNum, args.deletionReason, args.exceptIds).Return([]*model.TokenStore{t1Actor}, nil)
				md.mockSubjectCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.SubjectValue, t1Actor.SubjectType, args.tokenType).Return(fmt.Errorf("error in redis"))
			},
			want:    nil,
			wantErr: fmt.Errorf("error in expiring token from cache : error in redis"),
			testParams: &testParams{
				enableCachingBySubjectId: true,
			},
		},
		{
			name: "expire token : successful + disableCachingByTokenId: true",
			args: args{
				ctx:            ctx,
				tokenType:      t1Actor.TokenType,
				status:         model.TokenExpired,
				phoneNum:       t1Actor.PhoneNumber,
				deletionReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				exceptIds:      []string{},
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireTokenByPhoneNumber(args.ctx, args.tokenType, args.status, args.phoneNum, args.deletionReason, args.exceptIds).Return([]*model.TokenStore{t1Actor}, nil)
				md.mockSubjectCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.SubjectValue, t1Actor.SubjectType, args.tokenType).Return(nil)
			},
			want:    []*model.TokenStore{t1Actor},
			wantErr: nil,
			testParams: &testParams{
				disableCachingByTokenId:  true,
				enableCachingBySubjectId: true,
			},
		},
		{
			name: "expire token : successful + enableCachingBySubjectId: false",
			args: args{
				ctx:            ctx,
				tokenType:      t1Actor.TokenType,
				status:         model.TokenExpired,
				phoneNum:       t1Actor.PhoneNumber,
				deletionReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				exceptIds:      []string{},
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireTokenByPhoneNumber(args.ctx, args.tokenType, args.status, args.phoneNum, args.deletionReason, args.exceptIds).Return([]*model.TokenStore{t1Actor}, nil)
				md.mockIdCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.ID).Return(nil)
			},
			want:    []*model.TokenStore{t1Actor},
			wantErr: nil,
			testParams: &testParams{
				enableCachingBySubjectId: false,
			},
		},
		{
			name: "expire token : successful",
			args: args{
				ctx:            ctx,
				tokenType:      t1Actor.TokenType,
				status:         model.TokenExpired,
				phoneNum:       t1Actor.PhoneNumber,
				deletionReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
				exceptIds:      []string{},
				params:         nil,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDBV2.EXPECT().ExpireTokenByPhoneNumber(args.ctx, args.tokenType, args.status, args.phoneNum, args.deletionReason, args.exceptIds).Return([]*model.TokenStore{t1Actor}, nil)
				md.mockIdCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.ID).Return(nil)
				md.mockSubjectCacheDao.EXPECT().ExpireToken(args.ctx, t1Actor.SubjectValue, t1Actor.SubjectType, args.tokenType).Return(nil)
			},
			want:    []*model.TokenStore{t1Actor},
			wantErr: nil,
			testParams: &testParams{
				enableCachingBySubjectId: true,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()

			_ = gconf.TokenStoresCacheConfig().SetIsCachingEnabled(true, true, nil)
			_ = gconf.TokenStoresCacheConfig().SetDisableCachingByTokenIdForAccessTokens(tt.testParams.disableCachingByTokenId, true, nil)
			_ = gconf.TokenStoresCacheConfig().SetEnableCachingBySubject(tt.testParams.enableCachingBySubjectId, true, nil)

			mockDbDao := mockAuthDao.NewMockTokenStoresDBDaoV2(ctrl)
			mockIdCacheDao := mockAuthDao.NewMockTokenStoresIdCacheDao(ctrl)
			mockSubjectCacheDao := mockAuthDao.NewMockTokenStoresSubjectCacheDao(ctrl)

			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, &mockedDependencies{
					mockDBV2:            mockDbDao,
					mockIdCacheDao:      mockIdCacheDao,
					mockSubjectCacheDao: mockSubjectCacheDao,
				})
			}
			tokenStoresDaoV2 := dao.NewTokenStoresDaoV2Impl(gconf.TokenStoresCacheConfig(), mockDbDao, mockIdCacheDao, mockSubjectCacheDao)
			got, err := tokenStoresDaoV2.ExpireTokenByPhoneNumber(tt.args.ctx, tt.args.tokenType, tt.args.status, tt.args.phoneNum, tt.args.deletionReason, tt.args.exceptIds, tt.args.params...)
			if tt.wantErr != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("unexpected error \nwant: %v\n got: %v\n", tt.wantErr, err)
			}
			assert.Equal(t, got, tt.want)
		})
	}
}
