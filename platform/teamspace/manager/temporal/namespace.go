package temporal

import (
	"context"
	"errors"
	"time"

	temporalEnumsPb "go.temporal.io/api/enums/v1"
	"go.temporal.io/api/serviceerror"
	temporalWorkflowPb "go.temporal.io/api/workflowservice/v1"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/durationpb"

	"github.com/epifi/be-common/pkg/cfg"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	epifitemporalClient "github.com/epifi/be-common/pkg/epifitemporal/client"
	"github.com/epifi/be-common/pkg/epifitemporal/namespace"
	"github.com/epifi/be-common/pkg/logger"
)

func SetupWorkerNamespaces() error {
	temporalClientInitConf := &cfg.TemporalClientInitOptions{
		TemporalCodecAesKey: "deploy/temporal/codec-encryption-key",
		UseMigrationCluster: false,
	}

	err := cfg.LoadAllSecretsV3(temporalClientInitConf, "qa", "ap-south-1")
	if err != nil {
		logger.ErrorNoCtx("failed to load secrets", zap.Error(err))
		return err
	}
	gconf, _ := gencfg.NewTemporalClientInitOptions()
	err = gconf.Set(temporalClientInitConf, false, nil)
	if err != nil {
		logger.ErrorNoCtx("failed to create genconf from static config", zap.Error(err))
		return err
	}
	for _, namespace := range namespace.Namespaces() {
		name, err := namespace.GetName()
		if err != nil {
			return err
		}
		cl, err := epifitemporalClient.NewNamespaceClient("default", gconf)
		if err != nil {
			logger.ErrorNoCtx("failed to initialise default client options", zap.Error(err))
			return err
		}
		dur := time.Hour * 24
		err = cl.Register(context.Background(), &temporalWorkflowPb.RegisterNamespaceRequest{
			Namespace:                        name,
			Description:                      "namespace created to run smoke test automation",
			OwnerEmail:                       "<EMAIL>",
			WorkflowExecutionRetentionPeriod: durationpb.New(dur),
			HistoryArchivalState:             temporalEnumsPb.ARCHIVAL_STATE_DISABLED,
		})
		nsAlreadyExists := &serviceerror.NamespaceAlreadyExists{}
		if err != nil && !errors.As(err, &nsAlreadyExists) {
			logger.ErrorNoCtx("failed to register namespace", zap.Error(err))
			return err
		}
	}
	return nil
}
