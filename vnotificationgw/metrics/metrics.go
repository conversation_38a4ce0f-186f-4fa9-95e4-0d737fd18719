package metrics

import (
	"strconv"

	"github.com/prometheus/client_golang/prometheus"
)

const (
	namespace = "vngw"
)

var vendorNotificationGwMetricsRecorder = newVendorNotificationGwMetricsRecorder()

// VendorNotificationGwMetrics represents a collection of metrics to be registered on a
// Prometheus metrics registry for VendorNotificationGw service custom-metrics.
type vendorNotificationGwMetrics struct {
	// counter to measure count of errors during enach callback decryption in vendor notification gateway
	enachCallbackDecryptionErrorCounter *prometheus.CounterVec
	httpRequestsCounter                 *prometheus.CounterVec
}

func newVendorNotificationGwMetricsRecorder() *vendorNotificationGwMetrics {
	vnGwMetrics := &vendorNotificationGwMetrics{
		enachCallbackDecryptionErrorCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Name:      "enach_callback_decryption_err_total",
				Help:      "Total count of decryption errors which decrypting enach callback payload",
			},
			[]string{},
		),
		httpRequestsCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Name:      "http_requests_count",
				Help:      "Total number of HTTP requests with method, url, and status code.",
			},
			[]string{"method", "path", "status_code"},
		),
	}

	prometheus.MustRegister(vnGwMetrics.enachCallbackDecryptionErrorCounter)
	prometheus.MustRegister(vnGwMetrics.httpRequestsCounter)

	// initialise metrics to 0
	initialise(vnGwMetrics)

	return vnGwMetrics
}

// initialise metrics to 0
func initialise(vm *vendorNotificationGwMetrics) {
	vm.enachCallbackDecryptionErrorCounter.WithLabelValues()
}

func RecordEnachCallbackDecryptionError() {
	vendorNotificationGwMetricsRecorder.enachCallbackDecryptionErrorCounter.WithLabelValues().Inc()
}
func RecordHttpRequestsDetail(method, path string, statusCode int) {
	vendorNotificationGwMetricsRecorder.httpRequestsCounter.With(prometheus.Labels{"method": method, "path": path, "status_code": strconv.Itoa(statusCode)}).Inc()
}
