package dao

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epifierrors"

	pb "github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/card/dao/model"
)

var physicalCardDispatchRequestColumnNameMap = map[pb.PhysicalCardDispatchRequestFieldMask]string{
	pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_CARD_ID:                 "card_id",
	pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_STATE:                   "state",
	pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_RETRIES:                 "num_retries",
	pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_REQUEST_ID:              "request_id",
	pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_FAILURE_RESPONSE_CODE:   "failure_response_code",
	pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_FAILURE_RESPONSE_REASON: "failure_response_reason",
	pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_ID:                      "id",
	pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_CREATED_AT:              "created_at",
	pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_UPDATED_AT:              "updated_at",
	pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_DELETED_AT:              "deleted_at",
	pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_CLIENT_REQ_ID:           "client_req_id",
	pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_DETAILS:                 "details",
	pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_SUB_STATUS:              "sub_status",
	pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_CURRENT_STAGE:           "current_stage",
}

var physicalCardDispatchRequestUnspecifiedFieldFilter = func(field pb.PhysicalCardDispatchRequestFieldMask) bool {
	return pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_FIELD_MASK_UNSPECIFIED != field
}

type PhysicalCardDispatchRequestDaoCRDB struct {
	db *gorm.DB
}

var _ PhysicalCardDispatchRequestDao = &PhysicalCardDispatchRequestDaoCRDB{}

// Factory method for creating an instance of PhysicalCardDispatchRequestDao. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewPhysicalCardDispatchRequestDaoCRDB(db DebitCardPGDB) *PhysicalCardDispatchRequestDaoCRDB {
	return &PhysicalCardDispatchRequestDaoCRDB{db: db}
}

// nolint:dupl
func (ccd *PhysicalCardDispatchRequestDaoCRDB) Create(ctx context.Context, physicalCardDispatchRequest *pb.PhysicalCardDispatchRequest) (*pb.PhysicalCardDispatchRequest, error) {
	defer metric_util.TrackDuration("card/dao", "PhysicalCardDispatchRequestDaoCRDB", "Create", time.Now())
	// If ctx is carrying the gorm transaction connection object we will use it for db operation
	// if not, use the gorm.DB connection object in DAO service
	db := gormctxv2.FromContextOrDefault(ctx, ccd.db)

	physicalCardDispatchRequestModel := ccd.convertToModel(physicalCardDispatchRequest)
	res := db.Create(physicalCardDispatchRequestModel)
	if res.Error != nil {
		return nil, res.Error
	}
	return ccd.convertToProto(physicalCardDispatchRequestModel), nil
}

func (ccd *PhysicalCardDispatchRequestDaoCRDB) GetById(ctx context.Context, id string, selectFieldMasks []pb.PhysicalCardDispatchRequestFieldMask) (*pb.PhysicalCardDispatchRequest, error) {
	defer metric_util.TrackDuration("card/dao", "PhysicalCardDispatchRequestDaoCRDB", "GetById", time.Now())
	if id == "" {
		return nil, fmt.Errorf("id can't be empty %w", epifierrors.ErrInvalidArgument)
	}
	db := gormctxv2.FromContextOrDefault(ctx, ccd.db)
	var selectedColumns []string
	if len(selectFieldMasks) == 0 {
		selectedColumns = getAllPhysicalCardDispatchRequestColumnsForFieldMask()
	} else {
		selectedColumns = getSelectColumnsForPhysicalCardDispatchRequest(selectFieldMasks)
	}

	var mdl model.PhysicalCardDispatchRequest
	err := db.Where(&model.PhysicalCardDispatchRequest{Id: id}).Select(selectedColumns).Take(&mdl).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch physical card dispatch request: %w", err)
	}
	return ccd.convertToProto(&mdl), nil
}

func (ccd *PhysicalCardDispatchRequestDaoCRDB) GetByCardId(ctx context.Context, cardId string, selectFieldMasks []pb.PhysicalCardDispatchRequestFieldMask, options ...FilterOption) ([]*pb.PhysicalCardDispatchRequest, error) {
	defer metric_util.TrackDuration("card/dao", "PhysicalCardDispatchRequestDaoCRDB", "GetByCardId", time.Now())
	var (
		resp                                = make([]*pb.PhysicalCardDispatchRequest, 0)
		fetchedPhysicalCardDispatchRequests []*model.PhysicalCardDispatchRequest
	)
	if cardId == "" {
		return nil, fmt.Errorf("card id can't be empty %w", epifierrors.ErrInvalidArgument)
	}
	// If ctx is carrying the gorm transaction connection object we will use it for db operation
	// if not, use the gorm.DB connection object in DAO service
	db := gormctxv2.FromContextOrDefault(ctx, ccd.db)
	var selectedColumns []string
	if len(selectFieldMasks) == 0 {
		selectedColumns = getAllPhysicalCardDispatchRequestColumnsForFieldMask()
	} else {
		selectedColumns = getSelectColumnsForPhysicalCardDispatchRequest(selectFieldMasks)
	}
	db = db.Model(&model.PhysicalCardDispatchRequest{}).Select(selectedColumns).Where("card_id = ?", cardId)
	for _, opt := range options {
		db = opt.applyInGorm(db)
	}
	err := db.Find(&fetchedPhysicalCardDispatchRequests).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch physical card dispatch requests: %w", err)
	}
	if len(fetchedPhysicalCardDispatchRequests) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	for _, m := range fetchedPhysicalCardDispatchRequests {
		proto := ccd.convertToProto(m)
		resp = append(resp, proto)
	}
	return resp, nil
}

func (ccd *PhysicalCardDispatchRequestDaoCRDB) GetByRequestId(ctx context.Context, requestId string, selectFieldMasks ...pb.PhysicalCardDispatchRequestFieldMask) (*pb.PhysicalCardDispatchRequest, error) {
	defer metric_util.TrackDuration("card/dao", "PhysicalCardDispatchRequestDaoCRDB", "GetByRequestId", time.Now())
	if requestId == "" {
		return nil, fmt.Errorf("request Id can't be empty %w", epifierrors.ErrInvalidArgument)
	}
	// If ctx is carrying the gorm transaction connection object we will use it for db operation
	// if not, use the gorm.DB connection object in DAO service
	db := gormctxv2.FromContextOrDefault(ctx, ccd.db)

	mdl := &model.PhysicalCardDispatchRequest{}
	var selectedColumns []string
	if len(selectFieldMasks) == 0 {
		selectedColumns = getAllPhysicalCardDispatchRequestColumnsForFieldMask()
	} else {
		selectedColumns = getSelectColumnsForPhysicalCardDispatchRequest(selectFieldMasks)
	}
	res := db.Model(mdl).Select(selectedColumns).Where("request_id = ?", requestId).Take(mdl)
	if res.Error != nil {
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, res.Error
	}
	return ccd.convertToProto(mdl), nil
}

// UpdateIfStateMatches updates various physicalCardDispatchRequest fields along with request status.
// To avoid stale read updates without the need to acquire explicit locks, an update is performed only if
// the current state matches.
// nolint: dupl
func (ccd *PhysicalCardDispatchRequestDaoCRDB) UpdateIfStateMatches(ctx context.Context, physicalCardDispatchRequest *pb.PhysicalCardDispatchRequest,
	updateMask []pb.PhysicalCardDispatchRequestFieldMask, currentState, nextState pb.RequestState) error {
	defer metric_util.TrackDuration("card/dao", "PhysicalCardDispatchRequestDaoCRDB", "UpdateIfStateMatches", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, ccd.db)

	if physicalCardDispatchRequest.Id == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation %w", epifierrors.ErrInvalidArgument)
	}

	updateMask = filterPhysicalCardDispatchRequestFieldMaskSlice(updateMask, physicalCardDispatchRequestUnspecifiedFieldFilter)

	mdl := ccd.convertToModel(physicalCardDispatchRequest)

	if nextState != pb.RequestState_REQUEST_STATE_UNSPECIFIED {
		mdl.State = nextState
		updateMask = append(updateMask, pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_STATE)
	}
	if len(updateMask) == 0 {
		return fmt.Errorf("update mask can't be empty %w", epifierrors.ErrInvalidArgument)
	}
	updateColumns := getSelectColumnsForPhysicalCardDispatchRequest(updateMask)

	res := db.Model(mdl).Where("state = ?", currentState).Select(updateColumns).Updates(mdl)
	if res.Error != nil {
		return fmt.Errorf("unable to update physical card dispatch request %w", res.Error)
	}

	if res.RowsAffected == 0 {
		return fmt.Errorf("unable to update  physical card dispatch request")
	}

	return nil
}

// GetBetweenUpdatedAtInBatches runs a key set paginated query.
func (ccd *PhysicalCardDispatchRequestDaoCRDB) GetBetweenUpdatedAtInBatches(ctx context.Context, lastSeenId string, fromTime *time.Time, toTime *time.Time, batchSize int) ([]*pb.PhysicalCardDispatchRequest, error) {
	defer metric_util.TrackDuration("card/dao", "PhysicalCardDispatchRequestDaoCRDB", "GetBetweenUpdatedAtInBatches", time.Now())

	if fromTime.IsZero() || toTime.IsZero() {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "from time or to time cannot be zero")
	}
	if batchSize < 1 {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "limit cannot be less than 1")
	}
	db := gormctxv2.FromContextOrDefault(ctx, ccd.db)

	var dispatchRequests []*model.PhysicalCardDispatchRequest
	if lastSeenId == "" {
		if err := db.Where("updated_at >= ? and updated_at < ?", fromTime, toTime).Order("updated_at, id").Limit(batchSize).Find(&dispatchRequests).Error; err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "no records found")
			}
			return nil, errors.Wrap(err, "error fetching physical card dispatch requests")
		}
	} else {
		if err := db.Where("(updated_at,id) > (?,?) and updated_at < ?", fromTime, lastSeenId, toTime).Order("updated_at,id").Limit(batchSize).Find(&dispatchRequests).Error; err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "no records found")
			}
			return nil, errors.Wrap(err, "error fetching physical card dispatch requests")
		}
	}
	if len(dispatchRequests) == 0 {
		return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "no records found")
	}

	dispatchRequestsProto := make([]*pb.PhysicalCardDispatchRequest, 0)
	for _, mdl := range dispatchRequests {
		dispatchRequestsProto = append(dispatchRequestsProto, ccd.convertToProto(mdl))
	}

	return dispatchRequestsProto, nil
}

func (ccd *PhysicalCardDispatchRequestDaoCRDB) convertToModel(proto *pb.PhysicalCardDispatchRequest) *model.PhysicalCardDispatchRequest {
	mdl := &model.PhysicalCardDispatchRequest{
		Id:                    proto.GetId(),
		CardId:                proto.GetCardId(),
		State:                 proto.GetState(),
		RequestId:             proto.GetRequestId(),
		NumRetries:            proto.GetRetries(),
		FailureResponseCode:   proto.GetFailureResponseCode(),
		FailureResponseReason: proto.GetFailureResponseReason(),
		ClientReqId:           proto.GetClientReqId(),
		Details:               proto.GetDetails(),
		SubStatus:             proto.GetSubStatus(),
		CurrentStage:          proto.GetCurrentStage(),
	}
	if proto.GetCreatedAt() != nil {
		mdl.CreatedAt = proto.GetCreatedAt().AsTime()
	}
	if proto.GetUpdatedAt() != nil {
		mdl.UpdatedAt = proto.GetUpdatedAt().AsTime()
	}
	return mdl
}

func (ccd *PhysicalCardDispatchRequestDaoCRDB) convertToProto(mdl *model.PhysicalCardDispatchRequest) *pb.PhysicalCardDispatchRequest {
	var (
		deletedAt *timestampPb.Timestamp
	)
	if mdl.DeletedAt.Valid {
		deletedAt = timestampPb.New(mdl.DeletedAt.Time)
	}
	return &pb.PhysicalCardDispatchRequest{
		Id:                    mdl.Id,
		CardId:                mdl.CardId,
		State:                 mdl.State,
		Retries:               mdl.NumRetries,
		RequestId:             mdl.RequestId,
		CreatedAt:             timestampPb.New(mdl.CreatedAt),
		UpdatedAt:             timestampPb.New(mdl.UpdatedAt),
		FailureResponseCode:   mdl.FailureResponseCode,
		FailureResponseReason: mdl.FailureResponseReason,
		ClientReqId:           mdl.ClientReqId,
		Details:               mdl.Details,
		DeletedAt:             deletedAt,
		SubStatus:             mdl.SubStatus,
		CurrentStage:          mdl.CurrentStage,
	}
}

// filterPhysicalCardDispatchRequestFieldMaskSlice filters out elements from a slice based on the check function passed in arg
// elements are filtered out if they dont pass the check function
// NOTE- func returns a new copy of the slice. No changes are made to the existing slice
func filterPhysicalCardDispatchRequestFieldMaskSlice(fieldMasks []pb.PhysicalCardDispatchRequestFieldMask, check func(field pb.PhysicalCardDispatchRequestFieldMask) bool) []pb.PhysicalCardDispatchRequestFieldMask {
	var ret []pb.PhysicalCardDispatchRequestFieldMask
	for _, fieldMask := range fieldMasks {
		if check(fieldMask) {
			ret = append(ret, fieldMask)
		}
	}
	return ret
}

// getSelectColumnsForPhysicalCardDispatchRequest converts update mask to string slice with column name corresponding to field name enums
func getSelectColumnsForPhysicalCardDispatchRequest(fieldMasks []pb.PhysicalCardDispatchRequestFieldMask) []string {
	var selectColumns []string

	for _, field := range fieldMasks {
		selectColumns = append(selectColumns, physicalCardDispatchRequestColumnNameMap[field])
	}
	return selectColumns
}

// getAllPhysicalCardDispatchRequestColumnsForFieldMask converts all field mask enums to column_name string values array
func getAllPhysicalCardDispatchRequestColumnsForFieldMask() []string {
	var selectColumns []string
	for _, columnName := range physicalCardDispatchRequestColumnNameMap {
		selectColumns = append(selectColumns, columnName)
	}
	return selectColumns
}
