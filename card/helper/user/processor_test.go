package user_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"

	userPb "github.com/epifi/gamma/api/user"
	usergroupPb "github.com/epifi/gamma/api/user/group"
	userGroupMocks "github.com/epifi/gamma/api/user/group/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	userProcessor "github.com/epifi/gamma/card/helper/user"
)

func TestProcessor_GetUserGroupsByActorId(t *testing.T) {
	var (
		mockUserClient    *userMocks.MockUsersClient
		mockUserGrpClient *userGroupMocks.MockGroupClient
	)

	tests := []struct {
		name           string
		actorId        string
		setupMockCalls func()
		want           []commontypes.UserGroup
		wantErr        bool
	}{
		{
			name:    "should return user groups successfully",
			actorId: "actor-id",
			setupMockCalls: func() {
				mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: "actor-id"}}).Return(&userPb.GetUserResponse{
					User: &userPb.User{
						Id: "entity-id",
						Profile: &userPb.Profile{
							Email: "email-id",
						},
					},
					Status: rpc.StatusOk(),
				}, nil)
				mockUserGrpClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(),
					&usergroupPb.GetGroupsMappedToEmailRequest{Email: "email-id"}).Return(&usergroupPb.GetGroupsMappedToEmailResponse{
					Status: rpc.StatusOk(),
					Groups: []commontypes.UserGroup{commontypes.UserGroup_INTERNAL},
				}, nil)
			},
			want:    []commontypes.UserGroup{commontypes.UserGroup_INTERNAL},
			wantErr: false,
		},
		{
			name:    "failed to fetch user",
			actorId: "actor-id",
			setupMockCalls: func() {
				mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: "actor-id"}}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name:    "failed to fetch groups mapped to email",
			actorId: "actor-id",
			setupMockCalls: func() {
				mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: "actor-id"}}).Return(&userPb.GetUserResponse{
					User: &userPb.User{
						Id: "entity-id",
						Profile: &userPb.Profile{
							Email: "email-id",
						},
					},
					Status: rpc.StatusOk(),
				}, nil)
				mockUserGrpClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(),
					&usergroupPb.GetGroupsMappedToEmailRequest{Email: "email-id"}).Return(&usergroupPb.GetGroupsMappedToEmailResponse{
					Status: rpc.StatusInvalidArgument(),
				}, nil)
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockUserClient = userMocks.NewMockUsersClient(ctr)
			mockUserGrpClient = userGroupMocks.NewMockGroupClient(ctr)
			processor := userProcessor.NewProcessor(mockUserClient, mockUserGrpClient, nil, nil)
			tt.setupMockCalls()
			got, err := processor.GetUserGroupsByActorId(context.Background(), tt.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserGroupsByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserGroupsByActorId() got = %v, want %v", got, tt.want)
			}
		})
	}
}
