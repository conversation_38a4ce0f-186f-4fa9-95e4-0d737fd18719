// nolint: dupl
package comms

import (
	"context"

	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"

	cardCommsPb "github.com/epifi/gamma/api/card/comms"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/card/helper"
)

type DestNotAvailableRule struct {
	helper *helper.CommsHelper
}

func NewDestNotAvailableRule(helper *helper.CommsHelper) *DestNotAvailableRule {
	return &DestNotAvailableRule{
		helper: helper,
	}
}

func (c *DestNotAvailableRule) GetComms(ctx context.Context, data *cardCommsPb.ActionData) (res []commsPb.CommMessage, resErr error) {
	if c.isApplicable(data) {
		// Pn
		pnTitle, pnBody, err := c.helper.GetPnContentForSwitch(data.GetSwitchNotificationData().GetSwitchNotificationResponse())
		if err != nil {
			logger.Error(ctx, "error in fetching pn content", zap.Error(err))
		} else {
			res = append(res, helper.GetPnWithCommonFields(pnTitle, pnBody, nil),
				helper.GetInAppNotificationWithCommonFields(pnTitle, pnBody))
		}
		res = append(res, &commsPb.SendMessageRequest_Sms{
			Sms: &commsPb.SMSMessage{
				SmsOption: &commsPb.SmsOption{
					Option: &commsPb.SmsOption_DebitCardDestNotAvailableSmsOption{
						DebitCardDestNotAvailableSmsOption: &commsPb.DebitCardDestNotAvailableSmsOption{
							SmsType: commsPb.SmsType_DEBIT_CARD_DEST_NOT_AVAILABLE,
							Option: &commsPb.DebitCardDestNotAvailableSmsOption_DebitCardDestNotAvailableSmsOptionV1{
								DebitCardDestNotAvailableSmsOptionV1: &commsPb.DebitCardDestNotAvailableSmsOptionV1{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
									Merchant:        data.GetSwitchNotificationData().GetMerchant(),
									LastFourDigit:   data.GetSwitchNotificationData().GetLastFourDigits(),
								},
							},
						},
					},
				},
			},
		})
	}

	return
}

func (c *DestNotAvailableRule) isApplicable(data *cardCommsPb.ActionData) bool {
	if data.GetSwitchNotificationData().GetSwitchNotificationResponse() != cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_DEST_NOT_AVAILABLE {
		return false
	}
	return true
}
