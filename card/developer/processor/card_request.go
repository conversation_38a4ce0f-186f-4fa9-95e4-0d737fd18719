package processor

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	devPb "github.com/epifi/gamma/api/card/developer"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/card/dao"
	pkgDev "github.com/epifi/gamma/pkg/developer"
)

type DevCardRequest struct {
	cardRequestDao dao.CardRequestDao
}

func NewDevCardRequest(cardRequestDao dao.CardRequestDao) *DevCardRequest {
	return &DevCardRequest{cardRequestDao: cardRequestDao}
}

func (d *DevCardRequest) FetchParamList(ctx context.Context, entity devPb.CardEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            ActorId,
			Label:           "Actor ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            Workflow,
			Label:           "Workflow",
			Type:            db_state.ParameterDataType_DROPDOWN,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
			Options:         GetCardRequestWorkflowEnums(),
		},
		{
			Name:            OrchId,
			Label:           "Orch ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            CardRequestId,
			Label:           "Card Request ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL},
	}
	return paramList, nil
}

// nolint:funlen
func (d *DevCardRequest) FetchData(ctx context.Context, entity devPb.CardEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}

	var actorId, workflow, orchId, cardRequestId string
	var err error
	var cardRequest *cpPb.CardRequest
	cardRequests := make([]*cpPb.CardRequest, 0)

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case OrchId:
			orchId = filter.GetStringValue()
		case ActorId:
			actorId = filter.GetStringValue()
		case Workflow:
			workflow = filter.GetDropdownValue()
		case CardRequestId:
			cardRequestId = filter.GetStringValue()
		default:
			return "", fmt.Errorf("unknonw param type")
		}
	}

	switch {
	case orchId != "":
		cardRequest, err = d.cardRequestDao.GetByOrchestrationId(ctx, orchId, nil)
		if err != nil {
			logger.Error(ctx, "error fetching card request from db", zap.Error(err))
			return errWithMsg(ctx, []string{"cardRequest", "orchId"}), nil
		}
	case actorId != "" && workflow != "":
		cardRequests, err = d.cardRequestDao.GetByActorIdAndWorkflow(ctx, actorId, cardEnumsPb.CardRequestWorkflow(cardEnumsPb.CardRequestWorkflow_value[workflow]), nil)
		if err != nil {
			logger.Error(ctx, "error fetching card request from db", zap.Error(err))
			return errWithMsg(ctx, []string{"cardRequests", "actorId", "workflow"}), nil
		}
	case cardRequestId != "":
		cardRequest, err = d.cardRequestDao.GetById(ctx, cardRequestId, nil)
		if err != nil {
			logger.Error(ctx, "error fetching card request from db", zap.Error(err))
			return errWithMsg(ctx, []string{"cardRequest", "cardRequestId"}), nil
		}
	default:
		return errors.New("invalid parameters").Error(), nil
	}

	var cardRequestBytes []byte
	if len(cardRequests) > 0 {
		cardRequestBytes, err = pkgDev.JsonMarshalWithHumanReadableTimestamp(cardRequests)
	} else {
		cardRequestBytes, err = pkgDev.JsonMarshalWithHumanReadableTimestamp(cardRequest)
	}
	if err != nil {
		logger.Error(ctx, "cannot marshal struct to json")
		return marshalErr, nil
	}
	return string(cardRequestBytes), nil
}
