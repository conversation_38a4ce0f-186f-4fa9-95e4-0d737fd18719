package dao

import (
	"gorm.io/gorm"

	storage "github.com/epifi/be-common/pkg/storage/v2"
	maPb "github.com/epifi/gringott/api/stockguardian/matrix"
)

func WithStatus(status maPb.StageStatus) storage.FilterOption {
	return storage.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		if status == maPb.StageStatus_STAGE_STATUS_UNSPECIFIED {
			return db
		}
		return db.Where("status = ?", status)
	})
}
