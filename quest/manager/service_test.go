package manager

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"testing"
	"time"

	"google.golang.org/protobuf/types/known/durationpb"

	"github.com/epifi/be-common/pkg/epifierrors"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/segment"
	userpb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/quest/metrics"
	mocks4 "github.com/epifi/gamma/quest/metrics/mocks"

	web_helper "github.com/epifi/be-common/pkg/web"

	"google.golang.org/genproto/googleapis/rpc/code"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epificontext"
	mocks3 "github.com/epifi/be-common/pkg/lock/mocks"

	actormocks "github.com/epifi/gamma/api/actor/mocks"
	mockSegmentation "github.com/epifi/gamma/api/segment/mocks"
	usermocks "github.com/epifi/gamma/api/user/mocks"

	mockActor2 "github.com/epifi/gamma/api/actor/mocks"
	mocksUser "github.com/epifi/gamma/api/user/mocks"

	"github.com/epifi/be-common/api/pkg/web"
	questtypes "github.com/epifi/be-common/api/quest/types"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	mocks2 "github.com/epifi/be-common/pkg/storage/v2/mocks"

	pb "github.com/epifi/gamma/api/quest/manager"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/quest/config/genconf"
	"github.com/epifi/gamma/quest/dao"
	"github.com/epifi/gamma/quest/dao/mocks"
)

var (
	variable1 = &questtypes.Variable{
		Id:   "3fb35f9b-ebaa-4808-b52a-671ce1ad506d",
		Path: "frontend/home/<USER>/SearchBar/HelloText",
		Datatype: &web.Datatype{
			BaseType:     1,
			ExtendedType: 1,
		},
		Description: "Testing smart-deposit",
		Server:      "savings",
		Layer:       "InvestLayer",
		Tags:        []string{"home", "invest"},
		CreatedAt:   timestamppb.New(time.Now()),
		UpdatedAt:   timestamppb.New(time.Now()),
		Value:       &web.DataValue{DataValue: &web.DataValue_BoolValue{BoolValue: false}},
	}
	variable2 = &questtypes.Variable{
		Id:   "wfb35f9b-ebaa-4808-b52a-671ce1qqqqff",
		Path: "/home/<USER>/",
		Datatype: &web.Datatype{
			BaseType:     1,
			ExtendedType: 1,
		},
		Description: "Testing add-money",
		Server:      "add-money",
		Layer:       "InvestLayer",
		Tags:        []string{"home", "money"},
		CreatedAt:   timestamppb.New(time.Now()),
		UpdatedAt:   timestamppb.New(time.Now()),
		Value:       &web.DataValue{DataValue: &web.DataValue_IntValue{IntValue: 1000}},
	}
	variable3 = &questtypes.Variable{
		Id:   "3fb35f9b-ebaa-4808-b52a-671ce1ad506d",
		Path: "frontend/home/<USER>/SearchBar/HelloText",
		Datatype: &web.Datatype{
			BaseType:     1,
			ExtendedType: 1,
		},
		Description: "Testing smart-deposit",
		Server:      "savings",
		Layer:       "InvestLayer",
		Tags:        []string{"home", "invest"},
		CreatedAt:   timestamppb.New(time.Now()),
		UpdatedAt:   timestamppb.New(time.Now()),
		Value:       &web.DataValue{DataValue: &web.DataValue_BoolValue{BoolValue: true}},
	}
	variable4 = &questtypes.Variable{
		Id:   "wfb35f9b-ebaa-4808-b52a-671ce1qqqqff",
		Path: "/home/<USER>/",
		Datatype: &web.Datatype{
			BaseType:     1,
			ExtendedType: 1,
		},
		Description: "Testing add-money",
		Server:      "add-money",
		Layer:       "HomeLayer",
		Tags:        []string{"home", "money"},
		CreatedAt:   timestamppb.New(time.Now()),
		UpdatedAt:   timestamppb.New(time.Now()),
		Value:       &web.DataValue{DataValue: &web.DataValue_IntValue{IntValue: 5000}},
	}
	variant1 = &questtypes.Variant{
		Id:          "3fb35f9b-ebaa-4808-b52a-671ce1addddd",
		Name:        "Variant - 1",
		Status:      questtypes.VariantStatus_VARIANT_STATUS_ENABLED,
		Description: "Testing Variant AB",
		ExpId:       uuid.New().String(),
		OverridePolicy: &questtypes.OverridePolicy{
			UserGroups: []commontypes.UserGroup{0},
			PhoneNumbers: []*commontypes.PhoneNumber{
				{
					CountryCode:    91,
					NationalNumber: 1234567810,
				},
				{
					CountryCode:    91,
					NationalNumber: 8688055935,
				},
			},
			ActorIds: []string{"actor-id-1", "actor-id-2"},
		},
		IsControlVariant: false,
		PercentageStart:  10,
		PercentageEnd:    20,
		Variables:        []*questtypes.Variable{variable1, variable2},
		LastUpdatedBy:    "Avinash",
		CreatedAt:        timestamppb.New(time.Now()),
		UpdatedAt:        timestamppb.New(time.Now()),
	}
	variant2 = &questtypes.Variant{
		Id:          "ffbggg9b-ebaa-4808-b52a-671ce1addddd",
		Name:        "Variant - 2",
		Status:      questtypes.VariantStatus_VARIANT_STATUS_ENABLED,
		Description: "Testing Variant AB-2",
		ExpId:       uuid.New().String(),
		OverridePolicy: &questtypes.OverridePolicy{
			UserGroups: []commontypes.UserGroup{0},
			PhoneNumbers: []*commontypes.PhoneNumber{
				{
					CountryCode:    91,
					NationalNumber: 1234567810,
				},
				{
					CountryCode:    91,
					NationalNumber: 8688055935,
				},
			},
			ActorIds: []string{"actor-id-1", "actor-id-2"},
		},
		IsControlVariant: false,
		PercentageStart:  10,
		PercentageEnd:    20,
		Variables:        []*questtypes.Variable{variable3, variable4},
		LastUpdatedBy:    "Avinash",
		CreatedAt:        timestamppb.New(time.Now()),
		UpdatedAt:        timestamppb.New(time.Now()),
	}
	experiment1 = &questtypes.Experiment{
		Id:          "44b35f9b-ebaa-4808-b52a-671ce1adgggg",
		Name:        "experiment-1-name",
		Description: "add money experiment - desc",
		Area:        "homepage",
		Status:      questtypes.ExperimentStatus_EXPERIMENT_STATUS_ENABLED,
		Tags:        []string{"money", "add"},
		Layer:       "OthersLayer",
		Duration: &durationpb.Duration{
			Seconds: 1111,
			Nanos:   2222,
		},
		Condition: &questtypes.ExperimentCondition{
			AllowedUserGroups: []commontypes.UserGroup{0},
			AllowedAppVersions: []*questtypes.AppVersionCondition{{
				Platform:      1,
				MinAppVersion: 1,
				MaxAppVersion: 1,
			}},
			Intervals: []*questtypes.UserLayerSegmentInterval{{
				PercentStartInclusive: 0,
				PercentEndExclusive:   10,
			}},
			PercentRequired: 10,
			Segment:         nil,
		},
		StartTime:     timestamppb.Now(),
		EndTime:       timestamppb.New(time.Now().AddDate(0, 0, 5)),
		CreatedBy:     "<EMAIL>",
		LastUpdatedBy: "avinash",
		CreatedAt:     timestamppb.New(time.Now()),
		UpdatedAt:     timestamppb.New(time.Now()),
		Variables: []*questtypes.Variable{
			variable1,
			variable2,
		},
		Variants: []*questtypes.Variant{variant1, variant2},
	}
	experiment2 = &questtypes.Experiment{
		Id:          "44b35f9b-ebaa-4808-b52a-671ce1adgggg",
		Name:        "experiment-1-name-2",
		Description: "add money experiment - without intervals",
		Area:        "homepage",
		Status:      questtypes.ExperimentStatus_EXPERIMENT_STATUS_ENABLED,
		Tags:        []string{"money", "add"},
		Layer:       "OthersLayer",
		Duration: &durationpb.Duration{
			Seconds: 1111,
			Nanos:   2222,
		},
		Condition: &questtypes.ExperimentCondition{
			AllowedUserGroups: []commontypes.UserGroup{0},
			AllowedAppVersions: []*questtypes.AppVersionCondition{{
				Platform:      1,
				MinAppVersion: 1,
				MaxAppVersion: 1,
			}},
			Intervals:       nil,
			PercentRequired: 10,
			Segment:         nil,
		},
		StartTime:     timestamppb.Now(),
		EndTime:       timestamppb.New(time.Now().AddDate(0, 0, 5)),
		CreatedBy:     "<EMAIL>",
		LastUpdatedBy: "avinash",
		CreatedAt:     timestamppb.New(time.Now()),
		UpdatedAt:     timestamppb.New(time.Now()),
		Variables: []*questtypes.Variable{
			variable1,
			variable2,
		},
		Variants: []*questtypes.Variant{variant1, variant2},
	}
)

//nolint:govet
func TestService_CreateExperimentVersion(t *testing.T) {
	t.Skip("TODO(growth-infra): Re-enable the test once the issue is fixed")
	t.Parallel()
	logger.Init("test")
	a := require.New(t)
	questGenConf, err := genconf.Load()
	a.NoError(err, "failed to load config")
	trueVal, err := web_helper.GetDataValue(true)
	a.NoError(err, "failed to get true val")
	falseVal, err := web_helper.GetDataValue(false)
	a.NoError(err, "failed to get false val")
	variant1 := &questtypes.Variant{
		PercentageStart: 1,
		PercentageEnd:   90,
		Variables: []*questtypes.Variable{
			{
				Path: "test_var1", Datatype: &web.Datatype{
					BaseType:     web.PrimitiveType_PRIMITIVE_TYPE_BOOL,
					ExtendedType: web.ExtendedType_EXTENDED_TYPE_HEX_COLOR_CODE,
				},
				Value: trueVal,
			},
		},
	}
	variant1WithOverridePolicy := &questtypes.Variant{
		PercentageStart: 1,
		PercentageEnd:   90,
		Variables: []*questtypes.Variable{
			{
				Path: "test_var1", Datatype: &web.Datatype{
					BaseType:     web.PrimitiveType_PRIMITIVE_TYPE_BOOL,
					ExtendedType: web.ExtendedType_EXTENDED_TYPE_HEX_COLOR_CODE,
				},
				Value: trueVal,
			},
		},
		OverridePolicy: &questtypes.OverridePolicy{
			PhoneNumbers: []*commontypes.PhoneNumber{
				{
					CountryCode:    91,
					NationalNumber: 9876543210,
				},
			},
		},
	}
	variant2 := &questtypes.Variant{
		IsControlVariant: true,
		PercentageStart:  91,
		PercentageEnd:    100,
		Variables: []*questtypes.Variable{
			{
				Path: "test_var1", Datatype: &web.Datatype{
					BaseType:     web.PrimitiveType_PRIMITIVE_TYPE_BOOL,
					ExtendedType: web.ExtendedType_EXTENDED_TYPE_HEX_COLOR_CODE,
				},
				Value: falseVal,
			},
		},
	}

	testExp := &questtypes.Experiment{
		Name:      "test_name",
		Layer:     "HomeLayer",
		Condition: &questtypes.ExperimentCondition{},
		Variants: []*questtypes.Variant{
			variant1, variant2,
		},
		Variables: []*questtypes.Variable{
			{
				Path: "test_var1", Datatype: &web.Datatype{
					BaseType:     web.PrimitiveType_PRIMITIVE_TYPE_BOOL,
					ExtendedType: web.ExtendedType_EXTENDED_TYPE_HEX_COLOR_CODE,
				},
				Layer: "HomeLayer",
			},
		},
	}
	testExpVersion := &questtypes.ExperimentVersion{Id: "test_id", ExpData: testExp}
	testExpVersionForExistingExp := proto.Clone(testExpVersion).(*questtypes.ExperimentVersion)
	testExpVersionForExistingExp.ExpData.Id = "expid-1"
	testExpWithOverridePolicy := &questtypes.Experiment{
		Name:      "test_name",
		Condition: &questtypes.ExperimentCondition{},
		Variants: []*questtypes.Variant{
			variant1WithOverridePolicy, variant2,
		},
		Variables: []*questtypes.Variable{
			{
				Path: "test_var1", Datatype: &web.Datatype{
					BaseType:     web.PrimitiveType_PRIMITIVE_TYPE_BOOL,
					ExtendedType: web.ExtendedType_EXTENDED_TYPE_HEX_COLOR_CODE,
				},
			},
		},
	}

	testExpVersionWithOverridePolicy := &questtypes.ExperimentVersion{Id: "test_id-2", ExpData: testExpWithOverridePolicy}
	// testExpWithId := &pb.Experiment{Id: "test_id"}
	type fields struct {
		experimentDao        dao.ExperimentDao
		experimentVersionDao dao.ExperimentVersionDao
		variantDao           dao.VariantDao
	}
	type args struct {
		ctx     context.Context
		request *pb.CreateExperimentVersionRequest
	}
	tests := []struct {
		name           string
		fields         fields
		setupMockCalls func(experimentDao *mocks.MockExperimentDao, experimentVersionDao *mocks.MockExperimentVersionDao, variantDao *mocks.MockVariantDao, variableDao *mocks.MockVariableDao, variantVariableMapDao *mocks.MockVariantVariableMapDao, userClientMock *usermocks.MockUsersClient,
			actorClientMock *actormocks.MockActorClient, mockLockManager *mocks3.MockILockManager,
			mockLock *mocks3.MockILock)
		args    args
		want    *pb.CreateExperimentVersionResponse
		wantErr bool
	}{
		{
			name: "create experiment version entry failed",
			args: args{
				ctx:     context.Background(),
				request: &pb.CreateExperimentVersionRequest{ExpVers: testExpVersion},
			},
			setupMockCalls: func(experimentDao *mocks.MockExperimentDao, experimentVersionDao *mocks.MockExperimentVersionDao, variantDao *mocks.MockVariantDao, variableDao *mocks.MockVariableDao, variantVariableMapDao *mocks.MockVariantVariableMapDao, userClientMock *usermocks.MockUsersClient,
				actorClientMock *actormocks.MockActorClient, mockLockManager *mocks3.MockILockManager,
				mockLock *mocks3.MockILock) {
				variableDao.EXPECT().GetByPath(gomock.Any(), gomock.Any(), gomock.Any()).Return(&questtypes.Variable{
					Id:   "variabe-id-1",
					Path: "test_var1", Datatype: &web.Datatype{
						BaseType:     web.PrimitiveType_PRIMITIVE_TYPE_BOOL,
						ExtendedType: web.ExtendedType_EXTENDED_TYPE_HEX_COLOR_CODE,
					},
					Value: trueVal,
				}, nil).Times(1)
				experimentDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Experiment{}, nil, nil).AnyTimes()
				experimentDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, gorm.ErrInvalidData)
				mockLockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockLock, nil).Times(1)
				mockLock.EXPECT().Release(gomock.Any()).Return(nil).Times(1)
			},
			want:    &pb.CreateExperimentVersionResponse{Status: rpc.NewStatus(uint32(code.Code_INTERNAL), gorm.ErrInvalidData.Error(), "")},
			wantErr: false,
		},
		{
			name: "create experiment version entry success",
			args: args{
				ctx:     context.Background(),
				request: &pb.CreateExperimentVersionRequest{ExpVers: testExpVersion},
			},
			setupMockCalls: func(experimentDao *mocks.MockExperimentDao, experimentVersionDao *mocks.MockExperimentVersionDao, variantDao *mocks.MockVariantDao, variableDao *mocks.MockVariableDao, variantVariableMapDao *mocks.MockVariantVariableMapDao, userClientMock *usermocks.MockUsersClient,
				actorClientMock *actormocks.MockActorClient, mockLockManager *mocks3.MockILockManager,
				mockLock *mocks3.MockILock) {
				variableDao.EXPECT().GetByPath(gomock.Any(), gomock.Any(), gomock.Any()).Return(&questtypes.Variable{
					Id:   "variabe-id-1",
					Path: "test_var1", Datatype: &web.Datatype{
						BaseType:     web.PrimitiveType_PRIMITIVE_TYPE_BOOL,
						ExtendedType: web.ExtendedType_EXTENDED_TYPE_HEX_COLOR_CODE,
					},
					Value: trueVal,
				}, nil).Times(1)
				experimentVersionDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(testExpVersion, nil).AnyTimes()
				experimentVersionDao.EXPECT().GetByExpId(gomock.Any(), gomock.Any(), gomock.Any()).Return(testExpVersion, nil).Times(1)
				experimentDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Experiment{}, nil, nil).AnyTimes()
				experimentDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(testExp, nil)
				variantDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				variantVariableMapDao.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(2)
				mockLockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockLock, nil).AnyTimes()
				mockLock.EXPECT().Release(gomock.Any()).Return(nil).AnyTimes()
			},
			want:    &pb.CreateExperimentVersionResponse{Status: rpc.StatusOk(), ExpVers: testExpVersion},
			wantErr: false,
		},
		{
			name: "create experiment version for existing experiment success",
			args: args{
				ctx:     context.Background(),
				request: &pb.CreateExperimentVersionRequest{ExpVers: testExpVersionForExistingExp},
			},
			setupMockCalls: func(experimentDao *mocks.MockExperimentDao, experimentVersionDao *mocks.MockExperimentVersionDao, variantDao *mocks.MockVariantDao, variableDao *mocks.MockVariableDao, variantVariableMapDao *mocks.MockVariantVariableMapDao,
				userClientMock *usermocks.MockUsersClient, actorClientMock *actormocks.MockActorClient, mockLockManager *mocks3.MockILockManager,
				mockLock *mocks3.MockILock) {
				variableDao.EXPECT().GetByPath(gomock.Any(), gomock.Any(), gomock.Any()).Return(&questtypes.Variable{
					Id:   "variabe-id-1",
					Path: "test_var1", Datatype: &web.Datatype{
						BaseType:     web.PrimitiveType_PRIMITIVE_TYPE_BOOL,
						ExtendedType: web.ExtendedType_EXTENDED_TYPE_HEX_COLOR_CODE,
					},
					Value: trueVal,
				}, nil).Times(1)
				experimentVersionDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(testExpVersionForExistingExp, nil).AnyTimes()
				experimentVersionDao.EXPECT().GetByExpId(gomock.Any(), gomock.Any(), gomock.Any()).Return(testExpVersionForExistingExp, nil).Times(1)
				experimentDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Experiment{}, nil, nil).AnyTimes()
				variantDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&questtypes.Variant{Id: "variant-id-1"}, nil).Times(1)
				variantDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&questtypes.Variant{Id: "variant-id-2"}, nil).Times(1)
				variantVariableMapDao.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(2)
				mockLockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockLock, nil).AnyTimes()
				mockLock.EXPECT().Release(gomock.Any()).Return(nil).AnyTimes()

			},
			want:    &pb.CreateExperimentVersionResponse{Status: rpc.StatusOk(), ExpVers: testExpVersionForExistingExp},
			wantErr: false,
		},
		{
			name: "create experiment version with override policy entry success",
			args: args{
				ctx:     context.Background(),
				request: &pb.CreateExperimentVersionRequest{ExpVers: testExpVersionWithOverridePolicy},
			},
			setupMockCalls: func(experimentDao *mocks.MockExperimentDao, experimentVersionDao *mocks.MockExperimentVersionDao, variantDao *mocks.MockVariantDao, variableDao *mocks.MockVariableDao, variantVariableMapDao *mocks.MockVariantVariableMapDao, userClientMock *usermocks.MockUsersClient,
				actorClientMock *actormocks.MockActorClient, mockLockManager *mocks3.MockILockManager,
				mockLock *mocks3.MockILock) {
				variableDao.EXPECT().GetByPath(gomock.Any(), gomock.Any(), gomock.Any()).Return(&questtypes.Variable{
					Id:   "variabe-id-1",
					Path: "test_var1", Datatype: &web.Datatype{
						BaseType:     web.PrimitiveType_PRIMITIVE_TYPE_BOOL,
						ExtendedType: web.ExtendedType_EXTENDED_TYPE_HEX_COLOR_CODE,
					},
					Value: trueVal,
				}, nil).Times(1)
				experimentVersionDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(testExpVersionWithOverridePolicy, nil).AnyTimes()
				experimentVersionDao.EXPECT().GetByExpId(gomock.Any(), gomock.Any(), gomock.Any()).Return(testExpVersionWithOverridePolicy, nil).Times(1)
				experimentDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Experiment{}, nil, nil).AnyTimes()
				experimentDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(testExp, nil)
				variantDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				variantVariableMapDao.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(2)
				mockLockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockLock, nil).AnyTimes()
				mockLock.EXPECT().Release(gomock.Any()).Return(nil).AnyTimes()
				userClientMock.EXPECT().GetUsers(gomock.Any(), gomock.Any()).Return(&userpb.GetUsersResponse{
					Status: rpc.StatusOk(),
					Users: []*userpb.User{{
						Id: "user-id-1",
					}},
				}, nil).Times(1)
				actorClientMock.EXPECT().GetActorByEntityId(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByEntityIdResponse{
					Status: rpc.StatusOk(),
					Actor:  &types.Actor{Id: "actor-id-1"},
				}, nil).Times(1)
			},
			want:    &pb.CreateExperimentVersionResponse{Status: rpc.StatusOk(), ExpVers: testExpVersionWithOverridePolicy},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockExperimentDao := mocks.NewMockExperimentDao(ctr)
			mockExperimentVersionDao := mocks.NewMockExperimentVersionDao(ctr)
			mockVariantDao := mocks.NewMockVariantDao(ctr)
			mockLockManager := mocks3.NewMockILockManager(ctr)
			mockVariableDao := mocks.NewMockVariableDao(ctr)
			mockVariantVariableMapDao := mocks.NewMockVariantVariableMapDao(ctr)
			mockLock := mocks3.NewMockILock(ctr)
			mockUserClient := usermocks.NewMockUsersClient(ctr)
			mockActorClient := actormocks.NewMockActorClient(ctr)
			s := &Service{
				questGenConf:          questGenConf,
				experimentDao:         mockExperimentDao,
				experimentVersionDao:  mockExperimentVersionDao,
				variantDao:            mockVariantDao,
				variableDao:           mockVariableDao,
				lockManager:           mockLockManager,
				variantVariableMapDao: mockVariantVariableMapDao,
				userClient:            mockUserClient,
				actorClient:           mockActorClient,
				questDbTxnExecutor:    TxnExecutor{}}
			tt.setupMockCalls(mockExperimentDao, mockExperimentVersionDao, mockVariantDao, mockVariableDao, mockVariantVariableMapDao, mockUserClient, mockActorClient, mockLockManager, mockLock)
			got, err := s.CreateExperimentVersion(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateExperimentVersion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("CreateExperimentVersion() \n got: %v\nwant: %v\ndiff = %s", got, tt.want, diff)
			}
		})
	}
}

//nolint:govet
func TestService_UpdateExperimentVersion(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	a := require.New(t)
	questGenConf, err := genconf.Load()
	a.NoError(err, "failed to load config")
	testExp := &questtypes.Experiment{
		Name:      "test_name",
		Layer:     "HomeLayer",
		Condition: &questtypes.ExperimentCondition{},
		Variants: []*questtypes.Variant{
			{
				Id: "variant_id",
				Variables: []*questtypes.Variable{
					{
						Path: "variable_path",
					},
				},
				PercentageStart: 1,
				PercentageEnd:   100,
			},
		},
		Variables: []*questtypes.Variable{
			{
				Path: "variable_path",
			},
		},
	}
	testLayer := &questtypes.Layer{
		Id:                   "1",
		Name:                 "GlobalLayer",
		IsOverlappingAllowed: false,
	}
	// testExpWithId := &pb.Experiment{Id: "test_id", Name: "test_name"}
	testExpVersion := &questtypes.ExperimentVersion{Id: "test_id", ExpData: testExp, Status: questtypes.ExperimentVersionStatus_EXPERIMENT_VERSION_STATUS_APPROVED}
	testExpVersion2 := &questtypes.ExperimentVersion{Id: "123", ExpData: testExp, Status: questtypes.ExperimentVersionStatus_EXPERIMENT_VERSION_STATUS_DECLINED}
	// testExpVersionApprovedNoExp := &pb.ExperimentVersion{Id: "test_id", ExpData: testExp, Status: pb.ExperimentVersionStatus_EXPERIMENT_VERSION_STATUS_APPROVED}
	// testExpVersionApprovedWithExp := &pb.ExperimentVersion{Id: "test_id", ExpData: testExpWithId, Status: pb.ExperimentVersionStatus_EXPERIMENT_VERSION_STATUS_APPROVED}
	type fields struct {
		experimentDao        dao.ExperimentDao
		experimentVersionDao dao.ExperimentVersionDao
		variantDao           dao.VariantDao
	}
	type args struct {
		ctx     context.Context
		request *pb.UpdateExperimentVersionRequest
	}
	tests := []struct {
		name           string
		fields         fields
		args           args
		setupMockCalls func(experimentDao *mocks.MockExperimentDao, experimentVersionDao *mocks.MockExperimentVersionDao, variantDao *mocks.MockVariantDao, variableDao *mocks.MockVariableDao, variantVariableMapDao *mocks.MockVariantVariableMapDao, layerDao *mocks.MockLayerDao, userClientMock *usermocks.MockUsersClient,
			actorClientMock *actormocks.MockActorClient, mockLockManager *mocks3.MockILockManager,
			mockLock *mocks3.MockILock, cache *mocks.MockQuestSdkCache)
		want    *pb.UpdateExperimentVersionResponse
		wantErr bool
	}{
		{
			name: "txn success",
			args: args{
				ctx: context.Background(),
				request: &pb.UpdateExperimentVersionRequest{
					ExpVers: testExpVersion,
				},
			},
			setupMockCalls: func(experimentDao *mocks.MockExperimentDao, experimentVersionDao *mocks.MockExperimentVersionDao, variantDao *mocks.MockVariantDao, variableDao *mocks.MockVariableDao, variantVariableMapDao *mocks.MockVariantVariableMapDao, layerDao *mocks.MockLayerDao, userClientMock *usermocks.MockUsersClient,
				actorClientMock *actormocks.MockActorClient, mockLockManager *mocks3.MockILockManager,
				mockLock *mocks3.MockILock, cache *mocks.MockQuestSdkCache) {
				mockLockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockLock, nil).Times(1)
				mockLock.EXPECT().Release(gomock.Any()).Return(nil).Times(1)
				experimentDao.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(testExp, nil).Times(2)
				experimentDao.EXPECT().GetActiveExperimentsForLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Experiment{testExp}, nil).AnyTimes()
				experimentDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
				variantDao.EXPECT().DeleteById(gomock.Any(), gomock.Any()).Return(nil).Times(1)
				variantDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&questtypes.Variant{Id: "variant-id-1"}, nil).Times(1)
				variantVariableMapDao.EXPECT().DeleteByVariableIdAndVariantId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
				variableDao.EXPECT().GetByPath(gomock.Any(), gomock.Any(), gomock.Any()).Return(&questtypes.Variable{
					Id:   "id-1",
					Path: "variable_path",
				}, nil).Times(1)
				variantVariableMapDao.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
				layerDao.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(testLayer, nil).AnyTimes()
				layerDao.EXPECT().GetNonOverlappingChildren(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Layer{}, nil).AnyTimes()
				layerDao.EXPECT().GetLayersInSubtree(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Layer{}, nil).AnyTimes()
				cache.EXPECT().SetValueInCache(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				cache.EXPECT().SetExperimentVariablesMapping(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				experimentVersionDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(testExpVersion, nil).AnyTimes()
				experimentVersionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			},
			want: &pb.UpdateExperimentVersionResponse{Status: rpc.StatusOk(), ExpVers: testExpVersion},
		},
		{
			name: "txn success for decline exp version",
			args: args{
				ctx: context.Background(),
				request: &pb.UpdateExperimentVersionRequest{
					ExpVers: testExpVersion2,
				},
			},
			setupMockCalls: func(experimentDao *mocks.MockExperimentDao, experimentVersionDao *mocks.MockExperimentVersionDao, variantDao *mocks.MockVariantDao, variableDao *mocks.MockVariableDao, variantVariableMapDao *mocks.MockVariantVariableMapDao, layerDao *mocks.MockLayerDao, userClientMock *usermocks.MockUsersClient,
				actorClientMock *actormocks.MockActorClient, mockLockManager *mocks3.MockILockManager,
				mockLock *mocks3.MockILock, cache *mocks.MockQuestSdkCache) {
				mockLockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockLock, nil).AnyTimes()
				mockLock.EXPECT().Release(gomock.Any()).Return(nil).AnyTimes()
				experimentDao.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(testExp, nil).AnyTimes()
				experimentDao.EXPECT().GetActiveExperimentsForLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Experiment{testExp}, nil).AnyTimes()
				experimentDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				variantDao.EXPECT().DeleteById(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				variantDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&questtypes.Variant{Id: "variant-id-1"}, nil).AnyTimes()
				variantVariableMapDao.EXPECT().DeleteByVariableIdAndVariantId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				variableDao.EXPECT().GetByPath(gomock.Any(), gomock.Any(), gomock.Any()).Return(&questtypes.Variable{
					Id:   "id-1",
					Path: "variable_path",
				}, nil).AnyTimes()
				variantVariableMapDao.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				layerDao.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(testLayer, nil).AnyTimes()
				layerDao.EXPECT().GetNonOverlappingChildren(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Layer{}, nil).AnyTimes()
				layerDao.EXPECT().GetLayersInSubtree(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Layer{}, nil).AnyTimes()
				cache.EXPECT().SetValueInCache(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				cache.EXPECT().SetExperimentVariablesMapping(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				experimentVersionDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(testExpVersion, nil).AnyTimes()
				experimentVersionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			},
			want: &pb.UpdateExperimentVersionResponse{Status: rpc.StatusOk(), ExpVers: testExpVersion2},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockQuestSdkDao := mocks.NewMockQuestSdkCache(ctr)
			mockExperimentDao := mocks.NewMockExperimentDao(ctr)
			mockExperimentVersionDao := mocks.NewMockExperimentVersionDao(ctr)
			mockVariantDao := mocks.NewMockVariantDao(ctr)
			mockLockManager := mocks3.NewMockILockManager(ctr)
			mockVariableDao := mocks.NewMockVariableDao(ctr)
			mockVariantVariableMapDao := mocks.NewMockVariantVariableMapDao(ctr)
			mockLayerDao := mocks.NewMockLayerDao(ctr)
			mockLock := mocks3.NewMockILock(ctr)
			mockUserClient := usermocks.NewMockUsersClient(ctr)
			mockActorClient := actormocks.NewMockActorClient(ctr)
			s := &Service{
				questGenConf:          questGenConf,
				experimentDao:         mockExperimentDao,
				experimentVersionDao:  mockExperimentVersionDao,
				variantDao:            mockVariantDao,
				variableDao:           mockVariableDao,
				lockManager:           mockLockManager,
				variantVariableMapDao: mockVariantVariableMapDao,
				layerDao:              mockLayerDao,
				userClient:            mockUserClient,
				actorClient:           mockActorClient,
				questDbTxnExecutor:    TxnExecutor{},
				questSdkCacheDao:      mockQuestSdkDao,
			}
			tt.setupMockCalls(mockExperimentDao, mockExperimentVersionDao, mockVariantDao, mockVariableDao, mockVariantVariableMapDao, mockLayerDao, mockUserClient, mockActorClient, mockLockManager, mockLock, mockQuestSdkDao)
			got, err := s.UpdateExperimentVersion(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateExperimentVersion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("UpdateExperimentVersion() diff = %s ", diff)
			}
		})
	}
}

//nolint:govet
func TestService_GetExperimentVersions(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	testExp := &questtypes.Experiment{Name: "test_name"}
	testExpVersion := &questtypes.ExperimentVersion{Id: "test_id", ExpData: testExp}
	type fields struct {
		experimentDao        dao.ExperimentDao
		experimentVersionDao dao.ExperimentVersionDao
		variantDao           dao.VariantDao
	}
	type args struct {
		ctx     context.Context
		request *pb.GetExperimentVersionsRequest
	}
	tests := []struct {
		name           string
		fields         fields
		args           args
		setupMockCalls func(experimentVersionDao *mocks.MockExperimentVersionDao)
		want           *pb.GetExperimentVersionsResponse
		wantErr        bool
	}{
		{
			name: "failure to fetch experiment by filters",
			args: args{
				ctx:     context.Background(),
				request: &pb.GetExperimentVersionsRequest{},
			},
			setupMockCalls: func(experimentVersionDao *mocks.MockExperimentVersionDao) {
				experimentVersionDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, gorm.ErrInvalidData)
			},
			want: &pb.GetExperimentVersionsResponse{Status: rpc.NewStatus(uint32(code.Code_INTERNAL), gorm.ErrInvalidData.Error(), "")},
		},
		{
			name: "fetch experiment by filters, success",
			args: args{
				ctx:     context.Background(),
				request: &pb.GetExperimentVersionsRequest{},
			},
			setupMockCalls: func(experimentVersionDao *mocks.MockExperimentVersionDao) {
				experimentVersionDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.ExperimentVersion{testExpVersion}, nil, nil)
			},
			want: &pb.GetExperimentVersionsResponse{
				Status:   rpc.StatusOk(),
				Versions: []*questtypes.ExperimentVersion{testExpVersion},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockExperimentVersionDao := mocks.NewMockExperimentVersionDao(ctr)
			mockTxnManager := mocks2.NewMockTxnExecutor(ctr)
			s := &Service{
				experimentVersionDao: mockExperimentVersionDao,
				questDbTxnExecutor:   mockTxnManager,
			}
			tt.setupMockCalls(mockExperimentVersionDao)
			got, err := s.GetExperimentVersions(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExperimentVersions() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetExperimentVersions() diff = %s", diff)
			}
		})
	}
}

//nolint:govet
func TestService_GetExperiments(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	testVariable := func() *questtypes.Variable {
		return &questtypes.Variable{Path: "x/y/z", Value: &web.DataValue{DataValue: &web.DataValue_BoolValue{BoolValue: false}}}
	}
	testVariant := func() *questtypes.Variant {
		return &questtypes.Variant{Name: "test_variant", Variables: []*questtypes.Variable{testVariable()}}
	}
	testExp := func() *questtypes.Experiment {
		return &questtypes.Experiment{Name: "test_name", Variants: []*questtypes.Variant{testVariant()}}
	}
	testExpWithVariant := &questtypes.Experiment{
		Name:      "test_name",
		Variants:  []*questtypes.Variant{testVariant()},
		Variables: []*questtypes.Variable{testVariable()},
	}
	type fields struct {
		experimentDao        dao.ExperimentDao
		experimentVersionDao dao.ExperimentVersionDao
		variantDao           dao.VariantDao
	}
	// field mask for experiment
	experimentFM, _ := fieldmaskpb.New(&questtypes.Experiment{}, "id", "name", "description", "status", "condition", "start_time", "end_time", "created_by", "last_updated_by", "created_at", "updated_at", "variables", "variants")

	type args struct {
		ctx     context.Context
		request *pb.GetExperimentsRequest
	}
	tests := []struct {
		name           string
		fields         fields
		args           args
		setupMockCalls func(experimentDao *mocks.MockExperimentDao, variantDao *mocks.MockVariantDao, variableDao *mocks.MockVariableDao, variantVariableMapDao *mocks.MockVariantVariableMapDao)
		want           *pb.GetExperimentsResponse
		wantErr        bool
	}{
		{
			name: "happy flow",
			args: args{
				ctx: context.Background(),
				request: &pb.GetExperimentsRequest{
					FieldMask: experimentFM,
				},
			},
			setupMockCalls: func(experimentDao *mocks.MockExperimentDao, variantDao *mocks.MockVariantDao, variableDao *mocks.MockVariableDao, variantVariableMapDao *mocks.MockVariantVariableMapDao) {
				experimentDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Experiment{testExp()}, nil,
					nil).Times(1)
				variantDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Variant{proto.Clone(testVariant()).(*questtypes.Variant)}, nil, nil).Times(1)
				variableDao.EXPECT().GetVariablesByVariantIds(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Variable{proto.Clone(testVariable()).(*questtypes.Variable)}, nil).AnyTimes()
				variantVariableMapDao.EXPECT().GetByVariantIdAndVariableId(gomock.Any(), gomock.Any(), gomock.Any()).Return(&web.DataValue{DataValue: &web.DataValue_BoolValue{BoolValue: false}}, nil).AnyTimes()
			},
			want: &pb.GetExperimentsResponse{
				Status: rpc.StatusOk(),
				Experiments: []*questtypes.Experiment{
					proto.Clone(testExpWithVariant).(*questtypes.Experiment),
				},
			},
		},
		{
			name: "failure to fetch from experiment table",
			args: args{
				ctx:     context.Background(),
				request: &pb.GetExperimentsRequest{},
			},
			setupMockCalls: func(experimentDao *mocks.MockExperimentDao, variantDao *mocks.MockVariantDao, variableDao *mocks.MockVariableDao, variantVariableMapDao *mocks.MockVariantVariableMapDao) {
				experimentDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, gorm.ErrInvalidData).Times(1)
			},
			want: &pb.GetExperimentsResponse{Status: rpc.NewStatus(uint32(code.Code_INTERNAL), gorm.ErrInvalidData.Error(), "")},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockExperimentDao := mocks.NewMockExperimentDao(ctr)
			mockVariableDao := mocks.NewMockVariableDao(ctr)
			mockVariantDao := mocks.NewMockVariantDao(ctr)
			mockVariantVariableDao := mocks.NewMockVariantVariableMapDao(ctr)
			mockTxnManager := mocks2.NewMockTxnExecutor(ctr)
			s := &Service{
				experimentDao:         mockExperimentDao,
				variantDao:            mockVariantDao,
				variableDao:           mockVariableDao,
				variantVariableMapDao: mockVariantVariableDao,
				questDbTxnExecutor:    mockTxnManager,
			}
			tt.setupMockCalls(mockExperimentDao, mockVariantDao, mockVariableDao, mockVariantVariableDao)
			got, err := s.GetExperiments(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExperiments() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetExperiments() diff = %s", diff)
			}
		})
	}
}

// nolint:govet
func TestService_UpdateExperimentsCache(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *pb.UpdateExperimentsCacheRequest
	}
	variable1 := &questtypes.Variable{
		Id:   "3fb35f9b-ebaa-4808-b52a-671ce1ad506d",
		Path: "/home/<USER>/smart-deposit/new",
		Datatype: &web.Datatype{
			BaseType:     web.PrimitiveType_PRIMITIVE_TYPE_BOOL,
			ExtendedType: web.ExtendedType_EXTENDED_TYPE_HEX_COLOR_CODE,
		},
		Description: "Testing smart-deposit",
		Server:      "savings",
		Layer:       "savings",
		Tags:        []string{"home", "invest"},
		CreatedAt:   timestamppb.New(time.Now()),
		UpdatedAt:   timestamppb.New(time.Now()),
		Value:       &web.DataValue{DataValue: &web.DataValue_BoolValue{BoolValue: false}},
	}
	variant1 := &questtypes.Variant{
		Id:          "3fb35f9b-ebaa-4808-b52a-671ce1addddd",
		Name:        "Variant - 1",
		Status:      questtypes.VariantStatus_VARIANT_STATUS_ENABLED,
		Description: "Testing Variant AB",
		ExpId:       uuid.New().String(),
		OverridePolicy: &questtypes.OverridePolicy{
			UserGroups: []commontypes.UserGroup{0},
			PhoneNumbers: []*commontypes.PhoneNumber{
				{
					CountryCode:    91,
					NationalNumber: 1234567810,
				},
				{
					CountryCode:    91,
					NationalNumber: 8688055935,
				},
			},
			ActorIds: []string{"actor-id-1", "actor-id-2"},
		},
		IsControlVariant: false,
		PercentageStart:  10,
		PercentageEnd:    20,
		LastUpdatedBy:    "Avinash",
		CreatedAt:        timestamppb.New(time.Now()),
		UpdatedAt:        timestamppb.New(time.Now()),
	}
	experiment1 := &questtypes.Experiment{
		Id:          "44b35f9b-ebaa-4808-b52a-671ce1adgggg",
		Name:        "experiment-1",
		Description: "add money experiment",
		Status:      questtypes.ExperimentStatus_EXPERIMENT_STATUS_ENABLED,
		Condition: &questtypes.ExperimentCondition{
			AllowedUserGroups: []commontypes.UserGroup{0},
			AllowedAppVersions: []*questtypes.AppVersionCondition{{
				Platform:      1,
				MinAppVersion: 1,
				MaxAppVersion: 1,
			}},
			Intervals: []*questtypes.UserLayerSegmentInterval{{
				PercentStartInclusive: 0,
				PercentEndExclusive:   0,
			}},
			Segment: nil,
		},
		StartTime:     timestamppb.Now(),
		EndTime:       timestamppb.New(time.Now().AddDate(0, 0, 5)),
		CreatedBy:     "avinash",
		LastUpdatedBy: "avinash",
		CreatedAt:     timestamppb.New(time.Now()),
		UpdatedAt:     timestamppb.New(time.Now()),
		Variables: []*questtypes.Variable{
			variable1,
		},
		Variants: []*questtypes.Variant{variant1},
	}
	experiment2 := &questtypes.Experiment{
		Id:          "44b35f9b-ebaa-4808-b52b-671ce1aeeeee",
		Name:        "experiment-2",
		Description: "add money experiment",
		Status:      questtypes.ExperimentStatus_EXPERIMENT_STATUS_ENABLED,
		Condition: &questtypes.ExperimentCondition{
			AllowedUserGroups: []commontypes.UserGroup{0},
			AllowedAppVersions: []*questtypes.AppVersionCondition{{
				Platform:      1,
				MinAppVersion: 1,
				MaxAppVersion: 1,
			}},
			Intervals: []*questtypes.UserLayerSegmentInterval{{
				PercentStartInclusive: 0,
				PercentEndExclusive:   0,
			}},
			Segment: nil,
		},
		StartTime:     timestamppb.Now(),
		EndTime:       timestamppb.New(time.Now().AddDate(0, 0, 5)),
		CreatedBy:     "avinash",
		LastUpdatedBy: "avinash",
		CreatedAt:     timestamppb.New(time.Now()),
		UpdatedAt:     timestamppb.New(time.Now()),
		Variables: []*questtypes.Variable{
			variable1,
		},
		Variants: []*questtypes.Variant{
			variant1,
		},
	}
	experiment3 := &questtypes.Experiment{
		Id:          "55b35f9b-ebaa-4808-b52b-671ce1aeeeef",
		Name:        "experiment-3",
		Description: "add money experiment",
		Status:      questtypes.ExperimentStatus_EXPERIMENT_STATUS_ENABLED,
		Condition: &questtypes.ExperimentCondition{
			AllowedUserGroups: []commontypes.UserGroup{0},
			AllowedAppVersions: []*questtypes.AppVersionCondition{{
				Platform:      1,
				MinAppVersion: 1,
				MaxAppVersion: 1,
			}},
			Intervals: []*questtypes.UserLayerSegmentInterval{{
				PercentStartInclusive: 0,
				PercentEndExclusive:   0,
			}},
			Segment: nil,
		},
		StartTime:     timestamppb.Now(),
		EndTime:       timestamppb.New(time.Now().AddDate(0, 0, 5)),
		CreatedBy:     "avinash",
		LastUpdatedBy: "avinash",
		CreatedAt:     timestamppb.New(time.Now()),
		UpdatedAt:     timestamppb.New(time.Now()),
		Variables: []*questtypes.Variable{
			variable1,
		},
		Variants: []*questtypes.Variant{variant1},
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(experimentDao *mocks.MockExperimentDao, variantDao *mocks.MockVariantDao, variableDao *mocks.MockVariableDao, questSdkCache *mocks.MockQuestSdkCache, variantVariableMapDao *mocks.MockVariantVariableMapDao)
		want           *pb.UpdateExperimentsCacheResponse
		wantErr        bool
	}{
		{
			name: "delete the expired experiment",
			args: args{
				ctx: context.Background(),
				request: &pb.UpdateExperimentsCacheRequest{
					UpdatedAtStart: timestamppb.New(time.Now().AddDate(0, 0, -2)),
					UpdatedAtEnd:   timestamppb.New(time.Now().AddDate(0, 0, 1)),
				},
			},
			setupMockCalls: func(experimentDao *mocks.MockExperimentDao, variantDao *mocks.MockVariantDao, variableDao *mocks.MockVariableDao, questSdkCache *mocks.MockQuestSdkCache, variantVariableMapDao *mocks.MockVariantVariableMapDao) {
				experimentDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Experiment{experiment1,
					experiment2,
					experiment3}, nil, nil).AnyTimes()
				variantDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Variant{variant1}, nil, nil).AnyTimes()
				variableDao.EXPECT().GetVariablesByVariantIds(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Variable{variable1}, nil).AnyTimes()
				variantVariableMapDao.EXPECT().GetByVariantIdAndVariableId(gomock.Any(), gomock.Any(), gomock.Any()).Return(&web.DataValue{DataValue: &web.DataValue_BoolValue{BoolValue: false}}, nil).AnyTimes()
				questSdkCache.EXPECT().DelValueInCache(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				questSdkCache.EXPECT().SetValueInCache(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				questSdkCache.EXPECT().SetExperimentVariablesMapping(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				questSdkCache.EXPECT().DeleteExperimentVariablesMapping(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			},
			want:    &pb.UpdateExperimentsCacheResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			experimentsDao := mocks.NewMockExperimentDao(ctrl)
			variantsDao := mocks.NewMockVariantDao(ctrl)
			variableDao := mocks.NewMockVariableDao(ctrl)
			questSdkCache := mocks.NewMockQuestSdkCache(ctrl)
			variantVariableMapDao := mocks.NewMockVariantVariableMapDao(ctrl)
			gconf, _ := genconf.Load()
			tt.setupMockCalls(experimentsDao, variantsDao, variableDao, questSdkCache, variantVariableMapDao)
			a := require.New(t)
			mockActor := mockActor2.NewMockActorClient(ctrl)
			mockUser := mocksUser.NewMockUsersClient(ctrl)
			mockSegment := mockSegmentation.NewMockSegmentationServiceClient(ctrl)
			svc, err := NewManagerService(gconf, mockActor, mockUser, experimentsDao, nil, variantsDao, variableDao, variantVariableMapDao, questSdkCache, nil, nil, nil, nil, nil, mockSegment)
			a.NoError(err, "not able to initialize NewManagerService")
			resp, err := svc.UpdateExperimentsCache(tt.args.ctx, tt.args.request)
			a.NoError(err, "not able to update the cache with UpdateExperimentsCache call")
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateExperimentsCache() error = %v, wantErr %v got %v", err, tt.wantErr, resp)
				return
			}
			if !tt.wantErr {
				if diff := cmp.Diff(tt.want, resp, protocmp.Transform()); diff != "" {
					t.Errorf("UpdateExperimentsCache() \ngot: %v, \nwant: %v \ndiff: %v", resp, tt.want, diff)
				}
			}
		})
	}
}

//nolint:govet
func TestService_CreateVariables(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	type fields struct {
		questGConf            *genconf.Config
		experimentDao         dao.ExperimentDao
		experimentVersionDao  dao.ExperimentVersionDao
		variantDao            dao.VariantDao
		variableDao           dao.VariableDao
		variantVariableMapDao dao.VariantVariableMapDao
		questSdkCacheDao      dao.QuestSdkCache
		questDbTxnExecutor    storagev2.TxnExecutor
		idFieldMask           fieldmaskpb.FieldMask
		expDataInExpVerFM     *fieldmaskpb.FieldMask
		expDataUpdateFM       *fieldmaskpb.FieldMask
		variantUpdateFM       *fieldmaskpb.FieldMask
	}
	type args struct {
		ctx context.Context
		req *pb.CreateVariablesRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.CreateVariablesResponse
		wantErr bool
	}{
		{
			name: "create variables, success",
			args: args{
				ctx: context.Background(),
				req: &pb.CreateVariablesRequest{
					Variables: []*questtypes.Variable{{Path: "x/y/z"}},
					Server:    "frontend",
				},
			},
			want: &pb.CreateVariablesResponse{
				Status:    rpc.StatusOk(),
				Variables: []*questtypes.Variable{{Path: "x/y/z"}},
				Server:    "frontend",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			variableDao := mocks.NewMockVariableDao(ctr)
			txnExecutor := mocks2.NewMockTxnExecutor(ctr)
			questGenConf, err := genconf.Load()
			if err != nil {
				logger.Fatal("failed to load gen conf", zap.Error(err))
			}
			s := &Service{
				questGenConf:       questGenConf,
				variableDao:        variableDao,
				questDbTxnExecutor: txnExecutor,
			}
			variableDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Variable{{Path: "x/y/z"}}, &rpc.PageContextResponse{HasAfter: false}, nil).AnyTimes()
			txnExecutor.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			got, err := s.CreateVariables(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateVariables() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("CreateVariables() diff %s", diff)
			}
		})
	}
}

// nolint:govet
func TestService_UpdateExperimentStatus(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	variable1 := &questtypes.Variable{
		Id:   "3fb35f9b-ebaa-4808-b52a-671ce1ad506d",
		Path: "/home/<USER>/smart-deposit/new",
		Datatype: &web.Datatype{
			BaseType:     web.PrimitiveType_PRIMITIVE_TYPE_BOOL,
			ExtendedType: web.ExtendedType_EXTENDED_TYPE_HEX_COLOR_CODE,
		},
		Description: "Testing smart-deposit",
		Server:      "savings",
		Layer:       "savings",
		Tags:        []string{"home", "invest"},
		CreatedAt:   timestamppb.New(time.Now()),
		UpdatedAt:   timestamppb.New(time.Now()),
		Value:       &web.DataValue{DataValue: &web.DataValue_BoolValue{BoolValue: false}},
	}
	variant1 := &questtypes.Variant{
		Id:          "3fb35f9b-ebaa-4808-b52a-671ce1addddd",
		Name:        "Variant - 1",
		Status:      questtypes.VariantStatus_VARIANT_STATUS_ENABLED,
		Description: "Testing Variant AB",
		ExpId:       uuid.New().String(),
		OverridePolicy: &questtypes.OverridePolicy{
			UserGroups: []commontypes.UserGroup{0},
			PhoneNumbers: []*commontypes.PhoneNumber{
				{
					CountryCode:    91,
					NationalNumber: 1234567810,
				},
				{
					CountryCode:    91,
					NationalNumber: 8688055935,
				},
			},
			ActorIds: []string{"actor-id-1", "actor-id-2"},
		},
		IsControlVariant: false,
		PercentageStart:  10,
		PercentageEnd:    20,
		LastUpdatedBy:    "Avinash",
		CreatedAt:        timestamppb.New(time.Now()),
		UpdatedAt:        timestamppb.New(time.Now()),
	}
	experiment1 := &questtypes.Experiment{
		Id:          "44b35f9b-ebaa-4808-b52a-671ce1adgggg",
		Name:        "experiment-1",
		Description: "add money experiment",
		Status:      questtypes.ExperimentStatus_EXPERIMENT_STATUS_UNSPECIFIED,
		Condition: &questtypes.ExperimentCondition{
			AllowedUserGroups: []commontypes.UserGroup{0},
			AllowedAppVersions: []*questtypes.AppVersionCondition{{
				Platform:      1,
				MinAppVersion: 1,
				MaxAppVersion: 1,
			}},
			Intervals: []*questtypes.UserLayerSegmentInterval{{
				PercentStartInclusive: 0,
				PercentEndExclusive:   0,
			}},
			Segment: nil,
		},
		StartTime:     timestamppb.Now(),
		EndTime:       timestamppb.New(time.Now().AddDate(0, 0, 5)),
		CreatedBy:     "avinash",
		LastUpdatedBy: "avinash",
		CreatedAt:     timestamppb.New(time.Now()),
		UpdatedAt:     timestamppb.New(time.Now()),
		Variables: []*questtypes.Variable{
			variable1,
		},
		Variants: []*questtypes.Variant{variant1},
	}
	experiment2 := &questtypes.Experiment{}
	_ = copier.Copy(experiment2, experiment1)
	newCtx := context.Background()
	newCtx = epificontext.CtxWithInternalUserEmail(newCtx, "sakthi")
	newCtx = epificontext.AppendInternalUserEmailToOutgoingContext(newCtx, "sakthi")
	experiment2.LastUpdatedBy = "sakthi"
	experiment2.Status = questtypes.ExperimentStatus_EXPERIMENT_STATUS_ENABLED
	experiment1.Status = questtypes.ExperimentStatus_EXPERIMENT_STATUS_DISABLED
	type args struct {
		ctx context.Context
		req *pb.UpdateExperimentStatusRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(experimentDao *mocks.MockExperimentDao, variantDao *mocks.MockVariantDao, variableDao *mocks.MockVariableDao, variantVariableMap *mocks.MockVariantVariableMapDao, cache *mocks.MockQuestSdkCache)
		want           *pb.UpdateExperimentStatusResponse
		wantErr        bool
	}{
		{
			name: "update experiment status, success",
			args: args{
				ctx: newCtx,
				req: &pb.UpdateExperimentStatusRequest{
					ExpId:         experiment1.GetId(),
					ExpStatus:     questtypes.ExperimentStatus_EXPERIMENT_STATUS_ENABLED,
					LastUpdatedBy: "sakthi",
				},
			},
			setupMockCalls: func(experimentDao *mocks.MockExperimentDao, variantDao *mocks.MockVariantDao, variableDao *mocks.MockVariableDao, variantVariableMap *mocks.MockVariantVariableMapDao, cache *mocks.MockQuestSdkCache) {
				experimentDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Experiment{experiment1}, nil, nil).AnyTimes()
				variantDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Variant{variant1}, nil, nil).AnyTimes()
				variableDao.EXPECT().GetVariablesByVariantIds(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Variable{variable1}, nil).AnyTimes()
				experimentDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				variantVariableMap.EXPECT().GetByVariantIdAndVariableId(gomock.Any(), gomock.Any(), gomock.Any()).Return(&web.DataValue{DataValue: &web.DataValue_BoolValue{BoolValue: false}}, nil).AnyTimes()
				cache.EXPECT().SetValueInCache(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				cache.EXPECT().SetExperimentVariablesMapping(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			},
			want: &pb.UpdateExperimentStatusResponse{
				Status: rpc.StatusOk(),
				Exp:    experiment2,
			},
			wantErr: false,
		},
		{
			name: "update experiment status for invalid expID, failure",
			args: args{
				ctx: context.Background(),
				req: &pb.UpdateExperimentStatusRequest{
					ExpId:         "44b35f9b-ebaa-4808-b52a-671ce1adggff",
					ExpStatus:     questtypes.ExperimentStatus_EXPERIMENT_STATUS_ENABLED,
					LastUpdatedBy: "sakthi",
				},
			},
			setupMockCalls: func(experimentDao *mocks.MockExperimentDao, variantDao *mocks.MockVariantDao, variableDao *mocks.MockVariableDao, variantVariableMap *mocks.MockVariantVariableMapDao, cache *mocks.MockQuestSdkCache) {
				experimentDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, gorm.ErrInvalidData).Times(1)
			},
			want: &pb.UpdateExperimentStatusResponse{
				Status: rpc.NewStatus(uint32(code.Code_INTERNAL), gorm.ErrInvalidData.Error(), ""),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockExperimentDao := mocks.NewMockExperimentDao(ctr)
			mockVariableDao := mocks.NewMockVariableDao(ctr)
			mockVariantDao := mocks.NewMockVariantDao(ctr)
			mockQuestSdkDao := mocks.NewMockQuestSdkCache(ctr)
			mockVariantVariableDao := mocks.NewMockVariantVariableMapDao(ctr)
			mockTxnManager := mocks2.NewMockTxnExecutor(ctr)
			s := &Service{
				experimentDao:         mockExperimentDao,
				variantDao:            mockVariantDao,
				variableDao:           mockVariableDao,
				questSdkCacheDao:      mockQuestSdkDao,
				variantVariableMapDao: mockVariantVariableDao,
				questDbTxnExecutor:    mockTxnManager,
			}
			tt.setupMockCalls(mockExperimentDao, mockVariantDao, mockVariableDao, mockVariantVariableDao, mockQuestSdkDao)
			got, err := s.UpdateExperimentStatus(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateExperimentStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("UpdateExperimentStatus() diff: %s", diff)
			}
		})
	}
}

//nolint:govet
func TestService_GetServersList(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	var serverList = []string{
		"frontend",
		"fittt",
	}
	type args struct {
		ctx context.Context
		req *pb.GetServersListRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(variableDaoMock *mocks.MockVariableDao)
		want           *pb.GetServersListResponse
		wantErr        bool
	}{
		{
			name: "happy flow",
			args: args{
				ctx: context.Background(),
				req: &pb.GetServersListRequest{},
			},
			setupMockCalls: func(variableDaoMock *mocks.MockVariableDao) {
				variableDaoMock.EXPECT().GetServersList(gomock.Any()).Return(serverList, nil).AnyTimes()
			},
			want: &pb.GetServersListResponse{
				Status:  rpc.StatusOk(),
				Servers: serverList,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockVariableDao := mocks.NewMockVariableDao(ctr)
			tt.setupMockCalls(mockVariableDao)
			mockTxnManager := mocks2.NewMockTxnExecutor(ctr)
			s := &Service{
				variableDao:        mockVariableDao,
				questDbTxnExecutor: mockTxnManager,
			}
			got, err := s.GetServersList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetServersList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetServersList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

//nolint:govet
func TestService_GetAreasList(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	var areasList = []string{
		"frontend",
		"fittt",
	}
	type args struct {
		ctx context.Context
		req *pb.GetAreasListRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(variableDaoMock *mocks.MockVariableDao)
		want           *pb.GetAreasListResponse
		wantErr        bool
	}{
		{
			name: "happy flow",
			args: args{
				ctx: context.Background(),
				req: &pb.GetAreasListRequest{},
			},
			setupMockCalls: func(variableDaoMock *mocks.MockVariableDao) {
				variableDaoMock.EXPECT().GetAreasList(gomock.Any()).Return(areasList, nil).AnyTimes()
			},
			want: &pb.GetAreasListResponse{
				Status: rpc.StatusOk(),
				Areas:  areasList,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockVariableDao := mocks.NewMockVariableDao(ctr)
			tt.setupMockCalls(mockVariableDao)
			mockTxnManager := mocks2.NewMockTxnExecutor(ctr)
			s := &Service{
				variableDao:        mockVariableDao,
				questDbTxnExecutor: mockTxnManager,
			}
			got, err := s.GetAreasList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAreasList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAreasList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

//nolint:govet
func TestService_GetAllLeafLayers(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	var layersList = []*questtypes.Layer{
		{
			Id:   "123",
			Name: "Layer1",
		},
		{
			Id:   "124",
			Name: "Layer2",
		},
	}
	type args struct {
		ctx context.Context
		req *pb.GetAllLeafLayersRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(layerDaoMock *mocks.MockLayerDao)
		want           *pb.GetAllLeafLayersResponse
		wantErr        bool
	}{
		{
			name: "list all leaf layers",
			args: args{
				ctx: context.Background(),
				req: &pb.GetAllLeafLayersRequest{},
			},
			setupMockCalls: func(layerDaoMocks *mocks.MockLayerDao) {
				layerDaoMocks.EXPECT().GetAllLeafLayers(gomock.Any(), gomock.Any()).Return(layersList, nil)
			},
			want: &pb.GetAllLeafLayersResponse{
				Status: rpc.StatusOk(),
				Layers: layersList,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockLayerDao := mocks.NewMockLayerDao(ctr)
			tt.setupMockCalls(mockLayerDao)
			s := &Service{
				layerDao: mockLayerDao,
			}
			got, err := s.GetAllLeafLayers(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllLeafLayers() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetAllLeafLayers() diff: %s", diff)
			}
		})
	}
}

// nolint:govet
func TestService_CreateLayers(t *testing.T) {
	t.Skip("TODO(growth-infra): Re-enable the test once the issue is fixed")
	t.Parallel()
	logger.Init("test")
	layers := []*questtypes.Layer{
		{
			Name:                 "GlobalLayer",
			ParentLayer:          "",
			IsOverlappingAllowed: true,
			Status:               questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "ControlGroupLayer",
			ParentLayer:          "GlobalLayer",
			IsOverlappingAllowed: false,
			Status:               questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "HomeLayer",
			ParentLayer:          "GlobalLayer",
			IsOverlappingAllowed: false,
			VariableExpressions: []string{
				"frontend/home/<USER>",
				"!frontend/home/<USER>/Cards/*",
				"!frontend/home/<USER>/SearchBar/*",
			},
			Status: questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "InvestLayer",
			ParentLayer:          "GlobalLayer",
			IsOverlappingAllowed: true,
			Status:               questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "InvestHomeLayer",
			ParentLayer:          "InvestLayer",
			IsOverlappingAllowed: false,
			VariableExpressions: []string{
				"frontend/invest/home/<USER>",
			},
			Status: questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "OthersLayer",
			ParentLayer:          "GlobalLayer",
			IsOverlappingAllowed: true,
			VariableExpressions: []string{
				"frontend/home/<USER>/SearchBar/*",
				"frontend/home/<USER>/Cards/*",
			},
			Status: questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
	}
	layers2 := []*questtypes.Layer{
		{
			Name:                 "GlobalLayerV2",
			ParentLayer:          "",
			IsOverlappingAllowed: true,
			Status:               questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
	}
	type args struct {
		ctx context.Context
		req *pb.CreateLayersRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(layerDaoMock *mocks.MockLayerDao, experimentDaoMocks *mocks.MockExperimentDao)
		want           *pb.CreateLayersResponse
		wantErr        bool
	}{
		{
			name: "create layers, success",
			args: args{
				ctx: context.Background(),
				req: &pb.CreateLayersRequest{Layers: layers},
			},
			setupMockCalls: func(layerDaoMocks *mocks.MockLayerDao, experimentDaoMocks *mocks.MockExperimentDao) {
				layerDaoMocks.EXPECT().GetBulkLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
				layerDaoMocks.EXPECT().GetBulkLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
				layerDaoMocks.EXPECT().GetBulkLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(layers, nil).AnyTimes()
				layerDaoMocks.EXPECT().Create(gomock.Any(), gomock.Any()).Return(layers[0], nil).AnyTimes()
				layerDaoMocks.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				experimentDaoMocks.EXPECT().GetActiveExperimentsForLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			want: &pb.CreateLayersResponse{
				Status: rpc.StatusOk(),
				Layers: layers,
			},
			wantErr: false,
		},
		{
			name: "create layers, failure case 2 - already layers exists",
			args: args{
				ctx: context.Background(),
				req: &pb.CreateLayersRequest{Layers: layers2},
			},
			setupMockCalls: func(layerDaoMocks *mocks.MockLayerDao, experimentDaoMocks *mocks.MockExperimentDao) {
				layerDaoMocks.EXPECT().GetBulkLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(layers, nil).AnyTimes()
				layerDaoMocks.EXPECT().Create(gomock.Any(), gomock.Any()).Return(layers[0], nil).AnyTimes()
				layerDaoMocks.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				experimentDaoMocks.EXPECT().GetActiveExperimentsForLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			want: &pb.CreateLayersResponse{
				Status: rpc.NewStatus(uint32(code.Code_INTERNAL), "layers with names already exists and archived: GlobalLayer", ""),
			},
			wantErr: false,
		},
		{
			name: "create layers, success case 3 - already layers exists but different",
			args: args{
				ctx: context.Background(),
				req: &pb.CreateLayersRequest{Layers: layers2},
			},
			setupMockCalls: func(layerDaoMocks *mocks.MockLayerDao, experimentDaoMocks *mocks.MockExperimentDao) {
				layerDaoMocks.EXPECT().GetBulkLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(layers, nil).Times(1)
				layerDaoMocks.EXPECT().GetBulkLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(layers, nil).Times(1)
				layerDaoMocks.EXPECT().GetBulkLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(layers2, nil).AnyTimes()
				layerDaoMocks.EXPECT().Create(gomock.Any(), gomock.Any()).Return(layers[0], nil).AnyTimes()
				layerDaoMocks.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				experimentDaoMocks.EXPECT().GetActiveExperimentsForLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			want: &pb.CreateLayersResponse{
				Status: rpc.StatusOk(),
				Layers: layers2,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockLayerDao := mocks.NewMockLayerDao(ctr)
			experimentLayerDao := mocks.NewMockExperimentDao(ctr)
			questGenConf, err := genconf.Load()
			if err != nil {
				logger.Fatal("failed to load gen conf", zap.Error(err))
			}
			tt.setupMockCalls(mockLayerDao, experimentLayerDao)
			s := &Service{
				questGenConf:       questGenConf,
				layerDao:           mockLayerDao,
				experimentDao:      experimentLayerDao,
				questDbTxnExecutor: TxnExecutor{},
			}
			got, err := s.CreateLayers(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateLayers() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("CreateLayers() diff: %s", diff)
			}
		})
	}
}

//nolint:govet
func TestService_GetConflictingExperiments(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	layers1 := []*questtypes.Layer{
		{
			Name:                 "GlobalLayer",
			ParentLayer:          "",
			IsOverlappingAllowed: true,
			Status:               questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "ControlGroupLayer",
			ParentLayer:          "GlobalLayer",
			IsOverlappingAllowed: false,
			Status:               questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "HomeLayer",
			ParentLayer:          "GlobalLayer",
			IsOverlappingAllowed: false,
			VariableExpressions: []string{
				"frontend/home/<USER>",
				"!frontend/home/<USER>/Cards/*",
				"!frontend/home/<USER>/SearchBar/*",
			},
			Status: questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "InvestLayer",
			ParentLayer:          "GlobalLayer",
			IsOverlappingAllowed: true,
			Status:               questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "InvestHomeLayer",
			ParentLayer:          "InvestLayer",
			IsOverlappingAllowed: false,
			VariableExpressions: []string{
				"frontend/invest/home/<USER>",
			},
			Status: questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "OthersLayer",
			ParentLayer:          "GlobalLayer",
			IsOverlappingAllowed: true,
			VariableExpressions: []string{
				"frontend/home/<USER>/SearchBar/*",
				"frontend/home/<USER>/Cards/*",
			},
			Status: questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
	}
	layers2 := []*questtypes.Layer{
		{
			Name:                 "GlobalLayer",
			ParentLayer:          "",
			IsOverlappingAllowed: true,
			Status:               questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "ControlGroupLayer",
			ParentLayer:          "GlobalLayer",
			IsOverlappingAllowed: false,
			Status:               questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "HomeLayer",
			ParentLayer:          "GlobalLayer",
			IsOverlappingAllowed: false,
			VariableExpressions: []string{
				"frontend/home/<USER>",
				"!frontend/home/<USER>/Cards/*",
				"frontend/home/<USER>/SearchBar/*",
			},
			Status: questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "InvestLayer",
			ParentLayer:          "GlobalLayer",
			IsOverlappingAllowed: true,
			Status:               questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "InvestHomeLayer",
			ParentLayer:          "InvestLayer",
			IsOverlappingAllowed: false,
			VariableExpressions: []string{
				"frontend/invest/home/<USER>",
			},
			Status: questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "OthersLayer",
			ParentLayer:          "GlobalLayer",
			IsOverlappingAllowed: true,
			VariableExpressions: []string{
				"!frontend/home/<USER>/SearchBar/*",
				"frontend/home/<USER>/Cards/*",
			},
			Status: questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
	}
	layers3 := []*questtypes.Layer{
		{
			Name:                 "GlobalLayer",
			ParentLayer:          "",
			IsOverlappingAllowed: true,
			Status:               questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "ControlGroupLayer",
			ParentLayer:          "GlobalLayer",
			IsOverlappingAllowed: false,
			Status:               questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "HomeLayer",
			ParentLayer:          "GlobalLayer",
			IsOverlappingAllowed: false,
			VariableExpressions: []string{
				"frontend/home/<USER>",
				"!frontend/home/<USER>/Cards/*",
				"!frontend/home/<USER>/SearchBar/*",
			},
			Status: questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "InvestLayer",
			ParentLayer:          "GlobalLayer",
			IsOverlappingAllowed: true,
			Status:               questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "InvestHomeLayer",
			ParentLayer:          "InvestLayer",
			IsOverlappingAllowed: false,
			VariableExpressions: []string{
				"frontend/invest/home/<USER>",
				"*",
			},
			Status: questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
		{
			Name:                 "OthersLayer",
			ParentLayer:          "GlobalLayer",
			IsOverlappingAllowed: true,
			VariableExpressions: []string{
				"frontend/home/<USER>/Cards/*",
			},
			Status: questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		},
	}
	type args struct {
		ctx context.Context
		req *pb.GetConflictingExperimentsRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(layerDaoMock *mocks.MockLayerDao, experimentDaoMocks *mocks.MockExperimentDao, variableDaoMocks *mocks.MockVariableDao, variantDaoMocks *mocks.MockVariantDao)
		want           *pb.GetConflictingExperimentsResponse
		wantErr        bool
	}{
		{
			name: "no conflicts experiment",
			args: args{
				ctx: context.Background(),
				req: &pb.GetConflictingExperimentsRequest{Layers: layers1},
			},
			setupMockCalls: func(layerDaoMocks *mocks.MockLayerDao, experimentDaoMocks *mocks.MockExperimentDao, variableDaoMocks *mocks.MockVariableDao, variantDaoMocks *mocks.MockVariantDao) {
				layerDaoMocks.EXPECT().GetBulkLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				experimentDaoMocks.EXPECT().GetActiveExperimentsForLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			want: &pb.GetConflictingExperimentsResponse{
				Status:      rpc.StatusOk(),
				Experiments: nil,
			},
			wantErr: false,
		},
		{
			name: "has conflicts experiment, where 1 variable_expression is changed",
			args: args{
				ctx: context.Background(),
				req: &pb.GetConflictingExperimentsRequest{Layers: layers2},
			},
			setupMockCalls: func(layerDaoMocks *mocks.MockLayerDao, experimentDaoMocks *mocks.MockExperimentDao, variableDaoMocks *mocks.MockVariableDao, variantDaoMocks *mocks.MockVariantDao) {
				layerDaoMocks.EXPECT().GetBulkLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(layers1, nil).AnyTimes()
				experimentDaoMocks.EXPECT().GetActiveExperimentsForLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
				experimentDaoMocks.EXPECT().GetActiveExperimentsForLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Experiment{experiment1}, nil).Times(1)
				variableDaoMocks.EXPECT().GetVariablesByVariantIds(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Variable{variable1}, nil).AnyTimes()
				variantDaoMocks.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, nil).AnyTimes()
			},
			want: &pb.GetConflictingExperimentsResponse{
				Status:      rpc.StatusOk(),
				Experiments: []*questtypes.Experiment{experiment1},
			},
			wantErr: false,
		},
		{
			name: "has conflicts experiment, where only layer is deleted",
			args: args{
				ctx: context.Background(),
				req: &pb.GetConflictingExperimentsRequest{Layers: layers3},
			},
			setupMockCalls: func(layerDaoMocks *mocks.MockLayerDao, experimentDaoMocks *mocks.MockExperimentDao, variableDaoMocks *mocks.MockVariableDao, variantDaoMocks *mocks.MockVariantDao) {
				layerDaoMocks.EXPECT().GetBulkLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(layers1, nil).AnyTimes()
				experimentDaoMocks.EXPECT().GetActiveExperimentsForLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
				experimentDaoMocks.EXPECT().GetActiveExperimentsForLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Experiment{experiment1}, nil).Times(1)
				variableDaoMocks.EXPECT().GetVariablesByVariantIds(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Variable{variable1}, nil).AnyTimes()
				variantDaoMocks.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, nil).AnyTimes()
			},
			want: &pb.GetConflictingExperimentsResponse{
				Status:      rpc.StatusOk(),
				Experiments: []*questtypes.Experiment{experiment1},
			},
			wantErr: false,
		},
		{
			name: "has conflicts experiment, where only layer is deleted & added in other layer",
			args: args{
				ctx: context.Background(),
				req: &pb.GetConflictingExperimentsRequest{Layers: layers3},
			},
			setupMockCalls: func(layerDaoMocks *mocks.MockLayerDao, experimentDaoMocks *mocks.MockExperimentDao, variableDaoMocks *mocks.MockVariableDao, variantDaoMocks *mocks.MockVariantDao) {
				layerDaoMocks.EXPECT().GetBulkLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(layers1, nil).AnyTimes()
				experimentDaoMocks.EXPECT().GetActiveExperimentsForLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
				experimentDaoMocks.EXPECT().GetActiveExperimentsForLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Experiment{experiment1}, nil).Times(1)
				variableDaoMocks.EXPECT().GetVariablesByVariantIds(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Variable{variable1}, nil).AnyTimes()
				variantDaoMocks.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, nil).AnyTimes()
			},
			want: &pb.GetConflictingExperimentsResponse{
				Status:      rpc.StatusOk(),
				Experiments: []*questtypes.Experiment{experiment1},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockLayerDao := mocks.NewMockLayerDao(ctr)
			experimentLayerDao := mocks.NewMockExperimentDao(ctr)
			variableDao := mocks.NewMockVariableDao(ctr)
			variantDao := mocks.NewMockVariantDao(ctr)
			questGenConf, err := genconf.Load()
			if err != nil {
				logger.Fatal("failed to load gen conf", zap.Error(err))
			}
			tt.setupMockCalls(mockLayerDao, experimentLayerDao, variableDao, variantDao)
			s := &Service{
				questGenConf:       questGenConf,
				layerDao:           mockLayerDao,
				experimentDao:      experimentLayerDao,
				variableDao:        variableDao,
				variantDao:         variantDao,
				questDbTxnExecutor: TxnExecutor{},
			}
			got, err := s.GetConflictingExperiments(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetConflictingExperiments() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetConflictingExperiments() diff: %s", diff)
			}
		})
	}
}

func TestService_CreateQuestVariables(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	layer := &questtypes.Layer{
		Name:                 "RewardsLayer",
		ParentLayer:          "GlobalLayer",
		IsOverlappingAllowed: true,
		VariableExpressions: []string{
			"rewards/x/y/z/*",
		},
	}
	rewardsVariable1 := &questtypes.Variable{
		Path:   "rewards/x/y/z/reward-1",
		Server: "rewards",
		Layer:  "RewardsLayer",
		Datatype: &web.Datatype{
			BaseType:     0,
			ExtendedType: 0,
			IsArray:      false,
		},
	}
	rewardsVariable2 := &questtypes.Variable{
		Path:   "rewards/x/y/z/reward-2",
		Server: "rewards",
		Layer:  "RewardsLayer",
		Datatype: &web.Datatype{
			BaseType:     0,
			ExtendedType: 0,
			IsArray:      false,
		},
	}
	rewardsVariable3 := &questtypes.Variable{
		Path:   "frontend/rewards/x/y/z/reward-3",
		Server: "rewards",
		Layer:  "RewardsLayer",
		Datatype: &web.Datatype{
			BaseType:     0,
			ExtendedType: 0,
			IsArray:      false,
		},
	}
	rewardsVariable4 := &questtypes.Variable{
		Path:   "rewards/x/y/reward-4",
		Server: "rewards",
		Datatype: &web.Datatype{
			BaseType:     0,
			ExtendedType: 0,
			IsArray:      false,
		},
	}
	rewardsVariable5 := &questtypes.Variable{
		Path:   "rewards/x/y/reward-5",
		Server: "rewards",
		Layer:  "RewardsLayer",
		Datatype: &web.Datatype{
			BaseType:     0,
			ExtendedType: 0,
			IsArray:      false,
		},
	}
	rewardsVariable6 := &questtypes.Variable{
		Path:   "rewards/x/y/reward-6",
		Server: "rewards",
		Layer:  "RewardsLayer",
	}

	type args struct {
		ctx context.Context
		req *pb.CreateQuestVariablesRequest
	}
	tests := []struct {
		name           string
		setupMockCalls func(layerDaoMock *mocks.MockLayerDao, variableDaoMocks *mocks.MockVariableDao, txnExecutorMocks *mocks2.MockTxnExecutor)
		args           args
		want           *pb.CreateQuestVariablesResponse
		wantErr        bool
	}{
		{
			name: "create variables, success",
			args: args{
				ctx: context.Background(),
				req: &pb.CreateQuestVariablesRequest{
					Variables: []*questtypes.Variable{
						rewardsVariable1, rewardsVariable2,
					},
				},
			},
			setupMockCalls: func(layerDaoMock *mocks.MockLayerDao, variableDaoMocks *mocks.MockVariableDao, txnExecutorMocks *mocks2.MockTxnExecutor) {
				txnExecutorMocks.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				layerDaoMock.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(layer, nil).AnyTimes()
				variableDaoMocks.EXPECT().Create(gomock.Any(), gomock.Any()).Return(rewardsVariable1, nil).AnyTimes()
			},
			want: &pb.CreateQuestVariablesResponse{
				Status: rpc.StatusOk(),
				Variables: []*questtypes.Variable{
					rewardsVariable1, rewardsVariable2,
				},
			},
			wantErr: false,
		},
		{
			name: "create variables, failure because of server validation",
			args: args{
				ctx: context.Background(),
				req: &pb.CreateQuestVariablesRequest{
					Variables: []*questtypes.Variable{
						rewardsVariable1, rewardsVariable3,
					},
				},
			},
			setupMockCalls: func(layerDaoMock *mocks.MockLayerDao, variableDaoMocks *mocks.MockVariableDao, txnExecutorMocks *mocks2.MockTxnExecutor) {
				txnExecutorMocks.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				layerDaoMock.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(layer, nil).AnyTimes()
				variableDaoMocks.EXPECT().Create(gomock.Any(), gomock.Any()).Return(rewardsVariable1, nil).AnyTimes()
			},
			want: &pb.CreateQuestVariablesResponse{
				Status: rpc.NewStatus(uint32(code.Code_INTERNAL), "variable paths doesn't start with server name frontend/rewards/x/y/z/reward-3", ""),
			},
			wantErr: false,
		},
		{
			name: "create variables, failure because of layer validation of empty layer",
			args: args{
				ctx: context.Background(),
				req: &pb.CreateQuestVariablesRequest{
					Variables: []*questtypes.Variable{
						rewardsVariable1, rewardsVariable4,
					},
				},
			},
			setupMockCalls: func(layerDaoMock *mocks.MockLayerDao, variableDaoMocks *mocks.MockVariableDao, txnExecutorMocks *mocks2.MockTxnExecutor) {
				txnExecutorMocks.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				layerDaoMock.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(layer, nil).AnyTimes()
				variableDaoMocks.EXPECT().Create(gomock.Any(), gomock.Any()).Return(rewardsVariable1, nil).AnyTimes()
			},
			want: &pb.CreateQuestVariablesResponse{
				Status: rpc.NewStatus(uint32(code.Code_INTERNAL), "variable layer can't be empty for variable rewards/x/y/reward-4", ""),
			},
			wantErr: false,
		},
		{
			name: "create variables, failure because of layer validation for variable",
			args: args{
				ctx: context.Background(),
				req: &pb.CreateQuestVariablesRequest{
					Variables: []*questtypes.Variable{
						rewardsVariable1, rewardsVariable5,
					},
				},
			},
			setupMockCalls: func(layerDaoMock *mocks.MockLayerDao, variableDaoMocks *mocks.MockVariableDao, txnExecutorMocks *mocks2.MockTxnExecutor) {
				txnExecutorMocks.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				layerDaoMock.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(layer, nil).AnyTimes()
				variableDaoMocks.EXPECT().Create(gomock.Any(), gomock.Any()).Return(rewardsVariable1, nil).AnyTimes()
			},
			want: &pb.CreateQuestVariablesResponse{
				Status: rpc.NewStatus(uint32(code.Code_INTERNAL), "variable paths are conflicting with the layer RewardsLayer for variable rewards/x/y/reward-5", ""),
			},
			wantErr: false,
		},
		{
			name: "create variables, failure because of datatype validation for variable",
			args: args{
				ctx: context.Background(),
				req: &pb.CreateQuestVariablesRequest{
					Variables: []*questtypes.Variable{
						rewardsVariable1, rewardsVariable6,
					},
				},
			},
			setupMockCalls: func(layerDaoMock *mocks.MockLayerDao, variableDaoMocks *mocks.MockVariableDao, txnExecutorMocks *mocks2.MockTxnExecutor) {
				txnExecutorMocks.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				layerDaoMock.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(layer, nil).AnyTimes()
				variableDaoMocks.EXPECT().Create(gomock.Any(), gomock.Any()).Return(rewardsVariable1, nil).AnyTimes()
			},
			want: &pb.CreateQuestVariablesResponse{
				Status: rpc.NewStatus(uint32(code.Code_INTERNAL), "variable datatype can't be nil for variable rewards/x/y/reward-6", ""),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			variableDao := mocks.NewMockVariableDao(ctr)
			layerDao := mocks.NewMockLayerDao(ctr)
			txnExecutor := mocks2.NewMockTxnExecutor(ctr)
			questGenConf, err := genconf.Load()
			if err != nil {
				logger.Fatal("failed to load gen conf", zap.Error(err))
			}
			tt.setupMockCalls(layerDao, variableDao, txnExecutor)
			s := &Service{
				questGenConf:       questGenConf,
				variableDao:        variableDao,
				layerDao:           layerDao,
				questDbTxnExecutor: txnExecutor,
			}
			got, err := s.CreateQuestVariables(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateQuestVariables() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("CreateQuestVariables() diff %s", diff)
			}
		})
	}
}

// nolint:govet
func TestService_GetVariables(t *testing.T) {
	t.Parallel()
	logger.Init("test")

	type args struct {
		ctx     context.Context
		request *pb.GetVariablesRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(variableDao *mocks.MockVariableDao)
		want           *pb.GetVariablesResponse
		wantErr        bool
	}{
		{
			name: "happy flow",
			args: args{
				ctx:     context.Background(),
				request: &pb.GetVariablesRequest{},
			},
			setupMockCalls: func(variableDao *mocks.MockVariableDao) {
				variableDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Variable{variable1, variable2}, nil, nil).AnyTimes()
			},
			want: &pb.GetVariablesResponse{
				Status: rpc.StatusOk(),
				Variables: []*questtypes.Variable{
					variable1, variable2,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockVariableDao := mocks.NewMockVariableDao(ctr)
			s := &Service{
				variableDao: mockVariableDao,
			}
			tt.setupMockCalls(mockVariableDao)
			got, err := s.GetVariables(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetVariables() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetVariables() diff = %s", diff)
			}
		})
	}
}

//nolint:govet
func TestService_ValidateVariables(t *testing.T) {
	t.Parallel()
	logger.Init("test")

	type args struct {
		ctx     context.Context
		request *pb.ValidateVariablesRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(variableDao *mocks.MockVariableDao)
		want           *pb.ValidateVariablesResponse
		wantErr        bool
	}{
		{
			name: "success, no invalid variabkes",
			args: args{
				ctx: context.Background(),
				request: &pb.ValidateVariablesRequest{
					VariablePaths: []string{"frontend/home/<USER>/SearchBar/HelloText"},
					LayerName:     "InvestLayer",
				},
			},
			setupMockCalls: func(variableDao *mocks.MockVariableDao) {
				variableDao.EXPECT().GetByPath(gomock.Any(), gomock.Any(), gomock.Any()).Return(variable1, nil).AnyTimes()
			},
			want: &pb.ValidateVariablesResponse{
				Status:               rpc.StatusOk(),
				InvalidVariablePaths: nil,
			},
		},
		{
			name: "success, invalid variables",
			args: args{
				ctx: context.Background(),
				request: &pb.ValidateVariablesRequest{
					VariablePaths: []string{"frontend/home/<USER>/SearchBar/HelloText"},
					LayerName:     "HomeLayer",
				},
			},
			setupMockCalls: func(variableDao *mocks.MockVariableDao) {
				variableDao.EXPECT().GetByPath(gomock.Any(), gomock.Any(), gomock.Any()).Return(variable1, nil).AnyTimes()
			},
			want: &pb.ValidateVariablesResponse{
				Status:               rpc.StatusOk(),
				InvalidVariablePaths: []string{"frontend/home/<USER>/SearchBar/HelloText"},
			},
		},
		{
			name: "failure, invalid variables",
			args: args{
				ctx: context.Background(),
				request: &pb.ValidateVariablesRequest{
					VariablePaths: []string{"frontend/home/<USER>/SearchBar/HelloText"},
					LayerName:     "InvestLayer",
				},
			},
			setupMockCalls: func(variableDao *mocks.MockVariableDao) {
				variableDao.EXPECT().GetByPath(gomock.Any(), gomock.Any(), gomock.Any()).Return(variable1, epifierrors.ErrRecordNotFound).AnyTimes()
			},
			want: &pb.ValidateVariablesResponse{
				Status:               rpc.StatusOk(),
				InvalidVariablePaths: []string{"frontend/home/<USER>/SearchBar/HelloText"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockVariableDao := mocks.NewMockVariableDao(ctr)
			s := &Service{
				variableDao: mockVariableDao,
			}
			tt.setupMockCalls(mockVariableDao)
			got, err := s.ValidateVariables(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateVariables() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("ValidateVariables() diff = %s", diff)
			}
		})
	}
}

// nolint:govet
func TestService_GetUserBucketAvailability(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	layer1 := &questtypes.Layer{
		Name:                       "GlobalLayer",
		ParentLayer:                "",
		IsOverlappingAllowed:       true,
		Status:                     questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		MaxUserSegmentBucketForExp: 10,
		MaxUserSegmentBucket:       100,
	}
	layer2 := &questtypes.Layer{
		Name:                       "ControlGroupLayer",
		ParentLayer:                "GlobalLayer",
		IsOverlappingAllowed:       false,
		Status:                     questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		MaxUserSegmentBucketForExp: 10,
		MaxUserSegmentBucket:       100,
	}
	layer3 := &questtypes.Layer{
		Name:                 "OthersLayer",
		ParentLayer:          "GlobalLayer",
		IsOverlappingAllowed: false,
		VariableExpressions: []string{
			"frontend/home/<USER>",
			"!frontend/home/<USER>/Cards/*",
			"!frontend/home/<USER>/SearchBar/*",
		},
		Status:                     questtypes.LayerStatus_LAYER_STATUS_ACTIVE,
		MaxUserSegmentBucketForExp: 10,
		MaxUserSegmentBucket:       100,
	}
	expVers := &questtypes.ExperimentVersion{
		Id:          "123",
		Description: "test",
		Status:      questtypes.ExperimentVersionStatus_EXPERIMENT_VERSION_STATUS_WAITING_FOR_APPROVAL,
		ExpData: &questtypes.Experiment{
			Id:     "exp-123",
			Name:   "test",
			Status: questtypes.ExperimentStatus_EXPERIMENT_STATUS_ENABLED,
			Layer:  "OthersLayer",
		},
	}
	exp := &questtypes.Experiment{
		Id:     "exp-123",
		Name:   "test",
		Status: questtypes.ExperimentStatus_EXPERIMENT_STATUS_ENABLED,
		Layer:  "OthersLayer",
	}
	type args struct {
		ctx     context.Context
		request *pb.GetUserBucketAvailabilityRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(layerDao *mocks.MockLayerDao, experimentDao *mocks.MockExperimentDao, experimentVersionDao *mocks.MockExperimentVersionDao)
		want           *pb.GetUserBucketAvailabilityResponse
		wantErr        bool
	}{
		{
			name: "success, no invalid variables",
			args: args{
				ctx: context.Background(),
				request: &pb.GetUserBucketAvailabilityRequest{
					LayerName: "OthersLayer",
					ExpName:   "123",
				},
			},
			setupMockCalls: func(layerDao *mocks.MockLayerDao, experimentDao *mocks.MockExperimentDao, experimentVersionDao *mocks.MockExperimentVersionDao) {
				layerDao.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(layer3, nil).Times(1)
				layerDao.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(layer1, nil).Times(1)
				layerDao.EXPECT().GetNonOverlappingChildren(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Layer{layer2}, nil).Times(1)
				layerDao.EXPECT().GetLayersInSubtree(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
				layerDao.EXPECT().GetNonOverlappingChildren(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Layer{layer2}, nil).AnyTimes()
				// layerDao.EXPECT().GetLayersInSubtree(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
				// layerDao.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(layer1, nil).Times(1)
				// layerDao.EXPECT().GetLayersInSubtree(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
				experimentDao.EXPECT().GetActiveExperimentsForLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Experiment{experiment1}, nil).AnyTimes()
				// layerDao.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(layer3, nil).Times(1)
				layerDao.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(layer1, nil).Times(1)
				layerDao.EXPECT().GetLayersInSubtree(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Layer{layer2}, nil).Times(1)
				layerDao.EXPECT().GetLayersInSubtree(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Layer{layer2}, nil).AnyTimes()
				experimentDao.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(exp, nil).AnyTimes()
				experimentVersionDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.ExperimentVersion{expVers}, nil, nil).AnyTimes()
			},
			want: &pb.GetUserBucketAvailabilityResponse{
				Status:                      rpc.StatusOk(),
				ReservedPercent:             10,
				RemainingPercent:            100, // TODO Change this to 90 once remaining percent computation is fixed
				MaxUserBucketsPerExperiment: 10,
			},
		},
		{
			name: "success, experiment version is not empty",
			args: args{
				ctx: context.Background(),
				request: &pb.GetUserBucketAvailabilityRequest{
					LayerName: "OthersLayer",
				},
			},
			setupMockCalls: func(layerDao *mocks.MockLayerDao, experimentDao *mocks.MockExperimentDao, experimentVersionDao *mocks.MockExperimentVersionDao) {
				layerDao.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(layer3, nil).Times(1)
				layerDao.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(layer1, nil).Times(1)
				layerDao.EXPECT().GetNonOverlappingChildren(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Layer{layer2}, nil).Times(1)
				layerDao.EXPECT().GetLayersInSubtree(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
				layerDao.EXPECT().GetNonOverlappingChildren(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Layer{layer2}, nil).AnyTimes()
				// layerDao.EXPECT().GetLayersInSubtree(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
				// layerDao.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(layer1, nil).Times(1)
				// layerDao.EXPECT().GetLayersInSubtree(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
				experimentDao.EXPECT().GetActiveExperimentsForLayers(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Experiment{experiment1}, nil).AnyTimes()
				// layerDao.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(layer3, nil).Times(1)
				layerDao.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(layer1, nil).Times(1)
				layerDao.EXPECT().GetLayersInSubtree(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Layer{layer2}, nil).Times(1)
				layerDao.EXPECT().GetLayersInSubtree(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.Layer{layer2}, nil).AnyTimes()
				experimentVersionDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(expVers, nil).AnyTimes()
				experimentDao.EXPECT().GetByName(gomock.Any(), gomock.Any(), gomock.Any()).Return(exp, nil).AnyTimes()
				experimentVersionDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.ExperimentVersion{expVers}, nil, nil).AnyTimes()
			},
			want: &pb.GetUserBucketAvailabilityResponse{
				Status:                      rpc.StatusOk(),
				ReservedPercent:             10,
				RemainingPercent:            100, // TODO Change this to 90 once remaining percent computation is fixed
				MaxUserBucketsPerExperiment: 10,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockLayerDao := mocks.NewMockLayerDao(ctr)
			mockExperimentDao := mocks.NewMockExperimentDao(ctr)
			mockExperimentVersionDao := mocks.NewMockExperimentVersionDao(ctr)
			s := &Service{
				layerDao:             mockLayerDao,
				experimentDao:        mockExperimentDao,
				experimentVersionDao: mockExperimentVersionDao,
			}
			tt.setupMockCalls(mockLayerDao, mockExperimentDao, mockExperimentVersionDao) //nolint:govet
			got, err := s.GetUserBucketAvailability(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserBucketAvailability() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetUserBucketAvailability() diff = %s", diff)
			}
		})
	}
}

// nolint:govet
func TestService_GetNumberOfUsersInSegmentExpression(t *testing.T) {
	t.Parallel()
	logger.Init("test")

	type args struct {
		ctx     context.Context
		request *pb.GetNumberOfUsersInSegmentExpressionRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(segmentMock *mockSegmentation.MockSegmentationServiceClient)
		want           *pb.GetNumberOfUsersInSegmentExpressionResponse
		wantErr        bool
	}{
		{
			name: "success, happy flow",
			args: args{
				ctx: context.Background(),
				request: &pb.GetNumberOfUsersInSegmentExpressionRequest{
					SegmentExpression: "s1",
				},
			},
			setupMockCalls: func(segmentMock *mockSegmentation.MockSegmentationServiceClient) {
				segmentMock.EXPECT().GetNumberOfUsersInSegment(gomock.Any(), gomock.Any(), gomock.Any()).Return(&segment.GetNumberOfUsersInSegmentResponse{
					Status:        rpc.StatusOk(),
					NumberOfUsers: 100,
				}, nil).AnyTimes()
			},
			want: &pb.GetNumberOfUsersInSegmentExpressionResponse{
				Status:        rpc.StatusOk(),
				NumberOfUsers: 100,
			},
		},
		{
			name: "failure, contains &&",
			args: args{
				ctx: context.Background(),
				request: &pb.GetNumberOfUsersInSegmentExpressionRequest{
					SegmentExpression: "s1 && s2",
				},
			},
			setupMockCalls: func(segmentMock *mockSegmentation.MockSegmentationServiceClient) {
				segmentMock.EXPECT().GetNumberOfUsersInSegment(gomock.Any(), gomock.Any(), gomock.Any()).Return(&segment.GetNumberOfUsersInSegmentResponse{
					Status:        rpc.StatusOk(),
					NumberOfUsers: 100,
				}, nil).AnyTimes()
			},
			want: &pb.GetNumberOfUsersInSegmentExpressionResponse{
				Status: rpc.NewStatus(uint32(code.Code_INVALID_ARGUMENT), "fetching counts for segment expressions is not supported, you can still create an experiment using segment expressions", ""),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockSegment := mockSegmentation.NewMockSegmentationServiceClient(ctr)
			s := &Service{
				segmentClient: mockSegment,
			}
			tt.setupMockCalls(mockSegment)
			got, err := s.GetNumberOfUsersInSegmentExpression(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNumberOfUsersInSegmentExpression() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetNumberOfUsersInSegmentExpression() diff = %s", diff)
			}
		})
	}
}

// nolint:govet
func TestService_GetSegmentExpressionDynamism(t *testing.T) {
	t.Parallel()
	logger.Init("test")

	type args struct {
		ctx     context.Context
		request *pb.GetSegmentExpressionDynamismRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(segmentMock *mockSegmentation.MockSegmentationServiceClient)
		want           *pb.GetSegmentExpressionDynamismResponse
		wantErr        bool
	}{
		{
			name: "success, happy flow",
			args: args{
				ctx: context.Background(),
				request: &pb.GetSegmentExpressionDynamismRequest{
					SegmentExpression: "s1",
				},
			},
			setupMockCalls: func(segmentMock *mockSegmentation.MockSegmentationServiceClient) {
				segmentMock.EXPECT().GetSegmentTypes(gomock.Any(), gomock.Any(), gomock.Any()).Return(&segment.GetSegmentTypesResponse{
					Status: rpc.StatusOk(),
					SegmentTypes: map[string]segment.SegmentType{
						"s1": segment.SegmentType_DYNAMIC,
					},
				}, nil).AnyTimes()
			},
			want: &pb.GetSegmentExpressionDynamismResponse{
				Status:    rpc.StatusOk(),
				IsDynamic: true,
			},
		},
		{
			name: "success, happy flow",
			args: args{
				ctx: context.Background(),
				request: &pb.GetSegmentExpressionDynamismRequest{
					SegmentExpression: "s1 && s2",
				},
			},
			setupMockCalls: func(segmentMock *mockSegmentation.MockSegmentationServiceClient) {
				segmentMock.EXPECT().GetSegmentTypes(gomock.Any(), gomock.Any(), gomock.Any()).Return(&segment.GetSegmentTypesResponse{
					Status: rpc.StatusOk(),
					SegmentTypes: map[string]segment.SegmentType{
						"s1": segment.SegmentType_DYNAMIC,
					},
				}, nil).AnyTimes()
			},
			want: &pb.GetSegmentExpressionDynamismResponse{
				Status: rpc.NewStatus(uint32(code.Code_INTERNAL), "error while fetching segment types", ""),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockSegment := mockSegmentation.NewMockSegmentationServiceClient(ctr)
			s := &Service{
				segmentClient: mockSegment,
			}
			tt.setupMockCalls(mockSegment)
			got, err := s.GetSegmentExpressionDynamism(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSegmentExpressionDynamism() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetSegmentExpressionDynamism() diff = %s", diff)
			}
		})
	}
}

//nolint:govet
func TestService_ValidateSegmentExpression(t *testing.T) {
	t.Parallel()
	logger.Init("test")

	type args struct {
		ctx     context.Context
		request *pb.ValidateSegmentExpressionRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(segmentMock *mockSegmentation.MockSegmentationServiceClient)
		want           *pb.ValidateSegmentExpressionResponse
		wantErr        bool
	}{
		{
			name: "success, happy flow",
			args: args{
				ctx: context.Background(),
				request: &pb.ValidateSegmentExpressionRequest{
					SegmentExpression: "s1",
				},
			},
			setupMockCalls: func(segmentMock *mockSegmentation.MockSegmentationServiceClient) {
				segmentMock.EXPECT().AreSegmentsAvailable(gomock.Any(), gomock.Any(), gomock.Any()).Return(&segment.AreSegmentsAvailableResponse{
					Status: rpc.StatusOk(),
					AvailabilityMap: map[string]bool{
						"s1": true,
					},
				}, nil).AnyTimes()
			},
			want: &pb.ValidateSegmentExpressionResponse{
				Status:  rpc.StatusOk(),
				IsValid: true,
			},
		},
		{
			name: "failure",
			args: args{
				ctx: context.Background(),
				request: &pb.ValidateSegmentExpressionRequest{
					SegmentExpression: "s1",
				},
			},
			setupMockCalls: func(segmentMock *mockSegmentation.MockSegmentationServiceClient) {
				segmentMock.EXPECT().AreSegmentsAvailable(gomock.Any(), gomock.Any(), gomock.Any()).Return(&segment.AreSegmentsAvailableResponse{
					Status: rpc.StatusOk(),
					AvailabilityMap: map[string]bool{
						"s1": false,
					},
				}, nil).AnyTimes()
			},
			want: &pb.ValidateSegmentExpressionResponse{
				Status: rpc.NewStatus(uint32(code.Code_INVALID_ARGUMENT), "segment ids not found: s1", ""),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockSegment := mockSegmentation.NewMockSegmentationServiceClient(ctr)
			s := &Service{
				segmentClient: mockSegment,
			}
			tt.setupMockCalls(mockSegment)
			got, err := s.ValidateSegmentExpression(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateSegmentExpression() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("ValidateSegmentExpression() diff = %s", diff)
			}
		})
	}
}

// nolint:govet
func TestService_ValidateEvents(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	type args struct {
		ctx     context.Context
		request *pb.ValidateEventsRequest
	}
	questGenConf, _ := genconf.Load()

	tests := []struct {
		name           string
		args           args
		setupMockCalls func(mockMetricsDatasource *mocks4.MockDatasource)
		want           *pb.ValidateEventsResponse
		wantErr        bool
	}{
		{
			name: "success, happy flow",
			args: args{
				ctx: context.Background(),
				request: &pb.ValidateEventsRequest{
					Events: []string{"e1:EPIFI_TECH:search"},
				},
			},
			setupMockCalls: func(mockMetricsDatasource *mocks4.MockDatasource) {
				mockMetricsDatasource.EXPECT().PreDefinedQueries(gomock.Any()).Return(&metrics.PreDefinedQueries{
					ImpressionCount: &metrics.Query{
						RawQuery: "SELECT COUNT(*) FROM QUEST_VARIANT_EVENTS WHERE json_extract_scalar(properties, '$.a_bvariant_id') = ?",
						Arguments: []*web.Datatype{
							{
								BaseType: web.PrimitiveType_PRIMITIVE_TYPE_STRING,
							},
						},
						ResultSchema: []*metrics.Field{
							{
								Name: "ImpressionCount",
								Datatype: &web.Datatype{
									BaseType: web.PrimitiveType_PRIMITIVE_TYPE_INT,
								},
							},
						},
					},
					EventCount: &metrics.Query{
						RawQuery: "SELECT COUNT(*) FROM QUEST_VARIANT_EVENTS t1 INNER JOIN %%s AS t2 ON (json_extract_scalar(t1.properties, '$.a_bvariant_id') = ? AND t2.%[1]s = t1.%[1]s AND t2.event = ?)",
						Arguments: []*web.Datatype{
							{
								BaseType: web.PrimitiveType_PRIMITIVE_TYPE_STRING,
							},
							{
								BaseType: web.PrimitiveType_PRIMITIVE_TYPE_STRING,
							},
						},
						ResultSchema: []*metrics.Field{
							{
								Name: "EventCount",
								Datatype: &web.Datatype{
									BaseType: web.PrimitiveType_PRIMITIVE_TYPE_INT,
								},
							},
						},
					},
					EventValidation: &metrics.Query{
						RawQuery: "SELECT EXISTS(SELECT 1 FROM %s WHERE event = ? LIMIT 1)",
						Arguments: []*web.Datatype{
							{
								BaseType: web.PrimitiveType_PRIMITIVE_TYPE_STRING,
							},
						},
						ResultSchema: []*metrics.Field{
							{
								Name: "EventValidation",
								Datatype: &web.Datatype{
									BaseType: web.PrimitiveType_PRIMITIVE_TYPE_BOOL,
								},
							},
						},
					},
				}).AnyTimes()
				mockMetricsDatasource.EXPECT().Fetch(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound).AnyTimes()
			},
			want: &pb.ValidateEventsResponse{
				Status: rpc.StatusOk(),
				ExistenceMap: map[string]bool{
					"e1:EPIFI_TECH:search": false,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockMetricsDatasource := mocks4.NewMockDatasource(ctr)
			s := &Service{
				metricsDatasource: mockMetricsDatasource,
				questGenConf:      questGenConf,
			}
			tt.setupMockCalls(mockMetricsDatasource)
			got, err := s.ValidateEvents(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateEvents() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("ValidateEvents() diff = %s", diff)
			}
		})
	}
}

// nolint:govet
func TestService_GetAllEventDetails(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	eventDetails1 := &questtypes.EventDetails{
		Name:        "HomeScreenLoaded",
		TableName:   "home_events",
		EventSchema: "event",
	}
	eventDetails2 := &questtypes.EventDetails{
		Name:        "LoadedHomeNavigationBar",
		TableName:   "home_events",
		EventSchema: "event",
	}
	type args struct {
		ctx     context.Context
		request *pb.GetAllEventDetailsRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(eventDetailsDao *mocks.MockEventDetailsDao)
		want           *pb.GetAllEventDetailsResponse
		wantErr        bool
	}{
		{
			name: "happy flow",
			args: args{
				ctx: context.Background(),
				request: &pb.GetAllEventDetailsRequest{
					SchemaName: "event",
					TableName:  "Home_Events",
				},
			},
			setupMockCalls: func(eventDetailsDao *mocks.MockEventDetailsDao) {
				eventDetailsDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*questtypes.EventDetails{eventDetails1, eventDetails2}, nil).AnyTimes()
			},
			want: &pb.GetAllEventDetailsResponse{
				Status: rpc.StatusOk(),
				EventDetailsList: []*questtypes.EventDetails{
					eventDetails1, eventDetails2,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockEventDetailsDao := mocks.NewMockEventDetailsDao(ctr)
			s := &Service{
				eventDetailsDao: mockEventDetailsDao,
			}
			tt.setupMockCalls(mockEventDetailsDao)
			got, err := s.GetAllEventDetails(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllEventDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetAllEventDetails() diff = %s", diff)
			}
		})
	}
}

type TxnExecutor struct {
}

func (TxnExecutor) RunTxn(ctx context.Context, fn storagev2.InTransaction) error {
	return fn(ctx)
}
