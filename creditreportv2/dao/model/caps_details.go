package model

import (
	"strconv"
	"time"

	guuid "github.com/google/uuid"

	"github.com/epifi/gamma/api/vendorgateway/credit_report"
	"github.com/epifi/be-common/pkg/nulltypes"
)

type CapsDetails struct {
	// Primary identifier to credit_report_summary table.
	Id string `gorm:"primary_key"`

	// Foreign Key identifier, from credit_reports table
	CreditReportId string
	// Dedup identifier to prevent duplicates
	DedupId string
	// Timestamp of credit report download. Used to determine purged_at.
	CreditReportDownloadedAt time.Time
	// Actor mapped to credit_report
	ActorId nulltypes.NullString

	ReportId             string
	ReportDate           string
	ReportTime           string
	SubscriberCode       string
	SubscriberName       string
	DateOfRequest        string
	ReportTimeCaps       string
	ReportIdCaps         string
	EnquiryReason        string
	FinancePurpose       string
	AmountFinanced       string
	DurationOfAgreement  string
	CapsApplicantDetails *CapsApplicantDetails
}

func GetCapsDetails(c *CreditReport) ([]*CapsDetails, error) {
	var capsApplicationDetails []*CapsDetails
	if c.CreditReportDataRaw == nil {
		return capsApplicationDetails, nil
	}
	var capsDetailsArray []*credit_report.CAPSApplicationDetails = c.CreditReportDataRaw.GetCaps().GetCapsApplicationDetailsArray()
	if c.CreditReportDataRaw.GetCaps().GetCapsApplicationDetailsArray() != nil {
		capsDetailsArray = c.CreditReportDataRaw.GetCaps().GetCapsApplicationDetailsArray()
	} else if c.CreditReportDataRaw.GetCaps().GetCapsApplicationDetails() != nil {
		capsDetailsArray = []*credit_report.CAPSApplicationDetails{c.CreditReportDataRaw.GetCaps().GetCapsApplicationDetails()}
	}
	for idx, application := range capsDetailsArray {
		dedupId := c.CreditReportDataRaw.GetCreditProfileHeader().GetReportNumber() + "_" + strconv.Itoa(idx)
		model := &CapsDetails{
			Id:                       guuid.New().String(),
			CreditReportId:           c.Id,
			DedupId:                  dedupId,
			CreditReportDownloadedAt: c.CreatedAt,
			ActorId:                  c.ActorId,
			ReportId:                 c.CreditReportDataRaw.GetCreditProfileHeader().GetReportNumber(),
			ReportDate:               c.CreditReportDataRaw.GetCreditProfileHeader().GetReportDate(),
			ReportTime:               c.CreditReportDataRaw.GetCreditProfileHeader().GetReportTime(),
			SubscriberCode:           application.GetSubscriberCode(),
			SubscriberName:           application.GetSubscriberName(),
			DateOfRequest:            application.GetDateOfRequest(),
			ReportTimeCaps:           application.GetReportTime(),
			ReportIdCaps:             application.GetReportNumber(),
			EnquiryReason:            application.GetEnquiryReason(),
			FinancePurpose:           application.GetFinancePurpose(),
			AmountFinanced:           application.GetAmountFinanced(),
			DurationOfAgreement:      application.GetDurationOfAgreement(),
			CapsApplicantDetails:     GetCapsApplicantDetails(c, application, dedupId),
		}
		capsApplicationDetails = append(capsApplicationDetails, model)
	}

	return capsApplicationDetails, nil
}
