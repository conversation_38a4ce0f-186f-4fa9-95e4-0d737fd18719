package digilocker

import "encoding/xml"

type Certificate struct {
	XMLName         xml.Name         `xml:"Certificate"`
	CertificateData *CertificateData `xml:"CertificateData"`
}

type CertificateData struct {
	XMLName xml.Name `xml:"CertificateData"`
	KycRes  *KycRes  `xml:"KycRes"`
}

type KycRes struct {
	XMLName xml.Name `xml:"KycRes"`
	UidData *UidData `xml:"UidData"`
	Code    string   `xml:"code,attr"`
	Ts      string   `xml:"ts,attr"`
	Ttl     string   `xml:"ttl,attr"`
	Txn     string   `xml:"txn,attr"`
}
type UidData struct {
	XMLName xml.Name `xml:"UidData"`
	Uid     string   `xml:"uid,attr"`
	Poi     *Poi     `xml:"Poi"`
	Poa     *Poa     `xml:"Poa"`
	LData   *LData   `xml:"LData"`
	Pht     string   `xml:"Pht"`
}

type Poi struct {
	XMLName xml.Name `xml:"Poi"`
	Dob     string   `xml:"dob,attr"`
	Gender  string   `xml:"gender,attr"`
	Name    string   `xml:"name,attr"`
}

type Poa struct {
	XMLName  xml.Name `xml:"Poa"`
	Co       string   `xml:"co,attr"`
	Country  string   `xml:"country,attr"`
	Dist     string   `xml:"dist,attr"`
	House    string   `xml:"house,attr"`
	Landmark string   `xml:"lm,attr"`
	Loc      string   `xml:"loc,attr"`
	PinCode  string   `xml:"pc,attr"`
	State    string   `xml:"state,attr"`
	Street   string   `xml:"street,attr"`
	Vtc      string   `xml:"vtc,attr"`
}

type LData struct {
	XMLName  xml.Name `xml:"LData"`
	Co       string   `xml:"co,attr"`
	Country  string   `xml:"country,attr"`
	Dist     string   `xml:"dist,attr"`
	House    string   `xml:"house,attr"`
	Lang     string   `xml:"lang"`
	Landmark string   `xml:"lm,attr"`
	Loc      string   `xml:"loc,attr"`
	PinCode  string   `xml:"pc,attr"`
	State    string   `xml:"state,attr"`
	Street   string   `xml:"street,attr"`
	Vtc      string   `xml:"vtc,attr"`
}
