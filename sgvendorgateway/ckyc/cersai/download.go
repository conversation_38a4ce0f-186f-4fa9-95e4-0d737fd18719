package cersai

import (
	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"strconv"
	"time"

	"github.com/jinzhu/copier"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/names"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gringott/api/stockguardian/sgvendorgateway/ckyc"
	"github.com/epifi/gringott/sgvendorgateway/config"
)

type GetCkycDownloadRequest struct {
	Method string
	Req    *ckyc.CkycDownloadRequest
	Url    string
	Conf   *config.Config
	Ctx    context.Context
	*SecureExchange
}

func (r *GetCkycDownloadRequest) HTTPMethod() string {
	return r.Method
}

func (r *GetCkycDownloadRequest) URL() string {
	return r.Url
}

func (r *GetCkycDownloadRequest) ContentTypeString() string {
	return vendorapi.ContentTypeXML
}

func (r *GetCkycDownloadRequest) Marshal() ([]byte, error) {
	return xml.Marshal(&DownloadXmlRequest{
		Header: &ReqHeader{
			FiCode:  r.Conf.CKYC.FICode,
			ReqId:   r.Req.GetReqId(),
			Version: apiVersion1Dot2,
		},
		CkycInq: &DownloadCkycInq{
			Pid: &DownloadCkycPid{
				PidData: &DownloadCkycPidData{
					DateTime:       time.Now().In(datetime.IST).Format(dateTimeLayout),
					CkycNo:         r.Req.GetCkycNo(),
					AuthFactorType: "01",
					AuthFactor:     r.Req.GetDob(),
				},
			},
		},
	})
}

func (r *GetCkycDownloadRequest) GetResponse() vendorapi.Response {
	return &GetCkycDownloadResponse{
		Ctx: r.Ctx,
	}
}

type GetCkycDownloadResponse struct {
	Ctx context.Context
}

func convertToBeDownloadResp(ctx context.Context, downloadXmlResponse *DownloadXmlResponse) *ckyc.CkycDownloadResponse {
	// Check if CkycInq is nil to prevent nil pointer dereference
	if downloadXmlResponse.CkycInq == nil {
		logger.Error(ctx, "CkycInq is nil in convertToBeDownloadResp")
		return &ckyc.CkycDownloadResponse{
			Status: rpc.StatusInternal(),
		}
	}

	if downloadXmlResponse.CkycInq.ErrorMessage != "" {
		return &ckyc.CkycDownloadResponse{
			Status:       rpc.StatusOk(),
			ErrorMessage: downloadXmlResponse.CkycInq.ErrorMessage,
		}
	}
	return &ckyc.CkycDownloadResponse{
		Status:          rpc.StatusOk(),
		PersonalDetails: convertToBePersonalDetails(ctx, downloadXmlResponse),
		IdentityDetails: convertToBeIdentityDetails(ctx, downloadXmlResponse),
		ImagesData:      convertToImagesData(ctx, downloadXmlResponse),
	}
}

func convertToImagesData(ctx context.Context, downloadXmlResponse *DownloadXmlResponse) []*ckyc.ImageDetails {
	var resImageDetails []*ckyc.ImageDetails

	// Check for nil pointers to prevent panic
	if downloadXmlResponse.CkycInq == nil || downloadXmlResponse.CkycInq.Pid == nil ||
		downloadXmlResponse.CkycInq.Pid.PidData == nil || downloadXmlResponse.CkycInq.Pid.PidData.ImageDetails == nil ||
		downloadXmlResponse.CkycInq.Pid.PidData.ImageDetails.ImagesList == nil {
		logger.Info(ctx, "convertToImagesData: missing image data structure")
		return resImageDetails
	}

	for _, imageDetail := range downloadXmlResponse.CkycInq.Pid.PidData.ImageDetails.ImagesList {
		// if image data is empty, skip the image and log the error
		if len(imageDetail.ImageData) == 0 {
			logger.Info(ctx, "convertToImagesData: image data empty", zap.String("imageCode", imageDetail.ImageCode))
			continue
		}
		resImageDetails = append(resImageDetails, &ckyc.ImageDetails{
			DocumentType: getDocumentImageType(imageDetail.ImageCode),
			ImageType:    commontypes.StringToImageType(imageDetail.ImageType),
			ImageData:    imageDetail.ImageData,
		})
	}
	return resImageDetails
}

func getDocumentImageType(imageCode string) ckyc.IdentityType {
	switch imageCode {
	case "03":
		return ckyc.IdentityType_IDENTITY_TYPE_PAN
	case "04":
		return ckyc.IdentityType_IDENTITY_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR
	case "05":
		return ckyc.IdentityType_IDENTITY_TYPE_PASSPORT
	case "06":
		return ckyc.IdentityType_IDENTITY_TYPE_DRIVING_LICENSE
	case "07":
		return ckyc.IdentityType_IDENTITY_TYPE_VOTER_ID
	case "08":
		return ckyc.IdentityType_IDENTITY_TYPE_NREGA
	case "10":
		return ckyc.IdentityType_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT
	case "11":
		return ckyc.IdentityType_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED
	case "35":
		return ckyc.IdentityType_IDENTITY_TYPE_NATIONAL_POPULATION_REGISTRY_LETTER
	case "36":
		return ckyc.IdentityType_IDENTITY_TYPE_EKYC_AUTHENTICATION
	case "37":
		return ckyc.IdentityType_IDENTITY_TYPE_OFFLINE_AADHAAR_VERIFICATION
	case "98":
		return ckyc.IdentityType_IDENTITY_TYPE_OFFLINE_OTHERS
		// Figure out how to handle unexpected
	default:
		return 0
	}
}

func getBeIdentityType(identityType string) (ckyc.IdentityType, error) {
	switch identityType {
	case "A":
		return ckyc.IdentityType_IDENTITY_TYPE_PASSPORT, nil
	case "B":
		return ckyc.IdentityType_IDENTITY_TYPE_VOTER_ID, nil
	case "C":
		return ckyc.IdentityType_IDENTITY_TYPE_PAN, nil
	case "D":
		return ckyc.IdentityType_IDENTITY_TYPE_DRIVING_LICENSE, nil
	case "E":
		return ckyc.IdentityType_IDENTITY_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR, nil
	case "F":
		return ckyc.IdentityType_IDENTITY_TYPE_NREGA, nil
	case "G":
		return ckyc.IdentityType_IDENTITY_TYPE_NATIONAL_POPULATION_REGISTRY_LETTER, nil
	case "H":
		return ckyc.IdentityType_IDENTITY_TYPE_EKYC_AUTHENTICATION, nil
	case "I":
		return ckyc.IdentityType_IDENTITY_TYPE_OFFLINE_AADHAAR_VERIFICATION, nil
	case "Z":
		return ckyc.IdentityType_IDENTITY_TYPE_OFFLINE_OTHERS, nil
	case "S01":
		return ckyc.IdentityType_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT, nil
	case "S02":
		return ckyc.IdentityType_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED, nil
	default:
		return ckyc.IdentityType_IDENTITY_TYPE_TYPE_UNSPECIFIED, fmt.Errorf("unable to map identity_type %v", identityType)
	}
}

func convertToBeName(ctx context.Context, firstName, middleName, lastName string) *commontypes.Name {
	if firstName == "" {
		logger.Info(ctx, "convertToBeName: firstName empty")
	}
	if lastName == "" {
		logger.Info(ctx, "convertToBeName: lastName empty")
	}
	isLastNameValid := true
	if lastName == "" ||
		lastName == "NULL" {
		isLastNameValid = false
	}
	if firstName != "" && isLastNameValid {
		return &commontypes.Name{
			FirstName:  firstName,
			MiddleName: middleName,
			LastName:   lastName,
		}
	}
	return names.ParseString(firstName)
}

func convertToBePermanentAddress(details *DownloadCkycPersonalDetailsRes) *commontypes.PostalAddress {
	res := &commontypes.PostalAddress{
		Locality:           details.PermCity,
		Sublocality:        details.PermDist,
		RegionCode:         details.PermCountry,
		AdministrativeArea: convertToStateName(details.PermState),
		PostalCode:         details.PermPin,
	}
	addressLines := []string{details.PermLine1, details.PermLine2, details.PermLine3}
	for _, address := range addressLines {
		if address != "" {
			res.AddressLines = append(res.AddressLines, address)
		}
	}
	return res
}
func convertToBeCurrentAddress(details *DownloadCkycPersonalDetailsRes) *commontypes.PostalAddress {
	res := &commontypes.PostalAddress{
		Locality:           details.CorresCity,
		Sublocality:        details.CorresDist,
		RegionCode:         details.CorresCountry,
		AdministrativeArea: convertToStateName(details.CorresState),
		PostalCode:         details.CorresPin,
	}
	addressLines := []string{details.CorresLine1, details.CorresLine2, details.CorresLine3}
	for _, address := range addressLines {
		if address != "" {
			res.AddressLines = append(res.AddressLines, address)
		}
	}
	return res
}

func convertToStateName(stateCode string) string {
	switch stateCode {
	case "AN":
		return "Andaman And Nicobar Islands"
	case "AP":
		return "Andhra Pradesh"
	case "AR":
		return "Arunachal Pradesh"
	case "AS":
		return "Assam"
	case "BR":
		return "Bihar"
	case "CH":
		return "Chandigarh"
	case "CG":
		return "Chhattisgarh"
	case "DD":
		return "The Dadra And Nagar Haveli And Daman And Diu"
	case "DL":
		return "Delhi"
	case "GA":
		return "Goa"
	case "GJ":
		return "Gujarat"
	case "HR":
		return "Haryana"
	case "HP":
		return "Himachal Pradesh"
	case "JK":
		return "Jammu And Kashmir"
	case "JH":
		return "Jharkhand"
	case "KA":
		return "Karnataka"
	case "KL":
		return "Kerala"
	case "LA":
		return "Ladakh"
	case "LD":
		return "Lakshadweep"
	case "MP":
		return "Madhya Pradesh"
	case "MH":
		return "Maharashtra"
	case "MN":
		return "Manipur"
	case "ML":
		return "Meghalaya"
	case "MZ":
		return "Mizoram"
	case "NL":
		return "Nagaland"
	case "OR":
		return "Odisha"
	case "OD":
		return "Odisha"
	case "PY":
		return "Puducherry"
	case "PB":
		return "Punjab"
	case "RJ":
		return "Rajasthan"
	case "SK":
		return "Sikkim"
	case "TN":
		return "Tamil Nadu"
	case "TS":
		return "Telangana"
	case "TG":
		return "Telangana"
	case "TR":
		return "Tripura"
	case "UP":
		return "Uttar Pradesh"
	case "UA":
		return "Uttarakhand"
	case "WB":
		return "West Bengal"
	case "XX":
		return "Other"
	case "AO":
		return "Army Post Office"
	default:
		return stateCode
	}
}

func getMobileNumber(pd *DownloadCkycPersonalDetailsRes) *commontypes.PhoneNumber {
	mobNumInt, err := strconv.ParseUint(pd.MobNum, 10, 64)
	if err != nil {
		return nil
	}
	mobCodeInt, err := strconv.ParseUint(pd.MobCode, 10, 32)
	if err != nil {
		return nil
	}
	return &commontypes.PhoneNumber{CountryCode: uint32(mobCodeInt), NationalNumber: mobNumInt}
}

func convertToBeImageDetailsList(ctx context.Context, imageDetailsList []*DownloadCkycImage) []*ckyc.ImageDetails {
	var resImageDetailsList []*ckyc.ImageDetails
	for _, imageDetail := range imageDetailsList {
		if len(imageDetail.ImageData) == 0 {
			logger.Info(ctx, "convertToBeImageDetailsList: image data empty", zap.String("imageCode", imageDetail.ImageCode))
			continue
		}
		resImageDetailsList = append(resImageDetailsList, &ckyc.ImageDetails{
			DocumentType: getDocumentImageType(imageDetail.ImageCode),
			ImageType:    commontypes.StringToImageType(imageDetail.ImageType),
			ImageData:    imageDetail.ImageData,
		})
	}
	return resImageDetailsList
}

func convertToBeGender(ctx context.Context, gender string) commontypes.Gender {
	switch gender {
	case "M":
		return commontypes.Gender_MALE
	case "F":
		return commontypes.Gender_FEMALE
	case "T":
		return commontypes.Gender_TRANSGENDER
	default:
		logger.Info(ctx, "convertToBeGender: gender is unrecognized", zap.String("genderCode", gender))
		return commontypes.Gender_GENDER_UNSPECIFIED
	}
}

func convertToBeAccountType(ctx context.Context, accType string) ckyc.AccountType {
	switch accType {
	case "01":
		return ckyc.AccountType_ACCOUNT_TYPE_NORMAL
	case "02":
		return ckyc.AccountType_ACCOUNT_TYPE_SMALL
	case "03":
		return ckyc.AccountType_ACCOUNT_TYPE_SIMPLIFIED
	case "04":
		return ckyc.AccountType_ACCOUNT_TYPE_OTP_EKYC
	default:
		logger.Info(ctx, "account type is unrecognized", zap.String(logger.ACCOUNT_TYPE, accType))
		return ckyc.AccountType_ACCOUNT_TYPE_TYPE_UNSPECIFIED
	}
}

func convertToBePersonalDetails(ctx context.Context, downloadXmlResponse *DownloadXmlResponse) *ckyc.PersonalDetails {
	// Check for nil pointers to prevent panic
	if downloadXmlResponse.CkycInq == nil || downloadXmlResponse.CkycInq.Pid == nil || downloadXmlResponse.CkycInq.Pid.PidData == nil {
		logger.Info(ctx, "missing required data structure while converting to BE response")
		return &ckyc.PersonalDetails{}
	}

	if downloadXmlResponse.CkycInq.Pid.PidData.PersonalDetails == nil {
		logger.Info(ctx, "personal details nil while converting to BE response")
		return &ckyc.PersonalDetails{}
	}
	if downloadXmlResponse.CkycInq.Pid.PidData.ImageDetails == nil {
		logger.Info(ctx, "image details nil while converting to BE response")
		return &ckyc.PersonalDetails{}
	}
	personalDetails := downloadXmlResponse.CkycInq.Pid.PidData.PersonalDetails
	imageDetailsList := downloadXmlResponse.CkycInq.Pid.PidData.ImageDetails.ImagesList
	permAddress := convertToBePermanentAddress(personalDetails)
	corresAddress := convertToBeCurrentAddress(personalDetails)
	logger.Info(ctx, "perm corres same flag", zap.String(logger.RESULT, personalDetails.PermCorresSameFlag))
	// As per documentation correspondence address can still be available if PermCorresSameFlag is 'Y', so populating it with permanent address if correspondece address region code is empty
	if personalDetails.PermCorresSameFlag == "Y" && corresAddress.GetRegionCode() == "" {
		logger.Info(ctx, "empty region code using perm address as correspondence address")
		corresAddress = permAddress
	}
	res := &ckyc.PersonalDetails{
		AccountType: convertToBeAccountType(ctx, personalDetails.AccType),
		Name:        convertToBeName(ctx, personalDetails.Fname, personalDetails.Mname, personalDetails.Lname),
		FathersName: convertToBeName(ctx, personalDetails.FatherFname, personalDetails.FatherMname, personalDetails.FatherLname),
		MothersName: convertToBeName(ctx, personalDetails.MotherFname, personalDetails.MotherMname, personalDetails.MotherLname),
		Gender:      convertToBeGender(ctx, personalDetails.Gender),
		Dob:         getDob(ctx, personalDetails.Dob),
		// Nationality:      commontypes.Nationality_NATIONALITY_INDIAN,
		PermanentAddress: permAddress,
		CurrentAddress:   corresAddress,
		TelOffice:        &commontypes.Landline{Number: personalDetails.OffTelNum, StdCode: personalDetails.OffStdCode},
		TelResidential:   &commontypes.Landline{Number: personalDetails.ResTelNum, StdCode: personalDetails.ResStdCode},
		MobileNumber:     getMobileNumber(personalDetails),
		EmailId:          personalDetails.Email,
		ImageDetailsList: convertToBeImageDetailsList(ctx, imageDetailsList),
		Pan:              personalDetails.Pan,
		// ProofOfAddress:   convertAddressProofToBeDocCode(personalDetails.PermPoa),
		Remarks:    personalDetails.Remarks,
		MaidenName: convertToBeName(ctx, personalDetails.MaidenFname, personalDetails.MaidenMname, personalDetails.MaidenLname),
		CkycNo:     personalDetails.CkycNo,
	}
	return res
}

func getDob(ctx context.Context, dob string) *date.Date {
	if dob == "" {
		logger.Info(ctx, "dob is empty")
	}
	return datetime.DateFromString(dob)
}

func convertToBeIdentityDetails(ctx context.Context, downloadXmlResponse *DownloadXmlResponse) []*ckyc.IdentityDetail {
	var beIdentityDetails []*ckyc.IdentityDetail

	// Check for nil pointers to prevent panic
	if downloadXmlResponse.CkycInq == nil || downloadXmlResponse.CkycInq.Pid == nil || downloadXmlResponse.CkycInq.Pid.PidData == nil {
		logger.Info(ctx, "missing required data structure while converting to BE identity details")
		return beIdentityDetails
	}

	identityDetails := downloadXmlResponse.CkycInq.Pid.PidData.IdentityDetails
	if identityDetails == nil {
		logger.Info(ctx, "identity details nil while converting to BE response")
		return beIdentityDetails
	}
	for _, identity := range identityDetails.IdentityList {
		identityType, identityTypeErr := getBeIdentityType(identity.IdentityType)
		if identityTypeErr != nil {
			logger.Error(ctx, "error while mapping identity_type", zap.Error(identityTypeErr))
		}
		beIdentityDetails = append(beIdentityDetails, &ckyc.IdentityDetail{
			IdentityType:       identityType,
			IdentityNumber:     identity.IdentityNum,
			VerificationStatus: ckyc.VerificationStatus(identity.IdverStatus),
		})
	}
	return beIdentityDetails
}

func (r *GetCkycDownloadResponse) Unmarshal(b []byte) (proto.Message, error) {
	downloadXmlResponse := &DownloadXmlResponse{}
	err := xml.Unmarshal(b, downloadXmlResponse)
	if err != nil {
		return nil, err
	}

	// Check if CkycInq is nil first to prevent nil pointer dereference
	if downloadXmlResponse.CkycInq == nil {
		logger.Error(r.Ctx, "CkycInq is nil in download response")
		return &ckyc.CkycDownloadResponse{Status: rpc.StatusInternal(), VendorStatus: &commonvgpb.VendorStatus{Description: string(b)}}, nil
	}

	if downloadXmlResponse.CkycInq.ErrorMessage == "" && (downloadXmlResponse.CkycInq.Pid == nil || downloadXmlResponse.CkycInq.Pid.PidData == nil) {
		logger.Error(r.Ctx, "empty DownloadXmlResponse")
		return &ckyc.CkycDownloadResponse{Status: rpc.StatusInternal(), VendorStatus: &commonvgpb.VendorStatus{Description: string(b)}}, nil
	}
	response := convertToBeDownloadResp(r.Ctx, downloadXmlResponse)
	response.VendorStatus = &commonvgpb.VendorStatus{Description: string(b)}
	response.RawResponse = convertResponseStructToRedactedString(r.Ctx, downloadXmlResponse)
	return response, nil
}

// RedactRequestBody overrides the redaction method. Redacted logging is not required for this API hence returning
// empty response to avoid logging redacted request
func (r *GetCkycDownloadRequest) RedactRequestBody(_ context.Context, _ []byte, _ string) ([]byte, error) {
	return nil, nil
}

func (r *GetCkycDownloadResponse) RedactResponseBody(_ context.Context, _ []byte, _ string) ([]byte, error) {
	return nil, nil
}

// convertResponseStructToRedactedString converts the response struct to string by redacting the image data from the response as we need to store it in DB
// We are copying the struct since we don't want to modify the original response
func convertResponseStructToRedactedString(ctx context.Context, response *DownloadXmlResponse) string {
	copyStruct := &DownloadXmlResponse{}
	err := copier.Copy(copyStruct, response)
	if err != nil {
		logger.Error(ctx, "error while copying response struct", zap.Error(err))
		return ""
	}
	if copyStruct.CkycInq == nil ||
		copyStruct.CkycInq.Pid == nil ||
		copyStruct.CkycInq.Pid.PidData == nil ||
		copyStruct.CkycInq.Pid.PidData.ImageDetails == nil ||
		copyStruct.CkycInq.Pid.PidData.ImageDetails.ImagesList == nil {
		logger.Info(ctx, "ImageDetails or ImagesList nil while converting to redacted string")
		jsonBytes, errMarshal := json.Marshal(copyStruct)
		if errMarshal != nil {
			logger.Error(ctx, "error while marshalling response struct", zap.Error(errMarshal))
			return ""
		}
		return string(jsonBytes)
	}
	for idx, imageDets := range copyStruct.CkycInq.Pid.PidData.ImageDetails.ImagesList {
		// this masks the image data to a static value if non-empty
		// this will help us in checking if the image data is present in the response or not
		if imageDets.ImageData != "" {
			imageDets.ImageData = mask.GetMaskedString(mask.MaskToStaticValue, imageDets.ImageData)
		}
		copyStruct.CkycInq.Pid.PidData.ImageDetails.ImagesList[idx] = imageDets
	}
	jsonBytes, errMarshal := json.Marshal(copyStruct)
	if errMarshal != nil {
		logger.Error(ctx, "error while marshalling response struct", zap.Error(errMarshal))
		return ""
	}
	return string(jsonBytes)
}
