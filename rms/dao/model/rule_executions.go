package model

import (
	"time"

	pb "github.com/epifi/gamma/api/rms/command_processor"
	"github.com/epifi/be-common/pkg/pagination"
)

const (
	StateColumnName       = "state"
	DescriptionColumnName = "description"
)

// RuleExecutions will store execution states of rule subscription
// can be used for debugging purposes or can be consumed by client to present historical stats to user
// eg: XXXX rule was executed 10 times
type RuleExecutions struct {
	Id                    string `gorm:"type:uuid;default:gen_random_uuid();primary_key"`
	RuleSubscriptionId    string `gorm:"not null"`
	SubscriptionVersionId string `gorm:"not null"`
	ClientEventId         string
	// state defines last state of subscription under execution or post execution
	State pb.RuleExecutionState `gorm:"not null"`
	// description is nullable column. It will be used for debugging purposes
	// It may provide detailed view of the last state
	// this would be helpful mainly in cases of FAILURE, error string can be dumped
	Description string
	// FITTT can send events in batches to RMS (eg: Bowling event contains event for all the bowlers)
	// now R<PERSON> creates atomic events using event data fittt
	// atomic event has a key, on which uniqueness of the event can be identifies (eg: player_name, team_id, order_id etc)
	// EventUniqueKey will hold unique key for atomic event
	// will be useful for ensuring idempotency when combined with subscription_id and event_id
	EventUniqueKey string
	CreatedAt      time.Time `gorm:"not null"`
	UpdatedAt      time.Time `gorm:"not null"`
	// represent all the rule executions for a given batch executions
	BatchId string
}

type ExecutionMetrics struct {
	SubId           string
	TotalExecutions int64
	LastExecutedAt  time.Time
}

type RuleExecutionsModelRows []*RuleExecutions

func (rs RuleExecutionsModelRows) Slice(start, end int) pagination.Rows { return rs[start:end] }
func (rs RuleExecutionsModelRows) GetTimestamp(index int) time.Time     { return rs[index].CreatedAt }
func (rs RuleExecutionsModelRows) Size() int                            { return len(rs) }
