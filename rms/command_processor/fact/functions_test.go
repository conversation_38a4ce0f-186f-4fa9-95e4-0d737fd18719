package fact_test

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/rms/ui"
	"github.com/epifi/gamma/rms/command_processor/fact"
)

func TestFunctions_IsCurrentDate(t *testing.T) {
	val, err := fact.IsCurrentDate(timestamppb.Now())
	assert.Nil(t, err)
	assert.True(t, val.(bool))

	val, err = fact.IsCurrentDate(timestamppb.New(time.Now().AddDate(0, 0, 1)))
	assert.Nil(t, err)
	assert.False(t, val.(bool))

	val, err = fact.IsCurrentDate(timestamppb.New(time.Now().AddDate(0, 1, 0)))
	assert.Nil(t, err)
	assert.False(t, val.(bool))

	val, err = fact.IsCurrentDate(timestamppb.New(time.Now().AddDate(1, 0, 0)))
	assert.Nil(t, err)
	assert.False(t, val.(bool))

	val, err = fact.IsCurrentDate(timestamppb.New(time.Now().AddDate(0, 0, 7)))
	assert.Nil(t, err)
	assert.False(t, val.(bool))
}

func TestFunctions_ShouldExecuteBasedOnFrequency(t *testing.T) {
	startDate := timestamppb.Now()
	endDate := timestamppb.New(time.Now().AddDate(3, 0, 0))
	// present day's subscription
	executeForAllFrequency(t, startDate, endDate, true)
	// execution should have happened a day before
	startDate = timestamppb.New(time.Now().AddDate(0, 0, -1))
	executeForAllFrequency(t, startDate, endDate, false)
	// execution should have happened three days before
	startDate = timestamppb.New(time.Now().AddDate(0, 0, -3))
	executeForAllFrequency(t, startDate, endDate, false)

	// execution happened a week before
	startDate = timestamppb.New(time.Now().AddDate(0, 0, -7))
	val, err := fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_WEEKLY)
	assert.Nil(t, err)
	assert.Equal(t, true, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_FORTNIGHTLY)
	assert.Nil(t, err)
	assert.Equal(t, false, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_MONTHLY)
	assert.Nil(t, err)
	assert.Equal(t, false, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_QUARTERLY)
	assert.Nil(t, err)
	assert.Equal(t, false, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_HALF_YEARLY)
	assert.Nil(t, err)
	assert.Equal(t, false, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_YEARLY)
	assert.Nil(t, err)
	assert.Equal(t, false, val.(bool))

	startDate = timestamppb.New(time.Now().AddDate(0, -3, 0))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_MONTHLY)
	assert.Nil(t, err)
	assert.Equal(t, true, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_QUARTERLY)
	assert.Nil(t, err)
	assert.Equal(t, true, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_HALF_YEARLY)
	assert.Nil(t, err)
	assert.Equal(t, false, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_YEARLY)
	assert.Nil(t, err)
	assert.Equal(t, false, val.(bool))

	startDate = timestamppb.New(time.Now().AddDate(0, -12, 0))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_MONTHLY)
	assert.Nil(t, err)
	assert.Equal(t, true, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_QUARTERLY)
	assert.Nil(t, err)
	assert.Equal(t, true, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_HALF_YEARLY)
	assert.Nil(t, err)
	assert.Equal(t, true, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_YEARLY)
	assert.Nil(t, err)
	assert.Equal(t, true, val.(bool))

	startDate = timestamppb.New(time.Now().AddDate(0, -12, 1))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_MONTHLY)
	assert.Nil(t, err)
	assert.Equal(t, false, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_QUARTERLY)
	assert.Nil(t, err)
	assert.Equal(t, false, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_HALF_YEARLY)
	assert.Nil(t, err)
	assert.Equal(t, false, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_YEARLY)
	assert.Nil(t, err)
	assert.Equal(t, false, val.(bool))
}

func executeForAllFrequency(t *testing.T, startDate, endDate *timestamppb.Timestamp, result bool) {
	val, err := fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_WEEKLY)
	assert.Nil(t, err)
	assert.Equal(t, result, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_FORTNIGHTLY)
	assert.Nil(t, err)
	assert.Equal(t, result, val.(bool))
	// first day of execution
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_MONTHLY)
	assert.Nil(t, err)
	assert.Equal(t, result, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_QUARTERLY)
	assert.Nil(t, err)
	assert.Equal(t, result, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_HALF_YEARLY)
	assert.Nil(t, err)
	assert.Equal(t, result, val.(bool))
	val, err = fact.ShouldExecuteBasedOnFrequency(startDate, endDate, ui.Frequency_FREQUENCY_YEARLY)
	assert.Nil(t, err)
	assert.Equal(t, result, val.(bool))
}
