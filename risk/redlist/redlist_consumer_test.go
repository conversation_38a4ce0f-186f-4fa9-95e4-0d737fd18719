package redlist

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"testing"

	"github.com/golang/mock/gomock"

	mocks2 "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/api/risk"
	mocks5 "github.com/epifi/gamma/api/risk/mocks"
	mocks4 "github.com/epifi/gamma/api/vendorgateway/transactionmonitoring/dronapay/mocks"
	mocks "github.com/epifi/gamma/risk/dao/mocks"
	mocks3 "github.com/epifi/gamma/risk/test/mocks"
	"github.com/epifi/gamma/risk/txn_monitoring"
)

type mockStruct struct {
	dao                    *mocks.MockRedListDao
	s3Client               *mocks2.MockS3Client
	hashedContactMock      *mocks3.MockProcessor
	latLongMock            *mocks3.MockProcessor
	pinCodeMock            *mocks3.MockProcessor
	atmMock                *mocks3.MockProcessor
	onboardingMock         *mocks3.MockProcessor
	riskEvaluatorEntityDao *mocks.MockRiskEvaluatorEntityDao
	vgTxnMonitoringClient  *mocks4.MockTransactionRiskDronaPayClient
}

const csvFixtureContacts = "hashed_number,fraud_associates,score,updated_at\n" +
	"4a714616d7f333ea4f9847ba5d544365,5,75.0,2022-08-04T06:49:24.395Z\n" +
	"27cef2f81fa39bece60b26134181255f,3,75.0,2022-08-04T06:49:24.395Z\n" +
	"9c96f16219f039ecd310cda38fb486e1,3,75.0,2022-08-04T06:49:24.395Z\n" +
	"40f78d58b07413ce29eb5d0f90655e6b,61,75.0,2022-08-04T06:49:24.395Z\n" +
	"849dec21042cae361ac7252d7cd6a8b5,4,75.0,2022-08-04T06:49:24.395Z\n" +
	"dbace6fcc442d527e7c7688f32dda58e,3,75.0,2022-08-04T06:49:24.395Z\n" +
	"3d7dac2fb1f62637fce185baea71921c,3,75.0,2022-08-04T06:49:24.395Z\n" +
	"1b64877a8d5b7b50db03954f34b5de6,5,75.0,2022-08-04T06:49:24.395Z\n" +
	"0cc4c6175f126ae3c18dc851e01a1f8,4,75.0,2022-08-04T06:49:24.395Z\n" +
	"0ba0a3a73d55acffe936eba56868a6f,11,75.0,2022-08-04T06:49:24.395Z"

const csvFixtureOnboardingStates = "onboarding_state,score\n" + "Kerala,65\n" + "Assam,45\n" + "Delhi,85\n"
const csvFixturesAtm = "atm_pi,score,updated_at\n" +
	"1000,35,2022-08-04T06:49:24.395Z\n" +
	"1001,45,2022-08-04T06:49:24.395Z\n" +
	"1002,63,2022-08-04T06:49:24.395Z\n" +
	"1003,68,2022-08-04T06:49:24.395Z\n" +
	"1004,78,2022-08-04T06:49:24.395Z\n"

const csvFixturesIPAddress = "ip_address_type,ip_address,score,reason\n" +
	"IP_ADDRESS_TYPE_IPV4,*******,50,ip address is risky\n" +
	"IP_ADDRESS_TYPE_IPV6,*******,100,ip address is very risky\n"

const csvFixturesIPAddress1 = "ip_address_type,ip_address,score,reason\n" +
	"IP_ADDRESS_TYPE_IPV4,*******,50,ip address is risky\n" +
	"ABCD,*******,100,ip address is very risky\n"

var (
	ipAddressResLister = []*risk.RedLister{
		{
			Key: &risk.RedListPair{
				Category: risk.RedListCategory_RED_LIST_CATEGORY_IP_ADDRESS,
				Value: &risk.RedListPair_IpAddressDetail{
					IpAddressDetail: &risk.IPAddressDetail{
						IpAddressType: risk.IPAddressDetail_IP_ADDRESS_TYPE_IPV4,
						IpAddress:     "*******",
					},
				},
			},
			RiskScore: 50,
			Reason: &risk.RedListerReason{
				RawReason: "ip address is risky",
				Source:    risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Key: &risk.RedListPair{
				Category: risk.RedListCategory_RED_LIST_CATEGORY_IP_ADDRESS,
				Value: &risk.RedListPair_IpAddressDetail{
					IpAddressDetail: &risk.IPAddressDetail{
						IpAddressType: risk.IPAddressDetail_IP_ADDRESS_TYPE_IPV6,
						IpAddress:     "*******",
					},
				},
			},
			RiskScore: 100,
			Reason: &risk.RedListerReason{
				RawReason: "ip address is very risky",
				Source:    risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
	}
	ipAddressResLister1 = []*risk.RedLister{
		{
			Key: &risk.RedListPair{
				Category: risk.RedListCategory_RED_LIST_CATEGORY_IP_ADDRESS,
				Value: &risk.RedListPair_IpAddressDetail{
					IpAddressDetail: &risk.IPAddressDetail{
						IpAddressType: risk.IPAddressDetail_IP_ADDRESS_TYPE_IPV4,
						IpAddress:     "*******",
					},
				},
			},
			RiskScore: 50,
			Reason: &risk.RedListerReason{
				RawReason: "ip address is risky",
				Source:    risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
	}
)

var (
	riskEvaluatorEntityFieldMask = []risk.RiskEvaluatorEntityFieldMask{
		risk.RiskEvaluatorEntityFieldMask_RISK_EVALUATOR_ENTITY_FIELD_MASK_ID,
		risk.RiskEvaluatorEntityFieldMask_RISK_EVALUATOR_ENTITY_FIELD_MASK_ENTITY_ID,
		risk.RiskEvaluatorEntityFieldMask_RISK_EVALUATOR_ENTITY_FIELD_MASK_ENTITY_TYPE,
	}
	onboardingStateRedListPair = []*risk.RedListPair{
		{
			Category: risk.RedListCategory_REDLIST_CATEGORY_ONBOARDING_STATE,
			Value: &risk.RedListPair_OnboardingState{
				OnboardingState: "Kerala",
			},
		},
		{
			Category: risk.RedListCategory_REDLIST_CATEGORY_ONBOARDING_STATE,
			Value: &risk.RedListPair_OnboardingState{
				OnboardingState: "Assam",
			},
		},
		{
			Category: risk.RedListCategory_REDLIST_CATEGORY_ONBOARDING_STATE,
			Value: &risk.RedListPair_OnboardingState{
				OnboardingState: "Delhi",
			},
		},
	}
	atmRedListPair = []*risk.RedListPair{
		{
			Category: risk.RedListCategory_REDLIST_CATEGORY_ATM,
			Value: &risk.RedListPair_Atm{
				Atm: &risk.AtmRedList{
					AtmPi: "1000",
				},
			},
		},
		{
			Category: risk.RedListCategory_REDLIST_CATEGORY_ATM,
			Value: &risk.RedListPair_Atm{
				Atm: &risk.AtmRedList{
					AtmPi: "1001",
				},
			},
		},
		{
			Category: risk.RedListCategory_REDLIST_CATEGORY_ATM,
			Value: &risk.RedListPair_Atm{
				Atm: &risk.AtmRedList{
					AtmPi: "1002",
				},
			},
		},
		{
			Category: risk.RedListCategory_REDLIST_CATEGORY_ATM,
			Value: &risk.RedListPair_Atm{
				Atm: &risk.AtmRedList{
					AtmPi: "1003",
				},
			},
		},
		{
			Category: risk.RedListCategory_REDLIST_CATEGORY_ATM,
			Value: &risk.RedListPair_Atm{
				Atm: &risk.AtmRedList{
					AtmPi: "1004",
				},
			},
		},
	}
	onboardingStateRedLister = []*risk.RedLister{
		{
			Key: onboardingStateRedListPair[0],
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
			RiskScore: 65,
		},
		{
			Key: onboardingStateRedListPair[1],
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
			RiskScore: 45,
		},
		{
			Key: onboardingStateRedListPair[2],
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
			RiskScore: 85,
		},
	}
	atmRedLister = []*risk.RedLister{
		{
			Key:       atmRedListPair[0],
			RiskScore: 35,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Key:       atmRedListPair[1],
			RiskScore: 45,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Key:       atmRedListPair[2],
			RiskScore: 63,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Key:       atmRedListPair[3],
			RiskScore: 68,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Key:       atmRedListPair[4],
			RiskScore: 78,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
	}
	atmRedListerInDB = []*risk.RedLister{
		{
			Id:        "Id1",
			Key:       atmRedListPair[0],
			RiskScore: 32,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Id:        "Id2",
			Key:       atmRedListPair[1],
			RiskScore: 86,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Id:        "Id3",
			Key:       atmRedListPair[2],
			RiskScore: 23,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Id:        "Id4",
			Key:       atmRedListPair[3],
			RiskScore: 56,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Id:        "Id5",
			Key:       atmRedListPair[4],
			RiskScore: 67,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
	}
	atmRedListerInDB2 = []*risk.RedLister{
		{
			Id:        "Id1",
			Key:       atmRedListPair[0],
			RiskScore: 35,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Id:        "Id2",
			Key:       atmRedListPair[1],
			RiskScore: 86,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Id:        "Id3",
			Key:       atmRedListPair[2],
			RiskScore: 65,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Id:        "Id4",
			Key:       atmRedListPair[3],
			RiskScore: 56,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Id:        "Id5",
			Key:       atmRedListPair[4],
			RiskScore: 78,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
	}
	atmRedListerNewInDB = []*risk.RedLister{
		{
			Id:        "Id1",
			Key:       atmRedListPair[0],
			RiskScore: 35,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Id:        "Id2",
			Key:       atmRedListPair[1],
			RiskScore: 45,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Id:        "Id3",
			Key:       atmRedListPair[2],
			RiskScore: 63,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Id:        "Id4",
			Key:       atmRedListPair[3],
			RiskScore: 68,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
		{
			Id:        "Id5",
			Key:       atmRedListPair[4],
			RiskScore: 78,
			Reason: &risk.RedListerReason{
				Source: risk.RedListerSource_RISK_ANALYST_INTERNAL,
			},
		},
	}
)

func testProcessor(t *testing.T, typename risk.RedListCategory) error {
	ctrl := gomock.NewController(t)
	mockDao := mocks.NewMockRedListDao(ctrl)
	mockEntityDao := mocks.NewMockRiskEvaluatorEntityDao(ctrl)
	mockTxnMonitoring := mocks4.NewMockTransactionRiskDronaPayClient(ctrl)
	mockRiskClient := mocks5.NewMockTxnRiskScoreServiceClient(ctrl)
	pusher := txn_monitoring.NewDataPusher(mockEntityDao, mockTxnMonitoring, mockRiskClient)
	var proc Processor
	switch typename {
	case risk.RedListCategory_REDLIST_CATEGORY_LOCATION_PINCODE, risk.RedListCategory_REDLIST_CATEGORY_KYC_PINCODE:
		proc = NewPinCodeProc(mockDao, pusher, riskDependencies.GenConf)
	case risk.RedListCategory_REDLIST_CATEGORY_LOCATION_LATLONG:
		proc = NewLatLongProc(mockDao, pusher, riskDependencies.GenConf)
	case risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT:
		proc = NewHashedContactProc(mockDao)
	case risk.RedListCategory_REDLIST_CATEGORY_ATM:
		proc = NewAtmProc(mockDao, pusher, riskDependencies.GenConf)
	case risk.RedListCategory_REDLIST_CATEGORY_ONBOARDING_STATE:
		proc = NewOnboardingStateProc(mockDao, pusher, riskDependencies.GenConf)
	default:
		return fmt.Errorf("invalid typename")
	}
	if proc == nil {
		return fmt.Errorf("invalid processor for %s", typename)
	}
	return nil
}

func TestRedListConsumer_testProcessor(t *testing.T) {
	tests := []struct {
		name     string
		typename risk.RedListCategory
	}{
		{
			name:     "atm processor",
			typename: risk.RedListCategory_REDLIST_CATEGORY_ATM,
		},
		{
			name:     "onboarding state processor",
			typename: risk.RedListCategory_REDLIST_CATEGORY_ONBOARDING_STATE,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := testProcessor(t, tt.typename); err != nil {
				t.Errorf("error in test: %v", err)
			}
		})
	}
}

func TestRedListConsumer_ProcessRedListMemberUpdate(t *testing.T) {
	type args struct {
		ctx context.Context
		req *ProcessNewRedlistMembersRequest
	}
	tests := []struct {
		name    string
		args    args
		mock    func(args args, mocks mockStruct)
		wantErr error
	}{
		{
			name: "empty filename",
			args: args{
				ctx: context.Background(),
				req: &ProcessNewRedlistMembersRequest{
					Filename: "",
				},
			},
			mock:    func(args args, mocks mockStruct) {},
			wantErr: nil,
		},
		{
			name: "file in root directory",
			args: args{
				ctx: context.Background(),
				req: &ProcessNewRedlistMembersRequest{
					Filename: "negative_db_contacts_association.csv",
				},
			},
			mock: func(args args, mocks mockStruct) {
				mocks.s3Client.EXPECT().Read(gomock.Any(), args.req.Filename).Return([]byte{1, 2, 3, 4}, nil)
				mocks.hashedContactMock.EXPECT().ProcessS3FileContent(gomock.Any(), []byte{1, 2, 3, 4})
			},
			wantErr: nil,
		},
		{
			name: "file not in root directory",
			args: args{
				ctx: context.Background(),
				req: &ProcessNewRedlistMembersRequest{
					Filename: "test/dir/negative_db_contacts_association.csv",
				},
			},
			mock: func(args args, mocks mockStruct) {
				mocks.s3Client.EXPECT().Read(gomock.Any(), args.req.Filename).Return([]byte{1, 2, 3, 4}, nil)
				mocks.hashedContactMock.EXPECT().ProcessS3FileContent(gomock.Any(), []byte{1, 2, 3, 4})
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockS3Client := mocks2.NewMockS3Client(ctrl)
			mockProcessor := mocks3.NewMockProcessor(ctrl)
			mocks := mockStruct{
				s3Client:          mockS3Client,
				hashedContactMock: mockProcessor,
				pinCodeMock:       mockProcessor,
				latLongMock:       mockProcessor,
			}
			c := &RedListConsumer{
				S3Client:          mockS3Client,
				hashedContactProc: mockProcessor,
				pinCodeProc:       mockProcessor,
				latLongProc:       mockProcessor,
			}
			tt.mock(tt.args, mocks)
			if err := c.ProcessRedListMemberUpdate(tt.args.ctx, tt.args.req); tt.wantErr != nil && errors.Is(err, tt.wantErr) {
				t.Errorf("ProcessRedListMemberUpdate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_hashedContact_ProcessS3FileContent(t *testing.T) {
	type args struct {
		ctx     context.Context
		content []byte
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(args args, mock mockStruct) *sync.WaitGroup
		wantErr bool
	}{
		{
			name: "Success",
			args: args{
				ctx:     context.Background(),
				content: []byte(csvFixtureContacts),
			},
			mocks: func(args args, mock mockStruct) *sync.WaitGroup {
				wg := sync.WaitGroup{}
				wg.Add(2)
				mock.dao.EXPECT().GetValuesNotInDbByCategory(gomock.Any(), risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT, []*risk.RedListPair{
					{
						Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
						Value: &risk.RedListPair_HashedContact{
							HashedContact: "4a714616d7f333ea4f9847ba5d544365",
						},
					},
					{
						Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
						Value: &risk.RedListPair_HashedContact{
							HashedContact: "27cef2f81fa39bece60b26134181255f",
						},
					},
					{
						Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
						Value: &risk.RedListPair_HashedContact{
							HashedContact: "9c96f16219f039ecd310cda38fb486e1",
						},
					},
					{
						Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
						Value: &risk.RedListPair_HashedContact{
							HashedContact: "40f78d58b07413ce29eb5d0f90655e6b",
						},
					},
					{
						Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
						Value: &risk.RedListPair_HashedContact{
							HashedContact: "849dec21042cae361ac7252d7cd6a8b5",
						},
					},
					{
						Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
						Value: &risk.RedListPair_HashedContact{
							HashedContact: "dbace6fcc442d527e7c7688f32dda58e",
						},
					},
					{
						Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
						Value: &risk.RedListPair_HashedContact{
							HashedContact: "3d7dac2fb1f62637fce185baea71921c",
						},
					}, {
						Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
						Value: &risk.RedListPair_HashedContact{
							HashedContact: "1b64877a8d5b7b50db03954f34b5de6",
						},
					},
					{
						Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
						Value: &risk.RedListPair_HashedContact{
							HashedContact: "0cc4c6175f126ae3c18dc851e01a1f8",
						},
					}, {
						Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
						Value: &risk.RedListPair_HashedContact{
							HashedContact: "0ba0a3a73d55acffe936eba56868a6f",
						},
					},
				}).Return(
					[]*risk.RedLister{
						{
							Key: &risk.RedListPair{
								Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
								Value: &risk.RedListPair_HashedContact{
									HashedContact: "9c96f16219f039ecd310cda38fb486e2",
								},
							},
							RiskScore: 75,
							Reason: &risk.RedListerReason{
								RawReason: "NO. OF KNOWN FRAUDSTER CONTACTS: 3",
							},
						},
					}, nil)
				mock.dao.EXPECT().Upsert(gomock.Any(), []*risk.RedLister{
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
							Value: &risk.RedListPair_HashedContact{
								HashedContact: "4a714616d7f333ea4f9847ba5d544365",
							},
						},
						RiskScore: 75,
						Reason: &risk.RedListerReason{
							RawReason: "NO. OF KNOWN FRAUDSTER CONTACTS: 5",
							Source:    risk.RedListerSource_RISK_ANALYST_INTERNAL,
						},
					},
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
							Value: &risk.RedListPair_HashedContact{
								HashedContact: "27cef2f81fa39bece60b26134181255f",
							},
						},
						RiskScore: 75,
						Reason: &risk.RedListerReason{
							RawReason: "NO. OF KNOWN FRAUDSTER CONTACTS: 3",
							Source:    risk.RedListerSource_RISK_ANALYST_INTERNAL,
						},
					},
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
							Value: &risk.RedListPair_HashedContact{
								HashedContact: "9c96f16219f039ecd310cda38fb486e1",
							},
						},
						RiskScore: 75,
						Reason: &risk.RedListerReason{
							RawReason: "NO. OF KNOWN FRAUDSTER CONTACTS: 3",
							Source:    risk.RedListerSource_RISK_ANALYST_INTERNAL,
						},
					},
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
							Value: &risk.RedListPair_HashedContact{
								HashedContact: "40f78d58b07413ce29eb5d0f90655e6b",
							},
						},
						RiskScore: 75,
						Reason: &risk.RedListerReason{
							RawReason: "NO. OF KNOWN FRAUDSTER CONTACTS: 61",
							Source:    risk.RedListerSource_RISK_ANALYST_INTERNAL,
						},
					},
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
							Value: &risk.RedListPair_HashedContact{
								HashedContact: "849dec21042cae361ac7252d7cd6a8b5",
							},
						},
						RiskScore: 75,
						Reason: &risk.RedListerReason{
							RawReason: "NO. OF KNOWN FRAUDSTER CONTACTS: 4",
							Source:    risk.RedListerSource_RISK_ANALYST_INTERNAL,
						},
					},
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
							Value: &risk.RedListPair_HashedContact{
								HashedContact: "dbace6fcc442d527e7c7688f32dda58e",
							},
						},
						RiskScore: 75,
						Reason: &risk.RedListerReason{
							RawReason: "NO. OF KNOWN FRAUDSTER CONTACTS: 3",
							Source:    risk.RedListerSource_RISK_ANALYST_INTERNAL,
						},
					},
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
							Value: &risk.RedListPair_HashedContact{
								HashedContact: "3d7dac2fb1f62637fce185baea71921c",
							},
						},
						RiskScore: 75,
						Reason: &risk.RedListerReason{
							RawReason: "NO. OF KNOWN FRAUDSTER CONTACTS: 3",
							Source:    risk.RedListerSource_RISK_ANALYST_INTERNAL,
						},
					},
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
							Value: &risk.RedListPair_HashedContact{
								HashedContact: "1b64877a8d5b7b50db03954f34b5de6",
							},
						},
						RiskScore: 75,
						Reason: &risk.RedListerReason{
							RawReason: "NO. OF KNOWN FRAUDSTER CONTACTS: 5",
							Source:    risk.RedListerSource_RISK_ANALYST_INTERNAL,
						},
					},
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
							Value: &risk.RedListPair_HashedContact{
								HashedContact: "0cc4c6175f126ae3c18dc851e01a1f8",
							},
						},
						RiskScore: 75,
						Reason: &risk.RedListerReason{
							RawReason: "NO. OF KNOWN FRAUDSTER CONTACTS: 4",
							Source:    risk.RedListerSource_RISK_ANALYST_INTERNAL,
						},
					},
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
							Value: &risk.RedListPair_HashedContact{
								HashedContact: "0ba0a3a73d55acffe936eba56868a6f",
							},
						},
						RiskScore: 75,
						Reason: &risk.RedListerReason{
							RawReason: "NO. OF KNOWN FRAUDSTER CONTACTS: 11",
							Source:    risk.RedListerSource_RISK_ANALYST_INTERNAL,
						},
					},
				}).DoAndReturn(func(context.Context, []*risk.RedLister) ([]*risk.RedLister, error) {
					wg.Done()
					return nil, nil
				})
				mock.dao.EXPECT().DeleteByCategoryAndValue(gomock.Any(), []*risk.RedListPair{
					{
						Category: risk.RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT,
						Value: &risk.RedListPair_HashedContact{
							HashedContact: "9c96f16219f039ecd310cda38fb486e2",
						},
					},
				}).
					DoAndReturn(func(context.Context, []*risk.RedListPair) error {
						wg.Done()
						return nil
					})
				return &wg
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockDao := mocks.NewMockRedListDao(ctrl)

			c := &hashedContact{
				dao: mockDao,
			}
			mock := mockStruct{
				dao: mockDao,
			}
			wg := tt.mocks(tt.args, mock)
			if err := c.ProcessS3FileContent(tt.args.ctx, tt.args.content); (err != nil) != tt.wantErr {
				t.Errorf("ProcessS3FileContent() error = %v, wantErr %v", err, tt.wantErr)
			}
			if wg != nil {
				wg.Wait()
			}
		})
	}
}

func Test_onboardingState_ProcessS3FileContent(t *testing.T) {
	type args struct {
		ctx     context.Context
		content []byte
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(args args, mock mockStruct)
		wantErr bool
	}{
		{
			name: "Unmarshalling csv error",
			args: args{
				ctx:     context.Background(),
				content: []byte{},
			},
			wantErr: true,
		},
		{
			name: "failed to get objects to delete",
			args: args{
				ctx:     context.Background(),
				content: []byte(csvFixtureOnboardingStates),
			},
			wantErr: true,
			mocks: func(args args, mock mockStruct) {
				mock.dao.EXPECT().GetValuesNotInDbByCategory(context.Background(), risk.RedListCategory_REDLIST_CATEGORY_ONBOARDING_STATE, onboardingStateRedListPair).Return(nil, epifierrors.ErrPermanent)
			},
		},
		{
			name: "failed to get objects",
			args: args{
				ctx:     context.Background(),
				content: []byte(csvFixtureOnboardingStates),
			},
			wantErr: true,
			mocks: func(args args, mock mockStruct) {
				mock.dao.EXPECT().GetValuesNotInDbByCategory(context.Background(), risk.RedListCategory_REDLIST_CATEGORY_ONBOARDING_STATE, onboardingStateRedListPair).Return([]*risk.RedLister{
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_ONBOARDING_STATE,
							Value: &risk.RedListPair_OnboardingState{
								OnboardingState: "Bihar",
							},
						},
					},
				}, nil)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), onboardingStateRedListPair[0]).Return(nil, epifierrors.ErrPermanent)
			},
		},
		{
			name: "failed to delete objects",
			args: args{
				ctx:     context.Background(),
				content: []byte(csvFixtureOnboardingStates),
			},
			wantErr: true,
			mocks: func(args args, mock mockStruct) {
				mock.dao.EXPECT().GetValuesNotInDbByCategory(context.Background(), risk.RedListCategory_REDLIST_CATEGORY_ONBOARDING_STATE, onboardingStateRedListPair).Return([]*risk.RedLister{
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_ONBOARDING_STATE,
							Value: &risk.RedListPair_OnboardingState{
								OnboardingState: "Bihar",
							},
						},
					},
				}, nil)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), onboardingStateRedListPair[0]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), onboardingStateRedListPair[1]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), onboardingStateRedListPair[2]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().DeleteByCategoryAndValue(context.Background(), []*risk.RedListPair{
					{
						Category: risk.RedListCategory_REDLIST_CATEGORY_ONBOARDING_STATE,
						Value: &risk.RedListPair_OnboardingState{
							OnboardingState: "Bihar",
						},
					},
				}).Return(epifierrors.ErrPermanent)
			},
		},
		{
			name: "failed to create objects",
			args: args{
				ctx:     context.Background(),
				content: []byte(csvFixtureOnboardingStates),
			},
			wantErr: true,
			mocks: func(args args, mock mockStruct) {
				mock.dao.EXPECT().GetValuesNotInDbByCategory(context.Background(), risk.RedListCategory_REDLIST_CATEGORY_ONBOARDING_STATE, onboardingStateRedListPair).Return([]*risk.RedLister{
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_ONBOARDING_STATE,
							Value: &risk.RedListPair_OnboardingState{
								OnboardingState: "Bihar",
							},
						},
					},
				}, nil)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), onboardingStateRedListPair[0]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), onboardingStateRedListPair[1]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), onboardingStateRedListPair[2]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().DeleteByCategoryAndValue(context.Background(), []*risk.RedListPair{
					{
						Category: risk.RedListCategory_REDLIST_CATEGORY_ONBOARDING_STATE,
						Value: &risk.RedListPair_OnboardingState{
							OnboardingState: "Bihar",
						},
					},
				}).Return(nil)
				mock.dao.EXPECT().Upsert(context.Background(), onboardingStateRedLister).Return(nil, epifierrors.ErrPermanent)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockDao := mocks.NewMockRedListDao(ctrl)
			mockRiskEvaluatorEntityDao := mocks.NewMockRiskEvaluatorEntityDao(ctrl)
			mockTxnMoniotoringClient := mocks4.NewMockTransactionRiskDronaPayClient(ctrl)
			mock := mockStruct{
				dao:                    mockDao,
				riskEvaluatorEntityDao: mockRiskEvaluatorEntityDao,
				vgTxnMonitoringClient:  mockTxnMoniotoringClient,
			}
			d := &onboardingState{
				dao:     mockDao,
				pusher:  txn_monitoring.NewDataPusher(mockRiskEvaluatorEntityDao, mockTxnMoniotoringClient, nil),
				genConf: riskDependencies.GenConf,
			}
			if tt.mocks != nil {
				tt.mocks(tt.args, mock)
			}
			if err := d.ProcessS3FileContent(tt.args.ctx, tt.args.content); (err != nil) != tt.wantErr {
				t.Errorf("ProcessS3FileContent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_atm_ProcessS3FileContent(t *testing.T) {
	type args struct {
		ctx     context.Context
		content []byte
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(args args, mock mockStruct)
		wantErr bool
	}{
		{
			name: "Unmarshalling csv error",
			args: args{
				ctx:     context.Background(),
				content: []byte{},
			},
			wantErr: true,
		},
		{
			name: "failed to get objects to delete",
			args: args{
				ctx:     context.Background(),
				content: []byte(csvFixturesAtm),
			},
			wantErr: true,
			mocks: func(args args, mock mockStruct) {
				mock.dao.EXPECT().GetValuesNotInDbByCategory(context.Background(), risk.RedListCategory_REDLIST_CATEGORY_ATM, atmRedListPair).Return(nil, epifierrors.ErrPermanent)
			},
		},
		{
			name: "failed to get objects",
			args: args{
				ctx:     context.Background(),
				content: []byte(csvFixturesAtm),
			},
			wantErr: true,
			mocks: func(args args, mock mockStruct) {
				mock.dao.EXPECT().GetValuesNotInDbByCategory(context.Background(), risk.RedListCategory_REDLIST_CATEGORY_ATM, atmRedListPair).Return([]*risk.RedLister{
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_ATM,
							Value: &risk.RedListPair_Atm{
								Atm: &risk.AtmRedList{
									AtmPi: "999",
								},
							},
						},
					},
				}, nil)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[0]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[1]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[2]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[3]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[4]).Return(nil, epifierrors.ErrPermanent)
			},
		},
		{
			name: "failed to delete objects",
			args: args{
				ctx:     context.Background(),
				content: []byte(csvFixturesAtm),
			},
			wantErr: true,
			mocks: func(args args, mock mockStruct) {
				mock.dao.EXPECT().GetValuesNotInDbByCategory(context.Background(), risk.RedListCategory_REDLIST_CATEGORY_ATM, atmRedListPair).Return([]*risk.RedLister{
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_ATM,
							Value: &risk.RedListPair_Atm{
								Atm: &risk.AtmRedList{
									AtmPi: "1000",
								},
							},
						},
					},
				}, nil)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[0]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[1]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[2]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[3]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[4]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().DeleteByCategoryAndValue(context.Background(), []*risk.RedListPair{
					{
						Category: risk.RedListCategory_REDLIST_CATEGORY_ATM,
						Value: &risk.RedListPair_Atm{
							Atm: &risk.AtmRedList{
								AtmPi: "1000",
							},
						},
					},
				}).Return(epifierrors.ErrPermanent)
			},
		},
		{
			name: "failed to create objects",
			args: args{
				ctx:     context.Background(),
				content: []byte(csvFixturesAtm),
			},
			wantErr: true,
			mocks: func(args args, mock mockStruct) {
				mock.dao.EXPECT().GetValuesNotInDbByCategory(context.Background(), risk.RedListCategory_REDLIST_CATEGORY_ATM, atmRedListPair).Return([]*risk.RedLister{
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_ATM,
							Value: &risk.RedListPair_Atm{
								Atm: &risk.AtmRedList{
									AtmPi: "999",
								},
							},
						},
					},
				}, nil)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[0]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[1]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[2]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[3]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[4]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().DeleteByCategoryAndValue(context.Background(), []*risk.RedListPair{
					{
						Category: risk.RedListCategory_REDLIST_CATEGORY_ATM,
						Value: &risk.RedListPair_Atm{
							Atm: &risk.AtmRedList{
								AtmPi: "999",
							},
						},
					},
				}).Return(nil)
				mock.dao.EXPECT().Upsert(context.Background(), atmRedLister).Return(nil, epifierrors.ErrPermanent)
			},
		},
		{
			name: "failed to create objects, all updates",
			args: args{
				ctx:     context.Background(),
				content: []byte(csvFixturesAtm),
			},
			wantErr: true,
			mocks: func(args args, mock mockStruct) {
				mock.dao.EXPECT().GetValuesNotInDbByCategory(context.Background(), risk.RedListCategory_REDLIST_CATEGORY_ATM, atmRedListPair).Return([]*risk.RedLister{
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_ATM,
							Value: &risk.RedListPair_Atm{
								Atm: &risk.AtmRedList{
									AtmPi: "999",
								},
							},
						},
					},
				}, nil)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[0]).Return(atmRedListerInDB[0], nil)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[1]).Return(atmRedListerInDB[1], nil)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[2]).Return(atmRedListerInDB[2], nil)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[3]).Return(atmRedListerInDB[3], nil)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[4]).Return(atmRedListerInDB[4], nil)
				mock.dao.EXPECT().DeleteByCategoryAndValue(context.Background(), []*risk.RedListPair{
					{
						Category: risk.RedListCategory_REDLIST_CATEGORY_ATM,
						Value: &risk.RedListPair_Atm{
							Atm: &risk.AtmRedList{
								AtmPi: "999",
							},
						},
					},
					atmRedListPair[1],
					atmRedListPair[2],
					atmRedListPair[3],
				}).Return(nil)
				mock.dao.EXPECT().Upsert(context.Background(), []*risk.RedLister{atmRedLister[0], atmRedLister[4]}).Return(nil, nil)
				mock.dao.EXPECT().Upsert(context.Background(), []*risk.RedLister{atmRedLister[1], atmRedLister[2], atmRedLister[3]}).
					Return(nil, epifierrors.ErrPermanent)
			},
		},
		{
			name: "failed to create objects, updates and create	",
			args: args{
				ctx:     context.Background(),
				content: []byte(csvFixturesAtm),
			},
			wantErr: true,
			mocks: func(args args, mock mockStruct) {
				mock.dao.EXPECT().GetValuesNotInDbByCategory(context.Background(), risk.RedListCategory_REDLIST_CATEGORY_ATM, atmRedListPair).Return([]*risk.RedLister{
					{
						Key: &risk.RedListPair{
							Category: risk.RedListCategory_REDLIST_CATEGORY_ATM,
							Value: &risk.RedListPair_Atm{
								Atm: &risk.AtmRedList{
									AtmPi: "999",
								},
							},
						},
					},
				}, nil)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[0]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[1]).Return(atmRedListerInDB2[1], nil)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[2]).Return(atmRedListerInDB2[2], nil)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[3]).Return(atmRedListerInDB2[3], nil)
				mock.dao.EXPECT().GetByCategoryAndVal(context.Background(), atmRedListPair[4]).Return(nil, epifierrors.ErrRecordNotFound)
				mock.dao.EXPECT().DeleteByCategoryAndValue(context.Background(), []*risk.RedListPair{
					{
						Category: risk.RedListCategory_REDLIST_CATEGORY_ATM,
						Value: &risk.RedListPair_Atm{
							Atm: &risk.AtmRedList{
								AtmPi: "999",
							},
						},
					},
					atmRedListPair[1],
					atmRedListPair[2],
					atmRedListPair[3],
				}).Return(nil)
				mock.dao.EXPECT().Upsert(context.Background(), []*risk.RedLister{atmRedLister[0], atmRedLister[1], atmRedLister[2], atmRedLister[3], atmRedLister[4]}).
					Return(nil, epifierrors.ErrPermanent)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockDao := mocks.NewMockRedListDao(ctrl)
			mockRiskEvaluatorEntityDao := mocks.NewMockRiskEvaluatorEntityDao(ctrl)
			mockTxnMoniotoringClient := mocks4.NewMockTransactionRiskDronaPayClient(ctrl)
			mock := mockStruct{
				dao:                    mockDao,
				riskEvaluatorEntityDao: mockRiskEvaluatorEntityDao,
				vgTxnMonitoringClient:  mockTxnMoniotoringClient,
			}
			d := &atm{
				dao:     mockDao,
				pusher:  txn_monitoring.NewDataPusher(mockRiskEvaluatorEntityDao, mockTxnMoniotoringClient, nil),
				genConf: riskDependencies.GenConf,
			}
			if tt.mocks != nil {
				tt.mocks(tt.args, mock)
			}
			if err := d.ProcessS3FileContent(tt.args.ctx, tt.args.content); (err != nil) != tt.wantErr {
				t.Errorf("ProcessS3FileContent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestIPAddress_ProcessS3FileContent(t *testing.T) {
	type args struct {
		ctx     context.Context
		content []byte
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(args args, mock mockStruct)
		wantErr bool
	}{
		{
			name: "success when valid csv is given",
			args: args{
				ctx:     context.Background(),
				content: []byte(csvFixturesIPAddress),
			},
			mocks: func(args args, mock mockStruct) {
				mock.dao.EXPECT().Upsert(context.Background(), ipAddressResLister).Return(nil, nil)
			},
			wantErr: false,
		},
		{
			name: "failure when valid csv is given but Upsert call fails",
			args: args{
				ctx:     context.Background(),
				content: []byte(csvFixturesIPAddress),
			},
			mocks: func(args args, mock mockStruct) {
				mock.dao.EXPECT().Upsert(context.Background(), ipAddressResLister).Return(nil, fmt.Errorf(""))
			},
			wantErr: true,
		},
		{
			name: "success when csv contains one invalid row",
			args: args{
				ctx:     context.Background(),
				content: []byte(csvFixturesIPAddress1),
			},
			mocks: func(args args, mock mockStruct) {
				mock.dao.EXPECT().Upsert(context.Background(), ipAddressResLister1).Return(nil, nil)
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockDao := mocks.NewMockRedListDao(ctrl)
			mock := mockStruct{
				dao: mockDao,
			}
			i := &IPAddress{
				dao: mockDao,
			}
			if tt.mocks != nil {
				tt.mocks(tt.args, mock)
			}
			if err := i.ProcessS3FileContent(tt.args.ctx, tt.args.content); (err != nil) != tt.wantErr {
				t.Errorf("ProcessS3FileContent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
