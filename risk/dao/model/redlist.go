package model

import (
	"time"

	"gorm.io/gorm"
	"gorm.io/plugin/soft_delete"

	"github.com/epifi/gamma/api/risk"
)

type RedLister struct {
	Id            string `gorm:"type:uuid;default:gen_random_uuid()"`
	Category      risk.RedListCategory
	Value         string
	RiskScore     float32
	Reason        *risk.RedListerReason
	CreatedAt     time.Time
	UpdatedAt     time.Time
	Metadata      *risk.Metadata
	DeletedAtUnix soft_delete.DeletedAt
}

func (l *RedLister) AfterSave(_ *gorm.DB) error {
	// Implement Changefeed for RedLister
	return nil
}
