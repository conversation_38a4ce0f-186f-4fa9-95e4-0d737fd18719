package activity

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	cmActivityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
)

// ApplyScreenerVerdict is a generic screener verdict handler
// Calls the underlying /risk/screener process screener verdict interface
func (p *Processor) ApplyScreenerVerdict(ctx context.Context, req *cmActivityPb.ApplyScreenerVerdictRequest) (*cmActivityPb.ApplyScreenerVerdictResponse, error) {
	lg := activity.GetLogger(ctx)
	if verdictErr := p.screenerVerdictProcessor.Process(ctx, req.GetVerdictRequest()); verdictErr != nil {
		switch {
		case errors.Is(verdictErr, epifierrors.ErrInvalidArgument):
			lg.Error("invalid argument passed processing verdict", zap.Error(verdictErr))
			return nil, epifitemporal.NewPermanentErrorWithType(fmt.Errorf("invalid argument  passed processing verdict %w", verdictErr), epifitemporal.ErrInvalidArgument)
		default:
			lg.Error("failed to process verdict", zap.Error(verdictErr))
			return nil, epifitemporal.NewTransientError(fmt.Errorf("failed to process verdict %w", verdictErr))
		}
	}

	return &cmActivityPb.ApplyScreenerVerdictResponse{}, nil
}
