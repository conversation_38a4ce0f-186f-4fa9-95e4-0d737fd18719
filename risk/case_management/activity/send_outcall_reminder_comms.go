package activity

import (
	"context"
	"errors"
	"fmt"

	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	activityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
)

// SendOutcallReminderComms sends outcall attempt reminder comms and adds comment to case on success.
func (p *Processor) SendOutcallReminderComms(ctx context.Context, req *activityPb.SendOutcallReminderCommsRequest) (
	*activityPb.SendOutcallReminderCommsResponse, error) {
	lg := activity.GetLogger(ctx)

	err := p.outcallReminder.RemindForUnansweredCall(ctx, req.GetCaseId())
	if err != nil {
		lg.Error("failed to send outcall reminder", zap.String(logger.CASE_ID, req.GetCaseId()), zap.Error(err))
	}
	switch {
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return nil, epifitemporal.NewPermanentErrorWithType(fmt.Errorf("failed to send outcall reminder %w", err),
			epifitemporal.ErrInvalidArgument)
	case err != nil:
		return nil, epifitemporal.NewTransientError(fmt.Errorf("failed to send outcall reminder %w", err))
	default:
		return &activityPb.SendOutcallReminderCommsResponse{}, nil
	}
}
