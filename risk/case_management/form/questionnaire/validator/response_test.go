package validator

import (
	"context"
	"errors"
	"testing"

	structPb "google.golang.org/protobuf/types/known/structpb"

	formPb "github.com/epifi/gamma/api/risk/case_management/form"
	"github.com/epifi/be-common/pkg/epifierrors"
)

func TestResponseValidatorImpl_ValidateFileResponse(t *testing.T) {
	type args struct {
		response *formPb.QuestionWithResponse
	}
	tests := []struct {
		name      string
		args      args
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name:    "invalid question type",
			args:    args{},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, epifierrors.ErrInvalidArgument)
			},
		},
		{
			name: "failed for empty value",
			args: args{
				response: &formPb.QuestionWithResponse{
					IsMandatory: true,
					Question:    &formPb.Question{Type: formPb.QuestionType_QUESTION_TYPE_FILE},
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, ErrInvalidResponseValue)
			},
		},
		{
			name: "success",
			args: args{
				response: &formPb.QuestionWithResponse{
					Response: &formPb.QuestionResponse{
						Response: &formPb.Response{
							Response: &formPb.Response_File{
								File: &formPb.File{
									S3Path: "test/test1/test2/test3.png",
								},
							},
						},
					},
					IsMandatory: true,
					Question: &formPb.Question{
						Type: formPb.QuestionType_QUESTION_TYPE_FILE,
						Options: &formPb.QuestionOptions{
							Options: &formPb.QuestionOptions_FileOptions{
								FileOptions: &formPb.FileOptions{
									AllowedContentTypes: []formPb.FileContentType{
										formPb.FileContentType_FILE_CONTENT_TYPE_PNG,
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name: "failed for invalid file type",
			args: args{
				response: &formPb.QuestionWithResponse{
					Response: &formPb.QuestionResponse{
						Response: &formPb.Response{
							Response: &formPb.Response_File{
								File: &formPb.File{
									S3Path: "test/test1/test2/test3.rtcx",
								},
							},
						},
					},
					IsMandatory: true,
					Question: &formPb.Question{
						Type: formPb.QuestionType_QUESTION_TYPE_FILE,
						Options: &formPb.QuestionOptions{
							Options: &formPb.QuestionOptions_FileOptions{
								FileOptions: &formPb.FileOptions{
									AllowedContentTypes: []formPb.FileContentType{
										formPb.FileContentType_FILE_CONTENT_TYPE_PNG,
									},
								},
							},
						},
					},
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, ErrInvalidResponseValue)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ResponseValidatorImpl{}
			err := r.ValidateFileResponse(context.Background(), tt.args.response)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateFileResponse() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.assertErr != nil && !tt.assertErr(err) {
				t.Errorf("ValidateFileResponse() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestResponseValidatorImpl_ValidateMultiChoiceResponse(t *testing.T) {
	type args struct {
		response *formPb.QuestionWithResponse
	}
	tests := []struct {
		name      string
		args      args
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name:    "invalid question type",
			args:    args{},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, epifierrors.ErrInvalidArgument)
			},
		},
		{
			name: "failed for empty value",
			args: args{
				response: &formPb.QuestionWithResponse{
					IsMandatory: true,
					Question:    &formPb.Question{Type: formPb.QuestionType_QUESTION_TYPE_MULTI_CHOICE},
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, ErrInvalidResponseValue)
			},
		},
		{
			name: "failed for empty value",
			args: args{
				response: &formPb.QuestionWithResponse{
					Response: &formPb.QuestionResponse{
						Response: &formPb.Response{
							Response: &formPb.Response_Value{
								Value: &structPb.Value{
									Kind: &structPb.Value_ListValue{
										ListValue: &structPb.ListValue{},
									},
								},
							},
						},
					},
					Question:    &formPb.Question{Type: formPb.QuestionType_QUESTION_TYPE_MULTI_CHOICE},
					IsMandatory: true,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, ErrInvalidResponseValue)
			},
		},
		{
			name: "failed for invalid value in multi select",
			args: args{
				response: &formPb.QuestionWithResponse{
					Response: &formPb.QuestionResponse{
						Response: &formPb.Response{
							Response: &formPb.Response_Value{
								Value: &structPb.Value{
									Kind: &structPb.Value_ListValue{
										ListValue: &structPb.ListValue{
											Values: []*structPb.Value{
												{
													Kind: &structPb.Value_StringValue{StringValue: "invalid"},
												},
											},
										},
									},
								},
							},
						},
					},
					Question: &formPb.Question{Type: formPb.QuestionType_QUESTION_TYPE_MULTI_CHOICE,
						Options: &formPb.QuestionOptions{Options: &formPb.QuestionOptions_MultiChoice{
							MultiChoice: &formPb.MultiChoiceOptions{
								ChoiceConditionalQuestionsMap: map[string]*formPb.StoredConditionalQuestions{
									"Yes": nil,
								},
								IsMultiSelect: true,
							},
						}}},
					IsMandatory: true,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, ErrInvalidResponseValue)
			},
		},
		{
			name: "failed for duplicate value in multi select",
			args: args{
				response: &formPb.QuestionWithResponse{
					Response: &formPb.QuestionResponse{
						Response: &formPb.Response{
							Response: &formPb.Response_Value{
								Value: &structPb.Value{
									Kind: &structPb.Value_ListValue{
										ListValue: &structPb.ListValue{
											Values: []*structPb.Value{
												{
													Kind: &structPb.Value_StringValue{StringValue: "Yes"},
												},
												{
													Kind: &structPb.Value_StringValue{StringValue: "Yes"},
												},
											},
										},
									},
								},
							},
						},
					},
					Question: &formPb.Question{Type: formPb.QuestionType_QUESTION_TYPE_MULTI_CHOICE,
						Options: &formPb.QuestionOptions{Options: &formPb.QuestionOptions_MultiChoice{
							MultiChoice: &formPb.MultiChoiceOptions{
								ChoiceConditionalQuestionsMap: map[string]*formPb.StoredConditionalQuestions{
									"Yes": nil,
								},
								IsMultiSelect: true,
							},
						}}},
					IsMandatory: true,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, ErrInvalidResponseValue)
			},
		},
		{
			name: "success for multi select",
			args: args{
				response: &formPb.QuestionWithResponse{
					Response: &formPb.QuestionResponse{
						Response: &formPb.Response{
							Response: &formPb.Response_Value{
								Value: &structPb.Value{
									Kind: &structPb.Value_ListValue{
										ListValue: &structPb.ListValue{
											Values: []*structPb.Value{
												{
													Kind: &structPb.Value_StringValue{StringValue: "Yes"},
												},
											},
										},
									},
								},
							},
						},
					},
					Question: &formPb.Question{Type: formPb.QuestionType_QUESTION_TYPE_MULTI_CHOICE,
						Options: &formPb.QuestionOptions{Options: &formPb.QuestionOptions_MultiChoice{
							MultiChoice: &formPb.MultiChoiceOptions{
								ChoiceConditionalQuestionsMap: map[string]*formPb.StoredConditionalQuestions{
									"Yes": nil,
								},
								IsMultiSelect: true,
							},
						}}},
					IsMandatory: true,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to invalid value in single select",
			args: args{
				response: &formPb.QuestionWithResponse{
					Response: &formPb.QuestionResponse{
						Response: &formPb.Response{
							Response: &formPb.Response_Value{
								Value: &structPb.Value{
									Kind: &structPb.Value_StringValue{
										StringValue: "yes",
									},
								},
							},
						},
					},
					Question: &formPb.Question{Type: formPb.QuestionType_QUESTION_TYPE_MULTI_CHOICE,
						Options: &formPb.QuestionOptions{Options: &formPb.QuestionOptions_MultiChoice{
							MultiChoice: &formPb.MultiChoiceOptions{
								ChoiceConditionalQuestionsMap: map[string]*formPb.StoredConditionalQuestions{
									"Yes": nil,
								},
								IsMultiSelect: false,
							},
						}}},
					IsMandatory: true,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, ErrInvalidResponseValue)
			},
		},
		{
			name: "success in single select",
			args: args{
				response: &formPb.QuestionWithResponse{
					Response: &formPb.QuestionResponse{
						Response: &formPb.Response{
							Response: &formPb.Response_Value{
								Value: &structPb.Value{
									Kind: &structPb.Value_StringValue{
										StringValue: "Yes",
									},
								},
							},
						},
					},
					Question: &formPb.Question{Type: formPb.QuestionType_QUESTION_TYPE_MULTI_CHOICE,
						Options: &formPb.QuestionOptions{Options: &formPb.QuestionOptions_MultiChoice{
							MultiChoice: &formPb.MultiChoiceOptions{
								ChoiceConditionalQuestionsMap: map[string]*formPb.StoredConditionalQuestions{
									"Yes": nil,
								},
								IsMultiSelect: false,
							},
						}}},
					IsMandatory: true,
				},
			},
		},
		{
			name: "success for empty response in single select non mandatory question",
			args: args{
				response: &formPb.QuestionWithResponse{
					Response: &formPb.QuestionResponse{
						Response: &formPb.Response{},
					},
					Question: &formPb.Question{Type: formPb.QuestionType_QUESTION_TYPE_MULTI_CHOICE,
						Options: &formPb.QuestionOptions{Options: &formPb.QuestionOptions_MultiChoice{
							MultiChoice: &formPb.MultiChoiceOptions{
								ChoiceConditionalQuestionsMap: map[string]*formPb.StoredConditionalQuestions{
									"Yes": nil,
								},
								IsMultiSelect: false,
							},
						}}},
					IsMandatory: false,
				},
			},
		},
		{
			name: "success for empty response in multi select non mandatory question",
			args: args{
				response: &formPb.QuestionWithResponse{
					Response: &formPb.QuestionResponse{
						Response: &formPb.Response{},
					},
					Question: &formPb.Question{Type: formPb.QuestionType_QUESTION_TYPE_MULTI_CHOICE,
						Options: &formPb.QuestionOptions{Options: &formPb.QuestionOptions_MultiChoice{
							MultiChoice: &formPb.MultiChoiceOptions{
								ChoiceConditionalQuestionsMap: map[string]*formPb.StoredConditionalQuestions{
									"Yes": nil,
								},
								IsMultiSelect: true,
							},
						}}},
					IsMandatory: false,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ResponseValidatorImpl{}
			err := r.ValidateMultiChoiceResponse(context.Background(), tt.args.response)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateMultiChoiceResponse() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.assertErr != nil && !tt.assertErr(err) {
				t.Errorf("ValidateMultiChoiceResponse() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestResponseValidatorImpl_ValidateTextResponse(t *testing.T) {
	type args struct {
		response *formPb.QuestionWithResponse
	}
	tests := []struct {
		name      string
		args      args
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name:    "invalid question type",
			args:    args{},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, epifierrors.ErrInvalidArgument)
			},
		},
		{
			name: "failed for empty value",
			args: args{
				response: &formPb.QuestionWithResponse{
					IsMandatory: true,
					Question:    &formPb.Question{Type: formPb.QuestionType_QUESTION_TYPE_TEXT},
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, ErrInvalidResponseValue)
			},
		},
		{
			name: "failed for empty value",
			args: args{
				response: &formPb.QuestionWithResponse{
					Response: &formPb.QuestionResponse{
						Response: &formPb.Response{
							Response: &formPb.Response_Value{
								Value: &structPb.Value{
									Kind: &structPb.Value_StringValue{
										StringValue: "",
									},
								},
							},
						},
					},
					Question:    &formPb.Question{Type: formPb.QuestionType_QUESTION_TYPE_TEXT},
					IsMandatory: true,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, ErrInvalidResponseValue)
			},
		},
		{
			name: "success with empty response for ",
			args: args{
				response: &formPb.QuestionWithResponse{
					Question:    &formPb.Question{Type: formPb.QuestionType_QUESTION_TYPE_TEXT},
					IsMandatory: false,
				},
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				response: &formPb.QuestionWithResponse{
					Response: &formPb.QuestionResponse{
						Response: &formPb.Response{
							Response: &formPb.Response_Value{
								Value: &structPb.Value{
									Kind: &structPb.Value_StringValue{
										StringValue: "value",
									},
								},
							},
						},
					},
					Question: &formPb.Question{Type: formPb.QuestionType_QUESTION_TYPE_TEXT,
						Options: &formPb.QuestionOptions{
							Options: &formPb.QuestionOptions_TextOptions{
								TextOptions: &formPb.TextOptions{
									MaxCharsLimit: 100,
								},
							},
						},
					},
					IsMandatory: true,
				},
			},
			wantErr: false,
		},
		{
			name: "failed for response large than limit",
			args: args{
				response: &formPb.QuestionWithResponse{
					Response: &formPb.QuestionResponse{
						Response: &formPb.Response{
							Response: &formPb.Response_Value{
								Value: &structPb.Value{
									Kind: &structPb.Value_StringValue{
										StringValue: "value",
									},
								},
							},
						},
					},
					Question: &formPb.Question{Type: formPb.QuestionType_QUESTION_TYPE_TEXT,
						Options: &formPb.QuestionOptions{
							Options: &formPb.QuestionOptions_TextOptions{
								TextOptions: &formPb.TextOptions{
									MaxCharsLimit: 1,
								},
							},
						},
					},
					IsMandatory: true,
				},
			},
			assertErr: func(err error) bool {
				return errors.Is(err, ErrContentSizeLimitExceeded)
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ResponseValidatorImpl{}
			err := r.ValidateTextResponse(context.Background(), tt.args.response)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateTextResponse() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.assertErr != nil && !tt.assertErr(err) {
				t.Errorf("ValidateTextResponse() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
