package case_management

import (
	"os"
	"testing"

	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	"github.com/epifi/be-common/pkg/logger"
	caseStoreMocks "github.com/epifi/gamma/risk/case_management/casestore/mocks"
	mockCaseStore "github.com/epifi/gamma/risk/case_management/casestore/mocks"
	mock_dao "github.com/epifi/gamma/risk/case_management/dao/mocks"
	"github.com/epifi/gamma/risk/case_management/essential/aggregator/mocks"
	"github.com/epifi/gamma/risk/test"

	"github.com/golang/mock/gomock"
)

type mockedDependencies struct {
	mockCelestialClient *celestialMocks.MockCelestialClient
	mockCaseStore       *mockCaseStore.MockCaseStore
	mockAlertDao        *mock_dao.MockAlertDao
	mockAggregator      *mocks.MockAggregator
}

func newServerWithMocks(t *testing.T) (*Service, *mockedDependencies, func()) {
	ctr := gomock.NewController(t)
	mockCelestialClient := celestialMocks.NewMockCelestialClient(ctr)
	mockCaseStore := caseStoreMocks.NewMockCaseStore(ctr)
	mockAlertDao := mock_dao.NewMockAlertDao(ctr)
	mockAggregator := mocks.NewMockAggregator(ctr)
	md := &mockedDependencies{
		mockCelestialClient: mockCelestialClient,
		mockCaseStore:       mockCaseStore,
		mockAlertDao:        mockAlertDao,
		mockAggregator:      mockAggregator,
	}

	svc := &Service{
		celestialClient: mockCelestialClient,
		caseStore:       mockCaseStore,
		alertDao:        mockAlertDao,
		aggregator:      mockAggregator,
	}
	return svc, md, func() {
		ctr.Finish()
	}
}

func TestMain(m *testing.M) {
	var teardown func()
	testDep, teardown := test.InitTestServerWithoutDB()
	conf := testDep.Conf
	genCfg = testDep.GenConf
	// Setup logger
	logger.Init(conf.Application.Environment)
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
