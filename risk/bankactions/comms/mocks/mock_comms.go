// Code generated by MockGen. DO NOT EDIT.
// Source: comms.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	comms "github.com/epifi/gamma/api/comms"
	risk "github.com/epifi/gamma/api/risk"
	gomock "github.com/golang/mock/gomock"
)

// MockBuilder is a mock of Builder interface.
type MockBuilder struct {
	ctrl     *gomock.Controller
	recorder *MockBuilderMockRecorder
}

// MockBuilderMockRecorder is the mock recorder for MockBuilder.
type MockBuilderMockRecorder struct {
	mock *MockBuilder
}

// NewMockBuilder creates a new mock instance.
func NewMockBuilder(ctrl *gomock.Controller) *MockBuilder {
	mock := &MockBuilder{ctrl: ctrl}
	mock.recorder = &MockBuilderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBuilder) EXPECT() *MockBuilderMockRecorder {
	return m.recorder
}

// GetNotifications mocks base method.
func (m *MockBuilder) GetNotifications(ctx context.Context, action *risk.RiskBankActions, formId string, isReminder bool) ([]*comms.Communication, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNotifications", ctx, action, formId, isReminder)
	ret0, _ := ret[0].([]*comms.Communication)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNotifications indicates an expected call of GetNotifications.
func (mr *MockBuilderMockRecorder) GetNotifications(ctx, action, formId, isReminder interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNotifications", reflect.TypeOf((*MockBuilder)(nil).GetNotifications), ctx, action, formId, isReminder)
}
