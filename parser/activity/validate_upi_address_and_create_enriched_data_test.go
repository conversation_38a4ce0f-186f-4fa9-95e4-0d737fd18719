package activity_test

import (
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	parserNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/parser"

	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	paymentNotificationPb "github.com/epifi/gamma/api/order/payment/notification"
	parserActivityPkg "github.com/epifi/gamma/api/parser/activity"
	payPb "github.com/epifi/gamma/api/pay"
	payTransactionBackfill "github.com/epifi/gamma/api/pay/transaction_backfill"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
)

var (
	orderTxnPayload = &payTransactionBackfill.OrderTxnPayData{
		TxnDetail: &payTransactionBackfill.TxnDetail{
			Id:              "id-1",
			PiFrom:          "pi-from",
			PiTo:            "pi-to",
			Utr:             "utr",
			PaymentProtocol: paymentPb.PaymentProtocol_UPI,
			Remarks:         "remarks",
			RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
				"CREDIT": {
					Particulars:       "particulars",
					AdditionalDetails: "pTid",
					InstrumentDetails: &paymentPb.InstrumentDetails{
						InstrumentType:   "app",
						InstrumentNumber: "124",
					},
					CbsId: "cbs-id",
				},
			},
			CreditedAt: timestamp.Now(),
			Metadata:   &paymentPb.Metadata{},
		},
		OrderDetail: &payTransactionBackfill.OrderDetail{
			Id:          "order-id-1",
			FromActorId: "actor-1",
			ToActorId:   "actor-2",
			Provenance:  orderPb.OrderProvenance_USER_APP,
		},
	}

	orderTxnResponsePayload = &payTransactionBackfill.OrderTxnPayData{
		TxnDetail: &payTransactionBackfill.TxnDetail{
			Id:              "id-1",
			PiFrom:          "pi-from",
			PiTo:            "pi-to",
			Utr:             "utr",
			PaymentProtocol: paymentPb.PaymentProtocol_UPI,
			Remarks:         "remarks",
			RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
				"CREDIT": {
					Particulars:       "particulars",
					AdditionalDetails: "pTid",
					InstrumentDetails: &paymentPb.InstrumentDetails{
						InstrumentType:   "app",
						InstrumentNumber: "124",
					},
					CbsId: "cbs-id",
				},
			},
			CreditedAt: timestamp.Now(),
			Metadata: &paymentPb.Metadata{
				FetchedRemitterDetails: &paymentPb.EnrichedPiDetailsOfTransactingActors{
					PayerPiId: "payer-id",
				},
			},
		},
		OrderDetail: &payTransactionBackfill.OrderDetail{
			Id:          "order-id-1",
			FromActorId: "actor-1",
			ToActorId:   "actor-2",
			Provenance:  orderPb.OrderProvenance_USER_APP,
		},
	}
	parsedTxnParticulars = &paymentNotificationPb.ParsedTxnParticulars{
		Protocol:       paymentPb.PaymentProtocol_UPI,
		OtherActorName: "other-actor-name",
		PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Vpa{
			Vpa: "vpa@fbl",
		},
		Utr: "utr",
	}
)

func TestProcessor_ValidateUpiAddressAndCreateEnrichedData(t *testing.T) {
	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type mockGetPiDetailsFromVendorByTxnId struct {
		enable bool
		req    *payPb.GetPiDetailsFromVendorByTxnIdRequest
		res    *payPb.GetPiDetailsFromVendorByTxnIdResponse
		err    error
	}
	tests := []struct {
		name                              string
		req                               *parserActivityPkg.ValidateUpiAddressAndCreateEnrichedDataRequest
		res                               *parserActivityPkg.ValidateUpiAddressAndCreateEnrichedDataResponse
		mockGetPiDetailsFromVendorByTxnId mockGetPiDetailsFromVendorByTxnId
		wantErr                           bool
		assertErr                         func(err error) bool
	}{
		{
			name: "successfully validated upi address and created enriched txn request",
			req: &parserActivityPkg.ValidateUpiAddressAndCreateEnrichedDataRequest{
				RequestHeader: &activity.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				OrderWithTransaction: orderTxnPayload,
			},
			res: &parserActivityPkg.ValidateUpiAddressAndCreateEnrichedDataResponse{
				ParsedNotificationDetails: parsedTxnParticulars,
				OrderWithTransaction:      orderTxnResponsePayload,
			},
			mockGetPiDetailsFromVendorByTxnId: mockGetPiDetailsFromVendorByTxnId{
				enable: true,
				req: &payPb.GetPiDetailsFromVendorByTxnIdRequest{
					TxnId: orderTxnPayload.GetTxnDetail().GetId(),
				},
				res: &payPb.GetPiDetailsFromVendorByTxnIdResponse{
					Status: rpc.StatusOk(),
					PayerPi: &piPb.PaymentInstrument{
						Id:   "payer-id",
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:      "vpa@fbl",
								Name:     "other-actor-name",
								IfscCode: "ABCD@1234",
							},
						},
						VerifiedName: "other-actor-name",
					},
				},
			},
		},
		{
			name: "GetPiDetailsFromVendorByTxnId RPC returned error",
			req: &parserActivityPkg.ValidateUpiAddressAndCreateEnrichedDataRequest{
				RequestHeader: &activity.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				OrderWithTransaction: orderTxnPayload,
			},
			res: nil,
			mockGetPiDetailsFromVendorByTxnId: mockGetPiDetailsFromVendorByTxnId{
				enable: true,
				req: &payPb.GetPiDetailsFromVendorByTxnIdRequest{
					TxnId: orderTxnPayload.GetTxnDetail().GetId(),
				},
				err: epifierrors.ErrTransient,
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "GetPiDetailsFromVendorByTxnId RPC returned non success response status",
			req: &parserActivityPkg.ValidateUpiAddressAndCreateEnrichedDataRequest{
				RequestHeader: &activity.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				OrderWithTransaction: orderTxnPayload,
			},
			res: nil,
			mockGetPiDetailsFromVendorByTxnId: mockGetPiDetailsFromVendorByTxnId{
				enable: true,
				req: &payPb.GetPiDetailsFromVendorByTxnIdRequest{
					TxnId: orderTxnPayload.GetTxnDetail().GetId(),
				},
				res: &payPb.GetPiDetailsFromVendorByTxnIdResponse{
					Status: rpc.StatusInternal(),
				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetPiDetailsFromVendorByTxnId.enable {
				md.payClient.EXPECT().GetPiDetailsFromVendorByTxnId(gomock.Any(), tt.mockGetPiDetailsFromVendorByTxnId.req).
					Return(tt.mockGetPiDetailsFromVendorByTxnId.res, tt.mockGetPiDetailsFromVendorByTxnId.err)
			}

			var result *parserActivityPkg.ValidateUpiAddressAndCreateEnrichedDataResponse
			got, err := env.ExecuteActivity(parserNs.ValidateUpiAddressAndCreateEnrichedData, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ValidateUpiAddressAndCreateEnrichedData() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("ValidateUpiAddressAndCreateEnrichedData() error = %v, wantErr %v", err, tt.wantErr)
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("ValidateUpiAddressAndCreateEnrichedData() error = %v, assertion failed", err)
				return
			case tt.res != nil && !proto.Equal(tt.res, result):
				t.Errorf("ValidateUpiAddressAndCreateEnrichedData() got = %v,\n want = %v", result, tt.res)
				return
			}
			assertTest()
		})
	}
}
