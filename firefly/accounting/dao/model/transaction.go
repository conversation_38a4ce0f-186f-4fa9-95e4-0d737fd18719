package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"

	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	ffAccEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/pagination"
)

type CreditCardTransaction struct {
	Id                             string `gorm:"primary_key"`
	AccountId                      string
	CardId                         string
	Amount                         *money.Money
	Balance                        *money.Money
	TxnTime                        time.Time
	TxnStatus                      ffAccEnumsPb.TransactionStatus
	TxnCategory                    ffAccEnumsPb.TransactionCategory
	TxnOrigin                      ffAccEnumsPb.TransactionOrigin
	TxnType                        ffAccEnumsPb.TransactionType
	BeneficiaryInfo                *ffAccPb.BeneficiaryInfo `gorm:"type:jsonb;default:'null'"`
	ConversionInfo                 *ffAccPb.ConversionInfo  `gorm:"type:jsonb;default:'null'"`
	Description                    string
	DisputeInfo                    *ffAccPb.DisputeInfo `gorm:"type:jsonb;default:'null'"`
	ExternalTxnId                  string
	BillRefNo                      string
	BankTxnId                      string
	AuthCode                       string
	AcquirerId                     string
	RetrievalReferenceNo           string
	SorTxnId                       string
	TxnReferenceNo                 string
	VendorExtTxnId                 string
	CreatedAt                      time.Time `gorm:"not null"`
	UpdatedAt                      time.Time `gorm:"not null"`
	DeletedAt                      gorm.DeletedAt
	FailureInfo                    *ffAccPb.FailureInfo `gorm:"type:jsonb;default:'null'"`
	ParentTransactionId            string
	ChildTransactionIds            *ChildTransactionIds
	DedupeId                       string
	TransactionAuthorizationStatus ffAccEnumsPb.TransactionAuthorizationStatus
}

type ChildTransactionIds struct {
	TxnIds []string
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (ot *ChildTransactionIds) Value() (driver.Value, error) {
	return json.Marshal(ot)
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (ot *ChildTransactionIds) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	err := json.Unmarshal(marshalledData, &ot)
	if err != nil {
		return err
	}
	return nil
}

type CreditCardTransactionModelRows []*CreditCardTransaction

func (t CreditCardTransactionModelRows) Slice(start, end int) pagination.Rows {
	return t[start:end]
}

func (t CreditCardTransactionModelRows) GetTimestamp(index int) time.Time {
	return t[index].TxnTime.In(datetime.IST)
}

func (t CreditCardTransactionModelRows) Size() int {
	return len(t)
}
