package onboarding

import (
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/firefly/workflow/stages"
	filiteFed "github.com/epifi/gamma/firefly/workflow/stages/fi_lite_onboarding/federal"
)

type DeviceRegistrationStageProvider struct {
}

func NewDeviceRegistrationStageProvider() *DeviceRegistrationStageProvider {
	return &DeviceRegistrationStageProvider{}
}

func (d *DeviceRegistrationStageProvider) GetStage(cardProgram *types.CardProgram, workflow ffEnumsPb.CardRequestWorkFlow) stages.Stage {
	switch {
	case cardProgram.GetCardProgramOrigin() == types.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE:
		return filiteFed.NewDeviceRegistration()
	default:
		return nil
	}
}
