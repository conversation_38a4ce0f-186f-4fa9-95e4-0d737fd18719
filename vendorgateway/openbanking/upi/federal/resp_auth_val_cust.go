package fedral

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"encoding/xml"
	"fmt"

	paymentPb "github.com/epifi/gamma/api/order/payment"

	"github.com/epifi/gamma/api/upi/mandate"

	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	upiPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	"github.com/epifi/gamma/api/vendors"
	"github.com/epifi/gamma/api/vendors/federal/upi"
	"github.com/epifi/gamma/api/vendors/federal/upi/mandate/notification"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/federal"
	"github.com/epifi/gamma/vendorgateway/openbanking/vendor_mapping"
)

// request object for response auth details
type RespAuthValCustRequest struct {
	defaultSigningTechnique
	*federal.DefaultHeaderAdder

	Method string
	Req    *upiPb.RespAuthValCustRequest
	Url    string
}

var (
	MandateReqTypeMap = map[mandate.MandateType]string{
		mandate.MandateType_CREATE:               "CREATE",
		mandate.MandateType_REVOKE:               "REVOKE",
		mandate.MandateType_UPDATE:               "UPDATE",
		mandate.MandateType_PAUSE:                "PAUSE",
		mandate.MandateType_UNPAUSE:              "UNPAUSE",
		mandate.MandateType_MANDATE_NOTIFICATION: "MandateNotification",
	}
)

// Marshal provides the xml for Federal bank's API call.
// nolint: dupl
func (v *RespAuthValCustRequest) Marshal() ([]byte, error) {
	var (
		req notification.RespAuthValCustRequest
		vpa string
		err error
	)

	vpa = v.Req.Payer.GetPaymentAddress()

	PopulateCommonNamespaceAttr(&req.XmlnsFed, &req.XmlnsUpi, &req.XmlnsXsi)
	err = PopulateMerchantHeaders(&req.MerchantHeader, vendors.ApiTypeRespAuthValCust, vpa)
	if err != nil {
		return nil, err
	}
	err = PopulateTransaction(&req.MerchantBody.RespAuthValCust.Txn, v.Req.TxnHeader, MandateReqTypeMap[v.Req.GetReqType()])
	if err != nil {
		return nil, err
	}

	err = PopulateMerchantBodyHead(&req.MerchantBody.RespAuthValCust.Head, v.Req.UpiVersion, vpa)
	if err != nil {
		return nil, err
	}
	req.MerchantBody.RespAuthValCust.Payer = GetCustomerV1(v.Req.GetPayer())
	req.MerchantBody.RespAuthValCust.Payee = GetCustomerV1(v.Req.GetPayee())
	req.MerchantBody.RespAuthValCust.Resp.ReqMsgId = v.Req.GetResp().GetReqMsgId()
	req.MerchantBody.RespAuthValCust.Resp.Result = v.Req.GetResp().GetResult().String()
	req.MerchantBody.RespAuthValCust.Resp.ErrCode = v.Req.GetResp().GetErrCode()
	req.XsiSchemalocation = federalSchemaLocation
	req.MerchantBody.RespAuthValCust.XmlnsNS1 = xmlNs1Attr
	execTimeStamp := v.Req.ExecTimestamp.AsTime().In(federal.Timezone)
	req.MerchantBody.RespAuthValCust.Txn.UmnExecTs = execTimeStamp.Format(vendors.UPINPCITimestampLayout)
	return xml.Marshal(req)
}

// HTTPMethod returns the http method to use for the API call.
func (v *RespAuthValCustRequest) HTTPMethod() string {
	return v.Method
}

// url to use for the api call to get token
func (v *RespAuthValCustRequest) URL() string {
	return v.Url
}

// return the content type of the request
func (v *RespAuthValCustRequest) ContentTypeString() string {
	return vendorapi.ContentTypeXML
}

type respAuthValCustResponse struct {
	txnType mandate.MandateType
}

// Unmarshal converts the response received from RespAuthValCustRequest API into
// RespAuthValCustResponse proto
// nolint: dupl
func (v *respAuthValCustResponse) Unmarshal(b []byte) (proto.Message, error) {
	var res upi.AckResponse
	var ackStatus rpc.StatusFactory
	err := xml.Unmarshal(b, &res)
	if err != nil {
		return nil, fmt.Errorf("could not parse XML response: %w", err)
	}

	// TODO(Ankit): Mapping here
	ackStatus = getAckStatus(&res, respAuthMandateAckErrorToStatusMap)

	rawErrorCode := getRawAckErrorCode(&res)
	ackStatusMapping := vendor_mapping.GetPayAckStatusMapping(commonvgpb.Vendor_FEDERAL_BANK, paymentPb.PaymentProtocol_UPI, rawErrorCode)
	return &upiPb.RespAuthValCustResponse{
		Status:                 ackStatus(),
		ReqMsgId:               res.ReqMsgId,
		RawStatusCode:          rawErrorCode,
		StatusCode:             ackStatusMapping.StatusCode,
		StatusDescriptionPayer: ackStatusMapping.StatusCodeDescriptionPayer,
		StatusDescriptionPayee: ackStatusMapping.StatusCodeDescriptionPayee,
	}, nil
}

// GetResponse returns Response struct that can deserialize the vendor response
func (v *RespAuthValCustRequest) GetResponse() vendorapi.Response {
	return &respAuthValCustResponse{txnType: v.Req.GetReqType()}
}
