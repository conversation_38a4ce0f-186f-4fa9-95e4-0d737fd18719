//nolint:dupl
package bridgewise

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	"github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	vendorBridgewise "github.com/epifi/gamma/api/vendors/catalog/bridgewise"
	"github.com/epifi/gamma/vendorgateway/config"
)

type GetFundParagraphsReq struct {
	Method string
	Req    *bridgewise.GetFundParagraphsRequest
	Conf   *config.Bridgewise
	AddTokenToRequestHeader
}

func (g *GetFundParagraphsReq) HTTPMethod() string {
	return g.Method
}

func (g *GetFundParagraphsReq) URL() string {
	url := fmt.Sprintf("%s/funds/%s/paragraphs", g.Conf.ApiHost, g.Req.GetFundId())
	return url
}

func (g *GetFundParagraphsReq) GetResponse() vendorapi.Response {
	return &GetFundParagraphsResp{}
}

func (g *GetFundParagraphsReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (g *GetFundParagraphsReq) Marshal() ([]byte, error) {
	return nil, nil
}

type GetFundParagraphsResp struct {
}

func (g *GetFundParagraphsResp) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
	// Bridgewise is returning 400 Bad Request  if the data is not found
	if httpStatus == http.StatusBadRequest {
		httpStatus = http.StatusNotFound
	}
	return &bridgewise.GetFundParagraphsResponse{
		Status: vendorapi.GetRpcStatusFromHttpCode(ctx, httpStatus),
	}, nil
}

func (g *GetFundParagraphsResp) Unmarshal(b []byte) (proto.Message, error) {
	var res []*vendorBridgewise.FundParagraphs
	err := json.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.Wrap(err, "failed to unmarshal FundParagraphs")
	}

	paragraphs := lo.FilterMap(res, func(item *vendorBridgewise.FundParagraphs, _ int) (*bridgewise.FundParagraphs, bool) {
		var paragraphType bridgewise.SecurityParagraphType
		switch item.GetParagraphType() {
		case "fund_description":
			paragraphType = bridgewise.SecurityParagraphType_SECURITY_PARAGRAPH_TYPE_DESCRIPTION
		default:
			return nil, false
		}
		return &bridgewise.FundParagraphs{
			AnalysisType:  item.GetAnalysisType(),
			SectionType:   item.GetSectionType(),
			ParagraphType: paragraphType,
			Paragraph:     item.GetParagraph(),
			Language:      item.GetLanguage(),
			UpdatedAt:     item.GetUpdatedAt(),
			Year:          item.GetYear(),
			Quarter:       item.GetQuarter(),
		}, true
	})

	return &bridgewise.GetFundParagraphsResponse{
		Status:     rpc.StatusOk(),
		Paragraphs: paragraphs,
	}, nil
}
