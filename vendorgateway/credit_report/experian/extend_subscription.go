package experian

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/vendorapi"
	creditReportVgPb "github.com/epifi/gamma/api/vendorgateway/credit_report"
	creditReportVendorPb "github.com/epifi/gamma/api/vendors/experian/credit_report"
	"github.com/epifi/gamma/vendorgateway/credit_report/tokenstore"
)

const (
	ExtendSubscriptionApiInvalidRequestError = "invalid subscription details"
)

type ExtendSubscriptionReq struct {
	Method     string
	Req        *creditReportVgPb.ExtendSubscriptionRequest
	Url        string
	ClientName string
}

type ExtendSubscriptionRes struct{}

type ExtendSubscriptionReqV1 struct {
	*ExtendSubscriptionReq
	TokenStoreManager *tokenstore.TokenStoreManager
	AuthToken         string
}

func (c *ExtendSubscriptionReqV1) Add(req *http.Request) *http.Request {
	req.Header.Set(authorizationString, bearerTokenString+" "+c.AuthToken)
	return req
}

func (c *ExtendSubscriptionReqV1) ContentTypeString() string {
	return httpcontentredactor.ContentTypeExperianText
}

func (c *ExtendSubscriptionReqV1) CustomRequestContentTypeHeaderString() string {
	return vendorapi.ContentTypeText
}

func (c *ExtendSubscriptionReqV1) GetResponseContentTypeString() string {
	return httpcontentredactor.ContentTypeExperianJSON
}

func (c *ExtendSubscriptionReqV1) Marshal() ([]byte, error) {
	experianToken, err := c.TokenStoreManager.GetExperianAuthToken(context.Background())
	if err != nil {
		return nil, fmt.Errorf("error in fetching auth token from experian: %w", err)
	}

	c.AuthToken = experianToken

	return c.ExtendSubscriptionReq.Marshal()
}

// Marshal provides the request for on demand fetch call.
func (c *ExtendSubscriptionReq) Marshal() ([]byte, error) {
	requestBody := fmt.Sprintf("clientName=%v"+
		"&stgOneHitId=%v"+
		"&mobileNo=%v",
		c.ClientName, c.Req.GetStageOneHitId(), c.Req.GetPhoneNumber())
	return []byte(requestBody), nil
}

func (c *ExtendSubscriptionReq) HTTPMethod() string {
	return c.Method
}

func (c *ExtendSubscriptionReq) URL() string {
	return c.Url
}

func (c *ExtendSubscriptionReq) GetResponse() vendorapi.Response {
	return &ExtendSubscriptionRes{}
}

func (c *ExtendSubscriptionReq) ContentTypeString() string {
	return httpcontentredactor.ContentTypeExperianText
}

func (c *ExtendSubscriptionRes) Unmarshal(b []byte) (proto.Message, error) {
	m := &creditReportVendorPb.ExtendSubscriptionResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(b, m)
	if err != nil {
		return &creditReportVgPb.ExtendSubscriptionResponse{Status: rpc.StatusInternalWithDebugMsg("error in unmarshal response")}, err
	}
	resp := c.getResponse(m)
	return resp, nil
}

func (c *ExtendSubscriptionRes) getResponse(m *creditReportVendorPb.ExtendSubscriptionResponse) *creditReportVgPb.ExtendSubscriptionResponse {
	resp := &creditReportVgPb.ExtendSubscriptionResponse{
		StageOneHitId: strconv.FormatInt(m.GetStgOneHitId(), 10),
		ErrorString:   m.GetErrorString(),
		ConsentStatus: m.GetConsentStatus(),
	}
	st := c.getRPCStatus(m)
	if !st.IsSuccess() {
		logger.ErrorNoCtx(fmt.Sprintf("received non OK response from vendor:%s", m.ErrorString))
	}
	resp.Status = st
	return resp
}

func (c *ExtendSubscriptionRes) getRPCStatus(resp *creditReportVendorPb.ExtendSubscriptionResponse) *rpc.Status {
	switch {
	case strings.ToLower(resp.GetErrorString()) == ExtendSubscriptionApiInvalidRequestError:
		return rpc.StatusInvalidArgument()
	case resp.GetErrorString() != "":
		return rpc.StatusInternal()
	default:
		return rpc.StatusOk()
	}
}

func (c *ExtendSubscriptionReq) CustomRequestContentTypeHeaderString() string {
	return vendorapi.ContentTypeText
}
