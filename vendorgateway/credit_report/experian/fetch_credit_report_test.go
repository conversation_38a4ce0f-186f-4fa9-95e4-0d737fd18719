package experian

import (
	"testing"

	"google.golang.org/protobuf/proto" // nolint: depguard

	"github.com/epifi/be-common/api/rpc"
	creditReportVgPb "github.com/epifi/gamma/api/vendorgateway/credit_report"
	creditReportVendorPb "github.com/epifi/gamma/api/vendors/experian/credit_report"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
)

func TestFetchCreditReportResGetRPCStatus(t *testing.T) {
	logger.Init(cfg.TestEnv)
	testFetchCreditReportRes := &FetchCreditReportRes{}
	tests := []struct {
		name                string
		fetchReportResponse *creditReportVendorPb.FetchReportResponse
		want                *rpc.Status
	}{
		{
			name: "Got error - consumer record not found",
			fetchReportResponse: &creditReportVendorPb.FetchReportResponse{
				ErrorString: "consumer record not found",
			},
			want: rpc.StatusRecordNotFound(),
		},
		{
			name: "Got error - SYS100009 (We are unable to deliver your report through this channel due to a very large number of accounts)",
			fetchReportResponse: &creditReportVendorPb.FetchReportResponse{
				ErrorString: "SYS100009 (We are unable to deliver your report through this channel due to a very large number of accounts)",
			},
			want: rpc.NewStatusWithoutDebug(uint32(creditReportVgPb.FetchReportResponse_REPORT_CANNOT_BE_EXPORTED_BY_VENDOR),
				"SYS100009 (We are unable to deliver your report through this channel due to a very large number of accounts)"),
		},
		{
			name: "Got unknown error",
			fetchReportResponse: &creditReportVendorPb.FetchReportResponse{
				ErrorString: "random",
			},
			want: rpc.NewStatusWithoutDebug(uint32(creditReportVgPb.FetchReportResponse_REPORT_CANNOT_BE_EXPORTED_BY_VENDOR),
				"random"),
		},
		{
			name: "No error",
			fetchReportResponse: &creditReportVendorPb.FetchReportResponse{
				ErrorString: "",
			},
			want: rpc.StatusOk(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := testFetchCreditReportRes.getRPCStatus(tt.fetchReportResponse)
			want := tt.want
			if !proto.Equal(got, want) {
				t.Errorf("getRPCStatus() got = %v, want %v", got, want)
			}
		})
	}
}
