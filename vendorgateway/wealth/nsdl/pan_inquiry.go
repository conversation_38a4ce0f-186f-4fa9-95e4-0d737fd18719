package nsdl

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	nsdlVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/nsdl"
	nsdl "github.com/epifi/gamma/api/vendors/wealth"
	"github.com/epifi/be-common/pkg/vendorapi"
)

var (
	errcodeMap = map[string]string{
		"2":  "System Error",
		"3":  "Authentication Failure",
		"4":  "User not authorized",
		"5":  "No PANs Entered",
		"6":  "User validity has expired",
		"7":  "Number of PANs exceeds the limit",
		"8":  "Not enough balance",
		"9":  "Not an HTTPs request",
		"10": "POST method not used",
		"11": "Slab change running",
		"12": "Invalid version number",
	}
	panStatusMap = map[string]nsdl.NsdlPanStatus{
		"E":  nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_EXISTING_AND_VALID,
		"F":  nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_MARKED_AS_FAKE,
		"X":  nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_MARKED_AS_DEACTIVATED,
		"D":  nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_DELETED,
		"N":  nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_INVALID_PAN,
		"EA": nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_EXISTING_MARKED_AMALGAMATION,
		"EC": nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_EXISTING_MARKED_ACQUISITION,
		"ED": nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_EXISTING_MARKED_DEATH,
		"EI": nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_EXISTING_MARKED_DISSOLUTION,
		"EL": nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_EXISTING_MARKED_LIQUIDATED,
		"EM": nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_EXISTING_MARKED_MERGER,
		"EP": nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_EXISTING_MARKED_PARTITION,
		"ES": nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_EXISTING_MARKED_SPLIT,
		"EU": nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_EXISTING_MARKED_UNDER_LIQUIDATION,
		"I":  nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_INOPERATIVE,
	}
)

type panInquiryReq struct {
	method  string
	req     *nsdlVgPb.NsdlPanInquiryRequest
	url     string
	userId  string
	version string
}

func (n *panInquiryReq) Marshal() ([]byte, error) {
	return nil, nil
}

func (n *panInquiryReq) URL() string {
	data := n.userId + "^" + n.req.GetPanNumber()
	resultUrl := n.url + "?" + getUrlEncodedParams(data, n.req.GetSignature(), n.version)
	return resultUrl
}

func getUrlEncodedParams(data, digitalSignature, version string) string {
	params := url.Values{}
	params.Add("data", data)
	params.Add("signature", digitalSignature)
	params.Add("version", version)
	return params.Encode()
}

func (n *panInquiryReq) HTTPMethod() string {
	return n.method
}

func (n *panInquiryReq) Add(req *http.Request) *http.Request {
	return req
}

func (n *panInquiryReq) GetResponse() vendorapi.Response {
	return &panInquiryResp{}
}

func (n *panInquiryReq) ContentTypeString() string {
	return vendorapi.ContentTypeText
}

type panInquiryResp struct{}

func (n *panInquiryResp) Unmarshal(b []byte) (proto.Message, error) {
	rawResponseString := strings.TrimSpace(string(b))
	rawResponse := strings.Split(rawResponseString, "^")
	if len(rawResponse) == 0 {
		return &nsdlVgPb.NsdlPanInquiryResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while splitting nsdl pan inquiry response, len is zero"),
		}, nil
	}
	statusCode := rawResponse[0]
	if isErrorCode(statusCode) {
		errMsg, ok := errcodeMap[statusCode]
		if !ok {
			errMsg = "error code unspecified = " + statusCode
		}
		return &nsdlVgPb.NsdlPanInquiryResponse{
			Status: rpcPb.StatusInternal(),
			ErrMsg: errMsg,
		}, nil
	}
	return &nsdlVgPb.NsdlPanInquiryResponse{
		Status:        rpcPb.StatusOk(),
		AadhaarStatus: getNsdlAadhaarStatus(rawResponse),
		PanStatus:     getNsdlPanStatus(rawResponse),
		Name: &commontypes.Name{
			FirstName:  getNsdlString(rawResponse, 4),
			MiddleName: getNsdlString(rawResponse, 5),
			LastName:   getNsdlString(rawResponse, 3),
			Honorific:  getNsdlString(rawResponse, 6),
		},
		LastUpdatedDate: getNsdlString(rawResponse, 7),
		PanCardName:     getNsdlString(rawResponse, 8),
	}, nil
}

func (n *panInquiryResp) HandleHttpError(_ context.Context, httpStatus int, b []byte) (proto.Message, error) {
	return &nsdlVgPb.NsdlPanInquiryResponse{
		Status: rpcPb.StatusInternalWithDebugMsg(fmt.Sprintf("error in panInquiryReq, http_status = %v, response = %v", httpStatus, string(b))),
	}, nil
}

func getNsdlString(response []string, position int) string {
	if len(response) < position+1 {
		return ""
	}
	return response[position]
}

// getNsdlPanStatus returns the enum specified from the string value
func getNsdlPanStatus(response []string) nsdl.NsdlPanStatus {
	if len(response) < 3 {
		return nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_UNSPECIFIED
	}
	status, ok := panStatusMap[response[2]]
	if !ok {
		return nsdl.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_UNSPECIFIED
	}
	return status
}

// getNsdlAadhaarStatus returns the enum specified from the string value
func getNsdlAadhaarStatus(response []string) nsdl.NsdlAadhaarStatus {
	if len(response) < 10 {
		return nsdl.NsdlAadhaarStatus_NSDL_AADHAAR_STATUS_TYPE_UNSPECIFIED
	}
	return getAadhaarStatus(response[9])
}

func getAadhaarStatus(statusStr string) nsdl.NsdlAadhaarStatus {
	switch statusStr {
	case "Y":
		return nsdl.NsdlAadhaarStatus_NSDL_AADHAAR_STATUS_TYPE_SUCCESSFUL
	case "R":
		return nsdl.NsdlAadhaarStatus_NSDL_AADHAAR_STATUS_TYPE_UNSUCCESSFUL
	case "NA":
		return nsdl.NsdlAadhaarStatus_NSDL_AADHAAR_STATUS_TYPE_NA
	case "":
		return nsdl.NsdlAadhaarStatus_NSDL_AADHAAR_STATUS_TYPE_NO_VALUE
	default:
		return nsdl.NsdlAadhaarStatus_NSDL_AADHAAR_STATUS_TYPE_UNSPECIFIED
	}
}

func isErrorCode(statusCode string) bool {
	return statusCode != "1"
}
