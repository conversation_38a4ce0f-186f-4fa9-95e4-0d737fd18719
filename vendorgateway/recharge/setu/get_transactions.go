// nolint:dupl
package setu

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commonPb "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/vendorapi"

	rechargeVgPb "github.com/epifi/gamma/api/vendorgateway/recharge"
	setuRechargePb "github.com/epifi/gamma/api/vendors/setu/recharge"
	"github.com/epifi/gamma/vendorgateway/config/genconf"
)

type GetTransactionsRequest struct {
	Conf              *genconf.Config
	ProductInstanceId string
	BaseUrl           string
	EndpointUri       string
	Method            string
	Req               *rechargeVgPb.GetTransactionsRequest
	ctx               context.Context
	AccessToken       string
}

func (p *GetTransactionsRequest) Marshal() ([]byte, error) {
	// Build query params as per Setu API
	// The vendor expects query params, not a JSON body, so we return nil here
	return nil, nil
}

func (p *GetTransactionsRequest) HTTPMethod() string {
	return http.MethodGet
}

func (p *GetTransactionsRequest) URL() string {
	// Build the URL with query params
	params := "?"
	if p.Req.GetLimit() > 0 {
		params += fmt.Sprintf("limit=%d&", p.Req.GetLimit())
	}
	if p.Req.GetPageNumber() > 0 {
		params += fmt.Sprintf("page_number=%d&", p.Req.GetPageNumber())
	}
	if p.Req.GetOperator() != rechargeVgPb.Operator_OPERATOR_UNSPECIFIED {
		params += fmt.Sprintf("operator=%s&", p.Req.GetOperator().ToVendorString())
	}
	if p.Req.GetStartDate() != nil {
		startDateStr := datetime.DateToTimeV2(p.Req.GetStartDate(), datetime.IST).Format(time.DateOnly)
		params += fmt.Sprintf("start_date=%s&", startDateStr)
	}
	if p.Req.GetEndDate() != nil {
		endDateStr := datetime.DateToTimeV2(p.Req.GetEndDate(), datetime.IST).Format(time.DateOnly)
		params += fmt.Sprintf("end_date=%s&", endDateStr)
	}
	if params[len(params)-1] == '&' {
		params = params[:len(params)-1]
	}
	return fmt.Sprintf("%s/%s%s", p.BaseUrl, p.EndpointUri, params)
}

func (p *GetTransactionsRequest) Add(req *http.Request) *http.Request {
	req.Header.Add("X-PRODUCT-INSTANCE-ID", p.ProductInstanceId)
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", p.AccessToken))
	req.Header.Add("Accept", "*/*")
	req.Header.Add("Content-Type", "application/json")
	return req
}

func (p *GetTransactionsRequest) GetResponse() vendorapi.Response {
	return &GetTransactionsResponse{ctx: p.ctx}
}

type GetTransactionsResponse struct {
	ctx context.Context
}

func (p *GetTransactionsResponse) Unmarshal(b []byte) (proto.Message, error) {
	resp := &setuRechargePb.FetchTransactionsPaginatedResponse{}
	unmarshaller := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if errUnmarshal := unmarshaller.Unmarshal(b, resp); errUnmarshal != nil {
		return &rechargeVgPb.GetTransactionsResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in unmarshalling get transactions response : %v", errUnmarshal)),
		}, nil
	}

	if resp.GetError() != nil {
		return p.handleErrorMessage(resp)
	}

	if resp.GetSuccess() && resp.GetData() != nil {
		var transactions []*rechargeVgPb.Transaction
		for _, txn := range resp.GetData().GetTransactions() {
			txnTimestamp, parseErr := time.Parse(time.RFC3339, txn.GetTimestamp())
			if parseErr != nil {
				return &rechargeVgPb.GetTransactionsResponse{
					Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error parsing timestamp: %v", parseErr)),
				}, nil
			}

			mobileNumber, _ := commonPb.ParsePhoneNumber(txn.GetMobileNumber())
			transactions = append(transactions, &rechargeVgPb.Transaction{
				MobileNumber: mobileNumber,
				Operator:     rechargeVgPb.GetOperatorEnumForVendorString(txn.GetOperator()),
				Location:     txn.GetLocation(),
				Amount: &gmoney.Money{
					CurrencyCode: moneyPkg.RupeeCurrencyCode,
					Units:        txn.GetAmount(),
				},
				Status:                      rechargeVgPb.GetRechargeStatusEnumFromVendorString(txn.GetStatus()),
				Timestamp:                   timestampPb.New(txnTimestamp),
				PaymentId:                   txn.GetPaymentId(),
				VendorSystemReferenceNumber: txn.GetVendorSystemReferenceNumber(),
				VendorTxnReference:          txn.GetVendorTxnReference(),
				PaymentMode:                 rechargeVgPb.GetPaymentModeEnumForVendorString(txn.GetPaymentMode()),
			})
		}

		totalRecords, pageNumber, limit, generatedAt, err := p.parseMetadata(resp.GetData().GetMetadata())
		if err != nil {
			return &rechargeVgPb.GetTransactionsResponse{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error parsing metadata: %v", err)),
			}, nil
		}

		return &rechargeVgPb.GetTransactionsResponse{
			Status:       rpc.StatusOk(),
			Success:      resp.GetSuccess(),
			Transactions: transactions,
			TotalRecords: totalRecords,
			PageNumber:   pageNumber,
			Limit:        limit,
			GenereatedAt: generatedAt,
			TraceId:      resp.GetTraceId(),
		}, nil
	}

	return &rechargeVgPb.GetTransactionsResponse{
		Status: rpc.StatusUnknown(),
	}, nil
}

func (p *GetTransactionsResponse) parseMetadata(metadata *setuRechargePb.Metadata) (totalRecords, pageNumber, limit int64, generatedAt *timestampPb.Timestamp, err error) {
	var parseErr error
	totalRecords, parseErr = strconv.ParseInt(metadata.GetTotalRecords(), 10, 64)
	if parseErr != nil {
		return 0, 0, 0, nil, fmt.Errorf("error parsing total records: %v", parseErr)
	}

	pageNumber, parseErr = strconv.ParseInt(metadata.GetPageNumber(), 10, 64)
	if parseErr != nil {
		return 0, 0, 0, nil, fmt.Errorf("error parsing page number: %v", parseErr)
	}

	limit, parseErr = strconv.ParseInt(metadata.GetLimit(), 10, 64)
	if parseErr != nil {
		return 0, 0, 0, nil, fmt.Errorf("error parsing limit: %v", parseErr)
	}

	var generatedAtTime time.Time
	generatedAtTime, parseErr = time.Parse("2006-01-02T15:04:05.999999", metadata.GetGeneratedAt())
	if parseErr != nil {
		return 0, 0, 0, nil, fmt.Errorf("error parsing generated at timestamp: %v", parseErr)
	}
	generatedAt = timestampPb.New(generatedAtTime)

	return totalRecords, pageNumber, limit, generatedAt, nil
}

func (p *GetTransactionsResponse) HandleHttpError(_ context.Context, httpStatus int, b []byte) (proto.Message, error) {
	if len(b) == 0 || httpStatus == http.StatusServiceUnavailable {
		return &rechargeVgPb.GetTransactionsResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("%d", httpStatus)),
		}, nil
	}

	resp := &setuRechargePb.FetchTransactionsPaginatedResponse{}
	unmarshaller := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if errUnmarshal := unmarshaller.Unmarshal(b, resp); errUnmarshal != nil {
		return &rechargeVgPb.GetTransactionsResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in unmarshalling get transactions response : %v", errUnmarshal)),
		}, nil
	}
	return p.handleErrorMessage(resp)
}

// nolint:unparam
func (p *GetTransactionsResponse) handleErrorMessage(resp *setuRechargePb.FetchTransactionsPaginatedResponse) (*rechargeVgPb.GetTransactionsResponse, error) {
	vgResp := &rechargeVgPb.GetTransactionsResponse{
		Success: false,
		TraceId: resp.GetTraceId(),
	}
	switch resp.GetError().GetCode() {
	case InvalidInputParameter:
		vgResp.Status = rpc.StatusInvalidArgumentWithDebugMsg(resp.GetError().GetCode() + "-" + resp.GetError().GetFailureReason() + "-" + resp.GetError().GetMessage())
	case InvalidRequest, InvalidProductInstanceId, ApiConnectivityError, InternalServerError:
		vgResp.Status = rpc.StatusInternalWithDebugMsg(resp.GetError().GetCode() + "-" + resp.GetError().GetFailureReason() + "-" + resp.GetError().GetMessage())
	default:
		vgResp.Status = rpc.StatusUnknownWithDebugMsg(resp.GetError().GetCode() + "-" + resp.GetError().GetFailureReason() + "-" + resp.GetError().GetMessage())
	}
	return vgResp, nil
}
