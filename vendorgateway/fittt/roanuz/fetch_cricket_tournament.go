package roanuz

import (
	"fmt"
	"net/http"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	pb "github.com/epifi/gamma/api/vendorgateway/fittt"
	vendorspb "github.com/epifi/gamma/api/vendors/roanuz/cricket"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
)

type FetchCricketTournamentReq struct {
	Method     string
	Req        *pb.FetchCricketTournamentRequest
	Url        string
	ProjectKey string
}

func (f *FetchCricketTournamentReq) Marshal() ([]byte, error) {
	return nil, nil
}

func (f *FetchCricketTournamentReq) HTTPMethod() string {
	return f.Method
}

func (f *FetchCricketTournamentReq) URL() string {
	return fmt.Sprintf("%v/%v/%v/%v/", f.Url, f.ProjectKey, "tournament", f.Req.TournamentKey)
}

func (f *FetchCricketTournamentReq) GetResponse() vendorapi.Response {
	return &FetchCricketTournamentFixturesRes{}
}

func (f *FetchCricketTournamentReq) Add(req *http.Request) *http.Request {
	req.Header.Add("rs-token", f.Req.AccessToken)
	return req
}

// nolint: dupl
func (g *FetchCricketTournamentReq) Unmarshal(b []byte) (proto.Message, error) {
	unmarshaller := protojson.UnmarshalOptions{AllowPartial: true, DiscardUnknown: true}
	apiResp := &vendorspb.TournamentResponse{}
	logger.DebugNoCtx("Raw json response form vendor for FetchCricketTournament", zap.String("response", string(b)))
	if err := unmarshaller.Unmarshal(b, apiResp); err != nil {
		logger.ErrorNoCtx("error in unmarshalling FetchCricketTournament response for roanuz api", zap.Error(err))
		return nil, errors.Wrap(err, "error in unmarshalling FetchCricketTournament response for roanuz api")
	}
	logger.DebugNoCtx("Unmarshalled vendor response", zap.String("response", apiResp.String()))
	resp := &pb.FetchCricketTournamentResponse{}

	if apiResp.GetError() != nil {
		logger.ErrorNoCtx("error in FetchCricketTournament", zap.Error(fmt.Errorf("error in FetchCricketTournament details %v",
			apiResp.GetError().GetMessage())))
		resp.Status = rpc.StatusInternalWithDebugMsg(apiResp.GetError().GetMessage())
		return resp, nil
	}
	resp.Tournament = getCricketTournament(apiResp.GetData().GetTournament(), apiResp.GetData().GetTeams())
	resp.Status = rpc.StatusOk()
	return resp, nil
}

func getCricketTournament(t *vendorspb.Tournament, teams map[string]*vendorspb.Team) *pb.Tournament {

	vgTournament := &pb.Tournament{
		Key:       t.GetKey(),
		Name:      t.GetName(),
		ShortName: t.GetShortName(),
		StartDate: timestamppb.New(time.Unix(int64(t.GetStartDate()), 0)),
		EndDate:   timestamppb.New(time.Unix(int64(t.GetEndDate()), 0)),
		Teams:     getCricketTeams(teams),
	}

	return vgTournament
}

func getCricketTeams(vTeams map[string]*vendorspb.Team) (teams []*pb.Team) {
	for _, t := range vTeams {
		teams = append(teams, &pb.Team{
			TeamId: t.GetTeamId(),
			Code:   t.GetCode(),
			Name:   t.GetName(),
		})
	}
	return teams
}
