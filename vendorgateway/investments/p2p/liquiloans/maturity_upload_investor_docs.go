package liquiloans

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"

	"google.golang.org/protobuf/proto" //nolint:depguard

	"github.com/epifi/be-common/api/rpc"
	p2pPb "github.com/epifi/gamma/api/vendorgateway/investments/p2p"
	liquiloansPb "github.com/epifi/gamma/api/vendors/liquiloans"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
)

var (
	transactionAlreadyMaturedErrorMsg = "Maturity Consent document not upload because transaction is already matured"
)

type MaturityUploadInvestorDocsReq struct {
	Method      string
	Req         *p2pPb.UploadMaturityInvestorDocsRequest
	Conf        *config.Liquiloans
	contentType string
}

// nolint:dupl
func (r *MaturityUploadInvestorDocsReq) Marshal() ([]byte, error) {
	documentType, err := getDocumentType(r.Req.GetDocumentType())
	if err != nil {
		return nil, err
	}

	timestamp := time.Now().In(datetime.IST).Format(dateTimeLayout)
	checksumDataString := fmt.Sprintf("%v||%v||%v", documentType, r.Req.GetInvestorId(), timestamp)

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	if err = writer.WriteField("MID", r.Conf.Mid); err != nil {
		return nil, err
	}
	if err = writer.WriteField("Timestamp", timestamp); err != nil {
		return nil, err
	}
	if err = writer.WriteField("InvestorId", r.Req.GetInvestorId()); err != nil {
		return nil, err
	}
	if err = writer.WriteField("InvestmentId", r.Req.GetInvestmentId()); err != nil {
		return nil, err
	}
	if err = writer.WriteField("DocumentType", documentType); err != nil {
		return nil, err
	}
	if err = writer.WriteField("Checksum", calculateChecksum(checksumDataString, r.Conf.Key)); err != nil {
		return nil, err
	}
	fW, err := writer.CreateFormFile("File", "maturityConsent.pdf")
	if err != nil {
		return nil, err
	}
	if _, err = fW.Write(r.Req.GetFile()); err != nil {
		return nil, err
	}

	if err = writer.Close(); err != nil {
		return nil, err
	}
	r.contentType = writer.FormDataContentType()
	return body.Bytes(), nil
}

func (r *MaturityUploadInvestorDocsReq) URL() string {
	return fmt.Sprintf("%v/MaturityUploadInvestorDocs", r.Conf.SupplyIntegrationHost)
}

func (r *MaturityUploadInvestorDocsReq) HTTPMethod() string {
	return r.Method
}

func (r *MaturityUploadInvestorDocsReq) ContentTypeString() string {
	return r.contentType
}

func (r *MaturityUploadInvestorDocsReq) GetResponse() vendorapi.Response {
	return &maturityUploadInvestorDocsRes{}
}

func (r *MaturityUploadInvestorDocsReq) CanLogUnredactedEncryptedPayload() bool {
	return true
}

type maturityUploadInvestorDocsRes struct {
}

// MaturityUploadInvestorDocsPartialRes is used to unmarshal only the non-data fields
// for special handling of error codes and messages
// This is because, in case of errors, LiquiLoans may not conform to the usual type of the data field in response
type MaturityUploadInvestorDocsPartialRes struct {
	Status  bool   `json:"status"`
	Message string `json:"message"`
	Code    int64  `json:"code"`
}

func (r *maturityUploadInvestorDocsRes) Unmarshal(b []byte) (proto.Message, error) {
	partialRes := &MaturityUploadInvestorDocsPartialRes{}
	err := json.Unmarshal(b, partialRes)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling response partially")
	}
	switch partialRes.Code {
	case 400:
		if strings.Contains(strings.ToLower(partialRes.Message), strings.ToLower(transactionAlreadyMaturedErrorMsg)) {
			return &p2pPb.UploadMaturityInvestorDocsResponse{
				Status:        rpc.NewStatus(uint32(p2pPb.UploadMaturityInvestorDocsResponse_TRANSACTION_ALREADY_MATURED), "transaction already matured", ""),
				RawStatusCode: strconv.Itoa(int(partialRes.Code)),
				RawMessage:    partialRes.Message,
			}, nil
		}
		return &p2pPb.UploadMaturityInvestorDocsResponse{
			Status:        rpc.StatusInternal(),
			RawStatusCode: strconv.Itoa(int(partialRes.Code)),
			RawMessage:    partialRes.Message,
		}, nil
	default:
		res := &liquiloansPb.MaturityUploadInvestorDocsResponse{}
		err = protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, res)
		if err != nil {
			return nil, errors.Wrap(err, "error unmarshalling response completely")
		}
		return &p2pPb.UploadMaturityInvestorDocsResponse{
			Status:        rpc.StatusOk(),
			RawStatusCode: strconv.Itoa(int(res.GetCode())),
			RawMessage:    res.GetMessage(),
		}, nil
	}
}

func (r *maturityUploadInvestorDocsRes) HandleHttpError(_ context.Context, httpStatus int, _ []byte) (proto.Message, error) {
	return nil, fmt.Errorf("http error, http_status = %v", httpStatus)
}

func (r *maturityUploadInvestorDocsRes) CanLogUnredactedEncryptedPayload() bool {
	return true
}

func (r *maturityUploadInvestorDocsRes) GetResponseContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (r *maturityUploadInvestorDocsRes) RedactResponseBody(_ context.Context, responseBody []byte, _ string) ([]byte, error) {
	// no fields in response contain PII
	return responseBody, nil
}
