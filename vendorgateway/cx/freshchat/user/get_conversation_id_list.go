package user

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	freshchatPb "github.com/epifi/gamma/api/vendorgateway/cx/freshchat"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
)

type GetAllConversationIdsForUserReq struct {
	Method string
	Req    *freshchatPb.GetAllConversationIdsForUserRequest
	Url    string
	ApiKey string
}

// Marshal provides the json for get freshchat conversation request call.
func (r GetAllConversationIdsForUserReq) Marshal() ([]byte, error) {
	req := freshchatPb.GetAllConversationIdsForUserRequest{}
	requestJson, err := protojson.Marshal(&req)
	return requestJson, err
}

// URL provides the URL to send the request to
func (r GetAllConversationIdsForUserReq) URL() string {
	return fmt.Sprintf("%v/%v/conversations", r.Url, r.Req.GetUserId())
}

// HTTPMethod returns the http method to use for the API call.
func (r GetAllConversationIdsForUserReq) HTTPMethod() string {
	return r.Method
}

func (s GetAllConversationIdsForUserReq) Add(req *http.Request) *http.Request {
	// Create a Bearer string by appending string access token
	var bearer = "Bearer " + s.ApiKey
	req.Header.Add("Authorization", bearer)
	return req
}

type GetAllConversationIdsForUserResp struct{}

// NewResponse returns Response struct that can deserialize the vendor response
func (r GetAllConversationIdsForUserReq) GetResponse() vendorapi.Response {
	return GetAllConversationIdsForUserResp{}
}

// nolint:dupl
func (r GetAllConversationIdsForUserResp) Unmarshal(b []byte) (proto.Message, error) {
	var resp freshchatPb.GetFreshchatConvForUserResponse
	unmarshaler := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if err := unmarshaler.Unmarshal(b, &resp); err != nil {
		logger.ErrorNoCtx("Error in getting user from freshchat, failed to unmarshal response",
			zap.Any("error", err.Error()),
		)
		return &freshchatPb.GetUserResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	var idList []string
	for _, conv := range resp.GetConversations() {
		idList = append(idList, conv.GetId())
	}
	return &freshchatPb.GetAllConversationIdsForUserResponse{
		Status:             rpc.StatusOk(),
		ConversationIdList: idList,
	}, nil
}

func (g GetAllConversationIdsForUserResp) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	type ErrorResponse struct {
		Code    string
		Message string
	}
	errorResponse := &ErrorResponse{}
	if err := json.Unmarshal(b, errorResponse); err != nil {
		return &freshchatPb.GetAllConversationIdsForUserResponse{
			Status: vendorapi.GetRpcStatusFromHttpCode(ctx, httpStatus),
		}, nil
	}
	return &freshchatPb.GetAllConversationIdsForUserResponse{
		Status: vendorapi.GetRpcStatusFromHttpCodeWithDebugMsg(
			ctx,
			httpStatus,
			fmt.Sprintf("ShortMessage: %s, DebugMessage: %s", errorResponse.Code, errorResponse.Message),
		),
	}, nil
}
