package lenden

import (
	"context"
	"fmt"
	"net/http"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	pkgMoney "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/vendorapi"
	lendenPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	vendorLendenPb "github.com/epifi/gamma/api/vendors/lenden"
	"github.com/epifi/gamma/vendorgateway/config"
)

// SelectOfferRequest represents the request structure for selecting an offer.
type SelectOfferRequest struct {
	Req  *lendenPb.SelectOfferRequest
	Conf *config.Lenden
	Cryptor
	LogRedactor
}

// SelectOfferResponse represents the response structure for selecting an offer.
type SelectOfferResponse struct {
	Conf *config.Lenden
	Cryptor
	LogRedactor
}

func (c *SelectOfferRequest) URL() string {
	return c.Conf.BaseUrl
}

func (c *SelectOfferRequest) HTTPMethod() string {
	return http.MethodPost
}

func (c *SelectOfferRequest) GetResponse() vendorapi.Response {
	return &SelectOfferResponse{
		Conf:        c.Conf,
		Cryptor:     c.Cryptor,
		LogRedactor: c.LogRedactor,
	}
}

func (c *SelectOfferRequest) Marshal() ([]byte, error) {
	if c.Req.GetSelectedAmount().GetCurrencyCode() != pkgMoney.RupeeCurrencyCode {
		return nil, fmt.Errorf("currency code is not INR")
	}

	requestPayload := &vendorLendenPb.SelectOfferRequest{
		Params: &vendorLendenPb.Params{
			// Required To be passed empty, as it's a mandatory field from LDC
		},
		Fields: &vendorLendenPb.Fields{
			// Required To be passed empty, as it's a mandatory field from LDC
		},
		// TODO(Brijesh): Check if validations on selected amount and tenure are necessary
		Json: &vendorLendenPb.SelectOfferRequestPayload{
			ProductId:      c.Conf.ProductId,
			LoanId:         c.Req.GetLoanId(),
			UserId:         c.Req.GetUserId(),
			SelectedOffer:  c.Req.GetSelectedOfferId(),
			SelectedAmount: c.Req.GetSelectedAmount().GetUnits(),
			Tenure:         c.Req.GetTenure(),
		},
		Attributes: &vendorLendenPb.Attributes{
			Authorization: c.Conf.Authorization,
		},
		ApiCode: string(InternalApiCodeSelectOffer),
	}

	// Step 2: Marshal the VendorRequest into JSON
	vendorReqBytes, err := protojson.Marshal(requestPayload)
	if err != nil {
		return nil, fmt.Errorf("error marshaling vendor request: %w", err)
	}

	// Step 3: Encrypt the JSON request
	encryptedReqBytes, err := c.Cryptor.Encrypt(vendorReqBytes)
	if err != nil {
		return nil, fmt.Errorf("error encrypting vendor request: %w", err)
	}

	// Step 4: Return the encrypted bytes
	return encryptedReqBytes, nil
}

//nolint:dupl
func (c *SelectOfferResponse) Unmarshal(b []byte) (proto.Message, error) {
	unmarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	encryptedResponse := vendorLendenPb.LendenEncryptedResponse{}

	if err := unmarshaller.Unmarshal(b, &encryptedResponse); err != nil {
		return nil, fmt.Errorf("error unmarshalling response body: %w", err)
	}
	encryptedResponsePayload := encryptedResponse.GetResponse().GetPayload()
	if encryptedResponsePayload == "" {
		return nil, errors.New("empty response received from vendor")
	}

	decryptedBytes, err := c.Cryptor.Decrypt([]byte(encryptedResponsePayload))
	if err != nil {
		return nil, fmt.Errorf("error decrypting response: %w", err)
	}

	wrappedRes := vendorLendenPb.SelectOfferResponseWrapper{}
	if err = unmarshaller.Unmarshal(decryptedBytes, &wrappedRes); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %w", err)
	}
	return &lendenPb.SelectOfferResponse{
		Status:    rpc.StatusOk(),
		OfferCode: wrappedRes.GetResponse().GetResponseData().GetOfferCode(),
	}, nil
}

func (c *SelectOfferResponse) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "http error in lenden select offer API", zap.String(logger.PAYLOAD, string(b)), zap.Any(logger.HTTP_STATUS, httpStatus))
	if httpStatus >= 400 && httpStatus < 500 {
		wrappedRes := &vendorLendenPb.SelectOfferResponseWrapper{}
		err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, wrappedRes)
		if err != nil {
			return nil, errors.Wrap(err, "error unmarshalling response")
		}
		switch wrappedRes.GetResponse().GetMessageCode() {
		case MessageCodeFieldAlreadyPresent.String():
			return &lendenPb.SelectOfferResponse{
				Status:    rpc.StatusAlreadyExistsWithDebugMsg(wrappedRes.GetResponse().GetMessage()),
				OfferCode: wrappedRes.GetResponse().GetResponseData().GetSelectedOffer(),
			}, nil
		default:
			return &lendenPb.SelectOfferResponse{Status: rpc.StatusInternalWithDebugMsg(wrappedRes.GetResponse().GetMessage())}, nil
		}
	}
	return &lendenPb.SelectOfferResponse{
		Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error response: %s", string(b))),
	}, nil
}
