// nolint:dupl
package impl

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/vendorapi"

	vgPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	vendorPb "github.com/epifi/gamma/api/vendors/fiftyfin"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/fiftyfin"
)

var getLoanSoaStatementStatusMapping = map[string]rpc.StatusFactory{
	"User not found":                     rpc.StatusFailedPrecondition,
	"SOA Statement fetched successfully": rpc.StatusOk,
	"SOA Statement not found":            rpc.StatusRecordNotFound,
	"Loan already closed":                newStatusFactory(uint32(vgPb.GetLoanSoaStatementResponse_LOAN_ACCOUNT_CLOSED)),
	"Loan Process Not Completed":         newStatusFactory(uint32(vgPb.GetLoanSoaStatementResponse_LOAN_PROCESS_NOT_COMPLETED)),
	"Loan Pre Closure Pending":           newStatusFactory(uint32(vgPb.GetLoanSoaStatementResponse_LOAN_PRE_CLOSURE_IN_PROGRESS)),
}

type GetLoanSoaStatementRequest struct {
	Method string
	Conf   *config.SecuredLoans
	Req    *vgPb.GetLoanSoaStatementRequest
	*fiftyfin.DefaultHeaderSetter
	*fiftyfin.HeaderContentSetter
	*RequestBodyRedactor
}

type GetLoanSoaStatementResponse struct {
	*ResponseBodyRedactor
}

func (u *GetLoanSoaStatementRequest) Marshal() ([]byte, error) {
	details := &vendorPb.GetLoanSoaStatementRequest{
		UserId: u.Req.GetUserId(),
		LoanId: u.Req.GetLoanId(),
	}
	res, err := protojson.Marshal(details)
	if err != nil {
		return nil, fmt.Errorf("error in marshallingget soa statement details. err: %v", zap.Error(err))
	}
	return res, nil
}

func (u *GetLoanSoaStatementRequest) GetResponse() vendorapi.Response {
	return &GetLoanSoaStatementResponse{}
}

func (u *GetLoanSoaStatementRequest) HTTPMethod() string {
	return u.Method
}

func (u *GetLoanSoaStatementRequest) URL() string {
	return u.Conf.Url + "/post_loan/api/v1/get_soa_statement/"
}

func (u *GetLoanSoaStatementResponse) Unmarshal(b []byte) (proto.Message, error) {
	res := vendorPb.GetLoanSoaStatementResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, fmt.Errorf("unable to unmarshal byte array to proto vendor response. err: %v", zap.Error(err))
	}

	// if len(res.GetData().GetSoaData().GetTransactions()) == 0 || res.GetData().GetSoaData().GetTransactions()[0].GetDate() == "" {
	//	return &vgPb.GetLoanSoaStatementResponse{
	//		Status: rpc.StatusInternalWithDebugMsg("transaction details are not available in the api"),
	//	}, nil
	// }

	return &vgPb.GetLoanSoaStatementResponse{
		Status: getStatusForMsg(context.Background(), res.GetCode(), res.GetDetail(), getLoanSoaStatementStatusMapping, fiftyfinStatusForMsgReqParams("GetSoaStatement")),
		Data:   res.GetData(),
	}, nil
}

func (u *GetLoanSoaStatementResponse) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	res := vendorPb.GetLoanSoaStatementResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshal byte array to proto vendor response")
	}
	return &vgPb.GetLoanSoaStatementResponse{
		Status: getStatusForMsg(ctx, res.GetCode(), res.GetDetail(), getLoanSoaStatementStatusMapping, fiftyfinStatusForMsgReqParams("GetSoaStatement")),
	}, nil
}
