Application:
  Environment: "demo"
  Name: "claim_all_rewards"

RewardsDb:
  AppName: "rewards"
  StatementTimeout: 30s
  Name: "rewards"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 1
  MaxIdleConn: 1
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Secrets:
  Ids:
    DbCredentials: "demo/rds/postgres"

Aws:
  Region: "ap-south-1"
