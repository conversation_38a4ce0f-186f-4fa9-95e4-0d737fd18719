package main

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"

	"github.com/epifi/gamma/api/actor"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	wob "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type FoliosToUpdate struct {
	FolioID string
	ActorID string
}

func processFolioUpdateEntries(ctx context.Context, wobClient wob.WealthOnboardingClient, actorClient actor.ActorClient, userClient user.UsersClient, _ catalogPb.CatalogManagerClient) {
	var foliosToUpdate []*FoliosToUpdate
	panToActorIDMap := make(map[string]string)
	var err error

	foliosToUpdateString := strings.Split(*foliosWithDataToUpdate, "\n")
	for _, folioToUpdate := range foliosToUpdateString {
		logger.Info(ctx, fmt.Sprintf("processing folioToUpdate: %s", folioToUpdate))
		arr := strings.Split(folioToUpdate, ",")
		actorID, ok := panToActorIDMap[arr[2]]
		if !ok {
			actorID, err = getActorIDFromPan(ctx, actorClient, userClient, arr[2])
			if err != nil {
				logger.Error(ctx, "error in getActorIDFromPan", zap.String(logger.FOLIO_ID, arr[0]), zap.String(logger.MF_AMC, arr[1]))
			}
		}
		foliosToUpdate = append(foliosToUpdate, &FoliosToUpdate{
			FolioID: arr[0],
			ActorID: actorID,
		})
	}

	actorIDsToFolioMapping := make(map[string][]string)
	for _, val := range foliosToUpdate {
		actorIDsToFolioMapping[val.ActorID] = append(actorIDsToFolioMapping[val.ActorID], val.FolioID)
	}

	for actorID, _ := range actorIDsToFolioMapping {
		logNomineeActionForActor(ctx, actorID, wobClient)
		// ToDo(Junaid): Uncomment once nominee apis from vendor starts working
		/*
			resp, err := mfCatalogClient.UpdateNomineeForActor(ctx, &catalogPb.UpdateNomineeForActorRequest{
				ActorId:  actorID,
				FolioIds: folioIDs,
			})
			if te := epifigrpc.RPCError(resp, err); te != nil {
				logger.Error(ctx, "error in UpdateNomineeForActor", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorID))
			}
		*/
	}
}

func logNomineeActionForActor(ctx context.Context, actorId string, wobClient wob.WealthOnboardingClient) {
	logger.Info(ctx, "logging nominee status for actor", zap.String(logger.ACTOR_ID_V2, actorId))
	res, err := wobClient.GetInvestmentDataV2(ctx, &wob.GetInvestmentDataV2Request{ActorIds: []string{actorId}})
	if te := epifigrpc.RPCError(res, err); te != nil {
		logger.Error(ctx, "error in wealthOnboardingClient.GetInvestmentDataV2", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId))
		return
	}
	if res.InvestmentDetailInfo[actorId] == nil {
		logger.Error(ctx, "empty response from wealth onboarding for GetInvestmentDataV2", zap.String(logger.ACTOR_ID_V2, actorId))
		return
	}
	if len(res.InvestmentDetailInfo[actorId].GetInvestmentDetails().GetNomineeDeclarationDetails().GetWealthAccountNominees()) > 1 {
		logger.Error(ctx, "more than 1 nominee present for actor", zap.String(logger.ACTOR_ID_V2, actorId))
		return
	}

	if len(res.InvestmentDetailInfo[actorId].GetInvestmentDetails().GetNomineeDeclarationDetails().GetWealthAccountNominees()) == 0 &&
		res.InvestmentDetailInfo[actorId].GetInvestmentDetails().GetNomineeDeclarationDetails().GetChoice() == commontypes.BooleanEnum_TRUE {
		logger.Error(ctx, "no nominee present for nominee opt in", zap.String(logger.ACTOR_ID_V2, actorId))
		return
	}

	if res.GetInvestmentDetailInfo()[actorId].GetInvestmentDetails().GetNomineeDeclarationDetails().GetChoice() == commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		logger.Info(ctx, "actor has not opted for nominee for UpdateNomineeForActor", zap.String(logger.ACTOR_ID_V2, actorId))
	}

}

func getActorIDFromPan(ctx context.Context, actorClient actor.ActorClient, userClient user.UsersClient, pan string) (string, error) {
	logger.Info(ctx, "invoking getActorIDFromPan")
	resp, err := userClient.GetUsers(ctx, &user.GetUsersRequest{Identifier: []*user.GetUsersRequest_GetUsersIdentifier{
		{
			Identifier: &user.GetUsersRequest_GetUsersIdentifier_Pan{Pan: pan},
		},
	}})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error while getting GetUserByPAN response", zap.Error(te))
		return "", te
	}
	if resp == nil || len(resp.GetUsers()) == 0 {
		logger.Error(ctx, "no user is present for given pan number", zap.String("pan", pan))
		return "", nil
	}

	userID := resp.GetUsers()[0].GetId()

	actorResp, err := actorClient.GetActorByEntityId(ctx, &actor.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: userID,
	})

	if te := epifigrpc.RPCError(actorResp, err); te != nil {
		logger.Error(ctx, "error in GetActorByEntityId", zap.String("pan", pan), zap.Error(te))
		return "", nil
	}
	return actorResp.GetActor().GetId(), nil
}
