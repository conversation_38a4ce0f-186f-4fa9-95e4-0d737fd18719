package config

import (
	"fmt"
	"path/filepath"
	"runtime"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	_, b, _, _ = runtime.Caller(0)

	once   sync.Once
	config *Config
	err    error
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, "inappreferral_referees_savings_accounts_pn")
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	keyToSecret, err := cfg.LoadAllSecrets(conf.EpifiDb, nil, conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		return nil, err
	}
	updateDefaultConfig(conf.EpifiDb, keyToSecret)
	return conf, nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..")
	fmt.Println(configPath)
	return configPath
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *cfg.DB, keyToSecret map[string]string) {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c, dbServerEndpoint)
	cfg.UpdateSecretValues(c, nil, keyToSecret)
}

type Config struct {
	Application *application
	EpifiDb     *cfg.DB
	SimulatorDb *cfg.DB
	Aws         *aws
}

type application struct {
	Environment string
	Name        string
}

type aws struct {
	Region string
}
