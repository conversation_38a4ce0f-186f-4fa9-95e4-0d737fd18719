Application:
  Environment: "prod"

Aws:
  Region: "ap-south-1"

USStocksAlpacaDb:
  DbType: "PGDB"
  AppName: "usstocks"
  StatementTimeout: 10s
  Username: "usstocks_alpaca_dev_user"
  Password: ""
  Name: "usstocks_alpaca"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  SecretName: "prod/rds/epifimetis/usstocks_alpaca_dev_user"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

S3Conf:
  Bucket: "epifi-prod-usstocks-alpaca"

EpifiDb:
  DbType: "CRDB"
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 1
  MaxIdleConn: 1
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Secrets:
  Ids:
    SlackBotOauthToken: "prod/ift/slack-bot-oauth-token"
