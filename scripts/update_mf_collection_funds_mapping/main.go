package main

import (
	"context"
	"flag"
	"fmt"

	pkgErrors "github.com/pkg/errors"
	"github.com/slack-go/slack"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	pb "github.com/epifi/gamma/api/investment/mutualfund"
	catalogpb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	"github.com/epifi/gamma/investment/mutualfund/catalog/dao"
	impl2 "github.com/epifi/gamma/investment/mutualfund/catalog/dao/impl"
	"github.com/epifi/gamma/investment/mutualfund/dao/impl"
	"github.com/epifi/gamma/investment/pagination"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifierrors"
	invPkg "github.com/epifi/gamma/pkg/investment"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/scripts/update_mf_collection_funds_mapping/config"
)

var (
	pageSize              = uint32(500)
	errorsDuringExecution []string
)

// nolint:funlen
func main() {
	ctx := context.Background()
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(fmt.Errorf("failed to get environment: %w", err))
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	conf, err := config.Load()
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err))
		return
	}

	slackOauthToken := flag.String("oauth", "", "slack oauth token")
	slackChannelID := flag.String("channelid", "", "slack channel id to post alerts in")
	flag.Parse()
	logger.InfoNoCtx(fmt.Sprint("slackOauthToken-", *slackOauthToken, " slackChannelID-", *slackChannelID))
	slackClient := slack.New(*slackOauthToken, slack.OptionDebug(true))
	epifiWealthDb, err := storagev2.NewCRDBWithConfig(conf.EpifiWealthDb, false)
	if err != nil {
		logger.ErrorNoCtx("Failed to load epifiWealthDb", zap.Error(err))
		sendSlackUpdates(err.Error(), slackClient, slackChannelID, env)
		return
	}

	mfDao := impl.NewMutualFundCrdb(epifiWealthDb, nil)
	collectionFundMappingDao := impl2.NewCollectionFundMappingCrdb(epifiWealthDb)

	pageToken, err := pagination.GetPageToken(&rpc.PageContextRequest{PageSize: pageSize})
	if err != nil {
		logger.ErrorNoCtx("Error in getting pageToken", zap.Error(err))
		sendSlackUpdates(err.Error(), slackClient, slackChannelID, env)
		return
	}
	filterValueMap := make(map[pb.MutualFundFieldMask]string)
	filterValueMap[pb.MutualFundFieldMask_INTERNAL_STATUS] = pb.MutualFundInternalStatus_AVAILABLE.String()

	mutualFunds, pageResponse, mfErr := mfDao.GetPaginatedMutualFundsForFilters(ctx, filterValueMap, pageToken, pageSize, pb.MutualFundFieldMask_CURRENT_AUM, false)
	if mfErr != nil {
		logger.ErrorNoCtx("Error in getting paginated mutual funds", zap.Error(mfErr))
		errorsDuringExecution = append(errorsDuringExecution, "error getting paginated mf funds : "+mfErr.Error())
	}

	for pageResponse.GetHasAfter() {
		pt, ptErr := pagination.GetPageToken(&rpc.PageContextRequest{
			Token: &rpc.PageContextRequest_AfterToken{
				AfterToken: pageResponse.GetAfterToken(),
			},
			PageSize: pageSize,
		})

		if ptErr != nil {
			logger.ErrorNoCtx("Error in getting after pageToken", zap.Error(err))
			errorsDuringExecution = append(errorsDuringExecution, "Error in getting after pageToken : "+err.Error())
		}

		nextFunds, pgRsp, mfErr := mfDao.GetPaginatedMutualFundsForFilters(ctx, filterValueMap, pt, pageSize, pb.MutualFundFieldMask_CURRENT_AUM, false)
		if mfErr != nil {
			logger.ErrorNoCtx("Error in getting paginated mutual funds", zap.Error(mfErr))
			errorsDuringExecution = append(errorsDuringExecution, "Error getting paginated mf funds : "+mfErr.Error())
		}
		pageResponse = pgRsp

		mutualFunds = append(mutualFunds, nextFunds...)
	}

	if len(errorsDuringExecution) == 0 {
		logger.InfoNoCtx(fmt.Sprintf("Successfully fetched all mfs, count of mfs: %d", len(mutualFunds)))
	} else {
		logger.InfoNoCtx("fetched mfs with error")
		for _, msg := range errorsDuringExecution {
			sendSlackUpdates(msg, slackClient, slackChannelID, env)
		}
		return
	}

	// Arrays for mf ids of Mutual fund table
	var dailySipMfIds, weeklySipMfIds, monthlySipMfIds []string
	// Arrays for mf collection mf ids
	var dailyMappingMfIds, weeklyMappingMfIds, monthlyMappingMfIds []string
	for _, mf := range mutualFunds {
		for _, v := range mf.GetTxnConstraints().GetAllowedSipFrequencies() {
			switch {
			case v == pb.AipFrequency_DAILY && mf.GetTxnConstraints().GetSipMetadata().GetSiDetails()[pb.AipFrequency_DAILY.String()].GetMinAmount() <= 100:
				dailySipMfIds = append(dailySipMfIds, mf.GetId())
			case v == pb.AipFrequency_WEEKLY && mf.GetTxnConstraints().GetSipMetadata().GetSiDetails()[pb.AipFrequency_WEEKLY.String()].GetMinAmount() <= 2000:
				weeklySipMfIds = append(weeklySipMfIds, mf.GetId())
			case v == pb.AipFrequency_MONTHLY:
				monthlySipMfIds = append(monthlySipMfIds, mf.GetId())
			default:
				continue
			}
		}
	}

	collectionIds := []string{"DAILY_DEEDS", "CONSISTENCY_IS_KEY", "THINKING_AHEAD"}
	collectionMappings, err := collectionFundMappingDao.GetCollectionMappingsByFilterOptions(ctx, dao.WithCollectionIds(collectionIds))

	if err != nil {
		if !pkgErrors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.ErrorNoCtx("Error in getting collectionMappings ", zap.Error(err))
			errorsDuringExecution = append(errorsDuringExecution, "Error in getting collectionMappings : "+err.Error())
			for _, msg := range errorsDuringExecution {
				sendSlackUpdates(msg, slackClient, slackChannelID, env)
			}
			return
		}

		logger.InfoNoCtx("mf_collection_funds_mapping table is empty")
	}

	for _, v := range collectionMappings {
		switch v.GetCollectionId() {
		case "DAILY_DEEDS":
			dailyMappingMfIds = append(dailyMappingMfIds, v.GetMutualFundId())
		case "CONSISTENCY_IS_KEY":
			weeklyMappingMfIds = append(weeklyMappingMfIds, v.GetMutualFundId())
		case "THINKING_AHEAD":
			monthlyMappingMfIds = append(monthlyMappingMfIds, v.GetMutualFundId())
		default:
			continue
		}
	}

	addMfIdToCollIdMap := make(map[string][]string, 0)
	deleteMfIdToCollIdMap := make(map[string][]string, 0)
	commonDailyMfIdsMap := invPkg.GetCommonKeysMap(dailyMappingMfIds, dailySipMfIds)
	for _, v := range dailySipMfIds {
		if commonDailyMfIdsMap[v] {
			continue
		}
		addMfIdToCollIdMap[v] = append(addMfIdToCollIdMap[v], "DAILY_DEEDS")
	}

	for _, v := range dailyMappingMfIds {
		if commonDailyMfIdsMap[v] {
			continue
		}
		deleteMfIdToCollIdMap[v] = append(deleteMfIdToCollIdMap[v], "DAILY_DEEDS")
	}
	commonWeeklyMfIdsMap := invPkg.GetCommonKeysMap(weeklyMappingMfIds, weeklySipMfIds)
	for _, v := range weeklySipMfIds {
		if commonWeeklyMfIdsMap[v] {
			continue
		}
		addMfIdToCollIdMap[v] = append(addMfIdToCollIdMap[v], "CONSISTENCY_IS_KEY")
	}

	for _, v := range weeklyMappingMfIds {
		if commonWeeklyMfIdsMap[v] {
			continue
		}
		deleteMfIdToCollIdMap[v] = append(deleteMfIdToCollIdMap[v], "CONSISTENCY_IS_KEY")
	}
	commonMonthlyMfIdsMap := invPkg.GetCommonKeysMap(monthlyMappingMfIds, monthlySipMfIds)
	for _, v := range monthlySipMfIds {
		if commonMonthlyMfIdsMap[v] {
			continue
		}
		addMfIdToCollIdMap[v] = append(addMfIdToCollIdMap[v], "THINKING_AHEAD")
	}

	for _, v := range monthlyMappingMfIds {
		if commonMonthlyMfIdsMap[v] {
			continue
		}
		deleteMfIdToCollIdMap[v] = append(deleteMfIdToCollIdMap[v], "THINKING_AHEAD")
	}

	logger.InfoNoCtx(fmt.Sprintf("Number of Mf Ids to collection mappings to be added are %d", len(addMfIdToCollIdMap)))
	addNewMfCollectionFundsMappings(ctx, addMfIdToCollIdMap, collectionFundMappingDao)

	if len(errorsDuringExecution) > 0 {
		for _, msg := range errorsDuringExecution {
			sendSlackUpdates(msg, slackClient, slackChannelID, env)
		}
		return
	}

	logger.InfoNoCtx(fmt.Sprintf("Number of Mf Ids to collection mappings to be deleted are %d", len(deleteMfIdToCollIdMap)))
	deleteObsoleteCollectionFundsMappings(deleteMfIdToCollIdMap, epifiWealthDb)

	if len(errorsDuringExecution) > 0 {
		for _, msg := range errorsDuringExecution {
			sendSlackUpdates(msg, slackClient, slackChannelID, env)
		}
	}
}

func addNewMfCollectionFundsMappings(ctx context.Context, addMfIdToCollIdMap map[string][]string, collectionFundMappingDao *impl2.CollectionFundMappingCrdb) {

	for k, collIds := range addMfIdToCollIdMap {
		for _, collId := range collIds {
			_, err := collectionFundMappingDao.Create(ctx, &catalogpb.CollectionFundMapping{
				MutualFundId: k,
				CollectionId: collId,
			})
			if err != nil {
				logger.ErrorNoCtx("cannot create mf collection mapping", zap.Error(err))
				errorsDuringExecution = append(errorsDuringExecution, "cannot create mf collection "+collId+" mapping : "+err.Error())
			}
		}

	}
}

func deleteObsoleteCollectionFundsMappings(deleteMfIdToCollIdMap map[string][]string, epifiWealthDb *gorm.DB) {
	for k, collIds := range deleteMfIdToCollIdMap {
		for _, collId := range collIds {
			if queryErr := epifiWealthDb.Exec(`delete from mf_collection_funds_mappings where mutual_fund_id= ? and collection_id = ?`, k, collId).Error; queryErr != nil {
				logger.ErrorNoCtx("cannot delete obsolete mappings after update", zap.Error(queryErr))
				errorsDuringExecution = append(errorsDuringExecution, "cannot delete obsolete "+collId+" mappings after update : "+queryErr.Error())
			}
		}
	}
}

func sendSlackUpdates(msg string, client *slack.Client, channelID *string, env string) {
	if env != "prod" {
		return
	}

	attachment := slack.Attachment{
		Pretext: "Jenkins Golang Bot",
		Text:    msg,
		Color:   "#36a64f",
	}
	_, timestamp, err := client.PostMessage(*channelID, slack.MsgOptionAttachments(attachment))
	if err != nil {
		logger.ErrorNoCtx(fmt.Sprintf("Error with bot service : %v on %v", err, timestamp))
	}
	logger.InfoNoCtx("Successfully sent updates on error")
}
