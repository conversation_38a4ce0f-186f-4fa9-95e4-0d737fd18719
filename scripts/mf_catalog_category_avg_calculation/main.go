// nolint:dupl,unused,deadcode,gosec,funlen
package main

import (
	"context"
	"flag"
	"fmt"
	"os"

	"go.uber.org/zap"

	"github.com/epifi/gamma/api/investment/mutualfund/catalog/scheduler"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

var (
	inputJobType = flag.String("JobType", "", "job type, refer to the type of aggregation we want to perform")
)

var jobTypeToAggFreq = map[string]scheduler.TriggerCatalogAggregationsRequest_CatalogAggregationFrequency{
	"DAILY_AGGREGATION":   scheduler.TriggerCatalogAggregationsRequest_DAILY_CATALOG_AGGREGATION,
	"MONTHLY_AGGREGATION": scheduler.TriggerCatalogAggregationsRequest_MONTHLY_CATALOG_AGGREGATION,
}

func main() {
	flag.Parse()
	err := calculateMutualFundCategoryAverages()
	if err != nil {
		os.Exit(1)
	}
}

func calculateMutualFundCategoryAverages() error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	cleanedInputJob := *inputJobType
	fmt.Printf("\n JOB: '%v' \n", *inputJobType)
	fmt.Printf("\n AggregationFrequency: '%v' \n", jobTypeToAggFreq[cleanedInputJob])
	investmentConn := epifigrpc.NewConnByService(cfg.INVESTMENT_SERVICE)
	defer epifigrpc.CloseConn(investmentConn)
	catalogSchClient := scheduler.NewSchedulerClient(investmentConn)
	ctx := context.Background()
	res, err := catalogSchClient.TriggerCatalogAggregations(ctx, &scheduler.TriggerCatalogAggregationsRequest{
		AggregationFrequency: jobTypeToAggFreq[cleanedInputJob],
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		logger.Error(ctx, "error in triggering mutual fund catalog aggregator", zap.Error(err))
		return te
	}
	return nil
}
