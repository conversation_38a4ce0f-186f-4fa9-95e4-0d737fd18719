Application:
  Environment: "demo"
  Name: "schedule-pgdb-expiry-workflow"
  Namespace : "demo-nebula"
  TaskQueue: "demo-nebula-task-queue"

Dbs:
  - Host: "postgres.cgej1reeydov.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "demo/rds/postgres"
    Name: "auth"
    SslMode: "verify-full"
  - Host: "postgres.cgej1reeydov.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "demo/rds/postgres"
    Name: "comms"
    SslMode: "verify-full"
  - Host: "postgres.cgej1reeydov.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "demo/rds/postgres"
    Name: "inapphelp"
    SslMode: "verify-full"

  - Host: "postgres.cgej1reeydov.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "demo/rds/postgres"
    Name: "sherlock"
    SslMode: "verify-full"

  - Host: "postgres.cgej1reeydov.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "demo/rds/postgres"
    Name: "fittt"
    SslMode: "verify-full"

  - Host: "postgres.cgej1reeydov.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "demo/rds/postgres"
    Name: "insights"
    SslMode: "verify-full"

  - Host: "postgres.cgej1reeydov.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "demo/rds/postgres"
    Name: "actor_insights"
    SslMode: "verify-full"

  - Host: "postgres.cgej1reeydov.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "demo/rds/postgres14"
    Name: "budgeting"
    SslMode: "verify-full"

  - Host: "postgres.cgej1reeydov.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "demo/rds/postgres14"
    Name: "connected_account"
    SslMode: "verify-full"

TemporalClientInitOptions:
  TemporalCodecAesKey: "demo/temporal/codec-encryption-key"
  UseMigrationCluster: false
