// nolint: funlen
package main

import (
	"context"
	"database/sql"
	"errors"
	"flag"
	"fmt"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/aws/session"
	"github.com/epifi/be-common/pkg/aws/sns"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifiserver"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/storage"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/scripts/tiering_assign_tier/config"
	"github.com/epifi/gamma/tiering/dao"
	"github.com/epifi/gamma/tiering/helper"
	"github.com/epifi/gamma/tiering/tiermappings"
)

var (
	actorIdsFlag                   = flag.String("actorIds", "", "pass list of actor ids seperated by space")
	toTierFlag                     = flag.String("toTier", "TIER_TWO_THOUSAND", "select the tier to force downgrade to")
	silentGracePeriodFlag          = flag.Int("silentGracePeriod", 720, "silent grace period for upgrade/downgrade(in hours)")
	defaultStartingGracePeriodFlag = flag.Int("defaultStartingGracePeriod", 720, "default starting grace period for upgrade/downgrade(in hours)")
	shouldSkipDowngrade            = flag.Bool("shouldSkipDowngrade", false, "skip downgrade to the latest version")

	silentGracePeriod          time.Duration
	defaultStartingGracePeriod time.Duration
	cachePrefixes              = []string{"TIERING:ATI:", "TIERING:ETM:", "TIERING:TMH:"}
	windowSize                 = 365.25 * 24 * time.Hour

	zeroGraceDetails = func() *tieringPb.GraceDetails {
		return &tieringPb.GraceDetails{
			GraceLadderPosition: 0,
			Period:              defaultStartingGracePeriod.Seconds(),
		}
	}

	zeroCoolOffDetails = func() *tieringPb.CooloffDetails {
		return &tieringPb.CooloffDetails{
			MovementsDone: 0,
			WindowSize:    windowSize.Seconds(),
		}
	}
)

type AssignTierService struct {
	tieringClient         tieringPb.TieringClient
	tierUpdateEventPub    queue.Publisher
	redisClient           *redis.Client
	etmDao                dao.EligibleTierMovementDao
	tmhDao                dao.TierMovementHistoryDao
	atiDao                dao.ActorTierInfoDao
	txnExecutor           storagev2.TxnExecutor
	actorsAlreadyInToTier []string
}

func NewAssignTierService(
	tieringClient tieringPb.TieringClient,
	tierUpdateEventPub queue.Publisher,
	redisClient *redis.Client,
	etmDao dao.EligibleTierMovementDao,
	tmhDao dao.TierMovementHistoryDao,
	atiDao dao.ActorTierInfoDao,
	txnExecutor storagev2.TxnExecutor,
) *AssignTierService {
	actorsAlreadyInToTier := make([]string, 0)
	return &AssignTierService{
		tieringClient:         tieringClient,
		tierUpdateEventPub:    tierUpdateEventPub,
		redisClient:           redisClient,
		etmDao:                etmDao,
		tmhDao:                tmhDao,
		atiDao:                atiDao,
		txnExecutor:           txnExecutor,
		actorsAlreadyInToTier: actorsAlreadyInToTier,
	}
}

func main() {
	epifiserver.HandlePanic()
	conf, err := config.Load()
	if err != nil {
		panic(err)
	}

	logger.Init(conf.Application.Environment)
	defer func() { _ = logger.Log.Sync() }()

	flag.Parse()
	actorIdsUnCleaned := *actorIdsFlag
	if actorIdsUnCleaned == "" {
		flag.Usage()
		logger.Panic("actorId is empty")
		return
	}
	actorIds := getActorIdsAfterCleaning(actorIdsUnCleaned)

	toTierStr := *toTierFlag
	if toTierStr == "" {
		flag.Usage()
		logger.Panic("toTier is empty")
		return
	}
	toTier, getToTierErr := getToTierAfterCleaning(toTierStr)
	if getToTierErr != nil {
		logger.Panic("error getting to tier after cleaning", zap.Error(getToTierErr))
		return
	}

	// set silent grace period
	if *silentGracePeriodFlag == 0 {
		logger.Panic("silentGracePeriod is empty")
		return
	}

	silentGracePeriod = time.Duration(*silentGracePeriodFlag) * time.Hour

	// set default starting grace period
	if *defaultStartingGracePeriodFlag == 0 {
		defaultStartingGracePeriod = 1 * time.Second
	} else {
		defaultStartingGracePeriod = time.Duration(*defaultStartingGracePeriodFlag) * time.Hour
	}

	// Connect to tiering PGDB
	var psqlDb *sql.DB
	pgdbConn, getPgdbErr := storagev2.NewPostgresDBWithConfig(conf.TieringDb, true)
	if getPgdbErr != nil {
		logger.Panic("unable to connect to tiering pgdb", zap.Error(getPgdbErr))
	}

	psqlDb, err = pgdbConn.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}

	teardown := func() {
		_ = psqlDb.Close()
	}
	defer teardown()

	logger.InfoNoCtx(fmt.Sprintf("Successfully connected to tiering PGDB: %s on %s:%d with debug mode: %t",
		conf.TieringDb.GetName(), conf.TieringDb.Host, conf.TieringDb.Port, conf.TieringDb.EnableDebug),
		zap.String("service", conf.Application.Name))

	tieringConn := epifigrpc.NewConnByService(cfg.TIERING_SERVICE)
	defer epifigrpc.CloseConn(tieringConn)
	tieringClient := tieringPb.NewTieringClient(tieringConn)

	awsSess, err := session.NewSession(conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		logger.Panic(fmt.Sprintf("failed to initialize AWS session: %v", err))
	}

	tierUpdateEventPub, tierUpdateEventPubInitErr := sns.NewSnsPublisherWithConfig(conf.TierUpdateEventExternalPublisher, awsSess, queue.NewDefaultMessage())
	if tierUpdateEventPubInitErr != nil {
		panic(tierUpdateEventPubInitErr)
	}

	etmDao := dao.NewEligibleTierMovementImpl(pgdbConn)
	tmhDao := dao.NewTierMovementHistoryImpl(pgdbConn, 0)
	atiDao := dao.NewActorTierInfoImpl(pgdbConn)
	txnExecutor := storagev2.NewGormTxnExecutor(pgdbConn)

	redisClient := storage.NewRedisClientFromConfig(conf.TieringActorRedisStore, true)

	s := NewAssignTierService(tieringClient, tierUpdateEventPub, redisClient, etmDao, tmhDao, atiDao, txnExecutor)

	failedActorIds := make([]string, 0)
	ctx := context.Background()
	for _, actorId := range actorIds {
		assignTierErr := s.assignTierForActor(ctx, actorId, toTier, *shouldSkipDowngrade)
		if assignTierErr != nil {
			logger.Error(ctx, "error assigning tier for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(assignTierErr))
			failedActorIds = append(failedActorIds, actorId)
		}
	}
	logger.Info(ctx, "job failed for", zap.Any("failedActorIds", failedActorIds))
	logger.Info(ctx, "actors already in target tier", zap.Any("alreadyInToTierActorIds", s.actorsAlreadyInToTier))
}

func getActorIdsAfterCleaning(actorIdsUncleaned string) []string {
	actorIds := make([]string, 0)
	actorIdsCleaned := strings.Split(actorIdsUncleaned, ":")
	for _, actorId := range actorIdsCleaned {
		actorIds = append(actorIds, strings.Trim(actorId, " "))
	}
	return actorIds
}

func getToTierAfterCleaning(toTierStr string) (tieringEnumPb.Tier, error) {
	enumValInt, ok := tieringEnumPb.Tier_value[toTierStr]
	if !ok {
		return tieringEnumPb.Tier_TIER_UNSPECIFIED, fmt.Errorf("unexpected Tier value: %s", toTierStr)
	}
	return tieringEnumPb.Tier(enumValInt), nil
}

func (s *AssignTierService) assignTierForActor(ctx context.Context, actorId string, toTier tieringEnumPb.Tier, shouldSkipDowngrade bool) error {
	evaluatedTier, criteriaRefId, toTierErr := s.calculateToTier(ctx, actorId)
	if toTierErr != nil {
		return fmt.Errorf("error evaluating toTier: %w", toTierErr)
	}
	return s.assignTier(ctx, actorId, toTier, evaluatedTier, criteriaRefId, shouldSkipDowngrade)
}

func (s *AssignTierService) calculateToTier(ctx context.Context, actorId string) (tieringEnumPb.Tier, string, error) {
	evaluateTierResp, evaluateTierErr := s.tieringClient.EvaluateTierForActor(ctx, &tieringPb.EvaluateTierForActorRequest{
		ActorId: actorId,
		Options: &tieringPb.EvaluateTierForActorRequest_Options{
			ToEvalForMultipleWays: true,
			ToSkipAppAccessCheck:  true,
		},
	})
	if rpcErr := epifigrpc.RPCError(evaluateTierResp, evaluateTierErr); rpcErr != nil {
		return tieringEnumPb.Tier_TIER_UNSPECIFIED, "", fmt.Errorf("error in calling evaluate tier RPC: %w", rpcErr)
	}
	evaluatedTier, conversionErr := tiermappings.GetInternalTierFromExternalTier(evaluateTierResp.GetEvaluatedTier())
	if conversionErr != nil {
		return tieringEnumPb.Tier_TIER_UNSPECIFIED, "", fmt.Errorf("error in converting external to internal tier: %w", conversionErr)
	}
	return evaluatedTier, evaluateTierResp.GetCriteriaReferenceId(), nil
}

func (s *AssignTierService) assignTier(ctx context.Context, actorId string, toTier, evaluatedTier tieringEnumPb.Tier, criteriaRefId string, shouldSkipDowngrade bool) error {
	// get current tier of actor
	var currentTier tieringEnumPb.Tier
	existingAti, getAtiErr := s.atiDao.Get(ctx, actorId)
	if getAtiErr != nil {
		if !errors.Is(getAtiErr, epifierrors.ErrRecordNotFound) {
			return fmt.Errorf("error fetching current tier of actor: %w", getAtiErr)
		}
		// default to STANDARD if no entry is found
		currentTier = tieringEnumPb.Tier_TIER_TEN
	} else {
		currentTier = existingAti.GetTier()
	}

	// validate movement type is of downgrade type
	if existingAti != nil && existingAti.GetTier().Number() == toTier.Number() {
		s.actorsAlreadyInToTier = append(s.actorsAlreadyInToTier, actorId)
		logger.Info(ctx, "user is already in required tier",
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String("current_tier", existingAti.GetTier().String()),
			zap.String("to_tier", toTier.String()),
		)
		return nil
	}

	mmtType, getMmtTypeErr := helper.GetMovementTypeFromStartAndEndTiers(currentTier, toTier)
	if getMmtTypeErr != nil {
		return fmt.Errorf("error getting movement type: %w", getMmtTypeErr)
	}
	if mmtType == tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_DOWNGRADE && shouldSkipDowngrade {
		logger.Info(ctx, "skipping downgrade tier-moment", zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String(logger.TO_TIER, toTier.String()),
			zap.String(logger.FROM_TIER, currentTier.String()))
		return nil
	}

	// check if actor already has eligible movement
	lastEligibleEtm, getLastEtmErr := s.etmDao.GetLatestByActorIdAndStatus(ctx, actorId,
		tieringEnumPb.EligibleTierMovementStatus_ELIGIBLE_TIER_MOVEMENT_STATUS_ELIGIBLE)
	if getLastEtmErr != nil && !errors.Is(getLastEtmErr, epifierrors.ErrRecordNotFound) {
		return fmt.Errorf("error fetching last eligible movement: %w", getLastEtmErr)
	}
	etmDetails, getEtmDetailsErr := getEtmDetails(mmtType, lastEligibleEtm)
	if getEtmDetailsErr != nil {
		return fmt.Errorf("error fetching etm details: %w", getEtmDetailsErr)
	}

	didTierUpdateHappen := false
	var createdTMH *tieringPb.TierMovementHistory
	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		// invalidate existing eligible etm if present
		if lastEligibleEtm != nil {
			lastEligibleEtm.Status = tieringEnumPb.EligibleTierMovementStatus_ELIGIBLE_TIER_MOVEMENT_STATUS_INVALID
			updErr := s.etmDao.Update(txnCtx, lastEligibleEtm, []tieringPb.EligibleTierMovementFieldMask{
				tieringPb.EligibleTierMovementFieldMask_ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_STATUS,
			})
			if updErr != nil {
				return fmt.Errorf("error invalidating existing eligible ETM: %w", updErr)
			}
		}

		curTimestamp := timestampPb.Now()

		// create new etm
		newEtm := &tieringPb.EligibleTierMovement{
			CriteriaReferenceId: criteriaRefId,
			FromTier:            currentTier,
			ToTier:              toTier,
			ActorId:             actorId,
			MovementType:        mmtType,
			MovementTimestamp:   curTimestamp,
			Status:              tieringEnumPb.EligibleTierMovementStatus_ELIGIBLE_TIER_MOVEMENT_STATUS_DONE,
			CreatedAt:           curTimestamp,
			UpdatedAt:           curTimestamp,
			Details:             etmDetails,
		}
		createdDoneEtm, createEtmErr := s.etmDao.Create(txnCtx, newEtm)
		if createEtmErr != nil {
			return fmt.Errorf("error creating DONE movement ETM: %w", createEtmErr)
		}

		// create new tmh
		newTmh := &tieringPb.TierMovementHistory{
			EligibilityMovementReferenceId: createdDoneEtm.GetId(),
			CriteriaReferenceId:            createdDoneEtm.GetCriteriaReferenceId(),
			FromTier:                       createdDoneEtm.GetFromTier(),
			ToTier:                         createdDoneEtm.GetToTier(),
			ActorId:                        createdDoneEtm.GetActorId(),
			MovementType:                   createdDoneEtm.GetMovementType(),
			Provenance:                     tieringEnumPb.Provenance_PROVENANCE_FORCED_MOVEMENT,
			CreatedAt:                      curTimestamp,
			UpdatedAt:                      curTimestamp,
		}
		var createdTMHErr error
		createdTMH, createdTMHErr = s.tmhDao.Create(txnCtx, newTmh)
		if createdTMHErr != nil {
			return fmt.Errorf("error creating TMH: %w", createdTMHErr)
		}

		// update ati
		newAti := &tieringPb.ActorTierInfo{
			ActorId:             createdTMH.GetActorId(),
			Tier:                createdTMH.GetToTier(),
			MovementReferenceId: createdTMH.GetId(),
			CriteriaReferenceId: createdTMH.GetCriteriaReferenceId(),
		}
		if existingAti == nil {
			_, createdAtiErr := s.atiDao.Create(txnCtx, newAti)
			if createdAtiErr != nil {
				return fmt.Errorf("error creating ATI: %w", createdAtiErr)
			}
		} else {
			_, updAtiErr := s.atiDao.Update(txnCtx, newAti, []tieringPb.ActorTierInfoFieldMask{
				tieringPb.ActorTierInfoFieldMask_ACTOR_TIER_INFO_FIELD_MASK_TIER,
			})
			if updAtiErr != nil {
				return fmt.Errorf("error updating ATI: %w", updAtiErr)
			}
		}

		// remove entries from the cache
		for _, cachePrefix := range cachePrefixes {
			_, delCacheEntryErr := s.redisClient.Del(ctx, cachePrefix+actorId).Result()
			if delCacheEntryErr != nil {
				return fmt.Errorf("error deleting entry from cache: %w", delCacheEntryErr)
			}
		}

		defer func() {
			didTierUpdateHappen = true
		}()

		if toTier.Number() <= evaluatedTier.Number() {
			// no need to create new etm if movement
			logger.Info(ctx, "continuing without creating new etm since user is already is same or higher tier")
			return nil
		}
		// create new ETM with custom silent grace
		newEligibleEtm := &tieringPb.EligibleTierMovement{
			CriteriaReferenceId: criteriaRefId,
			FromTier:            toTier,
			ToTier:              evaluatedTier,
			ActorId:             actorId,
			MovementType:        tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_DOWNGRADE,
			MovementTimestamp:   timestampPb.New(helper.RoundUpToNextDay(time.Now().Add(silentGracePeriod).Add(defaultStartingGracePeriod))),
			Status:              tieringEnumPb.EligibleTierMovementStatus_ELIGIBLE_TIER_MOVEMENT_STATUS_ELIGIBLE,
			CreatedAt:           curTimestamp,
			UpdatedAt:           curTimestamp,
		}
		if lastEligibleEtm != nil && lastEligibleEtm.GetMovementType() == tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_DOWNGRADE {
			details := lastEligibleEtm.GetDetails()
			newEligibleEtm.Details = details
		} else {
			newEligibleEtm.Details = &tieringPb.EligibilityDetails{
				Details: &tieringPb.EligibilityDetails_GraceDetails{
					GraceDetails: zeroGraceDetails(),
				},
			}
		}
		newEligibleEtm.GetDetails().GetGraceDetails().SilentGracePeriod = silentGracePeriod.Seconds()

		_, createNewEligibleEtmErr := s.etmDao.Create(txnCtx, newEligibleEtm)
		if createNewEligibleEtmErr != nil {
			return fmt.Errorf("error creating new eligible etm: %w", createNewEligibleEtmErr)
		}

		return nil
	})
	if txnErr != nil {
		return fmt.Errorf("error in transaction: %w", txnErr)
	}
	if didTierUpdateHappen {
		publishErr := s.publishTierUpdateEvent(ctx, createdTMH)
		if publishErr != nil {
			return fmt.Errorf("error publishing tier update event: %w", publishErr)
		}
	}
	return nil
}

// nolint: unparam
func getEtmDetails(mmtType tieringEnumPb.TierMovementType, lastEligibleEtm *tieringPb.EligibleTierMovement) (*tieringPb.EligibilityDetails, error) {
	if lastEligibleEtm != nil {
		return lastEligibleEtm.GetDetails(), nil
	}
	// If new movement is of downgrade type, return zero grace details
	if mmtType == tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_DOWNGRADE {
		return &tieringPb.EligibilityDetails{
			Details: &tieringPb.EligibilityDetails_GraceDetails{
				GraceDetails: zeroGraceDetails(),
			},
		}, nil
	}
	// If new movement is of upgrade type, return zero cool off details
	return &tieringPb.EligibilityDetails{
		Details: &tieringPb.EligibilityDetails_CooloffDetails{
			CooloffDetails: zeroCoolOffDetails(),
		},
	}, nil
}

func (s *AssignTierService) publishTierUpdateEvent(ctx context.Context, createdTMH *tieringPb.TierMovementHistory) error {
	curExtTier, conversionErr := tiermappings.GetExternalTierFromInternalTier(createdTMH.GetFromTier())
	if conversionErr != nil {
		return fmt.Errorf("error converting from tier to external: %w", conversionErr)
	}
	toExtTier, conversionErr := tiermappings.GetExternalTierFromInternalTier(createdTMH.GetToTier())
	if conversionErr != nil {
		return fmt.Errorf("error converting to tier to external: %w", conversionErr)
	}
	pubId, pubErr := s.tierUpdateEventPub.Publish(ctx, &tieringExtPb.TierUpdateEvent{
		EventId:           createdTMH.GetId(),
		ActorId:           createdTMH.GetActorId(),
		FromTier:          curExtTier,
		ToTier:            toExtTier,
		MovementType:      helper.ConvertInternalMovementTypeToExternal(createdTMH.GetMovementType()),
		EventTimestamp:    timestampPb.Now(),
		MovementTimestamp: createdTMH.GetCreatedAt(),
	})
	if pubErr != nil {
		return fmt.Errorf("error publishing message to tier update topic: %w", pubErr)
	}
	logger.Info(ctx, "successfully published tier update event for actor",
		zap.String(logger.QUEUE_MESSAGE_ID, pubId),
		zap.String(logger.ACTOR_ID_V2, createdTMH.GetActorId()),
		zap.String("from_tier", curExtTier.String()),
		zap.String("to_tier", toExtTier.String()),
	)
	return nil
}
