Application:
  Environment: "development"
  Name: "process_pay_tickets"

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLCertPath: "./crdb/development/"
  SSLRootCert: "development/cockroach/ca.crt"
  SSLClientCert: "development/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "development/cockroach/client.epifi_dev_user.key"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"

ProcessTicketJobConfig:
  MaxTicketsThresholdMap:
    UPI_PINSET: 10
  JobStatsEmailParam:
    FromEmailId: "<EMAIL>"
    FromEmailName: "Process Ticket Automation non-prod"
    ReceiverMailIdList:
      UPI_PINSET:
        ReceiverMailInfo1:
          EmailName: "Pay Team"
          EmailId: "<EMAIL>"
    EmailMsg:
      ONBOARDING: "Please find troubleshooting details in attachment."
  NumberOfDays: 89
