package main

import (
	"context"
	"os"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"

	schedulerpb "github.com/epifi/gamma/api/fittt/scheduler"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

func main() {
	if err := triggerFitScheduler(); err != nil {
		logger.ErrorNoCtx("failed to trigger fit scheduler", zap.Error(err))
		os.Exit(1)
	}
}

func triggerFitScheduler() error {
	// Get environment
	envName, err := cfg.GetEnvironment()
	if err != nil {
		logger.ErrorNoCtx("failed to get environment", zap.Error(err))
		return err
	}

	// Setup logger
	logger.Init(envName)
	defer func() { _ = logger.Log.Sync() }()

	fitConn := epifigrpc.NewConnByService(cfg.FITTT_SERVICE)
	schedulerClient := schedulerpb.NewSchedulerServiceClient(fitConn)
	defer epifigrpc.CloseConn(fitConn)

	ctx := epificontext.WithTraceId(context.Background(), metadata.New(map[string]string{}))
	till := time.Now()
	resp, err := schedulerClient.TriggerJobs(ctx, &schedulerpb.TriggerJobsRequest{
		ScheduledTill: timestamppb.New(till),
	})
	if err2 := epifigrpc.RPCError(resp, err); err2 != nil {
		logger.Error(ctx, "TriggerJobs rpc failed", zap.Error(err2))
		return err2
	}
	logger.Info(ctx, "Triggered scheduling jobs in FIT", zap.Time("ScheduledTill", till))
	return nil
}
