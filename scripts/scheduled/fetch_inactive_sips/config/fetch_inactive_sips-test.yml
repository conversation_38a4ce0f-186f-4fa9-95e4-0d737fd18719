Application:
  Environment: "test"

RMSDb:
  Name: "rms"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

AWS:
  Region: "ap-south-1"

RMSSecrets:
  Ids:
    DbCredentials: "{\"username\": \"root\", \"password\": \"\"}"

EpifiDb:
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "epifi_wealth_test"
  EnableDebug: false
  StatementTimeout: 2m
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

S3Conf:
  Bucket: "epifi-wealth-onboarding"
  LivenessPath: "liveness"
  DownloadedKraDocS3Path: "downloaded_kra_doc"
