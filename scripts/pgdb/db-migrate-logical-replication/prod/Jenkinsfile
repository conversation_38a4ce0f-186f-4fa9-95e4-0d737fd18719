@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools{
        go 'Go stable'
    }
    options {
        ansiColor('xterm')
        buildDiscarder(logRotator(numToKeepStr: '10', artifactNumToKeepStr: '10'))
    }
    parameters {
        choice(name: 'ENV', choices: ['prod'], description: 'Environment to run on.')
        choice(name: 'STEP', choices: ['sync_pre_data_schema', 'start_logical_replication','sync_post_data_schema','stop_logical_replication','make_users_readonly','rollback_users_change','make_dest_users_readonly','rollback_dest_users_change', 'compare_last_data'], description: '')
        string(name: 'dbs', defaultValue:"vendormapping", description: 'Space separated list of dbs to apply the step to  e.g. vendormapping fittt accrual')
        string(name: 'S3_BUCKET', defaultValue:"pg-migration", description: 's3 bucket to store the pre and post dump')
        string(name: 'SOURCE_DB_HOST', defaultValue:"staging-postgres-14.cet3be1aev95.ap-south-1.rds.amazonaws.com", description:'')
        string(name: 'SOURCE_DB_CREDS_SECRET', defaultValue:"staging/rds/postgres", description: 'The secret which contains the username and password for the root user of the db. General format is `env/rds/postgres` for non prod and `prod/rds/epifipostgres` for postgres ')
        string(name: 'DEST_DB_HOST', defaultValue:"staging-postgres.cet3be1aev95.ap-south-1.rds.amazonaws.com", description: '')
        string(name: 'DEST_DB_CREDS_SECRET', defaultValue:"staging/rds/postgres", description: 'The secret which contains the username and password for the root user of the db. General format is `env/rds/postgres` for non prod and `prod/rds/epifipostgres` for prod ')
    }
    environment {
        ENV = "${params.ENV}"
    }
    stages {
        stage('Assume Role') {
            steps {
                script {
                    assumeRole()
                }
            }
        }
         stage('Get Gamma Repo') {
            steps {
                gitCheckout(repoName: "gamma", gitBranch: "master", githubOrg: "epifi")
            }
         }
        stage("Execute") {
            steps {
                dir("gamma") {
                    script {
                        sh """
                           set +x
                           export SOURCE_DB_PASS= \$(aws secretsmanager get-secret-value --secret-id "\${params.SOURCE_DB_CREDS_SECRET}" | jq -r '.SecretString | fromjson | .password')
                           export SOURCE_DB_USER=\$(aws secretsmanager get-secret-value --secret-id "\${params.SOURCE_DB_CREDS_SECRET}" | jq -r '.SecretString | fromjson | .username')
                           export DEST_DB_PASS= \$(aws secretsmanager get-secret-value --secret-id "\${params.DEST_DB_CREDS_SECRET}" | jq -r '.SecretString | fromjson | .password')
                           export DEST_DB_USER=\$(aws secretsmanager get-secret-value --secret-id "\${params.DEST_DB_CREDS_SECRET}" | jq -r '.SecretString | fromjson | .username')
                           export S3_BUCKET=epifi-${ENV}-${params.S3_BUCKET}
                           set -x

                           set +x
                           curl https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add -
                           sudo sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt \$(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list'
                           sudo apt update -y
                           sudo apt-get install postgresql-client-14 -y
                           set -x

                           cd  scripts/pgdb/migration
                           ENVIRONMENT=${ENV} ./migrate.sh "${params.step}" "${params.dbs}"
                        """
                    }
                }
            }
        }
    }
}
