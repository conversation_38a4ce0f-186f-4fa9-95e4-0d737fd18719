package config

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	_, b, _, _ = runtime.Caller(0)

	once   sync.Once
	config *Config
	err    error
)

const (
	TieringDbCreds = "TieringDbCreds" // nolint:gosec
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, "tiering_knobs")
	if err != nil {
		return nil, fmt.Errorf("failed to load  config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to  unmarshal config: %w", err)
	}

	keyToSecret, err := cfg.LoadAllSecretsV2(conf.EpifiDb, conf.TieringDb, conf.Secrets.Ids, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, err
	}
	if err := updateDefaultConfig(conf, keyToSecret); err != nil {
		return nil, err
	}
	return conf, nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..")
	fmt.Println(configPath)
	return configPath
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c.EpifiDb, dbServerEndpoint)

	pgdbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_RDS)
	cfg.UpdateDbEndpointInConfig(c.TieringDb, pgdbServerEndpoint)

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	cfg.UpdateSecretValues(c.EpifiDb, c.Secrets, keyToSecret)
	cfg.UpdatePGDBSecretValuesV2(c.TieringDb, keyToSecret)

	// update db username and password in db config
	// if env is development keys are not fetched from SM hence update from yml file itself
	if cfg.IsLocalEnv(c.Application.Environment) {
		cfg.UpdateDbUsernamePasswordInConfig(c.TieringDb, c.Secrets.Ids[TieringDbCreds])
		return nil
	}
	if _, ok := keyToSecret[TieringDbCreds]; !ok {
		return errors.New("db username password not fetched tiering db from secrets manager")
	}
	cfg.UpdateDbUsernamePasswordInConfig(c.TieringDb, keyToSecret[TieringDbCreds])
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Port = intVal
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		c.EpifiDb.Host = val
	}

	if val, ok := os.LookupEnv("PGDB_HOST"); ok {
		c.TieringDb.Host = val
	}

	return nil
}

type Config struct {
	Application                     *application
	EpifiDb                         *cfg.DB
	TieringDb                       *cfg.DB
	SimulatorDb                     *cfg.DB
	Aws                             *aws
	CooloffNoOfMovementAllowed      int64
	ProcessTierReEvalEventPublisher *cfg.SqsPublisher
	EvaluationWindowDuration        time.Duration
	CoolOffDefaultPeriod            time.Duration
	Secrets                         *cfg.Secrets
	Server                          *Server
	AWS                             *cfg.AWS
	RedisOptions                    *cfg.RedisOptions
}

type application struct {
	Environment string
	Name        string
}

type aws struct {
	Region string
}

type Server struct {
	Port            int
	HealthCheckPort int
}
