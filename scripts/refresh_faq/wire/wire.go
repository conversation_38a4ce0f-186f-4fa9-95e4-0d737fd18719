//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"
	gormv2 "gorm.io/gorm"

	solPb "github.com/epifi/gamma/api/vendorgateway/cx/solutions"

	"github.com/epifi/be-common/pkg/cmd/types"

	"github.com/epifi/gamma/inapphelp/faq/processor/dao"
	dao2 "github.com/epifi/gamma/inapphelp/faq/serving/dao"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/gamma/scripts/refresh_faq/config"
	"github.com/epifi/gamma/scripts/refresh_faq/processor"
)

func InitializeRefreshFAQ(db *gormv2.DB, scClient solPb.SolutionsClient, faqPub queue.Publisher, faqConfig *config.FaqConfig) *processor.RefreshFAQ {
	wire.Build(
		processor.NewRefreshFAQ,
		pgdbProvider,
		wire.NewSet(dao2.NewFetchFAQData, wire.Bind(new(dao2.FetchFAQ), new(*dao2.FetchFAQData))),
		wire.NewSet(dao.NewPersistFAQData, wire.Bind(new(dao.PersistFAQ), new(*dao.PersistFAQData))),
	)
	return &processor.RefreshFAQ{}
}

func pgdbProvider(db *gormv2.DB) types.InapphelpPGDB {
	return types.InapphelpPGDB(db)
}
