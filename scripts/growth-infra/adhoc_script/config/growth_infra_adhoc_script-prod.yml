Application:
  Environment: "prod"
  Name: "growth_infra_adhoc_script"

Aws:
  Region: "ap-south-1"

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLCertPath: "./crdb/prod/"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  StatementTimeout: 2m
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

RewardsDb:
  AppName: "rewards"
  StatementTimeout: 30s
  Name: "rewards"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-16139.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:16139"
    Password: ""
  AuthDetails:
    SecretPath: "prod/redis/segment/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: segment

Secrets:
  Ids:
    DbCredentials: "prod/rds/epifimetis/rewards"

RewardsNotificationEventSqsPublisher:
  QueueName: "prod-rewards-notification-queue"

S3Config:
  OnboardingBucketName: "epifi-prod-onboarding"
