Application:
  Environment: "qa"
  Name: "soft_delete_rewards"

RewardsDb:
  Name: "rewards"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    DbCredentials: "qa/rds/postgres/rewards"

RewardStatusUpdateEventSnsPublisher:
  TopicName: "qa-rewards-reward-status-update-event-topic"

ProjectionEventSnsPublisher:
  TopicName: "qa-rewards-projection-event-topic"
