package config

import (
	"fmt"
	"path/filepath"
	"runtime"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	_, b, _, _ = runtime.Caller(0)

	once   sync.Once
	config *Config
	err    error
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// Get the config directory path
	configDirPath := filepath.Join(filepath.Dir(b), ".")
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, "recon_automated")
	if err != nil {
		return nil, fmt.Errorf("failed to load  config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return conf, nil
}

type Config struct {
	Application *application
	Aws         *aws
}

type application struct {
	Environment string
	Name        string
}

type aws struct {
	Region       string
	S3BucketName string
	S3BasePath   string
}
