package main

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"flag"
	"fmt"
	"strconv"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	accountPb "github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/balance/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/scripts/Pay/balance_update/config"
	"github.com/epifi/gamma/simulator/dao/model"
)

var (
	phoneNumberFlag = flag.String("phoneno", "", "phone number for which balance needs to be updated")
	addMoneyFlag    = flag.String("addmoney", "", "amount by which balance needs to be updated")
	conf            *config.Config
	actorId         string
	phoneNumber     *commontypes.PhoneNumber
	amountToBeAdded int64
	account         model.Account
	err             error
)

// nolint: funlen
func main() {
	flag.Parse()
	env, errEnv := cfg.GetEnvironment()
	if errEnv != nil {
		panic(errEnv)
	}
	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	// load config
	conf, err = config.Load()
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to load config: %v", err))
	}

	// initialise connections to ACCOUNTS_SERVICE, SAVINGS SERVICE and USER_SERVICE
	accountsConn := epifigrpc.NewConnByService(cfg.ACCOUNTS_SERVICE)
	defer epifigrpc.CloseConn(accountsConn)
	accountsClient := accountPb.NewBalanceClient(accountsConn)

	savingsConn := epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	defer epifigrpc.CloseConn(savingsConn)
	savingsClient := savingsPb.NewSavingsClient(savingsConn)

	userConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	defer epifigrpc.CloseConn(userConn)
	userClient := user.NewUsersClient(userConn)

	phnNo, amountToBeAddedInPaisa, errParse := parsePhoneNumberAndAmountFromFlags(phoneNumberFlag, addMoneyFlag)
	if errParse != nil {
		logger.Panic("error in parsing Flags: ", zap.Error(errParse))
	}
	actorId, err = getActorIDFromPhoneNumber(phnNo, userClient)
	if err != nil {
		logger.Panic("error in fetching actorId from phoneNumber", zap.Error(err))
	}
	logger.InfoNoCtx(fmt.Sprintf("Actor ID: %s", actorId))

	requestId, accountId, errReqAcc := fetchRequestIdAndAccountIdForUser(actorId, savingsClient)
	if errReqAcc != nil {
		logger.Panic("error in fetching requestId and accountId from actorId", zap.Error(err))
	}
	logger.InfoNoCtx(fmt.Sprintf("Request ID: %s, Account ID: %s", requestId, accountId))

	simulatorDbConn, simulatorDbConnErr := storageV2.NewGormDB(conf.SimulatorDb)
	if simulatorDbConnErr != nil {
		logger.Panic("failed to Establish connection with simulator DB", zap.Error(simulatorDbConnErr))
	}
	if err = simulatorDbConn.Where("request_id = ?", requestId).First(&account).Update("balance", account.Balance+amountToBeAddedInPaisa).Error; err != nil {
		logger.Panic("failed to update accountBalance in SimulatorDB", zap.Error(err))
	}

	accBalanceResp, getAccBalErr := accountsClient.GetAccountBalance(context.Background(), &accountPb.GetAccountBalanceRequest{
		Identifier:    &accountPb.GetAccountBalanceRequest_Id{Id: accountId},
		ActorId:       actorId,
		DataFreshness: enums.DataFreshness_DATA_FRESHNESS_REAL_TIME,
	})
	if getAccBalErr != nil {
		logger.Panic("Error in calling getAccountBalance Rpc ", zap.Error(err))
	}
	logger.InfoNoCtx(fmt.Sprintf("Updated account Balance  = %s", accBalanceResp.GetAvailableBalance().String()))
}

// Fetching requestId and accountId for User with the help of actorID
// requestId will look like this "NEOEPIFIAccount", and accountId like this "**************"
func fetchRequestIdAndAccountIdForUser(actorId string, savingsClient savingsPb.SavingsClient) (string, string, error) {
	getAcctResp, getAcctErr := savingsClient.GetAccount(context.Background(), &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_ActorId{
		ActorId: actorId,
	},
	})
	switch {
	case getAcctErr != nil && status.Code(getAcctErr) == codes.NotFound:
		logger.Error(context.Background(), "record not found fetching accountResp using actor id", zap.String(logger.ACTOR_ID_V2, actorId))
		return "", "", fmt.Errorf("error in fetching accountResponse for actorID: %s : %v ", actorId, getAcctErr)
	case getAcctErr != nil:
		logger.Error(context.Background(), "error fetching accountResp using actor id", zap.String(logger.ACTOR_ID_V2, actorId))
		return "", "", fmt.Errorf("error in fetching accountResponse for actorID: %s : %v ", actorId, getAcctErr)
	}
	return getAcctResp.GetAccount().GetRetryInfo().GetRequestId(), getAcctResp.GetAccount().GetId(), nil
}

func getActorIDFromPhoneNumber(phoneNumber string, userClient user.UsersClient) (string, error) {
	ph, parseErr := commontypes.ParsePhoneNumber(phoneNumber)
	if parseErr != nil {
		return "", parseErr
	}
	userResp, getUserErr := userClient.GetUser(context.Background(), &user.GetUserRequest{
		Identifier: &user.GetUserRequest_PhoneNumber{
			PhoneNumber: ph,
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, getUserErr); rpcErr != nil {
		return "", fmt.Errorf("error in getting actorId from phoneNumber: %v", rpcErr)
	}
	return userResp.GetUser().GetActorId(), nil
}

// parsing values from Flag Variables
func parsePhoneNumberAndAmountFromFlags(phoneNumberFlag *string, addMoneyFlag *string) (string, int64, error) {
	if *phoneNumberFlag != "" {
		if phoneNumber, err = commontypes.ParsePhoneNumber(*phoneNumberFlag); err != nil {
			return "", 0, fmt.Errorf("error in parsing phone number from string: %v", err)
		}
		phoneNumber.CountryCode = 91
	} else {
		return "", 0, fmt.Errorf("phone number is Mandatory")
	}

	amountToBeAdded = 0
	if *addMoneyFlag != "" {
		if amountToBeAdded, err = strconv.ParseInt(*addMoneyFlag, 10, 64); err != nil {
			return "", 0, fmt.Errorf("error while parsing money as int: %v", err)
		}
	} else {
		return "", 0, fmt.Errorf("addMoney is Mandatory")
	}
	logger.InfoNoCtx(fmt.Sprintf("Amount entered by user in Rupees:  = %s", *addMoneyFlag))
	return phoneNumber.ToString(), amountToBeAdded * 100, nil
}
