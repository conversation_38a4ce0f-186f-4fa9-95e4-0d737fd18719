// nolint:all
package job

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"encoding/csv"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	types "github.com/epifi/gamma/api/typesv2"
	user2 "github.com/epifi/gamma/api/user"
	llVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	address2 "github.com/epifi/gamma/pkg/address"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/dao/model"
	"github.com/epifi/gamma/scripts/pal/helper"
	"github.com/epifi/gamma/scripts/pl_analytics/job/vendors"
	vendorMappingDao "github.com/epifi/gamma/vendormapping/dao"
)

type BackfillLoanAccountIdJob struct {
	dbResourceProvider       *storageV2.DBResourceProvider[*gorm.DB]
	loanInstallmentPayoutDao dao.LoanInstallmentPayoutDao
	txnExecutor              storageV2.TxnExecutor
	lsedao                   dao.LoanStepExecutionsDao
	helper                   *helper.Helper
	s3Client                 s3.S3Client
	loanApplicantDao         dao.LoanApplicantDao
	loanAccountsDao          dao.LoanAccountsDao
	loanRequestDao           dao.LoanRequestsDao
	userClient               user2.UsersClient
	vendorMappingDao         vendorMappingDao.IVendorMappingDAO
	llPalVgClient            llVgPb.LiquiloansClient
}

func NewBackfillLoanAccountIdJob(
	dbResourceProvider *storageV2.DBResourceProvider[*gorm.DB],
	loanInstallmentPayoutDao dao.LoanInstallmentPayoutDao,
	txnExecutor storageV2.TxnExecutor,
	lsedao dao.LoanStepExecutionsDao,
	helper *helper.Helper,
	s3Client s3.S3Client,
	loanApplicantDao dao.LoanApplicantDao,
	loanRequestDao dao.LoanRequestsDao,
	userClient user2.UsersClient,
	vendorMappingDao vendorMappingDao.IVendorMappingDAO,
	llPalVgClient llVgPb.LiquiloansClient,

) *BackfillLoanAccountIdJob {
	return &BackfillLoanAccountIdJob{
		dbResourceProvider:       dbResourceProvider,
		loanInstallmentPayoutDao: loanInstallmentPayoutDao,
		txnExecutor:              txnExecutor,
		lsedao:                   lsedao,
		helper:                   helper,
		s3Client:                 s3Client,
		loanApplicantDao:         loanApplicantDao,
		loanRequestDao:           loanRequestDao,
		userClient:               userClient,
		vendorMappingDao:         vendorMappingDao,
		llPalVgClient:            llPalVgClient,
	}
}

type BackfillLoanAccountIdJobArgs struct {
	//Vendor palPb.Vendor `json:"vendor"`
	S3Path string `json:"s3Path"`
}

func (b *BackfillLoanAccountIdJob) GetArgs() interface{} {
	return &BackfillLoanAccountIdJobArgs{}
}

// JobName : "BACKFILL_LOAN_ACCOUNT_ID"
// jobArguments : `{"vendor":"FEDERAL"}`

// nolint:funlen
func (b *BackfillLoanAccountIdJob) Run(ctx context.Context, args ...interface{}) error {
	//vendor := args[0].(*BackfillLoanAccountIdJobArgs).Vendor
	//b.backfillLoanAccountId(ctx, vendor)
	//err := b.FetchUserData(ctx)
	// err := b.CgDataFill(ctx)
	//s3Path := args[0].(*BackfillLoanAccountIdJobArgs).S3Path
	//
	//csvRecords, readErr := b.helper.ReadCsv(ctx, s3Path)
	//if readErr != nil {
	//	logger.ErrorNoCtx("error in readCsv", zap.Error(readErr))
	//	return nil
	//}
	// err := b.CgDataFill(ctx)
	// b.getLoanAccountIdfc(ctx)

	csvRows, err := b.helper.ReadCsvFromS3CancelLead(ctx, args[0].(*BackfillLoanAccountIdJobArgs).S3Path)
	if err != nil {
		return errors.Wrap(err, "error in readCsvFromS3")
	}
	b.llCancelLead(ctx, csvRows)
	return nil
}

func (b *BackfillLoanAccountIdJob) llCancelLead(ctx context.Context, csvRows []*helper.CancelLeadCsvRow) {
	for _, row := range csvRows {
		lp := helper.GetLoanProgramFromString(strings.ToLower(row.LoanProgram))
		cancelLeadRes, err := b.llPalVgClient.CancelLead(ctx, &llVgPb.CancelLeadRequest{
			Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LIQUILOANS},
			ApplicationId: row.ApplicationId,
			ApplicantId:   row.ApplicantId,
			LoanProgram:   b.helper.ConvertToVGLoanProgram(lp),
			SchemeVersion: llVgPb.SchemeVersion_SCHEME_VERSION_V1,
		})
		if te := epifigrpc.RPCError(cancelLeadRes, err); te != nil {
			logger.Error(ctx, "failed in cancel lead vg call", zap.Error(te))
			continue
		}
		time.Sleep(2 * time.Second)
	}
}

func (b *BackfillLoanAccountIdJob) getLoanAccountIdfc(ctx context.Context) {
	ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_IDFC_PL)
	db, err := vendors.GetConnFromContextOrProvider(ctx, b.dbResourceProvider)
	if err != nil {
		fmt.Println("error in getConnFromContextOrProvider")
		return
	}
	loanIds := map[string]bool{
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
		"*********": true,
	}
	var loanAccounts []*model.LoanAccount

	err = db.Where("status = ?", palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE).
		Order("created_at desc").Limit(300).Find(&loanAccounts).Error
	if err != nil {
		fmt.Println("failed to fetch loan accounts", err)
		return
	}

	for _, loanAccount := range loanAccounts {
		loanRequestModels := make([]*model.LoanRequest, 0)
		err = db.Where("loan_account_id = ? and type = ?", loanAccount.Id, palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION).Find(&loanRequestModels).Error
		if err != nil {
			fmt.Println("failed to fetch loan request by loan account: ", loanAccount.Id)
			continue
		}
		if len(loanRequestModels) == 0 {
			fmt.Println("failed to fetch loan request by loan account: ", loanAccount.Id)
			continue
		}
		lr := loanRequestModels[0]
		var lse model.LoanStepExecution
		err = db.Where("ref_id = ? and flow = ? and step_name = ?", lr.Id,
			palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
			palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE).First(&lse).Error
		if err != nil {
			fmt.Println("failed to fetch mandate lse by La ID: ", loanAccount.Id, "LR ID: ", lr.Id)
			continue
		}

		merchantTxnId := lse.GetProto().GetDetails().GetMandateData().GetMerchantTxnId()

		if loanIds[loanAccount.GetProto().GetAccountNumber()] {
			var lii model.LoanInstallmentInfo
			err = db.Where("account_id = ?", loanAccount.Id).First(&lii).Error
			if err != nil {
				fmt.Println("failed to fetch lii for loan account: ", loanAccount.LoanAccountId, "error : ", err.Error())
				continue
			}
			fmt.Println("ActorID: ", lr.ActorId, "AccountNumber: ", loanAccount.LoanAccountId, "Created At: ", loanAccount.CreatedAt, "MerchantTxnId: ", merchantTxnId)
			fmt.Println("----- LoanInstallmentData ------")
			fmt.Println("Account number : ", loanAccount.GetProto().GetAccountNumber(), " loan installment info : ", lii.GetProto().String(), " loan account : ", loanAccount.GetProto().String())
		}
	}
}

func (b *BackfillLoanAccountIdJob) backfillLoanAccountId(ctx context.Context, vendor palPb.Vendor) {
	ctx = epificontext.WithOwnership(ctx, vendors.GetPalOwnership(vendor))
	db, err := vendors.GetConnFromContextOrProvider(ctx, b.dbResourceProvider)
	if err != nil {
		fmt.Println("error in getConnFromContextOrProvider")
		return
	}

	var loanInstallmentInfoModel []*model.LoanInstallmentInfo

	dbResults := db.Where("status = ?", palPb.LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_ACTIVE).FindInBatches(&loanInstallmentInfoModel, 100, func(tx *gorm.DB, batch int) error {
		for i := range loanInstallmentInfoModel {
			curr := loanInstallmentInfoModel[i]

			lipRes, lipErr := b.loanInstallmentPayoutDao.GetByLoanInstallmentInfoId(ctx, curr.Id)
			if len(lipRes) == 0 {
				continue
			}
			if lipErr != nil {
				fmt.Printf("failed to get LIP data for given LII ID, %s, err : %v\n", curr.Id, lipErr)
			}
			for _, row := range lipRes {
				row.LoanAccountId = curr.AccountId
				updateError := b.loanInstallmentPayoutDao.Update(ctx, row,
					[]palPb.LoanInstallmentPayoutFieldMask{
						palPb.LoanInstallmentPayoutFieldMask_LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_LOAN_ACCOUNT_ID,
					})
				if updateError != nil {
					fmt.Printf("failed to update LIP table's Loan Account ID for LII ID, %s, err : %v\n", curr.Id, updateError)
				}
			}
		}

		// tx.save() will not be used as tx.save() is only used when we
		// change data of table on which FindInBatches is applied; tx.save() example:
		// if err := tx.Save(&loanInstallmentInfoModel).Error; err != nil {
		// 	 fmt.Printf("error: %v", err)
		//	 return err
		// }
		time.Sleep(1 * time.Second)
		return nil
	})
	if err := dbResults.Error; err != nil {
		fmt.Printf("error in fetching loan installment info data, error: %v\n", err)
	}
}

type LLUser struct {
	ApplicantID string
	Pan         string
	Name        *commontypes.Name
	LoanProgram string
	ActorId     string
	FiLitePan   string
	FiLIteName  *commontypes.Name
}

//	func (p *BackfillLoanAccountIdJob) FetchUserData(ctx context.Context) error {
//		ctx = epificontext.WithOwnership(ctx, vendors.GetPalOwnership(palPb.Vendor_LIQUILOANS))
//		var users []*LLUser
//		//loanRequests := make([]*model.LoanRequest, 0)
//		//if err := db.Where("EXTRACT(month from created_at) = ? and EXTRACT(year from created_at) = ? and status = ? and loan_program IN (?,?)", int(time.Now().Month()), time.Now().Year(), palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED, palPb.LoanProgram_LOAN_PROGRAM_FLDG, palPb.LoanProgram_LOAN_PROGRAM_STPL).Find(&loanRequests).Error; err != nil {
//		//	logger.Error(ctx, "failed to fetch loan requests")
//		//	return err
//		//}
//		//logger.Info(ctx, fmt.Sprintf("failed total: %v", len(loanRequests)))
//		for _, lseId := range failedlse {
//			lse, err := p.lsedao.GetById(ctx, lseId)
//			if err != nil {
//				logger.Error(ctx, "failed to fetch lse", zap.String("LSE_ID", lseId))
//			}
//			actor, err := p.helper.GetUserByActorId(ctx, lse.GetActorId())
//			if err != nil {
//				logger.Error(ctx, "failed to fetch user", zap.String(logger.ACTOR_ID, lse.ActorId))
//				continue
//			}
//			loanApplicant, err := p.loanApplicantDao.GetByActorId(ctx, lse.ActorId)
//			if err != nil {
//				logger.Error(ctx, "failed to fetch loan applicant", zap.String(logger.ACTOR_ID, lse.ActorId))
//				continue
//			}
//			pan := ""
//			for _, val := range actor.GetDataVerificationDetails().GetDataVerificationDetails() {
//				if val.GetDataType() == user2.DataType_DATA_TYPE_PAN {
//					pan = val.GetPanNumber()
//					break
//				}
//			}
//			users = append(users, &LLUser{
//				Name:        actor.GetProfile().GetKycName(),
//				Pan:         actor.GetProfile().GetPAN(),
//				ApplicantID: loanApplicant.GetVendorApplicantId(),
//				LoanProgram: loanApplicant.GetLoanProgram().String(),
//				ActorId:     lse.GetActorId(),
//				FiLIteName:  actor.GetProfile().GivenName,
//				FiLitePan:   pan,
//			})
//		}
//		payload, err := convertToCsv([]*Addresses{})
//		if err != nil {
//			logger.Error(ctx, "failed to get csv")
//			return err
//		}
//		dateNow := datetime.TimeToDateInLoc(time.Now().In(datetime.IST), datetime.IST)
//		path := fmt.Sprintf("preapproved-loan/ll_%d_%d_%d.csv",
//			dateNow.GetDay(), dateNow.GetMonth(), dateNow.GetYear())
//		signedUrl, writeToS3error := p.s3Client.WriteAndGetPreSignedUrl(path, []byte(payload), 1800)
//		if writeToS3error != nil {
//			return err
//		}
//		logger.Info(ctx, fmt.Sprintf("Signed URL: %s,path: %s", signedUrl, path))
//		return nil
//	}
func convertToCsv(req []*Addresses) (string, error) {
	var tuple strings.Builder
	writer := csv.NewWriter(&tuple)
	rows := make([][]string, 0)

	rows = append(rows, []string{"actor_id", "address", "type"})
	for _, data := range req {
		rows = append(rows, []string{
			data.actorId, data.address, data.Type,
		})
	}
	err := writer.WriteAll(rows)
	if err != nil {
		return "", err
	}
	// Flush and close the writer
	writer.Flush()
	return tuple.String(), nil
}

type Addresses struct {
	actorId string
	address string
	Type    string
}

type CsvData struct {
	actorId    string
	lseDetails string
	StepName   string
}

func (p *BackfillLoanAccountIdJob) CgDataFill(ctx context.Context) error {
	addresses := make([]*Addresses, 0)
	ctx = epificontext.WithOwnership(ctx, vendors.GetPalOwnership(palPb.Vendor_LIQUILOANS))
	for _, actorId := range failedActorIds {
		lrs, err := p.loanRequestDao.GetByActorIdAndVendorAndStatus(ctx, actorId, palPb.Vendor_LIQUILOANS, []palPb.LoanRequestStatus{})
		if err != nil {
			logger.Error(ctx, err.Error(), zap.String(logger.ACTOR_ID, actorId))
			continue
		}
		for _, lr := range lrs {
			lseFlow := palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION
			if lr.GetType() == palPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY {
				lseFlow = palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY
			}

			lse, err := p.lsedao.GetByRefIdAndFlowAndName(ctx, lr.GetId(), lseFlow, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS)
			if err != nil {
				logger.Error(ctx, err.Error(), zap.String(logger.ACTOR_ID, actorId))
				continue
			}
			address := lse.GetDetails().GetOnboardingData().GetAddressDetails().GetAddressDetails()
			if address == nil {
				logger.Error(ctx, "failed to get address", zap.String(logger.ACTOR_ID, actorId))
				continue
			}
			postalAddress := ConvertToPostalAddress(address)
			strPostalAddress := address2.ConvertPostalAddressToString(postalAddress)
			addresses = append(addresses, &Addresses{
				actorId: actorId,
				address: strPostalAddress,
			})
			user, err := p.userClient.GetUser(ctx, &user2.GetUserRequest{
				Identifier: &user2.GetUserRequest_ActorId{
					ActorId: actorId,
				},
			})
			if err != nil {
				logger.Error(ctx, err.Error(), zap.String(logger.ACTOR_ID, actorId))
				continue
			}
			userAddresses, err := p.userClient.GetAllAddresses(ctx, &user2.GetAllAddressesRequest{
				UserId: user.GetUser().GetId(),
				Format: types.AddressFormat_USER_READABLE,
			})
			if err != nil {
				logger.Error(ctx, err.Error(), zap.String(logger.ACTOR_ID, actorId))
				continue
			}
			for key, userAddresses := range userAddresses.GetAddresses() {
				for _, postalAddress := range userAddresses.GetAddresses() {
					strPostalAddress := address2.ConvertPostalAddressToString(postalAddress)
					addresses = append(addresses, &Addresses{
						actorId: actorId,
						address: strPostalAddress,
						Type:    key,
					})
				}
			}
		}
		time.Sleep(10 * time.Millisecond)
	}

	payload, err := convertToCsv(addresses)
	if err != nil {
		logger.Error(ctx, "failed to get csv")
		return err
	}
	fmt.Println(payload)
	dateNow := datetime.TimeToDateInLoc(time.Now().In(datetime.IST), datetime.IST)
	path := fmt.Sprintf("preapproved-loan/ll_cg_address_backfill_%d_%d_%d.csv",
		dateNow.GetDay(), dateNow.GetMonth(), dateNow.GetYear())
	signedUrl, writeToS3error := p.s3Client.WriteAndGetPreSignedUrl(ctx, path, []byte(payload), 1800)
	if writeToS3error != nil {
		return err
	}
	logger.Info(ctx, fmt.Sprintf("Signed URL: %s", signedUrl))
	return nil
}

// func (p *BackfillLoanAccountIdJob) FetchLseData(ctx context.Context, csvRecords *[][]string) error {
//	fireHoseIds := make([]string, 0)
//	csvLseData := make([]*CsvData, 0)
//	for _, rawRecord := range (*csvRecords)[1:] {
//		fireHoseIds = append(fireHoseIds, rawRecord[0])
//	}
//	fireHoseIdToActorIdMap := vendors.GetActorIdsFromFirehoseIds(ctx, fireHoseIds, p.vendorMappingDao)
//
//	for _, csvRow := range (*csvRecords)[1:] {
//		actorId := fireHoseIdToActorIdMap[csvRow[0]]
//		vendor := strings.ToUpper(csvRow[2])
//		palVendor := palPb.Vendor(palPb.Vendor_value[vendor])
//		ctx = epificontext.WithOwnership(ctx, vendors.GetPalOwnership(palVendor))
//		lses, err := p.lsedao.GetByActorId(ctx, actorId)
//		if err != nil {
//			logger.Error(ctx, err.Error(), zap.String(logger.ACTOR_ID, actorId))
//			continue
//		}
//
//		for _, lse := range lses {
//			csvLseData = append(csvLseData, &CsvData{
//				actorId:    actorId,
//				lseDetails: lse.GetDetails().String(),
//				StepName:   lse.GetStepName().String(),
//			})
//
//		}
//	}
//	payload, err := convertToCsv(csvLseData)
//	if err != nil {
//		logger.Error(ctx, "failed to get csv")
//		return err
//	}
//	dateNow := datetime.TimeToDateInLoc(time.Now().In(datetime.IST), datetime.IST)
//	path := fmt.Sprintf("preapproved-loan/ll_cg_address_backfill_%d_%d_%d.csv",
//		dateNow.GetDay(), dateNow.GetMonth(), dateNow.GetYear())
//	signedUrl, writeToS3error := p.s3Client.WriteAndGetPreSignedUrl(path, []byte(payload), 1800)
//	if writeToS3error != nil {
//		return err
//	}
//	logger.Info(ctx, fmt.Sprintf("Signed URL: %s,path: %s", signedUrl, path))
//	return nil
// }
