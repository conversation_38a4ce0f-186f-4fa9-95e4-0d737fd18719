package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/aml"
)

func main() {
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}
	logger.Init(env)
	ctx := epificontext.WithTraceId(context.Background(), metadata.New(map[string]string{}))

	var (
		fromDateTimeStr = flag.String("from", "", "From date YYYY-MM-DDTHH:MM:SSZ07:00")
		toDateTimeStr   = flag.String("to", "", "To date YYYY-MM-DDTHH:MM:SSZ07:00")
	)
	flag.Parse()
	if *fromDateTimeStr == "" || *toDateTimeStr == "" {
		logger.Error(ctx, "from and to dates are required")
		os.Exit(1)
	}

	err = syncCases(ctx, &syncCasesReq{
		fromDateTime: *fromDateTimeStr,
		toDateTime:   *toDateTimeStr,
	})
	if err != nil {
		logger.Error(ctx, "error syncing cases", zap.Error(err))
		os.Exit(1)
	}
	logger.Info(ctx, "Cases synced successfully")
	os.Exit(0)
}

type syncCasesReq struct {
	fromDateTime, toDateTime string
}

func syncCases(ctx context.Context, req *syncCasesReq) error {
	fromTime, err := time.Parse("2006-01-02T15:04:05Z07:00", req.fromDateTime)
	if err != nil {
		return errors.Wrapf(err, "error parsing from time: %s", req.fromDateTime)
	}
	toTime, err := time.Parse("2006-01-02T15:04:05Z07:00", req.toDateTime)
	if err != nil {
		return errors.Wrapf(err, "error parsing to time: %s", req.toDateTime)
	}
	logger.Info(ctx, fmt.Sprintf("Syncing cases from %s to %s...\n", req.fromDateTime, req.toDateTime))

	conn := epifigrpc.NewConnByService(cfg.AML_SERVICE)
	defer epifigrpc.CloseConn(conn)
	client := aml.NewAmlClient(conn)
	owners := []common.Owner{
		common.Owner_OWNER_EPIFI_TECH,
		common.Owner_OWNER_STOCK_GUARDIAN_TSP,
	}
	for _, owner := range owners {
		var res *aml.SyncCasesResponse
		res, err = client.SyncCases(ctx, &aml.SyncCasesRequest{
			Owner:        owner,
			FromDateTime: timestamppb.New(fromTime),
			ToDateTime:   timestamppb.New(toTime),
		})
		if err = epifigrpc.RPCError(res, err); err != nil {
			return errors.Wrapf(err, "error syncing cases for owner: %s, from: %s, to: %s", owner.String(),
				req.fromDateTime, req.toDateTime)
		}
	}
	return nil
}
