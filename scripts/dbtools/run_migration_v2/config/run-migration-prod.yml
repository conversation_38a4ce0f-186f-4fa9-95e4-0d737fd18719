Application:
  Environment: "prod"
  Name: "run-migration"

Clusters:
  - Name: "crdb"
    Type: "cockroachdb"
    DbConfTemplate:
      AppName: "run-migration-job"
      EnableDebug: true
      SSLMode: "verify-full"
      SSLCertPath: "./crdb/prod/"
      SSLRootCert: "prod/cockroach/ca.crt"
      MaxOpenConn: 1
      MaxIdleConn: 1
      StatementTimeout: -1s
      IdleInTxnSessionTimeout: -1s
      GormV2:
        LogLevelGormV2: "INFO"
        SlowQueryLogThreshold: 200ms
        UseInsecureLog: true
    Dbs:
      epifi:
        RepoName: "gamma"
        Username: "epifi_admin_user"
        SSLClientCert: "prod/cockroach/client.epifi_admin_user.crt"
        SSLClientKey: "prod/cockroach/client.epifi_admin_user.key"
      epifi_wealth:
        RepoName: "gamma"
        Username: "epifi_wealth_admin_user"
        SSLClientCert: "prod/cockroach/client.epifi_wealth_admin_user.crt"
        SSLClientKey: "prod/cockroach/client.epifi_wealth_admin_user.key"
      credit_card:
        RepoName: "gamma"
        Username: "credit_card_admin_user"
        SSLClientCert: "prod/cockroach/client.credit_card_admin_user.crt"
        SSLClientKey: "prod/cockroach/client.credit_card_admin_user.key"
      federal:
        RepoName: "gamma"
        Username: "federal_admin_user"
        SSLClientCert: "prod/cockroach/client.federal_admin_user.crt"
        SSLClientKey: "prod/cockroach/client.federal_admin_user.key"
      frm:
        RepoName: "gamma"
        Username: "frm_admin_user"
        SSLClientCert: "prod/cockroach/client.frm_admin_user.crt"
        SSLClientKey: "prod/cockroach/client.frm_admin_user.key"
      p2pinvestment_liquiloans:
        RepoName: "gamma"
        Username: "p2pinvestment_liquiloans_admin_user"
        SSLClientCert: "prod/cockroach/client.p2pinvestment_liquiloans_admin_user.crt"
        SSLClientKey: "prod/cockroach/client.p2pinvestment_liquiloans_admin_user.key"
      pl_idfc:
        RepoName: "gamma"
        Username: "pl_idfc_admin_user"
        SSLClientCert: "prod/cockroach/client.pl_idfc_admin_user.crt"
        SSLClientKey: "prod/cockroach/client.pl_idfc_admin_user.key"
      pl_liquiloans:
        RepoName: "gamma"
        Username: "pl_liquiloans_admin_user"
        SSLClientCert: "prod/cockroach/client.pl_liquiloans_admin_user.crt"
        SSLClientKey: "prod/cockroach/client.pl_liquiloans_admin_user.key"
      loans_fiftyfin_crdb:
        RepoName: "gamma"
        Username: "loans_fiftyfin_crdb_admin_user"
        SSLClientCert: "prod/cockroach/client.loans_fiftyfin_crdb_admin_user.crt"
        SSLClientKey: "prod/cockroach/client.loans_fiftyfin_crdb_admin_user.key"
      liquiloans_epifi:
        RepoName: "gamma"
        Username: "liquiloans_epifi_admin_user"
        SSLClientCert: "prod/cockroach/client.liquiloans_epifi_admin_user.crt"
        SSLClientKey: "prod/cockroach/client.liquiloans_epifi_admin_user.key"
      stockguardian_tsp_crdb:
        RepoName: "gamma"
        Username: "stockguardian_tsp_crdb_admin_user"
        SSLClientCert: "prod/cockroach/client.stockguardian_tsp_crdb_admin_user.crt"
        SSLClientKey: "prod/cockroach/client.stockguardian_tsp_crdb_admin_user.key"

  - Name: "metis"
    Type: "postgres"
    DbConfTemplate:
      AppName: "run-migration-job"
      EnableDebug: true
      SSLMode: "verify-full"
      SSLRootCert: "prod/rds/rds-ca-root-2061"
      MaxOpenConn: 1
      MaxIdleConn: 1
      StatementTimeout: -1s
      IdleInTxnSessionTimeout: -1s
      GormV2:
        LogLevelGormV2: "INFO"
        SlowQueryLogThreshold: 200ms
        UseInsecureLog: true
    Dbs:
      accrual:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/accruals"
      actor_insights:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/actor-insights"
      budgeting:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/budgeting_admin_user"
      casper:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/casper"
      connected_account:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/connected-account"
      epifi_wealth_analytics:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/epifi-wealth-analytics"
      feature_engineering:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/feature_engineering_admin_user"
      fittt:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/fittt_admin_user"
      inapphelp:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/inapphelp"
      insights:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/insights"
      nudge:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/nudge_admin_user"
      quest:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/quest_admin_user"
      rewards:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/rewards"
      rms:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/rms_admin_user"
      salaryprogram:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/salaryprogram"
      segment:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/segment"
      sherlock:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/sherlock"
      vendordata:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/vendordata"
      vendormapping:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/vendormapping"
      pay:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/pay_admin_user"
      frm_pgdb:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/frm_pgdb_admin_user"
      cms:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/cms_admin_user"
      loans_liquiloans:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/loans_liquiloans_admin_user"
      categorizer:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/categorizer_admin_user"
      debit_card_pgdb:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/debit_card_pgdb_admin_user"
      credit_card_pgdb:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/credit_card_pgdb_admin_user"
      credit_card_federal:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/credit_card_federal_admin_user"
      loans_federal:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/loans_federal_admin_user"
      loans_idfc:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/loans_idfc_admin_user"
      tiering:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/tiering_admin_user"
      usstocks_alpaca:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/usstocks_alpaca_admin_user"
      user_properties:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/user_properties_admin_user"
      bank_customer:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/bank_customer_admin_user"
      loans_fiftyfin:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/loans_fiftyfin_admin_user"
      credit_risk:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/credit_risk_admin_user"
      loans_moneyview:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/loans_moneyview_admin_user"
      loans_abfl:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/loans_abfl_admin_user"
      loans_lenden:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/loans_lenden_admin_user"
      loans_stock_guardian_lsp:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/loans_stock_guardian_lsp_admin_user"
      loans_epifi_tech:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/loans_epifi_tech_admin_user"
      omegle:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/omegle_admin_user"
      stock_guardian_tsp:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/stock_guardian_tsp_admin_user"
      stocks:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/stocks_admin_user"
      insights_federal:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/insights_federal_admin_user"
      nps:
        RepoName: "gamma"
        Password: "prod/rds/epifimetis/nps_admin_user"

  - Name: "minerva"
    Type: "postgres"
    DbConfTemplate:
      AppName: "run-migration-job"
      EnableDebug: true
      SSLMode: "verify-full"
      SSLRootCert: "prod/rds/rds-ca-root-2061"
      MaxOpenConn: 1
      MaxIdleConn: 1
      StatementTimeout: -1s
      IdleInTxnSessionTimeout: -1s
      GormV2:
        LogLevelGormV2: "INFO"
        SlowQueryLogThreshold: 200ms
        UseInsecureLog: true
    Dbs:
      comms:
        RepoName: "gamma"
        Password: "prod/rds/epifiminerva/comms"
      auth:
        RepoName: "gamma"
        Password: "prod/rds/epifiminerva/auth"
      kyc:
        RepoName: "gamma"
        Password: "prod/rds/epifiminerva/kyc_admin_user"
      kyc_non_resident:
        RepoName: "gamma"
        Password: "prod/rds/epifiminerva/kyc_non_resident_admin_user"

  - Name: "jarvis"
    Type: "postgres"
    DbConfTemplate:
      AppName: "run-migration-job"
      EnableDebug: true
      SSLMode: "verify-full"
      SSLRootCert: "prod/rds/rds-ca-root-2061"
      MaxOpenConn: 1
      MaxIdleConn: 1
      StatementTimeout: -1s
      IdleInTxnSessionTimeout: -1s
      GormV2:
        LogLevelGormV2: "INFO"
        SlowQueryLogThreshold: 200ms
        UseInsecureLog: true
    Dbs:
      jarvis:
        RepoName: "gamma"
        Password: "prod/rds/prod-jarvis/jarvis_admin_user"

  - Name: "plutus"
    Type: "postgres"
    DbConfTemplate:
      AppName: "run-migration-job"
      EnableDebug: true
      SSLMode: "verify-full"
      SSLRootCert: "prod/rds/rds-ca-root-2061"
      MaxOpenConn: 1
      MaxIdleConn: 1
      StatementTimeout: -1s
      IdleInTxnSessionTimeout: -1s
      GormV2:
        LogLevelGormV2: "INFO"
        SlowQueryLogThreshold: 200ms
        UseInsecureLog: true
    Dbs:
      timeline:
        RepoName: "gamma"
        Password: "prod/rds/prod-plutus/timeline_admin_user"
      actor:
        RepoName: "gamma"
        Password: "prod/rds/prod-plutus/actor_admin_user"
      payment_instrument:
        RepoName: "gamma"
        Password: "prod/rds/prod-plutus/payment_instrument_admin_user"
      merchant:
        RepoName: "gamma"
        Password: "prod/rds/prod-plutus/merchant_admin_user"
      recurring_payment:
        RepoName: "gamma"
        Password: "prod/rds/prod-plutus/recurring_payment_admin_user"
      stock_guardian_tsp_recurring_payment:
        RepoName: "gamma"
        Password: "prod/rds/prod-plutus/stock_guardian_tsp_recurring_payment_admin_user"
      billpay:
        RepoName: "gamma"
        Password: "prod/rds/prod-plutus/billpay_admin_user"
