package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"sort"
	"strings"

	monitoringv1 "github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1"
	promqlLabel "github.com/prometheus/prometheus/model/labels"
	promqlParse "github.com/prometheus/prometheus/promql/parser"
	"github.com/samber/lo"
	"k8s.io/apimachinery/pkg/util/yaml"

	"github.com/epifi/gamma/scripts/missing_alerts/services"
)

// Variables for Config logic
var (
	hermesToken = "********************************************************" //nolint: gosec
	// using onboarding alerts slack credentials by default
	channelId        = flag.String("ChannelId", "C01NTC58Z35", "channel id to send slack alerts to")
	oncallId         = flag.String("OncallId", "", "on-call id to send alerts to")
	team             = flag.String("Team", "onboarding", "Team name which is expected to be labelled with alert")
	enableSlackAlert = flag.Bool("EnableSlackAlert", false, "Flag to enable slack alert")

	// Relative file paths of protos (service.proto) with respect to protos directory
	// Can add ** to read all services in a directory
	// todo: make this part of alertsProcessor
	protosRelativeFilePaths = []string{
		"api/auth/**",
		"api/user/**",
		"api/kyc/**",
		"api/frontend/account/signup/service.proto",
		"api/vendorgateway/ckyc/**",
		"api/vendorgateway/openbanking/auth/**",
	}
)

// Variables for implementation
var (
	ignoreAlertList = make([]string, 0)
	// todo: add another filter option by team
	ignoreAlertsMap = map[string]services.AlertsProcessor{
		"auth": services.AuthIgnoreAlerts,
		"user": services.UserIgnoreAlerts,
		"kyc":  services.KycIgnoreAlerts,
	}

	// nolint: unparam
	_ = func() struct{} {
		for _, v := range ignoreAlertsMap {
			ignoreAlertList = append(ignoreAlertList, v.IgnoreAlertsForRPCs()...)
		}
		return struct{}{}
	}()
)

func handleMissingAlerts(alertsExistsMap map[string]bool) {
	// Key is service, and value is list of RPCs
	missingAlerts := make(map[string][]string)
	for key, value := range alertsExistsMap {
		if value {
			continue
		}
		serviceName, rpcName := splitLastDot(key)
		missingAlerts[serviceName] = append(missingAlerts[serviceName], rpcName)
	}
	sortedMissingAlertNames := lo.Keys(missingAlerts)
	sort.Strings(sortedMissingAlertNames)
	// Print missing alerts
	missingAlertsResult := "Missing Alerts \n"
	for _, key := range sortedMissingAlertNames {
		rpcNames := missingAlerts[key]
		missingAlertsResult = missingAlertsResult + key + " \n"
		for _, rpcName := range rpcNames {
			missingAlertsResult = missingAlertsResult + "\t" + rpcName + "\n"
		}
	}
	fmt.Printf("%v", missingAlertsResult)
	sendSlackNotification(*channelId, *oncallId, missingAlertsResult, *enableSlackAlert)
}

// nolint: funlen
func getRPCsWithAlerts() []string {
	rpcsWithAlerts := make([]string, 0)
	serviceRPCMp := getServiceRPCMap()
	alertsAbsoluteFilePaths := getAlertsAbsoluteFilePaths()
	for _, file := range alertsAbsoluteFilePaths {
		// nolint: gosec
		content, readErr := os.ReadFile(file)
		if readErr != nil {
			log.Fatalf("Error reading file %s: %v", file, readErr)
		}

		var ruleFile monitoringv1.PrometheusRuleSpec
		if err := yaml.Unmarshal(content, &ruleFile); err != nil {
			log.Fatalf("Error parsing YAML file %s: %v", file, err)
		}

		for _, group := range ruleFile.Groups {
			for _, rule := range group.Rules {
				if rule.Labels["team"] == *team {
					// Parse the expression
					parsedExpr, parseErr := promqlParse.ParseExpr(rule.Expr.String())
					if parseErr != nil {
						log.Fatalf("Error parsing expression: %v\n", parseErr)
					}

					isFirstVector := false
					// Function to recursively walk through the AST
					promqlParse.Inspect(parsedExpr, func(node promqlParse.Node, path []promqlParse.Node) error {
						// Read only first vector selector, as we are assessing if alert is setup for a RPC only based on the first vector statement in the rule
						if isFirstVector {
							return nil
						}
						n, ok := node.(*promqlParse.VectorSelector)
						if !ok {
							// ignoring nodes of other types
							return nil
						}
						isFirstVector = true
						var (
							methodMatcher  *promqlLabel.Matcher
							serviceMatcher *promqlLabel.Matcher
						)
						for _, v := range n.LabelMatchers {
							if v.Name == "grpc_method" {
								methodMatcher = v
							}
							if v.Name == "grpc_service" {
								serviceMatcher = v
							}
						}
						// invalid expression
						if serviceMatcher == nil {
							return nil
						}

						if serviceMatcher.Type == promqlLabel.MatchEqual || serviceMatcher.Type == promqlLabel.MatchRegexp {
							// Alerts present for all RPCs
							if methodMatcher == nil {
								methods := serviceRPCMp[serviceMatcher.Value]
								for _, method := range methods {
									rpcsWithAlerts = append(rpcsWithAlerts, serviceMatcher.Value+"."+method)
								}
								return nil
							}

							switch methodMatcher.Type {
							case promqlLabel.MatchRegexp:
								// todo: fix this, need to do regex comparison
								methods := strings.Split(methodMatcher.Value, "|")
								for _, method := range methods {
									rpcsWithAlerts = append(rpcsWithAlerts, serviceMatcher.Value+"."+method)
								}
							case promqlLabel.MatchNotRegexp:
								// todo: fix this, need to do regex comparison
								methods := strings.Split(methodMatcher.Value, "|")
								svcMethods := serviceRPCMp[serviceMatcher.Value]
								for _, svcMethod := range svcMethods {
									if lo.Contains(methods, svcMethod) {
										continue
									}
									rpcsWithAlerts = append(rpcsWithAlerts, serviceMatcher.Value+"."+svcMethod)
								}

							case promqlLabel.MatchEqual:
								rpcsWithAlerts = append(rpcsWithAlerts, serviceMatcher.Value+"."+methodMatcher.Value)
							case promqlLabel.MatchNotEqual:
								svcMethods := serviceRPCMp[serviceMatcher.Value]
								for _, svcMethod := range svcMethods {
									if methodMatcher.Value == svcMethod {
										continue
									}
									rpcsWithAlerts = append(rpcsWithAlerts, serviceMatcher.Value+"."+svcMethod)
								}
							}
							return nil
						}
						return nil
					})
				}
			}
		}
	}
	return lo.Uniq(rpcsWithAlerts)
}

func main() {
	alertsExistsMap := getAlertsExistsMap()
	rpcsWithAlerts := getRPCsWithAlerts()

	for _, rpc := range rpcsWithAlerts {
		alertsExistsMap[rpc] = true
	}
	handleMissingAlerts(alertsExistsMap)
}
