package main

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"flag"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"

	actorPb "github.com/epifi/gamma/api/actor"
	commsPb "github.com/epifi/gamma/api/comms"
	p2pPb "github.com/epifi/gamma/api/p2pinvestment"
	vgP2pPb "github.com/epifi/gamma/api/vendorgateway/investments/p2p"
	p2pModel "github.com/epifi/gamma/p2pinvestment/dao/model"
	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/aws/session"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/scripts/p2p_investment_send_yearly_account_statement/config"
)

var (
	investorIds   = flag.String("investorId", "", "vendor investorId of the user")
	financialYear = flag.String("financialYear", "23-24", "financial year for which we want to generate. Eg: 22-23")
)

const (
	FromEmailId   = "<EMAIL>"
	FromEmailName = "Fi Money"
)

func newS3Client(env, region, bucket string) *s3.Client {
	awsSess, err := session.NewSession(env, region)
	if err != nil {
		panic(err)
	}
	return s3.NewClient(awsSess, bucket)
}

func main() {
	flag.Parse()
	// Get environment
	envName, err := cfg.GetEnvironment()
	if err != nil {
		panic(fmt.Errorf("failed to get environment: %w", err))
	}

	investorIdList := strings.Split(*investorIds, ",")
	if len(investorIdList) == 0 {
		panic(fmt.Errorf("investor id list cannot be empty"))
	}
	// Setup logger
	logger.Init(envName)
	defer func() { _ = logger.Log.Sync() }()

	// Load configuration
	conf, err := config.Load()
	if err != nil {
		panic(err)
	}
	ctx := context.Background()

	llDb, err := storageV2.NewCRDBWithConfig(conf.P2PInvLLDb, false)
	if err != nil {
		logger.Panic("failed to load p2p ll DB", zap.Error(err))
		panic(err)
	}
	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(vgConn)
	vgP2PClient := vgP2pPb.NewP2PClient(vgConn)

	commsConn := epifigrpc.NewConnByService(cfg.COMMS_SERVICE)
	defer epifigrpc.CloseConn(commsConn)
	commsClient := commsPb.NewCommsClient(commsConn)

	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	defer epifigrpc.CloseConn(actorConn)
	actorClient := actorPb.NewActorClient(actorConn)

	p2pS3Client := newS3Client(conf.Application.Environment, conf.Aws.Region, conf.S3BucketP2PInvestmentLedger)

	doOnce := once.NewDoOnce(llDb)
	successCount := 0
	for _, investorId := range investorIdList {
		investor := getInvestor(llDb, investorId)
		if investor == nil {
			logger.Error(ctx, "investor not found", zap.String("investorId", investorId))
			continue
		}
		taskStringForEmail := generateTaskStringForEmail(investor.VendorInvestorId.GetValue(), *financialYear, "1")
		alreadyDone, dErr := doOnce.IsDone(ctx, taskStringForEmail)
		if dErr != nil {
			logger.Error(ctx, "error checking if task is done", zap.String("investorId", investor.VendorInvestorId.GetValue()))
			continue
		}
		if alreadyDone {
			logger.Info(ctx, "message already sent", zap.String("investorId", investor.VendorInvestorId.GetValue()))
			successCount++
			continue
		}
		resp, rErr := vgP2PClient.DownloadYearlyStatement(ctx, &vgP2pPb.DownloadYearlyStatementRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_LIQUILOANS,
			},
			FinancialYear:    *financialYear,
			VendorInvestorId: investorId,
		})
		if e := epifigrpc.RPCError(resp, rErr); e != nil {
			if resp.GetStatus().GetCode() == uint32(vgP2pPb.DownloadYearlyStatementResponse_INVESTOR_NOT_APPLICABLE_FOR_YEARLY_STATEMENT) {
				logger.Info(ctx, "investor not applicable", zap.String("investorId", investorId))
				continue
			}
			if resp.GetStatus().IsRecordNotFound() {
				logger.Info(ctx, "document not found", zap.String("investorId", investorId))
				continue
			}
			logger.PanicWithCtx(ctx, "error while calling DownloadYearlyStatement", zap.Error(e))
			return
		}
		logger.InfoNoCtx("response", zap.Any("resp", resp))

		readResp, readErr := p2pS3Client.Read(resp.GetS3Path())
		if readErr != nil {
			logger.PanicWithCtx(ctx, "failed to read tax document from s3", zap.String("investorId", investor.VendorInvestorId.GetValue()), zap.Error(readErr))
			return
		}
		logger.Info(ctx, "successfully read file from s3 and converted to byes", zap.String(logger.ACTOR_ID_V2, investor.ActorId), zap.String(logger.P2P_INVESTOR_ID, investor.VendorInvestorId.GetValue()))

		sendDocFetchData, sendErr := getSendDocFetchDataForEmail(ctx, investor, actorClient, *financialYear)
		if sendErr != nil {
			logger.Error(ctx, "error creating data for email", zap.Error(sendErr), zap.String("investorId", investorId))
			continue
		}
		fyYear := *financialYear
		emailAttachment := &DocumentData{
			DocumentByteArr: readResp,
			DocumentName:    fmt.Sprintf("Capital_Gains_20%s_%s_Jump.pdf", fyYear[0:2], fyYear[3:5]),
		}
		emailErr := sendEmailToUser(ctx, commsClient, doOnce, investor, taskStringForEmail, emailAttachment, sendDocFetchData)
		if emailErr != nil {
			logger.Error(ctx, "error in sending email", zap.Error(emailErr), zap.String("investorId", investorId))
			continue
		}
		logger.Info(ctx, "email sent to the investor", zap.String(logger.ACTOR_ID_V2, investor.ActorId), zap.String(logger.P2P_INVESTOR_ID, investor.VendorInvestorId.GetValue()))
		successCount++
	}
	logger.Info(ctx, fmt.Sprintf("Send Jump Documents, success: %d, total: %d", successCount, len(investorIdList)))
}

func sendEmailToUser(ctx context.Context, commsClient commsPb.CommsClient, doOnce once.DoOnce, investor *p2pModel.Investor, taskStringForEmail string, emailAttachment *DocumentData, sendDocFetchData *SendDocumentFetchData) error {
	sendNotificationFunc := func() error {
		err := sendEmail(ctx, commsClient, emailAttachment, sendDocFetchData)
		if err != nil {
			return err
		}
		logger.Info(ctx, "sending email to the investor", zap.String(logger.ACTOR_ID_V2, investor.ActorId),
			zap.String(logger.P2P_INVESTOR_ID, investor.VendorInvestorId.GetValue()))
		return nil
	}
	doOnceTaskError := doOnce.DoOnceFn(ctx, taskStringForEmail, sendNotificationFunc)
	if doOnceTaskError != nil {
		logger.ErrorNoCtx("DoOnceFn giving error", zap.Error(doOnceTaskError), zap.String(logger.ACTOR_ID_V2, investor.ActorId))
		return doOnceTaskError
	}
	return nil
}

func sendEmail(ctx context.Context, commsClient commsPb.CommsClient, emailAttachment *DocumentData, sendDocFetchData *SendDocumentFetchData) error {
	attachment := &commsPb.EmailMessage_Attachment{
		FileContent:    emailAttachment.DocumentByteArr,
		FileName:       emailAttachment.DocumentName,
		Disposition:    commsPb.Disposition_ATTACHMENT,
		AttachmentType: "application/pdf",
	}
	emailReq := &commsPb.SendMessageRequest{
		Type:   commsPb.QoS_BEST_EFFORT,
		Medium: commsPb.Medium_EMAIL,
		UserIdentifier: &commsPb.SendMessageRequest_EmailId{
			EmailId: sendDocFetchData.EmailId,
		},
		Message: &commsPb.SendMessageRequest_Email{
			Email: &commsPb.EmailMessage{
				FromEmailId:   FromEmailId,
				FromEmailName: FromEmailName,
				EmailOption: &commsPb.EmailOption{
					Option: &commsPb.EmailOption_JumpYearlyStatementEmailOption{
						JumpYearlyStatementEmailOption: &commsPb.JumpYearlyStatementEmailOption{
							EmailType: commsPb.EmailType_JUMP_YEARLY_ACCOUNT_STATEMENT_EMAIL,
							Option: &commsPb.JumpYearlyStatementEmailOption_JumpYearlyStatementEmailOptionV1{
								JumpYearlyStatementEmailOptionV1: &commsPb.JumpYearlyStatementEmailOptionV1{
									TemplateVersion:  commsPb.TemplateVersion_VERSION_V1,
									FinancialYearStr: sendDocFetchData.FinancialYear,
									FirstName:        sendDocFetchData.FirstName,
								},
							},
						},
					},
				},
				Attachment: []*commsPb.EmailMessage_Attachment{attachment},
			}},
	}
	sendMsgResp, sendMsgErr := commsClient.SendMessage(ctx, emailReq)
	if rpcErr := epifigrpc.RPCError(sendMsgResp, sendMsgErr); rpcErr != nil {
		return fmt.Errorf("failed to send email : %w", rpcErr)
	}
	return nil
}

func sendWhatsappMessage(ctx context.Context, lldb *gorm.DB, p2pInvestmentClient p2pPb.P2PInvestmentClient, actorClient actorPb.ActorClient, commsClient commsPb.CommsClient, doOnce once.DoOnce,
	investor *p2pModel.Investor, taskString string, path string) error {
	sendNotificationFunc := func() error {
		err := sendMessage(ctx, lldb, investor, p2pInvestmentClient, actorClient, commsClient, path)
		if err != nil {
			return err
		}
		logger.Info(ctx, "sending whatsapp message to the investor", zap.String(logger.ACTOR_ID_V2, investor.ActorId),
			zap.String(logger.P2P_INVESTOR_ID, investor.VendorInvestorId.GetValue()))
		return nil
	}
	doOnceTaskError := doOnce.DoOnceFn(ctx, taskString, sendNotificationFunc)
	if doOnceTaskError != nil {
		logger.ErrorNoCtx("DoOnceFn giving error", zap.Error(doOnceTaskError), zap.String(logger.ACTOR_ID_V2, investor.ActorId))
		return doOnceTaskError
	}
	return nil
}

func sendMessage(ctx context.Context, lldb *gorm.DB, investor *p2pModel.Investor, p2pInvestmentClient p2pPb.P2PInvestmentClient, actorClient actorPb.ActorClient,
	commsClient commsPb.CommsClient, s3Path string) error {
	getEntityDetailsByActorIdRequest := &actorPb.GetEntityDetailsByActorIdRequest{ActorId: investor.ActorId}
	getEntityDetailsByActorIdResponse, err := actorClient.GetEntityDetailsByActorId(ctx, getEntityDetailsByActorIdRequest)
	if te := epifigrpc.RPCError(getEntityDetailsByActorIdResponse, err); te != nil {
		logger.Error(ctx, "error in fetching actor entity id", zap.Error(te))
		return errors.Wrap(te, "failed to fetch actor entity id")
	}
	dashboardRes, resErr := p2pInvestmentClient.GetInvestmentDashboard(ctx, &p2pPb.GetInvestmentDashboardRequest{
		ActorId: investor.ActorId,
	})
	if te := epifigrpc.RPCError(dashboardRes, resErr); te != nil {
		logger.Error(ctx, "error in fetching investment summary ", zap.Error(te))
		return errors.Wrap(te, "failed to fetch investment summary")
	}
	userId := getEntityDetailsByActorIdResponse.GetEntityId()
	totalInvestedAmount := money.ToDisplayStringInIndianFormat(money.FromPaisa(getTotalInvestedAmount(lldb, investor.Id)*100), 2, true)
	currentInvestmentAmount := money.ToDisplayStringInIndianFormat(dashboardRes.GetInvestmentData().GetTotalPrincipalAmount(), 2, true)
	whatsappReqMessage := &commsPb.SendMessageRequest_Whatsapp{
		Whatsapp: &commsPb.WhatsappMessage{
			WhatsappOption: &commsPb.WhatsappOption{
				Option: &commsPb.WhatsappOption_JumpYearlyAccountStatementWhatsappOption{
					JumpYearlyAccountStatementWhatsappOption: &commsPb.JumpYearlyAccountStatementWhatsappOption{
						WhatsappType: commsPb.WhatsappType_JUMP_YEARLY_ACCOUNT_STATEMENT,
						Option: &commsPb.JumpYearlyAccountStatementWhatsappOption_JumpYearlyAccountStatementWhatsappOptionV1{
							JumpYearlyAccountStatementWhatsappOptionV1: &commsPb.JumpYearlyAccountStatementWhatsappOptionV1{
								TemplateVersion:       commsPb.TemplateVersion_VERSION_V1,
								MediaContentType:      commsPb.WhatsappMediaContentType_WHATSAPP_MEDIA_CONTENT_TYPE_DOCUMENT_PDF,
								MediaUrl:              s3Path,
								TotalInvestedAmount:   totalInvestedAmount,
								CurrentInvestedAmount: currentInvestmentAmount,
							},
						},
					},
				}},
		}}
	sendMessageRequest := &commsPb.SendMessageRequest{
		Type:           commsPb.QoS_BEST_EFFORT,
		Medium:         commsPb.Medium_WHATSAPP,
		UserIdentifier: &commsPb.SendMessageRequest_UserId{UserId: userId},
		Message:        whatsappReqMessage,
	}
	res, err := commsClient.SendMessage(ctx, sendMessageRequest)
	if te := epifigrpc.RPCError(res, err); te != nil {
		logger.Error(ctx, "error in sending whatsapp message", zap.Error(te))
		return errors.Wrap(te, "failed to send message")
	}
	return nil
}

func getInvestor(llDb *gorm.DB, vendorInvestorId string) *p2pModel.Investor {
	investor := &p2pModel.Investor{}
	if err := llDb.Where("vendor_investor_id = ?", vendorInvestorId).First(&investor).Error; err != nil {
		logger.Panic("error fetching user from db", zap.Error(err))
	}
	return investor
}

func generateTaskString(vendorInvestorId string, fYear string, tryCount string) string {
	return fmt.Sprintf("whatsapp_msg_%v_%v_%v", vendorInvestorId, fYear, tryCount)
}

func generateTaskStringForEmail(vendorInvestorId string, fYear string, tryCount string) string {
	return fmt.Sprintf("email_msg_%v_%v_%v", vendorInvestorId, fYear, tryCount)
}

/*
		Query being run
		select sum(computed_transaction_amount) as invested_transaction_amount
		from p2p_investment_transactions
		where investor_id = 'P2PIVRlcGWXCmXRTij56wEp0T/Sw230531=='
		and type = 'INVESTMENT_TRANSACTION_TYPE_INVESTMENT' and status = 'SUCCESS'
	  	and created_at >= '2022-04-01' and created_at <= '2023-03-31';

		TODO: Derive from and to date from financial year
*/
func getTotalInvestedAmount(llDb *gorm.DB, investorId string) int64 {
	txnAmount := struct {
		ComputedTransactionAmount int64
	}{}
	if err := llDb.Raw("select sum(computed_transaction_amount) as computed_transaction_amount from p2p_investment_transactions where investor_id = ? "+
		"and type = 'INVESTMENT_TRANSACTION_TYPE_INVESTMENT' "+
		"and status = 'SUCCESS' and "+
		"created_at >= '2022-04-01' and created_at <= '2023-03-31'", investorId).Scan(&txnAmount).Error; err != nil {
		logger.Panic("error fetching total invested amount from db", zap.Error(err))
	}
	return txnAmount.ComputedTransactionAmount
}

type DocumentData struct {
	DocumentByteArr []byte
	DocumentName    string
}

type SendDocumentFetchData struct {
	FinancialYear string
	EmailId       string
	FirstName     string
}

// getSendDocFetchDataForEmail fetches user data required to send email
// e.g. user email, financial year
func getSendDocFetchDataForEmail(ctx context.Context, investor *p2pModel.Investor, actorClient actorPb.ActorClient, financialYear string) (*SendDocumentFetchData, error) {
	getEntityDetailsByActorIdResponse, err := actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{
		ActorId: investor.ActorId,
	})
	if te := epifigrpc.RPCError(getEntityDetailsByActorIdResponse, err); te != nil {
		logger.Error(ctx, "error in fetching actor entity id", zap.Error(te))
		return nil, errors.Wrap(te, "failed to fetch actor entity id")
	}
	fyYear := fmt.Sprintf("20%s-20%s", financialYear[0:2], financialYear[3:5])
	return &SendDocumentFetchData{
		EmailId:       getEntityDetailsByActorIdResponse.GetEmailId(),
		FinancialYear: fyYear,
		FirstName:     getEntityDetailsByActorIdResponse.GetName().GetFirstName(),
	}, nil
}
