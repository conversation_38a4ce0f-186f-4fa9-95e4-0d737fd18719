@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools{
        go 'Go stable'
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '10'))
    }
    parameters {
        string(name: 'filePath', defaultValue: '', description: 'Relative file path from scripts/Pay/delete_merchant_pi_resolution')
        booleanParam(name: 'REFRESH', defaultValue: false, description: '<PERSON><PERSON>, ignore this variable! This is for DevOps to refresh Jenkinsfile.')
    }
    environment {
        ENV="prod"
        TARGET="Pay/delete_merchant_pi_resolution"
        VERSION="6.0.0"
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true}
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-REFRESH:${params.REFRESH}"
                    currentBuild.description = "#${BUILD_NUMBER}-REFRESH:${params.REFRESH}"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Fetch Binary') {
            steps {
                dir('app'){
                    script{
                    sh "aws s3 cp s3://epifi-gamma-binaries-prod/${TARGET}/linux/amd64/${VERSION} ./${TARGET}.zip"
                    sh "unzip -o ${TARGET}.zip"
                    }
                }
            }
        }
        stage("Execute Binary") {
            steps {
                dir('app') {
                    script {
                        sh """
                            cd ${TARGET}
                            chmod 700 ${TARGET}_bin
                            ENVIRONMENT=prod DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP=true ./${TARGET}_bin -filePath=${filePath}
                        """
                    }
                }
            }
        }
    }
}
