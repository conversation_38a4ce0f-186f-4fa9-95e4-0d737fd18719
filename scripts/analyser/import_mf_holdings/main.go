package main

import (
	"context"
	"flag"
	"fmt"
	"os"

	"go.uber.org/zap"

	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

var (
	holdingsImportStep = flag.String("holdingsImportStep", "", "Step in mf holdings import process - initiate_import or trigger_import")
	actorId            = flag.String("actorId", "", "Actor for which holdings import is to be triggered")
	otp                = flag.String("otp", "", "Otp for verify otp step")
	external_id        = flag.String("externalId", "", "Enternal Id for verify otp step")

	mfExternalOrdersClient mfExternalPb.MFExternalOrdersClient
)

const (
	initiateimport = "initiate_import"
	triggerimport  = "trigger_import"
	createrequest  = "create_request"
)

func validateFlags() error {
	if *holdingsImportStep == "" {
		return fmt.Errorf("holdingsImportStep cannot be empty")
	}

	switch *holdingsImportStep {
	case createrequest:
		if *actorId == "" {
			return fmt.Errorf("actor id cannot be empty for create_request step")
		}

	case initiateimport:
		if *actorId == "" {
			return fmt.Errorf("actor id cannot be empty for initiate_import step")
		}
		if *external_id == "" {
			return fmt.Errorf("external_id cannot be empty for verify_otp step")
		}
	case triggerimport:
		if *actorId == "" {
			return fmt.Errorf("actor id cannot be empty for trigger_import step")
		}
		if *otp == "" {
			return fmt.Errorf("otp cannot be empty for verify_otp step")
		}
		if *external_id == "" {
			return fmt.Errorf("external_id cannot be empty for verify_otp step")
		}
	}

	return nil
}

// script is used to manually trigger mf holdings import for an actor
func main() {
	ctx := context.Background()
	flag.Parse()

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}
	logger.Init(env)
	defer func() { _ = logger.Log.Sync() }()

	if err := validateFlags(); err != nil {
		logger.Error(ctx, "failed to validate params", zap.Error(err))
		os.Exit(2)
	}

	investmentConn := epifigrpc.NewConnByService(cfg.INVESTMENT_SERVICE)
	defer epifigrpc.CloseConn(investmentConn)
	mfExternalOrdersClient = mfExternalPb.NewMFExternalOrdersClient(investmentConn)

	switch *holdingsImportStep {
	case createrequest:
		err = createRequest(ctx, *actorId)
	case initiateimport:
		err = initiateImport(ctx, *actorId, *external_id)
	case triggerimport:
		err = triggerImport(ctx, *actorId, *otp, *external_id)
	}
	if err != nil {
		logger.Error(ctx, "failed to import mf holdings", zap.Error(err))
		os.Exit(2)
	}
}

// initiateImport for mf holdings
func initiateImport(ctx context.Context, actorId, externalId string) error {
	res, err := mfExternalOrdersClient.InitiateHoldingsImport(ctx, &mfExternalPb.InitiateHoldingsImportRequest{ActorId: actorId, ExternalId: externalId})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "failed to initiate mf holdings import for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return fmt.Errorf("failed to init holdings import: %w", rpcErr)
	}
	logger.Info(ctx, fmt.Sprintf("mf holdings import initiated: external id %s", res.GetExternalId()))
	return nil
}

// triggerImport for mf holdings using otp
func triggerImport(ctx context.Context, actorId, otp, externalId string) error {
	res, err := mfExternalOrdersClient.TriggerHoldingsImportFetch(ctx, &mfExternalPb.TriggerHoldingsImportFetchRequest{
		ActorId:    actorId,
		ExternalId: externalId,
		Otp:        otp,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "failed to trigger mf holdings import for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return fmt.Errorf("faield to trigger holdings import: %w", rpcErr)
	}
	logger.Info(ctx, fmt.Sprintf("mf holdings import triggered: isOtpVerified %t", res.GetIsOtpVerified()))
	return nil
}

func createRequest(ctx context.Context, actorId string) error {
	res, err := mfExternalOrdersClient.CreateMFHoldingsImport(ctx, &mfExternalPb.CreateMFHoldingsImportRequest{ActorId: actorId})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "failed to CreateMFHoldingsImport for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return fmt.Errorf("failed to CreateMFHoldingsImport: %w", rpcErr)
	}
	logger.Info(ctx, fmt.Sprintf("mf holdings import request is created: external id %s", res.GetExternalId()))
	return nil
}
