Application:
  Environment: "demo"

Aws:
  Region: "ap-south-1"

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  DbType: "CRDB"
  SSLMode: "verify-full"
  SSLRootCert: "demo/cockroach/ca.crt"
  SSLClientCert: "demo/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "demo/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 50
  MaxIdleConn: 14
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

EpifiWealthDb:
  Username: "epifi_wealth_dev_user"
  Password: ""
  DbType: "CRDB"
  Name: "epifi_wealth"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "demo/cockroach/ca.crt"
  SSLClientCert: "demo/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "demo/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

S3Conf:
  Bucket: "epifi-demo-wealth-onboarding"
  LivenessPath: "liveness"
  DownloadedKraDocS3Path: "downloaded_kra_doc"

ActorDb:
  Name: "actor"
  DbType: "PGDB"
  DbServerAlias: "PLUTUS_RDS"
  EnableDebug: false
  SSLMode: "disable"
  AppName: "actor"
  SecretName: "demo/rds/epifiplutus/actor_dev_user"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
