// nolint:funlen,goimports
package order

import (
	"context"
	"errors"
	"fmt"

	gormv2 "gorm.io/gorm"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	orderPb "github.com/epifi/gamma/api/order"
	domainPb "github.com/epifi/gamma/api/order/domain"
	"github.com/epifi/gamma/order/workflow"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

var (
	ignoredWorkflows = []orderPb.OrderWorkflow{
		orderPb.OrderWorkflow_B2C_FUND_TRANSFER,
		orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
		orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT,
		orderPb.OrderWorkflow_MODIFY_RECURRING_PAYMENT,
		orderPb.OrderWorkflow_REVOKE_RECURRING_PAYMENT,
	}
)

// ForceProcessOrder is a sync RPC to be used to force order order processing at any given
// point of time.
//
// Generally order processing happens abides by the retry strategies defined in the workflow config.
// However, in certain scenarios we might want to force order processing due to business requirement.
// This can be due various reasons but not limited to -
//  1. Enquire and move order to terminal state as next attempt is scheduled after X hrs.
//  2. Enquire and update order in MANUAL_INTERVENTION post it has been processed by domain service.
//  3. Enquire and update order in terminal state. In case there is a discrepancy between order state
//     and domain state
//
// NOTE: the RPC is meant to be triggered manually either using some script or from sherlock
func (s *Service) ForceProcessOrder(ctx context.Context, req *orderPb.ForceProcessOrderRequest) (*orderPb.ForceProcessOrderResponse, error) {
	var (
		res       = &orderPb.ForceProcessOrderResponse{}
		reqHeader = &domainPb.DomainRequestHeader{ShouldForceProcessOrder: true}
	)
	// Step 1: fetch order from DB
	order, err := s.orderDao.GetById(ctx, req.GetOrderId())
	if errors.Is(err, gormv2.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound) {
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	}

	if err != nil {
		logger.Error(ctx, "failed to fetch order", zap.String(logger.ORDER_ID, req.GetOrderId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	for _, workflowType := range ignoredWorkflows {
		if order.GetWorkflow() == workflowType {
			logger.Error(ctx, "workflow moved to celestial, invalid force process order", zap.String(logger.WORKFLOW, order.GetWorkflow().String()))
			res.Status = rpc.StatusPermissionDenied()
			return res, nil
		}
	}

	// Step 2: fetch order current stage
	currentStage, err := s.getCurrentOrderStage(ctx, order)
	if err != nil {
		logger.Error(ctx, "failed to determine current order stage", zap.String(logger.ORDER_ID, req.GetOrderId()),
			zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// Step 3: finalize the stage to be attempts based on order status
	finalStage, err := s.getNextStageIfApplicable(order, currentStage)
	if err != nil {
		logger.Error(ctx, "failed to determine order stage to make new processing attempt",
			zap.String(logger.ORDER_ID, req.GetOrderId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	switch {
	// setting last attempt as true for any attempt made after manual intervention as we dont want to trigger
	// new request altogether from the domain. Only enquiry is required.
	case order.GetStatus() == orderPb.OrderStatus_MANUAL_INTERVENTION:
		reqHeader.IsLastAttempt = true

	case finalStage == currentStage && workflow.IsTerminalStatusForStageV1(currentStage, order.GetStatus()):
		if !req.EnableProcessingAfterTermination {
			logger.Info(ctx, "order already in terminal state",
				zap.String(logger.ORDER_STATUS, order.GetStatus().String()), zap.String(logger.ORDER_ID, req.GetOrderId()))
			res.Status = rpc.StatusFailedPrecondition()
			return res, nil
		}

		reqHeader.IsLastAttempt = true
		// setting ShouldOverrideTerminalState to notify domain that the intent is to force update post enquiry
		reqHeader.ShouldOverrideTerminalState = true
	}

	// Step 5: call domain service and update based on the response.
	// error is ignored here as method returns error even in case processing is successful
	// this is because the method is common for the queue based consumer and there even on
	// when domain returns proper response we send the same to the
	_ = s.engine.ForceAttemptWithOrderDomain(ctx, order, finalStage, reqHeader)
	res.OrderStatus = order.GetStatus()
	res.Status = rpc.StatusOk()
	return res, nil
}

// getCurrentOrderStage fetches current order stage depending on whether order is in MANUAL_INTERVENTION vs other statuses
func (s *Service) getCurrentOrderStage(ctx context.Context, order *orderPb.Order) (orderPb.OrderStage, error) {
	if order.GetStatus() == orderPb.OrderStatus_MANUAL_INTERVENTION {
		stage, err := s.engine.GetManualInterventionOrderStage(ctx, order.GetId())
		if err != nil {
			return orderPb.OrderStage_ORDER_STAGE_UNSPECIFIED,
				fmt.Errorf("failed to get order stage for order in manual intervention: %w", err)
		}

		return stage, nil
	}

	stage, err := workflow.GetOrderStageFromStatusV1(order.GetStatus())
	if err != nil {
		return orderPb.OrderStage_ORDER_STAGE_UNSPECIFIED,
			fmt.Errorf("failed to get order stage: from status: %s : %w", order.GetStatus().String(), err)

	}

	return stage, nil
}

// getNextStageIfApplicable returns next order stage if order is in terminal state for the stage and next stage exists
// for the workflow, else current stage is returned
func (s *Service) getNextStageIfApplicable(order *orderPb.Order, stage orderPb.OrderStage) (orderPb.OrderStage, error) {
	if order.GetStatus() != orderPb.OrderStatus_MANUAL_INTERVENTION &&
		workflow.IsTerminalStatusForStageV1(stage, order.GetStatus()) {
		nextStage, nextErr := workflow.GetNextStage(order.GetWorkflow(), order.GetStatus())
		if errors.Is(nextErr, workflow.ErrNoNextStage) {
			return stage, nil
		}

		if nextErr != nil {
			return orderPb.OrderStage_ORDER_STAGE_UNSPECIFIED, fmt.Errorf("failed to get order next stage: %w", nextErr)
		}

		return nextStage, nil
	}

	return stage, nil
}
