package metrics

const (
	ParsingDeletedAccTxn                        = "PARSING_DELETED_ACC_TXN"
	TxnEventConversionFailure                   = "TXN_EVENT_CONVERSION_FAILURE"
	SmartParserReturnedInternalError            = "SMART_PARSER_RETURNED_INTERNAL"
	SmartParserReturnedParsedFalse              = "SMART_PARSER_RETURNED_PARSED_FALSE"
	ParsedParticularVpaTypeWithInvalidVpa       = "PARSED_PARTICULAR_VPA_TYPE_WITH_INVALID_VPA"
	ParsedParticularVpaTypeErrorFetchingUpiPi   = "PARSED_PARTICULAR_VPA_TYPE_ERROR_FETCHING_UPI_PI"
	ParsedParticularAccTypeErrorFetchingAccPi   = "PARSED_PARTICULAR_ACC_TYPE_ERROR_FETCHING_ACC_PI"
	ParsedParticularPiIdTypeErrorFetchingByPiId = "PARSED_PARTICULAR_PI_TYPE_ERROR_FETCHING_BY_PI_ID"
	Unknown                                     = "UNKNOWN"
)

var (
	txnEventParsingLabels = []string{
		ParsingDeletedAccTxn,
		TxnEventConversionFailure,
		SmartParserReturnedInternalError,
		Unknown,
	}
	enrichedTxnWithDefaultValuesLabels = []string{
		ParsedParticularVpaTypeWithInvalidVpa,
		ParsedParticularVpaTypeErrorFetchingUpiPi,
		ParsedParticularAccTypeErrorFetchingAccPi,
		ParsedParticularPiIdTypeErrorFetchingByPiId,
		Unknown,
	}
)
