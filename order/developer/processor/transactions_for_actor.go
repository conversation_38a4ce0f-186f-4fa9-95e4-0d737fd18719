package processor

import (
	"context"
	"fmt"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/proto/json"

	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/developer"
	accPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/order/dao"
)

type TxnsForActorProcessor struct {
	orderDao        dao.OrderDao
	accountPiClient accPiPb.AccountPIRelationClient
}

func NewTxnsForActorProcessor(orderDao dao.OrderDao, accountPiClient accPiPb.AccountPIRelationClient) *TxnsForActorProcessor {
	return &TxnsForActorProcessor{
		orderDao:        orderDao,
		accountPiClient: accountPiClient,
	}
}

func (d *TxnsForActorProcessor) FetchParamList(ctx context.Context, entity developer.OrderEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            actorId,
			Label:           "actor ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
		{
			Name:            startDate,
			Label:           "start Date",
			Type:            db_state.ParameterDataType_TIMESTAMP,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
		{
			Name:            endDate,
			Label:           "end Date",
			Type:            db_state.ParameterDataType_TIMESTAMP,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
		{
			Name:            ownership,
			Label:           "Ownership",
			Type:            db_state.ParameterDataType_DROPDOWN,
			Options:         getOwnershipList(),
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *TxnsForActorProcessor) FetchData(ctx context.Context, entity developer.OrderEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", ErrNilFilter
	}
	var (
		err                        error
		actorIdVal                 string
		startDateTime, endDateTime time.Time
		currentActorPIs            []string
		orderWithTransactions      []*order.OrderWithTransactions
		orderWithTxnsResp          []proto.Message
		e                          []byte
		inputOwnership             commontypes.Ownership
	)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case actorId:
			actorIdVal = filter.GetStringValue()
		case startDate:
			startDateTime = filter.GetTimestamp().AsTime()
		case endDate:
			endDateTime = datetime.EndOfDay(filter.GetTimestamp().AsTime())
		case ownership:
			inputOwnership = commontypes.Ownership(commontypes.Ownership_value[filter.GetDropdownValue()])
		default:
			return "", fmt.Errorf("unknown parameter name %s", filter.GetParameterName())
		}
	}
	ctx = epificontext.WithOwnership(ctx, inputOwnership)

	actorResp, actorErr := d.accountPiClient.GetByActorId(ctx, &accPiPb.GetByActorIdRequest{ActorId: actorIdVal})
	if grpcErr := epifigrpc.RPCError(actorResp, actorErr); grpcErr != nil {
		return "", fmt.Errorf("GetByActorId failed %w", grpcErr)
	}

	for _, accPi := range actorResp.GetAccountPis() {
		currentActorPIs = append(currentActorPIs, accPi.GetPiId())
	}

	orderWithTransactions, err = d.orderDao.GetOrdersWithTransactionsForActorV1(
		ctx, actorIdVal, startDateTime, endDateTime, order.OrderStatus_ORDER_STATUS_UNSPECIFIED,
		10, 0, true, nil, nil, nil,
		nil, "", nil, currentActorPIs, dao.UtrFilter{},
	)

	if err != nil {
		return "", fmt.Errorf("GetOrdersWithTransactionsForActorV1 failed, error while fetching order and transaction details: %w", err)
	}

	for _, orderWithTransaction := range orderWithTransactions {
		orderWithTxnsResp = append(orderWithTxnsResp, redactOrderWIthTxns(orderWithTransaction))
	}
	e, err = json.Marshal(orderWithTxnsResp)
	if err != nil {
		return "", fmt.Errorf("cannot marshal order with transactions proto to json: %w", err)
	}
	return string(e), nil
}
