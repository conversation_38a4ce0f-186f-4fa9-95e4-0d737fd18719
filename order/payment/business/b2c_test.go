package business

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	lockMocks "github.com/epifi/be-common/pkg/lock/mocks"

	accountPb "github.com/epifi/gamma/api/accounts"
	orderPb "github.com/epifi/gamma/api/order"
	domainPb "github.com/epifi/gamma/api/order/domain"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	"github.com/epifi/gamma/api/vendorgateway"
	vgB2CPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/b2c"
	vgB2CPaymentMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/b2c/mocks"

	config "github.com/epifi/gamma/order/config/genconf"
	daoMocks "github.com/epifi/gamma/order/dao/mocks"
	piInternalMocks "github.com/epifi/gamma/order/internal/pi/mocks"
	"github.com/epifi/gamma/order/test/mocks"
	"github.com/epifi/gamma/pkg/pay/attributes"
)

type b2cServiceTestSuite struct {
	b2cServer paymentPb.BusinessServer
	db        *gormv2.DB
	conf      *config.Config
}

func NewB2CServiceTestSuite(server paymentPb.BusinessServer, db *gormv2.DB, config *config.Config) b2cServiceTestSuite {
	return b2cServiceTestSuite{
		b2cServer: server,
		db:        db,
		conf:      config,
	}
}

var (
	bts  b2cServiceTestSuite
	db   *gormv2.DB
	conf *config.Config
)

func TestService_MakeB2CPayment(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	ctr := gomock.NewController(t)
	mockTxnDao := daoMocks.NewMockTransactionDao(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockOvgB2CPaymentClient := vgB2CPaymentMocks.NewMockPaymentClient(ctr)
	mockSavingClient := savingsMocks.NewMockSavingsClient(ctr)
	mockILockManager := lockMocks.NewMockILockManager(ctr)
	mockILock := lockMocks.NewMockILock(ctr)

	bts.b2cServer = NewService(mockTxnDao, nil, mockPiClient, mockOvgB2CPaymentClient, mockSavingClient, conf.ErrorRespCodesForPermanentFailure(), conf, mockILockManager, nil, nil, nil)

	defer ctr.Finish()

	type mockGetTxnsOfOrder struct {
		enable  bool
		orderId string
		want    []*paymentPb.Transaction
		err     error
	}

	type mockILockManagerLock struct {
		enable        bool
		orderId       string
		leaseDuration time.Duration
		want1         *lockMocks.MockILock
		err           error
	}

	tests := []struct {
		name                 string
		req                  *domainPb.ProcessPaymentRequest
		want                 *domainPb.ProcessPaymentResponse
		mockGetTxnsOfOrder   mockGetTxnsOfOrder
		mockILockManagerLock mockILockManagerLock
		wantErr              bool
	}{
		// TODO: Add test cases.
		{
			name: "error while fetching txns for order",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
				Payload: nil,
			},
			mockGetTxnsOfOrder: mockGetTxnsOfOrder{
				enable:  true,
				orderId: "order-id-1",
				want:    nil,
				err:     errors.New("unexpected error"),
			},
			mockILockManagerLock: mockILockManagerLock{
				enable:        true,
				orderId:       "PROCESS_B2C_PAYMENT_LOCK:order-id-1",
				leaseDuration: conf.B2CPaymentParams().ProcessB2cPaymentLockLeaseDuration,
				want1:         mockILock,
				err:           nil,
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "initiateNewB2CPayment when transaction record not found for given orderId but got TRANSIENT_FAILURE as got error while unmarshalling nil reqPayload",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
				Payload: nil,
			},
			mockILockManagerLock: mockILockManagerLock{
				enable:        true,
				orderId:       "PROCESS_B2C_PAYMENT_LOCK:order-id-1",
				leaseDuration: conf.B2CPaymentParams().ProcessB2cPaymentLockLeaseDuration,
				want1:         mockILock,
				err:           nil,
			},
			mockGetTxnsOfOrder: mockGetTxnsOfOrder{
				enable:  true,
				orderId: "order-id-1",
				want:    nil,
				err:     gormv2.ErrRecordNotFound,
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "Got PERMANENT_FAILURE due to non-retryable failure as txn latest raw status code is 119",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
			},
			mockILockManagerLock: mockILockManagerLock{
				enable:        true,
				orderId:       "PROCESS_B2C_PAYMENT_LOCK:order-id-1",
				leaseDuration: conf.B2CPaymentParams().ProcessB2cPaymentLockLeaseDuration,
				want1:         mockILock,
				err:           nil,
			},
			mockGetTxnsOfOrder: mockGetTxnsOfOrder{
				enable:  true,
				orderId: "order-id-1",
				want: []*paymentPb.Transaction{
					{
						Id:          "txn-id-1",
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						Status:      paymentPb.TransactionStatus_FAILED,
						DetailedStatus: &paymentPb.TransactionDetailedStatus{
							DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
								{
									RawStatusCode: "119",
								},
							},
						},
					},
				},
				err: nil,
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "Got PERMANENT_FAILURE as txn status is failed and is last attempt",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   true,
				},
			},
			mockILockManagerLock: mockILockManagerLock{
				enable:        true,
				orderId:       "PROCESS_B2C_PAYMENT_LOCK:order-id-1",
				leaseDuration: conf.B2CPaymentParams().ProcessB2cPaymentLockLeaseDuration,
				want1:         mockILock,
				err:           nil,
			},
			mockGetTxnsOfOrder: mockGetTxnsOfOrder{
				enable:  true,
				orderId: "order-id-1",
				want: []*paymentPb.Transaction{
					{
						Id:     "txn-id-1",
						Status: paymentPb.TransactionStatus_FAILED,
					},
				},
				err: nil,
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "got success in case transaction is already in success state.",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
			},
			mockILockManagerLock: mockILockManagerLock{
				enable:        true,
				orderId:       "PROCESS_B2C_PAYMENT_LOCK:order-id-1",
				leaseDuration: conf.B2CPaymentParams().ProcessB2cPaymentLockLeaseDuration,
				want1:         mockILock,
				err:           nil,
			},
			mockGetTxnsOfOrder: mockGetTxnsOfOrder{
				enable:  true,
				orderId: "order-id-1",
				want: []*paymentPb.Transaction{
					{
						Id:     "txn-id-1",
						Status: paymentPb.TransactionStatus_SUCCESS,
					},
				},
				err: nil,
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "transient failure when acquire lock fails",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
			},
			mockILockManagerLock: mockILockManagerLock{
				enable:        true,
				orderId:       "PROCESS_B2C_PAYMENT_LOCK:order-id-1",
				leaseDuration: conf.B2CPaymentParams().ProcessB2cPaymentLockLeaseDuration,
				want1:         nil,
				err:           fmt.Errorf("acquire lock fail"),
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetTxnsOfOrder.enable {
				mockTxnDao.EXPECT().GetTxnsOfOrder(context.Background(), tt.mockGetTxnsOfOrder.orderId).Return(tt.mockGetTxnsOfOrder.want, tt.mockGetTxnsOfOrder.err)
			}

			if tt.mockILockManagerLock.enable {
				mockILockManager.EXPECT().GetLock(context.Background(), tt.mockILockManagerLock.orderId, tt.mockILockManagerLock.leaseDuration).Return(tt.mockILockManagerLock.want1, tt.mockILockManagerLock.err)
				if tt.mockILockManagerLock.want1 != nil {
					mockILock.EXPECT().Release(gomock.Any())
				}
			}

			got, err := bts.b2cServer.MakeB2CPayment(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("MakeB2CPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MakeB2CPayment() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_MakeB2CPayment_New(t *testing.T) {
	t.Parallel()

	const (
		orderId1        = "order-id-1"
		txnId1          = "txn-id-1"
		piFromId        = "pi-from-id-1"
		piToId          = "pi-to-id-1"
		actorId1        = "actor-id-1"
		reqId           = "req-id-1"
		beneficiaryName = "John Doe"
		utr             = "UTR123456789"
	)

	type args struct {
		ctx context.Context
		req *domainPb.ProcessPaymentRequest
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(
			mockOrderDao *daoMocks.MockOrderDao,
			mockTxnDao *daoMocks.MockTransactionDao,
			mockPiClient *piMocks.MockPiClient,
			mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
			mockSavingClient *savingsMocks.MockSavingsClient,
			mockILockManager *lockMocks.MockILockManager,
			mockILock *lockMocks.MockILock,
			mockPiProcessor *piInternalMocks.MockPiProcessor,
			mockPublisher *mocks.MockPublisher,
		)
		want    *domainPb.ProcessPaymentResponse
		wantErr bool
	}{
		{
			name: "should return transient failure when lock acquisition fails",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId: orderId1,
						IsLastAttempt:   false,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("lock acquisition failed"))
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "should return transient failure when order fetch fails",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId: orderId1,
						IsLastAttempt:   false,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(nil, fmt.Errorf("order fetch failed"))
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "should initiate new payment when no transactions exist",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId: orderId1,
						IsLastAttempt:   false,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: orderId1,
					},
					Transactions: []*paymentPb.Transaction{},
				}, nil)
				// Mock for initiateNewB2CPayment
				mockTxnDao.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&paymentPb.Transaction{
					Id: txnId1,
				}, nil)
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("message-id", nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), gomock.Any()).Return(&piPb.GetPiByIdResponse{
					Status: &rpcPb.Status{Code: uint32(piPb.GetPiByIdResponse_OK)},
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: "**********",
								IfscCode:            "FDRL0001234",
								AccountType:         accountPb.Type_SAVINGS,
								Name:                "Test User",
							},
						},
						VerifiedName: "Test User",
					},
				}, nil).Times(2) // Called for both piFrom and piTo
				mockSavingClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{
						PhoneNumber: &common.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						EmailId:     "<EMAIL>",
					},
				}, nil)
				mockTxnDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockOvgB2CPaymentClient.EXPECT().InitiateB2CPay(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.InitiateB2CPayResponse{
					Status: &rpcPb.Status{Code: uint32(vgB2CPaymentPb.InitiateB2CPayResponse_OK)},
				}, nil)
				mockTxnDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_IN_PROGRESS,
				},
			},
			wantErr: false,
		},
		{
			name: "should return permanent failure when transaction is already failed with non-retryable error code",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId: orderId1,
						IsLastAttempt:   false,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: orderId1,
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:          txnId1,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
							Status:      paymentPb.TransactionStatus_FAILED,
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										RawStatusCode: "119", // Non-retryable error code
									},
								},
							},
						},
					},
				}, nil)
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "should return success when transaction is already successful",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId: orderId1,
						IsLastAttempt:   false,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: orderId1,
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:     txnId1,
							Status: paymentPb.TransactionStatus_SUCCESS,
						},
					},
				}, nil)
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "should initiate new payment when previous transaction failed and not last attempt",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId: orderId1,
						IsLastAttempt:   false,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: orderId1,
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:     txnId1,
							Status: paymentPb.TransactionStatus_FAILED,
						},
					},
				}, nil)
				// Mock for initiateNewB2CPayment
				mockTxnDao.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&paymentPb.Transaction{
					Id: txnId1,
				}, nil)
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("message-id", nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), gomock.Any()).Return(&piPb.GetPiByIdResponse{
					Status: &rpcPb.Status{Code: uint32(piPb.GetPiByIdResponse_OK)},
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: "**********",
								IfscCode:            "FDRL0001234",
								AccountType:         accountPb.Type_SAVINGS,
								Name:                "Test User",
							},
						},
						VerifiedName: "Test User",
					},
				}, nil).Times(2)
				mockSavingClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{
						PhoneNumber: &common.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						EmailId:     "<EMAIL>",
					},
				}, nil)
				mockTxnDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockOvgB2CPaymentClient.EXPECT().InitiateB2CPay(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.InitiateB2CPayResponse{
					Status: &rpcPb.Status{Code: uint32(vgB2CPaymentPb.InitiateB2CPayResponse_OK)},
				}, nil)
				mockTxnDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_IN_PROGRESS,
				},
			},
			wantErr: false,
		},
		{
			name: "should return permanent failure when transaction failed and is last attempt",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId: orderId1,
						IsLastAttempt:   true,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: orderId1,
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:     txnId1,
							Status: paymentPb.TransactionStatus_FAILED,
						},
					},
				}, nil)
				// Note: No Update call expected because transaction is already in terminal state (FAILED)
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "should fetch payment status when transaction is in progress",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId: orderId1,
						IsLastAttempt:   false,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:        orderId1,
						ToActorId: actorId1,
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:              txnId1,
							PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
							PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
							Status:          paymentPb.TransactionStatus_IN_PROGRESS,
							CreatedAt:       &timestamppb.Timestamp{Seconds: time.Now().Unix() - 100}, // Past time to avoid delay check
						},
					},
				}, nil)
				// Mock for fetchPaymentStatusAndUpdateDB
				mockTxnDao.EXPECT().GetByIdWithPaymentReqInfo(gomock.Any(), txnId1).Return(&paymentPb.Transaction{
					Id: txnId1,
				}, &paymentPb.PaymentRequestInformation{
					ReqId: reqId,
				}, nil)
				mockOvgB2CPaymentClient.EXPECT().GetB2CTransactionStatus(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.GetB2CTransactionStatusResponse{
					Status: &rpcPb.Status{Code: uint32(vgB2CPaymentPb.GetB2CTransactionStatusResponse_OK)},
					Utr:    utr,
				}, nil)
				mockPiProcessor.EXPECT().UpdatePiVerifiedNameV2(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockTxnDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "should return transient failure when no transactions exist and is last attempt",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId: orderId1,
						IsLastAttempt:   true,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: orderId1,
					},
					Transactions: []*paymentPb.Transaction{},
				}, nil)
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "should initiate new payment when transaction is in created state and not last attempt",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId: orderId1,
						IsLastAttempt:   false,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: orderId1,
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:     txnId1,
							Status: paymentPb.TransactionStatus_CREATED,
						},
					},
				}, nil)
				// Mock for initiateNewB2CPayment with existing transaction
				mockPiClient.EXPECT().GetPiById(gomock.Any(), gomock.Any()).Return(&piPb.GetPiByIdResponse{
					Status: &rpcPb.Status{Code: uint32(piPb.GetPiByIdResponse_OK)},
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: "**********",
								IfscCode:            "FDRL0001234",
								AccountType:         accountPb.Type_SAVINGS,
								Name:                "Test User",
							},
						},
						VerifiedName: "Test User",
					},
				}, nil).Times(2)
				mockSavingClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{
						PhoneNumber: &common.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						EmailId:     "<EMAIL>",
					},
				}, nil)
				mockTxnDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("message-id", nil)
				mockOvgB2CPaymentClient.EXPECT().InitiateB2CPay(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.InitiateB2CPayResponse{
					Status: &rpcPb.Status{Code: uint32(vgB2CPaymentPb.InitiateB2CPayResponse_OK)},
				}, nil)
				mockTxnDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_IN_PROGRESS,
				},
			},
			wantErr: false,
		},
		{
			name: "should initiate new payment when transaction is in initiated state and not last attempt",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId: orderId1,
						IsLastAttempt:   false,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: orderId1,
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:     txnId1,
							Status: paymentPb.TransactionStatus_INITIATED,
						},
					},
				}, nil)
				// Mock for initiateNewB2CPayment with existing transaction
				mockPiClient.EXPECT().GetPiById(gomock.Any(), gomock.Any()).Return(&piPb.GetPiByIdResponse{
					Status: &rpcPb.Status{Code: uint32(piPb.GetPiByIdResponse_OK)},
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: "**********",
								IfscCode:            "FDRL0001234",
								AccountType:         accountPb.Type_SAVINGS,
								Name:                "Test User",
							},
						},
						VerifiedName: "Test User",
					},
				}, nil).Times(2)
				mockSavingClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{
						PhoneNumber: &common.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						EmailId:     "<EMAIL>",
					},
				}, nil)
				mockTxnDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("message-id", nil)
				mockOvgB2CPaymentClient.EXPECT().InitiateB2CPay(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.InitiateB2CPayResponse{
					Status: &rpcPb.Status{Code: uint32(vgB2CPaymentPb.InitiateB2CPayResponse_OK)},
				}, nil)
				mockTxnDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_IN_PROGRESS,
				},
			},
			wantErr: false,
		},
		{
			name: "should initiate new payment when transaction is in created state and is last attempt but shouldForceProcessOrder is true",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:         orderId1,
						IsLastAttempt:           true,
						ShouldForceProcessOrder: true,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: orderId1,
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:     txnId1,
							Status: paymentPb.TransactionStatus_CREATED,
						},
					},
				}, nil)
				// Mock for initiateNewB2CPayment with existing transaction
				mockPiClient.EXPECT().GetPiById(gomock.Any(), gomock.Any()).Return(&piPb.GetPiByIdResponse{
					Status: &rpcPb.Status{Code: uint32(piPb.GetPiByIdResponse_OK)},
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: "**********",
								IfscCode:            "FDRL0001234",
								AccountType:         accountPb.Type_SAVINGS,
								Name:                "Test User",
							},
						},
						VerifiedName: "Test User",
					},
				}, nil).Times(2)
				mockSavingClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{
						PhoneNumber: &common.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						EmailId:     "<EMAIL>",
					},
				}, nil)
				mockTxnDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(3) // 2 for initiateNewB2CPayment + 1 for defer function
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("message-id", nil).Times(1)           // 1 for initiateNewB2CPayment
				mockOvgB2CPaymentClient.EXPECT().InitiateB2CPay(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.InitiateB2CPayResponse{
					Status: &rpcPb.Status{Code: uint32(vgB2CPaymentPb.InitiateB2CPayResponse_OK)},
				}, nil)
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_IN_PROGRESS,
				},
			},
			wantErr: false,
		},
		{
			name: "should fetch payment status when transaction is in unknown state",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId: orderId1,
						IsLastAttempt:   false,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:        orderId1,
						ToActorId: actorId1,
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:              txnId1,
							PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
							PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
							Status:          paymentPb.TransactionStatus_UNKNOWN,
							CreatedAt:       &timestamppb.Timestamp{Seconds: time.Now().Unix() - 100}, // Past time to avoid delay check
						},
					},
				}, nil)
				// Mock for fetchPaymentStatusAndUpdateDB
				mockTxnDao.EXPECT().GetByIdWithPaymentReqInfo(gomock.Any(), txnId1).Return(&paymentPb.Transaction{
					Id: txnId1,
				}, &paymentPb.PaymentRequestInformation{
					ReqId: reqId,
				}, nil)
				mockOvgB2CPaymentClient.EXPECT().GetB2CTransactionStatus(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.GetB2CTransactionStatusResponse{
					Status: &rpcPb.Status{Code: uint32(vgB2CPaymentPb.GetB2CTransactionStatusResponse_OK)},
					Utr:    utr,
				}, nil)
				mockPiProcessor.EXPECT().UpdatePiVerifiedNameV2(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockTxnDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "should fetch payment status when transaction is in manual intervention state and shouldForceProcessOrder is true",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:         orderId1,
						IsLastAttempt:           false,
						ShouldForceProcessOrder: true,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:        orderId1,
						ToActorId: actorId1,
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:              txnId1,
							PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
							PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
							Status:          paymentPb.TransactionStatus_MANUAL_INTERVENTION,
							CreatedAt:       &timestamppb.Timestamp{Seconds: time.Now().Unix() - 100}, // Past time to avoid delay check
						},
					},
				}, nil)
				// Mock for fetchPaymentStatusAndUpdateDB
				mockTxnDao.EXPECT().GetByIdWithPaymentReqInfo(gomock.Any(), txnId1).Return(&paymentPb.Transaction{
					Id: txnId1,
				}, &paymentPb.PaymentRequestInformation{
					ReqId: reqId,
				}, nil)
				mockOvgB2CPaymentClient.EXPECT().GetB2CTransactionStatus(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.GetB2CTransactionStatusResponse{
					Status: &rpcPb.Status{Code: uint32(vgB2CPaymentPb.GetB2CTransactionStatusResponse_OK)},
					Utr:    utr,
				}, nil)
				mockPiProcessor.EXPECT().UpdatePiVerifiedNameV2(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockTxnDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "should return transient failure when enquiry is called before expected delay",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId: orderId1,
						IsLastAttempt:   false,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: orderId1,
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:              txnId1,
							PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
							PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
							Status:          paymentPb.TransactionStatus_IN_PROGRESS,
							CreatedAt:       &timestamppb.Timestamp{Seconds: time.Now().Unix()}, // Current time - too early for enquiry
						},
					},
				}, nil)
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "should return transient failure when transaction is in unexpected state",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId: orderId1,
						IsLastAttempt:   false,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: orderId1,
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:     txnId1,
							Status: paymentPb.TransactionStatus_MANUAL_INTERVENTION, // Without shouldForceProcessOrder
						},
					},
				}, nil)
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "should return permanent failure when transaction failed and is last attempt with shouldForceProcessOrder",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:         orderId1,
						IsLastAttempt:           true,
						ShouldForceProcessOrder: true,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: orderId1,
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:     txnId1,
							Status: paymentPb.TransactionStatus_FAILED,
						},
					},
				}, nil)
				// Note: No Update call expected because transaction is already in terminal state (FAILED)
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "should pick last transaction when multiple transactions exist and return success based on last txn status",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId: orderId1,
						IsLastAttempt:   false,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: orderId1,
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:     "txn-id-1", // First transaction - FAILED
							Status: paymentPb.TransactionStatus_FAILED,
						},
						{
							Id:     "txn-id-2", // Second transaction - FAILED
							Status: paymentPb.TransactionStatus_FAILED,
						},
						{
							Id:     "txn-id-3", // Third transaction - SUCCESS (should be picked)
							Status: paymentPb.TransactionStatus_SUCCESS,
						},
					},
				}, nil)
				// Should return success based on the last transaction (txn-id-3) which is SUCCESS
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "should pick last transaction when multiple transactions exist and initiate new payment based on last txn status",
			args: args{
				ctx: context.Background(),
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId: orderId1,
						IsLastAttempt:   false,
					},
					Payload: []byte(`{"piFrom":"pi-from-id-1","piTo":"pi-to-id-1","amount":{"currencyCode":"INR","units":1000},"partner":"FEDERAL_BANK","preferredProtocol":"IMPS","remarks":"test payment"}`),
				},
			},
			setupMocks: func(
				mockOrderDao *daoMocks.MockOrderDao,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockPiClient *piMocks.MockPiClient,
				mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient,
				mockSavingClient *savingsMocks.MockSavingsClient,
				mockILockManager *lockMocks.MockILockManager,
				mockILock *lockMocks.MockILock,
				mockPiProcessor *piInternalMocks.MockPiProcessor,
				mockPublisher *mocks.MockPublisher,
			) {
				mockILockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any()).Return(nil)
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), orderId1).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: orderId1,
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:        "txn-id-1", // First transaction - FAILED
							Status:    paymentPb.TransactionStatus_FAILED,
							CreatedAt: timestamppb.New(time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)),
						},
						{
							Id:        "txn-id-2", // Second transaction - FAILED
							Status:    paymentPb.TransactionStatus_FAILED,
							CreatedAt: timestamppb.New(time.Date(2025, 1, 2, 0, 0, 0, 0, time.UTC)),
						},
						{
							Id:        "txn-id-3", // Third transaction - FAILED (should be picked)
							Status:    paymentPb.TransactionStatus_FAILED,
							CreatedAt: timestamppb.New(time.Date(2025, 1, 3, 0, 0, 0, 0, time.UTC)),
						},
					},
				}, nil)
				// Should initiate new payment based on the last transaction (txn-id-3) which is FAILED
				mockTxnDao.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&paymentPb.Transaction{
					Id: "txn-id-4",
				}, nil)
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("message-id", nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), gomock.Any()).Return(&piPb.GetPiByIdResponse{
					Status: &rpcPb.Status{Code: uint32(piPb.GetPiByIdResponse_OK)},
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: "**********",
								IfscCode:            "FDRL0001234",
								AccountType:         accountPb.Type_SAVINGS,
								Name:                "Test User",
							},
						},
						VerifiedName: "Test User",
					},
				}, nil).Times(2)
				mockSavingClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{
						PhoneNumber: &common.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						EmailId:     "<EMAIL>",
					},
				}, nil)
				mockTxnDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockOvgB2CPaymentClient.EXPECT().InitiateB2CPay(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.InitiateB2CPayResponse{
					Status: &rpcPb.Status{Code: uint32(vgB2CPaymentPb.InitiateB2CPayResponse_OK)},
				}, nil)
				mockTxnDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_IN_PROGRESS,
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctr := gomock.NewController(t)
			defer ctr.Finish()

			mockOrderDao := daoMocks.NewMockOrderDao(ctr)
			mockTxnDao := daoMocks.NewMockTransactionDao(ctr)
			mockPiClient := piMocks.NewMockPiClient(ctr)
			mockOvgB2CPaymentClient := vgB2CPaymentMocks.NewMockPaymentClient(ctr)
			mockSavingClient := savingsMocks.NewMockSavingsClient(ctr)
			mockILockManager := lockMocks.NewMockILockManager(ctr)
			mockILock := lockMocks.NewMockILock(ctr)
			mockPiProcessor := piInternalMocks.NewMockPiProcessor(ctr)
			mockPublisher := mocks.NewMockPublisher(ctr)
			attributedIdGen := idgen.NewAttributedIdGen[*attributes.PayAttributes](idgen.NewClock())

			service := NewService(
				mockTxnDao,
				mockOrderDao,
				mockPiClient,
				mockOvgB2CPaymentClient,
				mockSavingClient,
				conf.ErrorRespCodesForPermanentFailure(),
				conf,
				mockILockManager,
				mockPublisher,
				attributedIdGen,
				mockPiProcessor,
			)

			tt.setupMocks(mockOrderDao, mockTxnDao, mockPiClient, mockOvgB2CPaymentClient, mockSavingClient, mockILockManager, mockILock, mockPiProcessor, mockPublisher)

			got, err := service.MakeB2CPayment(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("MakeB2CPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MakeB2CPayment() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_getB2CAccountPaymentStatus(t *testing.T) {
	t.Parallel()

	const (
		actorId1        = "actor-id-1"
		accountId1      = "account-id-1"
		piToId          = "pi-to-id-1"
		reqId           = "req-id-1"
		beneficiaryName = "John Doe"
		utr             = "UTR123456789"
	)

	type args struct {
		ctx           context.Context
		txn           *paymentPb.Transaction
		order         *orderPb.Order
		reqInfo       *paymentPb.PaymentRequestInformation
		requestSource vendorgateway.RequestSource
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient, mockPiProcessor *piInternalMocks.MockPiProcessor)
		want       []paymentPb.TransactionFieldMask
		wantErr    bool
	}{
		{
			name: "should return success when enquiry response is successful",
			args: args{
				ctx: context.Background(),
				txn: &paymentPb.Transaction{
					Id:              "txn-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
					PiTo:            piToId,
				},
				order: &orderPb.Order{
					ToActorId: actorId1,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: reqId,
				},
				requestSource: vendorgateway.RequestSource_REQUEST_SOURCE_UNSPECIFIED,
			},
			setupMocks: func(mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient, mockPiProcessor *piInternalMocks.MockPiProcessor) {
				mockOvgB2CPaymentClient.EXPECT().GetB2CTransactionStatus(gomock.Any(), &vgB2CPaymentPb.GetB2CTransactionStatusRequest{
					Header:            &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Protocol:          paymentPb.PaymentProtocol_IMPS,
					OriginalRequestId: reqId,
					RequestSource:     vendorgateway.RequestSource_REQUEST_SOURCE_UNSPECIFIED,
				}).Return(&vgB2CPaymentPb.GetB2CTransactionStatusResponse{
					Status: &rpcPb.Status{
						Code: uint32(vgB2CPaymentPb.GetB2CTransactionStatusResponse_OK),
					},
					BeneficiaryName:        beneficiaryName,
					Utr:                    utr,
					StatusCode:             "00",
					StatusDescriptionPayer: "Success",
					StatusDescriptionPayee: "Success",
					ResponseCode:           "00",
					Reason:                 "Success",
				}, nil)
				mockPiProcessor.EXPECT().UpdatePiVerifiedNameV2(gomock.Any(), piToId, beneficiaryName, false).Return(nil)
			},
			want: []paymentPb.TransactionFieldMask{
				paymentPb.TransactionFieldMask_STATUS,
				paymentPb.TransactionFieldMask_DETAILED_STATUS,
				paymentPb.TransactionFieldMask_UTR,
			},
			wantErr: false,
		},
		{
			name: "should return in progress when enquiry response is in progress",
			args: args{
				ctx: context.Background(),
				txn: &paymentPb.Transaction{
					Id:              "txn-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
				},
				order: &orderPb.Order{
					ToActorId: actorId1,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: reqId,
				},
				requestSource: vendorgateway.RequestSource_REQUEST_SOURCE_UNSPECIFIED,
			},
			setupMocks: func(mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient, mockPiProcessor *piInternalMocks.MockPiProcessor) {
				mockOvgB2CPaymentClient.EXPECT().GetB2CTransactionStatus(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.GetB2CTransactionStatusResponse{
					Status: &rpcPb.Status{
						Code: uint32(vgB2CPaymentPb.GetB2CTransactionStatusResponse_IN_PROGRESS),
					},
					StatusCode:             "01",
					StatusDescriptionPayer: "In Progress",
					StatusDescriptionPayee: "In Progress",
					ResponseCode:           "01",
					Reason:                 "Processing",
				}, nil)
			},
			want: []paymentPb.TransactionFieldMask{
				paymentPb.TransactionFieldMask_STATUS,
				paymentPb.TransactionFieldMask_DETAILED_STATUS,
			},
			wantErr: true, // Returns ErrInProgress
		},
		{
			name: "should return in progress when enquiry response is deemed approved",
			args: args{
				ctx: context.Background(),
				txn: &paymentPb.Transaction{
					Id:              "txn-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
				},
				order: &orderPb.Order{
					ToActorId: actorId1,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: reqId,
				},
				requestSource: vendorgateway.RequestSource_REQUEST_SOURCE_UNSPECIFIED,
			},
			setupMocks: func(mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient, mockPiProcessor *piInternalMocks.MockPiProcessor) {
				mockOvgB2CPaymentClient.EXPECT().GetB2CTransactionStatus(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.GetB2CTransactionStatusResponse{
					Status: &rpcPb.Status{
						Code: uint32(vgB2CPaymentPb.GetB2CTransactionStatusResponse_DEEMED_APPROVED),
					},
					StatusCode:             "02",
					StatusDescriptionPayer: "Deemed Approved",
					StatusDescriptionPayee: "Deemed Approved",
					ResponseCode:           "02",
					Reason:                 "Approved",
				}, nil)
			},
			want: []paymentPb.TransactionFieldMask{
				paymentPb.TransactionFieldMask_STATUS,
				paymentPb.TransactionFieldMask_DETAILED_STATUS,
			},
			wantErr: true, // Returns ErrInProgress
		},
		{
			name: "should return failed when enquiry response is permanent failure",
			args: args{
				ctx: context.Background(),
				txn: &paymentPb.Transaction{
					Id:              "txn-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
				},
				order: &orderPb.Order{
					ToActorId: actorId1,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: reqId,
				},
				requestSource: vendorgateway.RequestSource_REQUEST_SOURCE_UNSPECIFIED,
			},
			setupMocks: func(mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient, mockPiProcessor *piInternalMocks.MockPiProcessor) {
				mockOvgB2CPaymentClient.EXPECT().GetB2CTransactionStatus(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.GetB2CTransactionStatusResponse{
					Status: &rpcPb.Status{
						Code: uint32(vgB2CPaymentPb.GetB2CTransactionStatusResponse_PERMANENT_FAILURE),
					},
					StatusCode:             "99",
					StatusDescriptionPayer: "Permanent Failure",
					StatusDescriptionPayee: "Permanent Failure",
					ResponseCode:           "99",
					Reason:                 "Failed",
				}, nil)
			},
			want: []paymentPb.TransactionFieldMask{
				paymentPb.TransactionFieldMask_STATUS,
				paymentPb.TransactionFieldMask_DETAILED_STATUS,
			},
			wantErr: true, // Returns ErrPermanent
		},
		{
			name: "should return failed when enquiry response is insufficient fund",
			args: args{
				ctx: context.Background(),
				txn: &paymentPb.Transaction{
					Id:              "txn-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
				},
				order: &orderPb.Order{
					ToActorId: actorId1,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: reqId,
				},
				requestSource: vendorgateway.RequestSource_REQUEST_SOURCE_UNSPECIFIED,
			},
			setupMocks: func(mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient, mockPiProcessor *piInternalMocks.MockPiProcessor) {
				mockOvgB2CPaymentClient.EXPECT().GetB2CTransactionStatus(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.GetB2CTransactionStatusResponse{
					Status: &rpcPb.Status{
						Code: uint32(vgB2CPaymentPb.GetB2CTransactionStatusResponse_INSUFFICIENT_FUND),
					},
					StatusCode:             "51",
					StatusDescriptionPayer: "Insufficient Fund",
					StatusDescriptionPayee: "Insufficient Fund",
					ResponseCode:           "51",
					Reason:                 "Low Balance",
				}, nil)
			},
			want: []paymentPb.TransactionFieldMask{
				paymentPb.TransactionFieldMask_STATUS,
				paymentPb.TransactionFieldMask_DETAILED_STATUS,
			},
			wantErr: true, // Returns ErrPermanent
		},
		{
			name: "should return failed when enquiry response is account is closed",
			args: args{
				ctx: context.Background(),
				txn: &paymentPb.Transaction{
					Id:              "txn-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
				},
				order: &orderPb.Order{
					ToActorId: actorId1,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: reqId,
				},
				requestSource: vendorgateway.RequestSource_REQUEST_SOURCE_UNSPECIFIED,
			},
			setupMocks: func(mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient, mockPiProcessor *piInternalMocks.MockPiProcessor) {
				mockOvgB2CPaymentClient.EXPECT().GetB2CTransactionStatus(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.GetB2CTransactionStatusResponse{
					Status: &rpcPb.Status{
						Code: uint32(vgB2CPaymentPb.GetB2CTransactionStatusResponse_ACCOUNT_IS_CLOSED),
					},
					StatusCode:             "52",
					StatusDescriptionPayer: "Account Closed",
					StatusDescriptionPayee: "Account Closed",
					ResponseCode:           "52",
					Reason:                 "Closed",
				}, nil)
			},
			want: []paymentPb.TransactionFieldMask{
				paymentPb.TransactionFieldMask_STATUS,
				paymentPb.TransactionFieldMask_DETAILED_STATUS,
			},
			wantErr: true, // Returns ErrPermanent
		},
		{
			name: "should return unknown when enquiry response has unknown status code",
			args: args{
				ctx: context.Background(),
				txn: &paymentPb.Transaction{
					Id:              "txn-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
				},
				order: &orderPb.Order{
					ToActorId: actorId1,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: reqId,
				},
				requestSource: vendorgateway.RequestSource_REQUEST_SOURCE_UNSPECIFIED,
			},
			setupMocks: func(mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient, mockPiProcessor *piInternalMocks.MockPiProcessor) {
				mockOvgB2CPaymentClient.EXPECT().GetB2CTransactionStatus(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.GetB2CTransactionStatusResponse{
					Status: &rpcPb.Status{
						Code: uint32(999), // Unknown status code
					},
					StatusCode:             "99",
					StatusDescriptionPayer: "Unknown",
					StatusDescriptionPayee: "Unknown",
					ResponseCode:           "99",
					Reason:                 "Unknown",
				}, nil)
			},
			want: []paymentPb.TransactionFieldMask{
				paymentPb.TransactionFieldMask_STATUS,
				paymentPb.TransactionFieldMask_DETAILED_STATUS,
			},
			wantErr: true, // Returns ErrInProgress
		},
		{
			name: "should return error when enquiry call fails",
			args: args{
				ctx: context.Background(),
				txn: &paymentPb.Transaction{
					Id:              "txn-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
				},
				order: &orderPb.Order{
					ToActorId: actorId1,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: reqId,
				},
				requestSource: vendorgateway.RequestSource_REQUEST_SOURCE_UNSPECIFIED,
			},
			setupMocks: func(mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient, mockPiProcessor *piInternalMocks.MockPiProcessor) {
				mockOvgB2CPaymentClient.EXPECT().GetB2CTransactionStatus(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("rpc call failed"))
			},
			want: []paymentPb.TransactionFieldMask{
				paymentPb.TransactionFieldMask_DETAILED_STATUS,
			},
			wantErr: true, // Returns wrapped error
		},
		{
			name: "should handle pi processor error gracefully when not invalid argument or already exists",
			args: args{
				ctx: context.Background(),
				txn: &paymentPb.Transaction{
					Id:              "txn-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
					PiTo:            piToId,
				},
				order: &orderPb.Order{
					ToActorId: actorId1,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: reqId,
				},
				requestSource: vendorgateway.RequestSource_REQUEST_SOURCE_UNSPECIFIED,
			},
			setupMocks: func(mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient, mockPiProcessor *piInternalMocks.MockPiProcessor) {
				mockOvgB2CPaymentClient.EXPECT().GetB2CTransactionStatus(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.GetB2CTransactionStatusResponse{
					Status: &rpcPb.Status{
						Code: uint32(vgB2CPaymentPb.GetB2CTransactionStatusResponse_OK),
					},
					BeneficiaryName:        beneficiaryName,
					Utr:                    utr,
					StatusCode:             "00",
					StatusDescriptionPayer: "Success",
					StatusDescriptionPayee: "Success",
					ResponseCode:           "00",
					Reason:                 "Success",
				}, nil)
				mockPiProcessor.EXPECT().UpdatePiVerifiedNameV2(gomock.Any(), piToId, beneficiaryName, false).Return(fmt.Errorf("database error"))
			},
			want:    nil,
			wantErr: true, // Returns error from pi processor
		},
		{
			name: "should continue when pi processor returns invalid argument error",
			args: args{
				ctx: context.Background(),
				txn: &paymentPb.Transaction{
					Id:              "txn-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
					PiTo:            piToId,
				},
				order: &orderPb.Order{
					ToActorId: actorId1,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: reqId,
				},
				requestSource: vendorgateway.RequestSource_REQUEST_SOURCE_UNSPECIFIED,
			},
			setupMocks: func(mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient, mockPiProcessor *piInternalMocks.MockPiProcessor) {
				mockOvgB2CPaymentClient.EXPECT().GetB2CTransactionStatus(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.GetB2CTransactionStatusResponse{
					Status: &rpcPb.Status{
						Code: uint32(vgB2CPaymentPb.GetB2CTransactionStatusResponse_OK),
					},
					BeneficiaryName:        beneficiaryName,
					Utr:                    utr,
					StatusCode:             "00",
					StatusDescriptionPayer: "Success",
					StatusDescriptionPayee: "Success",
					ResponseCode:           "00",
					Reason:                 "Success",
				}, nil)
				mockPiProcessor.EXPECT().UpdatePiVerifiedNameV2(gomock.Any(), piToId, beneficiaryName, false).Return(epifierrors.ErrInvalidArgument)
			},
			want: []paymentPb.TransactionFieldMask{
				paymentPb.TransactionFieldMask_STATUS,
				paymentPb.TransactionFieldMask_DETAILED_STATUS,
				paymentPb.TransactionFieldMask_UTR,
			},
			wantErr: false, // Continues despite pi processor error
		},
		{
			name: "should continue when pi processor returns already exists error",
			args: args{
				ctx: context.Background(),
				txn: &paymentPb.Transaction{
					Id:              "txn-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
					PiTo:            piToId,
				},
				order: &orderPb.Order{
					ToActorId: actorId1,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: reqId,
				},
				requestSource: vendorgateway.RequestSource_REQUEST_SOURCE_UNSPECIFIED,
			},
			setupMocks: func(mockOvgB2CPaymentClient *vgB2CPaymentMocks.MockPaymentClient, mockPiProcessor *piInternalMocks.MockPiProcessor) {
				mockOvgB2CPaymentClient.EXPECT().GetB2CTransactionStatus(gomock.Any(), gomock.Any()).Return(&vgB2CPaymentPb.GetB2CTransactionStatusResponse{
					Status: &rpcPb.Status{
						Code: uint32(vgB2CPaymentPb.GetB2CTransactionStatusResponse_OK),
					},
					BeneficiaryName:        beneficiaryName,
					Utr:                    utr,
					StatusCode:             "00",
					StatusDescriptionPayer: "Success",
					StatusDescriptionPayee: "Success",
					ResponseCode:           "00",
					Reason:                 "Success",
				}, nil)
				mockPiProcessor.EXPECT().UpdatePiVerifiedNameV2(gomock.Any(), piToId, beneficiaryName, false).Return(epifierrors.ErrAlreadyExists)
			},
			want: []paymentPb.TransactionFieldMask{
				paymentPb.TransactionFieldMask_STATUS,
				paymentPb.TransactionFieldMask_DETAILED_STATUS,
				paymentPb.TransactionFieldMask_UTR,
			},
			wantErr: false, // Continues despite pi processor error
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctr := gomock.NewController(t)
			defer ctr.Finish()

			mockOvgB2CPaymentClient := vgB2CPaymentMocks.NewMockPaymentClient(ctr)
			mockPiProcessor := piInternalMocks.NewMockPiProcessor(ctr)

			service := NewService(
				nil,
				nil,
				nil,
				mockOvgB2CPaymentClient,
				nil,
				nil,
				nil,
				nil,
				nil, // txnDetailedStatusUpdateSnsPublisher
				nil, // attributedIdGen
				mockPiProcessor,
			)

			tt.setupMocks(mockOvgB2CPaymentClient, mockPiProcessor)
			got, err := service.getB2CAccountPaymentStatus(tt.args.ctx, tt.args.txn, tt.args.order, tt.args.reqInfo, tt.args.requestSource)
			if (err != nil) != tt.wantErr {
				t.Errorf("getB2CAccountPaymentStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getB2CAccountPaymentStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}
