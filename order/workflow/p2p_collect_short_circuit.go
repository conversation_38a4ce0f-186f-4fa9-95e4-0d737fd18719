package workflow

import (
	"context"
	"errors"
	"fmt"
	"time"

	gormv2 "gorm.io/gorm"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/queue"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	config "github.com/epifi/gamma/order/config/genconf"
	"github.com/epifi/gamma/order/dao"
	"github.com/epifi/gamma/order/metrics"
	"github.com/epifi/gamma/pkg/pay"
)

type p2pCollectShortCircuit struct {
	// defaultProcessor contains default implementation of the `Processor` interface
	// By embedding this, we can skip default implementations
	defaultProcessor

	orderDao                    dao.OrderDao
	orderUpdateEventPublisher   queue.Publisher
	collectShortCircuitWorkflow *config.CollectShortCircuitWorkflow
	piClient                    piPb.PiClient
	conf                        *config.Config
}

func NewP2PCollectShortCircuit(conf *config.Config, orderDao dao.OrderDao, orderUpdatePublisher queue.Publisher, collectShortCircuitWorkflow *config.CollectShortCircuitWorkflow, piClient piPb.PiClient) *p2pCollectShortCircuit {
	return &p2pCollectShortCircuit{
		orderDao:                    orderDao,
		orderUpdateEventPublisher:   orderUpdatePublisher,
		collectShortCircuitWorkflow: collectShortCircuitWorkflow,
		piClient:                    piClient,
		conf:                        conf,
	}
}

// GetOrderExpiryDuration returns collect order expiry
func (p *p2pCollectShortCircuit) GetOrderExpiryDuration(ctx context.Context) time.Duration {
	return p.collectShortCircuitWorkflow.CollectExpirationDuration()
}

// ProcessPaymentStage process the order in payment stage.
// If order is in IN_PAYMENT, CREATED or COLLECT_IN_PROGRESS state, transaction status is updated based on underlying transaction status.
// In all other cases error is thrown. All the errors returned from the method must wrap
// queue.ErrPermanent or queue.ErrTransient. This because this method is invoked from a consumer service.
// Thus, it is necessary error categorization.
// nolint:dupl
func (p *p2pCollectShortCircuit) ProcessPaymentStage(ctx context.Context, orderId string) error {
	orderWithTxn, err := p.orderDao.GetByIdWithTransactions(ctx, orderId)
	if err != nil {
		var queueConsumerErr error
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			queueConsumerErr = queue.ErrRecordNotFound
		} else {
			queueConsumerErr = queue.ErrDB
		}
		return fmt.Errorf("unable to fetch order : %s : %v :%w", orderId, err.Error(), queueConsumerErr)
	}

	order := orderWithTxn.GetOrder()
	transactions := orderWithTxn.GetTransactions()

	if len(transactions) == 1 && transactions[0].GetStatus() == paymentPb.TransactionStatus_REVERSED {
		return p.updateOrderPaymentStatus(ctx, transactions[0], order)
	}

	switch order.GetStatus() {
	case orderPb.OrderStatus_IN_PAYMENT, orderPb.OrderStatus_COLLECT_REGISTERED:
		// Since, P2P workflow has 1-1 mapping between orders and transactions.
		if len(transactions) != 1 {
			return fmt.Errorf("invalid number of transaction attached to order: %s: %w", orderId, queue.ErrPermanent)
		}
		txn := transactions[0]
		return p.updateOrderPaymentStatus(ctx, txn, order)

	case orderPb.OrderStatus_PAYMENT_FAILED, orderPb.OrderStatus_PAID:
		return fmt.Errorf("payment is in terminal stage %s cant process ahead : %w", order.GetStatus().String(), queue.ErrPermanent)

	default:
		return fmt.Errorf("unknown order status for payment stage: orderId: %s status: %s : %w", orderId, order.GetStatus().String(), queue.ErrPermanent)
	}
}

// updateOrderPaymentStatus updates order status based on the status of the underlying transaction
// returns in case transaction has not completed yet so that another attempt to update order status can be made
// with the help of queues and consumer
func (p *p2pCollectShortCircuit) updateOrderPaymentStatus(ctx context.Context, txn *paymentPb.Transaction,
	order *orderPb.Order) error {
	var statusToBeUpdated orderPb.OrderStatus

	// TODO(nitesh): add unknown payment states to order state machine
	switch {
	case txn.GetStatus() == paymentPb.TransactionStatus_REVERSED:
		statusToBeUpdated = orderPb.OrderStatus_PAYMENT_REVERSED
	case p.isInPayment(txn.Status):
		statusToBeUpdated = orderPb.OrderStatus_IN_PAYMENT

	case txn.Status == paymentPb.TransactionStatus_SUCCESS:
		statusToBeUpdated = orderPb.OrderStatus_PAID

	case txn.Status == paymentPb.TransactionStatus_FAILED:
		statusToBeUpdated = orderPb.OrderStatus_PAYMENT_FAILED
	}

	if statusToBeUpdated == orderPb.OrderStatus_ORDER_STATUS_UNSPECIFIED {
		return fmt.Errorf("transaction in unexpected state: %s : %w", txn.Status.String(), queue.ErrPermanent)
	}

	if order.Status == statusToBeUpdated {
		return fmt.Errorf("order state already upto date! %w", queue.ErrPermanent)
	}

	// TODO(nitesh): Handle DB Transactions: https://monorail.pointz.in/p/fi-app/issues/detail?id=468
	if err := p.orderDao.UpdateAndChangeStatus(ctx, order, nil, order.Status, statusToBeUpdated); err != nil {
		return fmt.Errorf("failed to update order state - id: %s : %v : %w", order, err.Error(), queue.ErrDB)
	}

	metrics.IncrementOMSWorkflowStatusCount(order)
	metrics.RecordOMSWorkflowCompletionDuration(order)
	orderWithTxn := &orderPb.OrderWithTransactions{Order: order, Transactions: []*paymentPb.Transaction{txn}}

	_, err := pay.PublishOrderUpdate(ctx, p.orderUpdateEventPublisher, orderWithTxn, p.piClient)
	if err != nil {
		return fmt.Errorf("failed to publish order update event: %v: %w", err, queue.ErrPublish)
	}

	return nil
}

// isInPayment checks if txn status maps to `IN_PAYMENT`
// an order is moved to in payment in the following scenarios
// 1. payment initialization with partner bank times out and transaction ends up in unknown state
// 2. there is DB update failure and transaction is stuck in INITIATED state
// 3. payment got initiated but some how transaction ended up in MANUAL_INTERVENTION
// 4. payment initialisation was successful and system is awaiting for final transaction status
func (p *p2pCollectShortCircuit) isInPayment(status paymentPb.TransactionStatus) bool {
	return status == paymentPb.TransactionStatus_MANUAL_INTERVENTION ||
		status == paymentPb.TransactionStatus_UNKNOWN ||
		status == paymentPb.TransactionStatus_INITIATED ||
		status == paymentPb.TransactionStatus_IN_PROGRESS
}

// IsPinRequired returns if pin is required for authorising short circuit collect.
func (p *p2pCollectShortCircuit) IsPinRequired(ctx context.Context, orderId string, amount *moneyPb.Money) bool {
	lowValueTxnUpperLimit := p.conf.CollectShortCircuitWorkflow().LowValueTransactionUpperLimit()
	order, err := p.orderDao.GetById(ctx, orderId)
	if err != nil {
		return false
	}
	if paymentDetails, err := pay.GetPaymentDetailsFromPayload(order); err != nil ||
		paymentDetails.GetPaymentProtocol() != paymentPb.PaymentProtocol_UPI {
		if money.Compare(amount, lowValueTxnUpperLimit) <= 0 {
			return false
		}
	}
	return true
}
