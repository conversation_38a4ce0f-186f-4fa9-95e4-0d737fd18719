package rewardfulfillment

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	luckydrawPb "github.com/epifi/gamma/api/rewards/luckydraw"
	mock_luckydraw "github.com/epifi/gamma/api/rewards/luckydraw/mocks"
	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/rewards/config"
	"github.com/epifi/gamma/rewards/processor/dao/model"
	"github.com/epifi/gamma/rewards/test"
	mock_dao "github.com/epifi/gamma/rewards/test/mocks/processor/dao"
)

var (
	ldrts *LuckyDrawRewardProcessorTestSuite
)

type LuckyDrawRewardProcessorTestSuite struct {
	db     *gormv2.DB
	config *config.Config
}

func NewLuckyDrawRewardProcessorTestSuite(db *gormv2.DB, config *config.Config) *LuckyDrawRewardProcessorTestSuite {
	return &LuckyDrawRewardProcessorTestSuite{db: db, config: config}
}

func TestLuckyDrawRewardProcessor_Process(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockLuckyDrawClient := mock_luckydraw.NewMockLuckyDrawServiceClient(ctr)
	mockProcessingRequestDao := mock_dao.NewMockProcessingRequestDao(ctr)

	rewardInput := &rewardsPb.Reward{
		Id:        "rw-1",
		RefId:     "ref-1",
		ActorId:   "act-1",
		Status:    rewardsPb.RewardStatus_PROCESSING_PENDING,
		SubStatus: "",
		ChosenReward: &rewardsPb.RewardOption{
			Option: &rewardsPb.RewardOption_LuckyDraw{
				LuckyDraw: &rewardsPb.LuckyDraw{
					LuckyDrawId: uuid.New().String(),
				},
			},
		},
		ProcessingRef: uuid.New().String(),
	}

	// create lucky draw processor request from reward
	rewardProcessingRequest := NewLuckyDrawRewardProcessorRequest(rewardInput)

	type args struct {
		ctx     context.Context
		request IRequest
	}
	tests := []struct {
		name                      string
		args                      args
		want                      *Response
		wantErr                   bool
		mockLuckyDrawCalls        func()
		mockProcessingReqDaoCalls func()
	}{
		{
			name: "call to GET processing request returns error",
			args: args{
				ctx:     context.Background(),
				request: rewardProcessingRequest,
			},

			mockProcessingReqDaoCalls: func() {
				mockProcessingRequestDao.EXPECT().GetById(gomock.Any(), rewardProcessingRequest.GetProcessingReqId()).Return(nil,
					errors.New("error"))
			},
			mockLuckyDrawCalls: func() {},
			want:               nil,
			wantErr:            true,
		},
		{
			name: "registration status rpc call returns error",
			args: args{
				ctx:     context.Background(),
				request: rewardProcessingRequest,
			},
			mockLuckyDrawCalls: func() {
				mockLuckyDrawClient.EXPECT().CheckRegistrationStatus(gomock.Any(), gomock.Any()).Return(
					nil,
					errors.New("registration status call failed"),
				)
			},
			mockProcessingReqDaoCalls: func() {
				mockProcessingRequestDao.EXPECT().GetById(gomock.Any(), rewardProcessingRequest.GetProcessingReqId()).Return(&model.ProcessingRequest{
					Id:            rewardProcessingRequest.GetProcessingReqId(),
					ProcessingRef: uuid.New().String(),
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "registration status rpc call failed with INTERNAL SERVER ERROR",
			args: args{
				ctx:     context.Background(),
				request: rewardProcessingRequest,
			},
			mockLuckyDrawCalls: func() {
				mockLuckyDrawClient.EXPECT().CheckRegistrationStatus(gomock.Any(), gomock.Any()).Return(
					&luckydrawPb.CheckRegistrationStatusResponse{
						Status: rpc.StatusInternal(),
					},
					nil,
				)
			},
			mockProcessingReqDaoCalls: func() {
				mockProcessingRequestDao.EXPECT().GetById(gomock.Any(), rewardProcessingRequest.GetProcessingReqId()).Return(&model.ProcessingRequest{
					Id:            rewardProcessingRequest.GetProcessingReqId(),
					ProcessingRef: uuid.New().String(),
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "registration status rpc call returns UNSPECIFIED status",
			args: args{
				ctx:     context.Background(),
				request: rewardProcessingRequest,
			},
			mockLuckyDrawCalls: func() {
				mockLuckyDrawClient.EXPECT().CheckRegistrationStatus(gomock.Any(), gomock.Any()).Return(
					&luckydrawPb.CheckRegistrationStatusResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: luckydrawPb.RegistrationStatus_REGISTRATION_STATUS_UNSPECIFIED,
					},
					nil,
				)
			},
			mockProcessingReqDaoCalls: func() {
				mockProcessingRequestDao.EXPECT().GetById(gomock.Any(), rewardProcessingRequest.GetProcessingReqId()).Return(&model.ProcessingRequest{
					Id:            rewardProcessingRequest.GetProcessingReqId(),
					ProcessingRef: uuid.New().String(),
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "registration status rpc call returns REGISTERED status, processing ref was already persisted",
			args: args{
				ctx:     context.Background(),
				request: rewardProcessingRequest,
			},
			mockLuckyDrawCalls: func() {
				mockLuckyDrawClient.EXPECT().CheckRegistrationStatus(gomock.Any(), gomock.Any()).Return(
					&luckydrawPb.CheckRegistrationStatusResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: luckydrawPb.RegistrationStatus_REGISTERED,
						RegistrationId:     uuid.New().String(),
					},
					nil,
				)
			},
			mockProcessingReqDaoCalls: func() {
				mockProcessingRequestDao.EXPECT().GetById(gomock.Any(), rewardProcessingRequest.GetProcessingReqId()).Return(&model.ProcessingRequest{
					Id:            rewardProcessingRequest.GetProcessingReqId(),
					ProcessingRef: uuid.New().String(),
				}, nil)
			},
			want: &Response{
				Status: ProcessingStatus_PROCESSED,
			},
			wantErr: false,
		},
		{
			name: "registration status rpc call returns REGISTERED status, error updating processing_ref",
			args: args{
				ctx:     context.Background(),
				request: rewardProcessingRequest,
			},

			mockLuckyDrawCalls: func() {
				mockLuckyDrawClient.EXPECT().CheckRegistrationStatus(gomock.Any(), gomock.Any()).Return(
					&luckydrawPb.CheckRegistrationStatusResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: luckydrawPb.RegistrationStatus_REGISTERED,
						RegistrationId:     uuid.New().String(),
					},
					nil,
				)
			},
			mockProcessingReqDaoCalls: func() {
				mockProcessingRequestDao.EXPECT().GetById(gomock.Any(), rewardProcessingRequest.GetProcessingReqId()).Return(&model.ProcessingRequest{
					Id: rewardProcessingRequest.GetProcessingReqId(),
				}, nil)
				mockProcessingRequestDao.EXPECT().UpdateProcessingRef(gomock.Any(), rewardProcessingRequest.GetProcessingReqId(), gomock.Any()).Return(
					errors.New("error"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "registration status rpc call returns REGISTERED status, processing ref updated successfully",
			args: args{
				ctx:     context.Background(),
				request: rewardProcessingRequest,
			},

			mockLuckyDrawCalls: func() {
				mockLuckyDrawClient.EXPECT().CheckRegistrationStatus(gomock.Any(), gomock.Any()).Return(
					&luckydrawPb.CheckRegistrationStatusResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: luckydrawPb.RegistrationStatus_REGISTERED,
						RegistrationId:     uuid.New().String(),
					},
					nil,
				)
			},
			mockProcessingReqDaoCalls: func() {
				mockProcessingRequestDao.EXPECT().GetById(gomock.Any(), rewardProcessingRequest.GetProcessingReqId()).Return(&model.ProcessingRequest{
					Id: rewardProcessingRequest.GetProcessingReqId(),
				}, nil)
				mockProcessingRequestDao.EXPECT().UpdateProcessingRef(gomock.Any(), rewardProcessingRequest.GetProcessingReqId(), gomock.Any()).Return(nil)
			},
			want: &Response{
				Status: ProcessingStatus_PROCESSED,
			},
			wantErr: false,
		},
		{
			name: "registration status rpc returns NOT_REGISTERED, register rpc throws error",
			args: args{
				ctx:     context.Background(),
				request: rewardProcessingRequest,
			},
			mockLuckyDrawCalls: func() {
				mockLuckyDrawClient.EXPECT().CheckRegistrationStatus(gomock.Any(), gomock.Any()).Return(
					&luckydrawPb.CheckRegistrationStatusResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: luckydrawPb.RegistrationStatus_NOT_REGISTERED,
					},
					nil,
				)
				mockLuckyDrawClient.EXPECT().RegisterForLuckyDraw(gomock.Any(), gomock.Any()).Return(
					nil,
					errors.New("error during registration"),
				)
			},
			mockProcessingReqDaoCalls: func() {
				mockProcessingRequestDao.EXPECT().GetById(gomock.Any(), rewardProcessingRequest.GetProcessingReqId()).Return(&model.ProcessingRequest{
					Id:            rewardProcessingRequest.GetProcessingReqId(),
					ProcessingRef: uuid.New().String(),
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "registration status rpc returns NOT_REGISTERED, register rpc fails with INTERNAL SERVER ERROR",
			args: args{
				ctx:     context.Background(),
				request: rewardProcessingRequest,
			},
			mockLuckyDrawCalls: func() {
				mockLuckyDrawClient.EXPECT().CheckRegistrationStatus(gomock.Any(), gomock.Any()).Return(
					&luckydrawPb.CheckRegistrationStatusResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: luckydrawPb.RegistrationStatus_NOT_REGISTERED,
					},
					nil,
				)
				mockLuckyDrawClient.EXPECT().RegisterForLuckyDraw(gomock.Any(), gomock.Any()).Return(
					&luckydrawPb.RegisterForLuckyDrawResponse{
						Status: rpc.StatusInternal(),
					},
					nil,
				)
			},
			mockProcessingReqDaoCalls: func() {
				mockProcessingRequestDao.EXPECT().GetById(gomock.Any(), rewardProcessingRequest.GetProcessingReqId()).Return(&model.ProcessingRequest{
					Id:            rewardProcessingRequest.GetProcessingReqId(),
					ProcessingRef: uuid.New().String(),
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "registration status rpc returns NOT_REGISTERED, register rpc successful, update processing ref throws error",
			args: args{
				ctx:     context.Background(),
				request: rewardProcessingRequest,
			},
			mockLuckyDrawCalls: func() {
				mockLuckyDrawClient.EXPECT().CheckRegistrationStatus(gomock.Any(), gomock.Any()).Return(
					&luckydrawPb.CheckRegistrationStatusResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: luckydrawPb.RegistrationStatus_NOT_REGISTERED,
					},
					nil,
				)
				mockLuckyDrawClient.EXPECT().RegisterForLuckyDraw(gomock.Any(), gomock.Any()).Return(
					&luckydrawPb.RegisterForLuckyDrawResponse{
						Status:                  rpc.StatusOk(),
						LuckyDrawRegistrationId: "lucky-draw-registration-id",
					},
					nil,
				)
			},
			mockProcessingReqDaoCalls: func() {
				mockProcessingRequestDao.EXPECT().GetById(gomock.Any(), rewardProcessingRequest.GetProcessingReqId()).Return(&model.ProcessingRequest{
					Id:            rewardProcessingRequest.GetProcessingReqId(),
					ProcessingRef: uuid.New().String(),
				}, nil)

				mockProcessingRequestDao.EXPECT().UpdateProcessingRef(gomock.Any(), rewardProcessingRequest.GetProcessingReqId(), "lucky-draw-registration-id").Return(
					errors.New("error updating processing ref"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "registration status rpc returns NOT_REGISTERED, register rpc successful, update processing ref successful",
			args: args{
				ctx:     context.Background(),
				request: rewardProcessingRequest,
			},
			mockLuckyDrawCalls: func() {
				mockLuckyDrawClient.EXPECT().CheckRegistrationStatus(gomock.Any(), gomock.Any()).Return(
					&luckydrawPb.CheckRegistrationStatusResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: luckydrawPb.RegistrationStatus_NOT_REGISTERED,
					},
					nil,
				)
				mockLuckyDrawClient.EXPECT().RegisterForLuckyDraw(gomock.Any(), gomock.Any()).Return(
					&luckydrawPb.RegisterForLuckyDrawResponse{
						Status:                  rpc.StatusOk(),
						LuckyDrawRegistrationId: "lucky-draw-registration-id",
					},
					nil,
				)
			},
			mockProcessingReqDaoCalls: func() {
				mockProcessingRequestDao.EXPECT().GetById(gomock.Any(), rewardProcessingRequest.GetProcessingReqId()).Return(&model.ProcessingRequest{
					Id:            rewardProcessingRequest.GetProcessingReqId(),
					ProcessingRef: uuid.New().String(),
				}, nil)

				mockProcessingRequestDao.EXPECT().UpdateProcessingRef(gomock.Any(), rewardProcessingRequest.GetProcessingReqId(), "lucky-draw-registration-id").Return(nil)
			},
			want: &Response{
				Status: ProcessingStatus_PROCESSED,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ldp := &luckyDrawRewardProcessor{
				luckyDrawClient:      mockLuckyDrawClient,
				processingRequestDao: mockProcessingRequestDao,
			}
			// mocks calls
			tt.mockProcessingReqDaoCalls()
			tt.mockLuckyDrawCalls()

			pkgTestV2.TruncateAndPopulateRdsFixtures(t, ldrts.db, ldrts.config.RewardsDb.GetName(), test.AllTables)

			got, err := ldp.Process(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("Process() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got == nil && tt.want == nil {
				return
			}
			if (got == nil || tt.want == nil) || !reflect.DeepEqual(got.Status, tt.want.Status) {
				t.Errorf("Process() got = %v, want %v", got, tt.want)
				return
			}
		})
	}
}
