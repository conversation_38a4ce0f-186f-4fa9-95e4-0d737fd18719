package generator

import (
	"context"

	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/mfexternalorderupdate"
	"github.com/epifi/gamma/rewards/helper"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/rewards/datacollector"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	orderMgPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/rewards/config"
	"github.com/epifi/gamma/rewards/config/genconf"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/common"
	helper2 "github.com/epifi/gamma/rewards/generator/ruleengine/fact/helper"
)

type MFExternalOrderUpdateFactGenerator struct {
	rewardsConf      *config.Config
	userHelperSvc    helper.IUserHelperService
	ussOrderMgClient orderMgPb.OrderManagerClient
	dynConf          *genconf.Config
}

func NewMFExternalOrderUpdateFactGenerator(
	rewardsConf *config.Config,
	dynConf *genconf.Config,
	userHelperSvc helper.IUserHelperService,
	ussOrderMgClient orderMgPb.OrderManagerClient,
) *MFExternalOrderUpdateFactGenerator {
	return &MFExternalOrderUpdateFactGenerator{
		rewardsConf:      rewardsConf,
		dynConf:          dynConf,
		ussOrderMgClient: ussOrderMgClient,
		userHelperSvc:    userHelperSvc,
	}
}

// nolint: funlen
func (m *MFExternalOrderUpdateFactGenerator) GenerateFacts(ctx context.Context, collectedData *datacollector.CollectedData, rewardOffer *rewardOffersPb.RewardOffer) ([]common.IFact, error) {
	var facts []common.IFact
	investmentEvent := collectedData.GetInvestmentEvent()

	actorId := investmentEvent.GetActorId()

	fact := &mfexternalorderupdate.MFExternalOrderUpdateFact{
		CommonFact: &common.CommonFact{
			Ctx:                  ctx,
			RewardOffer:          rewardOffer,
			ActorId:              actorId,
			RefId:                collectedData.GetId(),
			ActionType:           collectedData.GetDataType(),
			ActionTime:           collectedData.GetActionTime(),
			ActionCollectionTime: collectedData.GetCreationTime(),
			UserHelperService:    m.userHelperSvc,
		},
		InvestmentEvent: investmentEvent,
		RewardsConf:     m.rewardsConf,
		DynConf:         m.dynConf,
	}

	// if reward offer is of REFERRAL_REFEREE type, we need to enrich fact with external_ref
	// for linking reward to actual referral and global dedupe id for deduping referral rewards.
	if rewardOffer.GetOfferType() == rewardsPb.RewardOfferType_REFERRAL_REFEREE_OFFER {
		actorReferralInfo, err := getActorReferralInfoForCommonFact(ctx, actorId, m.userHelperSvc)
		if err != nil {
			return nil, err
		}
		fact.CommonFact.ActorReferralInfo = actorReferralInfo
		referrerActorId := actorReferralInfo.ReferrerActorId
		fact.CommonFact.ExternalRefId = actorReferralInfo.FiniteCodeClaimId
		fact.CommonFact.RewardGlobalDedupeId = helper2.GenReferralRewardGlobalDedupeId(referrerActorId, actorId)
	}

	facts = append(facts, fact)

	return facts, nil
}
