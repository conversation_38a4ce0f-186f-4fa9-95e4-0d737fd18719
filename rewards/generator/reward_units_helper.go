package generator

import (
	"fmt"
	"math"
	"math/rand"

	"github.com/epifi/be-common/pkg/idgen"

	rewardOfferPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"

	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/common"
)

var RewardUnitsCalculator IRewardUnitsCalculator = &defaultRewardUnitsCalculator{}

type IRewardUnitsCalculator interface {
	CalculateUsingRangeProbabilityConfig(config *rewardOfferPb.RangeProbabilityConfig) (float64, error)
	CalculateUsingExpressionRangeProbabilityConfig(unitValue float64, config *rewardOfferPb.ExpressionRangeProbabilityConfig, fact common.IFact) (float64, error)
	// PerformMathematicalOperationOnUnits performs mathematical operation, i.e. addition/multiplication on baseUnits and newUnits and capped using the given unitsUpperCap
	// for e.g. if the operation is *, it will return baseUnits * newUnits and capped using the given unitsUpperCap.
	PerformMathematicalOperationOnUnits(baseUnits, newUnits int32, operation rewardOfferPb.MathematicalOperation, unitsUpperCap int32) (int32, error)
}

type defaultRewardUnitsCalculator struct {
}

func (rc *defaultRewardUnitsCalculator) CalculateUsingRangeProbabilityConfig(config *rewardOfferPb.RangeProbabilityConfig) (float64, error) {
	distributionTotal := 0
	if len(config.ConfigUnits) == 0 {
		return 0.0, fmt.Errorf("invalid config units: config units empty")
	}
	for _, x := range config.ConfigUnits {
		if x.Start > x.End {
			return 0.0, fmt.Errorf("invalid config unit %v, %v", x.Start, x.End)
		}
		// multiplying percentage by 1000 while adding to distributionTotal for supporting distribution for percentages with upto 3 decimal places precision.
		// Eg : 1.234 percentage would be added as 1234 in distributionTotal.
		distributionTotal += int(x.Percentage * 1000)
	}
	// nolint: gosec
	rng := rand.New(idgen.NewCryptoSeededSource())
	randP := uint32(rng.Intn(distributionTotal-1)) + 1
	var start uint32 = 0
	var end uint32 = 0
	var percentage uint32 = 0
	for _, configUnit := range config.ConfigUnits {
		if percentage <= randP && (percentage+uint32(configUnit.Percentage*1000)) >= randP {
			start = configUnit.Start
			end = configUnit.End
		}
		percentage += uint32(configUnit.Percentage * 1000)
	}

	var rewardUnits uint32
	if start == end {
		rewardUnits = start
	} else {
		// nolint: gosec
		rewardUnits = uint32(rng.Intn(int(end-start))) + start
	}
	return float64(rewardUnits), nil
}

// nolint:funlen
func (rc *defaultRewardUnitsCalculator) CalculateUsingExpressionRangeProbabilityConfig(seedUnitValue float64, config *rewardOfferPb.ExpressionRangeProbabilityConfig, fact common.IFact) (float64, error) {
	if len(config.GetRangeProbabilityUnits()) == 0 {
		return 0.0, fmt.Errorf("invalid expression range probability config: range units empty")
	}
	if seedUnitValue < 0 {
		return 0.0, fmt.Errorf("invalid expression range probability config: negative seed unit value")
	}
	if config.GetUpperLimit() < 0 || config.GetLowerLimit() < 0 {
		return 0.0, fmt.Errorf("invalid expression range probability config: negative upper/lower limit value")
	}
	if config.GetUpperLimit() < config.GetLowerLimit() {
		return 0.0, fmt.Errorf("invalid expression range probability config: upper limit < lower limit")
	}

	type RangeUnit struct {
		Start      int32
		End        int32
		Percentage float32
	}
	var rangeUnits []*RangeUnit

	distributionTotal := 0
	for _, x := range config.GetRangeProbabilityUnits() {
		// check for invalid range
		if x.UnitPercentageStart > x.UnitPercentageEnd || x.UnitPercentageStart < 0 || x.UnitPercentageEnd < 0 {
			return 0.0, fmt.Errorf("invalid range probability config unit %v, %v", x.UnitPercentageStart, x.UnitPercentageEnd)
		}

		start := int32((float32(seedUnitValue) * x.GetUnitPercentageStart()) / 100)
		end := int32((float32(seedUnitValue) * x.GetUnitPercentageEnd()) / 100)

		// if the reward/projection's action time is before August 1, 2025 (prod), ceil the start and end of range to nearest integer.
		// note : this may cause different reward units from the same reward offer for the exact same transaction value over two time periods.
		if fact.GetActionTime().AsTime().Before(accrualPkg.GetFiCoinsToFiPointsMigrationTime()) {
			start = int32(math.Ceil(float64(float32(seedUnitValue)*x.GetUnitPercentageStart()) / 100))
			end = int32(math.Ceil(float64(float32(seedUnitValue)*x.GetUnitPercentageEnd()) / 100))
		}

		//  convert start and end of range from percentages to absolute value
		rangeUnits = append(rangeUnits, &RangeUnit{
			Start:      start,
			End:        end,
			Percentage: x.GetPercentage(),
		})

		// multiplying percentage by 1000 while adding to distributionTotal for supporting distribution for percentages with upto 3 decimal places precision.
		// Eg : 1.234 percentage would be added as 1234 in distributionTotal.
		distributionTotal += int(x.GetPercentage() * 1000)
	}

	// nolint: gosec
	rng := rand.New(idgen.NewCryptoSeededSource())
	randP := int32(rng.Intn(int(distributionTotal)-1)) + 1
	var start int32 = 0
	var end int32 = 0
	var percentage int32 = 0
	for _, rangeUnit := range rangeUnits {
		if percentage <= randP && (percentage+int32(rangeUnit.Percentage*1000)) >= randP {
			start = rangeUnit.Start
			end = rangeUnit.End
		}
		percentage += int32(rangeUnit.Percentage * 1000)
	}

	var rewardUnits int32
	if start == end {
		rewardUnits = start
	} else {
		// nolint: gosec
		rewardUnits = int32(rng.Intn(int(end-start))) + start
	}

	// cap reward units to upper limit specified in the config, if it's non-zero.
	if config.GetUpperLimit() != 0 && rewardUnits > config.GetUpperLimit() {
		rewardUnits = config.GetUpperLimit()
	}

	// apply lower_bound on reward units.
	if rewardUnits < config.GetLowerLimit() {
		rewardUnits = config.GetLowerLimit()
	}

	return float64(rewardUnits), nil
}

func (rc *defaultRewardUnitsCalculator) PerformMathematicalOperationOnUnits(baseUnits, newUnits int32, operation rewardOfferPb.MathematicalOperation, unitsUpperCap int32) (int32, error) {
	finalUnits := int32(0)
	switch operation {
	case rewardOfferPb.MathematicalOperation_ADDITION:
		finalUnits = baseUnits + newUnits
	case rewardOfferPb.MathematicalOperation_MULTIPLICATION:
		finalUnits = baseUnits * newUnits
	default:
		return 0, fmt.Errorf("unsupported mathematical operation: %s", operation.String())
	}

	// if units upper cap is configured, then cap the final units using to the given units cap.
	// todo (team) : make unit caps as mandatory once all the rewardOffers are migrated to use unitsUpperCap config
	if unitsUpperCap != 0 && finalUnits > unitsUpperCap {
		finalUnits = unitsUpperCap
	}
	return finalUnits, nil
}
