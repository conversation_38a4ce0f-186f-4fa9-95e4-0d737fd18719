package dao

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/pagination"
	daoModel "github.com/epifi/gamma/rewards/generator/dao/model"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock/mocks"

	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epifierrors"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	rewardsPkg "github.com/epifi/gamma/pkg/rewards"
	"github.com/epifi/gamma/rewards/generator/internalerrors"
	"github.com/epifi/gamma/rewards/generator/model"
	"github.com/epifi/gamma/rewards/test"
)

func TestCrdbRewardsDao_PersistReward(t *testing.T) {

	type args struct {
		ctx    context.Context
		reward *model.Reward
	}
	tests := []struct {
		name             string
		args             args
		wantErr          bool
		expectedErrValue error
	}{
		{
			name: "successful reward creation flow 1 (empty GlobalDedupeId, offer_type, external_ref)",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					RefId:          "txn-1",
					SecondaryRefId: rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:        "act-1",
					OfferId:        "offer-1",
					Aggregates: &rewardOffersPb.RewardAggregates{
						UserAggregate:   1,
						ActionAggregate: 1,
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								Option: &rewardsPb.RewardOption_Cash{
									Cash: &rewardsPb.Cash{
										Amount: &money.Money{
											CurrencyCode: "INR",
											Units:        10,
										},
									},
								},
							}, {
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{
										Units: 0,
										ExpiresAt: &timestamp.Timestamp{
											Seconds: 123123,
											Nanos:   0,
										},
									},
								},
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "successful reward creation flow 2 (non empty GlobalDedupeId, offer_type, external_ref)",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					RefId:          "txn-1",
					SecondaryRefId: rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					GlobalDedupeId: "global-dedupe-id-2",
					ActorId:        "act-1",
					OfferId:        "offer-1",
					OfferType:      rewardsPb.RewardOfferType_REFERRAL_REFEREE_OFFER,
					Aggregates: &rewardOffersPb.RewardAggregates{
						UserAggregate:   1,
						ActionAggregate: 1,
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								Option: &rewardsPb.RewardOption_Cash{
									Cash: &rewardsPb.Cash{
										Amount: &money.Money{
											CurrencyCode: "INR",
											Units:        10,
										},
									},
								},
							}, {
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{
										Units: 0,
										ExpiresAt: &timestamp.Timestamp{
											Seconds: 123123,
											Nanos:   0,
										},
									},
								},
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "dedupe should happen, duplicate ActorId, GlobalDedupeId",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					RefId:          "txn-1",
					SecondaryRefId: rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					GlobalDedupeId: fixtureReward1.GlobalDedupeId,
					ActorId:        fixtureReward1.ActorId,
					OfferId:        "offer-1",
					Aggregates: &rewardOffersPb.RewardAggregates{
						UserAggregate:   1,
						ActionAggregate: 1,
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								Option: &rewardsPb.RewardOption_Cash{
									Cash: &rewardsPb.Cash{
										Amount: &money.Money{
											CurrencyCode: "INR",
											Units:        10,
										},
									},
								},
							}, {
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{
										Units: 0,
										ExpiresAt: &timestamp.Timestamp{
											Seconds: 123123,
											Nanos:   0,
										},
									},
								},
							},
						},
					},
				},
			},
			wantErr:          true,
			expectedErrValue: epifierrors.ErrDuplicateEntry,
		},
		{
			name: "Max offer actor aggregate reached for given offer and actor combination",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					RefId:          uuid.New().String(),
					SecondaryRefId: rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					// remaining aggregate count is zero for given offer and actor combination
					ActorId: fixtureRewardOfferActorAggregate2.ActorId,
					OfferId: fixtureRewardOfferActorAggregate2.OfferId,
					Aggregates: &rewardOffersPb.RewardAggregates{
						UserAggregate:   fixtureRewardOffer1.GetRewardMeta().GetRewardAggregates().GetUserAggregate(),
						ActionAggregate: fixtureRewardOffer1.GetRewardMeta().GetRewardAggregates().GetActionAggregate(),
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								Option: &rewardsPb.RewardOption_Cash{
									Cash: &rewardsPb.Cash{
										Amount: &money.Money{
											CurrencyCode: "INR",
											Units:        10,
										},
									},
								},
							}, {
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{
										Units: 0,
										ExpiresAt: &timestamp.Timestamp{
											Seconds: 123123,
											Nanos:   0,
										},
									},
								},
							},
						},
					},
				},
			},
			wantErr:          true,
			expectedErrValue: internalerrors.MaxRewardAggregateReached,
		},
		{
			name: "dedupe shouldn't happen, duplicate GlobalDedupeId but different actorId",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					RefId:          "txn-1",
					SecondaryRefId: rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					GlobalDedupeId: fixtureReward1.GlobalDedupeId,
					ActorId:        "act-3",
					OfferId:        "offer-1",
					Aggregates: &rewardOffersPb.RewardAggregates{
						UserAggregate:   1,
						ActionAggregate: 1,
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								Option: &rewardsPb.RewardOption_Cash{
									Cash: &rewardsPb.Cash{
										Amount: &money.Money{
											CurrencyCode: "INR",
											Units:        10,
										},
									},
								},
							}, {
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{
										Units: 0,
										ExpiresAt: &timestamp.Timestamp{
											Seconds: 123123,
											Nanos:   0,
										},
									},
								},
							},
						},
					},
				},
			},
			wantErr: false,
		},
		// todo(divyadeep): uncomment this after changes for capping are made.
		// {
		//	name: "should successfully persist all CC rewards info for a CC 1x reward",
		//	args: args{
		//		ctx: context.Background(),
		//		reward: &model.Reward{
		//			Id:         "",
		//			ExternalId: "external-id-cc-1",
		//			RefId:      "ref-id-cc-1",
		//          SecondaryRefId: rewardsPkg.RewardsSecondaryRefIdDefaultValue,
		//			ActorId:    "actor-1",
		//			OfferId:    "2b99dedf-63ae-4670-a68b-bad4b54da6c4",
		//			OfferType:  rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
		//			ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION,
		//			Status:     rewardsPb.RewardStatus_CREATED,
		//			RewardOptions: &rewardsPb.RewardOptions{
		//				Options: []*rewardsPb.RewardOption{
		//					{
		//						RewardType: rewardsPb.RewardType_FI_COINS,
		//						Option: &rewardsPb.RewardOption_FiCoins{
		//							FiCoins: &rewardsPb.FiCoins{Units: 500},
		//						},
		//					},
		//				},
		//			},
		//			Aggregates: &rewardOffersPb.RewardAggregates{
		//				Cc_1XRewardUnitsCcAccountLevelMonthlyAggregateCap: &rewardOffersPb.RewardUnitsCapAggregate{UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{{RewardType: rewardsPb.RewardType_FI_COINS, Units: 1000}}},
		//			},
		//			ExternalRef:          "external-ref-cc-1",
		//			GlobalDedupeId:       "global-cc-id-1",
		//			ProcessingRef:        "",
		//			RewardAdditionalInfo: &model.RewardAdditionalInfo{CreditCardRewardAdditionalInfo: &model.CreditCardRewardAdditionalInfo{CreditCardAccountId: "cc-account-1", CreditCardId: "cc-id", TxnTime: time.Date(2023, 3, 4, 0, 0, 0, 0, time.UTC)}},
		//		},
		//	},
		//	wantErr: false,
		// },
		{
			name: "should successfully create reward when monthly capping is specified for reward offer but generated reward amount lies within capping",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:              "",
					ExternalId:      "external-id-2",
					RefId:           "ref-id-2",
					SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:         "actor-1",
					OfferId:         "5afc94c1-4f3a-492a-8527-a4364bc2509e",
					ActionType:      rewardsPb.CollectedDataType_ORDER,
					ActionTimestamp: time.Date(2023, 3, 5, 0, 0, 0, 0, datetime.IST),
					Status:          rewardsPb.RewardStatus_CREATED,
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{Units: 500},
								},
							},
						},
					},
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapMonthlyUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{{RewardType: rewardsPb.RewardType_FI_COINS, Units: 1000}}},
					},
					ExternalRef:    "external-ref-3",
					GlobalDedupeId: "global-id-3",
					ProcessingRef:  "",
				},
			},
			wantErr: false,
		},
		{
			name: "should fail to create CC_BILL_ERASER reward when reward offer-actor level cap is hit",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:              "",
					ExternalId:      "external-id-2",
					RefId:           "ref-id-2",
					SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:         "actor-1",
					OfferId:         "5afc94c1-4f3a-492a-8527-a4364bc1235a",
					ActionType:      rewardsPb.CollectedDataType_ORDER,
					ActionTimestamp: time.Date(2023, 3, 5, 0, 0, 0, 0, datetime.IST),
					Status:          rewardsPb.RewardStatus_CREATED,
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_CREDIT_CARD_BILL_ERASER,
								Option: &rewardsPb.RewardOption_CreditCardBillEraser{
									CreditCardBillEraser: &rewardsPb.CreditCardBillEraser{Amount: &money.Money{Units: 100}},
								},
							},
						},
					},
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{
							UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
								{
									RewardType: rewardsPb.RewardType_CREDIT_CARD_BILL_ERASER,
									Units:      100,
								},
							},
						},
					},
					ExternalRef:    "external-ref-3",
					GlobalDedupeId: "global-id-3",
					ProcessingRef:  "",
				},
			},
			wantErr:          true,
			expectedErrValue: internalerrors.MaxRewardAggregateReached,
		},
		{
			name: "should fail to create CC_BILL_ERASER reward when reward offer group-actor level cap is hit",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:              "",
					ExternalId:      "external-id-2",
					RefId:           "ref-id-2",
					SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:         "actor-1",
					OfferId:         "5e5b62d8-a2f0-44cc-82de-7bc6284a3413",
					OfferGroupId:    "18b949a4-d0be-40cf-bc49-1f65916840fc",
					ActionType:      rewardsPb.CollectedDataType_ORDER,
					ActionTimestamp: time.Date(2023, 3, 5, 0, 0, 0, 0, datetime.IST),
					Status:          rewardsPb.RewardStatus_CREATED,
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_CREDIT_CARD_BILL_ERASER,
								Option: &rewardsPb.RewardOption_CreditCardBillEraser{
									CreditCardBillEraser: &rewardsPb.CreditCardBillEraser{Amount: &money.Money{Units: 100}},
								},
							},
						},
					},
					Aggregates: &rewardOffersPb.RewardAggregates{},
					OfferGroupRewardUnitsCapActorConfig: &rewardOffersPb.RewardUnitsCapAggregate{
						UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_CREDIT_CARD_BILL_ERASER,
								Units:      100,
							},
						},
					},
					ExternalRef:    "external-ref-3",
					GlobalDedupeId: "global-id-3",
					ProcessingRef:  "",
				},
			},
			wantErr:          true,
			expectedErrValue: internalerrors.MaxRewardAggregateReached,
		},
		{
			name: "should return max cap hit error when reward generated exceeds monthly cap and isHardCap is set",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:             "",
					ExternalId:     "external-id-cc-1",
					RefId:          "ref-id-cc-1",
					SecondaryRefId: rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:        "actor-1",
					OfferId:        "5afc94c1-4f3a-492a-8527-a4364bc2509e",
					ActionType:     rewardsPb.CollectedDataType_ORDER,
					Status:         rewardsPb.RewardStatus_CREATED,
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{Units: 1500},
								},
							},
						},
					},
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapMonthlyUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{{RewardType: rewardsPb.RewardType_FI_COINS, Units: 1000}}, IsHardCheck: true},
					},
					ExternalRef:    "external-ref-cc-1",
					GlobalDedupeId: "global-cc-id-1",
					ProcessingRef:  "",
				},
			},
			wantErr:          true,
			expectedErrValue: internalerrors.RewardUnitsMaxCapReached,
		},
		{
			name: "should successfully create reward when generated reward units would exceed monthly cap but hard check is disabled",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:              "",
					ExternalId:      "external-id-cc-1",
					RefId:           "ref-id-cc-1",
					SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:         "actor-1",
					OfferId:         "75021f37-aa95-43c5-9292-74826b333f97",
					ActionType:      rewardsPb.CollectedDataType_ORDER,
					ActionTimestamp: time.Date(2023, 3, 5, 0, 0, 0, 0, datetime.IST),
					Status:          rewardsPb.RewardStatus_CREATED,
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{Units: 500},
								},
							},
						},
					},
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapMonthlyUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{{RewardType: rewardsPb.RewardType_FI_COINS, Units: 1500}}},
					},
					ExternalRef:    "external-ref-cc-1",
					GlobalDedupeId: "global-cc-id-1",
					ProcessingRef:  "",
				},
			},
			wantErr: false,
		},
		{
			name: "should fail to create reward when monthly cap is hit (hard checks disabled)",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:              "",
					ExternalId:      "external-id-cc-1",
					RefId:           "ref-id-cc-1",
					SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:         "actor-1",
					OfferId:         "75021f37-aa95-43c5-9292-74826b333f97",
					ActionType:      rewardsPb.CollectedDataType_ORDER,
					ActionTimestamp: time.Date(2023, 3, 5, 0, 0, 0, 0, datetime.IST),
					Status:          rewardsPb.RewardStatus_CREATED,
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{Units: 500},
								},
							},
						},
					},
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapMonthlyUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{{RewardType: rewardsPb.RewardType_FI_COINS, Units: 1000}}},
					},
					ExternalRef:    "external-ref-cc-1",
					GlobalDedupeId: "global-cc-id-1",
					ProcessingRef:  "",
				},
			},
			wantErr:          true,
			expectedErrValue: internalerrors.RewardUnitsMaxCapReached,
		},
		{
			name: "should fail to create reward when group level monthly cap is hit (hard checks disabled)",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:              "",
					ExternalId:      "external-id-cc-1",
					RefId:           "ref-id-cc-1",
					SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:         "actor-1",
					OfferId:         "75f0b4b9-ae8b-4d35-9659-3d1124a782ca",
					OfferGroupId:    "f68b8299-d038-485e-a59c-606f53bd7f9e",
					ActionType:      rewardsPb.CollectedDataType_ORDER,
					ActionTimestamp: time.Date(2023, 3, 5, 0, 0, 0, 0, datetime.IST),
					Status:          rewardsPb.RewardStatus_CREATED,
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{Units: 500},
								},
							},
						},
					},
					OfferGroupRewardUnitsMonthlyCapActorConfig: &rewardOffersPb.RewardUnitsCapAggregate{
						UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Units:      1000,
							},
						},
						IsHardCheck: false,
					},
					Aggregates:     &rewardOffersPb.RewardAggregates{},
					ExternalRef:    "external-ref-cc-1",
					GlobalDedupeId: "global-cc-id-1",
					ProcessingRef:  "",
				},
			},
			wantErr:          true,
			expectedErrValue: internalerrors.RewardUnitsMaxCapReached,
		},
		{
			name: "should fail to create reward when monthly cap is hit (hard checks disabled, CC bill eraser reward)",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:              "",
					ExternalId:      "external-id-cc-1",
					RefId:           "ref-id-cc-1",
					SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:         "actor-1",
					OfferId:         "75021f37-aa95-43c5-9292-74826b333f97",
					ActionType:      rewardsPb.CollectedDataType_ORDER,
					ActionTimestamp: time.Date(2023, 3, 5, 0, 0, 0, 0, datetime.IST),
					Status:          rewardsPb.RewardStatus_CREATED,
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_CREDIT_CARD_BILL_ERASER,
								Option: &rewardsPb.RewardOption_CreditCardBillEraser{
									CreditCardBillEraser: &rewardsPb.CreditCardBillEraser{Amount: &money.Money{CurrencyCode: "INR", Units: 100}},
								},
							},
						},
					},
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapMonthlyUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{{RewardType: rewardsPb.RewardType_CREDIT_CARD_BILL_ERASER, Units: 100}}},
					},
					ExternalRef:    "external-ref-cc-1",
					GlobalDedupeId: "global-cc-id-1",
					ProcessingRef:  "",
				},
			},
			wantErr:          true,
			expectedErrValue: internalerrors.RewardUnitsMaxCapReached,
		},
		{
			name: "should fail to create reward when group level monthly cap is hit (hard checks enabled)",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:              "",
					ExternalId:      "external-id-cc-1",
					RefId:           "ref-id-cc-1",
					SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:         "actor-1",
					OfferId:         "75f0b4b9-ae8b-4d35-9659-3d1124a782ca",
					OfferGroupId:    "f68b8299-d038-485e-a59c-606f53bd7f9e",
					ActionType:      rewardsPb.CollectedDataType_ORDER,
					ActionTimestamp: time.Date(2023, 3, 5, 0, 0, 0, 0, datetime.IST),
					Status:          rewardsPb.RewardStatus_CREATED,
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{Units: 500},
								},
							},
						},
					},
					OfferGroupRewardUnitsMonthlyCapActorConfig: &rewardOffersPb.RewardUnitsCapAggregate{
						UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Units:      1400,
							},
						},
						IsHardCheck: true,
					},
					Aggregates:     &rewardOffersPb.RewardAggregates{},
					ExternalRef:    "external-ref-cc-1",
					GlobalDedupeId: "global-cc-id-1",
					ProcessingRef:  "",
				},
			},
			wantErr:          true,
			expectedErrValue: internalerrors.RewardUnitsMaxCapReached,
		},
		{
			name: "should fail to create reward when group level monthly cap is hit (hard checks enabled, cc bill eraser reward)",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:              "",
					ExternalId:      "external-id-cc-1",
					RefId:           "ref-id-cc-1",
					SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:         "actor-1",
					OfferId:         "75f0b4b9-ae8b-4d35-9659-3d1124a782ca",
					OfferGroupId:    "f68b8299-d038-485e-a59c-606f53bd7f9e",
					ActionType:      rewardsPb.CollectedDataType_ORDER,
					ActionTimestamp: time.Date(2023, 3, 5, 0, 0, 0, 0, datetime.IST),
					Status:          rewardsPb.RewardStatus_CREATED,
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_CREDIT_CARD_BILL_ERASER,
								Option: &rewardsPb.RewardOption_CreditCardBillEraser{
									CreditCardBillEraser: &rewardsPb.CreditCardBillEraser{Amount: &money.Money{CurrencyCode: "INR", Units: 100}},
								},
							},
						},
					},
					OfferGroupRewardUnitsMonthlyCapActorConfig: &rewardOffersPb.RewardUnitsCapAggregate{
						UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_CREDIT_CARD_BILL_ERASER,
								Units:      100,
							},
						},
						IsHardCheck: true,
					},
					Aggregates:     &rewardOffersPb.RewardAggregates{},
					ExternalRef:    "external-ref-cc-1",
					GlobalDedupeId: "global-cc-id-1",
					ProcessingRef:  "",
				},
			},
			wantErr:          true,
			expectedErrValue: internalerrors.RewardUnitsMaxCapReached,
		},
		{
			name: "should successfully create reward when no group level monthly cap is hit",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:              "",
					ExternalId:      "external-id-cc-1",
					RefId:           "ref-id-cc-1",
					SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:         "actor-1",
					OfferId:         "75f0b4b9-ae8b-4d35-9659-3d1124a782ca",
					OfferGroupId:    "f68b8299-d038-485e-a59c-606f53bd7f9e",
					ActionType:      rewardsPb.CollectedDataType_ORDER,
					ActionTimestamp: time.Date(2023, 3, 5, 0, 0, 0, 0, datetime.IST),
					Status:          rewardsPb.RewardStatus_CREATED,
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{Units: 500},
								},
							},
						},
					},
					Aggregates:     &rewardOffersPb.RewardAggregates{},
					ExternalRef:    "external-ref-cc-1",
					GlobalDedupeId: "global-cc-id-1",
					ProcessingRef:  "",
				},
			},
			wantErr: false,
		},
		{
			name: "should fail to create reward when monthly generated reward counts against an offer cap is hit",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:              "",
					ExternalId:      "external-id-cc-1",
					RefId:           "ref-id-cc-1",
					SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:         "actor-1",
					OfferId:         "75f0b4b9-ae8b-4d35-9659-3d1124a782ca",
					OfferGroupId:    "f68b8299-d038-485e-a59c-606f53bd7f9e",
					ActionType:      rewardsPb.CollectedDataType_ORDER,
					ActionTimestamp: time.Date(2023, 3, 5, 0, 0, 0, 0, datetime.IST),
					Status:          rewardsPb.RewardStatus_CREATED,
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{Units: 500},
								},
							},
						},
					},
					Aggregates: &rewardOffersPb.RewardAggregates{
						UserAggregateInTimePeriod: &rewardOffersPb.UserAggregateInTimePeriod{
							MonthlyAggregate: 1,
						},
					},
					ExternalRef:    "external-ref-cc-1",
					GlobalDedupeId: "global-cc-id-1",
					ProcessingRef:  "",
				},
			},
			wantErr:          true,
			expectedErrValue: internalerrors.MaxRewardAggregateReached,
		},
		{
			name: "should fail to create reward when monthly generated reward counts against an offer-group cap is hit",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:              "",
					ExternalId:      "external-id-cc-1",
					RefId:           "ref-id-cc-1",
					SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:         "actor-1",
					OfferId:         "75f0b4b9-ae8b-4d35-9659-3d1124a782ca",
					OfferGroupId:    "f68b8299-d038-485e-a59c-606f53bd7f9e",
					ActionType:      rewardsPb.CollectedDataType_ORDER,
					ActionTimestamp: time.Date(2023, 3, 5, 0, 0, 0, 0, datetime.IST),
					Status:          rewardsPb.RewardStatus_CREATED,
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{Units: 500},
								},
							},
						},
					},
					OfferGroupActorAggregateInTimePeriod: &rewardOffersPb.UserAggregateInTimePeriod{
						MonthlyAggregate: 1,
					},
					Aggregates:     &rewardOffersPb.RewardAggregates{},
					ExternalRef:    "external-ref-cc-1",
					GlobalDedupeId: "global-cc-id-1",
					ProcessingRef:  "",
				},
			},
			wantErr:          true,
			expectedErrValue: internalerrors.MaxRewardAggregateReached,
		},
		{
			name: "should fail to create reward when quarterly generated reward counts against an offer cap is hit",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:              "",
					ExternalId:      "external-id-cc-1",
					RefId:           "ref-id-cc-1",
					SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:         "actor-1",
					OfferId:         "75f0b4b9-ae8b-4d35-9659-3d1124a782ca",
					OfferGroupId:    "f68b8299-d038-485e-a59c-606f53bd7f9e",
					ActionType:      rewardsPb.CollectedDataType_ORDER,
					ActionTimestamp: time.Date(2023, 1, 5, 0, 0, 0, 0, datetime.IST),
					Status:          rewardsPb.RewardStatus_CREATED,
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{Units: 500},
								},
							},
						},
					},
					Aggregates: &rewardOffersPb.RewardAggregates{
						UserAggregateInTimePeriod: &rewardOffersPb.UserAggregateInTimePeriod{
							QuarterlyAggregate: 3,
						},
					},
					ExternalRef:    "external-ref-cc-1",
					GlobalDedupeId: "global-cc-id-1",
					ProcessingRef:  "",
				},
			},
			wantErr:          true,
			expectedErrValue: internalerrors.MaxRewardAggregateReached,
		},
		{
			name: "should fail to create reward when quarterly generated reward counts against an offer-group cap is hit",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:              "",
					ExternalId:      "external-id-cc-1",
					RefId:           "ref-id-cc-1",
					SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:         "actor-1",
					OfferId:         "75f0b4b9-ae8b-4d35-9659-3d1124a782ca",
					OfferGroupId:    "f68b8299-d038-485e-a59c-606f53bd7f9e",
					ActionType:      rewardsPb.CollectedDataType_ORDER,
					ActionTimestamp: time.Date(2023, 1, 5, 0, 0, 0, 0, datetime.IST),
					Status:          rewardsPb.RewardStatus_CREATED,
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{Units: 500},
								},
							},
						},
					},
					OfferGroupActorAggregateInTimePeriod: &rewardOffersPb.UserAggregateInTimePeriod{
						QuarterlyAggregate: 3,
					},
					Aggregates:     &rewardOffersPb.RewardAggregates{},
					ExternalRef:    "external-ref-cc-1",
					GlobalDedupeId: "global-cc-id-1",
					ProcessingRef:  "",
				},
			},
			wantErr:          true,
			expectedErrValue: internalerrors.MaxRewardAggregateReached,
		},
		{
			name: "should successfully create reward when no cap is hit for group offer monthly aggregate for cash reward type",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:              "",
					ExternalId:      "external-id-cc-1",
					RefId:           "ref-id-cc-1",
					SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
					ActorId:         "actor-1",
					OfferId:         "75f0b4b9-ae8b-4d35-9659-3d1124a782ca",
					OfferGroupId:    "f68b8299-d038-485e-a59c-606f53bd7f9e",
					ActionType:      rewardsPb.CollectedDataType_ORDER,
					ActionTimestamp: time.Date(2023, 1, 5, 0, 0, 0, 0, datetime.IST),
					Status:          rewardsPb.RewardStatus_CREATED,
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{Units: 500},
								},
							},
						},
					},
					OfferGroupActorAggregateInTimePeriod: &rewardOffersPb.UserAggregateInTimePeriod{
						MonthlyAggregate: 3,
					},
					Aggregates:     &rewardOffersPb.RewardAggregates{},
					ExternalRef:    "external-ref-cc-1",
					GlobalDedupeId: "global-cc-id-1",
					ProcessingRef:  "",
				},
			},
			wantErr: false,
		},
	}
	// nolint: scopelint
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)
			ctr := gomock.NewController(t)
			mockRedisLockManager := mocks.NewMockILockManager(ctr)
			rewardDaoWithRedis := NewRewardsDao(db, idgen.NewDomainIdGenerator(idgen.NewClock()), mockRedisLockManager)

			err := rewardDaoWithRedis.PersistReward(tc.args.ctx, tc.args.reward)
			if (err != nil) != tc.wantErr {
				t.Errorf("PersistReward() error = %v, wantErr %v", err, tc.wantErr)
			}
			// if error is expected and expectedErrValue is present, validate the actual error with expected error.
			if tc.wantErr && tc.expectedErrValue != nil && !errors.Is(err, tc.expectedErrValue) {
				t.Errorf("expected error %v, got error = %v", tc.expectedErrValue, err)
				return
			}
		})
	}
}

func TestCrdbRewardsDao_ChooseReward(t *testing.T) {

	type args struct {
		ctx              context.Context
		reward           *model.Reward
		chosenOption     *rewardsPb.RewardOption
		currentStatus    rewardsPb.RewardStatus
		currentSubStatus rewardsPb.SubStatus
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "no reward exists with given id",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id: uuid.New().String(),
				},
				chosenOption: &rewardsPb.RewardOption{
					Option: &rewardsPb.RewardOption_FiCoins{
						FiCoins: &rewardsPb.FiCoins{
							Units: 100,
							ExpiresAt: &timestamp.Timestamp{
								Seconds: 123123,
								Nanos:   0,
							},
						},
					},
				},
				currentStatus: rewardsPb.RewardStatus_CREATED,
			},
			wantErr: true,
		},
		{
			name: "cannot chose reward option, reward not in created state",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id: fixtureReward3.Id,
				},
				chosenOption: &rewardsPb.RewardOption{
					Option: &rewardsPb.RewardOption_FiCoins{
						FiCoins: &rewardsPb.FiCoins{
							Units: 100,
							ExpiresAt: &timestamp.Timestamp{
								Seconds: 123123,
								Nanos:   0,
							},
						},
					},
				},
				currentStatus: rewardsPb.RewardStatus_CREATED,
			},
			wantErr: true,
		},
		{
			name: "chose reward option successful",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id: fixtureReward2.Id,
				},
				chosenOption: &rewardsPb.RewardOption{
					Option: &rewardsPb.RewardOption_FiCoins{
						FiCoins: &rewardsPb.FiCoins{
							Units: 0,
							ExpiresAt: &timestamp.Timestamp{
								Seconds: 123123,
								Nanos:   0,
							},
						},
					},
				},
				currentStatus: rewardsPb.RewardStatus_CREATED,
			},
			wantErr: false,
		},
		{
			name: "chose reward option successfully (and also set the top level fields)",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Id:   fixtureReward2.Id,
					Tags: []rewardsPb.RewardTag{rewardsPb.RewardTag_BOOSTER_1X},
				},
				chosenOption: &rewardsPb.RewardOption{
					Option: &rewardsPb.RewardOption_FiCoins{
						FiCoins: &rewardsPb.FiCoins{
							Units: 0,
							ExpiresAt: &timestamp.Timestamp{
								Seconds: 123123,
								Nanos:   0,
							},
						},
					},
					Display: &rewardsPb.RewardOptionDisplay{
						Tags: []rewardsPb.RewardTag{rewardsPb.RewardTag_TIER_FI_BASIC, rewardsPb.RewardTag_BOOSTER_2X},
					},
				},
				currentStatus: rewardsPb.RewardStatus_CREATED,
			},
			wantErr: false,
		},
	}
	// nolint: scopelint
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)

			err := rewardDao.ChooseReward(tc.args.ctx, tc.args.reward, tc.args.chosenOption, tc.args.currentStatus, tc.args.currentSubStatus)
			if (err != nil) != tc.wantErr {
				t.Errorf("ChooseReward() error = %v, wantErr %v", err, tc.wantErr)
			}
		})
	}
}

func TestCrdbRewardsDao_FetchReward(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)

	reward := &model.Reward{
		RefId:          "txn-1",
		SecondaryRefId: rewardsPkg.RewardsSecondaryRefIdDefaultValue,
		ActorId:        "act-1",
		OfferId:        "offer-1",
		Status:         0,
		Aggregates: &rewardOffersPb.RewardAggregates{
			UserAggregate:   1,
			ActionAggregate: 1,
		},
		RewardOptions: &rewardsPb.RewardOptions{
			Options: []*rewardsPb.RewardOption{
				{
					Option: &rewardsPb.RewardOption_Cash{
						Cash: &rewardsPb.Cash{
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        10,
							},
						},
					},
				}, {
					Option: &rewardsPb.RewardOption_FiCoins{
						FiCoins: &rewardsPb.FiCoins{
							Units: 0,
							ExpiresAt: &timestamp.Timestamp{
								Seconds: 123123,
								Nanos:   0,
							},
						},
					},
				},
			},
		},
	}

	var id string

	t.Run("Persist and Fetch Cash Reward", func(t *testing.T) {
		err := rewardDao.PersistReward(context.Background(), reward)
		assert.Nil(t, err)
		res, err := rewardDao.FetchRewards(context.Background(), &model.QueryRewardsFilter{ActorId: "act-1"})
		assert.Nil(t, err)
		assert.Equal(t, 1, len(res))
		rw := res[0]
		assert.Equal(t, reward.ActorId, rw.ActorId)
		assert.Equal(t, reward.RefId, rw.RefId)
		assert.Equal(t, reward.OfferId, rw.OfferId)
		assert.Equal(t, reward.Status, rw.Status)
		id = rw.Id
	})

	t.Run("Update reward", func(t *testing.T) {
		count, err := rewardDao.UpdateRewardStatus(context.Background(), id, rewardsPb.RewardStatus_PROCESSING_PENDING, rewardsPb.SubStatus_SUB_STATUS_UNSPECIFIED)
		assert.Equal(t, int64(1), count)
		assert.Nil(t, err)
		res, err := rewardDao.FetchRewards(context.Background(), &model.QueryRewardsFilter{ActorId: "act-1"})
		assert.Nil(t, err)
		assert.Equal(t, 1, len(res))
		rw := res[0]
		assert.Equal(t, rw.Id, id)
		assert.Equal(t, rw.Status, rewardsPb.RewardStatus_PROCESSING_PENDING)
	})

	type args struct {
		ctx context.Context
		req *model.QueryRewardsFilter
	}
	tests := []struct {
		name    string
		args    args
		want    int
		wantErr bool
	}{
		{
			name: "fetch rewards by actor id",
			args: args{
				ctx: context.Background(),
				req: &model.QueryRewardsFilter{
					ActorId: "act-5",
				},
			},
			want:    5,
			wantErr: false,
		},
		{
			name: "fetch rewards by ids - 1",
			args: args{
				ctx: context.Background(),
				req: &model.QueryRewardsFilter{
					RewardIds: []string{"RW200911WjgI+xGYRY+0PhNl96WmYA==", "RW200911WjgI+xGYRY+0PhNl96WmYB=="},
				},
			},
			want:    2,
			wantErr: false,
		},
		{
			name: "fetch rewards by ids - 2",
			args: args{
				ctx: context.Background(),
				req: &model.QueryRewardsFilter{
					RewardIds: []string{uuid.New().String(), "RW200911WjgI+xGYRY+0PhNl96WmYA=="},
				},
			},
			want:    1,
			wantErr: false,
		},
		{
			name: "fetch rewards by external_ref ids - 1",
			args: args{
				ctx: context.Background(),
				req: &model.QueryRewardsFilter{
					ExternalRefIds: []string{"external-ref-1", "external-ref-2"},
				},
			},
			want:    2,
			wantErr: false,
		},
		{
			name: "fetch rewards by external_ref ids - 2",
			args: args{
				ctx: context.Background(),
				req: &model.QueryRewardsFilter{
					ExternalRefIds: []string{uuid.New().String(), "external-ref-2"},
				},
			},
			want:    1,
			wantErr: false,
		},
		{
			name: "fetch rewards by external_ref and offer_type - 1",
			args: args{
				ctx: context.Background(),
				req: &model.QueryRewardsFilter{
					ExternalRefIds:   []string{"external-ref-1"},
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFEREE_OFFER},
				},
			},
			want:    1,
			wantErr: false,
		},
		{
			name: "fetch rewards by external_ref and offer_type, does not exists case",
			args: args{
				ctx: context.Background(),
				req: &model.QueryRewardsFilter{
					ExternalRefIds:   []string{"external-ref-1"},
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
				},
			},
			want:    0,
			wantErr: false,
		},
		{
			name: "fetch rewards without mandatory params, should return error",
			args: args{
				ctx: context.Background(),
				req: &model.QueryRewardsFilter{},
			},
			want:    0,
			wantErr: true,
		},
		{
			name: "should fetch rewards earned after fromTime when it's present in filter",
			args: args{
				ctx: context.Background(),
				req: &model.QueryRewardsFilter{
					ActorId:  "act-5",
					FromTime: time.Date(2020, 9, 14, 0, 0, 0, 0, time.UTC),
				},
			},
			want:    2,
			wantErr: false,
		},
		{
			name: "should fetch rewards earned before uptoTime when it's present in filter",
			args: args{
				ctx: context.Background(),
				req: &model.QueryRewardsFilter{
					ActorId:  "act-5",
					UptoTime: time.Date(2020, 9, 14, 0, 0, 0, 0, time.UTC),
				},
			},
			want:    3,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CrdbRewardsDao{
				DB: db,
			}
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)

			got, err := s.FetchRewards(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchRewards() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(len(got), tt.want) {
				t.Errorf("FetchRewards() got = %v, want %v", len(got), tt.want)
			}
		})
	}
}

func TestCrdbRewardsDao_FetchRewardsByPageAndFilter(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)
	type args struct {
		ctx       context.Context
		filter    *model.QueryRewardsFilter
		pageToken *pagination.PageToken
	}
	tests := []struct {
		name         string
		args         args
		want         int
		wantErr      bool
		wantHasAfter bool
	}{
		{
			name: "first page",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryRewardsFilter{
					ActorId:        "act-5",
					PageSize:       5,
					VisibilityType: rewardsPb.VisibilityType_VISIBILITY_TYPE_UNSPECIFIED,
				},
			},
			want:    5,
			wantErr: false,
		},
		{
			name: "last page with visibility check",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryRewardsFilter{
					ActorId:        "act-5",
					PageSize:       2,
					VisibilityType: rewardsPb.VisibilityType_HIDDEN,
				},
			},
			want:    1,
			wantErr: false,
		},
		{
			name: "check reward offer id filter",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryRewardsFilter{
					ActorId:       "act-5",
					PageSize:      4,
					RewardOfferId: "offer-1",
				},
			},
			want:    4,
			wantErr: false,
		},
		{
			name: "check external ref list filter",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryRewardsFilter{
					ActorId:        "act-6",
					PageSize:       4,
					ExternalRefIds: []string{"external-ref-1", "external-ref-2"},
				},
			},
			want:    2,
			wantErr: false,
		},
		{
			name: "check external ref list filter, no entry exists",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryRewardsFilter{
					ActorId:        "act-5",
					PageSize:       4,
					ExternalRefIds: []string{"external-ref-1", "external-ref-2"},
				},
			},
			want:    0,
			wantErr: false,
		},
		{
			name: "check reward offer type filter",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryRewardsFilter{
					ActorId:          "act-6",
					PageSize:         4,
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
				},
			},
			want:    1,
			wantErr: false,
		},
		{
			name: "check reward offer type filter, no entry exists",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryRewardsFilter{
					ActorId:          "act-5",
					PageSize:         4,
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
				},
			},
			want:    0,
			wantErr: false,
		},
		{
			name: "no reward exists with given reward offer id",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryRewardsFilter{
					ActorId:       "act-5",
					PageSize:      2,
					RewardOfferId: "offer-10",
				},
			},
			want:    0,
			wantErr: false,
		},
		{
			name: "empty list page",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryRewardsFilter{
					ActorId:        "act-7",
					PageSize:       2,
					VisibilityType: rewardsPb.VisibilityType_HIDDEN,
				},
			},
			want:    0,
			wantErr: false,
		},
		{
			name: "should return pageCtxRes when there are more results available after the current set of results",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryRewardsFilter{
					ActorId:        "act-5",
					PageSize:       2,
					VisibilityType: rewardsPb.VisibilityType_VISIBILITY_TYPE_UNSPECIFIED,
				},
			},
			want:         2,
			wantErr:      false,
			wantHasAfter: true,
		},
		{
			name: "should return correct number of rewards when fromTime filter is passed",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryRewardsFilter{
					ActorId:  "act-5",
					PageSize: 5,
					FromTime: time.Date(2020, 9, 12, 0, 0, 0, 0, time.UTC),
				},
			},
			want:         4,
			wantErr:      false,
			wantHasAfter: false,
		},
		{
			name: "should return correct number of rewards when uptoTime filter is passed",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryRewardsFilter{
					ActorId:  "act-5",
					PageSize: 5,
					UptoTime: time.Date(2020, 9, 13, 0, 0, 0, 0, time.UTC),
				},
			},
			want:         2,
			wantErr:      false,
			wantHasAfter: false,
		},
		{
			name: "should return correct number of rewards for refIds filter",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryRewardsFilter{
					ActorId:  "act-5",
					PageSize: 10,
					RefIds:   []string{"txn-1"},
				},
			},
			want:         1,
			wantErr:      false,
			wantHasAfter: false,
		},
		{
			name: "should return correct number of rewards for actionTypes filter",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryRewardsFilter{
					ActorId:     "act-1-2",
					PageSize:    10,
					ActionTypes: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_ORDER},
				},
			},
			want:         2,
			wantErr:      false,
			wantHasAfter: false,
		},
		{
			name: "should return correct number of rewards for offer type and ref IDs filter when actor ID is not passed",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryRewardsFilter{
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
					RefIds:           []string{"txn-10"},
					PageSize:         10,
				},
			},
			want:         1,
			wantErr:      false,
			wantHasAfter: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CrdbRewardsDao{
				DB: db,
			}
			got, pageCtxRes, err := s.FetchRewardsByPageAndFilter(tt.args.ctx, tt.args.filter, tt.args.pageToken)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchRewardsByPageAndFilter() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(len(got), tt.want) {
				t.Errorf("FetchRewardsByPageAndFilter() got = %v, want %v", len(got), tt.want)
			}
			if pageCtxRes.HasAfter != tt.wantHasAfter {
				t.Errorf("FetchRewardsByPageAndFilter() pageCtxRes.HasAfter = %v, wantHasAfter %v", pageCtxRes.HasAfter, tt.wantHasAfter)
			}
		})
	}
}

func TestCrdbRewardsDao_FetchByActorAndOfferId(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)
	type args struct {
		ctx     context.Context
		actorId string
		offerId string
	}
	tests := []struct {
		name        string
		args        args
		wantRespLen int
		wantErr     bool
	}{
		{
			name: "no rewards exists for given actor-id offer-id combination",
			args: args{
				ctx:     context.Background(),
				actorId: "new-actor-1",
				offerId: "offer-1",
			},
			wantRespLen: 0,
			wantErr:     false,
		},
		{
			name: "rewards exists for given offer-id actor-id combination",
			args: args{
				ctx:     context.Background(),
				actorId: "act-5",
				offerId: "offer-1",
			},
			wantRespLen: 4,
			wantErr:     false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CrdbRewardsDao{
				DB: db,
			}
			got, err := s.FetchByActorAndOfferId(tt.args.ctx, tt.args.actorId, tt.args.offerId)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchByActorAndOfferId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(len(got), tt.wantRespLen) {
				t.Errorf("FetchByActorAndOfferId() got = %v, wantRespLen %v", got, tt.wantRespLen)
			}
		})
	}
}

func TestCrdbRewardsDao_upsertOfferRewardUnitsUtilisationForActor(t *testing.T) {
	pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)

	type args struct {
		ctx    context.Context
		reward *model.Reward
		tx     *gormv2.DB
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "Doesn't return any error if no caps config is defined",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Aggregates: &rewardOffersPb.RewardAggregates{
						UserAggregate: 5,
					},
				},
				tx: db,
			},
			wantErr: false,
		},
		{
			name: "Returns an error during the flow of creation of actor's utilisation if caps config is defined for unsupported rewardType",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId: "randomActorIdUpsert9191",
					OfferId: "randomOfferIdUpsert9191",
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{
							UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
								{
									RewardType: rewardsPb.RewardType_LUCKY_DRAW,
									Units:      100,
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: true,
		},
		{
			name: "creates new entry for actor, updates the utilised reward-units and returns no error",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId: "randomActorIdUpsert9192",
					OfferId: "randomOfferIdUpsert9192",
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{
							IsHardCheck: true,
							UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
								{
									RewardType: rewardsPb.RewardType_FI_COINS,
									Units:      100,
								},
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{
										Units: 90,
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: false,
		},
		{
			// rollback is handled by the txn block in which this method will be called.
			// adding the test case to depict the actual usage behaviour
			name: "creates new entry for actor and rollbacks it if the new reward-units exceeds the caps config (by returning error)",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId: "randomActorIdUpsert9192",
					OfferId: "randomOfferIdUpsert9192",
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{
							IsHardCheck: true,
							UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
								{
									RewardType: rewardsPb.RewardType_FI_COINS,
									Units:      100,
								},
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{
										Units: 120,
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: true,
		},
		{
			name: "No error is returned if no config is found for new reward-units rewardType (supported rewardType). Actor's utilisation entry already exists.",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId: "randomActorIdUpsert",
					OfferId: "randomOfferIdUpsert",
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{
							IsHardCheck: true,
							UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
								{
									RewardType: rewardsPb.RewardType_FI_COINS,
									Units:      100,
								},
								{
									RewardType: rewardsPb.RewardType_CASH,
									Units:      200,
								},
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_SMART_DEPOSIT,
								Option: &rewardsPb.RewardOption_SmartDeposit{
									SmartDeposit: &rewardsPb.SmartDeposit{
										Amount: &money.Money{
											CurrencyCode: "INR",
											Units:        123,
										},
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: false,
		},
		{
			name: "No error is returned if no config is found for new reward-units rewardType (unsupported rewardType). Actor's utilisation entry already exists.",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId: "randomActorIdUpsert",
					OfferId: "randomOfferIdUpsert",
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{
							IsHardCheck: true,
							UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
								{
									RewardType: rewardsPb.RewardType_FI_COINS,
									Units:      500,
								},
								{
									RewardType: rewardsPb.RewardType_CASH,
									Units:      200,
								},
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_GIFT_HAMPER,
								Option: &rewardsPb.RewardOption_GiftHamper{
									GiftHamper: &rewardsPb.GiftHamper{
										ProductName: "random gift hamper",
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: false,
		},
		{
			name: "updates the actor's utilisation with the new reward-units if config and current utilisation exist for the rewardType",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId: "randomActorIdUpsert",
					OfferId: "randomOfferIdUpsert",
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{
							IsHardCheck: true,
							UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
								{
									RewardType: rewardsPb.RewardType_FI_COINS,
									Units:      500,
								},
								{
									RewardType: rewardsPb.RewardType_CASH,
									Units:      200,
								},
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{
										Units: 93,
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: false,
		},
		{
			// soft checks for caps is performed, i.e. if the cap hasn't reached, then we allow the whole of new-reward-units to be rewarded instead of capping to available units
			name: "updates the actor's utilisation with the new reward-units if config and current utilisation exist for the rewardType (soft check of caps)",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId: "randomActorIdUpsert",
					OfferId: "randomOfferIdUpsert",
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{
							UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
								{
									RewardType: rewardsPb.RewardType_FI_COINS,
									Units:      500,
								},
								{
									RewardType: rewardsPb.RewardType_CASH,
									Units:      200,
								},
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{
										Units: 500,
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: false,
		},
		{
			name: "fails to update the actor's utilisation if it has already touched the cap for the rewardType",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId: "randomActorIdUpsert",
					OfferId: "randomOfferIdUpsert",
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{
							IsHardCheck: true,
							UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
								{
									RewardType: rewardsPb.RewardType_FI_COINS,
									Units:      200,
								},
								{
									RewardType: rewardsPb.RewardType_CASH,
									Units:      200,
								},
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{
										Units: 1,
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: true,
		},
		{
			name: "fails to update the actor's utilisation if it wasn't created with the reward-units' rewardType (i.e. new cap config added in the offer later)",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId: "randomActorIdUpsert",
					OfferId: "randomOfferIdUpsert",
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{
							IsHardCheck: true,
							UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
								{
									RewardType: rewardsPb.RewardType_FI_COINS,
									Units:      200,
								},
								{
									RewardType: rewardsPb.RewardType_CASH,
									Units:      200,
								},
								// new config added later post the creation of actor's entry
								{
									RewardType: rewardsPb.RewardType_SMART_DEPOSIT,
									Units:      100,
								},
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_SMART_DEPOSIT,
								Option: &rewardsPb.RewardOption_SmartDeposit{
									SmartDeposit: &rewardsPb.SmartDeposit{
										Amount: &money.Money{
											CurrencyCode: "INR",
											Units:        23,
										},
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: true,
		},
		{
			name: "(usstocks rewardType)creates new entry for actor, updates the utilised reward-units and returns no error",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId: "usstocksActorIdUpsert9192",
					OfferId: "usstocksOfferIdUpsert9192",
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{
							IsHardCheck: true,
							UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
								{
									RewardType: rewardsPb.RewardType_US_STOCK,
									Units:      100,
								},
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_US_STOCK,
								Option: &rewardsPb.RewardOption_UsstockReward{
									UsstockReward: &rewardsPb.USStockReward{
										Amount: &money.Money{
											CurrencyCode: "INR",
											Units:        10,
										},
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: false,
		},
		{
			// rollback is handled by the txn block in which this method will be called.
			// adding the test case to depict the actual usage behaviour
			name: "(usstock reward type) creates new entry for actor and rollbacks it if the new reward-units exceeds the caps config (by returning error)",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId: "usstocksActorIdUpsert9192",
					OfferId: "usstocksOfferIdUpsert9192",
					Aggregates: &rewardOffersPb.RewardAggregates{
						RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{
							IsHardCheck: true,
							UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
								{
									RewardType: rewardsPb.RewardType_US_STOCK,
									Units:      100,
								},
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_US_STOCK,
								Option: &rewardsPb.RewardOption_UsstockReward{
									UsstockReward: &rewardsPb.USStockReward{
										Amount: &money.Money{
											CurrencyCode: "INR",
											Units:        220,
										},
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CrdbRewardsDao{
				DB: db,
			}
			// running it in a txn block to write unit tests covering similar behaviour of actual usage
			txErr := s.DB.Transaction(func(tx *gormv2.DB) error {
				err := s.upsertOfferRewardUnitsUtilisationForActor(tt.args.ctx, tt.args.reward, tx)
				if err != nil {
					return err
				}

				return nil
			})

			if (txErr != nil) != tt.wantErr {
				t.Errorf("upsertOfferRewardUnitsUtilisationForActor() error = %v, wantErr %v", txErr, tt.wantErr)
			}
		})
	}
}

func TestCrdbRewardsDao_upsertOfferGroupRewardUnitsUtilisationForActor(t *testing.T) {
	pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)

	type args struct {
		ctx    context.Context
		reward *model.Reward
		tx     *gormv2.DB
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "Doesn't return any error if no caps config is defined",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					Aggregates: &rewardOffersPb.RewardAggregates{
						UserAggregate: 5,
					},
				},
				tx: db,
			},
			wantErr: false,
		},
		{
			name: "Returns an error during the flow of creation of actor's utilisation if caps config is defined for unsupported rewardType",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId:      "randomActorIdUpsert9191",
					OfferGroupId: "randomOfferGroupIdUpsert9191",
					OfferGroupRewardUnitsCapActorConfig: &rewardOffersPb.RewardUnitsCapAggregate{
						IsHardCheck: true,
						UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_LUCKY_DRAW,
								Units:      100,
							},
						},
					},
				},
				tx: db,
			},
			wantErr: true,
		},
		{
			name: "creates new entry for actor, updates the utilised reward-units and returns no error",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId:      "randomActorIdUpsert9192",
					OfferGroupId: "randomOfferGroupIdUpsert9192",
					OfferGroupRewardUnitsCapActorConfig: &rewardOffersPb.RewardUnitsCapAggregate{
						IsHardCheck: true,
						UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Units:      100,
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{
										Units: 90,
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: false,
		},
		{
			// rollback is handled by the txn block in which this method will be called.
			// adding the test case to depict the actual usage behaviour
			name: "creates new entry for actor and rollbacks it if the new reward-units exceeds the caps config (by returning error)",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId:      "randomActorIdUpsert9192",
					OfferGroupId: "randomOfferGroupIdUpsert9192",
					OfferGroupRewardUnitsCapActorConfig: &rewardOffersPb.RewardUnitsCapAggregate{
						IsHardCheck: true,
						UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Units:      100,
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{
										Units: 120,
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: true,
		},
		{
			name: "No error is returned if no config is found for new reward-units rewardType (supported rewardType). Actor's utilisation entry already exists.",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId:      "randomActorIdUpsert",
					OfferGroupId: "randomOfferGroupIdUpsert",
					OfferGroupRewardUnitsCapActorConfig: &rewardOffersPb.RewardUnitsCapAggregate{
						IsHardCheck: true,
						UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Units:      100,
							},
							{
								RewardType: rewardsPb.RewardType_CASH,
								Units:      200,
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_SMART_DEPOSIT,
								Option: &rewardsPb.RewardOption_SmartDeposit{
									SmartDeposit: &rewardsPb.SmartDeposit{
										Amount: &money.Money{
											CurrencyCode: "INR",
											Units:        123,
										},
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: false,
		},
		{
			name: "No error is returned if no config is found for new reward-units rewardType (unsupported rewardType). Actor's utilisation entry already exists.",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId:      "randomActorIdUpsert",
					OfferGroupId: "randomOfferGroupIdUpsert",
					OfferGroupRewardUnitsCapActorConfig: &rewardOffersPb.RewardUnitsCapAggregate{
						IsHardCheck: true,
						UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Units:      500,
							},
							{
								RewardType: rewardsPb.RewardType_CASH,
								Units:      200,
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_GIFT_HAMPER,
								Option: &rewardsPb.RewardOption_GiftHamper{
									GiftHamper: &rewardsPb.GiftHamper{
										ProductName: "random gift hamper",
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: false,
		},
		{
			name: "updates the actor's utilisation with the new reward-units if config and current utilisation exist for the rewardType",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId:      "randomActorIdUpsert",
					OfferGroupId: "randomOfferGroupIdUpsert",
					OfferGroupRewardUnitsCapActorConfig: &rewardOffersPb.RewardUnitsCapAggregate{
						IsHardCheck: true,
						UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Units:      500,
							},
							{
								RewardType: rewardsPb.RewardType_CASH,
								Units:      200,
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{
										Units: 93,
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: false,
		},
		{
			// soft checks for caps is performed, i.e. if the cap hasn't reached, then we allow the whole of new-reward-units to be rewarded instead of capping to available units
			name: "updates the actor's utilisation with the new reward-units if config and current utilisation exist for the rewardType",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId:      "randomActorIdUpsert",
					OfferGroupId: "randomOfferGroupIdUpsert",
					OfferGroupRewardUnitsCapActorConfig: &rewardOffersPb.RewardUnitsCapAggregate{
						UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Units:      500,
							},
							{
								RewardType: rewardsPb.RewardType_CASH,
								Units:      200,
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{
										Units: 500,
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: false,
		},
		{
			name: "fails to update the actor's utilisation if it has already touched the cap for the rewardType",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId:      "randomActorIdUpsert",
					OfferGroupId: "randomOfferGroupIdUpsert",
					OfferGroupRewardUnitsCapActorConfig: &rewardOffersPb.RewardUnitsCapAggregate{
						IsHardCheck: true,
						UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Units:      200,
							},
							{
								RewardType: rewardsPb.RewardType_CASH,
								Units:      200,
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Option: &rewardsPb.RewardOption_FiCoins{
									FiCoins: &rewardsPb.FiCoins{
										Units: 1,
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: true,
		},
		{
			name: "fails to update the actor's utilisation if it wasn't created with the reward-units' rewardType (i.e. new cap config added in the offer later)",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId:      "randomActorIdUpsert",
					OfferGroupId: "randomOfferGroupIdUpsert",
					OfferGroupRewardUnitsCapActorConfig: &rewardOffersPb.RewardUnitsCapAggregate{
						IsHardCheck: true,
						UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Units:      200,
							},
							{
								RewardType: rewardsPb.RewardType_CASH,
								Units:      200,
							},
							// new config added later post the creation of actor's entry
							{
								RewardType: rewardsPb.RewardType_SMART_DEPOSIT,
								Units:      100,
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_SMART_DEPOSIT,
								Option: &rewardsPb.RewardOption_SmartDeposit{
									SmartDeposit: &rewardsPb.SmartDeposit{
										Amount: &money.Money{
											CurrencyCode: "INR",
											Units:        23,
										},
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: true,
		},
		{
			name: "(usstocks rewardType)creates new entry for actor, updates the utilised reward-units and returns no error",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId:      "USStockActorIdUpsert9192",
					OfferGroupId: "USStockGroupIdUpsert9192",
					OfferGroupRewardUnitsCapActorConfig: &rewardOffersPb.RewardUnitsCapAggregate{
						IsHardCheck: true,
						UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_US_STOCK,
								Units:      100,
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_US_STOCK,
								Option: &rewardsPb.RewardOption_UsstockReward{
									UsstockReward: &rewardsPb.USStockReward{
										Amount: &money.Money{
											CurrencyCode: "INR",
											Units:        50,
										},
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: false,
		},
		{
			// rollback is handled by the txn block in which this method will be called.
			// adding the test case to depict the actual usage behaviour
			name: "(usstocks rewardType) creates new entry for actor and rollbacks it if the new reward-units exceeds the caps config (by returning error)",
			args: args{
				ctx: context.Background(),
				reward: &model.Reward{
					ActorId:      "USStockActorIdUpsert9192",
					OfferGroupId: "USStockGroupIdUpsert9192",
					OfferGroupRewardUnitsCapActorConfig: &rewardOffersPb.RewardUnitsCapAggregate{
						IsHardCheck: true,
						UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_US_STOCK,
								Units:      100,
							},
						},
					},
					RewardOptions: &rewardsPb.RewardOptions{
						Options: []*rewardsPb.RewardOption{
							{
								RewardType: rewardsPb.RewardType_US_STOCK,
								Option: &rewardsPb.RewardOption_UsstockReward{
									UsstockReward: &rewardsPb.USStockReward{
										Amount: &money.Money{
											CurrencyCode: "INR",
											Units:        150,
										},
									},
								},
							},
						},
					},
				},
				tx: db,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CrdbRewardsDao{
				DB: db,
			}
			// running it in a txn block to write unit tests covering similar behaviour of actual usage
			txErr := s.DB.Transaction(func(tx *gormv2.DB) error {
				err := s.upsertOfferGroupRewardUnitsUtilisationForActor(tt.args.ctx, tt.args.reward, tx)
				if err != nil {
					return err
				}
				return nil
			})

			if (txErr != nil) != tt.wantErr {
				t.Errorf("upsertOfferGroupRewardUnitsUtilisationForActor() error = %v, wantErr %v", txErr, tt.wantErr)
			}
		})
	}
}

func TestCrdbRewardsDao_AcquireLock(t *testing.T) {
	type args struct {
		ctx context.Context
		id  string
	}
	withoutDbTxnTests := []struct {
		name    string
		args    args
		want    *model.Reward
		wantErr bool
	}{
		{
			name: "reward exists with given id, should return error as no ongoing db txn is there for taking a lock",
			args: args{
				ctx: context.Background(),
				id:  fixtureReward1.Id,
			},
			wantErr: true,
			want:    nil,
		},
		{
			name: "invalid reward id, should return error as no ongoing db txn is there for taking a lock",
			args: args{
				ctx: context.Background(),
				id:  uuid.New().String(),
			},
			wantErr: true,
			want:    nil,
		},
	}
	for _, tt := range withoutDbTxnTests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := rewardDao.AcquireLock(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("AcquireLock() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareReward(got, tt.want) {
				t.Errorf("AcquireLock() got = %v, want %v", got, tt.want)
			}
		})
	}

	insideDbTxnTests := []struct {
		name    string
		args    args
		want    *model.Reward
		wantErr bool
	}{
		{
			name: "reward exists with given id, should return the reward",
			args: args{
				id: fixtureReward1.Id,
			},
			wantErr: false,
			want:    fixtureReward1,
		},
		{
			name: "invalid reward id, should return error",
			args: args{
				id: uuid.New().String(),
			},
			wantErr: true,
			want:    nil,
		},
	}
	for _, tt := range insideDbTxnTests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)
			txnExecutor := storageV2.NewGormTxnExecutor(db)

			_ = txnExecutor.RunTxn(context.Background(), func(txnCtx context.Context) error {
				got, err := rewardDao.AcquireLock(txnCtx, tt.args.id)
				if (err != nil) != tt.wantErr {
					t.Errorf("AcquireLock() error = %v, wantErr %v", err, tt.wantErr)
					return nil
				}

				if !compareReward(got, tt.want) {
					t.Errorf("AcquireLock() got = %v, want %v", got, tt.want)
				}
				return nil
			})
		})
	}
}

// compare actual and expected vales for equality
// returns true if equal
func compareReward(actual, expected *model.Reward) bool {
	if actual == nil && expected == nil {
		return true
	}
	if actual == nil || expected == nil {
		return false
	}
	actual.CreatedAt = expected.CreatedAt
	actual.UpdatedAt = expected.UpdatedAt

	// compare reward options using custom equality function
	if !compareRewardOptions(actual.RewardOptions, expected.RewardOptions) {
		return false
	}
	// setting field to same value to ignore it later in equality check.
	actual.RewardOptions = expected.RewardOptions

	// compare chosen option using custom equality function
	if !compareRewardOption(actual.ChosenReward, expected.ChosenReward) {
		return false
	}
	// setting field to same value to ignore it later in equality check.
	actual.ChosenReward = expected.ChosenReward

	// compare reward display using custom equality function
	if !compareRewardDisplay(actual.RewardDisplay, expected.RewardDisplay) {
		return false
	}
	// setting field to same value to ignore it later in equality check.
	actual.RewardDisplay = expected.RewardDisplay
	// todo(divyadeep/utkarsh): add validation for RewardMeta as well
	actual.RewardMetadata = expected.RewardMetadata

	return reflect.DeepEqual(actual, expected)
}

// compares actual and expected values for equality
func compareRewardOptions(actual, expected *rewardsPb.RewardOptions) bool {
	if actual == nil && expected == nil {
		return true
	}
	if len(actual.GetOptions()) != len(expected.GetOptions()) {
		return false
	}
	// compare each reward option using custom equality function
	for idx := range actual.GetOptions() {
		if !compareRewardOption(actual.GetOptions()[idx], expected.GetOptions()[idx]) {
			return false
		}
	}
	// setting field to same value to ignore it in proto equality check.
	actual.Options = expected.GetOptions()

	return proto.Equal(actual, expected)
}

// compares actual and expected values for equality
func compareRewardOption(actual, expected *rewardsPb.RewardOption) bool {
	if actual == nil && expected == nil {
		return true
	}
	// ignoring some fields for equality check.
	if actual.GetFiCoins() != nil && expected.GetFiCoins() != nil {
		actual.GetFiCoins().ExpiresAt = expected.GetFiCoins().GetExpiresAt()
	}
	actual.Id = expected.Id

	return proto.Equal(actual, expected)
}

// compares actual and expected values for equality
func compareRewardDisplay(actual, expected *rewardsPb.RewardDisplay) bool {
	return proto.Equal(actual, expected)
}

func TestCrdbRewardsDao_GetTotalRewardsGivenToActorForRewardOffers(t *testing.T) {
	type args struct {
		ctx      context.Context
		actorId  string
		offerIds []string
	}
	tests := []struct {
		name    string
		args    args
		want    uint32
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "should return the total count of rewards given to actor for the reward offers",
			args: args{
				ctx:      context.Background(),
				actorId:  "act-5",
				offerIds: []string{"offer-1", "offer-2"},
			},
			want:    5,
			wantErr: assert.NoError,
		},
		{
			name: "should return 0 for an actor if no offer exists by the given id",
			args: args{
				ctx:      context.Background(),
				actorId:  "act-5",
				offerIds: []string{"no-offer-exists-1", "no-offer-exists-2"},
			},
			want:    0,
			wantErr: assert.NoError,
		},
		{
			name: "should return 0 for a non-existent actor",
			args: args{
				ctx:      context.Background(),
				actorId:  "non-existent-actor",
				offerIds: []string{"offer-1", "offer-2"},
			},
			want:    0,
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)

			got, err := rewardDao.GetTotalRewardsGivenToActorForRewardOffers(tt.args.ctx, tt.args.actorId, tt.args.offerIds)
			if !tt.wantErr(t, err, fmt.Sprintf("GetTotalRewardsGivenToActorForRewardOffers(%v, %v, %v)", tt.args.ctx, tt.args.actorId, tt.args.offerIds)) {
				return
			}
			assert.Equalf(t, tt.want, got, "GetTotalRewardsGivenToActorForRewardOffers(%v, %v, %v)", tt.args.ctx, tt.args.actorId, tt.args.offerIds)
		})
	}
}

func TestCrdbRewardsDao_GetTotalRewardsGivenToActorForRewardOfferGroup(t *testing.T) {
	type args struct {
		ctx          context.Context
		actorId      string
		offerGroupId string
	}
	tests := []struct {
		name    string
		args    args
		want    uint32
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "should return the total count of rewards given to actor for the reward-offer-group",
			args: args{
				ctx:          context.Background(),
				actorId:      "act-1-1",
				offerGroupId: "18b949a4-d0be-40cf-bc49-1f65916840fc",
			},
			want:    2,
			wantErr: assert.NoError,
		},
		{
			name: "should return 0 if no offers are present within a group",
			args: args{
				ctx:     context.Background(),
				actorId: "act-1-1",
				// no offers created with this group-id
				offerGroupId: "18b949a4-d0be-40cf-bc49-1f65916840fd",
			},
			want:    0,
			wantErr: assert.NoError,
		},
		{
			name: "should return 0 for non-existent actor",
			args: args{
				ctx:          context.Background(),
				actorId:      "non-existent-actor",
				offerGroupId: "18b949a4-d0be-40cf-bc49-1f65916840fc",
			},
			want:    0,
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)

			got, err := rewardDao.GetTotalRewardsGivenToActorForRewardOfferGroup(tt.args.ctx, tt.args.actorId, tt.args.offerGroupId)
			if !tt.wantErr(t, err, fmt.Sprintf("GetTotalRewardsGivenToActorForRewardOfferGroup(%v, %v, %v)", tt.args.ctx, tt.args.actorId, tt.args.offerGroupId)) {
				return
			}
			assert.Equalf(t, tt.want, got, "GetTotalRewardsGivenToActorForRewardOfferGroup(%v, %v, %v)", tt.args.ctx, tt.args.actorId, tt.args.offerGroupId)
		})
	}
}

func TestCrdbRewardsDao_checkIfReferralRewardsCapHasReached(t *testing.T) {
	type args struct {
		ctx                context.Context
		actorId            string
		rewardsCappingInfo *model.RewardsCappingInfo
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "should return true when rewards global cap has reached",
			args: args{
				ctx:     context.Background(),
				actorId: "act-1-2",
				rewardsCappingInfo: &model.RewardsCappingInfo{
					CapCount:             1,
					CappingEffectiveDate: time.Date(2122, 6, 7, 0, 0, 0, 0, time.UTC),
				},
			},
			want:    true,
			wantErr: assert.NoError,
		},
		{
			name: "should return false when cap has not reached",
			args: args{
				ctx:     context.Background(),
				actorId: "act-1-2",
				rewardsCappingInfo: &model.RewardsCappingInfo{
					CapCount:             3,
					CappingEffectiveDate: time.Date(2122, 6, 7, 0, 0, 0, 0, time.UTC),
				},
			},
			want:    false,
			wantErr: assert.NoError,
		},
		{
			name: "should return false when no capping info exists",
			args: args{
				ctx:                context.Background(),
				actorId:            "act-1-2",
				rewardsCappingInfo: nil,
			},
			want:    false,
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)

			s := NewRewardsDao(db, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)

			_ = s.DB.Transaction(func(tx *gormv2.DB) error {
				got, err := s.checkIfReferralRewardsCapHasReached(tt.args.ctx, tt.args.actorId, tt.args.rewardsCappingInfo, tx)
				if !tt.wantErr(t, err, fmt.Sprintf("checkIfReferralRewardsCapHasReached(%v, %v, %v, %v)", tt.args.ctx, tt.args.actorId, tt.args.rewardsCappingInfo, tx)) {
					return nil
				}
				assert.Equalf(t, tt.want, got, "checkIfReferralRewardsCapHasReached(%v, %v, %v, %v)", tt.args.ctx, tt.args.actorId, tt.args.rewardsCappingInfo, tx)

				return nil
			})
		})
	}
}

func TestCrdbRewardsDao_BulkChooseRewards(t *testing.T) {

	type args struct {
		ctx                       context.Context
		rewardIdToChosenOptionMap map[string]*rewardsPb.RewardOption
		rewardIdToRewardMap       map[string]*model.Reward
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "should return error when no reward exists with given id",
			args: args{
				ctx: context.Background(),
				rewardIdToChosenOptionMap: map[string]*rewardsPb.RewardOption{
					"random-reward-id-1": {
						Option: &rewardsPb.RewardOption_FiCoins{
							FiCoins: &rewardsPb.FiCoins{
								Units: 100,
								ExpiresAt: &timestamp.Timestamp{
									Seconds: 123123,
									Nanos:   0,
								},
							},
						},
					},
				},
				rewardIdToRewardMap: map[string]*model.Reward{
					"random-reward-id-1": {
						Id:   "random-reward-id-1",
						Tags: []rewardsPb.RewardTag{rewardsPb.RewardTag_CREDIT_CARD_SPENDS_2X_BOOSTER},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when even a single reward not in created state",
			args: args{
				ctx: context.Background(),
				rewardIdToChosenOptionMap: map[string]*rewardsPb.RewardOption{
					fixtureReward2.Id: {
						Option: &rewardsPb.RewardOption_FiCoins{
							FiCoins: &rewardsPb.FiCoins{
								Units: 100,
								ExpiresAt: &timestamp.Timestamp{
									Seconds: 123123,
									Nanos:   0,
								},
							},
						},
					},
					fixtureReward3.Id: {
						Option: &rewardsPb.RewardOption_FiCoins{
							FiCoins: &rewardsPb.FiCoins{
								Units: 100,
								ExpiresAt: &timestamp.Timestamp{
									Seconds: 123123,
									Nanos:   0,
								},
							},
						},
					},
				},
				rewardIdToRewardMap: map[string]*model.Reward{
					fixtureReward2.Id: {
						Id:   fixtureReward2.Id,
						Tags: []rewardsPb.RewardTag{rewardsPb.RewardTag_CREDIT_CARD_SPENDS_2X_BOOSTER},
					},
					fixtureReward3.Id: {
						Id:   fixtureReward3.Id,
						Tags: []rewardsPb.RewardTag{rewardsPb.RewardTag_CREDIT_CARD_SPENDS_2X_BOOSTER},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error if a reward is present in rewardIdToChosenOptionMap but not in rewardIdToRewardMap",
			args: args{
				ctx: context.Background(),
				rewardIdToChosenOptionMap: map[string]*rewardsPb.RewardOption{
					fixtureReward2.Id: {
						Option: &rewardsPb.RewardOption_FiCoins{
							FiCoins: &rewardsPb.FiCoins{
								Units: 100,
								ExpiresAt: &timestamp.Timestamp{
									Seconds: 123123,
									Nanos:   0,
								},
							},
						},
						Display: &rewardsPb.RewardOptionDisplay{
							Tags: []rewardsPb.RewardTag{rewardsPb.RewardTag_BOOSTER_3X},
						},
					},
					fixtureReward4.Id: {
						Option: &rewardsPb.RewardOption_FiCoins{
							FiCoins: &rewardsPb.FiCoins{
								Units: 100,
								ExpiresAt: &timestamp.Timestamp{
									Seconds: 123123,
									Nanos:   0,
								},
							},
						},
					},
				},
				rewardIdToRewardMap: map[string]*model.Reward{
					fixtureReward2.Id: {
						Id:   fixtureReward2.Id,
						Tags: []rewardsPb.RewardTag{rewardsPb.RewardTag_CREDIT_CARD_SPENDS_2X_BOOSTER},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should successfully choose reward options when all rewards exist and are in created state",
			args: args{
				ctx: context.Background(),
				rewardIdToChosenOptionMap: map[string]*rewardsPb.RewardOption{
					fixtureReward2.Id: {
						Option: &rewardsPb.RewardOption_FiCoins{
							FiCoins: &rewardsPb.FiCoins{
								Units: 100,
								ExpiresAt: &timestamp.Timestamp{
									Seconds: 123123,
									Nanos:   0,
								},
							},
						},
						Display: &rewardsPb.RewardOptionDisplay{
							Tags: []rewardsPb.RewardTag{rewardsPb.RewardTag_BOOSTER_3X},
						},
					},
					fixtureReward4.Id: {
						Option: &rewardsPb.RewardOption_FiCoins{
							FiCoins: &rewardsPb.FiCoins{
								Units: 100,
								ExpiresAt: &timestamp.Timestamp{
									Seconds: 123123,
									Nanos:   0,
								},
							},
						},
					},
				},
				rewardIdToRewardMap: map[string]*model.Reward{
					fixtureReward2.Id: {
						Id:   fixtureReward2.Id,
						Tags: []rewardsPb.RewardTag{rewardsPb.RewardTag_CREDIT_CARD_SPENDS_2X_BOOSTER},
					},
					fixtureReward4.Id: {
						Id:   fixtureReward4.Id,
						Tags: []rewardsPb.RewardTag{rewardsPb.RewardTag_CREDIT_CARD_SPENDS_2X_BOOSTER},
					},
				},
			},
			wantErr: false,
		},
	}
	// nolint: scopelint
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)

			err := rewardDao.BulkChooseRewards(tc.args.ctx, tc.args.rewardIdToChosenOptionMap, tc.args.rewardIdToRewardMap)
			if (err != nil) != tc.wantErr {
				t.Errorf("BulkChooseRewards() error = %v, wantErr %v", err, tc.wantErr)
			}
		})
	}
}

func TestCrdbRewardsDao_ClawbackReward(t *testing.T) {
	type args struct {
		ctx    context.Context
		reward *model.Reward
		reason rewardsPb.ClawbackReason
		refId  string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "should return error if id param is empty",
			args:    args{ctx: context.Background(), reason: rewardsPb.ClawbackReason_TXN_REVERSAL, refId: "refId"},
			wantErr: true,
		},
		{
			name:    "should return error if reason param is empty",
			args:    args{ctx: context.Background(), reward: &model.Reward{Id: "id"}, refId: "refId"},
			wantErr: true,
		},
		{
			name:    "should return error if refId param is empty",
			args:    args{ctx: context.Background(), reward: &model.Reward{Id: "id"}, reason: rewardsPb.ClawbackReason_TXN_REVERSAL},
			wantErr: true,
		},
		{
			name:    "should return ErrRowNotUpdated if reward is not found in created state for the given ID",
			args:    args{ctx: context.Background(), reward: &model.Reward{Id: "RW200911WjgI+xGYRY+0PhNl96WmYB=="}, reason: rewardsPb.ClawbackReason_TXN_REVERSAL, refId: "refId"},
			wantErr: true,
		},
		{
			name:    "should return ErrRowNotUpdated if reward is not found for the given ID",
			args:    args{ctx: context.Background(), reward: &model.Reward{Id: "no-reward-present"}, reason: rewardsPb.ClawbackReason_TXN_REVERSAL, refId: "refId"},
			wantErr: true,
		},
		{
			name: "should not return error if reward is found in created state for the given ID, and is updated successfully, and has no reward aggregates or max cap configs",
			args: args{ctx: context.Background(), reward: &model.Reward{
				Id: "RW200911WjgI+xGYRY+0PhNl96WmYA==",
				Aggregates: &rewardOffersPb.RewardAggregates{
					UserAggregate:               0,
					ActionAggregate:             0,
					RewardUnitsCapUserAggregate: nil,
				},
				OfferGroupId: "",
			}, reason: rewardsPb.ClawbackReason_TXN_REVERSAL, refId: "refId"},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.Name, test.AllTables)

			err := rewardDao.ClawbackReward(tt.args.ctx, tt.args.reward, tt.args.reason, tt.args.refId)
			if (err != nil) != tt.wantErr {
				t.Errorf("ClawbackReward(%v, %v, %v, %v)", tt.args.ctx, tt.args.reward.Id, tt.args.reason, tt.args.refId)
			}
		})
	}
}

func TestCrdbRewardsDao_GetMonthlyRewardUnitsUtilisedForActorAndOffer(t *testing.T) {
	type args struct {
		ctx     context.Context
		actorId string
		offerId string
		date    time.Time
	}
	tests := []struct {
		name    string
		args    args
		want    *rewardsPb.RewardOfferRewardUnitsActorUtilisationInTimePeriod
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "should return error when actorId is not passed",
			args: args{
				ctx:     context.Background(),
				offerId: uuid.NewString(),
				date:    time.Now(),
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return err != nil
			},
		},
		{
			name: "should return error when offerId is not passed",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-2",
				date:    time.Now(),
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return err != nil
			},
		},
		{
			name: "should return error when date is not passed",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-2",
				offerId: uuid.NewString(),
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return err != nil
			},
		},
		{
			name: "should return ErrRecordNotFound when no record is found for given params",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-2",
				offerId: uuid.NewString(),
				date:    time.Now(),
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, epifierrors.ErrRecordNotFound)
			},
		},
		{
			name: "should return correct result",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
				offerId: "75021f37-aa95-43c5-9292-74826b333f97",
				date:    time.Date(2023, 3, 15, 0, 0, 0, 0, datetime.IST),
			},
			want: &rewardsPb.RewardOfferRewardUnitsActorUtilisationInTimePeriod{
				Id:                "1b951656-600d-42d4-b584-06b6e596fb92",
				ActorId:           "actor-1",
				OfferId:           "75021f37-aa95-43c5-9292-74826b333f97",
				FiCoinUnits:       1100,
				CashUnits:         0,
				SdCashUnits:       0,
				CcBillEraserUnits: 100,
				FromTime:          timestampPb.New(time.Date(2023, 3, 1, 0, 0, 0, 0, datetime.IST)),
				TillTime:          timestampPb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, datetime.IST)),
				CreatedAt:         timestampPb.New(time.Date(2023, 3, 4, 0, 0, 0, 0, datetime.IST)),
				UpdatedAt:         timestampPb.New(time.Date(2023, 3, 4, 0, 0, 0, 0, datetime.IST)),
			},
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return true
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.Name, test.AllTables)

			got, err := rewardDao.GetMonthlyRewardUnitsUtilisedForActorAndOffer(tt.args.ctx, tt.args.actorId, tt.args.offerId, tt.args.date)
			if !tt.wantErr(t, err, fmt.Sprintf("GetMonthlyRewardUnitsUtilisedForActorAndOffer(%v, %v, %v, %v)", tt.args.ctx, tt.args.actorId, tt.args.offerId, tt.args.date)) {
				return
			}
			assert.Equalf(t, tt.want, got, "GetMonthlyRewardUnitsUtilisedForActorAndOffer(%v, %v, %v, %v)", tt.args.ctx, tt.args.actorId, tt.args.offerId, tt.args.date)
		})
	}
}

func TestCrdbRewardsDao_GetMonthlyRewardUnitsUtilisedForActorAndGroup(t *testing.T) {
	type args struct {
		ctx     context.Context
		actorId string
		groupId string
		date    time.Time
	}
	tests := []struct {
		name    string
		args    args
		want    *rewardsPb.RewardOfferGroupRewardUnitsActorUtilisationInTimePeriod
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "should return error when actorId is not passed",
			args: args{
				ctx:     context.Background(),
				groupId: uuid.NewString(),
				date:    time.Now(),
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return err != nil
			},
		},
		{
			name: "should return error when groupId is not passed",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-2",
				date:    time.Now(),
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return err != nil
			},
		},
		{
			name: "should return error when date is not passed",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-2",
				groupId: uuid.NewString(),
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return err != nil
			},
		},
		{
			name: "should return ErrRecordNotFound when no record is found for given params",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-2",
				groupId: uuid.NewString(),
				date:    time.Now(),
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, epifierrors.ErrRecordNotFound)
			},
		},
		{
			name: "should return correct result",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
				groupId: "f68b8299-d038-485e-a59c-606f53bd7f9e",
				date:    time.Date(2023, 3, 15, 0, 0, 0, 0, datetime.IST),
			},
			want: &rewardsPb.RewardOfferGroupRewardUnitsActorUtilisationInTimePeriod{
				Id:                    "d9b7e66d-f782-4556-9347-8729104fcf58",
				ActorId:               "actor-1",
				GroupId:               "f68b8299-d038-485e-a59c-606f53bd7f9e",
				FiCoinUnits:           1100,
				CashUnits:             0,
				SdCashUnits:           0,
				CcBillEraserUnits:     100,
				GeneratedRewardsCount: 1,
				FromTime:              timestampPb.New(time.Date(2023, 3, 1, 0, 0, 0, 0, datetime.IST)),
				TillTime:              timestampPb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, datetime.IST)),
				CreatedAt:             timestampPb.New(time.Date(2023, 3, 4, 0, 0, 0, 0, datetime.IST)),
				UpdatedAt:             timestampPb.New(time.Date(2023, 3, 4, 0, 0, 0, 0, datetime.IST)),
			},
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return true
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.Name, test.AllTables)

			got, err := rewardDao.GetMonthlyRewardUnitsUtilisedForActorAndGroup(tt.args.ctx, tt.args.actorId, tt.args.groupId, tt.args.date)
			if !tt.wantErr(t, err, fmt.Sprintf("GetMonthlyRewardUnitsUtilisedForActorAndGroup(%v, %v, %v, %v)", tt.args.ctx, tt.args.actorId, tt.args.groupId, tt.args.date)) {
				return
			}
			assert.Equalf(t, tt.want, got, "GetMonthlyRewardUnitsUtilisedForActorAndGroup(%v, %v, %v, %v)", tt.args.ctx, tt.args.actorId, tt.args.groupId, tt.args.date)
		})
	}
}

func TestCrdbRewardsDao_GetRewardsCount(t *testing.T) {
	type args struct {
		ctx     context.Context
		actorId string
		filters *daoModel.RewardsCountFilters
	}
	tests := []struct {
		name    string
		args    args
		want    uint32
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "should return error when actorId is not passed",
			args: args{
				ctx:     context.Background(),
				actorId: "",
				filters: &daoModel.RewardsCountFilters{
					AndFilter: &daoModel.RewardsCountFilterFields{
						RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER},
					},
				},
			},
			wantErr: assert.Error,
		},
		{
			name: "should return error when all filters are empty",
			args: args{
				ctx:     context.Background(),
				actorId: "act-5",
				filters: &daoModel.RewardsCountFilters{},
			},
			wantErr: assert.Error,
		},
		{
			name: "should return error when filters don't match with indexes - 1",
			args: args{
				ctx:     context.Background(),
				actorId: "act-5",
				filters: &daoModel.RewardsCountFilters{
					AndFilter: &daoModel.RewardsCountFilterFields{
						FromTime: time.Date(2020, 9, 13, 0, 0, 0, 0, time.Local),
					},
				},
			},
			wantErr: assert.Error,
		},
		{
			name: "should return error when filters don't match with indexes - 2",
			args: args{
				ctx:     context.Background(),
				actorId: "act-5",
				filters: &daoModel.RewardsCountFilters{
					AndFilter: &daoModel.RewardsCountFilterFields{
						RewardTypes: []rewardsPb.RewardType{rewardsPb.RewardType_FI_COINS},
					},
				},
			},
			wantErr: assert.Error,
		},
		{
			name: "should return error when filters don't match with indexes - 3",
			args: args{
				ctx:     context.Background(),
				actorId: "act-5",
				filters: &daoModel.RewardsCountFilters{
					OrFilter: &daoModel.RewardsCountFilterFields{
						RewardTypes: []rewardsPb.RewardType{rewardsPb.RewardType_FI_COINS},
					},
				},
			},
			wantErr: assert.Error,
		},
		{
			name: "should return error when filters don't match with indexes - 5",
			args: args{
				ctx:     context.Background(),
				actorId: "act-5",
				filters: &daoModel.RewardsCountFilters{
					OrFilter: &daoModel.RewardsCountFilterFields{
						FromTime: time.Date(2020, 9, 13, 0, 0, 0, 0, time.Local),
					},
				},
			},
			wantErr: assert.Error,
		},
		{
			name: "should return reward count filtered by actorId - offerTypes - rewardType - fromTime combination when passed (and filter)",
			args: args{
				ctx:     context.Background(),
				actorId: "act-1-2",
				filters: &daoModel.RewardsCountFilters{
					AndFilter: &daoModel.RewardsCountFilterFields{
						RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
						RewardTypes:      []rewardsPb.RewardType{rewardsPb.RewardType_CASH},
						FromTime:         time.Date(2122, 6, 7, 0, 0, 0, 0, time.Local),
					},
				},
			},
			want:    uint32(1),
			wantErr: assert.NoError,
		},
		{
			name: "should return reward count filtered by actorId - offerTypes - rewardType - tillTime combination when passed (and filter)",
			args: args{
				ctx:     context.Background(),
				actorId: "act-1-2",
				filters: &daoModel.RewardsCountFilters{
					AndFilter: &daoModel.RewardsCountFilterFields{
						RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
						RewardTypes:      []rewardsPb.RewardType{rewardsPb.RewardType_CASH},
						TillTime:         time.Date(2122, 7, 1, 0, 0, 0, 0, time.Local),
					},
				},
			},
			want:    uint32(1),
			wantErr: assert.NoError,
		},
		{
			name: "should return reward count filtered by actorId - offerTypes combination when passed (and filter)",
			args: args{
				ctx:     context.Background(),
				actorId: "act-1-2",
				filters: &daoModel.RewardsCountFilters{
					AndFilter: &daoModel.RewardsCountFilterFields{
						RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
					},
				},
			},
			want:    uint32(1),
			wantErr: assert.NoError,
		},
		{
			name: "should return reward count filtered by actorId - offerTypes -rewardType combination passed (and filter)",
			args: args{
				ctx:     context.Background(),
				actorId: "act-1-2",
				filters: &daoModel.RewardsCountFilters{
					AndFilter: &daoModel.RewardsCountFilterFields{
						RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
						RewardTypes:      []rewardsPb.RewardType{rewardsPb.RewardType_CASH},
					},
				},
			},
			want:    uint32(1),
			wantErr: assert.NoError,
		},
		{
			name: "should return reward count filtered by combination of `and` and `or` when both are passed",
			args: args{
				ctx:     context.Background(),
				actorId: "act-1-2",
				filters: &daoModel.RewardsCountFilters{
					AndFilter: &daoModel.RewardsCountFilterFields{
						FromTime: time.Date(2122, 6, 7, 0, 0, 0, 0, time.Local),
					},
					OrFilter: &daoModel.RewardsCountFilterFields{
						RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
						RewardTypes:      []rewardsPb.RewardType{rewardsPb.RewardType_FI_COINS},
					},
				},
			},
			want:    uint32(2),
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.Name, test.AllTables)
			got, err := rewardDao.GetRewardsCount(tt.args.ctx, tt.args.actorId, tt.args.filters)
			if !tt.wantErr(t, err, fmt.Sprintf("GetRewardsCount(%v, %v, %v)", tt.args.ctx, tt.args.actorId, tt.args.filters)) {
				return
			}
			assert.Equalf(t, tt.want, got, "GetRewardsCount(%v, %v, %v)", tt.args.ctx, tt.args.actorId, tt.args.filters)
		})
	}
}

func TestCrdbRewardsDao_FetchPaginatedRewardsByOrFilter(t *testing.T) {
	type args struct {
		ctx       context.Context
		actorId   string
		filter    *model.QueryRewardsFilterV2
		pageToken *pagination.PageToken
		pageSize  int
	}
	tests := []struct {
		name        string
		args        args
		want        []*model.Reward
		wantPageCtx *rpc.PageContextResponse
		wantErr     assert.ErrorAssertionFunc
	}{
		{
			name: "should return error if filters are empty",
			args: args{
				ctx:      context.Background(),
				pageSize: 2,
			},
			wantErr: assert.Error,
		},
		{
			name: "should return error if page size is 0",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
				filter: &model.QueryRewardsFilterV2{
					AndFilter: &model.AndRewardsFilter{
						RewardType: rewardsPb.RewardType_FI_COINS,
					},
				},
			},
			wantErr: assert.Error,
		},
		{
			name: "should return error if any of the required filters are empty",
			args: args{
				ctx:      context.Background(),
				pageSize: 2,
				filter: &model.QueryRewardsFilterV2{
					AndFilter: &model.AndRewardsFilter{
						RewardType: rewardsPb.RewardType_FI_COINS,
					},
				},
			},
			wantErr: assert.Error,
		},
		{
			name: "should return rewards with pagination context response filtered by AND filters when they are passed correctly",
			args: args{
				ctx:      context.Background(),
				pageSize: 2,
				actorId:  "AC220704UJONP+gHTx2AURVKqgX4Jw==",
				filter: &model.QueryRewardsFilterV2{
					AndFilter: &model.AndRewardsFilter{
						RewardType: rewardsPb.RewardType_FI_COINS,
						Statuses:   []rewardsPb.RewardStatus{rewardsPb.RewardStatus_CREATED},
					},
				},
			},
			want: []*model.Reward{fixtureCreditCard1xReward2, fixtureCreditCard1xReward1},
			wantPageCtx: &rpc.PageContextResponse{
				HasAfter: false,
			},
			wantErr: assert.NoError,
		},
		{
			name: "should return rewards with pagination context response filtered by OR filters when they are passed correctly",
			args: args{
				ctx:      context.Background(),
				pageSize: 2,
				actorId:  "AC220704UJONP+gHTx2AURVKqgX4Jw==",
				filter: &model.QueryRewardsFilterV2{
					OrFilter: &model.OrRewardsFilter{
						ClaimType: rewardsPb.ClaimType_CLAIM_TYPE_MANUAL,
						Statuses:  []rewardsPb.RewardStatus{rewardsPb.RewardStatus_CREATED},
					},
				},
			},
			want: []*model.Reward{fixtureCreditCard1xReward2, fixtureCreditCard1xReward1},
			wantPageCtx: &rpc.PageContextResponse{
				HasAfter:   true,
				AfterToken: "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTY3NTI4MDkzNCwibmFub3MiOjg1MDU3OTAwMH0sIk9mZnNldCI6MSwiSXNSZXZlcnNlIjpmYWxzZX0=",
			},
			wantErr: assert.NoError,
		},
		{
			name: "should return rewards with pagination context response filtered by both AND and OR filters when they are passed correctly",
			args: args{
				ctx:      context.Background(),
				pageSize: 2,
				actorId:  "AC220704UJONP+gHTx2AURVKqgX4Jw==",
				filter: &model.QueryRewardsFilterV2{
					AndFilter: &model.AndRewardsFilter{
						RewardType: rewardsPb.RewardType_FI_COINS,
						RefIds:     []string{"cc-txn-1", "cc-txn-2"},
					},
					OrFilter: &model.OrRewardsFilter{
						ClaimType: rewardsPb.ClaimType_CLAIM_TYPE_MANUAL,
						Statuses:  []rewardsPb.RewardStatus{rewardsPb.RewardStatus_CREATED},
					},
				},
			},
			want: []*model.Reward{fixtureCreditCard1xReward2, fixtureCreditCard1xReward1},
			wantPageCtx: &rpc.PageContextResponse{
				HasAfter: false,
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.Name, test.AllTables)
			gotRewards, gotPageCtxRes, err := rewardDao.FetchPaginatedRewardsByFiltersV2(tt.args.ctx, tt.args.actorId, tt.args.filter, tt.args.pageToken, tt.args.pageSize)
			if !tt.wantErr(t, err, fmt.Sprintf("FetchPaginatedRewardsByFiltersV2(%v, %v, %v)", tt.args.ctx, tt.args.filter, tt.args.pageToken)) {
				return
			}
			assert.Equalf(t, len(tt.want), len(gotRewards), "FetchPaginatedRewardsByFiltersV2(%v, %v, %v)", tt.args.ctx, tt.args.filter, tt.args.pageToken)
			for i := range tt.want {
				if !areRewardsSame(tt.want[i], gotRewards[i]) {
					t.Errorf("FetchPaginatedRewardsByFiltersV2(%v, %v, %v), gotRewardId = %s, wantRewardId = %s", tt.args.ctx, tt.args.filter, tt.args.pageToken, gotRewards[i].Id, tt.want[i].Id)
				}
			}
			assert.Equalf(t, tt.wantPageCtx, gotPageCtxRes, "FetchPaginatedRewardsByFiltersV2(%v, %v, %v)", tt.args.ctx, tt.args.filter, tt.args.pageToken)
		})
	}
}

func areRewardsSame(reward1, reward2 *model.Reward) bool {
	// only matching rewardIds for now
	return reward1.Id == reward2.Id
}

func TestCrdbRewardsDao_ExpireRewardsByIds(t *testing.T) {
	type args struct {
		ctx       context.Context
		rewardIds []string
	}
	tests := []struct {
		name    string
		args    args
		want    int64
		wantErr bool
	}{
		{
			name: "should return error when reward ids are empty",
			args: args{
				ctx:       context.Background(),
				rewardIds: nil,
			},
			want:    0,
			wantErr: true,
		},
		{
			name: "should return 0 updated count when reward ids are not present in db",
			args: args{
				ctx:       context.Background(),
				rewardIds: []string{"123456", "789091"},
			},
			want:    0,
			wantErr: false,
		},
		{
			name: "should return successful count rows updated count when rewards are updated",
			args: args{
				ctx:       context.Background(),
				rewardIds: []string{fixtureReward1.Id, fixtureReward2.Id},
			},
			want:    2,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.Name, test.AllTables)
			got, err := rewardDao.ExpireRewardsByIds(tt.args.ctx, tt.args.rewardIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExpireRewardsByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				if diff := cmp.Diff(tt.want, got); diff != "" {
					t.Errorf("ExpireRewardsByIds() got = %v, want %v", got, tt.want)
				}
			}
		})
	}
}
