//go:generate mockgen -source=dao.go -destination=../../test/mocks/generator/dao/dao.go -package=mock_dao
//go:generate dao_metrics_gen .
package dao

import (
	"context"
	"time"

	"github.com/google/wire"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/pagination"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"

	daoModel "github.com/epifi/gamma/rewards/generator/dao/model"
	"github.com/epifi/gamma/rewards/generator/model"
)

var RewardsDaoWireSet = wire.NewSet(NewRewardsDao, wire.Bind(new(RewardsDao), new(*CrdbRewardsDao)))
var RewardOffersDaoWireSet = wire.NewSet(NewCrdbRewardOfferDao, wire.Bind(new(RewardOfferDao), new(*CrdbRewardOfferDao)))
var RewardOfferGroupDaoWireSet = wire.NewSet(NewPgdbRewardOfferGroupDao, wire.Bind(new(RewardOfferGroupDao), new(*PgdbRewardOfferGroupDao)))
var CreditCardRewardDaoWireSet = wire.NewSet(NewPGDBCreditCardRewardDao, wire.Bind(new(CreditCardRewardDao), new(*PGDBCreditCardRewardDao)))
var CreditCardRewardInfoDaoWireSet = wire.NewSet(NewPGDBCreditCardRewardInfoDao, wire.Bind(new(CreditCardRewardInfoDao), new(*PGDBCreditCardRewardInfoDao)))

type RewardsDao interface {
	PersistReward(ctx context.Context, reward *model.Reward) error
	FetchRewards(ctx context.Context, filter *model.QueryRewardsFilter) ([]*model.Reward, error)
	// Deprecated: use FetchPaginatedRewardsByFiltersV2 instead
	FetchRewardsByPageAndFilter(ctx context.Context, filter *model.QueryRewardsFilter, pageToken *pagination.PageToken) ([]*model.Reward, *rpc.PageContextResponse, error)
	UpdateRewardStatus(ctx context.Context, id string, status rewardsPb.RewardStatus, subStatus rewardsPb.SubStatus) (int64, error)
	ClawbackReward(ctx context.Context, reward *model.Reward, clawbackReason rewardsPb.ClawbackReason, clawbackRefId string) error
	FetchRewardById(ctx context.Context, id string) (*model.Reward, error)
	FetchByActorAndOfferId(ctx context.Context, actorId string, offerId string) ([]*model.Reward, error)
	ChooseReward(ctx context.Context, reward *model.Reward, chosenOption *rewardsPb.RewardOption, currentStatus rewardsPb.RewardStatus, currentSubStatus rewardsPb.SubStatus) error
	BulkChooseRewards(ctx context.Context, rewardIdToChosenOptionMap map[string]*rewardsPb.RewardOption, rewardIdToRewardMap map[string]*model.Reward) error
	UpdateProcessingRef(ctx context.Context, id string, processingRef string) error
	AcquireLock(ctx context.Context, id string) (*model.Reward, error)
	GetTotalRewardsGivenToActorForRewardOffers(ctx context.Context, actorId string, offerIds []string) (uint32, error)
	GetTotalRewardsGivenToActorForRewardOfferGroup(ctx context.Context, actorId, offerGroupId string) (uint32, error)
	// methods to fetch monthly aggregates
	// todo: rename it to GetRewardUnitsUtilisedForActorAndOfferInMonth
	GetMonthlyRewardUnitsUtilisedForActorAndOffer(ctx context.Context, actorId, offerId string, date time.Time) (*rewardsPb.RewardOfferRewardUnitsActorUtilisationInTimePeriod, error)
	GetMonthlyRewardUnitsUtilisedForActorAndGroup(ctx context.Context, actorId, groupId string, date time.Time) (*rewardsPb.RewardOfferGroupRewardUnitsActorUtilisationInTimePeriod, error)
	GetRewardsCount(ctx context.Context, actorId string, filters *daoModel.RewardsCountFilters) (uint32, error)
	FetchPaginatedRewardsByFiltersV2(ctx context.Context, actorId string, filter *model.QueryRewardsFilterV2, pageToken *pagination.PageToken, pageSize int) ([]*model.Reward, *rpc.PageContextResponse, error)
	ExpireRewardsByIds(ctx context.Context, rewardIds []string) (int64, error)
	ResetRewardProcessingStateById(ctx context.Context, rewardId string) error
	UpdateRewardMetadata(ctx context.Context, rewardId string, rewardMetadata *rewardsPb.RewardMetadata) error
}

type RewardOfferDao interface {
	CreateRewardOffer(ctx context.Context, req *rewardOffersPb.CreateRewardOfferRequest) (*rewardOffersPb.RewardOffer, error)
	UpdateRewardOfferStatus(ctx context.Context, req *rewardOffersPb.UpdateRewardOfferStatusRequest) (*rewardOffersPb.RewardOffer, error)
	UpdateRewardOfferDetails(ctx context.Context, req *rewardOffersPb.UpdateRewardOfferRequest) (*rewardOffersPb.RewardOffer, error)

	// Deprecated: FetchRewardOfferById is deprecated. Use FetchRewardOffersByIds instead.
	FetchRewardOfferById(ctx context.Context, id string) (*rewardOffersPb.RewardOffer, error)
	FetchRewardOffersByIds(ctx context.Context, ids []string) ([]*rewardOffersPb.RewardOffer, error)
	FetchRewardOffers(ctx context.Context, req *rewardOffersPb.GetRewardOffersRequest) ([]*rewardOffersPb.RewardOffer, error)
	FetchRewardOffersActiveAtTime(ctx context.Context, actionType string, activeAtTime time.Time, generationType rewardOffersPb.GenerationType) ([]*rewardOffersPb.RewardOffer, error)
	GetActionLevelRewardOfferInventory(ctx context.Context, offerIds []string) ([]*rewardOffersPb.RewardOfferInventory, error)
	GetActorLevelRewardOfferInventory(ctx context.Context, actorId string, offerIds []string) ([]*rewardOffersPb.RewardOfferInventory, error)
	GetRewardUnitsUtilisedForActor(ctx context.Context, actorId, offerId string) (*daoModel.RewardOfferRewardUnitsActorUtilisation, error)
	GetOffersRewardUnitsUtilized(ctx context.Context, actorId string, offerIds []string) ([]*rewardOffersPb.RewardOfferRewardUnitsActorUtilisation, error)
	GetOffersRewardUnitsUtilizedInTimePeriod(ctx context.Context, actorId string, offerIds []string, fromTime, tillTime time.Time) ([]*rewardsPb.RewardOfferRewardUnitsActorUtilisationInTimePeriod, error)
	// GetByLikeQuery returns all reward offers that match the query string on the constraint expression, reward meta, or display meta.
	GetByLikeQuery(ctx context.Context, queryString string, limit int) ([]*rewardOffersPb.RewardOffer, error)
}

type RewardOfferGroupDao interface {
	CreateOfferGroup(ctx context.Context, req *rewardOffersPb.CreateRewardOfferGroupRequest) (*rewardOffersPb.RewardOfferGroup, error)
	FetchOfferGroupById(ctx context.Context, id string) (*rewardOffersPb.RewardOfferGroup, error)
	GetOfferGroupsByIds(ctx context.Context, ids []string) ([]*rewardOffersPb.RewardOfferGroup, error)
	GetActorLevelOfferGroupInventory(ctx context.Context, actorId string, offerGroupIds []string) ([]*rewardOffersPb.RewardOfferGroupInventory, error)
	GetRewardUnitsUtilisedForActor(ctx context.Context, actorId, offerGroupId string) (*daoModel.RewardOfferGroupRewardUnitsActorUtilisation, error)
	GetOfferGroupsRewardUnitsUtilized(ctx context.Context, actorId string, offerGroupIds []string) ([]*rewardOffersPb.RewardOfferGroupRewardUnitsActorUtilisation, error)
	GetOfferGroupsRewardUnitsUtilizedInTimePeriod(ctx context.Context, actorId string, offerGroupIds []string, fromTime, tillTime time.Time) ([]*rewardsPb.RewardOfferGroupRewardUnitsActorUtilisationInTimePeriod, error)
}

type CreditCardRewardDao interface {
	// Note : the aggregates returned by this method does not takes into account the clawed-back reward amount.
	Get1xRewardAggregatesAtRewardStatusLevel(ctx context.Context, filters *daoModel.CreditCardRewardQueryFilters) ([]*daoModel.RewardStatusLevelAggregateUnit, error)
	Get1xRewardClawbackAggregatesAtClawbackStatusLevel(ctx context.Context, filters *daoModel.CreditCardRewardClawbackQueryFilters) ([]*daoModel.ClawbackStatusLevelAggregateUnit, error)
	// Note : the aggregates returned by this method does not takes into account the clawed-back reward amount.
	Get1xRewardAggregatesAtMerchantIdLevel(ctx context.Context, filters *daoModel.CreditCardRewardQueryFilters) ([]*daoModel.MerchantIdLevelAggregateUnit, error)
	Get1xRewardClawbackAggregatesAtMerchantIdLevel(ctx context.Context, filters *daoModel.CreditCardRewardClawbackQueryFilters) ([]*daoModel.MerchantIdLevelAggregateUnit, error)
}

type CreditCardRewardInfoDao interface {
	GetByRewardId(ctx context.Context, rewardId string) (*rewardsPb.CreditCardRewardInfo, error)
	GetByRewardRefIdAndOfferType(ctx context.Context, rewardRefId string, offerType rewardsPb.RewardOfferType) ([]*rewardsPb.CreditCardRewardInfo, error)
	GetCcAccountLevelMonthlyRewardUnitsUtilised(ctx context.Context, ccAccountId string, offerType rewardsPb.RewardOfferType, monthTimestamp time.Time) (*daoModel.CcRewardsCcAccountLevelMonthlyUtilisation, error)
}
