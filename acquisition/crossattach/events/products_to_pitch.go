package events

import (
	"fmt"
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

type ProductsToPitchEvent struct {
	EventId                       string
	ProspectId                    string
	ActorID                       string
	SessionId                     string
	EventType                     string
	DeviceId                      string
	UserAttributesMap             map[string]interface{} `structs:",flatten"`
	ProductToPitch                string
	UpsellAfterInDays             string
	ProductToEligibilityStatusMap map[string]interface{} `structs:",flatten"`
	ProductsToPitch               []string
	Cohort                        string
	Timestamp                     time.Time
}

func NewProductsToPitchEvent(actorId string, userAttributesMap map[string]interface{}, productToEligibilityStatusMap map[string]interface{}, productsToPitch []string, upsellAfter time.Duration, cohort string) *ProductsToPitchEvent {
	upsellAfterInDays := int(upsellAfter.Hours()/24) + 1
	return &ProductsToPitchEvent{
		ActorID:                       actorId,
		UserAttributesMap:             userAttributesMap,
		ProductToEligibilityStatusMap: productToEligibilityStatusMap,
		EventId:                       uuid.New().String(),
		Timestamp:                     time.Now(),
		EventType:                     events.EventTrack,
		ProductsToPitch:               productsToPitch,
		ProductToPitch:                productsToPitch[0],
		UpsellAfterInDays:             fmt.Sprintf("%d", upsellAfterInDays),
		Cohort:                        cohort,
	}
}

func (s *ProductsToPitchEvent) GetEventType() string {
	return events.EventTrack
}

func (s *ProductsToPitchEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *ProductsToPitchEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *ProductsToPitchEvent) GetEventId() string {
	return s.EventId
}

func (s *ProductsToPitchEvent) GetUserId() string {
	return s.ActorID
}

func (s *ProductsToPitchEvent) GetProspectId() string {
	return s.ProspectId
}

func (s *ProductsToPitchEvent) GetEventName() string {
	return EventProductsToPitch
}
