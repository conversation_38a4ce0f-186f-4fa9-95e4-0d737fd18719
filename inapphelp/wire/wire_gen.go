// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/ratelimiter"
	"github.com/epifi/be-common/pkg/ratelimiter/store"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/actor_activity"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/cx/chat"
	"github.com/epifi/gamma/api/cx/inapphelp_feedback_engine_clients/question_response_subscription"
	"github.com/epifi/gamma/api/cx/issue_config"
	"github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/api/cx/watson"
	"github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/fittt"
	mock_client2 "github.com/epifi/gamma/api/inapphelp/feedback_engine/mock_client"
	"github.com/epifi/gamma/api/inapphelp/issue_reporting"
	"github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/risk/profile"
	"github.com/epifi/gamma/api/search"
	"github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/cx/inhouse"
	"github.com/epifi/gamma/api/vendorgateway/cx/solutions"
	"github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/cx/helper"
	dao8 "github.com/epifi/gamma/cx/issue_category/dao"
	"github.com/epifi/gamma/cx/issue_category/manager"
	types3 "github.com/epifi/gamma/cx/wire/types"
	"github.com/epifi/gamma/inapphelp/app_feedback"
	dao6 "github.com/epifi/gamma/inapphelp/app_feedback/dao"
	"github.com/epifi/gamma/inapphelp/app_feedback/in_app_feedback_helper"
	"github.com/epifi/gamma/inapphelp/app_feedback/rule_engine"
	processor3 "github.com/epifi/gamma/inapphelp/app_feedback/rule_engine/processor"
	"github.com/epifi/gamma/inapphelp/config"
	"github.com/epifi/gamma/inapphelp/config/genconf"
	"github.com/epifi/gamma/inapphelp/developer"
	processor2 "github.com/epifi/gamma/inapphelp/developer/processor"
	dao4 "github.com/epifi/gamma/inapphelp/entity_mapping/dao"
	"github.com/epifi/gamma/inapphelp/faq/processor"
	"github.com/epifi/gamma/inapphelp/faq/processor/dao"
	"github.com/epifi/gamma/inapphelp/faq/serving"
	dao2 "github.com/epifi/gamma/inapphelp/faq/serving/dao"
	"github.com/epifi/gamma/inapphelp/feedback_engine"
	"github.com/epifi/gamma/inapphelp/feedback_engine/custom_eligibility_evaluation"
	dao7 "github.com/epifi/gamma/inapphelp/feedback_engine/dao"
	"github.com/epifi/gamma/inapphelp/feedback_engine/mock_client"
	question_response_subscription2 "github.com/epifi/gamma/inapphelp/feedback_engine/question_response_subscription"
	"github.com/epifi/gamma/inapphelp/feedback_engine/serving/jarvis"
	issue_reporting2 "github.com/epifi/gamma/inapphelp/issue_reporting"
	dao9 "github.com/epifi/gamma/inapphelp/issue_reporting/dao"
	"github.com/epifi/gamma/inapphelp/issue_reporting/issue_collector"
	"github.com/epifi/gamma/inapphelp/issue_reporting/prompt_generator"
	"github.com/epifi/gamma/inapphelp/issue_reporting/repository"
	"github.com/epifi/gamma/inapphelp/media"
	dao5 "github.com/epifi/gamma/inapphelp/media/dao"
	"github.com/epifi/gamma/inapphelp/recent_activity"
	dao3 "github.com/epifi/gamma/inapphelp/recent_activity/dao"
	types2 "github.com/epifi/gamma/inapphelp/wire/types"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Injectors from wire.go:

// config: {"s3Client": "RelatedFaqConfig().BucketName"}
func InitializeStoreFAQService(db types.InapphelpPGDB, scClient solutions.SolutionsClient, faqPub types2.FaqDocumentEventExternalQueuePublisher, s3Client types2.RelatedFaqConfigS3Client, conf *config.Config) *processor.RefreshFAQService {
	persistFAQData := dao.NewPersistFAQData(db)
	fetchFAQData := dao2.NewFetchFAQData(db)
	publisher := types2.FaqDocumentEventExternalQueuePublisherProvider(faqPub)
	int2 := getRelatedFaqCount(conf)
	relatedFaqDao := dao3.NewRelatedFaqDao(db, int2)
	s3S3Client := types2.RelatedFaqConfigS3ClientProvider(s3Client)
	string2 := getRelatedFaqS3Path(conf)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	refreshFAQService := processor.NewRefreshFAQService(persistFAQData, scClient, fetchFAQData, publisher, relatedFaqDao, s3S3Client, string2, gormTxnExecutor)
	return refreshFAQService
}

func InitializeFAQServingService(db types.InapphelpPGDB, broker events.Broker, depositClient deposit.DepositClient, cxInHouseVGClient inhouse.CXInHouseClient, actorClient actor.ActorClient, userClient user.UsersClient, userGroupClient group.GroupClient, conf *config.Config, genConf *genconf.Config, onboardingClient onboarding.OnboardingClient) *serving.FAQServingService {
	fetchFAQData := dao2.NewFetchFAQData(db)
	faqArticleFeedbackDao := dao2.NewFaqArticleFeedbackDao(db)
	faqArticleFeedbackConf := getFaqArticleFeedbackConf(conf)
	flags := getFlagsConfig(genConf)
	fetchFaqDaoV2 := dao2.NewFetchFaqDaoV2(db)
	gormDB := types.InapphelpPGDBGormDBProvider(db)
	txnExecutor := getTxnExecutor(gormDB)
	updateFAQData := dao2.NewUpdateFAQData(db)
	inAppHelpEntityMappingDao := dao4.NewInAppHelpEntityMappingDao(db)
	inAppHelpAppClientConfigMappingDao := dao4.NewInAppHelpAppClientConfigMappingDao(db)
	updateFaqWrapper := dao2.NewUpdateFaqWrapper(updateFAQData, inAppHelpEntityMappingDao, inAppHelpAppClientConfigMappingDao, txnExecutor)
	popularFAQConfig := getPopularFAQConfig(genConf)
	faqServingService := serving.NewFAQServingService(fetchFAQData, broker, faqArticleFeedbackDao, faqArticleFeedbackConf, depositClient, flags, cxInHouseVGClient, actorClient, userClient, userGroupClient, fetchFaqDaoV2, txnExecutor, updateFaqWrapper, popularFAQConfig, onboardingClient, inAppHelpEntityMappingDao)
	return faqServingService
}

func InitializeRecentActivityService(db types.InapphelpPGDB, conf *config.Config) *recent_activity.Service {
	activityTypeToFaqMapDao := dao3.NewActivityTypeToFaqMapDao(db)
	int2 := getRelatedFaqCount(conf)
	relatedFaqDao := dao3.NewRelatedFaqDao(db, int2)
	fetchFAQData := dao2.NewFetchFAQData(db)
	service := recent_activity.NewService(activityTypeToFaqMapDao, relatedFaqDao, fetchFAQData)
	return service
}

func InitializeDevInapphelpService(db types.InapphelpPGDB, conf *config.Config) *developer.InAppHelpDev {
	fetchFAQData := dao2.NewFetchFAQData(db)
	devArticles := processor2.NewDevArticles(fetchFAQData)
	devCategories := processor2.NewDevCategories(fetchFAQData)
	devFolders := processor2.NewDevFolders(fetchFAQData)
	devFaqVersion := processor2.NewDevFaqVersion(fetchFAQData)
	int2 := getRelatedFaqCount(conf)
	relatedFaqDao := dao3.NewRelatedFaqDao(db, int2)
	devRelatedFaq := processor2.NewDevRelatedFaq(relatedFaqDao)
	mediaContentDao := dao5.NewMediaContentDao(db)
	devMediaContent := processor2.NewDevMediaContent(mediaContentDao)
	mediaPlaylistDao := dao5.NewMediaPlaylistDao(db)
	devMediaPlaylist := processor2.NewDevMediaPlaylist(mediaPlaylistDao)
	uiContextToMediaPlaylistMappingDao := dao5.NewUIContextToMediaPlaylistMappingDao(db)
	devUIContextToMediaPlaylistMapping := processor2.NewDevUIContextToMediaPlaylistMapping(uiContextToMediaPlaylistMappingDao)
	mediaPlaylistToMediaContentMappingDao := dao5.NewMediaPlaylistToMediaContentMappingDao(db)
	devMediaPlaylistToMediaContentMapping := processor2.NewDevMediaPlaylistToMediaContentMapping(mediaPlaylistToMediaContentMappingDao)
	feedbackAttemptDao := dao6.NewFeedbackAttemptDao(db)
	devFeedbackAttempt := processor2.NewDevFeedbackAttempt(feedbackAttemptDao)
	userFeedbackDao := dao6.NewUserFeedbackDao(db)
	devUserFeedback := processor2.NewDevUserFeedback(userFeedbackDao)
	inAppHelpAppClientConfigMappingDao := dao4.NewInAppHelpAppClientConfigMappingDao(db)
	devInAppHelpAppClientConfigMapping := processor2.NewDevInAppHelpAppClientConfigMapping(inAppHelpAppClientConfigMappingDao)
	inAppHelpEntityMappingDao := dao4.NewInAppHelpEntityMappingDao(db)
	devInAppHelpEntityMapping := processor2.NewDevInAppHelpEntityMapping(inAppHelpEntityMappingDao)
	feedbackQuestionResponsesDao := dao7.NewFeedbackQuestionResponsesDao(db)
	devFeedbackQuestionResponse := processor2.NewDevFeedbackQuestionResponse(feedbackQuestionResponsesDao)
	feedbackQuestionsDao := dao7.NewFeedbackQuestionsDao(db)
	devFeedbackQuestion := processor2.NewDevFeedbackQuestion(feedbackQuestionsDao)
	feedbackSurveyAttemptsDao := dao7.NewFeedbackSurveyAttemptsDao(db)
	devFeedbackSurveyAttempt := processor2.NewDevFeedbackSurveyAttempt(feedbackSurveyAttemptsDao)
	feedbackSurveysDao := dao7.NewFeedbackSurveysDao(db)
	devFeedbackSurvey := processor2.NewDevFeedbackSurvey(feedbackSurveysDao)
	devFactory := developer.NewDevFactory(devArticles, devCategories, devFolders, devFaqVersion, devRelatedFaq, devMediaContent, devMediaPlaylist, devUIContextToMediaPlaylistMapping, devMediaPlaylistToMediaContentMapping, devFeedbackAttempt, devUserFeedback, devInAppHelpAppClientConfigMapping, devInAppHelpEntityMapping, devFeedbackQuestionResponse, devFeedbackQuestion, devFeedbackSurveyAttempt, devFeedbackSurvey)
	inAppHelpDev := developer.NewInAppHelpDev(devFactory)
	return inAppHelpDev
}

func InitializeAppFeedbackService(db types.InapphelpPGDB, rewardsClient rewards.RewardsGeneratorClient, orderClient order.OrderServiceClient, rlcRedisClient types2.RateLimiterRedisStore, userClient user.UsersClient, actorClient actor.ActorClient, chatClient chat.ChatsClient, vmClient vendormapping.VendorMappingServiceClient, onboardingClient onboarding.OnboardingClient, fitttClient fittt.FitttClient, cardClient provisioning.CardProvisioningClient, ticketClient ticket.TicketClient, caClient connected_account.ConnectedAccountClient, netWorthClient networth.NetWorthClient, creditReportClient creditreportv2.CreditReportManagerClient, genConf *genconf.Config, conf *config.Config) *app_feedback.Service {
	allRewardRuleProcessor := processor3.NewAllRewardRuleProcessor(rewardsClient)
	allTxnRuleProcessor := processor3.NewAllTxnRuleProcessor(orderClient)
	referralRewardRuleProcessor := processor3.NewReferralRewardRuleProcessor(rewardsClient)
	onbAddFundsRuleProcessor := processor3.NewOnbAddFundsProcessor(orderClient, actorClient, onboardingClient)
	genericAddFundsRuleProcessor := processor3.NewGenericAddFundsRuleProcessor(orderClient)
	fitRuleProcessor := processor3.NewFITRuleProcessor(fitttClient)
	debitCardActivationRuleProcessor := processor3.NewDebitCardActivationRuleProcessor(onboardingClient, cardClient, actorClient)
	depositAddFundsRuleProcessor := processor3.NewDepositAddFundsRuleProcessor(orderClient)
	connectedAccountRuleProcessor := processor3.NewConnectedAccountRuleProcessor(caClient)
	netWorthRuleProcessor := processor3.NewNetWorthRuleProcessor(netWorthClient)
	creditScoreRuleProcessor := processor3.NewCreditScoreRuleProcessor(creditReportClient)
	factory := processor3.NewRuleFactory(allRewardRuleProcessor, allTxnRuleProcessor, referralRewardRuleProcessor, onbAddFundsRuleProcessor, genericAddFundsRuleProcessor, fitRuleProcessor, debitCardActivationRuleProcessor, depositAddFundsRuleProcessor, connectedAccountRuleProcessor, netWorthRuleProcessor, creditScoreRuleProcessor)
	feedbackAttemptDao := dao6.NewFeedbackAttemptDao(db)
	customerIdentifier := helper.NewCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	client := types2.RateLimiterRedisStoreProvider(rlcRedisClient)
	rateLimiter := rateLimiterProvider(conf, client)
	ruleEngine := rule_engine.NewRuleEngine(factory, feedbackAttemptDao, customerIdentifier, rateLimiter, ticketClient, conf)
	userFeedbackDao := dao6.NewUserFeedbackDao(db)
	inAppFeedbackConfig := getInAppFeedbackConfig(conf)
	in_app_feedback_helperHelper := in_app_feedback_helper.NewInAppFeedbackHelper(inAppFeedbackConfig)
	service := app_feedback.NewAppFeedbackService(ruleEngine, feedbackAttemptDao, userFeedbackDao, in_app_feedback_helperHelper, genConf)
	return service
}

func InitializeInAppHelpMediaService(db types.InapphelpPGDB, segmentClient segment.SegmentationServiceClient, genConf *genconf.Config, onboardingClient onboarding.OnboardingClient) *media.Service {
	mediaContentDao := dao5.NewMediaContentDao(db)
	mediaPlaylistDao := dao5.NewMediaPlaylistDao(db)
	mediaPlaylistToMediaContentMappingDao := dao5.NewMediaPlaylistToMediaContentMappingDao(db)
	uiContextToMediaPlaylistMappingDao := dao5.NewUIContextToMediaPlaylistMappingDao(db)
	inAppHelpEntityMappingDao := dao4.NewInAppHelpEntityMappingDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	service := media.NewInAppHelpMediaService(mediaContentDao, mediaPlaylistDao, mediaPlaylistToMediaContentMappingDao, uiContextToMediaPlaylistMappingDao, inAppHelpEntityMappingDao, gormTxnExecutor, segmentClient, genConf, onboardingClient)
	return service
}

func InitializeMockCustomEligibilityEvaluationClient() *mock_client.MockCustomEligibilityEvaluationClient {
	mockCustomEligibilityEvaluationClient := mock_client.NewMockCustomEligibilityEvaluationClient()
	return mockCustomEligibilityEvaluationClient
}

func InitializeFeedbackEngineService(sherlockDb types.SherlockPGDB, db types.InapphelpPGDB, genConf *genconf.Config, cxClient question_response_subscription.FeedbackSubscriptionServiceClient, feedbackInfoEventSnsPublisher types2.FeedbackInfoEventSnsPublisher, rewardsClient rewards.RewardsGeneratorClient, payClient pay.PayClient, mockClient mock_client2.MockCustomEligibilityEvaluationServiceClient, vkycClient vkyc.VKYCFeClient, broker events.Broker) *feedback_engine.FeedbackEngineService {
	feedbackQuestionsDao := dao7.NewFeedbackQuestionsDao(db)
	feedbackSurveysDao := dao7.NewFeedbackSurveysDao(db)
	feedbackQuestionResponsesDao := dao7.NewFeedbackQuestionResponsesDao(db)
	feedbackSurveyAttemptsDao := dao7.NewFeedbackSurveyAttemptsDao(db)
	inAppHelpEntityMappingDao := dao4.NewInAppHelpEntityMappingDao(db)
	inAppHelpAppClientConfigMappingDao := dao4.NewInAppHelpAppClientConfigMappingDao(db)
	feedbackSurveyCoolOffsDao := dao7.NewFeedbackSurveyCoolOffsDao(db)
	feedbackSurveyMappingsDao := dao7.NewFeedbackSurveyMappingsDao(db)
	iCustomEligibilityEvaluationFactory := CustomEligibilityEvaluationFactoryProvider(mockClient, rewardsClient, payClient, vkycClient)
	iFeedbackSubscriptionFactory := QuestionResponseSubscriptionFactoryProvider(cxClient)
	feedbackEngineService := feedback_engine.NewFeedbackEngineService(genConf, feedbackQuestionsDao, feedbackSurveysDao, feedbackQuestionResponsesDao, feedbackSurveyAttemptsDao, inAppHelpEntityMappingDao, inAppHelpAppClientConfigMappingDao, feedbackSurveyCoolOffsDao, feedbackSurveyMappingsDao, iCustomEligibilityEvaluationFactory, iFeedbackSubscriptionFactory, feedbackInfoEventSnsPublisher, broker)
	return feedbackEngineService
}

func InitializeFeedbackEngineWebService(db types.InapphelpPGDB, genConf *genconf.Config) *jarvis.FeedbackEngineWeb {
	feedbackQuestionsDao := dao7.NewFeedbackQuestionsDao(db)
	feedbackSurveysDao := dao7.NewFeedbackSurveysDao(db)
	feedbackQuestionResponsesDao := dao7.NewFeedbackQuestionResponsesDao(db)
	feedbackSurveyAttemptsDao := dao7.NewFeedbackSurveyAttemptsDao(db)
	inAppHelpEntityMappingDao := dao4.NewInAppHelpEntityMappingDao(db)
	inAppHelpAppClientConfigMappingDao := dao4.NewInAppHelpAppClientConfigMappingDao(db)
	feedbackSurveyCoolOffsDao := dao7.NewFeedbackSurveyCoolOffsDao(db)
	feedbackSurveyMappingsDao := dao7.NewFeedbackSurveyMappingsDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	feedbackEntityApprovalsDao := dao7.NewFeedbackEntityApprovalsDao(db)
	feedbackEngineWeb := jarvis.NewFeedbackEngineWebService(genConf, feedbackQuestionsDao, feedbackSurveysDao, feedbackQuestionResponsesDao, feedbackSurveyAttemptsDao, inAppHelpEntityMappingDao, inAppHelpAppClientConfigMappingDao, feedbackSurveyCoolOffsDao, feedbackSurveyMappingsDao, gormTxnExecutor, feedbackEntityApprovalsDao)
	return feedbackEngineWeb
}

func InitializeIssueReportingService(sherlockDb types.SherlockPGDB, genConf *genconf.Config, userClient user.UsersClient, aaClient actor_activity.ActorActivityClient, ticketClient ticket.TicketClient, watsonClient watson.WatsonClient, issueReportingClient issue_reporting.ServiceClient, redisClient types3.CxRedisStore, searchClient search.ActionBarClient, issueConfigClient issue_config.IssueConfigManagementClient, eventBroker events.Broker, conf *config.Config, ffAccClient accounting.AccountingClient, cardProvisioningClient provisioning.CardProvisioningClient, tieringClient tiering.TieringClient, riskProfileClient profile.ProfileClient) *issue_reporting2.Service {
	issueReportingConfig := issueReportingFlowConfigProvider(genConf)
	smartGptImpl := provideSmartGptParser(issueReportingConfig)
	activityPromptGenerator := prompt_generator.NewActivityPromptGenerator(aaClient, issueReportingConfig, eventBroker)
	issueCategoryDao := dao8.NewIssueCategoryDao(sherlockDb)
	issueCategoryManagerImpl := manager.NewIssueCategoryManagerImpl(issueCategoryDao)
	ticketPromptGenerator := prompt_generator.NewTicketPromptGenerator(ticketClient, issueReportingConfig, issueCategoryManagerImpl, eventBroker)
	incidentPromptGenerator := prompt_generator.NewIncidentPromptGenerator(watsonClient, issueReportingConfig, issueCategoryManagerImpl, eventBroker)
	client := NewRedisClient(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	customer360PromptGenerator := prompt_generator.NewCustomer360PromptGenerator(issueReportingConfig, redisCacheStorage, ffAccClient, cardProvisioningClient, tieringClient, eventBroker)
	promptGeneratorFactoryImpl := prompt_generator.NewPromptGeneratorFactoryImpl(activityPromptGenerator, ticketPromptGenerator, incidentPromptGenerator, customer360PromptGenerator)
	searchHistoryDaoImpl := dao9.NewSearchHistoryDao(redisCacheStorage, issueReportingConfig)
	recentIssueCollector := issue_collector.NewRecentIssueCollector(searchHistoryDaoImpl, issueReportingConfig)
	configIssueReportingConfig := issueReportingFlowStaticConfigProvider(conf)
	trendingIssueCollector := issue_collector.NewTrendingIssueCollector(configIssueReportingConfig, genConf)
	suggestedIssueCollector := issue_collector.NewSuggestedIssueCollector(searchClient, issueReportingConfig)
	collectorFactoryImpl := issue_collector.NewCollectorFactory(recentIssueCollector, trendingIssueCollector, suggestedIssueCollector)
	userQueryLogDaoImpl := dao9.NewUserQueryLogDao(sherlockDb)
	issueCategorizerImpl := provideIssueCategorizer(genConf, eventBroker, redisCacheStorage)
	service := issue_reporting2.NewIssueReportingService(genConf, smartGptImpl, userClient, promptGeneratorFactoryImpl, issueReportingClient, collectorFactoryImpl, ticketClient, searchHistoryDaoImpl, userQueryLogDaoImpl, issueConfigClient, issueCategorizerImpl, issueCategoryManagerImpl, eventBroker, conf, redisCacheStorage, riskProfileClient, searchClient)
	return service
}

// wire.go:

func GormDBProvider(db types.InapphelpPGDB) *gorm.DB {
	return db
}

func getTxnExecutor(dbConn *gorm.DB) storagev2.TxnExecutor {
	txnExecutor := storagev2.NewGormTxnExecutor(dbConn)
	return txnExecutor
}

func getPopularFAQConfig(conf *genconf.Config) *genconf.PopularFAQConfig {
	return conf.PopularFAQConfig()
}

func getFaqArticleFeedbackConfig(conf *config.Config) *config.FaqArticleFeedbackConf {
	return conf.FaqArticleFeedbackConf
}

func getFlagsConfig(conf *genconf.Config) *genconf.Flags {
	return conf.Flags()
}

func getRelatedFaqS3Path(conf *config.Config) string {
	return conf.RelatedFaqConfig.ObjectKey
}

func getRelatedFaqCount(conf *config.Config) int {
	return conf.RelatedFaqCount
}

func getFaqArticleFeedbackConf(conf *config.Config) *config.FaqArticleFeedbackConf {
	return conf.FaqArticleFeedbackConf
}

func rateLimiterProvider(conf *config.Config, redisRlClient *redis.Client) ratelimiter.RateLimiter {
	inapphelpRLClient, err := ratelimiter.NewRateLimiter(store.NewSlidingWindowLogWithRedis(redisRlClient), conf.RateLimitConfig)
	if err != nil {
		logger.ErrorNoCtx("failed to initialize rate limit client", zap.Error(err))
	}
	return inapphelpRLClient
}

func getInAppFeedbackConfig(conf *config.Config) *config.InAppFeedbackConfig {
	return conf.InAppFeedbackConfig
}

func CustomEligibilityEvaluationFactoryProvider(mockClient mock_client2.MockCustomEligibilityEvaluationServiceClient, rewardsClient rewards.RewardsGeneratorClient, payClient pay.PayClient, vkycClient vkyc.VKYCFeClient) custom_eligibility_evaluation.ICustomEligibilityEvaluationFactory {
	customEligibilityEvaluationFactory := custom_eligibility_evaluation.NewCustomEligibilityEvaluationFactory()
	customEligibilityEvaluationFactory.RegisterCustomEligibilityEvaluator(typesv2.ServiceName_INAPP_HELP_SERVICE, mockClient)
	customEligibilityEvaluationFactory.RegisterCustomEligibilityEvaluator(typesv2.ServiceName_REWARD_SERVICE, rewardsClient)
	customEligibilityEvaluationFactory.RegisterCustomEligibilityEvaluator(typesv2.ServiceName_PAY_SERVICE, payClient)
	customEligibilityEvaluationFactory.RegisterCustomEligibilityEvaluator(typesv2.ServiceName_KYC_SERVICE, vkycClient)
	return customEligibilityEvaluationFactory
}

func QuestionResponseSubscriptionFactoryProvider(cxClient question_response_subscription.FeedbackSubscriptionServiceClient) question_response_subscription2.IFeedbackSubscriptionFactory {
	questionResponseSubscriptionFactory := question_response_subscription2.NewQuestionResponseSubscriberFactory()
	questionResponseSubscriptionFactory.RegisterFeedbackSubscriber(typesv2.ServiceName_CX_SERVICE, cxClient)
	return questionResponseSubscriptionFactory
}

func provideSmartGptParser(issueReportingGenConf *genconf.IssueReportingConfig) repository.SmartGptImpl {
	host := cfg.GetServerEndPoint(cfg.SMART_GPT_SERVER).URL()
	return repository.NewSmartGptImpl(host, issueReportingGenConf)
}

func provideIssueCategorizer(conf *genconf.Config, eventBroker events.Broker, cacheStorage cache.CacheStorage) *repository.IssueCategorizerImpl {
	host := cfg.GetServerEndPoint(cfg.NLU_SERVER).URL()
	return repository.NewIssueCategorizer(host, conf.IssueReportingConfig().IssueCategorizerModelConfig(),
		eventBroker, cacheStorage)
}

func NewRedisClient(cxRedisStore types3.CxRedisStore) *redis.Client {
	return cxRedisStore
}

func issueReportingFlowConfigProvider(conf *genconf.Config) *genconf.IssueReportingConfig {
	return conf.IssueReportingConfig()
}

func issueReportingFlowStaticConfigProvider(conf *config.Config) *config.IssueReportingConfig {
	return conf.IssueReportingConfig
}
