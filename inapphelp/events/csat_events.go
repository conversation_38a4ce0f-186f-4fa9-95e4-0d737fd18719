package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

type CsatLLMEvent struct {
	ActorId    string
	ProspectId string
	QueryId    string
	AttemptId  string
	EventId    string
	Timestamp  time.Time
	EventType  string
}

func NewCsatLLMEvent(actorId, queryId, attemptId string) *CsatLLMEvent {
	return &CsatLLMEvent{
		ActorId:    actorId,
		ProspectId: uuid.New().String(),
		QueryId:    queryId,
		AttemptId:  attemptId,
		EventId:    uuid.New().String(),
		Timestamp:  time.Now(),
		EventType:  events.EventTrack,
	}
}

func (c *CsatLLMEvent) GetEventType() string {
	return c.EventType
}

func (c *CsatLLMEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *CsatLLMEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *CsatLLMEvent) GetEventId() string {
	return c.EventId
}

func (c *CsatLLMEvent) GetUserId() string {
	return c.ActorId
}

func (c *CsatLLMEvent) GetProspectId() string {
	return c.ProspectId
}

func (c *CsatLLMEvent) GetEventName() string {
	return EventCSATLLMQueryResponse
}
