package onboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"math"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	oafv2Pb "github.com/epifi/gamma/api/frontend/pay/add_funds_v2/onboarding"
	typesUi "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config/genconf"
	helper2 "github.com/epifi/gamma/frontend/pay/transaction/add_funds_v2_params/helper"
)

func getBenefitsComponent(minorVersion oafv2Pb.OnboardingAddFundsV2MinorVersion, pageInfoForFlow *genconf.PageInfoForFlow, showUpdatedBenefitsForAffluentUser bool) *oafv2Pb.BenefitsComponent {
	// get range based benefits
	rangeBasedBenefits := getRangeBasedBenefits(minorVersion, pageInfoForFlow, showUpdatedBenefitsForAffluentUser)

	res := &oafv2Pb.BenefitsComponent{
		Title: &typesUi.IconTextComponent{
			RightIcon: commontypes.GetImageFromUrl(benefitsInfoIconUrl).WithHeight(16).WithWidth(16),
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(benefitsTitleText, benefitsTitleTextFontColour, commontypes.FontStyle_SUBTITLE_S),
			},
		},
		InfoPopup: &oafv2Pb.InfoPopUpDetails{
			TitleImage:  commontypes.GetImageFromUrl(pageInfoForFlow.BenefitInfoPopUpDetails().ImageUrl).WithHeight(48).WithWidth(48),
			Title:       commontypes.GetTextFromStringFontColourFontStyle(pageInfoForFlow.BenefitInfoPopUpDetails().Title, infoPopUpTitleFontColour, commontypes.FontStyle_HEADLINE_M),
			Description: commontypes.GetTextFromStringFontColourFontStyle(pageInfoForFlow.BenefitInfoPopUpDetails().Description, infoPopUpDescriptionFontColour, commontypes.FontStyle_BODY_3_PARA),
			BgColour:    widget.GetBlockBackgroundColour(infoPopUpBgColour),
		},
		BgColour:           widget.GetBlockBackgroundColour(benefitsBgColour),
		RangeBasedBenefits: rangeBasedBenefits,
	}

	if helper2.IsMinorVersionAsExpected(minorVersion, oafv2Pb.OnboardingAddFundsV2MinorVersion_ONE) {
		res.Title = nil
		res.InfoPopup = nil
		res.BgColour = widget.GetBlockBackgroundColour(benefitsBgColourV2_1)
	}

	return res
}

// nolint:funlen
func getRangeBasedBenefits(minorVersion oafv2Pb.OnboardingAddFundsV2MinorVersion, pageInfoForFlow *genconf.PageInfoForFlow, showUpdatedBenefitsForAffluentUser bool) []*oafv2Pb.RangeBasedBenefitsInfo {

	var res []*oafv2Pb.RangeBasedBenefitsInfo

	rangeBasedBenefits := pageInfoForFlow.RangeBasedBenefits()
	rangeBasedBenefits.Range(func(rangeName string, rangeBasedBenefit *genconf.OnbAddFundsV2RangeBasedBenefit) bool {
		var benefitInfosForRange []*oafv2Pb.BenefitInfo
		benefits := rangeBasedBenefit.Benefits()

		for _, benefit := range benefits {
			titleEnabled := benefit.Title
			titleDisabled := benefit.TitleDisabled

			if showUpdatedBenefitsForAffluentUser {
				titleEnabled = benefit.TitleAffluentUser
				titleDisabled = benefit.TitleDisabledAffluentUser
			}

			rightIconTextDisabled := &typesUi.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle(benefit.RightTextDisabled, benefitRightTextDisabledFontColour, commontypes.FontStyle_BODY_XS),
				},
			}

			if helper2.IsMinorVersionAsExpected(minorVersion, oafv2Pb.OnboardingAddFundsV2MinorVersion_ONE) {
				rightIconTextDisabled = &typesUi.IconTextComponent{
					Texts: []*commontypes.Text{
						commontypes.GetTextFromStringFontColourFontStyle(benefit.RightTextDisabled, benefitRightTextDisabledFontColourV2_1, commontypes.FontStyle_BODY_XS),
					},
					RightIcon:          commontypes.GetImageFromUrl(benefit.RightIconDisabled).WithHeight(benefitEnabledIconHeight).WithWidth(benefitEnabledIconWidth),
					RightImgTxtPadding: 8,
				}
			}

			benefitInfosForRange = append(benefitInfosForRange, &oafv2Pb.BenefitInfo{
				MinAmount:        getFeMoneyFromCurrCodeAndUnits(inrCurrencyCode, benefit.MinAmount),
				MaxAmount:        getFeMoneyFromCurrCodeAndUnits(inrCurrencyCode, math.MaxInt),
				LeftIcon:         commontypes.GetImageFromUrl(benefit.LeftIcon).WithHeight(benefitIconHeight).WithWidth(benefitIconWidth),
				LeftIconDisabled: commontypes.GetImageFromUrl(benefit.LeftIconDisabled),
				Title:            commontypes.GetTextFromStringFontColourFontStyle(titleEnabled, benefitsTitleEnabledFontColour, commontypes.FontStyle_SUBTITLE_S),
				TitleDisabled:    commontypes.GetTextFromStringFontColourFontStyle(titleDisabled, benefitsTitleDisabledFontColour, commontypes.FontStyle_SUBTITLE_S),
				RightIconText: &typesUi.IconTextComponent{
					RightIcon: commontypes.GetImageFromUrl(benefit.RightIconEnabled).WithHeight(benefitEnabledIconHeight).WithWidth(benefitEnabledIconWidth),
				},
				RightIconTextDisabled: rightIconTextDisabled,
			})
		}

		minAmount := getFeMoneyFromCurrCodeAndUnits(inrCurrencyCode, rangeBasedBenefit.MinAmount())
		maxAmount := getFeMoneyFromCurrCodeAndUnits(inrCurrencyCode, rangeBasedBenefit.MaxAmount())
		// -1 as max amount in a range denotes highest possible enter able amount
		if rangeBasedBenefit.MaxAmount() == -1 {
			maxAmount = getFeMoneyFromCurrCodeAndUnits(inrCurrencyCode, math.MaxInt)
		}

		res = append(res, &oafv2Pb.RangeBasedBenefitsInfo{
			MinAmount:    minAmount,
			MaxAmount:    maxAmount,
			BenefitInfos: benefitInfosForRange,
		})
		return true
	})

	return res
}
