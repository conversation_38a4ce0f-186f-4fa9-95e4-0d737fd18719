package processor

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	consentPb "github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/frontend/header"
	feUserPb "github.com/epifi/gamma/api/frontend/user"
	panPb "github.com/epifi/gamma/api/pan"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/form"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/mocks"
	feErrors "github.com/epifi/gamma/pkg/frontend/errors"
	headerPkg "github.com/epifi/gamma/pkg/frontend/header"
)

func TestNsdlPanValidator_ValidateFields(t *testing.T) {
	e := feErrors.NewClientError(feErrors.UNKNOWN_CLIENT_ERR)
	type args struct {
		ctx context.Context
		req *feUserPb.UpdateFormDetailsRequest
	}
	type mockStruct struct {
		mockUserClient *mocks.MockUsersClient
	}
	tests := []struct {
		name      string
		args      args
		mockCalls func(m *mockStruct)
		want      map[string]*commontypes.Text
		want1     *header.ResponseHeader
	}{
		{
			name: "invalid pan",
			args: args{
				ctx: context.Background(),
				req: &feUserPb.UpdateFormDetailsRequest{
					Values: map[string]*form.FieldValue{
						form.FieldIdentifier_FIELD_IDENTIFIER_PAN.String(): {
							Type:  form.FieldValueType_FIELD_VALUE_TYPE_STRING,
							Value: &form.FieldValue_StringValue{StringValue: "PPPPP1002"},
						},
					},
				},
			},
			want: map[string]*commontypes.Text{
				form.FieldIdentifier_FIELD_IDENTIFIER_PAN.String(): commontypes.GetTextFromStringFontColourFontStyle("Please enter a valid PAN number", "#FA3B11", commontypes.FontStyle_BODY_4),
			},
			want1: nil,
		},
		{
			name: "invalid dob",
			args: args{
				ctx: context.Background(),
				req: &feUserPb.UpdateFormDetailsRequest{
					Values: map[string]*form.FieldValue{
						form.FieldIdentifier_FIELD_IDENTIFIER_PAN_DATE_OF_BIRTH.String(): {
							Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
							Value: &form.FieldValue_DateValue{DateValue: &types.Date{
								Year:  19944,
								Month: 1,
								Day:   1,
							}},
						},
					},
				},
			},
			want: map[string]*commontypes.Text{
				form.FieldIdentifier_FIELD_IDENTIFIER_PAN_DATE_OF_BIRTH.String(): commontypes.GetTextFromStringFontColourFontStyle("Please enter valid date of birth", "#FA3B11", commontypes.FontStyle_BODY_4),
			},
			want1: nil,
		},
		{
			name: "successful pan dob form inputs, nsdl pan validation api failure",
			args: args{
				ctx: context.Background(),
				req: &feUserPb.UpdateFormDetailsRequest{
					Values: map[string]*form.FieldValue{
						form.FieldIdentifier_FIELD_IDENTIFIER_PAN_DATE_OF_BIRTH.String(): {
							Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
							Value: &form.FieldValue_DateValue{DateValue: &types.Date{
								Year:  1994,
								Month: 1,
								Day:   1,
							}},
						},
						form.FieldIdentifier_FIELD_IDENTIFIER_PAN.String(): {
							Type:  form.FieldValueType_FIELD_VALUE_TYPE_STRING,
							Value: &form.FieldValue_StringValue{StringValue: "**********"},
						},
					},
				},
			},
			mockCalls: func(m *mockStruct) {
				m.mockUserClient.EXPECT().ValidateAndStorePan(gomock.Any(), gomock.Any()).Return(&user.ValidateAndStorePanResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:  nil,
			want1: headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, fmt.Sprintf("%v", e.Code), e.Title, e.Subtitle, ""),
		},
		{
			name: "successful pan dob form inputs, nsdl pan validation api failure",
			args: args{
				ctx: context.Background(),
				req: &feUserPb.UpdateFormDetailsRequest{
					Values: map[string]*form.FieldValue{
						form.FieldIdentifier_FIELD_IDENTIFIER_PAN_DATE_OF_BIRTH.String(): {
							Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
							Value: &form.FieldValue_DateValue{DateValue: &types.Date{
								Year:  1994,
								Month: 1,
								Day:   1,
							}},
						},
						form.FieldIdentifier_FIELD_IDENTIFIER_PAN.String(): {
							Type:  form.FieldValueType_FIELD_VALUE_TYPE_STRING,
							Value: &form.FieldValue_StringValue{StringValue: "**********"},
						},
					},
				},
			},
			mockCalls: func(m *mockStruct) {
				m.mockUserClient.EXPECT().ValidateAndStorePan(gomock.Any(), gomock.Any()).Return(&user.ValidateAndStorePanResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:  nil,
			want1: headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, fmt.Sprintf("%v", e.Code), e.Title, e.Subtitle, ""),
		},
		{
			name: "successful pan dob form inputs, nsdl pan validation resource exhausted",
			args: args{
				ctx: context.Background(),
				req: &feUserPb.UpdateFormDetailsRequest{
					Values: map[string]*form.FieldValue{
						form.FieldIdentifier_FIELD_IDENTIFIER_PAN_DATE_OF_BIRTH.String(): {
							Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
							Value: &form.FieldValue_DateValue{DateValue: &types.Date{
								Year:  1994,
								Month: 1,
								Day:   1,
							}},
						},
						form.FieldIdentifier_FIELD_IDENTIFIER_PAN.String(): {
							Type:  form.FieldValueType_FIELD_VALUE_TYPE_STRING,
							Value: &form.FieldValue_StringValue{StringValue: "**********"},
						},
					},
				},
			},
			mockCalls: func(m *mockStruct) {
				m.mockUserClient.EXPECT().ValidateAndStorePan(gomock.Any(), gomock.Any()).Return(&user.ValidateAndStorePanResponse{
					Status: rpc.StatusResourceExhausted(),
				}, nil)
			},
			want:  nil,
			want1: headerPkg.InlineErrResp(feErrors.ErrViewStatus, feErrors.NewClientError(feErrors.PAN_MAX_RETRIES).CodeStr, feErrors.NewClientError(feErrors.PAN_MAX_RETRIES).Title),
		},
		{
			name: "successful pan dob form inputs, nsdl pan validation success",
			args: args{
				ctx: context.Background(),
				req: &feUserPb.UpdateFormDetailsRequest{
					ConsentIds: []string{
						consentPb.ConsentType_cKYC.String(),
						consentPb.ConsentType_CONSENT_NOT_POLITICALLY_EXPOSED.String(),
					},
					Values: map[string]*form.FieldValue{
						form.FieldIdentifier_FIELD_IDENTIFIER_PAN_DATE_OF_BIRTH.String(): {
							Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
							Value: &form.FieldValue_DateValue{DateValue: &types.Date{
								Year:  1994,
								Month: 1,
								Day:   1,
							}},
						},
						form.FieldIdentifier_FIELD_IDENTIFIER_PAN.String(): {
							Type:  form.FieldValueType_FIELD_VALUE_TYPE_STRING,
							Value: &form.FieldValue_StringValue{StringValue: "**********"},
						},
					},
				},
			},
			mockCalls: func(m *mockStruct) {
				m.mockUserClient.EXPECT().ValidateAndStorePan(gomock.Any(), gomock.Any()).Return(&user.ValidateAndStorePanResponse{
					Status:              rpc.StatusOk(),
					PanValid:            commontypes.BooleanEnum_TRUE,
					NameMatch:           commontypes.BooleanEnum_TRUE,
					DobMatch:            commontypes.BooleanEnum_TRUE,
					PanAadharLinkStatus: panPb.PANAadharLinkStatus_PAN_AADHAR_STATUS_LINKED,
				}, nil)
			},
			want:  map[string]*commontypes.Text{},
			want1: nil,
		},
		{
			name: "successful pan dob form inputs, nsdl pan validation - invalid dob",
			args: args{
				ctx: context.Background(),
				req: &feUserPb.UpdateFormDetailsRequest{
					Values: map[string]*form.FieldValue{
						form.FieldIdentifier_FIELD_IDENTIFIER_PAN_DATE_OF_BIRTH.String(): {
							Type: form.FieldValueType_FIELD_VALUE_TYPE_STRING,
							Value: &form.FieldValue_DateValue{DateValue: &types.Date{
								Year:  1994,
								Month: 1,
								Day:   1,
							}},
						},
						form.FieldIdentifier_FIELD_IDENTIFIER_PAN.String(): {
							Type:  form.FieldValueType_FIELD_VALUE_TYPE_STRING,
							Value: &form.FieldValue_StringValue{StringValue: "**********"},
						},
					},
				},
			},
			mockCalls: func(m *mockStruct) {
				m.mockUserClient.EXPECT().ValidateAndStorePan(gomock.Any(), gomock.Any()).Return(&user.ValidateAndStorePanResponse{
					Status:              rpc.StatusOk(),
					PanValid:            commontypes.BooleanEnum_TRUE,
					NameMatch:           commontypes.BooleanEnum_TRUE,
					DobMatch:            commontypes.BooleanEnum_FALSE,
					PanAadharLinkStatus: panPb.PANAadharLinkStatus_PAN_AADHAR_STATUS_LINKED,
				}, nil)
			},
			want: map[string]*commontypes.Text{
				form.FieldIdentifier_FIELD_IDENTIFIER_PAN_DATE_OF_BIRTH.String(): commontypes.GetTextFromStringFontColourFontStyle("Make sure this matches your PAN card", "#FA3B11", commontypes.FontStyle_BODY_4),
			},
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockUsersClient := mocks.NewMockUsersClient(ctrl)

			m := &mockStruct{
				mockUserClient: mockUsersClient,
			}

			if tt.mockCalls != nil {
				tt.mockCalls(m)
			}

			v := &NsdlPanValidator{
				usersClient: mockUsersClient,
				genConfig:   genconfig,
				// consentClient: tt.fields.consentClient,
				// eventBroker:   tt.fields.eventBroker,
			}
			got, got1 := v.ValidateFields(tt.args.ctx, tt.args.req)
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("ValidateFields() (-got +want), %s", diff)
			}
			if diff1 := cmp.Diff(got1, tt.want1, protocmp.Transform()); diff1 != "" {
				t.Errorf("ValidateFields() (-got1 +want1), %s", diff1)
			}
		})
	}
}
