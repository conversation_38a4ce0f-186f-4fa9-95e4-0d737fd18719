package metrics

import (
	"github.com/prometheus/client_golang/prometheus"

	types "github.com/epifi/be-common/api/typesv2/common"
)

type PanAutofillMetrics struct {
	panAutofillCounter   *prometheus.CounterVec
	panValidationCounter *prometheus.CounterVec
}

var (
	panAutofillCounter = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "pan_autofill_total",
		Help: "Counter of users for whom PAN details were autofilled",
	}, []string{"pan_autofilled", "dob_autofilled", "name_autofilled"})
	panValidationCounter = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "pan_validation_total",
		Help: "Counter of users for whom PAN details were validated",
	}, []string{"pan_valid", "dob_valid", "name_valid"})

	// nolint:unparam
	_ = func() struct{} {
		prometheus.MustRegister(panAutofillCounter)
		prometheus.MustRegister(panValidationCounter)
		return struct{}{}
	}()
)

func RecordPanAutofillMetrics(isPanAutofilled, isDobAutofilled, isNameAutofilled types.BooleanEnum) {
	panAutofillCounter.WithLabelValues(isPanAutofilled.String(), isDobAutofilled.String(), isNameAutofilled.String()).Inc()
}

func RecordPanValidationMetrics(isPanValid, isDobValid, isNameValid types.BooleanEnum) {
	panValidationCounter.WithLabelValues(isPanValid.String(), isDobValid.String(), isNameValid.String()).Inc()
}
