package onboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	payTypesPb "github.com/epifi/gamma/api/typesv2/pay"

	"context"
	"fmt"
	"strconv"
	"strings"

	pkgColors "github.com/epifi/be-common/pkg/colors"
	fePkgUpi "github.com/epifi/gamma/frontend/pkg/upi"

	"github.com/iancoleman/strcase"
	"github.com/samber/lo"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	feUpiOnbPb "github.com/epifi/gamma/api/frontend/upi/onboarding"
	feUpiOnbEnumsPb "github.com/epifi/gamma/api/frontend/upi/onboarding/enums"
	typesUiPb "github.com/epifi/gamma/api/typesv2/ui"
	upiOnbPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnbEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
)

const (
	dotsForMaskedAccountNumber = ".."
	activateUpiNumber          = "Activate"
	reclaimUpiNumber           = "Reclaim"
	timeFormat                 = "02-01-2006"
)

// GetUpiNumberDetails fetches the upiNumber details for a given derivedAccountId and vpa
// this rpc is responsible for each and every detail to be shown on ManageUpiNumberScreen
// nolint
func (s *Service) GetUpiNumberDetails(ctx context.Context, req *feUpiOnbPb.GetUpiNumberDetailsRequest) (*feUpiOnbPb.GetUpiNumberDetailsResponse, error) {

	var (
		res = &feUpiOnbPb.GetUpiNumberDetailsResponse{
			RespHeader: &headerPb.ResponseHeader{},
		}
		getUpiNumberDetailsBeRes *upiOnbPb.GetUpiNumberDetailsResponse
		feUpiNumberDetails       []*feUpiOnbPb.UpiNumberDetail
		accountId                string
		actorId                  = req.GetReq().GetAuth().GetActorId()
		err                      error
	)

	// mandatory field check
	if req.GetDerivedAccountId() == "" || req.GetVpa() == "" {
		logger.Error(ctx, "mandatory fields can't be empty",
			zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))

		res.RespHeader.Status = rpcPb.StatusInvalidArgument()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	// fetch accountId from derivedAccountId
	accountId, err = fePkgUpi.GetTpapAccountIdFromDerivedId(req.GetDerivedAccountId())
	if err != nil || accountId == "" {
		logger.Error(ctx, "error in fetching tpap account id from derived id", zap.String(logger.ACCOUNT_ID, accountId), zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	getUpiNumberDetailsBeRes, err = s.upiOnboardingClient.GetUpiNumberDetails(ctx,
		&upiOnbPb.GetUpiNumberDetailsRequest{
			AccountId: accountId,
			Vpa:       req.GetVpa(),
		})

	if err = epifigrpc.RPCError(getUpiNumberDetailsBeRes, err); err != nil &&
		// It is possible there is no upiNumberDetail for the user
		// when user has not linked any upiNumber
		!getUpiNumberDetailsBeRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error while calling GetUpiNumberDetails() backend rpc",
			zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(err))

		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	res.AccountDetail = s.getAccountDetail(getUpiNumberDetailsBeRes.GetAccountInfo(), req.GetVpa())
	if feUpiNumberDetails, err = s.getFeUpiNumberDetails(ctx, getUpiNumberDetailsBeRes.GetUpiNumberDetails(),
		req.GetVpa(), req.GetReq().GetAuth().GetActorId()); err != nil {
		logger.Error(ctx, "error while converting be UpiNumberDetails to fe", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusFromError(err)
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	res.UpiNumberDetails = feUpiNumberDetails

	// If upiNumber is being processed then we show `It make take up to 24 hours for your UPI Number to get set.` to the user
	for _, upiNumberDetail := range getUpiNumberDetailsBeRes.GetUpiNumberDetails() {
		// It means upi number is under processing (link / delink)
		if upiNumberDetail.GetUpiOnboardingStatus() != upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_UNSPECIFIED {
			res.FaqInfo = s.getUpiNumberBeingProcessedFaqInfo()
		}
	}

	// overflows options should be shown to user only if
	// user has not hit the MaxNumberOfNumericIdsAllowed limit
	if !getUpiNumberDetailsBeRes.GetIsMaxNumericIdsLimitHit() {
		if res.OverflowOptions, err = s.getOverFlowOptions(getUpiNumberDetailsBeRes.GetAccountInfo(), req.GetVpa()); err != nil {
			logger.Error(ctx, "error while fetching overflow options",
				zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))

			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
	}

	// If no upi number is being processed (link / delink)
	// then show default faqInfo to the user
	if res.FaqInfo == nil {
		res.FaqInfo = s.getFaqInfo()
	}

	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

// getAccountDetail basically converts be AccountDetail to fe AccountDetail to be shown to the user
func (s *Service) getAccountDetail(accountInfo *upiOnbPb.AccountInfoForUpiMapper, vpa string) *feUpiOnbPb.AccountDetail {
	accountDetailConf := s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.AccountDetailInfo
	accountDetail := &feUpiOnbPb.AccountDetail{
		BankLogoUrl: s.conf.BankNameToLogoUrlMap[accountInfo.GetBankName()],
		BankInfo: commontypes.GetTextFromStringFontColourFontStyle(s.getBankInfoText(accountInfo),
			accountDetailConf.Description[0].FontColour,
			commontypes.FontStyle(commontypes.FontStyle_value[accountDetailConf.Description[0].FontStyle])),
		AccountTypeWithVpa: commontypes.GetTextFromStringFontColourFontStyle(
			s.getAccountTypeWithVpaInfo(accountInfo.GetAccountType().String(), vpa),
			accountDetailConf.Description[1].FontColour,
			commontypes.FontStyle(commontypes.FontStyle_value[accountDetailConf.Description[1].FontStyle])),
	}

	// If account is Primary then the primary tag should be shown to the user
	if accountInfo.GetAccountPreference() == upiOnbEnumsPb.UpiAccountPreference_ACCOUNT_PREFERENCE_PRIMARY {
		accountDetail.PrimaryAccountTag = s.getPrimaryAccountTagInfo()
	}
	return accountDetail
}

// getBankInfoText generates bank info in the form: `Fi Account 4656`
func (s *Service) getBankInfoText(accountInfo *upiOnbPb.AccountInfoForUpiMapper) string {
	trimmedAccountNumber := dotsForMaskedAccountNumber + accountInfo.GetMaskedAccountNumber()[len(accountInfo.GetMaskedAccountNumber())-4:]
	return strings.Join([]string{
		accountInfo.GetBankName(),
		s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.AccountDetailInfo.Description[0].DisplayValue,
		trimmedAccountNumber}, " ")
}

// getAccountTypeWithVpaInfo generates the accountTypeWithVpa Info to be shown E.g. `Savings account • ykwatra@fbl`
func (s *Service) getAccountTypeWithVpaInfo(accountType, vpa string) string {
	return strcase.ToCamel(strings.ToLower(accountType)) + " " + s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.AccountDetailInfo.Description[1].DisplayValue + " • " + vpa
}

// getFeUpiNumberDetails basically converts be UpiNumberDetail to fe UpiNumberDetail
func (s *Service) getFeUpiNumberDetails(ctx context.Context, upiNumberDetailsBe []*upiOnbPb.UpiNumberDetail, vpa, actorId string) ([]*feUpiOnbPb.UpiNumberDetail, error) {
	var (
		upiNumberDetailsFe []*feUpiOnbPb.UpiNumberDetail
		phoneNumberFound   bool
	)

	for _, upiNumberDetailBe := range upiNumberDetailsBe {
		var upiNumberDetailFe *feUpiOnbPb.UpiNumberDetail
		switch {
		// It means some processing is going on and upiOnboardingDetail is either in CREATED / IN_PROGRESS state
		// It is sufficient to check if status is not unspecified because we are sending only CREATED / IN_PROGRESS status from Be
		case upiNumberDetailBe.GetUpiOnboardingStatus() != upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_UNSPECIFIED:
			processingUpiNumberDetailConf := s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.ProcessingUpiNumberDetail
			processingTagInfoConf := s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.ProcessingInfo
			upiNumberDetailFe = &feUpiOnbPb.UpiNumberDetail{
				// user should see processing tag in this case
				UpiNumber: commontypes.GetTextFromStringFontColourFontStyle(upiNumberDetailBe.GetUpiNumber(), processingUpiNumberDetailConf.Title.FontColour,
					(commontypes.FontStyle)(commontypes.FontStyle_value[processingUpiNumberDetailConf.Title.FontStyle])),

				// user should see processing while the upiNumber processing(E.g. Linking) is going
				UpiNumberTag: &feUpiOnbPb.UpiNumberDetail_ProcessingText{
					ProcessingText: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: processingTagInfoConf.Title.DisplayValue,
						},
						BgColor:   processingTagInfoConf.Title.BgColour,
						FontColor: processingTagInfoConf.Title.FontColour,
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: (commontypes.FontStyle)(commontypes.FontStyle_value[processingTagInfoConf.Title.FontStyle]),
						},
					},
				},
				UpiNumberLinkingDetail: commontypes.GetTextFromStringFontColourFontStyle(s.getUpiNumberLinkingDetailTextContent(processingUpiNumberDetailConf.Description[0].DisplayValue, vpa),
					processingUpiNumberDetailConf.Description[0].FontColour,
					(commontypes.FontStyle)(commontypes.FontStyle_value[processingUpiNumberDetailConf.Description[0].FontStyle])),
				UpiNumberType: beToFeUpiNumberTypeMap[upiNumberDetailBe.GetUpiNumberType()],
			}
		case upiNumberDetailBe.GetUpiNumberState() == upiOnbEnumsPb.UpiNumberState_UPI_NUMBER_STATE_ACTIVE:
			activeUpiNumberConf := s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.ActiveUpiNumberDetail
			possibleActionForUpiNumber, err := s.getUpiNumberActions(upiNumberDetailBe)
			if err != nil {
				return nil, fmt.Errorf("error while generating upi number actions for upi-number = %s : %w", upiNumberDetailBe.GetUpiNumber(), err)
			}
			upiNumberDetailFe = &feUpiOnbPb.UpiNumberDetail{
				UpiNumber: commontypes.GetTextFromStringFontColourFontStyle(upiNumberDetailBe.GetUpiNumber(),
					activeUpiNumberConf.Title.FontColour,
					(commontypes.FontStyle)(commontypes.FontStyle_value[activeUpiNumberConf.Title.FontStyle]),
				),
				UpiNumberTag: &feUpiOnbPb.UpiNumberDetail_Copy{
					Copy: &feUpiOnbPb.Copy{
						CopyImageUrl: activeUpiNumberConf.LogoUrls[0],
						ToastText:    activeUpiNumberConf.Description[0].DisplayValue,
						CopyContent:  upiNumberDetailBe.GetUpiNumber(),
					},
				},
				UpiNumberLinkingDetail: commontypes.GetTextFromStringFontColourFontStyle(s.getUpiNumberLinkingDetailTextContent(activeUpiNumberConf.Description[1].DisplayValue, vpa),
					activeUpiNumberConf.Description[1].FontColour,
					(commontypes.FontStyle)(commontypes.FontStyle_value[activeUpiNumberConf.Description[1].FontStyle])),
				// user should  see Deactivate and Delete Number option
				PossibleActionForUpiNumber: possibleActionForUpiNumber,
				UpiNumberType:              beToFeUpiNumberTypeMap[upiNumberDetailBe.GetUpiNumberType()],
			}
		case upiNumberDetailBe.GetUpiNumberState() == upiOnbEnumsPb.UpiNumberState_UPI_NUMBER_STATE_INACTIVE:
			inactiveUpiNumberConf := s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.InactiveUpiNumberDetail
			upiNumberDetailFe = &feUpiOnbPb.UpiNumberDetail{
				UpiNumber: commontypes.GetTextFromStringFontColourFontStyle(upiNumberDetailBe.GetUpiNumber(),
					inactiveUpiNumberConf.Title.FontColour,
					(commontypes.FontStyle)(commontypes.FontStyle_value[inactiveUpiNumberConf.Title.FontStyle])),
				UpiNumberTag: &feUpiOnbPb.UpiNumberDetail_Copy{
					Copy: &feUpiOnbPb.Copy{
						CopyImageUrl: inactiveUpiNumberConf.LogoUrls[0],
						ToastText:    inactiveUpiNumberConf.Description[0].DisplayValue,
						CopyContent:  upiNumberDetailBe.GetUpiNumber(),
					},
				},
				UpiNumberLinkingDetail: commontypes.GetTextFromStringFontColourFontStyle(
					inactiveUpiNumberConf.Description[1].DisplayValue,
					inactiveUpiNumberConf.Description[1].FontColour,
					(commontypes.FontStyle)(commontypes.FontStyle_value[inactiveUpiNumberConf.Description[1].FontStyle])),
				PossibleActionForUpiNumber: s.getActivateUpiNumberButton(activateUpiNumber, feUpiOnbEnumsPb.UpiNumberActionType_UPI_NUMBER_ACTION_TYPE_ACTIVATE_NUMBER),
				UpiNumberType:              beToFeUpiNumberTypeMap[upiNumberDetailBe.GetUpiNumberType()],
			}
		case upiNumberDetailBe.GetUpiNumberState() == upiOnbEnumsPb.UpiNumberState_UPI_NUMBER_STATE_DEREGISTERED:
			deregisteredUpiNumberConf := s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.DeregisteredUpiNumberDetail
			upiNumberDetailFe = &feUpiOnbPb.UpiNumberDetail{
				UpiNumber: commontypes.GetTextFromStringFontColourFontStyle(upiNumberDetailBe.GetUpiNumber(),
					deregisteredUpiNumberConf.Title.FontColour,
					(commontypes.FontStyle)(commontypes.FontStyle_value[deregisteredUpiNumberConf.Title.FontStyle])),
				UpiNumberTag: &feUpiOnbPb.UpiNumberDetail_Copy{
					Copy: &feUpiOnbPb.Copy{
						CopyImageUrl: deregisteredUpiNumberConf.LogoUrls[0],
						ToastText:    deregisteredUpiNumberConf.Description[0].DisplayValue,
						CopyContent:  upiNumberDetailBe.GetUpiNumber(),
					},
				},
				UpiNumberLinkingDetail: commontypes.GetTextFromStringFontColourFontStyle(
					fmt.Sprintf(deregisteredUpiNumberConf.Description[1].DisplayValue, upiNumberDetailBe.GetUpiNumberExpiresAt().AsTime().Format(timeFormat)),
					deregisteredUpiNumberConf.Description[1].FontColour,
					(commontypes.FontStyle)(commontypes.FontStyle_value[deregisteredUpiNumberConf.Description[1].FontStyle])),
				PossibleActionForUpiNumber: s.getActivateUpiNumberButton(reclaimUpiNumber, feUpiOnbEnumsPb.UpiNumberActionType_UPI_NUMBER_ACTION_TYPE_RECLAIM_NUMBER),
				UpiNumberType:              beToFeUpiNumberTypeMap[upiNumberDetailBe.GetUpiNumberType()],
			}
		default:
			return nil, fmt.Errorf("unexpected upi-number-type received, err: %w", rpcPb.StatusAsError(rpcPb.StatusInvalidArgument()))
		}

		upiNumberDetailsFe = append(upiNumberDetailsFe, upiNumberDetailFe)
		// Check if the upiNumberType is phoneNumber then we need not show the phoneNumber with plus icon
		if upiNumberDetailBe.GetUpiNumberType() == upiOnbEnumsPb.UpiNumberType_UPI_NUMBER_TYPE_PHONE_NUMBER {
			phoneNumberFound = true
		}
	}

	// PhoneNumber not found in the list, so we need to add it explicitly
	if !phoneNumberFound {
		phoneNumber, err := s.getPhoneNumberForActor(ctx, actorId)
		if err != nil {
			return nil, err
		}
		upiNumberDetailsFe = append(upiNumberDetailsFe, s.getPhoneNumberNotLinkedInfo(phoneNumber))
	}
	return upiNumberDetailsFe, nil
}

// getActivateUpiNumberButton : allows the user to activate the upi number from inactive or deregistered state.
func (s *Service) getActivateUpiNumberButton(buttonText string, actionType feUpiOnbEnumsPb.UpiNumberActionType) *feUpiOnbPb.UpiNumberDetail_OperationCta {
	return &feUpiOnbPb.UpiNumberDetail_OperationCta{
		OperationCta: &feUpiOnbPb.OperationCta{
			DisplayText: &typesUiPb.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle(buttonText, pkgColors.ColorLightPrimaryAction, commontypes.FontStyle_SUBTITLE_S),
				},
			},
			ActionType: actionType,
		},
	}
}

// getUpiNumberLinkingDetailTextContent generates the upiNumberLinkingDetail string content E.g. `Linked with ykwatra@fifederal`
func (s *Service) getUpiNumberLinkingDetailTextContent(linkedWith, vpa string) string {
	return strings.Join([]string{linkedWith, vpa}, " ")
}

// getUpiNumberActions finds the possible actions for a upiNumber based on its current state
func (s *Service) getUpiNumberActions(upiNumberDetailBe *upiOnbPb.UpiNumberDetail) (*feUpiOnbPb.UpiNumberDetail_UpiNumberActions, error) {
	var (
		upiNumberActions *feUpiOnbPb.UpiNumberDetail_UpiNumberActions
		feActions        []*feUpiOnbPb.UpiNumberAction
	)

	// Deactivate action should be available only for numeric id
	switch upiNumberDetailBe.GetUpiNumberType() {
	case upiOnbEnumsPb.UpiNumberType_UPI_NUMBER_TYPE_PHONE_NUMBER:
		feActions = []*feUpiOnbPb.UpiNumberAction{
			s.getDeleteNumberAction(upiNumberDetailBe.GetUpiNumber(), upiNumberDetailBe.GetUpiNumberType()),
		}
	case upiOnbEnumsPb.UpiNumberType_UPI_NUMBER_TYPE_NUMERIC_ID:
		feActions = []*feUpiOnbPb.UpiNumberAction{
			s.getDeleteNumberAction(upiNumberDetailBe.GetUpiNumber(), upiNumberDetailBe.GetUpiNumberType()),
			s.getDeactivateNumberAction(upiNumberDetailBe.GetUpiNumber()),
		}
	}

	switch upiNumberDetailBe.GetUpiNumberState() {
	case upiOnbEnumsPb.UpiNumberState_UPI_NUMBER_STATE_ACTIVE:
		upiNumberActions = &feUpiOnbPb.UpiNumberDetail_UpiNumberActions{
			UpiNumberActions: &feUpiOnbPb.UpiNumberActions{
				UpiNumberActions: feActions,
			},
		}
	default:
		return nil, fmt.Errorf("upi number received in unexpected state : %s", upiNumberDetailBe.GetUpiNumberState().String())

	}

	return upiNumberActions, nil
}

// getOverflowOptions fetches the overflow options from the upiNumber
// They should only be shown if user has less than 2 upi numbers
func (s *Service) getOverFlowOptions(accountInfo *upiOnbPb.AccountInfoForUpiMapper, vpa string) ([]*feUpiOnbPb.OverflowOption, error) {
	createCustomUpiNumberConf := s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.CreateCustomUpiNumber
	createCustomUpiNumberDeeplink, err := s.getDeeplinkForUpiNumberOverflowActionType(
		feUpiOnbEnumsPb.UpiMapperOverflowOptionsActionType_UPI_MAPPER_OVERFLOW_OPTIONS_CREATE_A_CUSTOM_UPI_NUMBER,
		accountInfo,
		vpa)
	if err != nil {
		return nil, err
	}

	return []*feUpiOnbPb.OverflowOption{
		{
			ActionType: (feUpiOnbEnumsPb.UpiMapperOverflowOptionsActionType)(feUpiOnbEnumsPb.UpiMapperOverflowOptionsActionType_value[createCustomUpiNumberConf.Title.DisplayType]),
			DisplayText: commontypes.GetTextFromStringFontColourFontStyle(
				createCustomUpiNumberConf.Title.DisplayValue,
				createCustomUpiNumberConf.Title.FontColour,
				commontypes.FontStyle(commontypes.FontStyle_value[createCustomUpiNumberConf.Title.FontStyle]),
			),
			Deeplink: createCustomUpiNumberDeeplink,
		},
	}, nil
}

// getFaqInfo is the default faqInfo to be shown to the user
func (s *Service) getFaqInfo() *typesUiPb.IconTextComponent {
	faqInfoConf := s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.DefaultFaqInfo
	return &typesUiPb.IconTextComponent{
		LeftIcon: &commontypes.Image{
			ImageUrl: faqInfoConf.ImageUrl,
			Height:   faqInfoConf.ImageHeight,
			Width:    faqInfoConf.ImageWidth,
		},
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(faqInfoConf.Description[0].DisplayValue,
				faqInfoConf.Description[0].FontColour,
				commontypes.FontStyle(commontypes.FontStyle_value[faqInfoConf.Description[0].FontStyle])),
			commontypes.GetTextFromStringFontColourFontStyle(faqInfoConf.Description[1].DisplayValue,
				faqInfoConf.Description[1].FontColour,
				commontypes.FontStyle(commontypes.FontStyle_value[faqInfoConf.Description[1].FontStyle])),
		},
		LeftImgTxtPadding: faqInfoConf.LeftImgTxtPadding,
		ContainerProperties: &typesUiPb.IconTextComponent_ContainerProperties{
			LeftPadding:   faqInfoConf.ContainerLeftPadding,
			RightPadding:  faqInfoConf.ContainerRightPadding,
			TopPadding:    faqInfoConf.ContainerTopPadding,
			BottomPadding: faqInfoConf.ContainerBottomPadding,
			CornerRadius:  faqInfoConf.ContainerCornerRadius,
			BorderColor:   faqInfoConf.ContainerBorderColour,
			BgColor:       faqInfoConf.ContainerBgColour,
		},
		Deeplink: &deeplinkPb.Deeplink{
			Screen: (deeplinkPb.Screen)(deeplinkPb.Screen_value[faqInfoConf.Deeplink[0].Screen]),
		},
	}
}

// getUpiNumberBeingProcessedFaqInfo generates the faqInfo to be shown to the user
// when a upiNumber (specifically phone Number) is currently being linked
func (s *Service) getUpiNumberBeingProcessedFaqInfo() *typesUiPb.IconTextComponent {
	upiNumberProcessingFaqInfo := s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.UpiNumberBeingProcessedFaqInfo
	return &typesUiPb.IconTextComponent{
		LeftIcon: &commontypes.Image{
			ImageUrl: upiNumberProcessingFaqInfo.ImageUrl,
			Height:   upiNumberProcessingFaqInfo.ImageHeight,
			Width:    upiNumberProcessingFaqInfo.ImageWidth,
		},
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(upiNumberProcessingFaqInfo.Description[0].DisplayValue,
				upiNumberProcessingFaqInfo.Description[0].FontColour,
				commontypes.FontStyle(commontypes.FontStyle_value[upiNumberProcessingFaqInfo.Description[0].FontStyle])),
			commontypes.GetTextFromStringFontColourFontStyle(upiNumberProcessingFaqInfo.Description[1].DisplayValue,
				upiNumberProcessingFaqInfo.Description[1].FontColour,
				commontypes.FontStyle(commontypes.FontStyle_value[upiNumberProcessingFaqInfo.Description[1].FontStyle])),
		},
		LeftImgTxtPadding: upiNumberProcessingFaqInfo.LeftImgTxtPadding,
		ContainerProperties: &typesUiPb.IconTextComponent_ContainerProperties{
			LeftPadding:   upiNumberProcessingFaqInfo.ContainerLeftPadding,
			RightPadding:  upiNumberProcessingFaqInfo.ContainerRightPadding,
			TopPadding:    upiNumberProcessingFaqInfo.ContainerTopPadding,
			BottomPadding: upiNumberProcessingFaqInfo.ContainerBottomPadding,
			CornerRadius:  upiNumberProcessingFaqInfo.ContainerCornerRadius,
			BorderColor:   upiNumberProcessingFaqInfo.ContainerBorderColour,
			BgColor:       upiNumberProcessingFaqInfo.ContainerBgColour,
		},
		Deeplink: &deeplinkPb.Deeplink{
			Screen: (deeplinkPb.Screen)(deeplinkPb.Screen_value[upiNumberProcessingFaqInfo.Deeplink[0].Screen]),
		},
	}
}

// getPrimaryAccountTagInfo generates the PrimaryUpiAccountTag details to be shown to the user
// if the account is Primary
func (s *Service) getPrimaryAccountTagInfo() *commontypes.Text {
	primaryAccountTagInfoConf := s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.PrimaryAccountTagInfo
	return &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: primaryAccountTagInfoConf.Title.DisplayValue,
		},
		BgColor:   primaryAccountTagInfoConf.Title.BgColour,
		FontColor: primaryAccountTagInfoConf.Title.FontColour,
		FontStyle: &commontypes.Text_StandardFontStyle{
			StandardFontStyle: (commontypes.FontStyle)(commontypes.FontStyle_value[primaryAccountTagInfoConf.Title.FontStyle]),
		},
	}
}

// getDeleteNumberAction generates the details for DeleteNumberAction for a
// given upiNumber
func (s *Service) getDeleteNumberAction(upiNumber string, upiNumberType upiOnbEnumsPb.UpiNumberType) *feUpiOnbPb.UpiNumberAction {
	deleteNumberActionConf := lo.Ternary(upiNumberType == upiOnbEnumsPb.UpiNumberType_UPI_NUMBER_TYPE_PHONE_NUMBER, s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.DeleteNumberAction, s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.DeleteCustomUpiNumberAction)
	return &feUpiOnbPb.UpiNumberAction{
		ActionType: (feUpiOnbEnumsPb.UpiNumberActionType)(feUpiOnbEnumsPb.UpiNumberActionType_value[deleteNumberActionConf.ActionType]),
		DisplayText: commontypes.GetTextFromStringFontColourFontStyle(deleteNumberActionConf.Title.DisplayValue,
			deleteNumberActionConf.Title.FontColour, (commontypes.FontStyle)(commontypes.FontStyle_value[deleteNumberActionConf.Title.FontStyle])),
		PreExecutionMessage: &payTypesPb.PreExecutionMessage{
			Icon: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/PayToUpiID.png", 48, 44),
			Title: commontypes.GetTextFromStringFontColourFontStyle(
				deleteNumberActionConf.SelectedActionInfo.Title.DisplayValue,
				deleteNumberActionConf.SelectedActionInfo.Title.FontColour,
				(commontypes.FontStyle)(commontypes.FontStyle_value[deleteNumberActionConf.SelectedActionInfo.Title.FontStyle])),
			Description: commontypes.GetTextFromStringFontColourFontStyle(
				deleteNumberActionConf.SelectedActionInfo.Description[0].DisplayValue+deleteNumberActionConf.SelectedActionInfo.Description[1].DisplayValue+deleteNumberActionConf.SelectedActionInfo.Description[2].DisplayValue,
				deleteNumberActionConf.SelectedActionInfo.Description[0].FontColour,
				(commontypes.FontStyle)(commontypes.FontStyle_value[deleteNumberActionConf.SelectedActionInfo.Description[0].FontStyle])),
			BgColor: "#FFFFFF",
			Ctas: []*deeplinkPb.Cta{
				{
					Type:         deeplinkPb.Cta_DONE,
					DisplayTheme: deeplinkPb.Cta_SECONDARY,
					Text:         "Cancel",
				},
				{
					Type:         deeplinkPb.Cta_CONTINUE,
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Text:         "Delete",
				},
			},
		},
		UpiNumberActionScreenInfo: &feUpiOnbPb.UpiNumberActionScreenInfo{
			Title: commontypes.GetTextFromStringFontColourFontStyle(deleteNumberActionConf.SelectedActionInfo.Title.DisplayValue,
				deleteNumberActionConf.SelectedActionInfo.Title.FontColour,
				(commontypes.FontStyle)(commontypes.FontStyle_value[deleteNumberActionConf.SelectedActionInfo.Title.FontStyle])),
			Description: &typesUiPb.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle(deleteNumberActionConf.SelectedActionInfo.Description[0].DisplayValue,
						deleteNumberActionConf.SelectedActionInfo.Description[0].FontColour,
						(commontypes.FontStyle)(commontypes.FontStyle_value[deleteNumberActionConf.SelectedActionInfo.Description[0].FontStyle])),
					commontypes.GetTextFromStringFontColourFontStyle(upiNumber,
						deleteNumberActionConf.SelectedActionInfo.Description[1].FontColour,
						(commontypes.FontStyle)(commontypes.FontStyle_value[deleteNumberActionConf.SelectedActionInfo.Description[1].FontStyle])),
					commontypes.GetTextFromStringFontColourFontStyle(deleteNumberActionConf.SelectedActionInfo.Description[2].DisplayValue,
						deleteNumberActionConf.SelectedActionInfo.Description[2].FontColour,
						(commontypes.FontStyle)(commontypes.FontStyle_value[deleteNumberActionConf.SelectedActionInfo.Description[2].FontStyle])),
				},
			},
			Ctas: []*deeplinkPb.Cta{
				{
					Type:         (deeplinkPb.Cta_Type)(deeplinkPb.Cta_Type_value[deleteNumberActionConf.SelectedActionInfo.Ctas[0].Type]),
					Text:         deleteNumberActionConf.SelectedActionInfo.Ctas[0].Text,
					DisplayTheme: (deeplinkPb.Cta_DisplayTheme)(deeplinkPb.Cta_DisplayTheme_value[deleteNumberActionConf.SelectedActionInfo.Ctas[0].DisplayTheme]),
				},
				{
					Type:         (deeplinkPb.Cta_Type)(deeplinkPb.Cta_Type_value[deleteNumberActionConf.SelectedActionInfo.Ctas[1].Type]),
					Text:         deleteNumberActionConf.SelectedActionInfo.Ctas[1].Text,
					DisplayTheme: (deeplinkPb.Cta_DisplayTheme)(deeplinkPb.Cta_DisplayTheme_value[deleteNumberActionConf.SelectedActionInfo.Ctas[1].DisplayTheme]),
				},
			},
		},
	}
}

// getDeactivateNumberAction generates the details for DeactivateNumberAction for a
// given upiNumber
func (s *Service) getDeactivateNumberAction(upiNumber string) *feUpiOnbPb.UpiNumberAction {
	deactivateNumberActionConf := s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.DeactivateNumberAction
	return &feUpiOnbPb.UpiNumberAction{
		ActionType: (feUpiOnbEnumsPb.UpiNumberActionType)(feUpiOnbEnumsPb.UpiNumberActionType_value[deactivateNumberActionConf.ActionType]),
		DisplayText: commontypes.GetTextFromStringFontColourFontStyle(deactivateNumberActionConf.Title.DisplayValue,
			deactivateNumberActionConf.Title.FontColour, (commontypes.FontStyle)(commontypes.FontStyle_value[deactivateNumberActionConf.Title.FontStyle])),
		PreExecutionMessage: &payTypesPb.PreExecutionMessage{
			Icon: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/PayToUpiID.png", 48, 44),
			Title: commontypes.GetTextFromStringFontColourFontStyle(
				deactivateNumberActionConf.SelectedActionInfo.Title.DisplayValue,
				deactivateNumberActionConf.SelectedActionInfo.Title.FontColour,
				(commontypes.FontStyle)(commontypes.FontStyle_value[deactivateNumberActionConf.SelectedActionInfo.Title.FontStyle])),
			Description: commontypes.GetTextFromStringFontColourFontStyle(
				deactivateNumberActionConf.SelectedActionInfo.Description[0].DisplayValue,
				deactivateNumberActionConf.SelectedActionInfo.Description[0].FontColour,
				(commontypes.FontStyle)(commontypes.FontStyle_value[deactivateNumberActionConf.SelectedActionInfo.Description[0].FontStyle])),
			BgColor: "#FFFFFF",
			Ctas: []*deeplinkPb.Cta{
				{
					Type:         deeplinkPb.Cta_DONE,
					DisplayTheme: deeplinkPb.Cta_SECONDARY,
					Text:         "Cancel",
				},
				{
					Type:         deeplinkPb.Cta_CONTINUE,
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Text:         "Deactivate",
				},
			},
		},
		UpiNumberActionScreenInfo: &feUpiOnbPb.UpiNumberActionScreenInfo{
			Title: commontypes.GetTextFromStringFontColourFontStyle(deactivateNumberActionConf.SelectedActionInfo.Title.DisplayValue,
				deactivateNumberActionConf.SelectedActionInfo.Title.FontColour,
				(commontypes.FontStyle)(commontypes.FontStyle_value[deactivateNumberActionConf.SelectedActionInfo.Title.FontStyle])),
			Description: &typesUiPb.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle(deactivateNumberActionConf.SelectedActionInfo.Description[0].DisplayValue,
						deactivateNumberActionConf.SelectedActionInfo.Description[0].FontColour,
						(commontypes.FontStyle)(commontypes.FontStyle_value[deactivateNumberActionConf.SelectedActionInfo.Description[0].FontStyle])),
					commontypes.GetTextFromStringFontColourFontStyle(upiNumber,
						deactivateNumberActionConf.SelectedActionInfo.Description[1].FontColour,
						(commontypes.FontStyle)(commontypes.FontStyle_value[deactivateNumberActionConf.SelectedActionInfo.Description[1].FontStyle])),
					commontypes.GetTextFromStringFontColourFontStyle(deactivateNumberActionConf.SelectedActionInfo.Description[2].DisplayValue,
						deactivateNumberActionConf.SelectedActionInfo.Description[2].FontColour,
						(commontypes.FontStyle)(commontypes.FontStyle_value[deactivateNumberActionConf.SelectedActionInfo.Description[2].FontStyle])),
				},
			},
			Ctas: []*deeplinkPb.Cta{
				{
					Type:         (deeplinkPb.Cta_Type)(deeplinkPb.Cta_Type_value[deactivateNumberActionConf.SelectedActionInfo.Ctas[0].Type]),
					Text:         deactivateNumberActionConf.SelectedActionInfo.Ctas[0].Text,
					DisplayTheme: (deeplinkPb.Cta_DisplayTheme)(deeplinkPb.Cta_DisplayTheme_value[deactivateNumberActionConf.SelectedActionInfo.Ctas[0].DisplayTheme]),
				},
				{
					Type:         (deeplinkPb.Cta_Type)(deeplinkPb.Cta_Type_value[deactivateNumberActionConf.SelectedActionInfo.Ctas[1].Type]),
					Text:         deactivateNumberActionConf.SelectedActionInfo.Ctas[1].Text,
					DisplayTheme: (deeplinkPb.Cta_DisplayTheme)(deeplinkPb.Cta_DisplayTheme_value[deactivateNumberActionConf.SelectedActionInfo.Ctas[1].DisplayTheme]),
				},
			},
		},
	}
}

// getActivateNumberAction generates the details for ActivateNumberAction
func (s *Service) getActivateNumberAction() *feUpiOnbPb.UpiNumberAction {
	activateNumberActionConf := s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.ActivateNumberAction
	return &feUpiOnbPb.UpiNumberAction{
		ActionType: (feUpiOnbEnumsPb.UpiNumberActionType)(feUpiOnbEnumsPb.UpiNumberActionType_value[activateNumberActionConf.ActionType]),
		DisplayText: commontypes.GetTextFromStringFontColourFontStyle(activateNumberActionConf.Title.DisplayValue,
			activateNumberActionConf.Title.FontColour,
			(commontypes.FontStyle)(commontypes.FontStyle_value[activateNumberActionConf.Title.FontStyle]),
		),
		PreExecutionMessage: &payTypesPb.PreExecutionMessage{
			Icon: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/PayToUpiID.png", 48, 44),
			Title: commontypes.GetTextFromStringFontColourFontStyle(
				activateNumberActionConf.SelectedActionInfo.Title.DisplayValue,
				activateNumberActionConf.SelectedActionInfo.Title.FontColour,
				(commontypes.FontStyle)(commontypes.FontStyle_value[activateNumberActionConf.SelectedActionInfo.Title.FontStyle])),
			Description: commontypes.GetTextFromStringFontColourFontStyle(
				activateNumberActionConf.SelectedActionInfo.Description[0].DisplayValue,
				activateNumberActionConf.SelectedActionInfo.Description[0].FontColour,
				(commontypes.FontStyle)(commontypes.FontStyle_value[activateNumberActionConf.SelectedActionInfo.Description[0].FontStyle])),
			BgColor: "#FFFFFF",
			Ctas: []*deeplinkPb.Cta{
				{
					Type:         deeplinkPb.Cta_DONE,
					DisplayTheme: deeplinkPb.Cta_SECONDARY,
					Text:         "Cancel",
				},
				{
					Type:         deeplinkPb.Cta_CONTINUE,
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Text:         "Activate",
				},
			},
		},
	}
}

// getPhoneNumberNotLinkedInfo generates the info asking user to link their phoneNumber as upiNumber
func (s *Service) getPhoneNumberNotLinkedInfo(phoneNumber *commontypes.PhoneNumber) *feUpiOnbPb.UpiNumberDetail {
	phoneNumberNotLinkingConf := s.conf.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.PhoneNumberNotLinkedDetail
	return &feUpiOnbPb.UpiNumberDetail{
		UpiNumber: commontypes.GetTextFromStringFontColourFontStyle(strconv.FormatUint(phoneNumber.GetNationalNumber(), 10),
			phoneNumberNotLinkingConf.Title.FontColour,
			(commontypes.FontStyle)(commontypes.FontStyle_value[phoneNumberNotLinkingConf.Title.FontStyle])),
		UpiNumberLinkingDetail: commontypes.GetTextFromStringFontColourFontStyle(phoneNumberNotLinkingConf.Description[0].DisplayValue,
			phoneNumberNotLinkingConf.Description[0].FontColour,
			(commontypes.FontStyle)(commontypes.FontStyle_value[phoneNumberNotLinkingConf.Description[0].FontStyle]),
		),
		PossibleActionForUpiNumber: &feUpiOnbPb.UpiNumberDetail_LinkPhoneNumberOption{
			LinkPhoneNumberOption: &feUpiOnbPb.LinkPhoneNumberOption{
				LinkPhoneNumberIcon: phoneNumberNotLinkingConf.LogoUrls[0],
			},
		},
	}
}
