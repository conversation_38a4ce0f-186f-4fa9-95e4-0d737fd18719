package contact_us

import (
	"flag"
	"log"
	"os"
	"runtime"
	"testing"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
)

var (
	conf          *config.Config
	generatedConf *genconf.Config
	_, b, _, _    = runtime.Caller(0)
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	teardown := func() {
		_ = logger.Log.Sync()
	}

	// load config
	var err error
	conf, err = config.Load()
	if err != nil {
		log.Fatal("failed to load config for get activities test", err)
	}

	generatedConf, err = genconf.Load()
	if err != nil {
		log.Fatal("failed to load dynamic config for get activities test", err)
	}

	// Setup logger
	logger.Init(conf.Application.Environment)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
