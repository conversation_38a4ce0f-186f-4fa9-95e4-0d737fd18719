package bank_customer

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/alfred"
	bcPb "github.com/epifi/gamma/api/bankcust"
	compliancePb "github.com/epifi/gamma/api/bankcust/compliance"
	consentPb "github.com/epifi/gamma/api/consent"
	feBcPb "github.com/epifi/gamma/api/frontend/bank_customer"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/deeplink/definitions"
	"github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/frontend/config/genconf"
	feErrors "github.com/epifi/gamma/pkg/frontend/errors"
)

type Service struct {
	feBcPb.UnimplementedBankCustomerServer
	genConf            *genconf.Config
	bankCustomerClient bcPb.BankCustomerServiceClient
	alfredClient       alfred.AlfredClient
	consentClient      consentPb.ConsentClient
	timeClient         datetime.Time
	compClient         compliancePb.ComplianceClient
}

type ErrorOptions struct {
	Title     string
	Subtitle  string
	Ctas      []*errors.CTA
	ErrorCode string
	Image     string
}

var (
	getFailureRespHeaders = func(ctx context.Context, orderChequebookRespStatus bcPb.OrderChequebookResponse_Status) (*header.ResponseHeader, bool) {
		switch orderChequebookRespStatus {
		case bcPb.OrderChequebookResponse_NOT_FOUND, bcPb.OrderChequebookResponse_INVALID_SENDER_DETAILS, bcPb.OrderChequebookResponse_DUPLICATE_REQUEST_ID:
			return feErrors.RespHeaderFromClientErr(ctx, feErrors.ORDER_CHEQUEBOOK_GENERIC_ERROR), true
		case bcPb.OrderChequebookResponse_TRANSACTION_LIMIT_EXCEEDED:
			return feErrors.RespHeaderFromClientErr(ctx, feErrors.ORDER_CHEQUEBOOK_TRANSACTION_LIMIT_EXCEEDED), true
		case bcPb.OrderChequebookResponse_MINIMUM_BALANCE:
			return feErrors.RespHeaderFromClientErr(ctx, feErrors.ORDER_CHEQUEBOOK_MINIMUM_BALANCE), true
		case bcPb.OrderChequebookResponse_NO_SIGNATURE:
			return feErrors.RespHeaderFromClientErr(ctx, feErrors.ORDER_CHEQUEBOOK_NO_SIGNATURE), true
		case bcPb.OrderChequebookResponse_EXCEEDED_MAX_CHEQUEBOOK_LEAF:
			return feErrors.RespHeaderFromClientErr(ctx, feErrors.ORDER_CHEQUEBOOK_EXCEEDED_MAX_CHEQUEBOOK_LEAF), true
		case bcPb.OrderChequebookResponse_INVALID_PIN:
			return feErrors.RespHeaderFromClientErr(ctx, feErrors.ORDER_CHEQUEBOOK_INVALID_PIN), true
		case bcPb.OrderChequebookResponse_NUMBER_OF_PIN_TRIES_EXCEEDED:
			return feErrors.RespHeaderFromClientErr(ctx, feErrors.ORDER_CHEQUEBOOK_NUMBER_OF_PIN_TRIES_EXCEEDED), true
		case bcPb.OrderChequebookResponse_STATE_CODE_ERROR:
			return feErrors.RespHeaderFromClientErr(ctx, feErrors.ORDER_CHEQUEBOOK_STATE_CODE_ERROR), true
		case bcPb.OrderChequebookResponse_PIN_CODE_ERROR:
			return feErrors.RespHeaderFromClientErr(ctx, feErrors.ORDER_CHEQUEBOOK_PIN_CODE_ERROR), true
		case bcPb.OrderChequebookResponse_ADDRESS_ERROR:
			return feErrors.RespHeaderFromClientErr(ctx, feErrors.ORDER_CHEQUEBOOK_ADDRESS_ERROR), true
		case bcPb.OrderChequebookResponse_COUNTRY_CODE_ERROR:
			return feErrors.RespHeaderFromClientErr(ctx, feErrors.ORDER_CHEQUEBOOK_COUNTRY_CODE_ERROR), true
		case bcPb.OrderChequebookResponse_GENERAL_ERROR:
			return feErrors.RespHeaderFromClientErr(ctx, feErrors.ORDER_CHEQUEBOOK_GENERAL_ERROR), true
		case bcPb.OrderChequebookResponse_TRANSACTION_PIN_NOT_SET:
			return feErrors.RespHeaderFromClientErr(ctx, feErrors.ORDER_CHEQUEBOOK_TRANSACTION_PIN_NOT_SET), true
		case bcPb.OrderChequebookResponse_MINIMUM_AMB_ERROR:
			return feErrors.RespHeaderFromClientErr(ctx, feErrors.ORDER_CHEQUEBOOK_MINIMUM_AMB_ERROR), true

		default:
			return nil, false
		}
	}

	errorOption = ErrorOptions{
		Title:    "Uh-Oh Request unsuccessful",
		Subtitle: "We could not process this request. Please, retry later.",
		Ctas: []*errors.CTA{
			{
				Type:         errors.CTA_RETRY,
				Text:         "Ok, Got it",
				DisplayTheme: errors.CTA_PRIMARY,
			},
		},
		Image: "https://epifi-icons.pointz.in/chequebook/offline.png",
	}

	timeoutErrorOption = ErrorOptions{
		Title:    "Processing your chequebook request is taking time",
		Subtitle: "Our partner is working on processing your chequebook request. This might take up to 6 hours. Please check for status at 6:30 PM",
		Ctas: []*errors.CTA{
			{
				Type:         errors.CTA_RETRY,
				Text:         "Ok, Got it",
				DisplayTheme: errors.CTA_PRIMARY,
			},
		},
		Image: "https://epifi-icons.pointz.in/chequebook/polling.png",
	}
)

func NewService(genConf *genconf.Config, bankCustomerClient bcPb.BankCustomerServiceClient, alfredClient alfred.AlfredClient, consentClient consentPb.ConsentClient,
	timeClient datetime.Time, compClient compliancePb.ComplianceClient) *Service {
	return &Service{
		genConf:            genConf,
		bankCustomerClient: bankCustomerClient,
		alfredClient:       alfredClient,
		consentClient:      consentClient,
		compClient:         compClient,
		timeClient:         timeClient,
	}
}

func (s *Service) OrderChequebook(ctx context.Context, request *feBcPb.OrderChequebookRequest) (*feBcPb.OrderChequebookResponse, error) {
	// create context independent of client context, this will avoid context cancelling of rpc in case user closes the app
	var cancelCtx func()
	ctx, cancelCtx = context.WithDeadline(epificontext.CloneCtx(ctx), time.Now().Add(90*time.Second))
	defer cancelCtx()

	var (
		actorId            = request.GetReq().GetAuth().GetActorId()
		credBlock          = request.GetCredBlock()
		credBlockRequestId = request.GetCredBlockRequestId()
		requestId          = request.GetRequestId()
	)

	if actorId == "" || credBlock == "" || credBlockRequestId == "" || requestId == "" {
		logger.Error(ctx, "actorId or credBlock or credBlockRequestId or requestId are missing from client side")
		return &feBcPb.OrderChequebookResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInvalidArgumentWithDebugMsg("actorId or credBlock or credBlockRequestId or requestId are missing from client side"),
				ErrorView: errorView(errorOption),
			},
		}, nil
	}

	// initiate request for chequebook
	orderChequebookResp, err := s.bankCustomerClient.OrderChequebook(ctx, &bcPb.OrderChequebookRequest{
		ActorId:            actorId,
		CredBlock:          credBlock,
		CredBlockRequestId: credBlockRequestId,
		RequestId:          requestId,
	})
	if err = epifigrpc.RPCError(orderChequebookResp, err); err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to order chequebook for alfred request %s", request.GetRequestId()), zap.Error(err))
		return failureResponse(ctx, orderChequebookResp)
	}

	// once request is submitted successfully, returning polling screen
	return &feBcPb.OrderChequebookResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NextAction: &deeplink.Deeplink{
			Screen: deeplink.Screen_STATUS_POLLING,
			ScreenOptions: &deeplink.Deeplink_StatusPollScreenOptions{
				StatusPollScreenOptions: &deeplink.StatusPollScreenOptions{
					PollingTarget: definitions.PollingTarget_POLLING_TARGET_ALFRED_REQUEST,
					ImgUrl:        "https://epifi-icons.pointz.in/chequebook/polling.png",
					Title:         "Submitting request",
					Subtitle:      "This will take a few moments ⏱",
					RequestPayloadOptions: &deeplink.StatusPollScreenOptions_AlfredRequestPayloadOptions{
						AlfredRequestPayloadOptions: &definitions.AlfredRequestPayloadOptions{
							RequestId: request.GetRequestId(),
						},
					},
				},
			},
		},
	}, nil
}

func (s *Service) GetDigitalCancelledCheque(ctx context.Context, req *feBcPb.GetDigitalCancelledChequeRequest) (*feBcPb.GetDigitalCancelledChequeResponse, error) {
	cheqRes, err := s.bankCustomerClient.GetDigitalCancelledCheque(ctx, &bcPb.GetDigitalCancelledChequeRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
	})
	if grpcErr := epifigrpc.RPCError(cheqRes, err); grpcErr != nil {
		logger.Error(ctx, "error fetching digital cancelled cheque from BE", zap.Error(grpcErr))
		return &feBcPb.GetDigitalCancelledChequeResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error fetching digital cancelled cheque from BE, err: %v", grpcErr)),
			},
		}, nil
	}

	return &feBcPb.GetDigitalCancelledChequeResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Cheque: cheqRes.GetCheque(),
	}, nil
}

func errorView(errorOptions ErrorOptions) *errors.ErrorView {
	return &errors.ErrorView{
		Type: errors.ErrorViewType_FULL_SCREEN,
		Options: &errors.ErrorView_FullScreenErrorView{FullScreenErrorView: &errors.FullScreenErrorView{
			ErrorCode: errorOptions.ErrorCode,
			Title:     errorOptions.Title,
			Subtitle:  errorOptions.Subtitle,
			Ctas:      errorOptions.Ctas,
			ImageUrl:  errorOptions.Image,
		}},
	}
}

//nolint:unparam,gosec
func failureResponse(ctx context.Context, orderChequebookResp *bcPb.OrderChequebookResponse) (*feBcPb.OrderChequebookResponse, error) {
	responseWithStatus := func(status *rpc.Status, errorOptions ErrorOptions) *feBcPb.OrderChequebookResponse {
		return &feBcPb.OrderChequebookResponse{
			RespHeader: &header.ResponseHeader{
				Status:    status,
				ErrorView: errorView(errorOptions),
			},
		}
	}

	if orderChequebookResp.GetStatus().IsInvalidArgument() ||
		orderChequebookResp.GetStatus().IsInternal() {
		return responseWithStatus(orderChequebookResp.GetStatus(), errorOption), nil
	}
	if orderChequebookResp.GetStatus().IsCancelled() ||
		orderChequebookResp.GetStatus().IsDeadlineExceeded() {
		return responseWithStatus(orderChequebookResp.GetStatus(), timeoutErrorOption), nil
	}
	failureResp, exist := getFailureRespHeaders(ctx, bcPb.OrderChequebookResponse_Status(orderChequebookResp.GetStatus().GetCode()))
	if exist {
		return &feBcPb.OrderChequebookResponse{
			RespHeader: failureResp,
		}, nil
	}
	return responseWithStatus(rpc.StatusUnknown(), errorOption), nil
}

func (s *Service) PeriodicKYCCallback(ctx context.Context, req *feBcPb.PeriodicKYCCallbackRequest) (*feBcPb.PeriodicKYCCallbackResponse, error) {
	// create context independent of client context, this will avoid context cancelling of rpc in case user closes the app
	// Increasing timeout as this API calls profile update which takes more time than standard default timeout set in interceptor
	var cancelCtx func()
	ctx, cancelCtx = context.WithDeadline(epificontext.CloneCtx(ctx), time.Now().Add(90*time.Second))
	defer cancelCtx()
	compResp, err := s.compClient.PeriodicKYCCallback(ctx, &compliancePb.PeriodicKYCCallbackRequest{
		ActorId:     req.GetReq().GetAuth().GetActorId(),
		ClientReqId: req.GetClientReqId(),
		Action:      req.GetAction(),
	})
	if rpcErr := epifigrpc.RPCError(compResp, err); rpcErr != nil {
		if rpc.StatusFromError(rpcErr).IsRecordNotFound() {
			return &feBcPb.PeriodicKYCCallbackResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusRecordNotFound(),
				},
			}, nil
		}
		logger.Error(ctx, "error in sending the callback for periodic kyc", zap.Error(err))
		return &feBcPb.PeriodicKYCCallbackResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
			},
		}, nil
	}
	return &feBcPb.PeriodicKYCCallbackResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NextAction: compResp.GetNextAction(),
	}, nil
}
