package mutualfund

import (
	"context"
	"fmt"
	"testing"
	"time"

	onbScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	onbPb "github.com/epifi/gamma/api/user/onboarding"

	analyserTypesPb "github.com/epifi/gamma/api/typesv2/analyser"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	uiWidget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/feature/release"

	insightsPkg "github.com/epifi/gamma/insights/pkg"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	fePb "github.com/epifi/gamma/api/frontend/investment/mutualfund"
	mfexternalpb "github.com/epifi/gamma/api/investment/mutualfund/external"
	mutualFundExtScreenOptPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/investment/mutualfund/external"
	typesUiPb "github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	wealthonboardingPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

var (
	actorId    = "actorId"
	phoneNo    = &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}
	screenOpt2 = &mutualFundExtScreenOptPb.MFHoldingsImportSubmitOtpScreenOptions{
		OtpRetryTimerSeconds: 30,
		ExternalId:           "externalId",
		Title:                commontypes.GetTextFromStringFontColourFontStyle("Verify with OTP", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L),
		Description:          commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("Waiting to automatically detect the OTP sent to %s via MF Central", phoneNo.ToSignedString()), colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_BODY_S),
		BackgroundColour:     uiWidget.GetBlockBackgroundColour(colors.ColorDarkBase),
		Identifier: &mutualFundExtScreenOptPb.MFHoldingsImportSubmitOtpScreenOptions_PhoneNumber{
			PhoneNumber: phoneNo,
		},
	}
	nextScreen1 = &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_MF_HOLDINGS_IMPORT_OTP_SUBMISSION_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(screenOpt2),
	}
	tracker1 = &mfexternalpb.GetMFHoldingsImportRequestResponse{
		Status: rpcPb.StatusOk(),
		MfHoldingsImportRequestTracker: &mfexternalpb.MFHoldingsImportRequestTracker{
			Id:                    "id1",
			ActorId:               "actorId",
			ClientReferenceNumber: "clientRefNo",
			State:                 mfexternalpb.State_CREATE_TRANSACTION_SUCCESSFUL,
			FailureReason:         mfexternalpb.FailureReason_FAILURE_REASON_UNSPECIFIED,
			VendorData:            &mfexternalpb.VendorData{},
			ExternalId:            "externalId",
			MetaData: &mfexternalpb.MFHoldingsImportRequestMetaData{
				PhoneNumber: phoneNo,
				OtpRequests: map[string]*mfexternalpb.OtpRequest{
					"clientRefNo1": {
						OtpMedium: &mfexternalpb.OtpMedium{
							OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
							Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
						},
					},
				},
			},
			Provenance: mfexternalpb.Provenance_PROVENANCE_NET_WORTH_REFRESH,
			FlowType:   mfexternalpb.FlowType_FLOW_TYPE_MF_HOLDINGS_REFRESH,
		},
	}
	tracker2 = &mfexternalpb.GetMFHoldingsImportRequestResponse{
		Status: rpcPb.StatusOk(),
		MfHoldingsImportRequestTracker: &mfexternalpb.MFHoldingsImportRequestTracker{
			Id:                    "id1",
			ActorId:               "actorId",
			ClientReferenceNumber: "clientRefNo",
			State:                 mfexternalpb.State_CREATE_TRANSACTION_SUCCESSFUL,
			FailureReason:         mfexternalpb.FailureReason_FAILURE_REASON_UNSPECIFIED,
			VendorData:            &mfexternalpb.VendorData{},
			ExternalId:            "externalId",
			MetaData: &mfexternalpb.MFHoldingsImportRequestMetaData{
				PhoneNumber:             phoneNo,
				OtpVerificationAttempts: 1,
				OtpRequests: map[string]*mfexternalpb.OtpRequest{
					"clientRefNo1": {
						OtpMedium: &mfexternalpb.OtpMedium{
							OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
							Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
						},
					},
					"clientRefNo2": {
						OtpMedium: &mfexternalpb.OtpMedium{
							OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
							Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
						},
					},
				},
			},
			Provenance: mfexternalpb.Provenance_PROVENANCE_NET_WORTH_REFRESH,
			FlowType:   mfexternalpb.FlowType_FLOW_TYPE_MF_HOLDINGS_REFRESH,
		},
	}
	tracker3 = &mfexternalpb.GetMFHoldingsImportRequestResponse{
		Status: rpcPb.StatusOk(),
		MfHoldingsImportRequestTracker: &mfexternalpb.MFHoldingsImportRequestTracker{
			Id:                    "id1",
			ActorId:               "actorId",
			ClientReferenceNumber: "clientRefNo",
			State:                 mfexternalpb.State_CREATE_TRANSACTION_SUCCESSFUL,
			FailureReason:         mfexternalpb.FailureReason_FAILURE_REASON_UNSPECIFIED,
			VendorData:            &mfexternalpb.VendorData{},
			ExternalId:            "externalId",
			MetaData: &mfexternalpb.MFHoldingsImportRequestMetaData{
				PhoneNumber: phoneNo,
				OtpRequests: map[string]*mfexternalpb.OtpRequest{
					"clientRefNo1": {
						OtpMedium: &mfexternalpb.OtpMedium{
							OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
							Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
						},
					},
				},
			},
			Provenance: mfexternalpb.Provenance_PROVENANCE_WEALTH_BUILDER_ONBOARDING,
			FlowType:   mfexternalpb.FlowType_FLOW_TYPE_UNSPECIFIED,
		},
	}
	phNumberInString = phoneNo.ToStringInMobileFormat()
)

func TestService_GenerateImportMFHoldingsOTPV2(t *testing.T) {
	t.Parallel()
	actorId := "actorId"
	reqHeader := &headerPb.RequestHeader{Auth: &headerPb.AuthHeader{ActorId: "actorId"}}
	panDetails := &insightsPkg.GetVerifiedOrUnverifiedPanResponse{
		Pan:        "**********",
		IsVerified: true,
	}
	logger.Init(cfg.TestEnv)
	sv := Service{}
	tests := []struct {
		name    string
		req     *fePb.GenerateImportMFHoldingsOTPV2Request
		before  func(m *mockFields)
		want    *fePb.GenerateImportMFHoldingsOTPV2Response
		wantErr bool
	}{
		{
			name: "Successfully generate otp for a phone number",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "ext1",
				Identifier: &fePb.GenerateImportMFHoldingsOTPV2Request_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999}},
			},
			before: func(m *mockFields) {
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(false, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(false, nil)
				m.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), actorId).Return(panDetails, nil)
				m.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "ext1",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
						Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999}},
					},
					Pan: "**********",
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{Status: rpcPb.StatusOk()}, nil)
				m.mfExternalOrdersClient.EXPECT().GetMFHoldingsImportRequest(gomock.Any(), &mfexternalpb.GetMFHoldingsImportRequestRequest{
					Identifier: &mfexternalpb.GetMFHoldingsImportRequestRequest_ExternalId{ExternalId: "ext1"},
				}).Return(&mfexternalpb.GetMFHoldingsImportRequestResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
				ExternalId: "ext1",
				NextScreen: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_MF_HOLDINGS_IMPORT_OTP_SUBMISSION_SCREEN,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&mutualFundExtScreenOptPb.MFHoldingsImportSubmitOtpScreenOptions{
						ExternalId:           "ext1",
						Title:                commontypes.GetTextFromStringFontColourFontStyle("Verify with OTP", colors.ColorNight, commontypes.FontStyle_HEADLINE_L),
						Description:          commontypes.GetTextFromStringFontColourFontStyle("Waiting to automatically detect the OTP sent to +919999999999 via MF Central", colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_S),
						OtpRetryTimerSeconds: 30,
						BackgroundColour:     uiWidget.GetBlockBackgroundColour(colors.ColorSnow),
						Identifier: &mutualFundExtScreenOptPb.MFHoldingsImportSubmitOtpScreenOptions_PhoneNumber{
							PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999},
						},
					}),
				},
			},
			wantErr: false,
		},
		{
			name: "Invalid pan mobile status while generating otp for a phone number should return error info screen",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "ext1",
				Identifier: &fePb.GenerateImportMFHoldingsOTPV2Request_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999}},
			},
			before: func(m *mockFields) {
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(false, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(false, nil)
				m.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), actorId).Return(panDetails, nil)
				m.mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any())
				m.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "ext1",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
						Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999}},
					},
					Pan: "**********",
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{Status: &rpcPb.Status{Code: uint32(mfexternalpb.InitiateHoldingsImportResponse_INVALID_PAN_MOBILE_COMBINATION)}}, nil)
				m.mfExternalOrdersClient.EXPECT().GetMFHoldingsImportRequest(gomock.Any(), &mfexternalpb.GetMFHoldingsImportRequestRequest{
					Identifier: &mfexternalpb.GetMFHoldingsImportRequestRequest_ExternalId{ExternalId: "ext1"},
				}).Return(&mfexternalpb.GetMFHoldingsImportRequestResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: &rpcPb.Status{Code: uint32(fePb.GenerateImportMFHoldingsOTPV2Response_INVALID_PAN_MOBILE_COMBINATION)}},
				ExternalId: "ext1",
				ErrorInfo:  sv.prepareInvalidPhoneNumberInfoScreen(&commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999}, "ext1", panDetails, lightThemeParams),
			},
		},
		{
			name: "Successfully generate otp for an email",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "ext1",
				Identifier: &fePb.GenerateImportMFHoldingsOTPV2Request_Email{Email: "<EMAIL>"},
			},
			before: func(m *mockFields) {
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(false, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(false, nil)
				m.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), actorId).Return(panDetails, nil)
				m.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "ext1",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_EMAIL,
						Identifier:    &mfexternalpb.OtpMedium_Email{Email: "<EMAIL>"},
					},
					Pan: "**********",
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{Status: rpcPb.StatusOk()}, nil)
				m.mfExternalOrdersClient.EXPECT().GetMFHoldingsImportRequest(gomock.Any(), &mfexternalpb.GetMFHoldingsImportRequestRequest{
					Identifier: &mfexternalpb.GetMFHoldingsImportRequestRequest_ExternalId{ExternalId: "ext1"},
				}).Return(&mfexternalpb.GetMFHoldingsImportRequestResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
				ExternalId: "ext1",
				NextScreen: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_MF_HOLDINGS_IMPORT_OTP_SUBMISSION_SCREEN,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&mutualFundExtScreenOptPb.MFHoldingsImportSubmitOtpScreenOptions{
						ExternalId:           "ext1",
						Title:                commontypes.GetTextFromStringFontColourFontStyle("Verify with OTP", colors.ColorNight, commontypes.FontStyle_HEADLINE_L),
						Description:          commontypes.GetTextFromStringFontColourFontStyle("Enter the OTP <NAME_EMAIL> via MF Central", colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_S),
						OtpRetryTimerSeconds: 30,
						BackgroundColour:     uiWidget.GetBlockBackgroundColour(colors.ColorSnow),
						Identifier: &mutualFundExtScreenOptPb.MFHoldingsImportSubmitOtpScreenOptions_Email{
							Email: "<EMAIL>",
						},
					}),
				},
			},
		},
		{
			name: "Invalid pan email status while generating otp for an email should return error info screen",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "ext1",
				Identifier: &fePb.GenerateImportMFHoldingsOTPV2Request_Email{Email: "<EMAIL>"},
			},
			before: func(m *mockFields) {
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(false, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(false, nil)
				m.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), actorId).Return(panDetails, nil)
				m.mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any())
				m.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "ext1",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_EMAIL,
						Identifier:    &mfexternalpb.OtpMedium_Email{Email: "<EMAIL>"},
					},
					Pan: "**********",
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{Status: &rpcPb.Status{Code: uint32(mfexternalpb.InitiateHoldingsImportResponse_INVALID_PAN_EMAIL_COMBINATION)}}, nil)
				m.mfExternalOrdersClient.EXPECT().GetMFHoldingsImportRequest(gomock.Any(), &mfexternalpb.GetMFHoldingsImportRequestRequest{
					Identifier: &mfexternalpb.GetMFHoldingsImportRequestRequest_ExternalId{ExternalId: "ext1"},
				}).Return(&mfexternalpb.GetMFHoldingsImportRequestResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: &rpcPb.Status{Code: uint32(fePb.GenerateImportMFHoldingsOTPV2Response_INVALID_PAN_EMAIL_COMBINATION)}},
				ExternalId: "ext1",
				ErrorInfo:  sv.prepareInvalidEmailInfoScreen("ext1", "<EMAIL>", panDetails, lightThemeParams),
			},
		},
		{
			name: "Successfully generate otp using phone number used in previous request",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "ext1",
			},
			before: func(m *mockFields) {
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(false, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(false, nil)
				m.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), actorId).Return(panDetails, nil)
				m.datetime.EXPECT().Now().Return(time.Date(2023, 7, 20, 0, 0, 0, 0, datetime.IST))
				m.wealthOnbClient.EXPECT().GetOrCreateUser(gomock.Any(), &wealthonboardingPb.GetOrCreateUserRequest{
					ActorId: "actorId",
				}).Return(&wealthonboardingPb.GetOrCreateUserResponse{
					Status: rpcPb.StatusOk(),
					UserDetails: &wealthonboardingPb.GetOrCreateUserResponse_UserDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						Email: "<EMAIL>",
					},
				}, nil)
				m.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "ext1",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
						Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999}},
					},
					Pan: "**********",
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{Status: rpcPb.StatusOk()}, nil)

				m.mfExternalOrdersClient.EXPECT().GetHoldingsImportRequestsInDateRange(gomock.Any(), &mfexternalpb.GetHoldingsImportRequestsInDateRangeRequest{
					ActorId:  "actorId",
					FromTime: timestamppb.New(time.Date(2022, 7, 20, 0, 0, 0, 0, datetime.IST)),
					ToTime:   timestamppb.New(time.Date(2023, 7, 20, 0, 0, 0, 0, datetime.IST)),
				}).Return(&mfexternalpb.GetHoldingsImportRequestsInDateRangeResponse{
					Status: rpcPb.StatusOk(),
					HoldingsImportRequests: []*mfexternalpb.MFHoldingsImportRequestTracker{
						{
							Id:                    "id1",
							ActorId:               "actorId",
							ClientReferenceNumber: "clientRefNo",
							CreatedAt:             timestamppb.New(time.Date(2023, 4, 20, 0, 0, 0, 0, datetime.IST)),
							MetaData: &mfexternalpb.MFHoldingsImportRequestMetaData{
								OtpVerificationAttempts: 1,
								OtpRequests: map[string]*mfexternalpb.OtpRequest{
									"clientRefNo": {
										OtpMedium: &mfexternalpb.OtpMedium{
											OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
											Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999}},
										},
									},
								},
							},
						},
					},
				}, nil)
				m.mfExternalOrdersClient.EXPECT().GetMFHoldingsImportRequest(gomock.Any(), &mfexternalpb.GetMFHoldingsImportRequestRequest{
					Identifier: &mfexternalpb.GetMFHoldingsImportRequestRequest_ExternalId{ExternalId: "ext1"},
				}).Return(&mfexternalpb.GetMFHoldingsImportRequestResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
				ExternalId: "ext1",
				NextScreen: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_MF_HOLDINGS_IMPORT_OTP_SUBMISSION_SCREEN,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&mutualFundExtScreenOptPb.MFHoldingsImportSubmitOtpScreenOptions{
						ExternalId:           "ext1",
						Title:                commontypes.GetTextFromStringFontColourFontStyle("Verify with OTP", colors.ColorNight, commontypes.FontStyle_HEADLINE_L),
						Description:          commontypes.GetTextFromStringFontColourFontStyle("Waiting to automatically detect the OTP sent to +919999999999 via MF Central", colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_S),
						OtpRetryTimerSeconds: 30,
						BackgroundColour:     uiWidget.GetBlockBackgroundColour(colors.ColorSnow),
						Identifier: &mutualFundExtScreenOptPb.MFHoldingsImportSubmitOtpScreenOptions_PhoneNumber{
							PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999},
						},
					}),
				},
			},
		},
		{
			name: "Successfully generate otp using phone number available with wealth",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "ext1",
			},
			before: func(m *mockFields) {
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(false, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(false, nil)
				m.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), actorId).Return(panDetails, nil)
				m.datetime.EXPECT().Now().Return(time.Date(2023, 7, 20, 0, 0, 0, 0, datetime.IST))
				m.wealthOnbClient.EXPECT().GetOrCreateUser(gomock.Any(), &wealthonboardingPb.GetOrCreateUserRequest{
					ActorId: "actorId",
				}).Return(&wealthonboardingPb.GetOrCreateUserResponse{
					Status: rpcPb.StatusOk(),
					UserDetails: &wealthonboardingPb.GetOrCreateUserResponse_UserDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						Email: "<EMAIL>",
					},
				}, nil)
				m.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "ext1",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
						Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
					},
					Pan: "**********",
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{Status: rpcPb.StatusOk()}, nil)

				m.mfExternalOrdersClient.EXPECT().GetHoldingsImportRequestsInDateRange(gomock.Any(), &mfexternalpb.GetHoldingsImportRequestsInDateRangeRequest{
					ActorId:  "actorId",
					FromTime: timestamppb.New(time.Date(2022, 7, 20, 0, 0, 0, 0, datetime.IST)),
					ToTime:   timestamppb.New(time.Date(2023, 7, 20, 0, 0, 0, 0, datetime.IST)),
				}).Return(&mfexternalpb.GetHoldingsImportRequestsInDateRangeResponse{
					Status: rpcPb.StatusOk(),
					HoldingsImportRequests: []*mfexternalpb.MFHoldingsImportRequestTracker{
						{
							Id:                    "id1",
							ActorId:               "actorId",
							ClientReferenceNumber: "clientRefNo",
							CreatedAt:             timestamppb.New(time.Date(2023, 4, 20, 0, 0, 0, 0, datetime.IST)),
							MetaData: &mfexternalpb.MFHoldingsImportRequestMetaData{
								OtpVerificationAttempts: 1,
								OtpRequests: map[string]*mfexternalpb.OtpRequest{
									"clientRefNo": {},
								},
							},
						},
					},
				}, nil)
				m.mfExternalOrdersClient.EXPECT().GetMFHoldingsImportRequest(gomock.Any(), &mfexternalpb.GetMFHoldingsImportRequestRequest{
					Identifier: &mfexternalpb.GetMFHoldingsImportRequestRequest_ExternalId{ExternalId: "ext1"},
				}).Return(&mfexternalpb.GetMFHoldingsImportRequestResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
				ExternalId: "ext1",
				NextScreen: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_MF_HOLDINGS_IMPORT_OTP_SUBMISSION_SCREEN,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&mutualFundExtScreenOptPb.MFHoldingsImportSubmitOtpScreenOptions{
						ExternalId:           "ext1",
						Title:                commontypes.GetTextFromStringFontColourFontStyle("Verify with OTP", colors.ColorNight, commontypes.FontStyle_HEADLINE_L),
						Description:          commontypes.GetTextFromStringFontColourFontStyle("Waiting to automatically detect the OTP sent to +919876543210 via MF Central", colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_S),
						OtpRetryTimerSeconds: 30,
						BackgroundColour:     uiWidget.GetBlockBackgroundColour(colors.ColorSnow),
						Identifier: &mutualFundExtScreenOptPb.MFHoldingsImportSubmitOtpScreenOptions_PhoneNumber{
							PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
						},
					}),
				},
			},
		},
		{
			name: "Successfully generate otp using email used in last request after the wealth phone number doesnt work",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "ext1",
			},
			before: func(m *mockFields) {
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(false, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(false, nil)
				m.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), actorId).Return(panDetails, nil)
				m.datetime.EXPECT().Now().Return(time.Date(2023, 7, 20, 0, 0, 0, 0, datetime.IST))
				m.wealthOnbClient.EXPECT().GetOrCreateUser(gomock.Any(), &wealthonboardingPb.GetOrCreateUserRequest{
					ActorId: "actorId",
				}).Return(&wealthonboardingPb.GetOrCreateUserResponse{
					Status: rpcPb.StatusOk(),
					UserDetails: &wealthonboardingPb.GetOrCreateUserResponse_UserDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						Email: "<EMAIL>",
					},
				}, nil)

				m.mfExternalOrdersClient.EXPECT().GetHoldingsImportRequestsInDateRange(gomock.Any(), &mfexternalpb.GetHoldingsImportRequestsInDateRangeRequest{
					ActorId:  "actorId",
					FromTime: timestamppb.New(time.Date(2022, 7, 20, 0, 0, 0, 0, datetime.IST)),
					ToTime:   timestamppb.New(time.Date(2023, 7, 20, 0, 0, 0, 0, datetime.IST)),
				}).Return(&mfexternalpb.GetHoldingsImportRequestsInDateRangeResponse{
					Status: rpcPb.StatusOk(),
					HoldingsImportRequests: []*mfexternalpb.MFHoldingsImportRequestTracker{
						{
							Id:                    "id1",
							ActorId:               "actorId",
							ClientReferenceNumber: "clientRefNo",
							CreatedAt:             timestamppb.New(time.Date(2023, 3, 20, 0, 0, 0, 0, datetime.IST)),
							MetaData: &mfexternalpb.MFHoldingsImportRequestMetaData{
								OtpVerificationAttempts: 1,
								OtpRequests: map[string]*mfexternalpb.OtpRequest{
									"clientRefNo": {
										OtpMedium: &mfexternalpb.OtpMedium{
											OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
											Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999}},
										},
									},
								},
							},
						},
						{
							Id:                    "id1",
							ActorId:               "actorId",
							ClientReferenceNumber: "clientRefNo",
							CreatedAt:             timestamppb.New(time.Date(2023, 4, 20, 0, 0, 0, 0, datetime.IST)),
							MetaData: &mfexternalpb.MFHoldingsImportRequestMetaData{
								OtpVerificationAttempts: 1,
								OtpRequests: map[string]*mfexternalpb.OtpRequest{
									"clientRefNo": {
										OtpMedium: &mfexternalpb.OtpMedium{
											OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_EMAIL,
											Identifier:    &mfexternalpb.OtpMedium_Email{Email: "<EMAIL>"},
										},
									},
								},
							},
						},
					},
				}, nil)

				m.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "ext1",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
						Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
					},
					Pan: "**********",
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{Status: &rpcPb.Status{Code: uint32(mfexternalpb.InitiateHoldingsImportResponse_INVALID_PAN_MOBILE_COMBINATION)}}, nil)

				m.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "ext1",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_EMAIL,
						Identifier:    &mfexternalpb.OtpMedium_Email{Email: "<EMAIL>"},
					},
					Pan: "**********",
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{Status: rpcPb.StatusOk()}, nil)
				m.mfExternalOrdersClient.EXPECT().GetMFHoldingsImportRequest(gomock.Any(), &mfexternalpb.GetMFHoldingsImportRequestRequest{
					Identifier: &mfexternalpb.GetMFHoldingsImportRequestRequest_ExternalId{ExternalId: "ext1"},
				}).Return(&mfexternalpb.GetMFHoldingsImportRequestResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
				ExternalId: "ext1",
				NextScreen: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_MF_HOLDINGS_IMPORT_OTP_SUBMISSION_SCREEN,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&mutualFundExtScreenOptPb.MFHoldingsImportSubmitOtpScreenOptions{
						ExternalId:           "ext1",
						Title:                commontypes.GetTextFromStringFontColourFontStyle("Verify with OTP", colors.ColorNight, commontypes.FontStyle_HEADLINE_L),
						Description:          commontypes.GetTextFromStringFontColourFontStyle("Enter the OTP <NAME_EMAIL> via MF Central", colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_S),
						OtpRetryTimerSeconds: 30,
						BackgroundColour:     uiWidget.GetBlockBackgroundColour(colors.ColorSnow),
						Identifier: &mutualFundExtScreenOptPb.MFHoldingsImportSubmitOtpScreenOptions_Email{
							Email: "<EMAIL>",
						},
					}),
				},
			},
		},
		{
			name: "Successfully generate otp using wealth email after the phone number doesnt work",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "ext1",
			},
			before: func(m *mockFields) {
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(false, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(false, nil)
				m.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), actorId).Return(panDetails, nil)
				m.datetime.EXPECT().Now().Return(time.Date(2023, 7, 20, 0, 0, 0, 0, datetime.IST))
				m.wealthOnbClient.EXPECT().GetOrCreateUser(gomock.Any(), &wealthonboardingPb.GetOrCreateUserRequest{
					ActorId: "actorId",
				}).Return(&wealthonboardingPb.GetOrCreateUserResponse{
					Status: rpcPb.StatusOk(),
					UserDetails: &wealthonboardingPb.GetOrCreateUserResponse_UserDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						Email: "<EMAIL>",
					},
				}, nil)

				m.mfExternalOrdersClient.EXPECT().GetHoldingsImportRequestsInDateRange(gomock.Any(), &mfexternalpb.GetHoldingsImportRequestsInDateRangeRequest{
					ActorId:  "actorId",
					FromTime: timestamppb.New(time.Date(2022, 7, 20, 0, 0, 0, 0, datetime.IST)),
					ToTime:   timestamppb.New(time.Date(2023, 7, 20, 0, 0, 0, 0, datetime.IST)),
				}).Return(&mfexternalpb.GetHoldingsImportRequestsInDateRangeResponse{
					Status: rpcPb.StatusOk(),
					HoldingsImportRequests: []*mfexternalpb.MFHoldingsImportRequestTracker{
						{
							Id:                    "id1",
							ActorId:               "actorId",
							ClientReferenceNumber: "clientRefNo",
							CreatedAt:             timestamppb.New(time.Date(2023, 4, 20, 0, 0, 0, 0, datetime.IST)),
							MetaData: &mfexternalpb.MFHoldingsImportRequestMetaData{
								OtpVerificationAttempts: 1,
								OtpRequests: map[string]*mfexternalpb.OtpRequest{
									"clientRefNo": {},
								},
							},
						},
					},
				}, nil)

				m.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "ext1",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
						Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
					},
					Pan: "**********",
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{Status: &rpcPb.Status{Code: uint32(mfexternalpb.InitiateHoldingsImportResponse_INVALID_PAN_MOBILE_COMBINATION)}}, nil)

				m.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "ext1",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_EMAIL,
						Identifier:    &mfexternalpb.OtpMedium_Email{Email: "<EMAIL>"},
					},
					Pan: "**********",
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{Status: rpcPb.StatusOk()}, nil)
				m.mfExternalOrdersClient.EXPECT().GetMFHoldingsImportRequest(gomock.Any(), &mfexternalpb.GetMFHoldingsImportRequestRequest{
					Identifier: &mfexternalpb.GetMFHoldingsImportRequestRequest_ExternalId{ExternalId: "ext1"},
				}).Return(&mfexternalpb.GetMFHoldingsImportRequestResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
				ExternalId: "ext1",
				NextScreen: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_MF_HOLDINGS_IMPORT_OTP_SUBMISSION_SCREEN,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&mutualFundExtScreenOptPb.MFHoldingsImportSubmitOtpScreenOptions{
						ExternalId:           "ext1",
						Title:                commontypes.GetTextFromStringFontColourFontStyle("Verify with OTP", colors.ColorNight, commontypes.FontStyle_HEADLINE_L),
						Description:          commontypes.GetTextFromStringFontColourFontStyle("Enter the OTP <NAME_EMAIL> via MF Central", colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_S),
						OtpRetryTimerSeconds: 30,
						BackgroundColour:     uiWidget.GetBlockBackgroundColour(colors.ColorSnow),
						Identifier: &mutualFundExtScreenOptPb.MFHoldingsImportSubmitOtpScreenOptions_Email{
							Email: "<EMAIL>",
						},
					}),
				},
			},
		},
		{
			name: "Failure to generate otp using either email or phone number should return edit phone number screen",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "ext1",
			},
			before: func(m *mockFields) {
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(false, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(false, nil)
				m.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), actorId).Return(panDetails, nil)
				m.mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any())
				m.datetime.EXPECT().Now().Return(time.Date(2023, 7, 20, 0, 0, 0, 0, datetime.IST))
				m.wealthOnbClient.EXPECT().GetOrCreateUser(gomock.Any(), &wealthonboardingPb.GetOrCreateUserRequest{
					ActorId: "actorId",
				}).Return(&wealthonboardingPb.GetOrCreateUserResponse{
					Status: rpcPb.StatusOk(),
					UserDetails: &wealthonboardingPb.GetOrCreateUserResponse_UserDetails{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						Email: "<EMAIL>",
					},
				}, nil)

				m.mfExternalOrdersClient.EXPECT().GetHoldingsImportRequestsInDateRange(gomock.Any(), &mfexternalpb.GetHoldingsImportRequestsInDateRangeRequest{
					ActorId:  "actorId",
					FromTime: timestamppb.New(time.Date(2022, 7, 20, 0, 0, 0, 0, datetime.IST)),
					ToTime:   timestamppb.New(time.Date(2023, 7, 20, 0, 0, 0, 0, datetime.IST)),
				}).Return(&mfexternalpb.GetHoldingsImportRequestsInDateRangeResponse{
					Status: rpcPb.StatusOk(),
					HoldingsImportRequests: []*mfexternalpb.MFHoldingsImportRequestTracker{
						{
							Id:                    "id1",
							ActorId:               "actorId",
							ClientReferenceNumber: "clientRefNo",
							CreatedAt:             timestamppb.New(time.Date(2023, 4, 20, 0, 0, 0, 0, datetime.IST)),
							MetaData: &mfexternalpb.MFHoldingsImportRequestMetaData{
								OtpVerificationAttempts: 1,
								OtpRequests: map[string]*mfexternalpb.OtpRequest{
									"clientRefNo": {},
								},
							},
						},
					},
				}, nil)

				m.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "ext1",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
						Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
					},
					Pan: "**********",
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{Status: &rpcPb.Status{Code: uint32(mfexternalpb.InitiateHoldingsImportResponse_INVALID_PAN_MOBILE_COMBINATION)}}, nil)

				m.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "ext1",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_EMAIL,
						Identifier:    &mfexternalpb.OtpMedium_Email{Email: "<EMAIL>"},
					},
					Pan: "**********",
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{Status: &rpcPb.Status{Code: uint32(mfexternalpb.InitiateHoldingsImportResponse_INVALID_PAN_EMAIL_COMBINATION)}}, nil)
				m.mfExternalOrdersClient.EXPECT().GetMFHoldingsImportRequest(gomock.Any(), &mfexternalpb.GetMFHoldingsImportRequestRequest{
					Identifier: &mfexternalpb.GetMFHoldingsImportRequestRequest_ExternalId{ExternalId: "ext1"},
				}).Return(&mfexternalpb.GetMFHoldingsImportRequestResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: &rpcPb.Status{Code: uint32(fePb.GenerateImportMFHoldingsOTPV2Response_INVALID_PAN_MOBILE_EMAIL_COMBINATION)}},
				ExternalId: "ext1",
				NextScreen: sv.preparePhoneNumberSubmissionScreen("ext1", nil, panDetails, lightThemeParams),
			},
		},
		{
			name: "Internal server error while generating otp should return generic error info screen",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "ext1",
				Identifier: &fePb.GenerateImportMFHoldingsOTPV2Request_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999}},
			},
			before: func(m *mockFields) {
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(false, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(false, nil)
				m.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), actorId).Return(panDetails, nil)
				m.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "ext1",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
						Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999}},
					},
					Pan: "**********",
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{Status: rpcPb.StatusInternal()}, nil)
				m.mfExternalOrdersClient.EXPECT().GetMFHoldingsImportRequest(gomock.Any(), &mfexternalpb.GetMFHoldingsImportRequestRequest{
					Identifier: &mfexternalpb.GetMFHoldingsImportRequestRequest_ExternalId{ExternalId: "ext1"},
				}).Return(&mfexternalpb.GetMFHoldingsImportRequestResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusInternal()},
				ExternalId: "ext1",
				ErrorInfo:  sv.prepareGenericErrorlInfoScreen(nil),
			},
		},
		{
			name: "If no pan is available then rpc should return pan mobile input screen",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "ext1",
				Identifier: &fePb.GenerateImportMFHoldingsOTPV2Request_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999}},
			},
			before: func(m *mockFields) {
				m.mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any())
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(false, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(false, nil)
				m.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), actorId).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
				ExternalId: "ext1",
				NextScreen: sv.preparePhoneNumberSubmissionScreenWithUnverifiedPan("ext1", "", nil, lightThemeParams),
			},
			wantErr: false,
		},
		{
			name: "Happy Flow for the new PAN Prefill consent flow, where PAN is fetched from the consent screen and is Verified",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "externalId",
				Identifier: &fePb.GenerateImportMFHoldingsOTPV2Request_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
				PanNumber:  panDetails3.Pan,
			},
			before: func(f *mockFields) {
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(true, nil)
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(true, nil)
				f.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), "actorId").Return(panDetails3, nil)
				f.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "externalId",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
						Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
					},
					Pan: panDetails3.Pan,
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{
					Status:     rpcPb.StatusOk(),
					ExternalId: "externalId",
				}, nil)
				f.mfExternalOrdersClient.EXPECT().GetMFHoldingsImportRequest(gomock.Any(), &mfexternalpb.GetMFHoldingsImportRequestRequest{
					Identifier: &mfexternalpb.GetMFHoldingsImportRequestRequest_ExternalId{ExternalId: "externalId"},
				}).Return(tracker1, nil)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
				ExternalId: "externalId",
				NextScreen: nextScreen1,
			},
			wantErr: false,
		},
		{
			name: "Happy Flow for the new PAN Prefill consent flow, where PAN is fetched from the consent service, pan is there in the request itself, got it from client",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "externalId",
				Identifier: &fePb.GenerateImportMFHoldingsOTPV2Request_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
				PanNumber:  panDetails2.Pan,
			},
			before: func(f *mockFields) {
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(true, nil)
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(true, nil)
				f.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), "actorId").Return(panDetails2, nil)
				f.mockPanProcessor.EXPECT().StoreUnverifiedPan(gomock.Any(), "actorId", panDetails2.Pan, userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_WEALTH_BUILDER_DATA).Return(nil)
				f.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "externalId",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
						Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
					},
					Pan: panDetails2.Pan,
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{
					Status:     rpcPb.StatusOk(),
					ExternalId: "externalId",
				}, nil)
				f.mfExternalOrdersClient.EXPECT().GetMFHoldingsImportRequest(gomock.Any(), &mfexternalpb.GetMFHoldingsImportRequestRequest{
					Identifier: &mfexternalpb.GetMFHoldingsImportRequestRequest_ExternalId{ExternalId: "externalId"},
				}).Return(tracker1, nil)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
				ExternalId: "externalId",
				NextScreen: nextScreen1,
			},
			wantErr: false,
		},
		{
			name: "Happy Flow for the new PAN Prefill consent flow, starting from onboarding",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "externalId",
				Identifier: &fePb.GenerateImportMFHoldingsOTPV2Request_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
				PanNumber:  panDetails2.Pan,
			},
			before: func(f *mockFields) {
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(true, nil)
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(true, nil)
				f.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), "actorId").Return(panDetails2, nil)
				f.mockPanProcessor.EXPECT().StoreUnverifiedPan(gomock.Any(), "actorId", panDetails2.Pan, userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_WEALTH_BUILDER_DATA).Return(nil)
				f.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "externalId",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
						Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
					},
					Pan: panDetails2.Pan,
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{
					Status:     rpcPb.StatusOk(),
					ExternalId: "externalId",
				}, nil)
				f.mfExternalOrdersClient.EXPECT().GetMFHoldingsImportRequest(gomock.Any(), &mfexternalpb.GetMFHoldingsImportRequestRequest{
					Identifier: &mfexternalpb.GetMFHoldingsImportRequestRequest_ExternalId{ExternalId: "externalId"},
				}).Return(tracker3, nil)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
				ExternalId: "externalId",
				NextScreen: nextScreen1,
			},
			wantErr: false,
		},
		{
			name: "Error while Initiating MF Holdings Import due to INVALID_PAN_MOBILE combination",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "externalId",
				Identifier: &fePb.GenerateImportMFHoldingsOTPV2Request_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
				PanNumber:  panDetails2.Pan,
			},
			before: func(f *mockFields) {
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(true, nil)
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(true, nil)
				f.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), "actorId").Return(panDetails2, nil)
				f.mockPanProcessor.EXPECT().StoreUnverifiedPan(gomock.Any(), "actorId", panDetails2.Pan, userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_WEALTH_BUILDER_DATA).Return(nil)
				f.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "externalId",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
						Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
					},
					Pan: panDetails2.Pan,
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{Status: &rpcPb.Status{Code: uint32(mfexternalpb.InitiateHoldingsImportResponse_INVALID_PAN_MOBILE_COMBINATION)}}, nil)
				f.mfExternalOrdersClient.EXPECT().GetMFHoldingsImportRequest(gomock.Any(), &mfexternalpb.GetMFHoldingsImportRequestRequest{
					Identifier: &mfexternalpb.GetMFHoldingsImportRequestRequest_ExternalId{ExternalId: "externalId"},
				}).Return(tracker1, nil)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: &rpcPb.Status{Code: uint32(fePb.GenerateImportMFHoldingsOTPV2Response_INVALID_PAN_MOBILE_COMBINATION)}},
				ExternalId: "externalId",
				ErrorInfo: analyserTypesPb.NewDefaultInfoScreen().
					WithImage(commontypes.GetVisualElementImageFromUrl(failureIcon).WithProperties(&commontypes.VisualElementProperties{Width: 146, Height: 156})).
					WithCustomTitle(
						typesUiPb.NewITC().WithTexts(
							commontypes.GetTextFromStringFontColourFontStyle("We were unable to find investments linked to ", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L).WithAlignment(commontypes.Text_ALIGNMENT_CENTER),
							commontypes.GetTextFromStringFontColourFontStyle(phNumberInString, colorJade, commontypes.FontStyle_HEADLINE_L).WithAlignment(commontypes.Text_ALIGNMENT_CENTER)),
					).
					WithDefaultDescription("Visit <a style='color: #00B899;' "+
						"href=\"https://www.camsonline.com/Investors/Service-requests/Updation_Email_Id_or_Mobile_No/Update-email\">camsonline.com</a> to update your mobile number").
					WithDefaultPrimaryAction("Try with another number", sv.preparePhoneNumberSubmissionScreenWithUnverifiedPan("externalId", panDetails2.Pan, &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}, darkThemeParams)).
					WithDefaultSecondaryAction("Do this later", &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_HOME}),
			},
			wantErr: false,
		},
		{
			name: "Error while Initiating MF Holdings Import due to INVALID_PAN_MOBILE combination, multiple OTP tries",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "externalId",
				Identifier: &fePb.GenerateImportMFHoldingsOTPV2Request_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
				PanNumber:  panDetails2.Pan,
			},
			before: func(f *mockFields) {
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(true, nil)
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(true, nil)
				f.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), "actorId").Return(panDetails2, nil)
				f.mockPanProcessor.EXPECT().StoreUnverifiedPan(gomock.Any(), "actorId", panDetails2.Pan, userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_WEALTH_BUILDER_DATA).Return(nil)
				f.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "externalId",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
						Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
					},
					Pan: panDetails2.Pan,
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{Status: &rpcPb.Status{Code: uint32(mfexternalpb.InitiateHoldingsImportResponse_INVALID_PAN_MOBILE_COMBINATION)}}, nil)
				f.mfExternalOrdersClient.EXPECT().GetMFHoldingsImportRequest(gomock.Any(), &mfexternalpb.GetMFHoldingsImportRequestRequest{
					Identifier: &mfexternalpb.GetMFHoldingsImportRequestRequest_ExternalId{ExternalId: "externalId"},
				}).Return(tracker2, nil)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: &rpcPb.Status{Code: uint32(fePb.GenerateImportMFHoldingsOTPV2Response_INVALID_PAN_MOBILE_COMBINATION)}},
				ExternalId: "externalId",
				ErrorInfo: analyserTypesPb.NewDefaultInfoScreen().
					WithImage(commontypes.GetVisualElementImageFromUrl(failureIcon).WithProperties(&commontypes.VisualElementProperties{Width: 146, Height: 156})).
					WithCustomTitle(
						typesUiPb.NewITC().WithTexts(
							commontypes.GetTextFromStringFontColourFontStyle("We were unable to find investments linked to your PAN ", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L).WithAlignment(commontypes.Text_ALIGNMENT_CENTER),
							commontypes.GetTextFromStringFontColourFontStyle(panDetails2.Pan, colorJade, commontypes.FontStyle_HEADLINE_L).WithAlignment(commontypes.Text_ALIGNMENT_CENTER),
							commontypes.GetTextFromStringFontColourFontStyle(" and mobile no. ", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L).WithAlignment(commontypes.Text_ALIGNMENT_CENTER),
							commontypes.GetTextFromStringFontColourFontStyle(phNumberInString, colorJade, commontypes.FontStyle_HEADLINE_L).WithAlignment(commontypes.Text_ALIGNMENT_CENTER)),
					).
					WithDefaultDescription("Visit <a style='color: #00B899;' "+
																"href=\"https://www.camsonline.com/Investors/Service-requests/Updation_Email_Id_or_Mobile_No/Update-email\">camsonline.com</a> to update your mobile number").
					WithDefaultPrimaryAction("Skip", &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_HOME}). // Get HomeScreen Deeplink
					WithDefaultSecondaryAction("Try again with PAN & Mobile no.", sv.preparePhoneNumberSubmissionScreenWithUnverifiedPan("externalId", panDetails2.Pan, phoneNo, darkThemeParams)),
			},
			wantErr: false,
		},
		{
			name: "Error while Initiating MF Holdings Import due to INVALID_PAN_MOBILE combination during onboarding",
			req: &fePb.GenerateImportMFHoldingsOTPV2Request{
				Req:        reqHeader,
				ExternalId: "externalId",
				Identifier: &fePb.GenerateImportMFHoldingsOTPV2Request_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
				PanNumber:  panDetails2.Pan,
			},
			before: func(f *mockFields) {
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW).WithActorId("actorId")).Return(true, nil)
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME).WithActorId("actorId")).Return(true, nil)
				f.mockPanProcessor.EXPECT().GetVerifiedOrUnverifiedPan(gomock.Any(), "actorId").Return(panDetails2, nil)
				f.mockPanProcessor.EXPECT().StoreUnverifiedPan(gomock.Any(), "actorId", panDetails2.Pan, userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_WEALTH_BUILDER_DATA).Return(nil)
				f.mfExternalOrdersClient.EXPECT().InitiateHoldingsImport(gomock.Any(), &mfexternalpb.InitiateHoldingsImportRequest{
					ActorId:    "actorId",
					ExternalId: "externalId",
					OtpMedium: &mfexternalpb.OtpMedium{
						OtpMediumType: mfexternalpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
						Identifier:    &mfexternalpb.OtpMedium_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}},
					},
					Pan: panDetails2.Pan,
				}).Return(&mfexternalpb.InitiateHoldingsImportResponse{Status: &rpcPb.Status{Code: uint32(mfexternalpb.InitiateHoldingsImportResponse_INVALID_PAN_MOBILE_COMBINATION)}}, nil)
				f.mfExternalOrdersClient.EXPECT().GetMFHoldingsImportRequest(gomock.Any(), &mfexternalpb.GetMFHoldingsImportRequestRequest{
					Identifier: &mfexternalpb.GetMFHoldingsImportRequestRequest_ExternalId{ExternalId: "externalId"},
				}).Return(tracker3, nil)
			},
			want: &fePb.GenerateImportMFHoldingsOTPV2Response{
				RespHeader: &headerPb.ResponseHeader{Status: &rpcPb.Status{Code: uint32(fePb.GenerateImportMFHoldingsOTPV2Response_INVALID_PAN_MOBILE_COMBINATION)}},
				ExternalId: "externalId",
				ErrorInfo: analyserTypesPb.NewDefaultInfoScreen().
					WithImage(commontypes.GetVisualElementImageFromUrl(failureIcon).WithProperties(&commontypes.VisualElementProperties{Width: 146, Height: 156})).
					WithCustomTitle(
						typesUiPb.NewITC().WithTexts(
							commontypes.GetTextFromStringFontColourFontStyle("We were unable to find investments linked to ", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L).WithAlignment(commontypes.Text_ALIGNMENT_CENTER),
							commontypes.GetTextFromStringFontColourFontStyle(phNumberInString, colorJade, commontypes.FontStyle_HEADLINE_L).WithAlignment(commontypes.Text_ALIGNMENT_CENTER)),
					).
					WithDefaultDescription("Visit <a style='color: #00B899;' "+
						"href=\"https://www.camsonline.com/Investors/Service-requests/Updation_Email_Id_or_Mobile_No/Update-email\">camsonline.com</a> to update your mobile number").
					WithDefaultPrimaryAction("Try with another number", sv.preparePhoneNumberSubmissionScreenWithUnverifiedPan("externalId", panDetails2.Pan, &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210}, darkThemeParams)).
					WithDefaultSecondaryAction("Do this later", &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
						ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&onbScreenOptions.GetNextOnbActionScreenOptions{
							Feature: onbPb.Feature_FEATURE_WEALTH_ANALYSER.String(),
						}),
					}),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := initMocks(ctrl)
			tt.before(f)

			s := NewInvestmentService(nil, nil, nil, nil, f.wealthOnbClient,
				nil, nil, nil, nil, nil, nil,
				nil, nil, nil, nil, nil, nil, nil,
				f.releaseEvaluator, f.mockEventBroker, nil, nil, f.consentClient, f.mfExternalOrdersClient,
				f.onbClient, f.datetime, nil, f.mockPanProcessor, f.netWorthClient)
			got, err := s.GenerateImportMFHoldingsOTPV2(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateImportMFHoldingsOTPV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			got.RespHeader.Status.DebugMessage = ""
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GenerateImportMFHoldingsOTPV2() got = %v,\n want %v\n diff %v", got, tt.want, diff)
			}
		})
	}
}
