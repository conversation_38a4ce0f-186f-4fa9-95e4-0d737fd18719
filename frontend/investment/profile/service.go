package profile

import (
	"github.com/epifi/gamma/api/frontend/investment/profile"
	investmentProfile "github.com/epifi/gamma/api/investment/profile"
)

type Service struct {
	profile.UnimplementedInvestmentProfileServer
	investmentProfileServiceClient investmentProfile.InvestmentProfileServiceClient
}

func NewService(
	investmentProfileServiceClient investmentProfile.InvestmentProfileServiceClient,
) *Service {
	return &Service{investmentProfileServiceClient: investmentProfileServiceClient}
}
