package fittt

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"

	fePb "github.com/epifi/gamma/api/frontend/fittt"
	feRecurringPaymentpb "github.com/epifi/gamma/api/frontend/recurringpayment"
	"github.com/epifi/gamma/api/rms/manager"
	types "github.com/epifi/gamma/api/typesv2"
	feRecurringPayment "github.com/epifi/gamma/frontend/recurringpayment"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) getPaymentRecipientParamValue(ctx context.Context, paymentRecipientVal *manager.RecurringPaymentInfo, paramId string) (*fePb.Value, error) {
	feRecurringPaymentInfo, err := s.getFeRecurringPaymentInfo(ctx, paymentRecipientVal.GetRecurringPaymentId())
	if err != nil {
		logger.Error(ctx, "failed to convert to FE recurring payment info", zap.Error(err))
		return nil, err
	}
	f := &fePb.Value{
		Value: &fePb.Value_RecipientSelector{
			RecipientSelector: &fePb.RecipientSelector{
				PayInfo: &fePb.RecipientInfo{
					RecurringPaymentInfo: feRecurringPaymentInfo,
					RecipientName:        &fePb.Text{Text: paymentRecipientVal.GetRecipientName()},
					MaskedAccId:          &fePb.Text{Text: paymentRecipientVal.GetMaskedAccId()},
					BankName:             &fePb.Text{Text: paymentRecipientVal.GetBankName()},
					SelectorIconUrls:     &fePb.IconUrls{RightIcon: EditIcon},
					PayeeBgColor:         PayeeBgColor,
					AccId:                paymentRecipientVal.GetAccId(),
					IfscCode:             paymentRecipientVal.GetIfscCode(),
				},
			}},
		Id:         paramId,
		IsSelected: true,
	}
	return f, nil
}

func (s *Service) getFeRecurringPaymentInfo(ctx context.Context, recurringPaymentId string) (*feRecurringPaymentpb.RecurringPayment, error) {
	recurringPayment, err := s.getRecurringPaymentInfo(ctx, recurringPaymentId)
	if err != nil {
		return nil, err
	}
	rpType, ok := feRecurringPayment.BeTypeToFeType[recurringPayment.Type]
	if !ok {
		return nil, fmt.Errorf("unknown recurringPayment type %s", recurringPayment.Type.String())
	}
	state, ok := feRecurringPayment.BeStateToFeStateMap[recurringPayment.State]
	if !ok {
		return nil, fmt.Errorf("unknown recurringPayment state %s", recurringPayment.State.String())
	}

	beFreq := recurringPayment.GetRecurrenceRule().GetAllowedFrequency()
	freq, ok := feRecurringPayment.BeAllowedFrequencyToFeAllowedFrequency[beFreq]
	if !ok {
		return nil, fmt.Errorf("unknown frequency %s", beFreq.String())
	}

	return &feRecurringPaymentpb.RecurringPayment{
		Id:                 recurringPayment.GetId(),
		Type:               rpType,
		State:              state,
		Amount:             types.GetFromBeMoney(recurringPayment.Amount),
		AllowedFrequency:   freq,
		CreationTimestamp:  recurringPayment.GetCreatedAt(),
		StartDate:          getFeDate(datetime.TimeToDateInLoc(recurringPayment.GetInterval().GetStartTime().AsTime(), datetime.IST)),
		EndDate:            getFeDate(datetime.TimeToDateInLoc(recurringPayment.GetInterval().GetEndTime().AsTime(), datetime.IST)),
		MaximumAllowedTxns: recurringPayment.GetMaximumAllowedTxns(),
	}, nil
}

func getFeDate(date *date.Date) *types.Date {
	feDate := &types.Date{
		Day:   date.GetDay(),
		Month: date.GetMonth(),
		Year:  date.GetYear(),
	}
	return feDate
}
