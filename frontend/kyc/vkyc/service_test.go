package vkyc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/google/go-cmp/cmp"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"github.com/epifi/gamma/api/actor"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	feVkycPb "github.com/epifi/gamma/api/frontend/kyc/vkyc"
	"github.com/epifi/gamma/api/kyc"
	beVkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	beVkycPbMocks "github.com/epifi/gamma/api/kyc/vkyc/mocks"
	mocks2 "github.com/epifi/gamma/api/pan/mocks"
	"github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/pkg/vkyc"
)

var okCtaList = &deeplink.Cta{
	Type:         deeplink.Cta_CUSTOM,
	Text:         vkyc.OkGotItCtaTxt,
	DisplayTheme: deeplink.Cta_PRIMARY,
	Deeplink: &deeplink.Deeplink{
		Screen: deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API,
	},
}

var FaqWebPageCtaList = vkyc.FaqWebPageCtaList[0]

var retryCtaList = &deeplink.Cta{
	Type:         deeplink.Cta_CUSTOM,
	Text:         vkyc.RetryCtaTxt,
	DisplayTheme: deeplink.Cta_PRIMARY,
	Deeplink: vkyc.GetVKYCNextActionDeeplink(&deeplink.GetVKYCNextActionApiScreenOptions{
		EntryPoint:      "ENTRY_POINT_ONBOARDING_STUDENT",
		ClientLastState: beVkycPb.VKYCClientState_VKYC_CLIENT_STATE_PAN_EVALUATION.String(),
		ShowCtaLoader:   true,
	}),
}

var knowMoreCtaList = &deeplink.Cta{
	Type:         deeplink.Cta_CUSTOM,
	Text:         vkyc.KnowMoreCtaTxt,
	DisplayTheme: deeplink.Cta_PRIMARY,
	Deeplink: &deeplink.Deeplink{
		Screen: deeplink.Screen_FAQ_CATEGORY,
		ScreenOptions: &deeplink.Deeplink_FaqCategoryOptions{
			FaqCategoryOptions: &deeplink.FaqCategoryOptions{
				CategoryId: "82000263662",
			},
		},
	},
}

var (
	now = timestamppb.Now()
	dl1 = &deeplink.Deeplink{
		Screen: deeplink.Screen_VKYC_ERROR_SCREEN,
		ScreenOptions: &deeplink.Deeplink_VkycErrorScreenOptions{
			VkycErrorScreenOptions: &deeplink.VKYCErrorScreenOptions{
				HideBackButton: true,
				Image: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  "https://epifi-icons.pointz.in/vkyc/verify-identity.png",
					Width:     vkyc.VesImageWidth,
					Height:    vkyc.VesImageHeight,
				},
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: vkyc.GetBoldTitle("Unclear photograph on your KYC documents"),
					},
					FontColor: "#333333",
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_HEADLINE_L,
					},
				},
				Description: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: "Sigh! We've all been there. Follow these pointers before calling again:",
					},
					FontColor: "#878A8D",
				},
				InstructionItemsBlock: &deeplink.InstructionItemsBlock{InstructionItems: []*deeplink.InstructionItem{
					{
						Text: &commontypes.Text{
							DisplayValue: &commontypes.Text_Html{
								Html: "The photo should ideally match your current appearance",
							},
							FontColor: "#333333",
						},
					},
					{
						Text: &commontypes.Text{
							DisplayValue: &commontypes.Text_Html{
								Html: "If not, update the photograph on your KYC documents",
							},
							FontColor: "#333333",
						},
					},
					{
						Text: &commontypes.Text{
							DisplayValue: &commontypes.Text_Html{
								Html: "After updating to a high-quality photo, start your Video KYC call",
							},
							FontColor: "#333333",
						},
					},
				},
					BgColor: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{
							BlockColour: vkyc.VesBGColor,
						},
					},
				},
				Ctas: []*deeplink.Cta{
					okCtaList,
				},
			},
		},
	}
	dl2 = &deeplink.Deeplink{
		Screen: deeplink.Screen_VKYC_ERROR_SCREEN,
		ScreenOptions: &deeplink.Deeplink_VkycErrorScreenOptions{
			VkycErrorScreenOptions: &deeplink.VKYCErrorScreenOptions{
				Image: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  "https://epifi-icons.pointz.in/vkyc/verify-identity.png",
					Width:     vkyc.VesImageWidth,
					Height:    vkyc.VesImageHeight,
				},
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: vkyc.GetBoldTitle("Our RM was unable to verify your PAN card"),
					},
					FontColor: "#333333",
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_HEADLINE_L,
					},
				},
				Description: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: "Don’t have your original PAN card? We recommend following these steps:",
					},
					FontColor: "#878A8D",
				},
				HideBackButton: true,
				InstructionItemsBlock: &deeplink.InstructionItemsBlock{
					InstructionItems: []*deeplink.InstructionItem{
						{
							Text: &commontypes.Text{
								DisplayValue: &commontypes.Text_Html{
									Html: "Place an order for a new PAN card using this link:",
								},
								FontColor: "#333333",
							},
							WeblinkBlock: &deeplink.WeblinkBlock{
								Weblink: "https://incometaxindia.gov.in/Pages/tax-services/apply-for-pan.aspx",
								Icon: &commontypes.Image{
									ImageUrl:  vkyc.VesShareIcon,
									ImageType: commontypes.ImageType_PNG,
								},
								BgColor: &widget.BackgroundColour{
									Colour: &widget.BackgroundColour_BlockColour{
										BlockColour: vkyc.VesWeblinkBlockBGColor,
									},
								},
							},
						},
						{
							Text: &commontypes.Text{
								DisplayValue: &commontypes.Text_Html{
									Html: "Once you receive the PAN card,  complete your KYC call",
								},
								FontColor: "#333333",
							},
						},
					},
					BgColor: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{
							BlockColour: vkyc.VesBGColor,
						},
					},
				},
				Ctas: []*deeplink.Cta{
					okCtaList,
				},
			},
		},
	}

	dl3 = &deeplink.Deeplink{
		Screen: deeplink.Screen_VKYC_ERROR_SCREEN,
		ScreenOptions: &deeplink.Deeplink_VkycErrorScreenOptions{
			VkycErrorScreenOptions: &deeplink.VKYCErrorScreenOptions{
				Image: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  "https://epifi-icons.pointz.in/vkyc/verify-identity.png",
					Width:     vkyc.VesImageWidth,
					Height:    vkyc.VesImageHeight,
				},
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: vkyc.GetBoldTitle("Our RM was unable to verify your PAN card"),
					},
					FontColor: "#333333",
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_HEADLINE_L,
					},
				},
				HideBackButton: true,
				Description: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: "Don’t have your original PAN card? We recommend following these steps:",
					},
					FontColor: "#878A8D",
				},
				InstructionItemsBlock: &deeplink.InstructionItemsBlock{
					InstructionItems: []*deeplink.InstructionItem{
						{
							Text: &commontypes.Text{
								DisplayValue: &commontypes.Text_Html{
									Html: "Place an order for a new PAN card using this link:",
								},
								FontColor: "#333333",
							},
							WeblinkBlock: &deeplink.WeblinkBlock{
								Weblink: "https://incometaxindia.gov.in/Pages/tax-services/apply-for-pan.aspx",
								Icon: &commontypes.Image{
									ImageUrl:  vkyc.VesShareIcon,
									ImageType: commontypes.ImageType_PNG,
								},
								BgColor: &widget.BackgroundColour{
									Colour: &widget.BackgroundColour_BlockColour{
										BlockColour: vkyc.VesWeblinkBlockBGColor,
									},
								},
							},
						},
						{
							Text: &commontypes.Text{
								DisplayValue: &commontypes.Text_Html{
									Html: "Once you receive the PAN card,  complete your KYC call",
								},
								FontColor: "#333333",
							},
						},
					},
					BgColor: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{
							BlockColour: "#F0F3F7",
						},
					},
				},
				Ctas: []*deeplink.Cta{
					FaqWebPageCtaList,
				},
			},
		},
	}

	dl4 = &deeplink.Deeplink{
		Screen: deeplink.Screen_VKYC_ERROR_SCREEN,
		ScreenOptions: &deeplink.Deeplink_VkycErrorScreenOptions{
			VkycErrorScreenOptions: &deeplink.VKYCErrorScreenOptions{
				Image: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  "https://epifi-icons.pointz.in/vkyc/customer.png",
					Width:     vkyc.VesImageWidth,
					Height:    vkyc.VesImageHeight,
				},
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: vkyc.GetBoldTitle("Yikes...call disconnected!"),
					},
					FontColor: "#333333",
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_HEADLINE_L,
					},
				},
				HideBackButton: true,
				Description: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: "Our RM couldn't interact well due to constant movements on your screen.",
					},
					FontColor: "#878A8D",
				},
				InstructionItemsBlock: &deeplink.InstructionItemsBlock{
					InstructionItems: []*deeplink.InstructionItem{
						{
							Text: &commontypes.Text{
								DisplayValue: &commontypes.Text_Html{
									Html: "Consider starting the call after you find a comfy & quiet spot",
								},
								FontColor: "#333333",
							},
						},
						{
							Text: &commontypes.Text{
								DisplayValue: &commontypes.Text_Html{
									Html: "Please try not to multi-task & focus on completing the 3-min video call.",
								},
								FontColor: "#333333",
							},
						},
					},
					BgColor: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{
							BlockColour: "#F0F3F7",
						},
					},
				},
				Ctas: []*deeplink.Cta{
					retryCtaList,
				},
			},
		},
	}
	dl5 = &deeplink.Deeplink{
		Screen: deeplink.Screen_VKYC_ERROR_SCREEN,
		ScreenOptions: &deeplink.Deeplink_VkycErrorScreenOptions{
			VkycErrorScreenOptions: &deeplink.VKYCErrorScreenOptions{
				Image: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  "https://epifi-icons.pointz.in/chequebook/address.png",
					Width:     vkyc.VesImageWidth,
					Height:    vkyc.VesImageHeight,
				},
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: vkyc.GetBoldTitle("Oops...your application has been flagged!"),
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_HEADLINE_L,
					},
					FontColor: "#333333",
				},
				HideBackButton: true,
				Description: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: "Our systems have flagged your video KYC details & KYC documents. Per regulations, we are unable to approve your application.",
					},
					FontColor: "#878A8D",
				},
				Ctas: vkyc.HomeCtaList,
			},
		},
	}

	dl6 = &deeplink.Deeplink{
		Screen: deeplink.Screen_VKYC_ERROR_SCREEN,
		ScreenOptions: &deeplink.Deeplink_VkycErrorScreenOptions{
			VkycErrorScreenOptions: &deeplink.VKYCErrorScreenOptions{
				HideBackButton: true,
				Image: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  "https://epifi-icons.pointz.in/vkyc/tortoise.png",
					Width:     vkyc.VesImageWidth,
					Height:    vkyc.VesImageHeight,
				},
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: vkyc.GetBoldTitle("Seems like you need a Hindi speaking RM"),
					},
					FontColor: "#333333",
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_HEADLINE_L,
					},
				},
				Description: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: "Press ‘Continue’ to  be redirected to an\nRM that speaks your language",
					},
					FontColor: "#878A8D",
				},
				Ctas: []*deeplink.Cta{
					retryCtaList,
				},
			},
		},
	}
)

func TestService_GetVKYCInfo(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx     context.Context
		request *feVkycPb.GetVKYCInfoRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(md *mockDependencies)
		want    *feVkycPb.GetVKYCInfoResponse
		wantErr error
	}{
		{
			name: "user is full kyc",
			args: args{
				ctx: context.Background(),
				request: &feVkycPb.GetVKYCInfoRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor_id",
						},
					},
				},
			},
			mocks: func(md *mockDependencies) {
				md.mockBankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankCustomerPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{
						ActorId: "actor_id",
					},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						KycInfo: &bankCustomerPb.KYCInfo{
							KycLevel: kyc.KYCLevel_FULL_KYC,
						},
						Status:                    bankCustomerPb.Status_STATUS_ACTIVE,
						VendorCreationSucceededAt: now,
					},
				}, nil)
				md.timeClient.EXPECT().Now().Return(time.Date(2022, 12, 25, 0, 0, 0, 0, time.UTC))
			},
			want: &feVkycPb.GetVKYCInfoResponse{
				Status: rpc.StatusOk(),
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				ShowTile: false,
			},
			wantErr: nil,
		},
		{
			name: "vkyc approved",
			args: args{
				ctx: context.Background(),
				request: &feVkycPb.GetVKYCInfoRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor_id",
						},
					},
				},
			},
			mocks: func(md *mockDependencies) {
				md.mockBankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankCustomerPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{
						ActorId: "actor_id",
					},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						KycInfo: &bankCustomerPb.KYCInfo{
							KycLevel: kyc.KYCLevel_MIN_KYC,
						},
						Status:                    bankCustomerPb.Status_STATUS_ACTIVE,
						VendorCreationSucceededAt: now,
					},
				}, nil)
				md.mockVkycClient.EXPECT().GetVKYCInfo(gomock.Any(), &beVkycPb.GetVKYCInfoRequest{
					ActorId: "actor_id",
				}).Return(&beVkycPb.GetVKYCInfoResponse{
					Status: rpc.StatusOk(),
					VkycStatusResponse: &beVkycPb.GetVKYCStatusResponse{
						VkycSummary: &beVkycPb.VKYCSummary{
							Status: beVkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED,
						},
					},
				}, nil)
				md.timeClient.EXPECT().Now().Return(time.Date(2022, 12, 25, 0, 0, 0, 0, time.UTC))
			},
			want: &feVkycPb.GetVKYCInfoResponse{
				Status: rpc.StatusOk(),
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				ShowTile: false,
			},
			wantErr: nil,
		},
		{
			name: "inactive customer",
			args: args{
				ctx: context.Background(),
				request: &feVkycPb.GetVKYCInfoRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor_id",
						},
					},
				},
			},
			mocks: func(md *mockDependencies) {
				md.mockBankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankCustomerPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{
						ActorId: "actor_id",
					},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						Status: bankCustomerPb.Status_CUSTOMER_STATUS_INACTIVE,
					},
				}, nil)
				md.timeClient.EXPECT().Now().Return(time.Now())
			},
			want: &feVkycPb.GetVKYCInfoResponse{
				Status: rpc.StatusFailedPrecondition(),
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusFailedPrecondition(),
				},
				ShowTile: false,
			},
			wantErr: nil,
		},
		{
			name: "BKYC in progress",
			args: args{
				ctx: context.Background(),
				request: &feVkycPb.GetVKYCInfoRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor_id",
						},
					},
				},
			},
			mocks: func(md *mockDependencies) {
				md.mockBankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankCustomerPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{
						ActorId: "actor_id",
					},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						KycInfo: &bankCustomerPb.KYCInfo{
							KycLevel: kyc.KYCLevel_MIN_KYC,
						},
						VendorMetadata: &bankCustomerPb.VendorMetadata{
							Vendor: &bankCustomerPb.VendorMetadata_FederalMetadata{
								FederalMetadata: &bankCustomerPb.FederalMetadata{
									KycUpgradeInfo: &bankCustomerPb.KycUpgradeInfo{
										Status: bankCustomerPb.KYCUpgradeStatus_KYC_UPGRADE_STATUS_IN_PROGRESS,
									},
								},
							},
						},
						VendorCreationSucceededAt: now,
						Status:                    bankCustomerPb.Status_STATUS_ACTIVE,
					},
				}, nil)
				md.timeClient.EXPECT().Now().Return(time.Now())
			},
			want: &feVkycPb.GetVKYCInfoResponse{
				Status: rpc.StatusRecordNotFound(),
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusRecordNotFound(),
				},
				ShowTile: false,
			},
			wantErr: nil,
		},
		{
			name: "account is frozen",
			args: args{
				ctx: context.Background(),
				request: &feVkycPb.GetVKYCInfoRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor_id",
						},
					},
				},
			},
			mocks: func(md *mockDependencies) {
				md.mockBankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankCustomerPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{
						ActorId: "actor_id",
					},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						KycInfo: &bankCustomerPb.KYCInfo{
							KycLevel: kyc.KYCLevel_MIN_KYC,
						},
						Status:                    bankCustomerPb.Status_STATUS_ACTIVE,
						VendorCreationSucceededAt: timestamppb.Now(),
					},
				}, nil)
				md.timeClient.EXPECT().Now().Return(time.Now().Add(351 * 24 * time.Hour))
			},
			want: &feVkycPb.GetVKYCInfoResponse{
				Status: rpc.StatusOk(),
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				ShowTile: false,
			},
			wantErr: nil,
		},
		{
			name: "eligible for balance transfer",
			args: args{
				ctx: context.Background(),
				request: &feVkycPb.GetVKYCInfoRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor_id",
						},
					},
				},
			},
			mocks: func(md *mockDependencies) {
				md.mockBankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankCustomerPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{
						ActorId: "actor_id",
					},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						KycInfo: &bankCustomerPb.KYCInfo{
							KycLevel: kyc.KYCLevel_MIN_KYC,
						},
						Status:                    bankCustomerPb.Status_STATUS_ACTIVE,
						VendorCreationSucceededAt: timestamppb.New(now.AsTime().Add(-340 * 24 * time.Hour)),
					},
				}, nil)
				md.mockVkycClient.EXPECT().GetVKYCInfo(gomock.Any(), &beVkycPb.GetVKYCInfoRequest{
					ActorId: "actor_id",
				}).Return(&beVkycPb.GetVKYCInfoResponse{
					Status: rpc.StatusOk(),
					VkycStatusResponse: &beVkycPb.GetVKYCStatusResponse{
						VkycSummary: &beVkycPb.VKYCSummary{
							Status: beVkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_PROGRESS,
						},
					},
				}, nil)
				md.timeClient.EXPECT().Now().Return(time.Now())
			},
			want: &feVkycPb.GetVKYCInfoResponse{
				Status:                       rpc.StatusOk(),
				ShowTile:                     true,
				PopupVkycTile:                getAccountClosureFundTransferPopupTile(9),
				PopupTileNonDismissableAfter: MaxInt32,
				PopupTileDuration:            0,
			},
			wantErr: nil,
		},
		{
			name: "credit limit reached",
			args: args{
				ctx: context.Background(),
				request: &feVkycPb.GetVKYCInfoRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor_id",
						},
					},
				},
			},
			mocks: func(md *mockDependencies) {
				md.mockBankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankCustomerPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{
						ActorId: "actor_id",
					},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						KycInfo: &bankCustomerPb.KYCInfo{
							KycLevel: kyc.KYCLevel_MIN_KYC,
						},
						Status:                    bankCustomerPb.Status_STATUS_ACTIVE,
						VendorCreationSucceededAt: timestamppb.Now(),
					},
				}, nil)
				md.mockVkycClient.EXPECT().GetVKYCInfo(gomock.Any(), &beVkycPb.GetVKYCInfoRequest{
					ActorId: "actor_id",
				}).Return(&beVkycPb.GetVKYCInfoResponse{
					Status: rpc.StatusOk(),
					VkycStatusResponse: &beVkycPb.GetVKYCStatusResponse{
						VkycSummary: &beVkycPb.VKYCSummary{
							Status: beVkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_PROGRESS,
						},
					},
					IsLiveFlowEnabled: true,
				}, nil)
				md.timeClient.EXPECT().Now().Return(time.Now())
				md.mockSavingsClient.EXPECT().IsTxnAllowed(gomock.Any(), &savings.IsTxnAllowedRequest{
					Identifier: &savings.IsTxnAllowedRequest_PrimaryAccountHolderActor{PrimaryAccountHolderActor: "actor_id"},
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
					},
				}).Return(&savings.IsTxnAllowedResponse{
					Status: rpc.StatusOk(),
					SavingsTxnLimits: &savings.IsTxnAllowedResponse_SavingsTxnLimits{
						AllowedCreditLimitPercent: 10,
					},
				}, nil)
			},
			want: &feVkycPb.GetVKYCInfoResponse{
				Status:                       rpc.StatusOk(),
				ShowTile:                     false,
				PopupVkycTile:                getCreditBalanceLimitReachPopupTile(context.Background(), true),
				PopupTileNonDismissableAfter: MaxInt32,
				PopupTileDuration:            12,
			},
			wantErr: nil,
		},
		{
			name: "savings account freeze popup",
			args: args{
				ctx: context.Background(),
				request: &feVkycPb.GetVKYCInfoRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor_id",
						},
					},
				},
			},
			mocks: func(md *mockDependencies) {
				md.mockBankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankCustomerPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{
						ActorId: "actor_id",
					},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						KycInfo: &bankCustomerPb.KYCInfo{
							KycLevel: kyc.KYCLevel_MIN_KYC,
						},
						Status:                    bankCustomerPb.Status_STATUS_ACTIVE,
						VendorCreationSucceededAt: timestamppb.Now(),
					},
				}, nil)
				md.mockVkycClient.EXPECT().GetVKYCInfo(gomock.Any(), &beVkycPb.GetVKYCInfoRequest{
					ActorId: "actor_id",
				}).Return(&beVkycPb.GetVKYCInfoResponse{
					Status: rpc.StatusOk(),
					VkycStatusResponse: &beVkycPb.GetVKYCStatusResponse{
						VkycSummary: &beVkycPb.VKYCSummary{
							Status: beVkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_PROGRESS,
						},
					},
					IsLiveFlowEnabled: true,
				}, nil)
				md.timeClient.EXPECT().Now().Return(time.Now())
				md.mockSavingsClient.EXPECT().IsTxnAllowed(gomock.Any(), &savings.IsTxnAllowedRequest{
					Identifier: &savings.IsTxnAllowedRequest_PrimaryAccountHolderActor{PrimaryAccountHolderActor: "actor_id"},
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
					},
				}).Return(&savings.IsTxnAllowedResponse{
					Status:            rpc.StatusOk(),
					AccountFreezeDate: timestamppb.Now(),
					SavingsTxnLimits: &savings.IsTxnAllowedResponse_SavingsTxnLimits{
						AllowedCreditLimitPercent:       100,
						AllowedSavingsLimitPercentFloat: 12,
					},
				}, nil)
			},
			want: &feVkycPb.GetVKYCInfoResponse{
				Status:   rpc.StatusOk(),
				ShowTile: false,
				PopupVkycTile: getSavingsAccountBalanceLimitReachPopupTile(context.Background(), true, &savings.IsTxnAllowedResponse_SavingsTxnLimits{
					AllowedCreditLimitPercent:       100,
					AllowedSavingsLimitPercentFloat: 12,
				}),
				PopupTileNonDismissableAfter: MaxInt32,
				PopupTileDuration:            12,
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, md := newMockDependencies(t)
			if tt.mocks != nil {
				tt.mocks(md)
			}
			got, err := svc.GetVKYCInfo(tt.args.ctx, tt.args.request)
			if (err != nil) || tt.wantErr != nil {
				if !errors.Is(err, tt.wantErr) {
					t.Errorf("GetVKYCInfo() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			}
			opts := cmp.Options{
				protocmp.Transform(),
				protocmp.IgnoreFields(&feVkycPb.GetVKYCInfoResponse{}),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetVKYCInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_getAccountFreezePopupTitle(t *testing.T) {
	t.Parallel()
	type fields struct {
		UnimplementedVkycServer feVkycPb.UnimplementedVkycServer
		beVkycClient            beVkycPb.VKYCClient
		kycClient               kyc.KycClient
		onbClient               onboarding.OnboardingClient
		userClient              user.UsersClient
		actorClient             actor.ActorClient
		userGroupClient         group.GroupClient
		flags                   *config.Flags
		vkycConfig              *config.VKYC
		savingsClient           savings.SavingsClient
	}
	type args struct {
		accFreezeDate *timestamp.Timestamp
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			name: "30days away",
			args: args{
				accFreezeDate: timestamppb.New(time.Now().Add(30*24*time.Hour + 1*time.Minute)),
			},
			want: "Your Federal savings account is nearing its validity limit",
		},
		{
			name: "29days away",
			args: args{
				accFreezeDate: timestamppb.New(time.Now().Add(29*24*time.Hour + 1*time.Minute)),
			},
			want: "You’re 29 days away from an account freeze",
		},
		{
			name: "179days away",
			args: args{
				accFreezeDate: timestamppb.New(time.Now().Add(30*24*time.Hour + 1*time.Minute)),
			},
			want: "Your Federal savings account is nearing its validity limit",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				UnimplementedVkycServer: tt.fields.UnimplementedVkycServer,
				beVkycClient:            tt.fields.beVkycClient,
				kycClient:               tt.fields.kycClient,
				onbClient:               tt.fields.onbClient,
				flags:                   tt.fields.flags,
				genConf:                 genConf,
				savingsClient:           tt.fields.savingsClient,
			}
			if got := s.getAccountFreezePopupTitle(context.Background(), tt.args.accFreezeDate); got != tt.want {
				t.Errorf("getAccountFreezePopupTitle() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetCallExitRedirectDeeplink(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *feVkycPb.GetCallExitRedirectDeeplinkRequest
	}
	type mockDependencies struct {
		panClient  *mocks2.MockPanClient
		vkycClient *beVkycPbMocks.MockVKYCClient
	}
	tests := []struct {
		name        string
		args        args
		want        *feVkycPb.GetCallExitRedirectDeeplinkResponse
		wantErr     bool
		wantMock    func(md *mockDependencies)
		enableRetry bool
	}{
		{
			name: "Error from vkyc client",
			args: args{
				ctx: context.Background(),
				req: &feVkycPb.GetCallExitRedirectDeeplinkRequest{
					EntryPoint: "test",
				},
			},
			want: &feVkycPb.GetCallExitRedirectDeeplinkResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInternal(),
				},
			},
			wantErr: false,
			wantMock: func(md *mockDependencies) {
				md.vkycClient.EXPECT().GetCallExitRedirectDeeplink(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallExitRedirectDeeplinkResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.vkycClient.EXPECT().GetCallStatus(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallStatusResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			enableRetry: true,
		},
		{
			name: "Call finished event deeplink",
			args: args{
				ctx: context.Background(),
				req: &feVkycPb.GetCallExitRedirectDeeplinkRequest{
					EntryPoint: "ENTRY_POINT_CREDIT_LIMIT_BREACH",
				},
			},
			want: &feVkycPb.GetCallExitRedirectDeeplinkResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: getCallFailedDeeplink(context.Background(), beVkycPb.EntryPoint_ENTRY_POINT_CREDIT_LIMIT_BREACH,
					nil, nil, "", genConf.VKYC(), nil, ""),
			},
			wantErr: false,
			wantMock: func(md *mockDependencies) {
				md.vkycClient.EXPECT().GetCallExitRedirectDeeplink(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallExitRedirectDeeplinkResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.vkycClient.EXPECT().GetCallStatus(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallStatusResponse{
					Status: rpc.StatusOk(),
					CallStatus: []*beVkycPb.KarzaCallStatus{
						{
							Status: beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED,
						},
					},
				}, nil)
				md.vkycClient.EXPECT().GetVKYCInfo(context.Background(), gomock.Any()).Return(&beVkycPb.GetVKYCInfoResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &beVkycPb.VKYCRecord{
						VkycKarzaCustomerInfos: []*beVkycPb.VKYCKarzaCustomerInfo{
							{
								TransactionMetadata: &beVkycPb.VKYCKarzaCustomerInfoTransactionMetadata{
									Weblink: "abc",
								},
							},
						},
					},
				}, nil).Times(1)
			},
			enableRetry: true,
		},
		{
			name: "Call finished due to pan issue retry internal",
			args: args{
				ctx: context.Background(),
				req: &feVkycPb.GetCallExitRedirectDeeplinkRequest{
					EntryPoint: "ENTRY_POINT_CREDIT_LIMIT_BREACH",
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor_id",
						},
					},
				},
			},
			want: &feVkycPb.GetCallExitRedirectDeeplinkResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: getCallFailedDeeplink(context.Background(), beVkycPb.EntryPoint_ENTRY_POINT_CREDIT_LIMIT_BREACH, []*beVkycPb.KarzaCallStatus{
					{
						Status:    beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED,
						SubStatus: beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PAN_OCR_CAPTURE_FAILED,
					},
				}, &beVkycPb.VKYCKarzaCustomerInfo{
					TransactionMetadata: &beVkycPb.VKYCKarzaCustomerInfoTransactionMetadata{
						Weblink: "abc",
						KycDate: now,
					},
				}, "", genConf.VKYC(), nil, ""),
			},
			wantErr: false,
			wantMock: func(md *mockDependencies) {
				md.vkycClient.EXPECT().GetCallExitRedirectDeeplink(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallExitRedirectDeeplinkResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.vkycClient.EXPECT().GetCallStatus(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallStatusResponse{
					Status: rpc.StatusOk(),
					CallStatus: []*beVkycPb.KarzaCallStatus{
						{
							Status:    beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED,
							SubStatus: beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PAN_OCR_CAPTURE_FAILED,
						},
					},
				}, nil).Times(1)
				md.vkycClient.EXPECT().GetVKYCInfo(context.Background(), gomock.Any()).Return(&beVkycPb.GetVKYCInfoResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &beVkycPb.VKYCRecord{
						VkycKarzaCustomerInfos: []*beVkycPb.VKYCKarzaCustomerInfo{
							{
								TransactionMetadata: &beVkycPb.VKYCKarzaCustomerInfoTransactionMetadata{
									Weblink: "abc",
									KycDate: now,
								},
							},
						},
					},
				}, nil).Times(1)
				md.vkycClient.EXPECT().UpsertCallFailureCount(context.Background(), &beVkycPb.UpsertCallFailureCountRequest{
					ActorId:       "actor_id",
					FailureReason: beVkycPb.FailureReason_FAILURE_REASON_PAN_ISSUE,
				}).Return(&beVkycPb.UpsertCallFailureCountResponse{
					Status: rpc.StatusInternal(),
				}, nil).Times(1)
			},
			enableRetry: true,
		},
		{
			name: "Call finished due to audio issue retry",
			args: args{
				ctx: context.Background(),
				req: &feVkycPb.GetCallExitRedirectDeeplinkRequest{
					EntryPoint: "ENTRY_POINT_CREDIT_LIMIT_BREACH",
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor_id",
						},
					},
				},
			},
			want: &feVkycPb.GetCallExitRedirectDeeplinkResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: vkyc.GetVKYCNextActionDeeplink(&deeplink.GetVKYCNextActionApiScreenOptions{
					EntryPoint:      "ENTRY_POINT_CREDIT_LIMIT_BREACH",
					ClientLastState: beVkycPb.VKYCClientState_VKYC_CLIENT_STATE_PAN_EVALUATION.String(),
					ShowCtaLoader:   false,
					Title:           callRetryTitle,
					Subtitle:        callRetrySubtitle,
					Illustration: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  callRetryIcon,
					},
				}),
			},
			wantErr: false,
			wantMock: func(md *mockDependencies) {
				md.vkycClient.EXPECT().GetCallExitRedirectDeeplink(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallExitRedirectDeeplinkResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.vkycClient.EXPECT().GetCallStatus(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallStatusResponse{
					Status: rpc.StatusOk(),
					CallStatus: []*beVkycPb.KarzaCallStatus{
						{
							Status:    beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED,
							SubStatus: beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DISCONNECTED,
						},
					},
				}, nil)
				md.vkycClient.EXPECT().UpsertCallFailureCount(context.Background(), &beVkycPb.UpsertCallFailureCountRequest{
					ActorId:       "actor_id",
					FailureReason: beVkycPb.FailureReason_FAILURE_REASON_TECHNICAL_ISSUE,
				}).Return(&beVkycPb.UpsertCallFailureCountResponse{
					Status: rpc.StatusOk(),
					FailureReasonCount: []*beVkycPb.FailureReasonCount{
						{
							FailureReason: beVkycPb.FailureReason_FAILURE_REASON_TECHNICAL_ISSUE,
							Count:         1,
						},
					},
				}, nil).Times(1)
			},
			enableRetry: true,
		},
		{
			name: "Call finished due to audio issue retry count more then 2",
			args: args{
				ctx: context.Background(),
				req: &feVkycPb.GetCallExitRedirectDeeplinkRequest{
					EntryPoint: "ENTRY_POINT_CREDIT_LIMIT_BREACH",
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor_id",
						},
					},
				},
			},
			want: &feVkycPb.GetCallExitRedirectDeeplinkResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: getCallFailedDeeplink(context.Background(), beVkycPb.EntryPoint_ENTRY_POINT_CREDIT_LIMIT_BREACH, []*beVkycPb.KarzaCallStatus{
					{
						Status:    beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED,
						SubStatus: beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DISCONNECTED,
					},
				}, &beVkycPb.VKYCKarzaCustomerInfo{
					TransactionMetadata: &beVkycPb.VKYCKarzaCustomerInfoTransactionMetadata{
						Weblink: "abc",
						KycDate: now,
					},
				}, "", genConf.VKYC(), nil, ""),
			},
			wantErr: false,
			wantMock: func(md *mockDependencies) {
				md.vkycClient.EXPECT().GetCallExitRedirectDeeplink(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallExitRedirectDeeplinkResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.vkycClient.EXPECT().GetCallStatus(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallStatusResponse{
					Status: rpc.StatusOk(),
					CallStatus: []*beVkycPb.KarzaCallStatus{
						{
							Status:    beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED,
							SubStatus: beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DISCONNECTED,
						},
					},
				}, nil)
				md.vkycClient.EXPECT().UpsertCallFailureCount(context.Background(), &beVkycPb.UpsertCallFailureCountRequest{
					ActorId:       "actor_id",
					FailureReason: beVkycPb.FailureReason_FAILURE_REASON_TECHNICAL_ISSUE,
				}).Return(&beVkycPb.UpsertCallFailureCountResponse{
					Status: rpc.StatusOk(),
					FailureReasonCount: []*beVkycPb.FailureReasonCount{
						{
							FailureReason: beVkycPb.FailureReason_FAILURE_REASON_TECHNICAL_ISSUE,
							Count:         2,
						},
					},
				}, nil).Times(1)
				md.vkycClient.EXPECT().GetVKYCInfo(context.Background(), gomock.Any()).Return(&beVkycPb.GetVKYCInfoResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &beVkycPb.VKYCRecord{
						VkycKarzaCustomerInfos: []*beVkycPb.VKYCKarzaCustomerInfo{
							{
								TransactionMetadata: &beVkycPb.VKYCKarzaCustomerInfoTransactionMetadata{
									Weblink: "abc",
									KycDate: now,
								},
							},
						},
					},
				}, nil)
			},
			enableRetry: true,
		},
		{
			name: "Call finished due to audio issue retry count more then 1 and retry disable",
			args: args{
				ctx: context.Background(),
				req: &feVkycPb.GetCallExitRedirectDeeplinkRequest{
					EntryPoint: "ENTRY_POINT_CREDIT_LIMIT_BREACH",
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor_id",
						},
					},
				},
			},
			want: &feVkycPb.GetCallExitRedirectDeeplinkResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: getCallFailedDeeplink(context.Background(), beVkycPb.EntryPoint_ENTRY_POINT_CREDIT_LIMIT_BREACH, []*beVkycPb.KarzaCallStatus{
					{
						Status:    beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED,
						SubStatus: beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DISCONNECTED,
					},
				}, &beVkycPb.VKYCKarzaCustomerInfo{
					TransactionMetadata: &beVkycPb.VKYCKarzaCustomerInfoTransactionMetadata{
						Weblink: "abc",
						KycDate: now,
					},
				}, "", genConf.VKYC(), nil, ""),
			},
			wantErr: false,
			wantMock: func(md *mockDependencies) {
				md.vkycClient.EXPECT().GetCallExitRedirectDeeplink(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallExitRedirectDeeplinkResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.vkycClient.EXPECT().GetCallStatus(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallStatusResponse{
					Status: rpc.StatusOk(),
					CallStatus: []*beVkycPb.KarzaCallStatus{
						{
							Status:    beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED,
							SubStatus: beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DISCONNECTED,
						},
					},
				}, nil)
				md.vkycClient.EXPECT().GetVKYCInfo(context.Background(), gomock.Any()).Return(&beVkycPb.GetVKYCInfoResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &beVkycPb.VKYCRecord{
						VkycKarzaCustomerInfos: []*beVkycPb.VKYCKarzaCustomerInfo{
							{
								TransactionMetadata: &beVkycPb.VKYCKarzaCustomerInfoTransactionMetadata{
									Weblink: "abc",
									KycDate: now,
								},
							},
						},
					},
				}, nil)
			},
			enableRetry: false,
		},
		{
			name: "call failed and new vkyc error screen enabled for photo unable to determine",
			args: args{
				ctx: context.Background(),
				req: &feVkycPb.GetCallExitRedirectDeeplinkRequest{
					EntryPoint: "ENTRY_POINT_CREDIT_LIMIT_BREACH",
				},
			},
			want: &feVkycPb.GetCallExitRedirectDeeplinkResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: dl1,
			},
			wantErr: false,
			wantMock: func(md *mockDependencies) {
				md.vkycClient.EXPECT().GetCallExitRedirectDeeplink(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallExitRedirectDeeplinkResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.vkycClient.EXPECT().GetCallStatus(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallStatusResponse{
					Status: rpc.StatusOk(),
					CallStatus: []*beVkycPb.KarzaCallStatus{
						{
							Status:    beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED,
							SubStatus: beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PHOTO_UNABLE_TO_DETERMINE,
						},
					},
				}, nil)
				md.vkycClient.EXPECT().GetVKYCInfo(context.Background(), gomock.Any()).Return(&beVkycPb.GetVKYCInfoResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &beVkycPb.VKYCRecord{
						VkycKarzaCustomerInfos: []*beVkycPb.VKYCKarzaCustomerInfo{
							{
								TransactionMetadata: &beVkycPb.VKYCKarzaCustomerInfoTransactionMetadata{
									Weblink: "abc",
									KycDate: now,
								},
							},
						},
					},
				}, nil)
			},
			enableRetry: false,
		},
		{
			name: "call failed due to language barrier",
			args: args{
				ctx: context.Background(),
				req: &feVkycPb.GetCallExitRedirectDeeplinkRequest{
					EntryPoint: "ENTRY_POINT_ONBOARDING_STUDENT",
				},
			},
			want: &feVkycPb.GetCallExitRedirectDeeplinkResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: dl6,
			},
			wantErr: false,
			wantMock: func(md *mockDependencies) {
				md.vkycClient.EXPECT().GetCallExitRedirectDeeplink(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallExitRedirectDeeplinkResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.vkycClient.EXPECT().GetCallStatus(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallStatusResponse{
					Status: rpc.StatusOk(),
					CallStatus: []*beVkycPb.KarzaCallStatus{
						{
							Status:    beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED,
							SubStatus: beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_LANGUAGE_BARRIER,
						},
					},
				}, nil)
				md.vkycClient.EXPECT().GetVKYCInfo(context.Background(), gomock.Any()).Return(&beVkycPb.GetVKYCInfoResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &beVkycPb.VKYCRecord{
						VkycSummary: &beVkycPb.VKYCSummary{
							Metadata: &beVkycPb.SummaryMetadata{
								PreferredLanguages: []types.Language{types.Language_LANGUAGE_HINDI},
							},
						},
						VkycKarzaCustomerInfos: []*beVkycPb.VKYCKarzaCustomerInfo{
							{
								TransactionMetadata: &beVkycPb.VKYCKarzaCustomerInfoTransactionMetadata{
									Weblink: "abc",
									KycDate: now,
								},
							},
						},
					},
				}, nil)
			},
			enableRetry: false,
		},
		{
			name: "call failed and new vkyc error screen enabled for customer without pan",
			args: args{
				ctx: context.Background(),
				req: &feVkycPb.GetCallExitRedirectDeeplinkRequest{
					EntryPoint: "ENTRY_POINT_CREDIT_LIMIT_BREACH",
				},
			},
			want: &feVkycPb.GetCallExitRedirectDeeplinkResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: dl2,
			},
			wantErr: false,
			wantMock: func(md *mockDependencies) {
				md.vkycClient.EXPECT().GetCallExitRedirectDeeplink(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallExitRedirectDeeplinkResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.vkycClient.EXPECT().GetCallStatus(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallStatusResponse{
					Status: rpc.StatusOk(),
					CallStatus: []*beVkycPb.KarzaCallStatus{
						{
							Status:    beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED,
							SubStatus: beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_WITHOUT_PAN,
						},
					},
				}, nil)
				md.vkycClient.EXPECT().GetVKYCInfo(context.Background(), gomock.Any()).Return(&beVkycPb.GetVKYCInfoResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &beVkycPb.VKYCRecord{
						VkycKarzaCustomerInfos: []*beVkycPb.VKYCKarzaCustomerInfo{
							{
								TransactionMetadata: &beVkycPb.VKYCKarzaCustomerInfoTransactionMetadata{
									Weblink: "abc",
									KycDate: now,
								},
							},
						},
					},
				}, nil)
			},
			enableRetry: false,
		},
		{
			name: "call failed and new vkyc error screen enabled for customer without pan and user is during onboarding",
			args: args{
				ctx: context.Background(),
				req: &feVkycPb.GetCallExitRedirectDeeplinkRequest{
					EntryPoint: "ENTRY_POINT_ONBOARDING_STUDENT",
				},
			},
			want: &feVkycPb.GetCallExitRedirectDeeplinkResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: dl3,
			},
			wantErr: false,
			wantMock: func(md *mockDependencies) {
				md.vkycClient.EXPECT().GetCallExitRedirectDeeplink(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallExitRedirectDeeplinkResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.vkycClient.EXPECT().GetCallStatus(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallStatusResponse{
					Status: rpc.StatusOk(),
					CallStatus: []*beVkycPb.KarzaCallStatus{
						{
							Status:    beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED,
							SubStatus: beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_WITHOUT_PAN,
						},
					},
				}, nil)
				md.vkycClient.EXPECT().GetVKYCInfo(context.Background(), gomock.Any()).Return(&beVkycPb.GetVKYCInfoResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &beVkycPb.VKYCRecord{
						VkycKarzaCustomerInfos: []*beVkycPb.VKYCKarzaCustomerInfo{
							{
								TransactionMetadata: &beVkycPb.VKYCKarzaCustomerInfoTransactionMetadata{
									Weblink: "abc",
									KycDate: now,
								},
							},
						},
					},
				}, nil)
			},
			enableRetry: false,
		},
		{
			name: "call failed and new vkyc error screen enabled with retry cta and user is during onboarding",
			args: args{
				ctx: context.Background(),
				req: &feVkycPb.GetCallExitRedirectDeeplinkRequest{
					EntryPoint: "ENTRY_POINT_ONBOARDING_STUDENT",
				},
			},
			want: &feVkycPb.GetCallExitRedirectDeeplinkResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: dl4,
			},
			wantErr: false,
			wantMock: func(md *mockDependencies) {
				md.vkycClient.EXPECT().GetCallExitRedirectDeeplink(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallExitRedirectDeeplinkResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.vkycClient.EXPECT().GetCallStatus(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallStatusResponse{
					Status: rpc.StatusOk(),
					CallStatus: []*beVkycPb.KarzaCallStatus{
						{
							Status:    beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED,
							SubStatus: beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_MOVING_CONSTANTLY,
						},
					},
				}, nil)
				md.vkycClient.EXPECT().GetVKYCInfo(context.Background(), gomock.Any()).Return(&beVkycPb.GetVKYCInfoResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &beVkycPb.VKYCRecord{
						VkycKarzaCustomerInfos: []*beVkycPb.VKYCKarzaCustomerInfo{
							{
								TransactionMetadata: &beVkycPb.VKYCKarzaCustomerInfoTransactionMetadata{
									Weblink: "abc",
									KycDate: now,
								},
							},
						},
					},
				}, nil)
			},
			enableRetry: false,
		},
		{
			name: "call failed and new vkyc error screen enabled with faq cta cta",
			args: args{
				ctx: context.Background(),
				req: &feVkycPb.GetCallExitRedirectDeeplinkRequest{
					EntryPoint: "ENTRY_POINT_CREDIT_LIMIT_BREACH",
				},
			},
			want: &feVkycPb.GetCallExitRedirectDeeplinkResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: dl5,
			},
			wantErr: false,
			wantMock: func(md *mockDependencies) {
				md.vkycClient.EXPECT().GetCallExitRedirectDeeplink(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallExitRedirectDeeplinkResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.vkycClient.EXPECT().GetCallStatus(context.Background(), gomock.Any()).Return(&beVkycPb.GetCallStatusResponse{
					Status: rpc.StatusOk(),
					CallStatus: []*beVkycPb.KarzaCallStatus{
						{
							Status:    beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED,
							SubStatus: beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_IS_FRAUDULENT,
						},
					},
				}, nil)
				md.vkycClient.EXPECT().GetVKYCInfo(context.Background(), gomock.Any()).Return(&beVkycPb.GetVKYCInfoResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &beVkycPb.VKYCRecord{
						VkycKarzaCustomerInfos: []*beVkycPb.VKYCKarzaCustomerInfo{
							{
								TransactionMetadata: &beVkycPb.VKYCKarzaCustomerInfoTransactionMetadata{
									Weblink: "abc",
									KycDate: now,
								},
							},
						},
					},
				}, nil)
			},
			enableRetry: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockVkycClient := beVkycPbMocks.NewMockVKYCClient(ctrl)
			mockPanClient := mocks2.NewMockPanClient(ctrl)

			md := &mockDependencies{
				vkycClient: mockVkycClient,
				panClient:  mockPanClient,
			}
			if tt.wantMock != nil {
				tt.wantMock(md)
			}

			s := &Service{
				beVkycClient: mockVkycClient,
				genConf:      genConf,
				panClient:    mockPanClient,
			}
			_ = s.genConf.VKYC().SetEnableCallRetry(tt.enableRetry, true, []string{})
			_ = s.genConf.VKYC().SetFAQCategoryId(conf.VKYC.FAQCategoryId, true, []string{})
			got, err := s.GetCallExitRedirectDeeplink(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCallExitRedirectDeeplink() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetCallExitRedirectDeeplink() got = %v, want %v", got, tt.want)
			}
		})
	}
}
