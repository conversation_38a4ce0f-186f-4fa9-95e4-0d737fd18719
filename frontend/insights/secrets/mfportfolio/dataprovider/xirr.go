package dataprovider

import (
	"context"
	"fmt"
	"math"

	"github.com/epifi/be-common/pkg/money"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	secretsModelPb "github.com/epifi/gamma/api/insights/secrets/model"
	"github.com/epifi/gamma/frontend/insights/secrets/mfportfolio/adapters"
	"github.com/epifi/gamma/frontend/insights/secrets/valuegenerator"
	"github.com/epifi/gamma/pkg/investment"
)

type XirrMetricDataProvider struct {
	*BaseMfDataProvider
}

func NewXirrMetricDataProvider(secretCfg *secretsModelPb.Secret, dataProviderReq *DataProviderRequest) *XirrMetricDataProvider {
	return &XirrMetricDataProvider{
		BaseMfDataProvider: NewBaseMfDataProvider(secretCfg, dataProviderReq),
	}
}

type XirrBarCharData struct {
	primaryTitle  string
	secondaryData string
	barChartItems []valuegenerator.BarChartItemData
}

func (x *XirrBarCharData) GetPrimaryValue() string {
	return x.primaryTitle
}

func (x *XirrBarCharData) GetSecondaryValue() string {
	return x.secondaryData
}

func (x *XirrBarCharData) GetBarChartItemsData() []valuegenerator.BarChartItemData {
	return x.barChartItems
}

func (x *XirrBarCharData) GetBenchmarkValue() float64 {
	return math.MaxFloat64
}

func (x *XirrMetricDataProvider) GetBarChartData(ctx context.Context) (valuegenerator.BarChartData, error) {
	allSchemes := x.filterSchemaAnalytics
	var (
		topSchemes = allSchemes
	)
	if len(allSchemes) > 5 {
		topSchemes = allSchemes[:5]
	}
	if len(allSchemes) > 5 {
		topSchemes = allSchemes[:5]
	}

	barChartItem := make([]valuegenerator.BarChartItemData, 0)
	for _, scheme := range topSchemes {
		xirr := scheme.SchemeAnalytics.EnrichedAnalytics.GetAnalytics().GetSchemeDetails().GetXIRR().GetValue()
		xirrDisplayValue := fmt.Sprintf("%.2f%%", xirr)
		barChartItem = append(barChartItem, &XirrBarItemData{
			barValue:         xirr,
			barDisplayString: xirrDisplayValue,
			bottomIconUrl:    investment.IconsForAmc[scheme.SchemeAnalytics.SchemeDetail.GetAmc()],
			barDeeplink:      BuildSchemeDetailsBottomSheet(ctx, scheme),
		})
	}
	primaryTitle := "-"
	secondryTitle := "-"
	if len(allSchemes) > 0 {
		primaryTitle = allSchemes[0].SchemeDetail.GetNameData().GetShortName()
		secondryTitle = allSchemes[0].GetSecretMetricDisplayValue(x.secretCfg.GetSecretBuilderDataConfig().GetMfPortfolioSecretDataConfig().GetSecretMetric())
	}
	return &XirrBarCharData{
		primaryTitle:  primaryTitle,
		secondaryData: secondryTitle,
		barChartItems: barChartItem,
	}, nil
}

type XirrBarItemData struct {
	barValue         float64
	barDisplayString string
	bottomIconUrl    string
	barDeeplink      *deeplinkPb.Deeplink
}

func (x *XirrBarItemData) GetTopValue() string {
	return x.barDisplayString
}

func (x *XirrBarItemData) GetBottomIconUrl() string {
	return x.bottomIconUrl
}

func (x *XirrBarItemData) GetBarValue() float64 {
	return x.barValue
}

func (x *XirrBarItemData) GetDeeplinkForBar() *deeplinkPb.Deeplink {
	return x.barDeeplink
}

func (x *XirrBarItemData) GetBarColor() string {
	return ""
}

type XirrLineItemsData struct {
	scheme       *adapters.MFSecretsSchemeAnalytics
	secretConfig *secretsModelPb.Secret
	deeplink     *deeplinkPb.Deeplink
}

func (x *XirrMetricDataProvider) GetLineItemsData(ctx context.Context) ([]LineItemData, error) {
	var lineItemData []LineItemData
	allSchemes := x.filterSchemaAnalytics
	for _, scheme := range allSchemes {
		lineItemData = append(lineItemData, &XirrLineItemsData{
			scheme:       scheme,
			secretConfig: x.secretCfg,
			deeplink:     BuildSchemeDetailsBottomSheet(ctx, scheme),
		})
	}
	return lineItemData, nil
}

func (x *XirrLineItemsData) GetLeftHeading() string {
	return x.scheme.SchemeAnalytics.SchemeDetail.GetNameData().GetShortName()
}

func (x *XirrLineItemsData) GetLeftTag() string {
	return ""
}

func (x *XirrLineItemsData) GetRightHeading() string {
	if x.scheme.EnrichedAnalytics.GetAnalytics().GetSchemeDetails().GetXIRR() == nil {
		return "-"
	}
	return fmt.Sprintf("%.2f%%", x.scheme.SchemeAnalytics.EnrichedAnalytics.GetAnalytics().GetSchemeDetails().GetXIRR().GetValue())
}

func (x *XirrLineItemsData) GetRightTag() string {
	return money.ToDisplayStringWithSuffixAndPrecisionV2(x.scheme.EnrichedAnalytics.GetAnalytics().GetSchemeDetails().GetCurrentValue(), true, true, 1, true, money.IndianNumberSystem)
}

func (x *XirrLineItemsData) GetIconUrl() string {
	return investment.IconsForAmc[x.scheme.SchemeAnalytics.SchemeDetail.GetAmc()]
}

func (x *XirrLineItemsData) GetDeeplink() *deeplinkPb.Deeplink {
	return x.deeplink
}
