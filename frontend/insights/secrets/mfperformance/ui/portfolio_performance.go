package ui

import (
	"context"
	"fmt"
	"math"
	"sort"

	"github.com/epifi/be-common/pkg/colors"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	"github.com/epifi/gamma/api/analyser/variables/mutualfund"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	secretsConfigPb "github.com/epifi/gamma/api/insights/secrets/config"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/secrets"
	secretsUiPb "github.com/epifi/gamma/api/typesv2/ui/insights/secrets"
	"github.com/epifi/gamma/frontend/insights/calculator"
	secretColors "github.com/epifi/gamma/frontend/insights/secrets/colors"
)

var (
	barColors = []string{secretColors.ColorSupportingAmber200, secretColors.ColorSupportingAmber400Opacity60, secretColors.ColorSupportingAmber700, colors.ColorSupportingAmber900}
)

const (
	FDDisplayName          = "Fixed Deposit"
	Nifty50DisplayName     = "Nifty50"
	PeersDisplayName       = "Peers"
	CAvgDisplayName        = "Category avg"
	YourReturnsDisplayName = "Your returns"
)

type MfPortfolioPerformanceComponentBuilder struct{}

func NewMfPortfolioPerformanceComponentBuilder() *MfPortfolioPerformanceComponentBuilder {
	return &MfPortfolioPerformanceComponentBuilder{}
}

// nolint: dupl
func (m *MfPortfolioPerformanceComponentBuilder) BuildSummaryComponent(ctx context.Context, req *BuildSummaryComponentRequest) (*secretsFePb.SecretSummary, error) {
	variable, ok := req.Variables[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_PORTFOLIO_PERFORMANCE]
	if !ok {
		return nil, fmt.Errorf("failed to find analysis variable %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_PORTFOLIO_PERFORMANCE)
	}
	portfolioPerformanceDetails := variable.GetMfPortfolioPerformanceDetails()
	summaryTitle := m.getTitleStringForSummaryComponent(portfolioPerformanceDetails)
	attentionScore := m.getAttentionScore(portfolioPerformanceDetails.GetUserPercentageReturns(), portfolioPerformanceDetails.GetCategoryAverageReturns())
	return &secretsFePb.SecretSummary{
		SummaryUi: &secretsFePb.SecretSummary_SecretSummaryCard{
			SecretSummaryCard: secretsUiPb.NewSecretSummaryCardBuilder(req.SecretConfig.GetSecretSummaryUiConfig().GetSecretSummaryCardConfiguration()).
				SetVisibleValue(fmt.Sprintf("%s", summaryTitle)).
				SetDeeplink(secrets.GetSecretAnalyserScreenDeeplink(req.SecretConfig.GetName())).
				Build(),
		},
		AttentionScore: attentionScore,
	}, nil
}

func (m *MfPortfolioPerformanceComponentBuilder) BuildComponent(ctx context.Context, req *BuildComponentRequest) (*secretsFePb.SecretAnalyserComponent, error) {
	variable, ok := req.Variables[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_PORTFOLIO_PERFORMANCE]
	if !ok {
		return nil, fmt.Errorf("failed to find analysis variable %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_PORTFOLIO_PERFORMANCE)
	}
	portfolioPerformanceDetails := variable.GetMfPortfolioPerformanceDetails()

	portfolioPerformanceDetailsList := m.getPortfolioPerformanceList(portfolioPerformanceDetails)
	switch req.Config.GetComponentName() {
	case MfPortfolioPerformanceBarChart:
		return m.buildBarChart(req.Config, portfolioPerformanceDetails, portfolioPerformanceDetailsList)
	case MfPortfolioPerformanceLineItems:
		return m.buildLineItems(req.Config, portfolioPerformanceDetailsList)
	default:
		return nil, nil
	}
}

func (m *MfPortfolioPerformanceComponentBuilder) buildBarChart(config *secretsConfigPb.SecretAnalyserUIConfig, portfolioPerformanceDetails *mutualfund.MfPortfolioPerformanceDetails, portfolioPerformanceDetailsList []*MfPerformanceUIData) (*secretsFePb.SecretAnalyserComponent, error) {
	barChartCardConfig := config.GetVisualisationCardConfig().GetBarChartCardConfig()

	secretAnalyserBarChart := secretsUiPb.NewSecretsAnalyserBarChartCard(barChartCardConfig).AddTitleValuePair(m.getTitleStringForSummaryComponent(portfolioPerformanceDetails))
	for _, portfolioPerformance := range portfolioPerformanceDetailsList {
		secretAnalyserBarChart.AddBarItem(secretsUiPb.NewBarItemBuilder(barChartCardConfig.GetBarChartConfig().GetDefaultBarConfig()).
			AddBarValue(portfolioPerformance.GetAllocationPercentage()).
			SetDisplayTextInTopComponent(calculator.GetPercentageReturnsString(portfolioPerformance.GetAllocationPercentage())).
			SetBarColor(portfolioPerformance.GetSolidColor()).
			Build())
	}

	return secretsFePb.NewSecretAnalyserComponent(&secretsFePb.SecretsAnalyserCard{
		Visualisation: &secretsFePb.SecretsAnalyserCard_BarChartCard{
			BarChartCard: secretAnalyserBarChart.Build(),
		},
	}, config), nil
}

func (m *MfPortfolioPerformanceComponentBuilder) buildLineItems(config *secretsConfigPb.SecretAnalyserUIConfig, portfolioPerformanceDetailsList []*MfPerformanceUIData) (*secretsFePb.SecretAnalyserComponent, error) {
	lineItemsConfig := config.GetLineItemsConfig()
	lineItemsBuilder := secretsFePb.NewLineItemCardWithAnalyserFilter(lineItemsConfig)

	for _, portfolioPerformance := range portfolioPerformanceDetailsList {
		lineItemsBuilder.AddLineItem(secretsFePb.NewLineItemBuilder(lineItemsConfig.GetLineItemConfig()).
			SetLeftHeading(portfolioPerformance.GetDisplayName()).
			SetRightHeading(calculator.GetPercentageReturnsString(portfolioPerformance.GetAllocationPercentage())).
			SetSolidColorForLeftIcon(portfolioPerformance.GetSolidColor()).Build())
	}

	return secretsFePb.NewSecretAnalyserComponent(lineItemsBuilder.Build(), config), nil
}

func (m *MfPortfolioPerformanceComponentBuilder) getTitleStringForSummaryComponent(portfolioPerformanceDetails *mutualfund.MfPortfolioPerformanceDetails) string {
	catAvg := portfolioPerformanceDetails.GetCategoryAverageReturns()
	yourReturns := portfolioPerformanceDetails.GetUserPercentageReturns()
	if yourReturns == 0 {
		return "--"
	}
	if catAvg > yourReturns {
		return fmt.Sprintf("%.2f%% lower", catAvg-yourReturns)
	}
	return fmt.Sprintf("%.2f%% higher", yourReturns-catAvg)
}

func (m *MfPortfolioPerformanceComponentBuilder) getPortfolioPerformanceList(portfolioPerformanceDetails *mutualfund.MfPortfolioPerformanceDetails) []*MfPerformanceUIData {
	var portfolioPerformanceUiDataList []*MfPerformanceUIData

	portfolioPerformanceUiDataList = append(portfolioPerformanceUiDataList,
		&MfPerformanceUIData{
			DisplayName:          FDDisplayName,
			AllocationPercentage: portfolioPerformanceDetails.GetFixedDepositPercentageReturns(),
		},
		&MfPerformanceUIData{
			DisplayName:          Nifty50DisplayName,
			AllocationPercentage: portfolioPerformanceDetails.GetNifty50PercentageReturns(),
		},
		&MfPerformanceUIData{
			DisplayName:          PeersDisplayName,
			AllocationPercentage: portfolioPerformanceDetails.GetPeersPercentageReturns(),
		},
		&MfPerformanceUIData{
			DisplayName:          CAvgDisplayName,
			AllocationPercentage: portfolioPerformanceDetails.GetCategoryAverageReturns(),
		},
	)

	sort.Slice(portfolioPerformanceUiDataList, func(i, j int) bool {
		return portfolioPerformanceUiDataList[i].GetAllocationPercentage() < portfolioPerformanceUiDataList[j].GetAllocationPercentage()
	})

	for i, portfolioPerformance := range portfolioPerformanceUiDataList {
		barColor := colors.ColorSupportingAmber900
		if len(barColors) > i {
			barColor = barColors[i]
		}
		portfolioPerformance.SolidColor = barColor
	}

	portfolioPerformanceUiDataList = append(portfolioPerformanceUiDataList, &MfPerformanceUIData{
		DisplayName:          YourReturnsDisplayName,
		AllocationPercentage: portfolioPerformanceDetails.GetUserPercentageReturns(),
		SolidColor:           colors.ColorLightPrimaryAction,
	})

	sort.Slice(portfolioPerformanceUiDataList, func(i, j int) bool {
		return portfolioPerformanceUiDataList[i].AllocationPercentage < portfolioPerformanceUiDataList[j].AllocationPercentage
	})

	return portfolioPerformanceUiDataList
}

func (m *MfPortfolioPerformanceComponentBuilder) getAttentionScore(yourReturnsPercentage float64, categoryAvgPercentage float64) float64 {
	if categoryAvgPercentage == 0 {
		return 0
	}
	var attentionScore float64
	if yourReturnsPercentage < categoryAvgPercentage {
		attentionScore = math.Max(-1, (yourReturnsPercentage-categoryAvgPercentage)/10)
	} else {
		attentionScore = math.Min(1, (yourReturnsPercentage-categoryAvgPercentage)/10)
	}
	return attentionScore
}
