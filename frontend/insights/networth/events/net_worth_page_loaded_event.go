// nolint: dupl
package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

const (
	EventNetWorthPageLoaded = "NetWorthPageLoadedEvent"
)

type NetWorthPageLoadedEvent struct {
	EventId    string
	ProspectId string
	ActorID    string
	SessionId  string
	AttemptId  string
	EventType  string
	DeviceId   string
	Timestamp  time.Time
}

func NewNetWorthPageLoadedEvent(actorId string) *NetWorthPageLoadedEvent {
	return &NetWorthPageLoadedEvent{
		ActorID:   actorId,
		EventId:   uuid.New().String(),
		Timestamp: time.Now(),
		EventType: events.EventTrack,
	}
}

func (s *NetWorthPageLoadedEvent) GetEventType() string {
	return events.EventTrack
}

func (s *NetWorthPageLoadedEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *NetWorthPageLoadedEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *NetWorthPageLoadedEvent) GetEventId() string {
	return s.EventId
}

func (s *NetWorthPageLoadedEvent) GetUserId() string {
	return s.ActorID
}

func (s *NetWorthPageLoadedEvent) GetProspectId() string {
	return s.ProspectId
}

func (s *NetWorthPageLoadedEvent) GetEventName() string {
	return EventNetWorthPageLoaded
}
