package utils

import (
	"github.com/epifi/gamma/api/insights/networth"
	types "github.com/epifi/gamma/api/typesv2"
)

var (
	AssetTypeToInstrumentTypeMap = map[networth.AssetType]types.InvestmentInstrumentType{
		networth.AssetType_ASSET_TYPE_AIF:                          types.InvestmentInstrumentType_AIF,
		networth.AssetType_ASSET_TYPE_PRIVATE_EQUITY:               types.InvestmentInstrumentType_PRIVATE_EQUITY,
		networth.AssetType_ASSET_TYPE_REAL_ESTATE:                  types.InvestmentInstrumentType_REAL_ESTATE,
		networth.AssetType_ASSET_TYPE_ART_ARTEFACTS:                types.InvestmentInstrumentType_ART_AND_ARTEFACTS,
		networth.AssetType_ASSET_TYPE_BONDS:                        types.InvestmentInstrumentType_BOND,
		networth.AssetType_ASSET_TYPE_CASH:                         types.InvestmentInstrumentType_CASH,
		networth.AssetType_ASSET_TYPE_DIGITAL_GOLD:                 types.InvestmentInstrumentType_DIGITAL_GOLD,
		networth.AssetType_ASSET_TYPE_DIGITAL_SILVER:               types.InvestmentInstrumentType_DIGITAL_SILVER,
		networth.AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE: types.InvestmentInstrumentType_PORTFOLIO_MANAGEMENT_SERVICE,
		networth.AssetType_ASSET_TYPE_EMPLOYEE_STOCK_OPTION:        types.InvestmentInstrumentType_EMPLOYEE_STOCK_OPTION,
		networth.AssetType_ASSET_TYPE_GADGETS:                      types.InvestmentInstrumentType_GADGETS,
		networth.AssetType_ASSET_TYPE_VEHICLES:                     types.InvestmentInstrumentType_VEHICLES,
		networth.AssetType_ASSET_TYPE_CRYPTO:                       types.InvestmentInstrumentType_CRYPTO,
		networth.AssetType_ASSET_TYPE_FURNITURE:                    types.InvestmentInstrumentType_FURNITURE,
		networth.AssetType_ASSET_TYPE_COLLECTIBLES:                 types.InvestmentInstrumentType_COLLECTIBLES,
		networth.AssetType_ASSET_TYPE_JEWELLERY:                    types.InvestmentInstrumentType_JEWELLERY,
		networth.AssetType_ASSET_TYPE_OTHERS:                       types.InvestmentInstrumentType_INVESTMENT_INSTRUMENT_TYPE_OTHERS,
		networth.AssetType_ASSET_TYPE_INDIAN_SECURITIES:            types.InvestmentInstrumentType_INDIAN_STOCKS,
		networth.AssetType_ASSET_TYPE_MUTUAL_FUND:                  types.InvestmentInstrumentType_MUTUAL_FUNDS,
		networth.AssetType_ASSET_TYPE_PUBLIC_PROVIDENT_FUND:        types.InvestmentInstrumentType_PUBLIC_PROVIDENT_FUND,
		networth.AssetType_ASSET_TYPE_NPS:                          types.InvestmentInstrumentType_NPS,
	}
	InstrumentTypeToAssetTypeMap = map[types.InvestmentInstrumentType]networth.AssetType{
		types.InvestmentInstrumentType_AIF:                               networth.AssetType_ASSET_TYPE_AIF,
		types.InvestmentInstrumentType_PRIVATE_EQUITY:                    networth.AssetType_ASSET_TYPE_PRIVATE_EQUITY,
		types.InvestmentInstrumentType_REAL_ESTATE:                       networth.AssetType_ASSET_TYPE_REAL_ESTATE,
		types.InvestmentInstrumentType_ART_AND_ARTEFACTS:                 networth.AssetType_ASSET_TYPE_ART_ARTEFACTS,
		types.InvestmentInstrumentType_BOND:                              networth.AssetType_ASSET_TYPE_BONDS,
		types.InvestmentInstrumentType_CASH:                              networth.AssetType_ASSET_TYPE_CASH,
		types.InvestmentInstrumentType_DIGITAL_GOLD:                      networth.AssetType_ASSET_TYPE_DIGITAL_GOLD,
		types.InvestmentInstrumentType_DIGITAL_SILVER:                    networth.AssetType_ASSET_TYPE_DIGITAL_SILVER,
		types.InvestmentInstrumentType_PORTFOLIO_MANAGEMENT_SERVICE:      networth.AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE,
		types.InvestmentInstrumentType_EMPLOYEE_STOCK_OPTION:             networth.AssetType_ASSET_TYPE_EMPLOYEE_STOCK_OPTION,
		types.InvestmentInstrumentType_FIXED_DEPOSIT:                     networth.AssetType_ASSET_TYPE_FIXED_DEPOSITS,
		types.InvestmentInstrumentType_RECURRING_DEPOSIT:                 networth.AssetType_ASSET_TYPE_FIXED_DEPOSITS,
		types.InvestmentInstrumentType_GADGETS:                           networth.AssetType_ASSET_TYPE_GADGETS,
		types.InvestmentInstrumentType_VEHICLES:                          networth.AssetType_ASSET_TYPE_VEHICLES,
		types.InvestmentInstrumentType_CRYPTO:                            networth.AssetType_ASSET_TYPE_CRYPTO,
		types.InvestmentInstrumentType_FURNITURE:                         networth.AssetType_ASSET_TYPE_FURNITURE,
		types.InvestmentInstrumentType_COLLECTIBLES:                      networth.AssetType_ASSET_TYPE_COLLECTIBLES,
		types.InvestmentInstrumentType_JEWELLERY:                         networth.AssetType_ASSET_TYPE_JEWELLERY,
		types.InvestmentInstrumentType_INVESTMENT_INSTRUMENT_TYPE_OTHERS: networth.AssetType_ASSET_TYPE_OTHERS,
		types.InvestmentInstrumentType_INDIAN_STOCKS:                     networth.AssetType_ASSET_TYPE_INDIAN_SECURITIES,
		types.InvestmentInstrumentType_MUTUAL_FUNDS:                      networth.AssetType_ASSET_TYPE_MUTUAL_FUND,
		types.InvestmentInstrumentType_PUBLIC_PROVIDENT_FUND:             networth.AssetType_ASSET_TYPE_PUBLIC_PROVIDENT_FUND,
		types.InvestmentInstrumentType_NPS:                               networth.AssetType_ASSET_TYPE_NPS,
	}
)
