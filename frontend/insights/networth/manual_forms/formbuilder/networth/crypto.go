package networth

import (
	"context"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/money"

	goUtils "github.com/epifi/be-common/pkg/go_utils"
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthBeFePb "github.com/epifi/gamma/api/insights/networth/frontend"
	networthBeModelPb "github.com/epifi/gamma/api/insights/networth/model"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/inputbuilder"
)

type Crypto struct {
}

func NewCrypto() *Crypto {
	return &Crypto{}
}

// nolint: dupl
func (a *Crypto) BuildForm(ctx context.Context, req *networthBeFePb.BuildFormRequest) (*networthFePb.NetWorthManualForm, error) {
	decl := req.GetInvestmentDeclaration().GetDeclarationDetails().GetCrypto()
	investmentName := inputbuilder.NewStringBuilder("INVESTMENT NAME", "Investment name", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_NAME.String())
	investedValue := inputbuilder.NewInt64Builder("INVESTED VALUE", "Invested Value", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTED_VALUE.String())
	currentValue := inputbuilder.NewInt64Builder("CURRENT VALUE", "Current Value", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_CURRENT_VALUE.String())
	numberOfUnits := inputbuilder.NewOptionalDoubleBuilder("NUMBER OF UNITS", "Number of Units", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_NUM_UNITS.String())
	currentValuePerUnit := inputbuilder.NewOptionalInt64Builder("CURRENT VALUE PER UNIT", "Current Value Per Unit", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_NAME_CURRENT_VALUE_PER_UNIT.String())
	if decl != nil {
		investmentName.WithValue(decl.GetInvestmentName())
		investedValue.WithValue(decl.GetInvestedValue().GetUnits())
		currentValue.WithValue(decl.GetCurrentValue().GetUnits())
		numberOfUnits.WithValue(decl.GetNumberOfUnits())
		currentValuePerUnit.WithValue(decl.GetCurrentValuePerUnit().GetUnits())
	}

	inputSection := networthFePb.NewNetWorthManualFormComponentsSection("Investment Details").
		WithInputComponent(investmentName.Build()).
		WithInputComponent(investedValue.Build()).
		WithInputComponent(currentValue.Build()).
		WithInputComponent(numberOfUnits.Build()).
		WithInputComponent(currentValuePerUnit.Build())

	form := networthFePb.NewNetWorthManualForm("Crypto Assets", "Add Investment").
		WithComponentsSection(inputSection)
	// If the data has already been entered by the user, display the option to delete it.
	if decl != nil {
		form = form.WithActionCta(networthFePb.NewAssetActionButton().
			WithActionType(networthFePb.AssetActionType_ASSET_ACTION_TYPE_DELETE).
			WithDisplayText(ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Remove", "#AA301F", commontypes.FontStyle_BUTTON_S)).
				WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/networth/delete_icon_manual_asset.png", 13, 13)).
				WithLeftImagePadding(8)))
	}
	return form, nil
}

func (a *Crypto) ConvertFormInputToInvestmentDeclaration(ctx context.Context, inputComponents []*networthFePb.NetWorthManualFormInputComponent) (*networthBeModelPb.InvestmentDeclaration, error) {
	details := &networthBeModelPb.Crypto{}
	decl := &networthBeModelPb.InvestmentDeclaration{
		InstrumentType: typesPb.InvestmentInstrumentType_CRYPTO,
		DeclarationDetails: &networthBeModelPb.OtherDeclarationDetails{
			Details: &networthBeModelPb.OtherDeclarationDetails_Crypto{
				Crypto: details,
			},
		},
	}

	for _, component := range inputComponents {
		data := component.GetInputData()
		inputValue := data.GetInputValueFromSingleOption()
		fieldName := goUtils.Enum(data.GetFieldName(), networthFePb.NetworthManualFormFieldName_value, networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_UNSPECIFIED)
		switch fieldName {
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_NAME:
			details.InvestmentName = inputValue.GetStringData().GetData().GetValue()
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTED_VALUE:
			details.InvestedValue = money.AmountINR(inputValue.GetInt64Data().GetData().GetValue()).GetPb()
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_NUM_UNITS:
			details.NumberOfUnits = inputValue.GetDoubleData().GetData().GetValue()
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_CURRENT_VALUE:
			details.CurrentValue = money.AmountINR(inputValue.GetInt64Data().GetData().GetValue()).GetPb()
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_NAME_CURRENT_VALUE_PER_UNIT:
			details.CurrentValuePerUnit = money.AmountINR(inputValue.GetInt64Data().GetData().GetValue()).GetPb()
		}
	}

	return decl, nil
}
