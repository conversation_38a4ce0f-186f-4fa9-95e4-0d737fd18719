package scrollable

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/syncmap"

	headerPb "github.com/epifi/gamma/api/frontend/header"
	homeFePb "github.com/epifi/gamma/api/frontend/home"
	homeOrchFePb "github.com/epifi/gamma/api/frontend/home/<USER>"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/home/<USER>"
	"github.com/epifi/gamma/frontend/insights/networth/wealth_builder_components/scrollable/dashboard"
	"github.com/epifi/gamma/frontend/insights/secrets/colors"
)

var (
	dashboardViews = []string{constants.EpfDashboardComponentId.String(), constants.MutualfundDashboardComponentId.String(), constants.CreditScoreDashboardComponentId.String(), constants.NetworthDashboardComponentId.String()}
)

type DashboardComponent struct {
	dashboardComponentViewGetterFactory dashboard.IWealthDashboardComponentViewGetterFactory
}

func NewDashboardComponent(dashboardComponentViewGetterFactory dashboard.IWealthDashboardComponentViewGetterFactory) *DashboardComponent {
	return &DashboardComponent{
		dashboardComponentViewGetterFactory: dashboardComponentViewGetterFactory,
	}
}

func (d *DashboardComponent) GetWealthScrollableComponent(ctx context.Context, request *headerPb.RequestHeader) (*homeOrchFePb.ScrollableComponent, error) {
	dashboardComponentViewMap := &syncmap.Map[string, *homeOrchFePb.DashboardComponent_DashboardView]{}
	errGroup, gCtx := errgroup.WithContext(ctx)
	actorId := request.GetAuth().GetActorId()

	for _, view := range dashboardViews {
		errGroup.Go(func() error {
			dashboardComponentViewGetter, err := d.dashboardComponentViewGetterFactory.GetDashboardComponentViewGetter(gCtx, view)
			if err != nil {
				logger.Error(ctx, "error while fetching dashboard component getter", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
				return nil
			}
			dashboardComponentView, err := dashboardComponentViewGetter.GetWealthDashboardComponentView(gCtx, actorId)
			if err != nil {
				logger.Error(ctx, "error while fetching dashboard component view", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
				return nil
			}
			if dashboardComponentView == nil {
				return nil
			}
			dashboardComponentViewMap.Store(view, dashboardComponentView)
			return nil
		})
	}

	if err := errGroup.Wait(); err != nil {
		logger.Error(ctx, "error while fetching dashboard component views", zap.Error(err))
		return &homeOrchFePb.ScrollableComponent{
			Id:         constants.DashboardSectionComponentId.String(),
			WidgetType: homeFePb.HomeWidget_WIDGET_TYPE_DASHBOARD,
		}, nil
	}

	dashboardComponentsViewsList := make([]*homeOrchFePb.DashboardComponent_DashboardView, 0)
	for _, getDashboardView := range dashboardViews {
		if component, ok := dashboardComponentViewMap.Load(getDashboardView); ok {
			dashboardComponentsViewsList = append(dashboardComponentsViewsList, component)
		}
	}

	return &homeOrchFePb.ScrollableComponent{
		Id:         constants.DashboardSectionComponentId.String(),
		WidgetType: homeFePb.HomeWidget_WIDGET_TYPE_DASHBOARD,
		WidgetBg: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{
			BlockColour: colors.ColorDarkBase,
		}},
		Component: &homeOrchFePb.ScrollableComponent_DashboardComponent{
			DashboardComponent: &homeOrchFePb.DashboardComponent{
				DashboardViews: dashboardComponentsViewsList,
			},
		},
	}, nil
}
