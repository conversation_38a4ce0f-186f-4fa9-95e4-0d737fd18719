package store

import (
	"fmt"

	"google.golang.org/protobuf/proto"

	investmentAnalyserPb "github.com/epifi/gamma/api/analyser/investment"
	investmentAnalyserModelPb "github.com/epifi/gamma/api/analyser/investment/model"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
)

type MutualFundsTopFundsStore struct {
	schemes                  []*Scheme
	portfolioAnalytics       *investmentAnalyserPb.EnrichedMFPortfolioAnalytics
	holdingsImportStatus     mfExternalPb.State
	unknownScheme            *UnknownScheme
	refreshEligibilityStatus mfExternalPb.RequestEligibilityStatus
}

type Scheme struct {
	DisplayName       string
	IconUrl           string
	PlanType          mfPb.PlanType
	OptionType        mfPb.OptionType
	Returns           *mfPb.Returns
	EnrichedAnalytics *investmentAnalyserPb.EnrichedMfSchemeAnalytics
	Category          mfPb.MutualFundCategoryName
}

type UnknownScheme struct {
	DisplayName   string
	IconUrl       string
	SchemeDetails *investmentAnalyserModelPb.MfSchemeDetails
}

func (s *Scheme) DisplayPlanType() string {
	switch s.PlanType {
	case mfPb.PlanType_REGULAR:
		return "Regular"
	case mfPb.PlanType_DIRECT:
		return "Direct"
	default:
		return ""
	}
}

func (s *Scheme) DisplayOptionType() string {
	switch s.OptionType {
	case mfPb.OptionType_DIVIDEND:
		return "Dividend"
	case mfPb.OptionType_GROWTH:
		return "Growth"
	default:
		return ""
	}
}

func NewMutualFundsTopFundsStore(schemes []*Scheme, portfolioAnalytics *investmentAnalyserPb.EnrichedMFPortfolioAnalytics, importReqState mfExternalPb.State,
	unknownScheme *UnknownScheme, refreshEligibilityStatus mfExternalPb.RequestEligibilityStatus) *MutualFundsTopFundsStore {
	return &MutualFundsTopFundsStore{
		schemes:                  schemes,
		portfolioAnalytics:       portfolioAnalytics,
		holdingsImportStatus:     importReqState,
		unknownScheme:            unknownScheme,
		refreshEligibilityStatus: refreshEligibilityStatus,
	}
}

func (s *MutualFundsTopFundsStore) GetSchemes() ([]*Scheme, error) {
	schemes := make([]*Scheme, 0)
	for _, item := range s.schemes {
		clonedObj := proto.Clone(item.EnrichedAnalytics)
		clonedAnalytics, ok := clonedObj.(*investmentAnalyserPb.EnrichedMfSchemeAnalytics)
		if !ok {
			return nil, fmt.Errorf("error while cloning mf scheme analytics: not able to cast cloned object to type")
		}
		clonedObj = proto.Clone(item.Returns)
		clonedReturns, ok := clonedObj.(*mfPb.Returns)
		if !ok {
			return nil, fmt.Errorf("error while cloning mf returns: not able to cast cloned object to type")
		}
		schemes = append(schemes, &Scheme{
			DisplayName:       item.DisplayName,
			IconUrl:           item.IconUrl,
			PlanType:          item.PlanType,
			OptionType:        item.OptionType,
			EnrichedAnalytics: clonedAnalytics,
			Returns:           clonedReturns,
			Category:          item.Category,
		})
	}
	return schemes, nil
}

func (s *MutualFundsTopFundsStore) GetPortfolioAnalytics() (*investmentAnalyserPb.EnrichedMFPortfolioAnalytics, error) {
	clonedObj := proto.Clone(s.portfolioAnalytics)
	clonedAnalytics, ok := clonedObj.(*investmentAnalyserPb.EnrichedMFPortfolioAnalytics)
	if !ok {
		return nil, fmt.Errorf("error while cloning mf scheme analytics: not able to cast cloned object to type")
	}
	return clonedAnalytics, nil
}

func (s *MutualFundsTopFundsStore) GetHoldingsImportRequestStatus() mfExternalPb.State {
	if s == nil {
		return mfExternalPb.State_STATE_UNSPECIFIED
	}
	return s.holdingsImportStatus
}

func (s *MutualFundsTopFundsStore) GetUnknownScheme() (*UnknownScheme, error) {
	if s.unknownScheme == nil {
		return nil, nil
	}
	clonedObj := proto.Clone(s.unknownScheme.SchemeDetails)
	clonedDetails, ok := clonedObj.(*investmentAnalyserModelPb.MfSchemeDetails)
	if !ok {
		return nil, fmt.Errorf("error while cloning mf scheme details: not able to cast cloned object to type")
	}
	return &UnknownScheme{
		DisplayName:   s.unknownScheme.DisplayName,
		SchemeDetails: clonedDetails,
		IconUrl:       s.unknownScheme.IconUrl,
	}, nil
}

func (s *MutualFundsTopFundsStore) GetHoldingsImportRefreshEligibilityStatus() mfExternalPb.RequestEligibilityStatus {
	if s == nil {
		return mfExternalPb.RequestEligibilityStatus_REQUEST_ELIGIBILITY_STATUS_UNSPECIFIED
	}
	return s.refreshEligibilityStatus
}
