//nolint:dupl
package lens

import (
	"context"
	"fmt"

	"github.com/samber/lo"

	categorizerPb "github.com/epifi/gamma/api/categorizer"
	pb "github.com/epifi/gamma/api/frontend/analyser"
	"github.com/epifi/gamma/frontend/analyser/spends/top_categories/store"
	"github.com/epifi/gamma/frontend/analyser/visualcomponents"
)

type TopInvestmentsByAmountLensProcessor struct {
	generator ICategoryLensGenerator
	store     *store.SpendTopCategoryStore
}

func NewTopInvestmentsByAmountLensProcessor(store *store.SpendTopCategoryStore, generator ICategoryLensGenerator) *TopInvestmentsByAmountLensProcessor {
	return &TopInvestmentsByAmountLensProcessor{
		generator: generator,
		store:     store,
	}
}

// GenerateLensData - filters investment category aggregates, perform validations over category aggregates and generates enriched category aggregates
func (p *TopInvestmentsByAmountLensProcessor) GenerateLensData(ctx context.Context, actorId string, filterValues []*pb.FilterValue) error {
	bankCategoryAggs, err := p.store.GetBankCategoryAggregates()
	if err != nil {
		return fmt.Errorf("error while fetching bank category txns from store : %w", err)
	}
	ccCategoryAggs, err := p.store.GetCreditCardCategoryAggregates()
	if err != nil {
		return fmt.Errorf("error while fetching CC category txns from store : %w", err)
	}
	ccAddFundsTxns, err := p.store.GetCcAddFundsTxns()
	if err != nil {
		return fmt.Errorf("error while fetching cc add funds txns from store : %w", err)
	}
	bankCategoryAggs = lo.Filter(bankCategoryAggs, func(agg *store.CategoryAggregate, i int) bool {
		return agg.CategoryL0 == categorizerPb.L0_L0_INVESTMENTS
	})
	ccCategoryAggs = lo.Filter(ccCategoryAggs, func(agg *store.CategoryAggregate, i int) bool {
		return agg.CategoryL0 == categorizerPb.L0_L0_INVESTMENTS
	})
	err = p.generator.GenerateLensData(ctx, bankCategoryAggs, ccCategoryAggs, ccAddFundsTxns)
	if err != nil {
		return fmt.Errorf("failed to generate lens data: %w", err)
	}

	return nil
}

// GetVisualComponentGenerators initialises visual component generator for top investments category lens
func (p *TopInvestmentsByAmountLensProcessor) GetVisualComponentGenerators(ctx context.Context, actorId string) ([]visualcomponents.VisualComponentGenerator, error) {
	components, err := p.generator.GetVisualComponentGenerators(ctx, actorId, pb.LensName_LENS_NAME_TOP_INVESTMENTS_BY_AMOUNT)
	if err != nil {
		return nil, fmt.Errorf("failed to generate visual components for %v: %w", pb.LensName_LENS_NAME_TOP_INVESTMENTS_BY_AMOUNT, err)
	}
	return components, nil
}
