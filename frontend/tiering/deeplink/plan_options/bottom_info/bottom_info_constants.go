package bottom_info

var (
	DeeplinkOptionWoBCheckUpgradeDetailsLeftTitleFontColor    = "#282828" // Gray/Ink
	DeeplinkOptionWoBCheckUpgradeDetailsLeftTitleText         = "How to upgrade?"
	DeeplinkOptionWoBCheckUpgradeDetailsLeftSubtitleFontColor = "#555555" // Gray/Iron
	DeeplinkOptionWoBCheckUpgradeDetailsLeftSubtitleText      = "Upgrade now and get a zero-balance account"
	DeeplinkOptionWoBCheckUpgradeDetailsBgColor               = "#ECEEF0" // Gray/Fog

	DeeplinkOptionFiPlusUpgradeDetailsLeftTitleFontColor     = "#282828" // Gray/Ink
	DeeplinkOptionFiPlusUpgradeDetailsLeftTitleText          = "How to upgrade?"
	DeeplinkOptionFiPlusUpgradeDetailsLeftSubtitleFontColor  = "#555555" // Gray/Iron
	DeeplinkOptionUpgradeDetailsLeftSubtitleText             = "Maintain a minimum balance of %s & enjoy access to %s!"
	DeeplinkOptionFiPlusUpgradeDetailsBgColor                = "#ECEEF0" // Gray/Fog
	DeeplinkOptionFiPlusUpgradeDetailsRightTitleFontColor    = "#555555" // Gray/Iron
	DeeplinkOptionFiPlusUpgradeDetailsRightTitleText         = "MINIMUM BALANCE"
	DeeplinkOptionFiPlusUpgradeDetailsRightSubtitleFontColor = "#282828" // Gray/Ink

	DeeplinkOptionFiPlusGraceUpgradeDetailsLeftTitleFontColor    = "#282828" // Gray/Ink
	DeeplinkOptionFiPlusGraceUpgradeDetailsLeftTitleText         = "You’re low on funds"
	DeeplinkOptionFiPlusGraceUpgradeDetailsLeftSubtitleFontColor = "#555555" // Gray/Iron
	DeeplinkOptionGraceUpgradeDetailsLeftSubtitleText            = "To stay on %s, maintain a minimum balance of %s."
	DeeplinkOptionFiPlusGraceUpgradeDetailsBgColor               = "#F4E7BF" // Colour/Pastel Lemon

	DeeplinkOptionFiPlusUpgradeDetailsSufFundsRightTitleFontColor = "#555555"
	DeeplinkOptionFiPlusUpgradeDetailsSufFundsRightTitleText      = "YOUR BALANCE"

	NotEligibleUpgradeDetailsLeftTitleFontColor    = "#282828" // Gray/Ink
	NotEligibleUpgradeDetailsLeftTitleText         = "Can’t upgrade at the moment"
	NotEligibleUpgradeDetailsLeftSubTitleFontColor = "#555555" // Gray/Iron
	NotEligibleUpgradeDetailsLeftSubTitleText      = "We are not able to upgrade you at the moment. Please try later."
	NotEligibleUpgradeDetailsRightIconUrl          = "https://epifi-icons.pointz.in/tiering/cooloff_clock.png"
	NotEligibleUpgradeDetailsRightIconHeight       = 48
	NotEligibleUpgradeDetailsRightIconWidth        = 48
	NotEligibleUpgradeDetailsBgColor               = "#F4E7BF" // Colour/Pastel Lemon

	SameTierUpgradeDetailsWithoutMinBalanceCheckLeftText      = "How this works"
	SameTierUpgradeDetailsWithoutMinBalanceCheckLeftFontColor = "#282828" // Gray/Ink
	SameTierUpgradeDetailsWithoutMinBalanceCheckSubText       = "Let Fi continue to remain your salary account"
	SameTierUpgradeDetailsWithoutMinBalanceCheckSubFontColor  = "#555555" // Gray/Iron
	SameTierUpgradeDetailsWithoutMinBalanceCheckBgColor       = "#ECEEF0" // Gray/Fog

	SameTierUpgradeDetailsSalaryLiteLeftText      = "How to unlock all the benefits?"
	SameTierUpgradeDetailsSalaryLiteLeftFontColor = "#282828" // Gray/Ink
	SameTierUpgradeDetailsSalaryLiteSubText       = "Share your Fi-Federal savings account with your HR and ask them to send the salary in it."
	SameTierUpgradeDetailsSalaryLiteSubFontColor  = "#555555" // Gray/Iron
	SameTierUpgradeDetailsSalaryLiteBgColor       = "#ECEEF0" // Gray/Fog

	SameTierUpgradeDetailsAaSalaryLeftText      = "How this works"
	SameTierUpgradeDetailsAaSalaryLeftFontColor = "#282828" // Gray/Ink
	SameTierUpgradeDetailsAaSalaryTransferText  = "Transfer ₹%d from your salary account to unlock rewards"
	SameTierUpgradeDetailsAaSalarySubText       = "Calculate your rewards, add money and get an instant upgrade!"
	SameTierUpgradeDetailsAaSalaryActiveSubText = "Add money every month to enjoy 3% back. No minimum balance."
	SameTierUpgradeDetailsAaSalarySubFontColor  = "#555555" // Gray/Iron
	SameTierUpgradeDetailsAaSalaryBgColor       = "#FFFFFF" // Gray/Fog

	SameTierUpgradeDetailsWithMinBalanceCheckLeftText        = "How this works"
	SameTierUpgradeDetailsWithMinBalanceCheckLeftFontColor   = "#282828" // Gray/Ink
	SameTierUpgradeDetailsWithMinBalanceCheckSubText_Regular = "Maintain a min. balance of %s to enjoy %s benefits. A %s monthly fee applies if not maintained."
	SameTierUpgradeDetailsWithMinBalanceCheckSubText         = "Maintain a min. balance of %s to enjoy %s benefits."
	SameTierUpgradeDetailsWithMinBalanceCheckSubFontColor    = "#555555" // Gray/Iron
	SameTierUpgradeDetailsWithMinBalanceCheckBgColor         = "#ECEEF0" // Gray/Fog

	UpgradeDetailsMinKycLeftTitleFontColor     = "#282828" // Gray/Ink
	UpgradeDetailsMinKycLeftTitleText          = "How to upgrade?"
	UpgradeDetailsMinKycLeftSubtitleFontColor  = "#555555" // Gray/Iron
	UpgradeDetailsMinKycBgColor                = "#ECEEF0" // Gray/Fog
	UpgradeDetailsMinKycRightTitleFontColor    = "#555555" // Gray/Iron
	UpgradeDetailsMinKycRightTitleText         = "MINIMUM BALANCE"
	UpgradeDetailsMinKycRightSubtitleFontColor = "#282828" // Gray/Ink
	UpgradeDetailsMinKycLeftSubtitleText       = "Upgrade to Full KYC & Maintain a minimum balance of %s to enjoy access to %s!"
	UpgradeDetailsMinKycLeftSubtitleTextSalary = "Upgrade to Full KYC & Enrol for Salary Program on Fi & enjoy access to the Salary Plan"
)
