package scrollable

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	analyticsPb "github.com/epifi/gamma/api/frontend/analytics"
	cxHomeFePb "github.com/epifi/gamma/api/frontend/cx/home"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	homeFePb "github.com/epifi/gamma/api/frontend/home"
	homeOrchFePb "github.com/epifi/gamma/api/frontend/home/<USER>"
	"github.com/epifi/gamma/frontend/home/<USER>"
	"github.com/epifi/gamma/frontend/home/<USER>/cache"
	"github.com/epifi/gamma/frontend/home/<USER>/metrics"
)

type HelpComponent struct {
	cxHomeFeClient cxHomeFePb.HomeClient
}

func NewHelpComponent(cxHomeFeClient cxHomeFePb.HomeClient) *HelpComponent {
	return &HelpComponent{
		cxHomeFeClient: cxHomeFeClient,
	}
}

func (h *HelpComponent) GetScrollableComponent(ctx context.Context, request *headerPb.RequestHeader, requestMetadata *homeOrchFePb.HomePageRequestMetadata, widgetLayout *homeFePb.HomeWidget, componentCacheStore *cache.ComponentCacheStore, subComponentId string) (*homeOrchFePb.ScrollableComponent, error) {
	if request == nil || widgetLayout == nil {
		return nil, fmt.Errorf("mandatory parameters request or layout are missing")
	}

	supportTicketWidgetRes, err := h.cxHomeFeClient.GetSupportTicketWidget(ctx, &cxHomeFePb.GetSupportTicketWidgetRequest{
		Req: request,
		Source: &cxHomeFePb.GetSupportTicketWidgetRequest_AnalyticsScreenName{
			AnalyticsScreenName: analyticsPb.AnalyticsScreenName_HOME_LANDING_V2,
		},
	})
	if rpcErr := epifigrpc.RPCError(supportTicketWidgetRes.GetRespHeader(), err); rpcErr != nil {
		metrics.Recorder.RecordComponentGenerationError(constants.HelpScrollableComponentId.String(), "", rpcErr)
		logger.Error(ctx, "error while fetching help widget", zap.Error(rpcErr))
		return &homeOrchFePb.ScrollableComponent{
			Id:                constants.HelpScrollableComponentId.String(),
			WidgetType:        homeFePb.HomeWidget_WIDGET_TYPE_HELP,
			WidgetBg:          widgetLayout.GetWidgetBg(),
			WidgetBgSeparator: widgetLayout.GetWidgetBgSeparator(),
			TopSpacer:         widgetLayout.GetTopSpacer(),
			BottomSpacer:      widgetLayout.GetBottomSpacer(),
			RetryStrategy: &homeOrchFePb.RetryStrategy{
				RetryType: homeOrchFePb.RetryType_RETRY_TYPE_CLIENT_INITIATED,
			},
		}, nil
	}

	metrics.Recorder.RecordComponentGenerationSuccess(constants.HelpScrollableComponentId.String(), "")
	return &homeOrchFePb.ScrollableComponent{
		Id:                constants.HelpScrollableComponentId.String(),
		WidgetType:        homeFePb.HomeWidget_WIDGET_TYPE_HELP,
		WidgetBg:          widgetLayout.GetWidgetBg(),
		WidgetBgSeparator: widgetLayout.GetWidgetBgSeparator(),
		TopSpacer:         widgetLayout.GetTopSpacer(),
		BottomSpacer:      widgetLayout.GetBottomSpacer(),
		Component: &homeOrchFePb.ScrollableComponent_HelpComponent{
			HelpComponent: supportTicketWidgetRes,
		},
	}, nil
}
