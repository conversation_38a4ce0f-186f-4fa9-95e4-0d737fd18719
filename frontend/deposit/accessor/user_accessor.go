package accessor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	beUserPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

type UserAccessor struct {
	usersClient     beUserPb.UsersClient
	userGroupClient userGroupPb.GroupClient
}

func NewUserAccessor(usersClient beUserPb.UsersClient, userGroupClient userGroupPb.GroupClient) *UserAccessor {
	return &UserAccessor{usersClient: usersClient, userGroupClient: userGroupClient}
}

func (u *UserAccessor) GetUserGroups(ctx context.Context, actorId string) ([]commontypes.UserGroup, error) {
	userResp, err := u.usersClient.GetUser(ctx, &beUserPb.GetUserRequest{
		Identifier: &beUserPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if te := epifigrpc.RPCError(userResp, err); te != nil {
		return nil, fmt.Errorf("failed to get user details, %w", err)
	}

	groupResp, err := u.userGroupClient.GetGroupsMappedToEmail(ctx, &userGroupPb.GetGroupsMappedToEmailRequest{
		Email: userResp.GetUser().GetProfile().GetEmail(),
	})
	if te := epifigrpc.RPCError(groupResp, err); te != nil {
		return nil, fmt.Errorf("failed to get user group details, %w", err)
	}
	return groupResp.GetGroups(), nil
}
