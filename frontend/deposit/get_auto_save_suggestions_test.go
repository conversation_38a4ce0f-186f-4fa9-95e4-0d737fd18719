// nolint
package deposit

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/dayofweek"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	depositPb "github.com/epifi/gamma/api/deposit"
	depositMock "github.com/epifi/gamma/api/deposit/mocks"
	analyticsPb "github.com/epifi/gamma/api/frontend/analytics"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	feDepositPb "github.com/epifi/gamma/api/frontend/deposit"
	fitttPb "github.com/epifi/gamma/api/frontend/fittt"
	fitttClientstatePb "github.com/epifi/gamma/api/frontend/fittt/clientstate"
	rulePb "github.com/epifi/gamma/api/frontend/fittt/rule"
	"github.com/epifi/gamma/api/frontend/header"
	types "github.com/epifi/gamma/api/typesv2"
	typesPb "github.com/epifi/gamma/api/typesv2"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	groupMock "github.com/epifi/gamma/api/user/group/mocks"
	usersMocks "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/deposit/ui"
	"github.com/epifi/be-common/pkg/datetime"
	timeMocks "github.com/epifi/be-common/pkg/datetime/mocks"
)

func TestService_GetAutoSaveSuggestions(t *testing.T) {
	t.Parallel()
	type fields struct {
		depositClient   *depositMock.MockDepositClient
		usersClient     *usersMocks.MockUsersClient
		actorClient     *actorMocks.MockActorClient
		userGroupClient *groupMock.MockGroupClient
		conf            *genconf.Config
		time            *timeMocks.MockTime
	}
	type args struct {
		ctx context.Context
		req *feDepositPb.GetAutoSaveSuggestionsRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *feDepositPb.GetAutoSaveSuggestionsResponse
		wantErr bool
		prepare func(*args, *fields)
	}{
		{
			name: "should successfully return auto save suggestions without customization",
			args: args{
				ctx: context.Background(),
				req: &feDepositPb.GetAutoSaveSuggestionsRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					DepositType: accounts.Type_SMART_DEPOSIT,
					Amount: &typesPb.Money{
						CurrencyCode: "INR",
						Units:        2345,
					},
					Term: &types.DepositTerm{
						Months: 1,
						Days:   5,
					},
					GoalDetails: &feDepositPb.GetAutoSaveSuggestionsRequest_GoalDetails{
						TargetAmount: &typesPb.Money{
							CurrencyCode: "INR",
							Units:        10000,
						},
					},
					DepositName: "abc deposit name",
					TemplateId:  "template-id-1",
				},
			},
			want: &feDepositPb.GetAutoSaveSuggestionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},

				AutoSaveSetupTile: &feDepositPb.AutoSaveSetupTileV2{
					Title:       ui.GetText("How do you want to save?", "#333333", commontypes.FontStyle_HEADLINE_3),
					Description: ui.GetText("Select a rule to save regularly after creating this deposit", "#8D8D8D", commontypes.FontStyle_BODY_XS),
					Options: []*feDepositPb.AutoSaveSuggestionOptionV2{
						{
							Title: []*commontypes.Text{
								ui.GetText("Save ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("₹50 everyday", "#333333", commontypes.FontStyle_SUBTITLE_3),
							},
							Description: ui.GetText("Total savings: ₹1,800", "#8D8D8D", commontypes.FontStyle_BODY_4_PARA),
							IsSelected:  true,
							EditCta: &deeplinkPb.Cta{
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_FIT_CUSTOMISE_RULE_SCREEN,
									ScreenOptions: &deeplinkPb.Deeplink_FitCustomiseRuleScreenOptions{
										FitCustomiseRuleScreenOptions: &deeplinkPb.FitCustomiseRuleScreenOptions{
											RuleId:     "43dd9b50-786b-47c0-bf0b-61401bc8da42",
											PageType:   fitttClientstatePb.SubscriptionPageType_SUBSCRIPTION_PAGE_CAPTURE_PARAMS,
											EntryPoint: analyticsPb.AnalyticsScreenName_DEPOSIT_AUTO_SAVE_SUGGESTIONS,
											ParamValues: &rulePb.RuleParamValues{
												RuleParamValues: map[string]*rulePb.Value{
													"depositAmount": {
														Value: &rulePb.Value_MoneyVal{
															MoneyVal: &types.Money{
																CurrencyCode: "INR",
																Units:        50,
															},
														},
													},
													"depositAccountId": {
														Value: &rulePb.Value_SdValue{
															SdValue: &rulePb.SdParamValue{
																Name: "abc deposit name",
															},
														},
														EditState: rulePb.RuleParamEditState_NOT_EDITABLE_WITH_HIDDEN_OPTIONS,
													},
												},
											},
										},
									},
								},
							},
							AutoSaveParams: &feDepositPb.DepositAutoSaveParams{
								RuleId: "43dd9b50-786b-47c0-bf0b-61401bc8da42",
								RuleParamValues: &fitttPb.RuleParamValues{
									RuleParamValues: map[string]*fitttPb.Value{
										"depositAmount": {
											Value: &fitttPb.Value_MoneyVal{
												MoneyVal: &types.Money{
													CurrencyCode: "INR",
													Units:        50,
												},
											},
										},
									},
								},
							},
						},
						{
							Title: []*commontypes.Text{
								ui.GetText("Save ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("₹150 every friday", "#333333", commontypes.FontStyle_SUBTITLE_3),
							},
							Description: ui.GetText("Total savings: ₹750", "#8D8D8D", commontypes.FontStyle_BODY_4_PARA),
							EditCta: &deeplinkPb.Cta{
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_FIT_CUSTOMISE_RULE_SCREEN,
									ScreenOptions: &deeplinkPb.Deeplink_FitCustomiseRuleScreenOptions{
										FitCustomiseRuleScreenOptions: &deeplinkPb.FitCustomiseRuleScreenOptions{
											RuleId:     "7b251ad6-a653-4c8a-88fa-a3714b45b480",
											PageType:   fitttClientstatePb.SubscriptionPageType_SUBSCRIPTION_PAGE_CAPTURE_PARAMS,
											EntryPoint: analyticsPb.AnalyticsScreenName_DEPOSIT_AUTO_SAVE_SUGGESTIONS,
											ParamValues: &rulePb.RuleParamValues{
												RuleParamValues: map[string]*rulePb.Value{
													"depositAmount": {
														Value: &rulePb.Value_MoneyVal{
															MoneyVal: &types.Money{
																CurrencyCode: "INR",
																Units:        150,
															},
														},
													},
													"configuredDayOfWeek": {
														Value: &rulePb.Value_DayOfWeekVal{
															DayOfWeekVal: &rulePb.DayOfWeekVal{
																Weekday:  "Friday",
																AbbrName: "Fri",
																Enabled:  true,
															},
														},
													},
													"depositAccountId": {
														Value: &rulePb.Value_SdValue{
															SdValue: &rulePb.SdParamValue{
																Name: "abc deposit name",
															},
														},
														EditState: rulePb.RuleParamEditState_NOT_EDITABLE_WITH_HIDDEN_OPTIONS,
													},
												},
											},
										},
									},
								},
							},
							AutoSaveParams: &feDepositPb.DepositAutoSaveParams{
								RuleId: "7b251ad6-a653-4c8a-88fa-a3714b45b480",
								RuleParamValues: &fitttPb.RuleParamValues{
									RuleParamValues: map[string]*fitttPb.Value{
										"depositAmount": {
											Value: &fitttPb.Value_MoneyVal{
												MoneyVal: &types.Money{
													CurrencyCode: "INR",
													Units:        150,
												},
											},
										},
										"configuredDayOfWeek": {
											Value: &fitttPb.Value_DayOfWeekVal{
												DayOfWeekVal: &fitttPb.DayOfWeekVal{
													Weekday:  "Friday",
													AbbrName: "Fri",
													Enabled:  true,
												},
											},
										},
									},
								},
							},
						},
						{
							Title: []*commontypes.Text{
								ui.GetText("Save ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("₹2,500", "#333333", commontypes.FontStyle_SUBTITLE_3),
								ui.GetText(" on ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("1st", "#333333", commontypes.FontStyle_SUBTITLE_3),
								ui.GetText(" of ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("every month", "#333333", commontypes.FontStyle_SUBTITLE_3),
							},
							Description: ui.GetText("Total savings: ₹2,500", "#8D8D8D", commontypes.FontStyle_BODY_4_PARA),
							EditCta: &deeplinkPb.Cta{
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_FIT_CUSTOMISE_RULE_SCREEN,
									ScreenOptions: &deeplinkPb.Deeplink_FitCustomiseRuleScreenOptions{
										FitCustomiseRuleScreenOptions: &deeplinkPb.FitCustomiseRuleScreenOptions{
											RuleId:     "bb093acb-fb94-4aa4-abe6-1d85a91feba2",
											PageType:   fitttClientstatePb.SubscriptionPageType_SUBSCRIPTION_PAGE_CAPTURE_PARAMS,
											EntryPoint: analyticsPb.AnalyticsScreenName_DEPOSIT_AUTO_SAVE_SUGGESTIONS,
											ParamValues: &rulePb.RuleParamValues{
												RuleParamValues: map[string]*rulePb.Value{
													"depositAmount": {
														Value: &rulePb.Value_MoneyVal{
															MoneyVal: &types.Money{
																CurrencyCode: "INR",
																Units:        2500,
															},
														},
													},
													"configuredDateOfMonth": {
														Value: &rulePb.Value_DateOfMonthVal{
															DateOfMonthVal: &rulePb.DateOfMonthVal{
																Date:          1,
																DisplayString: "1st",
																Enabled:       true,
															},
														},
													},
													"depositAccountId": {
														Value: &rulePb.Value_SdValue{
															SdValue: &rulePb.SdParamValue{
																Name: "abc deposit name",
															},
														},
														EditState: rulePb.RuleParamEditState_NOT_EDITABLE_WITH_HIDDEN_OPTIONS,
													},
												},
											},
										},
									},
								},
							},
							AutoSaveParams: &feDepositPb.DepositAutoSaveParams{
								RuleId: "bb093acb-fb94-4aa4-abe6-1d85a91feba2",
								RuleParamValues: &fitttPb.RuleParamValues{
									RuleParamValues: map[string]*fitttPb.Value{
										"depositAmount": {
											Value: &fitttPb.Value_MoneyVal{
												MoneyVal: &types.Money{
													CurrencyCode: "INR",
													Units:        2500,
												},
											},
										},
										"configuredDateOfMonth": {
											Value: &fitttPb.Value_DateOfMonthVal{
												DateOfMonthVal: &fitttPb.DateOfMonthVal{
													Date:          1,
													DisplayString: "1st",
													Enabled:       true,
												},
											},
										},
									},
								},
							},
						},
						{
							Title: []*commontypes.Text{
								ui.GetText("Remind me later", "#646464", commontypes.FontStyle_BODY_3),
							},
						},
					},
				},
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {

				f.time.EXPECT().Now().Return(time.Date(2022, 5, 20, 0, 0, 0, 0, datetime.IST)).Times(4)

				f.depositClient.EXPECT().GetAutoSaveSuggestions(gomock.Any(), &depositPb.GetAutoSaveSuggestionsRequest{
					DepositType: accounts.Type_SMART_DEPOSIT,
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2345,
					},
					CloseDate: timestampPb.New(time.Date(2022, 6, 25, 0, 0, 0, 0, datetime.IST)),
					GoalDetails: &depositPb.GetAutoSaveSuggestionsRequest_GoalDetails{
						TargetAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        10000,
						},
					},
				}).Return(&depositPb.GetAutoSaveSuggestionsResponse{
					Status: rpc.StatusOk(),
					AutoSaveRuleParams: []*depositPb.AutoSaveRuleParams{
						{
							Rule: &depositPb.AutoSaveRuleParams_Daily{
								Daily: &depositPb.AutoSaveDailyParams{
									Amount: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        50,
									},
								},
							},
						},
						{
							Rule: &depositPb.AutoSaveRuleParams_Weekly{
								Weekly: &depositPb.AutoSaveWeeklyParams{
									WeekDay: dayofweek.DayOfWeek_FRIDAY,
									Amount: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        150,
									},
								},
							},
						},
						{
							Rule: &depositPb.AutoSaveRuleParams_Monthly{
								Monthly: &depositPb.AutoSaveMonthlyParams{
									DayOfMonth: 1,
									Amount: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        2500,
									},
								},
							},
						},
					},
				}, nil).Times(1)

				// user group constraints mock
				f.userGroupClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(),
					gomock.Any()).Return(&userGroupPb.GetGroupsMappedToEmailResponse{
					Status: rpc.StatusOk(),
					Groups: []commontypes.UserGroup{commontypes.UserGroup_INTERNAL},
				}, nil).MaxTimes(1)
				f.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: "actor-id-1",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-id-1",
					},
				}, nil).MaxTimes(2)
			},
		},
		{
			name: "should successfully return auto save suggestions with daily rule customization",
			args: args{
				ctx: context.Background(),
				req: &feDepositPb.GetAutoSaveSuggestionsRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					DepositType: accounts.Type_SMART_DEPOSIT,
					Amount: &typesPb.Money{
						CurrencyCode: "INR",
						Units:        2345,
					},
					Term: &types.DepositTerm{
						Months: 3,
						Days:   0,
					},
					GoalDetails: &feDepositPb.GetAutoSaveSuggestionsRequest_GoalDetails{
						TargetAmount: &typesPb.Money{
							CurrencyCode: "INR",
							Units:        10000,
						},
					},
					DepositName: "abc deposit name",
					TemplateId:  "template-id-1",
					CustomizedAutoSaveParams: &feDepositPb.DepositAutoSaveParams{
						RuleId: "43dd9b50-786b-47c0-bf0b-61401bc8da42",
						RuleParamValues: &fitttPb.RuleParamValues{
							RuleParamValues: map[string]*fitttPb.Value{
								"depositAmount": {
									Value: &fitttPb.Value_MoneyVal{
										MoneyVal: &types.Money{
											CurrencyCode: "INR",
											Units:        151,
										},
									},
								},
							},
						},
					},
				},
			},
			want: &feDepositPb.GetAutoSaveSuggestionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},

				AutoSaveSetupTile: &feDepositPb.AutoSaveSetupTileV2{
					Title:       ui.GetText("How do you want to save?", "#333333", commontypes.FontStyle_HEADLINE_3),
					Description: ui.GetText("Select a rule to save regularly after creating this deposit", "#8D8D8D", commontypes.FontStyle_BODY_XS),
					Options: []*feDepositPb.AutoSaveSuggestionOptionV2{
						{
							Title: []*commontypes.Text{
								ui.GetText("Save ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("₹151 everyday", "#333333", commontypes.FontStyle_SUBTITLE_3),
							},
							Description: ui.GetText("Total savings: ₹13,892", "#8D8D8D", commontypes.FontStyle_BODY_4_PARA),
							IsSelected:  true,
							EditCta: &deeplinkPb.Cta{
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_FIT_CUSTOMISE_RULE_SCREEN,
									ScreenOptions: &deeplinkPb.Deeplink_FitCustomiseRuleScreenOptions{
										FitCustomiseRuleScreenOptions: &deeplinkPb.FitCustomiseRuleScreenOptions{
											RuleId:     "43dd9b50-786b-47c0-bf0b-61401bc8da42",
											PageType:   fitttClientstatePb.SubscriptionPageType_SUBSCRIPTION_PAGE_CAPTURE_PARAMS,
											EntryPoint: analyticsPb.AnalyticsScreenName_DEPOSIT_AUTO_SAVE_SUGGESTIONS,
											ParamValues: &rulePb.RuleParamValues{
												RuleParamValues: map[string]*rulePb.Value{
													"depositAmount": {
														Value: &rulePb.Value_MoneyVal{
															MoneyVal: &types.Money{
																CurrencyCode: "INR",
																Units:        151,
															},
														},
													},
													"depositAccountId": {
														Value: &rulePb.Value_SdValue{
															SdValue: &rulePb.SdParamValue{
																Name: "abc deposit name",
															},
														},
														EditState: rulePb.RuleParamEditState_NOT_EDITABLE_WITH_HIDDEN_OPTIONS,
													},
												},
											},
										},
									},
								},
							},
							AutoSaveParams: &feDepositPb.DepositAutoSaveParams{
								RuleId: "43dd9b50-786b-47c0-bf0b-61401bc8da42",
								RuleParamValues: &fitttPb.RuleParamValues{
									RuleParamValues: map[string]*fitttPb.Value{
										"depositAmount": {
											Value: &fitttPb.Value_MoneyVal{
												MoneyVal: &types.Money{
													CurrencyCode: "INR",
													Units:        151,
												},
											},
										},
									},
								},
							},
						},
						{
							Title: []*commontypes.Text{
								ui.GetText("Save ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("₹150 every friday", "#333333", commontypes.FontStyle_SUBTITLE_3),
							},
							Description: ui.GetText("Total savings: ₹1,950", "#8D8D8D", commontypes.FontStyle_BODY_4_PARA),
							EditCta: &deeplinkPb.Cta{
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_FIT_CUSTOMISE_RULE_SCREEN,
									ScreenOptions: &deeplinkPb.Deeplink_FitCustomiseRuleScreenOptions{
										FitCustomiseRuleScreenOptions: &deeplinkPb.FitCustomiseRuleScreenOptions{
											RuleId:     "7b251ad6-a653-4c8a-88fa-a3714b45b480",
											PageType:   fitttClientstatePb.SubscriptionPageType_SUBSCRIPTION_PAGE_CAPTURE_PARAMS,
											EntryPoint: analyticsPb.AnalyticsScreenName_DEPOSIT_AUTO_SAVE_SUGGESTIONS,
											ParamValues: &rulePb.RuleParamValues{
												RuleParamValues: map[string]*rulePb.Value{
													"depositAmount": {
														Value: &rulePb.Value_MoneyVal{
															MoneyVal: &types.Money{
																CurrencyCode: "INR",
																Units:        150,
															},
														},
													},
													"configuredDayOfWeek": {
														Value: &rulePb.Value_DayOfWeekVal{
															DayOfWeekVal: &rulePb.DayOfWeekVal{
																Weekday:  "Friday",
																AbbrName: "Fri",
																Enabled:  true,
															},
														},
													},
													"depositAccountId": {
														Value: &rulePb.Value_SdValue{
															SdValue: &rulePb.SdParamValue{
																Name: "abc deposit name",
															},
														},
														EditState: rulePb.RuleParamEditState_NOT_EDITABLE_WITH_HIDDEN_OPTIONS,
													},
												},
											},
										},
									},
								},
							},
							AutoSaveParams: &feDepositPb.DepositAutoSaveParams{
								RuleId: "7b251ad6-a653-4c8a-88fa-a3714b45b480",
								RuleParamValues: &fitttPb.RuleParamValues{
									RuleParamValues: map[string]*fitttPb.Value{
										"depositAmount": {
											Value: &fitttPb.Value_MoneyVal{
												MoneyVal: &types.Money{
													CurrencyCode: "INR",
													Units:        150,
												},
											},
										},
										"configuredDayOfWeek": {
											Value: &fitttPb.Value_DayOfWeekVal{
												DayOfWeekVal: &fitttPb.DayOfWeekVal{
													Weekday:  "Friday",
													AbbrName: "Fri",
													Enabled:  true,
												},
											},
										},
									},
								},
							},
						},
						{
							Title: []*commontypes.Text{
								ui.GetText("Save ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("₹2,500", "#333333", commontypes.FontStyle_SUBTITLE_3),
								ui.GetText(" on ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("1st", "#333333", commontypes.FontStyle_SUBTITLE_3),
								ui.GetText(" of ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("every month", "#333333", commontypes.FontStyle_SUBTITLE_3),
							},
							Description: ui.GetText("Total savings: ₹7,500", "#8D8D8D", commontypes.FontStyle_BODY_4_PARA),
							EditCta: &deeplinkPb.Cta{
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_FIT_CUSTOMISE_RULE_SCREEN,
									ScreenOptions: &deeplinkPb.Deeplink_FitCustomiseRuleScreenOptions{
										FitCustomiseRuleScreenOptions: &deeplinkPb.FitCustomiseRuleScreenOptions{
											RuleId:     "bb093acb-fb94-4aa4-abe6-1d85a91feba2",
											PageType:   fitttClientstatePb.SubscriptionPageType_SUBSCRIPTION_PAGE_CAPTURE_PARAMS,
											EntryPoint: analyticsPb.AnalyticsScreenName_DEPOSIT_AUTO_SAVE_SUGGESTIONS,
											ParamValues: &rulePb.RuleParamValues{
												RuleParamValues: map[string]*rulePb.Value{
													"depositAmount": {
														Value: &rulePb.Value_MoneyVal{
															MoneyVal: &types.Money{
																CurrencyCode: "INR",
																Units:        2500,
															},
														},
													},
													"configuredDateOfMonth": {
														Value: &rulePb.Value_DateOfMonthVal{
															DateOfMonthVal: &rulePb.DateOfMonthVal{
																Date:          1,
																DisplayString: "1st",
																Enabled:       true,
															},
														},
													},
													"depositAccountId": {
														Value: &rulePb.Value_SdValue{
															SdValue: &rulePb.SdParamValue{
																Name: "abc deposit name",
															},
														},
														EditState: rulePb.RuleParamEditState_NOT_EDITABLE_WITH_HIDDEN_OPTIONS,
													},
												},
											},
										},
									},
								},
							},
							AutoSaveParams: &feDepositPb.DepositAutoSaveParams{
								RuleId: "bb093acb-fb94-4aa4-abe6-1d85a91feba2",
								RuleParamValues: &fitttPb.RuleParamValues{
									RuleParamValues: map[string]*fitttPb.Value{
										"depositAmount": {
											Value: &fitttPb.Value_MoneyVal{
												MoneyVal: &types.Money{
													CurrencyCode: "INR",
													Units:        2500,
												},
											},
										},
										"configuredDateOfMonth": {
											Value: &fitttPb.Value_DateOfMonthVal{
												DateOfMonthVal: &fitttPb.DateOfMonthVal{
													Date:          1,
													DisplayString: "1st",
													Enabled:       true,
												},
											},
										},
									},
								},
							},
						},
						{
							Title: []*commontypes.Text{
								ui.GetText("Remind me later", "#646464", commontypes.FontStyle_BODY_3),
							},
						},
					},
				},
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {

				f.time.EXPECT().Now().Return(time.Date(2022, 5, 20, 0, 0, 0, 0, datetime.IST)).Times(4)

				f.depositClient.EXPECT().GetAutoSaveSuggestions(gomock.Any(), &depositPb.GetAutoSaveSuggestionsRequest{
					DepositType: accounts.Type_SMART_DEPOSIT,
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2345,
					},
					CloseDate: timestampPb.New(time.Date(2022, 8, 20, 0, 0, 0, 0, datetime.IST)),
					GoalDetails: &depositPb.GetAutoSaveSuggestionsRequest_GoalDetails{
						TargetAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        10000,
						},
					},
				}).Return(&depositPb.GetAutoSaveSuggestionsResponse{
					Status: rpc.StatusOk(),
					AutoSaveRuleParams: []*depositPb.AutoSaveRuleParams{
						{
							Rule: &depositPb.AutoSaveRuleParams_Daily{
								Daily: &depositPb.AutoSaveDailyParams{
									Amount: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        50,
									},
								},
							},
						},
						{
							Rule: &depositPb.AutoSaveRuleParams_Weekly{
								Weekly: &depositPb.AutoSaveWeeklyParams{
									WeekDay: dayofweek.DayOfWeek_FRIDAY,
									Amount: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        150,
									},
								},
							},
						},
						{
							Rule: &depositPb.AutoSaveRuleParams_Monthly{
								Monthly: &depositPb.AutoSaveMonthlyParams{
									DayOfMonth: 1,
									Amount: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        2500,
									},
								},
							},
						},
					},
				}, nil).Times(1)

				// user group constraints mock
				f.userGroupClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(),
					gomock.Any()).Return(&userGroupPb.GetGroupsMappedToEmailResponse{
					Status: rpc.StatusOk(),
					Groups: []commontypes.UserGroup{commontypes.UserGroup_INTERNAL},
				}, nil).MaxTimes(1)
				f.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: "actor-id-1",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-id-1",
					},
				}, nil).MaxTimes(2)
			},
		},
		{
			name: "should successfully return auto save suggestions with weekly rule customization",
			args: args{
				ctx: context.Background(),
				req: &feDepositPb.GetAutoSaveSuggestionsRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					DepositType: accounts.Type_SMART_DEPOSIT,
					Amount: &typesPb.Money{
						CurrencyCode: "INR",
						Units:        2345,
					},
					Term: &types.DepositTerm{
						Months: 5,
						Days:   1,
					},
					GoalDetails: &feDepositPb.GetAutoSaveSuggestionsRequest_GoalDetails{
						TargetAmount: &typesPb.Money{
							CurrencyCode: "INR",
							Units:        10000,
						},
					},
					DepositName: "abc deposit name",
					TemplateId:  "template-id-1",
					CustomizedAutoSaveParams: &feDepositPb.DepositAutoSaveParams{
						RuleId: "7b251ad6-a653-4c8a-88fa-a3714b45b480",
						RuleParamValues: &fitttPb.RuleParamValues{
							RuleParamValues: map[string]*fitttPb.Value{
								"depositAmount": {
									Value: &fitttPb.Value_CustomAmountCtaVal{
										CustomAmountCtaVal: &fitttPb.CustomAmountCTAValue{
											InitialVal: &types.Money{
												CurrencyCode: "INR",
												Units:        251,
											},
										},
									},
								},
								"configuredDayOfWeek": {
									Value: &fitttPb.Value_DayOfWeekVal{
										DayOfWeekVal: &fitttPb.DayOfWeekVal{
											Weekday:  "Tuesday",
											AbbrName: "Tue",
										},
									},
								},
							},
						},
					},
				},
			},
			want: &feDepositPb.GetAutoSaveSuggestionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},

				AutoSaveSetupTile: &feDepositPb.AutoSaveSetupTileV2{
					Title:       ui.GetText("How do you want to save?", "#333333", commontypes.FontStyle_HEADLINE_3),
					Description: ui.GetText("Select a rule to save regularly after creating this deposit", "#8D8D8D", commontypes.FontStyle_BODY_XS),
					Options: []*feDepositPb.AutoSaveSuggestionOptionV2{
						{
							Title: []*commontypes.Text{
								ui.GetText("Save ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("₹50 everyday", "#333333", commontypes.FontStyle_SUBTITLE_3),
							},
							Description: ui.GetText("Total savings: ₹7,700", "#8D8D8D", commontypes.FontStyle_BODY_4_PARA),
							EditCta: &deeplinkPb.Cta{
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_FIT_CUSTOMISE_RULE_SCREEN,
									ScreenOptions: &deeplinkPb.Deeplink_FitCustomiseRuleScreenOptions{
										FitCustomiseRuleScreenOptions: &deeplinkPb.FitCustomiseRuleScreenOptions{
											RuleId:     "43dd9b50-786b-47c0-bf0b-61401bc8da42",
											PageType:   fitttClientstatePb.SubscriptionPageType_SUBSCRIPTION_PAGE_CAPTURE_PARAMS,
											EntryPoint: analyticsPb.AnalyticsScreenName_DEPOSIT_AUTO_SAVE_SUGGESTIONS,
											ParamValues: &rulePb.RuleParamValues{
												RuleParamValues: map[string]*rulePb.Value{
													"depositAmount": {
														Value: &rulePb.Value_MoneyVal{
															MoneyVal: &types.Money{
																CurrencyCode: "INR",
																Units:        50,
															},
														},
													},
													"depositAccountId": {
														Value: &rulePb.Value_SdValue{
															SdValue: &rulePb.SdParamValue{
																Name: "abc deposit name",
															},
														},
														EditState: rulePb.RuleParamEditState_NOT_EDITABLE_WITH_HIDDEN_OPTIONS,
													},
												},
											},
										},
									},
								},
							},
							AutoSaveParams: &feDepositPb.DepositAutoSaveParams{
								RuleId: "43dd9b50-786b-47c0-bf0b-61401bc8da42",
								RuleParamValues: &fitttPb.RuleParamValues{
									RuleParamValues: map[string]*fitttPb.Value{
										"depositAmount": {
											Value: &fitttPb.Value_MoneyVal{
												MoneyVal: &types.Money{
													CurrencyCode: "INR",
													Units:        50,
												},
											},
										},
									},
								},
							},
						},
						{
							Title: []*commontypes.Text{
								ui.GetText("Save ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("₹251 every tuesday", "#333333", commontypes.FontStyle_SUBTITLE_3),
							},
							Description: ui.GetText("Total savings: ₹5,522", "#8D8D8D", commontypes.FontStyle_BODY_4_PARA),
							IsSelected:  true,
							EditCta: &deeplinkPb.Cta{
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_FIT_CUSTOMISE_RULE_SCREEN,
									ScreenOptions: &deeplinkPb.Deeplink_FitCustomiseRuleScreenOptions{
										FitCustomiseRuleScreenOptions: &deeplinkPb.FitCustomiseRuleScreenOptions{
											RuleId:     "7b251ad6-a653-4c8a-88fa-a3714b45b480",
											PageType:   fitttClientstatePb.SubscriptionPageType_SUBSCRIPTION_PAGE_CAPTURE_PARAMS,
											EntryPoint: analyticsPb.AnalyticsScreenName_DEPOSIT_AUTO_SAVE_SUGGESTIONS,
											ParamValues: &rulePb.RuleParamValues{
												RuleParamValues: map[string]*rulePb.Value{
													"depositAmount": {
														Value: &rulePb.Value_MoneyVal{
															MoneyVal: &types.Money{
																CurrencyCode: "INR",
																Units:        251,
															},
														},
													},
													"configuredDayOfWeek": {
														Value: &rulePb.Value_DayOfWeekVal{
															DayOfWeekVal: &rulePb.DayOfWeekVal{
																Weekday:  "Tuesday",
																AbbrName: "Tue",
																Enabled:  true,
															},
														},
													},
													"depositAccountId": {
														Value: &rulePb.Value_SdValue{
															SdValue: &rulePb.SdParamValue{
																Name: "abc deposit name",
															},
														},
														EditState: rulePb.RuleParamEditState_NOT_EDITABLE_WITH_HIDDEN_OPTIONS,
													},
												},
											},
										},
									},
								},
							},
							AutoSaveParams: &feDepositPb.DepositAutoSaveParams{
								RuleId: "7b251ad6-a653-4c8a-88fa-a3714b45b480",
								RuleParamValues: &fitttPb.RuleParamValues{
									RuleParamValues: map[string]*fitttPb.Value{
										"depositAmount": {
											Value: &fitttPb.Value_MoneyVal{
												MoneyVal: &types.Money{
													CurrencyCode: "INR",
													Units:        251,
												},
											},
										},
										"configuredDayOfWeek": {
											Value: &fitttPb.Value_DayOfWeekVal{
												DayOfWeekVal: &fitttPb.DayOfWeekVal{
													Weekday:  "Tuesday",
													AbbrName: "Tue",
													Enabled:  true,
												},
											},
										},
									},
								},
							},
						},
						{
							Title: []*commontypes.Text{
								ui.GetText("Save ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("₹2,500", "#333333", commontypes.FontStyle_SUBTITLE_3),
								ui.GetText(" on ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("1st", "#333333", commontypes.FontStyle_SUBTITLE_3),
								ui.GetText(" of ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("every month", "#333333", commontypes.FontStyle_SUBTITLE_3),
							},
							Description: ui.GetText("Total savings: ₹12,500", "#8D8D8D", commontypes.FontStyle_BODY_4_PARA),
							EditCta: &deeplinkPb.Cta{
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_FIT_CUSTOMISE_RULE_SCREEN,
									ScreenOptions: &deeplinkPb.Deeplink_FitCustomiseRuleScreenOptions{
										FitCustomiseRuleScreenOptions: &deeplinkPb.FitCustomiseRuleScreenOptions{
											RuleId:     "bb093acb-fb94-4aa4-abe6-1d85a91feba2",
											PageType:   fitttClientstatePb.SubscriptionPageType_SUBSCRIPTION_PAGE_CAPTURE_PARAMS,
											EntryPoint: analyticsPb.AnalyticsScreenName_DEPOSIT_AUTO_SAVE_SUGGESTIONS,
											ParamValues: &rulePb.RuleParamValues{
												RuleParamValues: map[string]*rulePb.Value{
													"depositAmount": {
														Value: &rulePb.Value_MoneyVal{
															MoneyVal: &types.Money{
																CurrencyCode: "INR",
																Units:        2500,
															},
														},
													},
													"configuredDateOfMonth": {
														Value: &rulePb.Value_DateOfMonthVal{
															DateOfMonthVal: &rulePb.DateOfMonthVal{
																Date:          1,
																DisplayString: "1st",
																Enabled:       true,
															},
														},
													},
													"depositAccountId": {
														Value: &rulePb.Value_SdValue{
															SdValue: &rulePb.SdParamValue{
																Name: "abc deposit name",
															},
														},
														EditState: rulePb.RuleParamEditState_NOT_EDITABLE_WITH_HIDDEN_OPTIONS,
													},
												},
											},
										},
									},
								},
							},
							AutoSaveParams: &feDepositPb.DepositAutoSaveParams{
								RuleId: "bb093acb-fb94-4aa4-abe6-1d85a91feba2",
								RuleParamValues: &fitttPb.RuleParamValues{
									RuleParamValues: map[string]*fitttPb.Value{
										"depositAmount": {
											Value: &fitttPb.Value_MoneyVal{
												MoneyVal: &types.Money{
													CurrencyCode: "INR",
													Units:        2500,
												},
											},
										},
										"configuredDateOfMonth": {
											Value: &fitttPb.Value_DateOfMonthVal{
												DateOfMonthVal: &fitttPb.DateOfMonthVal{
													Date:          1,
													DisplayString: "1st",
													Enabled:       true,
												},
											},
										},
									},
								},
							},
						},
						{
							Title: []*commontypes.Text{
								ui.GetText("Remind me later", "#646464", commontypes.FontStyle_BODY_3),
							},
						},
					},
				},
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {

				f.time.EXPECT().Now().Return(time.Date(2022, 5, 20, 0, 0, 0, 0, datetime.IST)).Times(4)

				f.depositClient.EXPECT().GetAutoSaveSuggestions(gomock.Any(), &depositPb.GetAutoSaveSuggestionsRequest{
					DepositType: accounts.Type_SMART_DEPOSIT,
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2345,
					},
					CloseDate: timestampPb.New(time.Date(2022, 10, 21, 0, 0, 0, 0, datetime.IST)),
					GoalDetails: &depositPb.GetAutoSaveSuggestionsRequest_GoalDetails{
						TargetAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        10000,
						},
					},
				}).Return(&depositPb.GetAutoSaveSuggestionsResponse{
					Status: rpc.StatusOk(),
					AutoSaveRuleParams: []*depositPb.AutoSaveRuleParams{
						{
							Rule: &depositPb.AutoSaveRuleParams_Daily{
								Daily: &depositPb.AutoSaveDailyParams{
									Amount: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        50,
									},
								},
							},
						},
						{
							Rule: &depositPb.AutoSaveRuleParams_Weekly{
								Weekly: &depositPb.AutoSaveWeeklyParams{
									WeekDay: dayofweek.DayOfWeek_FRIDAY,
									Amount: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        150,
									},
								},
							},
						},
						{
							Rule: &depositPb.AutoSaveRuleParams_Monthly{
								Monthly: &depositPb.AutoSaveMonthlyParams{
									DayOfMonth: 1,
									Amount: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        2500,
									},
								},
							},
						},
					},
				}, nil).Times(1)

				// user group constraints mock
				f.userGroupClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(),
					gomock.Any()).Return(&userGroupPb.GetGroupsMappedToEmailResponse{
					Status: rpc.StatusOk(),
					Groups: []commontypes.UserGroup{commontypes.UserGroup_INTERNAL},
				}, nil).MaxTimes(1)
				f.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: "actor-id-1",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-id-1",
					},
				}, nil).MaxTimes(2)
			},
		},
		{
			name: "should successfully return auto save suggestions with monthly rule customization",
			args: args{
				ctx: context.Background(),
				req: &feDepositPb.GetAutoSaveSuggestionsRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					DepositType: accounts.Type_SMART_DEPOSIT,
					Amount: &typesPb.Money{
						CurrencyCode: "INR",
						Units:        2345,
					},
					Term: &types.DepositTerm{
						Months: 0,
						Days:   5,
					},
					GoalDetails: &feDepositPb.GetAutoSaveSuggestionsRequest_GoalDetails{
						TargetAmount: &typesPb.Money{
							CurrencyCode: "INR",
							Units:        10000,
						},
					},
					DepositName: "abc deposit name",
					TemplateId:  "template-id-1",
					CustomizedAutoSaveParams: &feDepositPb.DepositAutoSaveParams{
						RuleId: "bb093acb-fb94-4aa4-abe6-1d85a91feba2",
						RuleParamValues: &fitttPb.RuleParamValues{
							RuleParamValues: map[string]*fitttPb.Value{
								"depositAmount": {
									Value: &fitttPb.Value_MoneyVal{
										MoneyVal: &types.Money{
											CurrencyCode: "INR",
											Units:        2500,
										},
									},
								},
								"configuredDateOfMonth": {
									Value: &fitttPb.Value_DateOfMonthVal{
										DateOfMonthVal: &fitttPb.DateOfMonthVal{
											Date:          22,
											DisplayString: "22nd",
										},
									},
								},
							},
						},
					},
				},
			},
			want: &feDepositPb.GetAutoSaveSuggestionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},

				AutoSaveSetupTile: &feDepositPb.AutoSaveSetupTileV2{
					Title:       ui.GetText("How do you want to save?", "#333333", commontypes.FontStyle_HEADLINE_3),
					Description: ui.GetText("Select a rule to save regularly after creating this deposit", "#8D8D8D", commontypes.FontStyle_BODY_XS),
					Options: []*feDepositPb.AutoSaveSuggestionOptionV2{
						{
							Title: []*commontypes.Text{
								ui.GetText("Save ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("₹50 everyday", "#333333", commontypes.FontStyle_SUBTITLE_3),
							},
							Description: ui.GetText("Total savings: ₹250", "#8D8D8D", commontypes.FontStyle_BODY_4_PARA),
							EditCta: &deeplinkPb.Cta{
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_FIT_CUSTOMISE_RULE_SCREEN,
									ScreenOptions: &deeplinkPb.Deeplink_FitCustomiseRuleScreenOptions{
										FitCustomiseRuleScreenOptions: &deeplinkPb.FitCustomiseRuleScreenOptions{
											RuleId:     "43dd9b50-786b-47c0-bf0b-61401bc8da42",
											PageType:   fitttClientstatePb.SubscriptionPageType_SUBSCRIPTION_PAGE_CAPTURE_PARAMS,
											EntryPoint: analyticsPb.AnalyticsScreenName_DEPOSIT_AUTO_SAVE_SUGGESTIONS,
											ParamValues: &rulePb.RuleParamValues{
												RuleParamValues: map[string]*rulePb.Value{
													"depositAmount": {
														Value: &rulePb.Value_MoneyVal{
															MoneyVal: &types.Money{
																CurrencyCode: "INR",
																Units:        50,
															},
														},
													},
													"depositAccountId": {
														Value: &rulePb.Value_SdValue{
															SdValue: &rulePb.SdParamValue{
																Name: "abc deposit name",
															},
														},
														EditState: rulePb.RuleParamEditState_NOT_EDITABLE_WITH_HIDDEN_OPTIONS,
													},
												},
											},
										},
									},
								},
							},
							AutoSaveParams: &feDepositPb.DepositAutoSaveParams{
								RuleId: "43dd9b50-786b-47c0-bf0b-61401bc8da42",
								RuleParamValues: &fitttPb.RuleParamValues{
									RuleParamValues: map[string]*fitttPb.Value{
										"depositAmount": {
											Value: &fitttPb.Value_MoneyVal{
												MoneyVal: &types.Money{
													CurrencyCode: "INR",
													Units:        50,
												},
											},
										},
									},
								},
							},
						},
						{
							Title: []*commontypes.Text{
								ui.GetText("Save ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("₹150 every friday", "#333333", commontypes.FontStyle_SUBTITLE_3),
							},
							EditCta: &deeplinkPb.Cta{
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_FIT_CUSTOMISE_RULE_SCREEN,
									ScreenOptions: &deeplinkPb.Deeplink_FitCustomiseRuleScreenOptions{
										FitCustomiseRuleScreenOptions: &deeplinkPb.FitCustomiseRuleScreenOptions{
											RuleId:     "7b251ad6-a653-4c8a-88fa-a3714b45b480",
											PageType:   fitttClientstatePb.SubscriptionPageType_SUBSCRIPTION_PAGE_CAPTURE_PARAMS,
											EntryPoint: analyticsPb.AnalyticsScreenName_DEPOSIT_AUTO_SAVE_SUGGESTIONS,
											ParamValues: &rulePb.RuleParamValues{
												RuleParamValues: map[string]*rulePb.Value{
													"depositAmount": {
														Value: &rulePb.Value_MoneyVal{
															MoneyVal: &types.Money{
																CurrencyCode: "INR",
																Units:        150,
															},
														},
													},
													"configuredDayOfWeek": {
														Value: &rulePb.Value_DayOfWeekVal{
															DayOfWeekVal: &rulePb.DayOfWeekVal{
																Weekday:  "Friday",
																AbbrName: "Fri",
																Enabled:  true,
															},
														},
													},
													"depositAccountId": {
														Value: &rulePb.Value_SdValue{
															SdValue: &rulePb.SdParamValue{
																Name: "abc deposit name",
															},
														},
														EditState: rulePb.RuleParamEditState_NOT_EDITABLE_WITH_HIDDEN_OPTIONS,
													},
												},
											},
										},
									},
								},
							},
							AutoSaveParams: &feDepositPb.DepositAutoSaveParams{
								RuleId: "7b251ad6-a653-4c8a-88fa-a3714b45b480",
								RuleParamValues: &fitttPb.RuleParamValues{
									RuleParamValues: map[string]*fitttPb.Value{
										"depositAmount": {
											Value: &fitttPb.Value_MoneyVal{
												MoneyVal: &types.Money{
													CurrencyCode: "INR",
													Units:        150,
												},
											},
										},
										"configuredDayOfWeek": {
											Value: &fitttPb.Value_DayOfWeekVal{
												DayOfWeekVal: &fitttPb.DayOfWeekVal{
													Weekday:  "Friday",
													AbbrName: "Fri",
													Enabled:  true,
												},
											},
										},
									},
								},
							},
						},
						{
							Title: []*commontypes.Text{
								ui.GetText("Save ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("₹2,500", "#333333", commontypes.FontStyle_SUBTITLE_3),
								ui.GetText(" on ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("22nd", "#333333", commontypes.FontStyle_SUBTITLE_3),
								ui.GetText(" of ", "#646464", commontypes.FontStyle_BODY_3),
								ui.GetText("every month", "#333333", commontypes.FontStyle_SUBTITLE_3),
							},
							IsSelected: true,
							EditCta: &deeplinkPb.Cta{
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_FIT_CUSTOMISE_RULE_SCREEN,
									ScreenOptions: &deeplinkPb.Deeplink_FitCustomiseRuleScreenOptions{
										FitCustomiseRuleScreenOptions: &deeplinkPb.FitCustomiseRuleScreenOptions{
											RuleId:     "bb093acb-fb94-4aa4-abe6-1d85a91feba2",
											PageType:   fitttClientstatePb.SubscriptionPageType_SUBSCRIPTION_PAGE_CAPTURE_PARAMS,
											EntryPoint: analyticsPb.AnalyticsScreenName_DEPOSIT_AUTO_SAVE_SUGGESTIONS,
											ParamValues: &rulePb.RuleParamValues{
												RuleParamValues: map[string]*rulePb.Value{
													"depositAmount": {
														Value: &rulePb.Value_MoneyVal{
															MoneyVal: &types.Money{
																CurrencyCode: "INR",
																Units:        2500,
															},
														},
													},
													"configuredDateOfMonth": {
														Value: &rulePb.Value_DateOfMonthVal{
															DateOfMonthVal: &rulePb.DateOfMonthVal{
																Date:          22,
																DisplayString: "22nd",
																Enabled:       true,
															},
														},
													},
													"depositAccountId": {
														Value: &rulePb.Value_SdValue{
															SdValue: &rulePb.SdParamValue{
																Name: "abc deposit name",
															},
														},
														EditState: rulePb.RuleParamEditState_NOT_EDITABLE_WITH_HIDDEN_OPTIONS,
													},
												},
											},
										},
									},
								},
							},
							AutoSaveParams: &feDepositPb.DepositAutoSaveParams{
								RuleId: "bb093acb-fb94-4aa4-abe6-1d85a91feba2",
								RuleParamValues: &fitttPb.RuleParamValues{
									RuleParamValues: map[string]*fitttPb.Value{
										"depositAmount": {
											Value: &fitttPb.Value_MoneyVal{
												MoneyVal: &types.Money{
													CurrencyCode: "INR",
													Units:        2500,
												},
											},
										},
										"configuredDateOfMonth": {
											Value: &fitttPb.Value_DateOfMonthVal{
												DateOfMonthVal: &fitttPb.DateOfMonthVal{
													Date:          22,
													DisplayString: "22nd",
													Enabled:       true,
												},
											},
										},
									},
								},
							},
						},
						{
							Title: []*commontypes.Text{
								ui.GetText("Remind me later", "#646464", commontypes.FontStyle_BODY_3),
							},
						},
					},
				},
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {

				f.time.EXPECT().Now().Return(time.Date(2022, 5, 20, 0, 0, 0, 0, datetime.IST)).Times(4)

				f.depositClient.EXPECT().GetAutoSaveSuggestions(gomock.Any(), &depositPb.GetAutoSaveSuggestionsRequest{
					DepositType: accounts.Type_SMART_DEPOSIT,
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2345,
					},
					CloseDate: timestampPb.New(time.Date(2022, 5, 25, 0, 0, 0, 0, datetime.IST)),
					GoalDetails: &depositPb.GetAutoSaveSuggestionsRequest_GoalDetails{
						TargetAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        10000,
						},
					},
				}).Return(&depositPb.GetAutoSaveSuggestionsResponse{
					Status: rpc.StatusOk(),
					AutoSaveRuleParams: []*depositPb.AutoSaveRuleParams{
						{
							Rule: &depositPb.AutoSaveRuleParams_Daily{
								Daily: &depositPb.AutoSaveDailyParams{
									Amount: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        50,
									},
								},
							},
						},
						{
							Rule: &depositPb.AutoSaveRuleParams_Weekly{
								Weekly: &depositPb.AutoSaveWeeklyParams{
									WeekDay: dayofweek.DayOfWeek_FRIDAY,
									Amount: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        150,
									},
								},
							},
						},
						{
							Rule: &depositPb.AutoSaveRuleParams_Monthly{
								Monthly: &depositPb.AutoSaveMonthlyParams{
									DayOfMonth: 1,
									Amount: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        2500,
									},
								},
							},
						},
					},
				}, nil).Times(1)

				// user group constraints mock
				f.userGroupClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(),
					gomock.Any()).Return(&userGroupPb.GetGroupsMappedToEmailResponse{
					Status: rpc.StatusOk(),
					Groups: []commontypes.UserGroup{commontypes.UserGroup_INTERNAL},
				}, nil).MaxTimes(1)
				f.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: "actor-id-1",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-id-1",
					},
				}, nil).MaxTimes(2)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			depositClient := depositMock.NewMockDepositClient(ctr)
			usersClient := usersMocks.NewMockUsersClient(ctr)
			actorClient := actorMocks.NewMockActorClient(ctr)
			userGroupClient := groupMock.NewMockGroupClient(ctr)
			timeClient := timeMocks.NewMockTime(ctr)

			f := fields{
				depositClient:   depositClient,
				usersClient:     usersClient,
				actorClient:     actorClient,
				userGroupClient: userGroupClient,
				conf:            conf,
				time:            timeClient,
			}
			if tt.prepare != nil {
				tt.prepare(&tt.args, &f)
			}
			s := &Service{
				depositClient:   depositClient,
				usersClient:     usersClient,
				actorClient:     actorClient,
				userGroupClient: userGroupClient,
				conf:            conf,
				time:            timeClient,
			}
			got, err := s.GetAutoSaveSuggestions(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAutoSaveSuggestions() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAutoSaveSuggestions() got = %v, want %v", got, tt.want)
			}
		})
	}
}
