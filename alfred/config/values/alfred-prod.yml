Application:
  Environment: "prod"
  Name: "alfred"
  IsSecureRedis: true

Server:
  Ports:
    GrpcPort: 8083
    GrpcSecurePort: 9521
    HttpPort: 9999
    HttpPProfPort: 9990

EpifiDb:
  AppName: "alfred"
  StatementTimeout: 10s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 30
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

AWS:
  Region: "ap-south-1"

SignEnabled:
  MinAndroidVersion: 269
  MinIOSVersion: 369
  FallbackToEnableFeature: true
  DisableFeature: false

EnableCopyTrackingUrl:
  MinAndroidVersion: 292
  MinIOSVersion: 2000
  FallbackToEnableFeature: true
  DisableFeature: false
