package profile

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/investment/profile"
	"github.com/epifi/gamma/api/user"
	genConf "github.com/epifi/gamma/investment/config/genconf"
	"github.com/epifi/gamma/investment/profile/dao"
	wtypes "github.com/epifi/gamma/investment/wire/types"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

type Service struct {
	profile.UnimplementedInvestmentProfileServiceServer
	cfg                      *genConf.Config
	investmentRiskSurveyDao  dao.InvestmentRiskSurveyDao
	investmentRiskProfileDao dao.InvestmentRiskProfileDao
	usersClient              user.UsersClient
	actorClient              actor.ActorClient
	consentClient            consent.ConsentClient
	eventPublisher           wtypes.NonFinancialEventSqsPublisher
}

func NewService(
	cfg *genConf.Config,
	investmentRiskSurveyDao dao.InvestmentRiskSurveyDao,
	investmentRiskProfileDao dao.InvestmentRiskProfileDao,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	consentClient consent.ConsentClient,
	eventPublisher wtypes.NonFinancialEventSqsPublisher,
) *Service {
	return &Service{
		cfg:                      cfg,
		investmentRiskSurveyDao:  investmentRiskSurveyDao,
		investmentRiskProfileDao: investmentRiskProfileDao,
		usersClient:              usersClient,
		actorClient:              actorClient,
		consentClient:            consentClient,
		eventPublisher:           eventPublisher,
	}
}

func (s *Service) GetInvestmentRiskSurveyStatus(ctx context.Context, req *profile.GetInvestmentRiskSurveyStatusRequest) (*profile.GetInvestmentRiskSurveyStatusResponse, error) {
	actorId := req.GetActorId()
	investmentRiskSurvey, err := s.investmentRiskSurveyDao.GetByActorId(ctx, actorId)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &profile.GetInvestmentRiskSurveyStatusResponse{
				Status:       rpc.StatusOk(),
				SurveyStatus: profile.SurveyStatus_SURVEY_STATUS_UNSPECIFIED,
			}, nil
		}
		logger.Error(ctx, "error in GetInvestmentRiskSurveyStatus, GetByActorId", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return &profile.GetInvestmentRiskSurveyStatusResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in GetByActorId: %s", err.Error())),
		}, nil
	}
	return &profile.GetInvestmentRiskSurveyStatusResponse{
		Status:       rpc.StatusOk(),
		SurveyStatus: investmentRiskSurvey.GetSurveyStatus(),
	}, nil
}

func (s *Service) GetRiskLevel(ctx context.Context, req *profile.GetRiskLevelRequest) (*profile.GetRiskLevelResponse, error) {
	actorId := req.GetActorId()
	investmentRiskProfile, err := s.investmentRiskProfileDao.GetByActorId(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error in GetRiskLevel, GetByActorId", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return &profile.GetRiskLevelResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in GetByActorId: %s", err.Error())),
		}, nil
	}

	return &profile.GetRiskLevelResponse{
		Status:    rpc.StatusOk(),
		RiskLevel: investmentRiskProfile.GetRiskLevel(),
	}, nil
}
