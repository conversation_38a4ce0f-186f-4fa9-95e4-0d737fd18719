package cams

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"reflect"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	pb "github.com/epifi/gamma/api/investment/mutualfund"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	fgPb "github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
	"github.com/epifi/gamma/api/investment/mutualfund/order/reverse_feed"
	"github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund"
	wob "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/investment/mutualfund/order/reverse_feed/vendor_processor/cams"
	wtypes "github.com/epifi/gamma/investment/wire/types"
)

type WBR9FeedFileProcessor struct {
	catalogManagerClient catalogPb.CatalogManagerClient
	s3Client             wtypes.CamsS3Client
	wobClient            wob.WealthOnboardingClient
	fileGeneratorClient  fgPb.FileGeneratorClient
	mutualFundClient     mutualfund.MutualFundClient
}

func NewWBR9FeedFileProcessor(catalogManagerClient catalogPb.CatalogManagerClient, s3Client wtypes.CamsS3Client,
	wobClient wob.WealthOnboardingClient, fileGeneratorClient fgPb.FileGeneratorClient, mutualFundClient mutualfund.MutualFundClient) *WBR9FeedFileProcessor {
	return &WBR9FeedFileProcessor{
		catalogManagerClient: catalogManagerClient,
		s3Client:             s3Client,
		wobClient:            wobClient,
		fileGeneratorClient:  fileGeneratorClient,
		mutualFundClient:     mutualFundClient,
	}
}

//nolint:funlen
func (w *WBR9FeedFileProcessor) ProcessProfileInfoFileContent(ctx context.Context, filePath string) ([]string, []string, []*reverse_feed.MisMatchFolioData, string, error) {
	readRes, err := w.s3Client.Read(ctx, filePath)
	if err != nil {
		return nil, nil, nil, "", errors.Wrap(err, "error while reading feed file from s3")
	}

	reader := csv.NewReader(bytes.NewReader(readRes))
	reader.Comma = '\t'
	feedRes, err := reader.ReadAll()
	if err != nil {
		return nil, nil, nil, "", errors.Wrap(err, "error while reading feed file")
	}

	var wbr9FeedRows []*WBR9File
	for _, feed := range feedRes {
		wbR9file := WBR9File{}
		for keyIdx, value := range feed {
			// The actual wbr9 files has some additional legacy columns which are present in the template. These columns can be ignored.
			if keyIdx >= len(WBR9FiledMap) {
				break
			}

			keyName, keyOk := WBR9FiledMap[keyIdx]
			if !keyOk {
				logger.ErrorNoCtx(fmt.Sprintf("error while fetching key name using index, keyIdx: %v", keyIdx))
				continue
			}
			reflect.ValueOf(&wbR9file).Elem().FieldByName(keyName).SetString(value)
		}
		logger.Debug(ctx, "value of struct", zap.Any("struct", wbR9file))
		wbr9FeedRows = append(wbr9FeedRows, &wbR9file)
	}

	logger.Debug(ctx, fmt.Sprintf("extracted data: %v", wbr9FeedRows))

	var processedActorAndAmcs = make(map[string]bool)

	var successfulFolioNumbers []string
	var missingFolioNumbers []string
	var misMatchFolioData []*reverse_feed.MisMatchFolioData
	var fileIDs []string

	var sb strings.Builder

	for _, wbr9 := range wbr9FeedRows {

		amc, amcErr := cams.GetAMC(wbr9.AMCCode)
		if amcErr != nil {
			return nil, nil, nil, "", amcErr
		}

		schemeCode := wbr9.ProductCode[len(wbr9.AMCCode):]

		folioNumberToDetailsMap, folioFetchErr := w.catalogManagerClient.GetFolioDetails(ctx, &catalogPb.GetFolioDetailsRequest{
			FolioIds:             []string{wbr9.FolioNumber},
			MutualFundIdentifier: &catalogPb.GetFolioDetailsRequest_SchemeCode{SchemeCode: schemeCode},
			Amc:                  amc})
		if grpcError := epifigrpc.RPCError(folioNumberToDetailsMap, folioFetchErr); grpcError != nil {
			logger.Error(ctx, "error in GetFolioDetails", zap.String(logger.FOLIO_ID, wbr9.FolioNumber),
				zap.String(logger.MF_AMC, amc.String()), zap.Error(grpcError))
			continue
		}

		folioDetails := folioNumberToDetailsMap.FolioIdToDetailsMap[wbr9.FolioNumber]
		actorID := folioDetails.FolioLedger.ActorId

		if processedActorAndAmcs[actorID+"_"+amc.String()] {
			logger.Info(ctx, "nft file for folio is already generated", zap.String(logger.FOLIO_ID, folioDetails.FolioLedger.FolioId),
				zap.String(logger.ACTOR_ID_V2, folioDetails.FolioLedger.ActorId), zap.String(logger.MF_AMC, amc.String()))
			continue
		}

		preinvestmentDetails, wobErr := w.getPreInvestmentDetail(ctx, actorID)
		if wobErr != nil {
			return nil, nil, nil, "", wobErr
		}

		isSameData, mismatchData := w.isFolioDataSame(ctx, wbr9.FolioNumber, folioDetails, wbr9, preinvestmentDetails)

		if isSameData {
			successfulFolioNumbers = append(successfulFolioNumbers, wbr9.FolioNumber)
		} else {
			if mismatchData != nil {
				nftProcessErr := w.generateAndSentNFTFileToVendor(ctx, actorID, amc, wbr9.MobileNumber)
				if nftProcessErr != nil {
					logger.Error(ctx, "error in generateAndSentNFTFileToVendor", zap.Error(nftProcessErr), zap.String(logger.ACTOR_ID_V2, actorID), zap.String(logger.MF_AMC, amc.String()))
					sb.WriteString(fmt.Sprintf("generateAndSentNFTFileToVendor failed for actorID: %s with reason: %v \n", actorID, nftProcessErr))
					continue
				}
				misMatchFolioData = append(misMatchFolioData, mismatchData)
				processedActorAndAmcs[actorID+"_"+amc.String()] = true
			} else {
				missingFolioNumbers = append(missingFolioNumbers, wbr9.FolioNumber)
			}
		}

	}
	logger.Debug(ctx, "fileIds", zap.Any("fileIds", fileIDs))

	return successfulFolioNumbers, missingFolioNumbers, misMatchFolioData, sb.String(), nil
}

func (w *WBR9FeedFileProcessor) generateAndSentNFTFileToVendor(ctx context.Context, actorID string, amc pb.Amc, vendorMobileNumber string) error {

	res, fgErr := w.fileGeneratorClient.GenerateNFTFile(ctx, &fgPb.GenerateNFTFileRequest{
		ActorId:         actorID,
		Amc:             amc,
		Vendor:          commonvgpb.Vendor_CAMS,
		OldMobileNumber: vendorMobileNumber,
	})
	if grpcErr := epifigrpc.RPCError(res, fgErr); grpcErr != nil {
		logger.Error(ctx, "error in generating nft file",
			zap.String(logger.ACTOR_ID, actorID), zap.String(logger.MF_AMC, amc.String()), zap.Error(grpcErr))
		return grpcErr
	}

	vgRes, vgErr := w.mutualFundClient.ProcessNFTFile(ctx, &mutualfund.ProcessNFTFileRequest{
		Header:                         &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_CAMS},
		FilePath:                       res.FilePath,
		AcknowledgementImagesFilePaths: res.AcknowledgementImagesFilePaths,
	})
	if grpcErr := epifigrpc.RPCError(vgRes, vgErr); grpcErr != nil {
		logger.Error(ctx, "error in generating ProcessNFTFile",
			zap.String(logger.ACTOR_ID, actorID), zap.String(logger.MF_AMC, amc.String()), zap.Error(grpcErr))
		return grpcErr
	}

	return nil
}

// If folio data matches then (true, nil) is returned
// If folio data mismatches, (false, &reverse_feed.MisMatchFolioData{}) is returned
// If there is no folio data present on our db to compare with, then false, nil is returned
func (w *WBR9FeedFileProcessor) isFolioDataSame(ctx context.Context, folioNumber string,
	folioNumberDetails *catalogPb.GetFolioDetailsOutput,
	vendorFolioNumberDetails *WBR9File, preInvestmentDetails *wob.PreInvestmentDetail) (bool, *reverse_feed.MisMatchFolioData) {

	if folioNumberDetails.Status != catalogPb.GetFolioDetailsStatus_GetFolioDetailsStatus_NOT_FOUND {
		vendorMobileNumber := vendorFolioNumberDetails.MobileNumber
		preInvestmentMobileNumber := preInvestmentDetails.MobileNo.ToString()

		if strings.Compare(vendorMobileNumber[len(vendorMobileNumber)-10:], preInvestmentMobileNumber[len(vendorMobileNumber)-10:]) == 0 {
			return true, nil
		} else {
			return false, w.getMisMatchFolioData(folioNumberDetails.FolioLedger, vendorMobileNumber, preInvestmentMobileNumber)
		}
	} else {
		return false, nil
	}
}

func (w *WBR9FeedFileProcessor) getMisMatchFolioData(folioLedger *pb.FolioLedger, vendorMobileNumber string, wobMobileNumebr string) *reverse_feed.MisMatchFolioData {
	return &reverse_feed.MisMatchFolioData{
		FolioNumber:     folioLedger.FolioId,
		ActorId:         folioLedger.ActorId,
		EpifiFolioData:  &reverse_feed.FolioData{MobileNumber: wobMobileNumebr},
		VendorFolioData: &reverse_feed.FolioData{MobileNumber: vendorMobileNumber},
	}
}

func (w *WBR9FeedFileProcessor) groupBySchemeCodeAndAMC(wbr9FeedRows []*WBR9File) map[string][]*WBR9File {

	schemeCodeToWBR9Map := make(map[string][]*WBR9File)

	for _, val := range wbr9FeedRows {
		// ProductCode is AMC_Code + SchemeCode. For eg: P8189 is a valid product code with
		schemeCode := val.ProductCode[len(val.AMCCode):]
		schemeCodeToWBR9Map[schemeCode+"_"+val.AMCCode] = append(schemeCodeToWBR9Map[schemeCode+"_"+val.AMCCode], val)
	}

	return schemeCodeToWBR9Map
}

func (s *WBR9FeedFileProcessor) getPreInvestmentDetail(ctx context.Context, actorID string) (*wob.PreInvestmentDetail, error) {

	actorIdDetailsMap, err := s.wobClient.GetInvestmentData(ctx, &wob.GetInvestmentDataRequest{ActorIds: []string{actorID}})
	if grpcError := epifigrpc.RPCError(actorIdDetailsMap, err); grpcError != nil {
		return nil, err
	}
	return actorIdDetailsMap.InvestmentDetailInfo[actorID], nil
}
