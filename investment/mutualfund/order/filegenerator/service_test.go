package filegenerator

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	genConf "github.com/epifi/gamma/investment/config/genconf"

	s3types "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	pb "github.com/epifi/gamma/api/investment/mutualfund"
	fgPb "github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
	mocks2 "github.com/epifi/gamma/api/investment/mutualfund/order/mocks"
	daoMocks "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/dao/mocks"
	"github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/file_name_generator"
	vendorProcessorMock "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/mocks"
)

// service test suite
type svcTestSuite struct {
	conf *genConf.Config
}

func newSvcTestSuite(conf *genConf.Config) *svcTestSuite {
	return &svcTestSuite{
		conf: conf,
	}
}

// nolint
var (
	svcTS       *svcTestSuite
	txnExecutor storagev2.IdempotentTxnExecutor
	ctx         = context.Background()
)

type OrderFeedFileGenerationArgs struct {
	ctx context.Context
	req *fgPb.GenerateOrderFeedFileRequest
}

type OrderFeedFileGenerationTest struct {
	name           string
	args           OrderFeedFileGenerationArgs
	setupMockCalls func()
	want           *fgPb.GenerateOrderFeedFileResponse
	wantErr        bool
}

func TestService_GenerateOrderFeedFile(t *testing.T) {
	ctr := gomock.NewController(t)
	mockEntityFileMapperDao := daoMocks.NewMockEntityFileMapperDao(ctr)
	mockFileGenerationAttemptDao := daoMocks.NewMockFileGenerationAttemptDao(ctr)
	mockS3Client := mocks.NewMockS3Client(ctr)
	mockVendorProcessor := vendorProcessorMock.NewMockVendorProcessor(ctr)
	mockOrderManagerClient := mocks2.NewMockOrderManagerClient(ctr)

	vendorProcessorFactory := &VendorProcessorFactory{camsVendorProcessor: mockVendorProcessor}
	filegeneratorSvc := NewService(vendorProcessorFactory, svcTS.conf, mockFileGenerationAttemptDao, mockEntityFileMapperDao, mockOrderManagerClient, nil, txnExecutor, nil, nil, nil, nil)

	tests := []OrderFeedFileGenerationTest{
		newOrderFeedFileWithSuccessfulOrders(mockEntityFileMapperDao, mockFileGenerationAttemptDao, mockVendorProcessor, mockS3Client),
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			tt.setupMockCalls()
			got, err := filegeneratorSvc.GenerateOrderFeedFile(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateOrderFeedFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(pb.AmcInfo{}, "CreatedAt", "UpdatedAt", "DeletedAt"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GenerateOrderFeedFile() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}

func newOrderFeedFileWithSuccessfulOrders(mockEntityFileMapperDao *daoMocks.MockEntityFileMapperDao,
	mockFileGenerationAttemptDao *daoMocks.MockFileGenerationAttemptDao, mockVendorProcessor *vendorProcessorMock.MockVendorProcessor,
	mockS3Client *mocks.MockS3Client) OrderFeedFileGenerationTest {

	fileId := uuid.New().String()
	order1 := fgPb.EntityFileMap{
		Id:         uuid.New().String(),
		EntityId:   uuid.New().String(),
		IsActive:   true,
		FileId:     fileId,
		VendorName: commonvgpb.Vendor_CAMS,
		EntityType: fgPb.EntityType_ENTITY_TYPE_ORDER_FEED,
		Status:     fgPb.EntityFileGenStatus_ENTITY_FILE_GEN_STATUS_INITIATED,
		SubStatus:  fgPb.EntitySubStatus_ENTITY_SUB_STATUS_UNSPECIFIED,
		CreatedAt:  timestamppb.Now(),
		UpdatedAt:  timestamppb.Now(),
	}
	var order2 fgPb.EntityFileMap
	_ = copier.Copy(&order2, &order1)
	order2.Id = uuid.New().String()
	order2.EntityId = uuid.New().String()

	clientRequestId := uuid.New().String()

	timeZoneLocation, _ := time.LoadLocation(istTimeZoneLocation)

	newFGA := fgPb.FileGenerationAttempt{
		Id:              fileId,
		FileName:        "",
		Status:          fgPb.FileStatus_FILE_STATUS_CREATION_INITIATED,
		FileType:        fgPb.FileType_FILE_TYPE_ORDER_FEED,
		VendorName:      commonvgpb.Vendor_CAMS,
		ClientRequestId: clientRequestId,
		CreatedAt:       timestamppb.Now(),
		UpdatedAt:       timestamppb.Now(),
	}

	fileName := "randomFile.txt"

	return OrderFeedFileGenerationTest{
		name: "generate a new file with all orders being new",
		args: OrderFeedFileGenerationArgs{
			ctx: ctx,
			req: &fgPb.GenerateOrderFeedFileRequest{
				Vendor:          commonvgpb.Vendor_CAMS,
				OrderIds:        []string{order1.EntityId, order2.EntityId},
				ClientRequestId: clientRequestId,
			},
		},
		want: &fgPb.GenerateOrderFeedFileResponse{
			Status:                      &rpc.Status{Code: uint32(fgPb.GenerateOrderFeedFileResponse_OK)},
			FileGenerationAttemptStatus: fgPb.FileGenAttemptStatus_GENERATION_SUCCESS,
			SuccessfulOrderIds:          []string{order1.EntityId, order2.EntityId},
			FailedOrderInfo:             make(map[string]fgPb.EntitySubStatus),
			FilePath:                    "CAMS/" + newFGA.CreatedAt.AsTime().In(timeZoneLocation).Format(DateFormat) + "/FILE_TYPE_ORDER_FEED/" + fileName,
		},
		setupMockCalls: func() {

			// create a new file generation attempt
			mockFileGenerationAttemptDao.EXPECT().CreateFileGenerationAttempt(gomock.Any(), &fgPb.FileGenerationAttempt{
				Status:          fgPb.FileStatus_FILE_STATUS_CREATION_INITIATED,
				FileType:        fgPb.FileType_FILE_TYPE_ORDER_FEED,
				VendorName:      commonvgpb.Vendor_CAMS,
				ClientRequestId: clientRequestId,
			}).Return(&newFGA, nil)

			// try to insert all orderIds for the fileId
			mockEntityFileMapperDao.EXPECT().CreateEntityFileMapperBatch(gomock.Any(),
				newEntityFileMapArrayMatcher([]*fgPb.EntityFileMap{
					{EntityId: order1.EntityId, IsActive: true, FileId: fileId, VendorName: commonvgpb.Vendor_CAMS,
						EntityType: fgPb.EntityType_ENTITY_TYPE_ORDER_FEED, Status: fgPb.EntityFileGenStatus_ENTITY_FILE_GEN_STATUS_INITIATED,
					},
					{EntityId: order2.EntityId, IsActive: true, FileId: fileId, VendorName: commonvgpb.Vendor_CAMS,
						EntityType: fgPb.EntityType_ENTITY_TYPE_ORDER_FEED, Status: fgPb.EntityFileGenStatus_ENTITY_FILE_GEN_STATUS_INITIATED,
					},
				})).Return(nil)

			// fetch all orderIds that were added to the file
			mockEntityFileMapperDao.EXPECT().GetEntityFileMapperBatchByFileId(gomock.Any(), newFGA.Id).Times(1).Return([]*fgPb.EntityFileMap{&order1, &order2}, nil)

			// process order feed file
			mockVendorProcessor.EXPECT().ProcessOrderFeedFile(gomock.Any(), []string{order1.EntityId, order2.EntityId}).Return("", []string{order1.EntityId, order2.EntityId}, make(map[string]fgPb.EntitySubStatus), nil)

			var newFGACopy fgPb.FileGenerationAttempt
			_ = copier.Copy(&newFGACopy, &newFGA)
			newFGACopy.Id = fileId

			// fetch file generation attempt in transaction with lock
			mockFileGenerationAttemptDao.EXPECT().GetFileGenerationAttemptByIdWithLock(gomock.Any(), newFGA.Id).Return(&newFGACopy, nil)

			// generate filename
			mockVendorProcessor.EXPECT().GenerateFileName(gomock.Any(), &file_name_generator.FileNameGeneratorInput{
				Vendor:       commonvgpb.Vendor_CAMS,
				FileType:     fgPb.FileType_FILE_TYPE_ORDER_FEED,
				Time:         newFGA.CreatedAt.AsTime(),
				TotalRecords: 2,
				AMCCode:      pb.Amc_AMC_UNSPECIFIED,
			}).Return(fileName, nil)

			mockVendorProcessor.EXPECT().GetS3Client().Return(mockS3Client)

			// Update Filename
			mockFileGenerationAttemptDao.EXPECT().UpdateFileGenerationAttempt(gomock.Any(), &fgPb.FileGenerationAttempt{Id: fileId, FileName: fileName},
				[]fgPb.FileGenerationAttemptFieldMask{fgPb.FileGenerationAttemptFieldMask_FILE_GENERATION_ATTEMPT_FIELD_MASK_FILE_NAME}).Return(nil)

			// Upload file
			filePath := "CAMS/" + newFGA.CreatedAt.AsTime().In(timeZoneLocation).Format(DateFormat) + "/FILE_TYPE_ORDER_FEED/" + fileName
			mockS3Client.EXPECT().Write(gomock.Any(), filePath, gomock.Any(), string(s3types.ObjectCannedACLBucketOwnerFullControl)).Return(nil)

			var newFGACopy2 fgPb.FileGenerationAttempt
			_ = copier.Copy(&newFGACopy2, &newFGA)
			newFGACopy2.Status = fgPb.FileStatus_FILE_STATUS_UPLOAD_SUCCESSFUL

			// Update file generation attempt status to successful
			mockFileGenerationAttemptDao.EXPECT().UpdateFileGenerationAttempt(gomock.Any(), &newFGACopy2, []fgPb.FileGenerationAttemptFieldMask{fgPb.FileGenerationAttemptFieldMask_FILE_GENERATION_ATTEMPT_FIELD_MASK_FILE_STATUS}).Return(nil)

			// Update for successful entityIds
			mockEntityFileMapperDao.EXPECT().UpdateEntityFileMapperByBatch(gomock.Any(), []string{order1.Id, order2.Id}, fileId,
				&fgPb.EntityFileMap{Status: fgPb.EntityFileGenStatus_ENTITY_FILE_GEN_STATUS_UPLOADED,
					SubStatus: fgPb.EntitySubStatus_ENTITY_SUB_STATUS_SUCCESSFUL},
				[]fgPb.EntityFileMapFieldMask{fgPb.EntityFileMapFieldMask_ENTITY_FILE_MAP_MASK_STATUS,
					fgPb.EntityFileMapFieldMask_ENTITY_FILE_MAP_MASK_SUB_STATUS}).Times(1).Return(nil)

			var newFGACopy3 fgPb.FileGenerationAttempt
			_ = copier.Copy(&newFGACopy3, &newFGA)
			newFGACopy3.FileName = fileName
			newFGACopy.Id = fileId

			mockFileGenerationAttemptDao.EXPECT().GetFileGenerationAttemptByClientRequestId(gomock.Any(), newFGA.ClientRequestId).Return(&newFGACopy3, nil)

			var order1Copy fgPb.EntityFileMap
			var order2Copy fgPb.EntityFileMap
			_ = copier.Copy(&order1Copy, &order1)
			_ = copier.Copy(&order2Copy, &order2)
			order1Copy.Status = fgPb.EntityFileGenStatus_ENTITY_FILE_GEN_STATUS_UPLOADED
			order2Copy.Status = fgPb.EntityFileGenStatus_ENTITY_FILE_GEN_STATUS_UPLOADED
			mockEntityFileMapperDao.EXPECT().GetEntityFileMapperBatchByFileId(gomock.Any(), newFGA.Id).Times(1).Return([]*fgPb.EntityFileMap{&order1Copy, &order2Copy}, nil)

		},
		wantErr: false,
	}
}

type EntityFileMapArrayMatcher struct {
	want []*fgPb.EntityFileMap
}

func newEntityFileMapArrayMatcher(want []*fgPb.EntityFileMap) *EntityFileMapArrayMatcher {
	return &EntityFileMapArrayMatcher{want: want}
}

func (f *EntityFileMapArrayMatcher) Matches(x interface{}) bool {

	got, ok := x.([]*fgPb.EntityFileMap)
	if !ok || len(f.want) != len(got) {
		return false
	}

	for i, w := range got {
		f.want[i] = w
	}

	return reflect.DeepEqual(f.want, got)
}

func (f *EntityFileMapArrayMatcher) String() string {
	return fmt.Sprintf("want: %v", f.want)
}
