package mocks

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"reflect"
	"time"

	"github.com/golang/mock/gomock"

	fgPb "github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
)

// MockEntityFileMapperDao is a mock of MutualFundDao interface
type MockEntityFileMapperDao struct {
	ctrl     *gomock.Controller
	recorder *MockEntityFileMapperDaoMockRecorder
}

// MockEntityFileMapperDaoMockRecorder is the mock recorder for MockMutualFundDao
type MockEntityFileMapperDaoMockRecorder struct {
	mock *MockEntityFileMapperDao
}

func NewMockEntityFileMapperDao(ctrl *gomock.Controller) *MockEntityFileMapperDao {
	mock := &MockEntityFileMapperDao{ctrl: ctrl}
	mock.recorder = &MockEntityFileMapperDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockEntityFileMapperDao) EXPECT() *MockEntityFileMapperDaoMockRecorder {
	return m.recorder
}

// CreateEntityFileMapperBatch mocks base method
func (m *MockEntityFileMapperDao) CreateEntityFileMapperBatch(arg0 context.Context, arg1 []*fgPb.EntityFileMap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateEntityFileMapperBatch", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateEntityFileMapperBatch indicates an expected call of Create
func (mr *MockEntityFileMapperDaoMockRecorder) CreateEntityFileMapperBatch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEntityFileMapperBatch",
		reflect.TypeOf((*MockEntityFileMapperDao)(nil).CreateEntityFileMapperBatch), arg0, arg1)
}

// GetEntityFileMapperBatchByFileId mocks base method
func (m *MockEntityFileMapperDao) GetEntityFileMapperBatchByFileId(arg0 context.Context, arg1 string) ([]*fgPb.EntityFileMap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityFileMapperBatchByFileId", arg0, arg1)
	ret0 := ret[0].([]*fgPb.EntityFileMap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityFileMapperBatchByFileId indicates an expected call of GetById
func (mr *MockEntityFileMapperDaoMockRecorder) GetEntityFileMapperBatchByFileId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityFileMapperBatchByFileId",
		reflect.TypeOf((*MockEntityFileMapperDao)(nil).GetEntityFileMapperBatchByFileId), arg0, arg1)
}

// UpdateEntityFileMapperByBatch mocks base method
func (m *MockEntityFileMapperDao) UpdateEntityFileMapperByBatch(arg0 context.Context, arg1 []string, arg2 string,
	arg3 *fgPb.EntityFileMap, arg4 []fgPb.EntityFileMapFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEntityFileMapperByBatch", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEntityFileMapperByBatch indicates an expected call of Update
func (mr *MockEntityFileMapperDaoMockRecorder) UpdateEntityFileMapperByBatch(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEntityFileMapperByBatch",
		reflect.TypeOf((*MockEntityFileMapperDao)(nil).UpdateEntityFileMapperByBatch), arg0, arg1, arg2, arg3, arg4)
}

// UpdateEntityFileMapperByBatchByEntityID mocks base method
func (m *MockEntityFileMapperDao) UpdateEntityFileMapperByBatchByEntityID(arg0 context.Context, arg1 []string,
	arg2 commonvgpb.Vendor, arg3 fgPb.EntityType, arg4 *fgPb.EntityFileMap, arg5 []fgPb.EntityFileMapFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEntityFileMapperByBatchByEntityID", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEntityFileMapperByBatchByEntityID indicates an expected call of Update
func (mr *MockEntityFileMapperDaoMockRecorder) UpdateEntityFileMapperByBatchByEntityID(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEntityFileMapperByBatchByEntityID",
		reflect.TypeOf((*MockEntityFileMapperDao)(nil).UpdateEntityFileMapperByBatch), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetActiveEntityDetails mocks base method
func (m *MockEntityFileMapperDao) GetActiveEntityDetails(arg0 context.Context, arg1 string, arg2 commonvgpb.Vendor,
	arg3 fgPb.EntityType) (*fgPb.EntityFileMap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveEntityDetails", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*fgPb.EntityFileMap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveEntityDetails indicates an expected call of Update
func (mr *MockEntityFileMapperDaoMockRecorder) GetActiveEntityDetails(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveEntityDetails",
		reflect.TypeOf((*MockEntityFileMapperDao)(nil).GetActiveEntityDetails), arg0, arg1, arg2, arg3)
}

// CreateEntityFileMapper mocks base method
func (m *MockEntityFileMapperDao) CreateEntityFileMapper(arg0 context.Context, arg1 *fgPb.EntityFileMap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateEntityFileMapper", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateEntityFileMapper indicates an expected call of Create
func (mr *MockEntityFileMapperDaoMockRecorder) CreateEntityFileMapper(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEntityFileMapper",
		reflect.TypeOf((*MockEntityFileMapperDao)(nil).CreateEntityFileMapper), arg0, arg1)
}

type MockFileGenerationAttemptDao struct {
	ctrl     *gomock.Controller
	recorder *MockFileGenerationAttemptDaoMockRecorder
}

// MockFileGenerationAttemptDaoMockRecorder is the mock recorder for MockMutualFundDao
type MockFileGenerationAttemptDaoMockRecorder struct {
	mock *MockFileGenerationAttemptDao
}

func NewMockFileGenerationAttemptDao(ctrl *gomock.Controller) *MockFileGenerationAttemptDao {
	mock := &MockFileGenerationAttemptDao{ctrl: ctrl}
	mock.recorder = &MockFileGenerationAttemptDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockFileGenerationAttemptDao) EXPECT() *MockFileGenerationAttemptDaoMockRecorder {
	return m.recorder
}

// CreateFileGenerationAttempt mocks base method
func (m *MockFileGenerationAttemptDao) CreateFileGenerationAttempt(arg0 context.Context, arg1 *fgPb.FileGenerationAttempt) (
	*fgPb.FileGenerationAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateFileGenerationAttempt", arg0, arg1)
	ret0, _ := ret[0].(*fgPb.FileGenerationAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateFileGenerationAttempt indicates an expected call of Create
func (mr *MockFileGenerationAttemptDaoMockRecorder) CreateFileGenerationAttempt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateFileGenerationAttempt",
		reflect.TypeOf((*MockFileGenerationAttemptDao)(nil).CreateFileGenerationAttempt), arg0, arg1)
}

// GetFileGenerationAttemptByClientRequestId mocks base method
func (m *MockFileGenerationAttemptDao) GetFileGenerationAttemptByClientRequestId(arg0 context.Context, arg1 string) (
	*fgPb.FileGenerationAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFileGenerationAttemptByClientRequestId", arg0, arg1)
	ret0 := ret[0].(*fgPb.FileGenerationAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFileGenerationAttemptByClientRequestId indicates an expected call of GetById
func (mr *MockFileGenerationAttemptDaoMockRecorder) GetFileGenerationAttemptByClientRequestId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFileGenerationAttemptByClientRequestId",
		reflect.TypeOf((*MockFileGenerationAttemptDao)(nil).GetFileGenerationAttemptByClientRequestId), arg0, arg1)
}

// GetFileGenerationAttemptByIdWithLock mocks base method
func (m *MockFileGenerationAttemptDao) GetFileGenerationAttemptByIdWithLock(arg0 context.Context, arg1 string) (
	*fgPb.FileGenerationAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFileGenerationAttemptByIdWithLock", arg0, arg1)
	ret0, _ := ret[0].(*fgPb.FileGenerationAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFileGenerationAttemptByIdWithLock indicates an expected call of Update
func (mr *MockFileGenerationAttemptDaoMockRecorder) GetFileGenerationAttemptByIdWithLock(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFileGenerationAttemptByIdWithLock",
		reflect.TypeOf((*MockFileGenerationAttemptDao)(nil).GetFileGenerationAttemptByIdWithLock), arg0, arg1)
}

// GetFileGenerationAttemptById mocks base method
func (m *MockFileGenerationAttemptDao) GetFileGenerationAttemptById(arg0 context.Context, arg1 string) (
	*fgPb.FileGenerationAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFileGenerationAttemptById", arg0, arg1)
	ret0, _ := ret[0].(*fgPb.FileGenerationAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFileGenerationAttemptById indicates an expected call of Update
func (mr *MockFileGenerationAttemptDaoMockRecorder) GetFileGenerationAttemptById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFileGenerationAttemptById",
		reflect.TypeOf((*MockFileGenerationAttemptDao)(nil).GetFileGenerationAttemptById), arg0, arg1)
}

// UpdateFileGenerationAttempt mocks base method
func (m *MockFileGenerationAttemptDao) UpdateFileGenerationAttempt(arg0 context.Context,
	arg1 *fgPb.FileGenerationAttempt, arg2 []fgPb.FileGenerationAttemptFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFileGenerationAttempt", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateFileGenerationAttempt indicates an expected call of Update
func (mr *MockFileGenerationAttemptDaoMockRecorder) UpdateFileGenerationAttempt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFileGenerationAttempt",
		reflect.TypeOf((*MockFileGenerationAttemptDao)(nil).UpdateFileGenerationAttempt), arg0, arg1, arg2)
}

// GetFileGenerationAttemptByDate mocks base method
func (m *MockFileGenerationAttemptDao) GetFileGenerationAttemptByDate(arg0 context.Context,
	arg1 time.Time, arg2 time.Time, arg3 fgPb.FileType) ([]*fgPb.FileGenerationAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFileGenerationAttemptByDate", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*fgPb.FileGenerationAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFileGenerationAttemptByDate indicates an expected call of GetFileGenerationAttemptByDate
func (mr *MockFileGenerationAttemptDaoMockRecorder) GetFileGenerationAttemptByDate(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFileGenerationAttemptByDate",
		reflect.TypeOf((*MockFileGenerationAttemptDao)(nil).GetFileGenerationAttemptByDate), arg0, arg1, arg2, arg3)
}

// GetFileGenerationAttemptByIdBatch mocks base method
func (m *MockFileGenerationAttemptDao) GetFileGenerationAttemptByIdBatch(arg0 context.Context, arg1 []string) (map[string]*fgPb.FileGenerationAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFileGenerationAttemptByIdBatch", arg0, arg1)
	ret0, _ := ret[0].(map[string]*fgPb.FileGenerationAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFileGenerationAttemptByIdBatch indicates an expected call of GetFileGenerationAttemptByIdBatch
func (mr *MockFileGenerationAttemptDaoMockRecorder) GetFileGenerationAttemptByIdBatch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFileGenerationAttemptByIdBatch",
		reflect.TypeOf((*MockFileGenerationAttemptDao)(nil).GetFileGenerationAttemptByIdBatch), arg0, arg1)
}
