package processor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"

	"go.uber.org/zap"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	devPb "github.com/epifi/gamma/api/investment/mutualfund/developer"
	"github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/investment/mutualfund/external/dao"
	"github.com/epifi/be-common/pkg/logger"
)

type MfSingleOtpCasImportReqProcessor struct {
	mfSingleOtpCasImportRequestDao dao.MfSingleOtpCasImportRequestDao
}

func NewMfSingleOtpCasImportReqProcessor(mfSingleOtpCasImportRequestDao dao.MfSingleOtpCasImportRequestDao) *MfSingleOtpCasImportReqProcessor {
	return &MfSingleOtpCasImportReqProcessor{
		mfSingleOtpCasImportRequestDao: mfSingleOtpCasImportRequestDao,
	}
}
func (s *MfSingleOtpCasImportReqProcessor) FetchParamList(ctx context.Context, entity devPb.MutualFundEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            ACTOR_ID,
			Label:           "ActorId",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

type byUpdatedAtMfSingleOtpCasImportRequests []*external.MfSingleOtpCasImportRequest

func (a byUpdatedAtMfSingleOtpCasImportRequests) Len() int {
	return len(a)
}
func (a byUpdatedAtMfSingleOtpCasImportRequests) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
func (a byUpdatedAtMfSingleOtpCasImportRequests) Less(i, j int) bool {
	return a[i].GetUpdatedAt().AsTime().After(a[j].GetUpdatedAt().AsTime())
}

func (s *MfSingleOtpCasImportReqProcessor) FetchData(ctx context.Context, entity devPb.MutualFundEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil to fetch mf single otp cas import requests")
	}
	var actorId string
	for _, filter := range filters {
		if filter.GetParameterName() == ACTOR_ID {
			actorId = filter.GetStringValue()
		}
	}
	mfSingleOtpCasImportReq, err := s.mfSingleOtpCasImportRequestDao.GetByActorId(ctx, actorId)
	if err != nil {
		return fmt.Sprintf("error occured fetching mf single otp cas import request, error: %v", err.Error()), nil
	}
	for _, req := range mfSingleOtpCasImportReq {
		req.VendorResponse = nil
	}
	sort.Sort(byUpdatedAtMfSingleOtpCasImportRequests(mfSingleOtpCasImportReq))
	e, marshalErr := json.Marshal(mfSingleOtpCasImportReq)
	if marshalErr != nil {
		logger.Error(ctx,
			fmt.Sprintf("cannot marshal mf single otp cas import requests to json for actorId: %s",
				actorId), zap.Error(err))
		return fmt.Sprintf("{\"error\": \"cannot marshal mf single otp cas import requests to json \", \"error\":\"%v\"}", err.Error()), nil
	}
	return string(e), nil
}
