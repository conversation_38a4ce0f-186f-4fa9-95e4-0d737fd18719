package model

import (
	"fmt"
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	catPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
)

type WatchList struct {
	// Primary identifier to the card. Unique for each entry.
	ID          string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	Name        string
	ActorId     string
	DisplayInfo *catPb.WatchListDisplayInfo
	// time of creation of the entry
	CreatedAt time.Time
	// last updated time of the entry
	UpdatedAt time.Time
	// signifies the date of soft deletion of the entry
	DeletedAt gormv2.DeletedAt
}

type WatchListFundMapping struct {
	// Primary identifier to the card. Unique for each entry.
	ID           string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	MutualFundId string
	WatchListId  string
	// time of creation of the entry
	CreatedAt time.Time
	// last updated time of the entry
	UpdatedAt time.Time
	// signifies the date of soft deletion of the entry
	DeletedAtUnix int64
	// nav at the time fund is white listed
	DisplayInfo *catPb.WatchListFundDisplayInfo
}

func (w *WatchList) ToProto() *catPb.WatchList {
	watchlist := &catPb.WatchList{
		Id:          w.ID,
		Name:        w.Name,
		ActorId:     w.ActorId,
		DisplayInfo: w.DisplayInfo,
		CreatedAt:   timestampPb.New(w.CreatedAt),
		UpdatedAt:   timestampPb.New(w.UpdatedAt),
	}
	if w.DeletedAt.Valid {
		watchlist.DeletedAt = timestampPb.New(w.DeletedAt.Time)
	}
	return watchlist
}

func NewWatchList(wat *catPb.WatchList) (*WatchList, error) {
	coll := &WatchList{
		ID:          wat.GetId(),
		Name:        wat.GetName(),
		ActorId:     wat.ActorId,
		DisplayInfo: wat.GetDisplayInfo(),
	}
	var err error
	if wat.CreatedAt != nil {
		if err = wat.CreatedAt.CheckValid(); err == nil {
			coll.CreatedAt = wat.CreatedAt.AsTime()
		} else {
			return nil, fmt.Errorf("unable to parse created at proto timestamp to time: %v : %w", wat.CreatedAt, err)
		}
	}
	if wat.UpdatedAt != nil {
		if err = wat.UpdatedAt.CheckValid(); err == nil {
			coll.UpdatedAt = wat.UpdatedAt.AsTime()
		} else {
			return nil, fmt.Errorf("unable to parse updated_at proto timestamp to time: %v : %w", wat.UpdatedAt, err)
		}

	}
	if wat.DeletedAt != nil {
		coll.DeletedAt = gormv2.DeletedAt{Time: wat.DeletedAt.AsTime(), Valid: true}
	}
	return coll, nil
}

func (c *WatchListFundMapping) ToProto() *catPb.WatchListFundMapping {
	wat := &catPb.WatchListFundMapping{
		Id:            c.ID,
		MutualFundId:  c.MutualFundId,
		WatchListId:   c.WatchListId,
		CreatedAt:     timestampPb.New(c.CreatedAt),
		UpdatedAt:     timestampPb.New(c.UpdatedAt),
		DeletedAtUnix: c.DeletedAtUnix,
		DisplayInfo:   c.DisplayInfo,
	}
	return wat
}

func NewWatchListFundMapping(watFundMapping *catPb.WatchListFundMapping) (*WatchListFundMapping, error) {

	coll := &WatchListFundMapping{
		ID:            watFundMapping.GetId(),
		MutualFundId:  watFundMapping.MutualFundId,
		WatchListId:   watFundMapping.WatchListId,
		DeletedAtUnix: watFundMapping.DeletedAtUnix,
		DisplayInfo:   watFundMapping.DisplayInfo,
	}
	var err error
	if watFundMapping.CreatedAt != nil {
		if err = watFundMapping.CreatedAt.CheckValid(); err == nil {
			coll.CreatedAt = watFundMapping.CreatedAt.AsTime()
		} else {
			return nil, fmt.Errorf("unable to parse created at proto timestamp to time: %v : %w", watFundMapping.CreatedAt, err)
		}
	}
	if watFundMapping.UpdatedAt != nil {
		if err = watFundMapping.UpdatedAt.CheckValid(); err == nil {
			coll.UpdatedAt = watFundMapping.UpdatedAt.AsTime()
		} else {
			return nil, fmt.Errorf("unable to parse updated_at proto timestamp to time: %v : %w", watFundMapping.UpdatedAt, err)
		}
	}
	return coll, nil
}

func (WatchList) TableName() string {
	return "mf_watch_lists"
}

func (WatchListFundMapping) TableName() string {
	return "mf_watch_list_funds_mappings"
}
