package impl

import (
	"context"
	"math"
	"reflect"
	"testing"

	"github.com/google/go-cmp/cmp"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	gormv2 "gorm.io/gorm"

	pb "github.com/epifi/gamma/api/investment/mutualfund"
	"github.com/epifi/gamma/investment/mutualfund/dao"
	"github.com/epifi/gamma/investment/mutualfund/dao/model"
	"github.com/epifi/gamma/investment/pagination"
	"github.com/epifi/be-common/pkg/money"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	test "github.com/epifi/be-common/pkg/test/v2"
)

type collectionMfTestSuite struct {
	db  *gormv2.DB
	dao dao.CollectionMutualFundDao
}

func newCollectionMfTestSuite(db *gormv2.DB, dao dao.CollectionMutualFundDao) *collectionMfTestSuite {
	return &collectionMfTestSuite{
		db:  db,
		dao: dao,
	}
}

var (
	collMfTS *collectionMfTestSuite

	// sampleCollMf and sampleCollMf2 are pre-populated in DB via fixtures
	sampleCollMf = &model.CollectionMutualFund{
		MutualFundId: "MF211116PruFuSSJTO6zZmIs+LFEjg==",
		CollectionId: "testCollection1",
		NameData: &pb.FundNameMetadata{
			ShortName:   "Equity&Debt Fund - Growth",
			LongName:    "ICICI Prudential Equity & Debt Fund - Growth",
			DisplayName: "Equity&Debt Fund - Growth",
		},
		Amc:                       pb.Amc_ICICI_PRUDENTIAL,
		OptionType:                pb.OptionType_GROWTH,
		AssetClass:                pb.AssetClass_EQUITY,
		CategoryName:              pb.MutualFundCategoryName_INDEX_FUNDS,
		FundhouseDefinedRiskLevel: pb.FundhouseDefinedRiskLevel_VERY_HIGH_RISK,
		Returns:                   &pb.Returns{},
		PerformanceMetrics:        &pb.PerformanceMetrics{},
		FundFundamentalDetails:    &pb.FundFundamentalDetails{},
		FiContent:                 &pb.FiContent{},
		PlanType:                  pb.PlanType_DIRECT,
		Nav: &money.Money{Pb: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        100,
			Nanos:        12,
		}},
		ComputedFiveYearAvgReturn:   0,
		ComputedCurrentAum:          0,
		ComputedCurrentExpenseRatio: 101.0,
		TxnConstraints: &pb.TransactionConstraints{
			NewpMnval: &moneyPb.Money{
				Units:        500,
				CurrencyCode: "INR",
			},
		},
		ComputedMinSipAmount: math.MaxInt32,
	}
	sampleCollMf2 = &model.CollectionMutualFund{
		MutualFundId: "MF223416PruFuSSJTO6zZmIs+LFEjp==",
		CollectionId: "testCollection1",
		Amc:          pb.Amc_HDFC,
		NameData: &pb.FundNameMetadata{
			ShortName:   "Equity&Debt Fund - Growth",
			LongName:    "HDFC Equity & Debt Fund - Growth",
			DisplayName: "Equity&Debt Fund - Growth",
		},
		OptionType:                pb.OptionType_GROWTH,
		AssetClass:                pb.AssetClass_EQUITY,
		CategoryName:              pb.MutualFundCategoryName_INDEX_FUNDS,
		FundhouseDefinedRiskLevel: pb.FundhouseDefinedRiskLevel_VERY_HIGH_RISK,
		Returns: &pb.Returns{
			AvgFundReturnOneYear:   1.0,
			AvgFundReturnThreeYear: 2.0,
		},
		PerformanceMetrics: &pb.PerformanceMetrics{},
		FundFundamentalDetails: &pb.FundFundamentalDetails{
			Aum: &pb.Aum{FundAum: 1000.0},
		},
		FiContent: &pb.FiContent{},
		PlanType:  pb.PlanType_DIRECT,
		Nav: &money.Money{Pb: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        100,
			Nanos:        12,
		}},
		ComputedFiveYearAvgReturn:   0,
		ComputedThreeYearAvgReturn:  2,
		ComputedOneYearAvgReturn:    1,
		ComputedCurrentAum:          1000.0,
		ComputedCurrentExpenseRatio: 101.0,
		TxnConstraints: &pb.TransactionConstraints{
			NewpMnval: &moneyPb.Money{
				Units:        500,
				CurrencyCode: "INR",
			},
		},
		ComputedMinSipAmount: math.MaxInt32,
	}
)

func TestCollectionMutualFundCRDB_GetPaginatedFundsInCollectionByFilters(t *testing.T) {
	type args struct {
		ctx      context.Context
		filters  []storagev2.FilterOption
		token    *pagination.PageToken
		pageSize uint32
	}
	tests := []struct {
		name    string
		args    args
		want    []*model.CollectionMutualFund
		wantErr bool
	}{
		{
			name: "get Status successful",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithMutualFundInternalStatus(pb.MutualFundInternalStatus_UNAVAILABLE),
					dao.WithCollectionId("testCollection1"),
				},
				token:    &pagination.PageToken{},
				pageSize: 1,
			},
			want:    []*model.CollectionMutualFund{sampleCollMf2},
			wantErr: false,
		},
		{
			name: "get empty user group successful",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithMutualFundInternalStatus(pb.MutualFundInternalStatus_UNAVAILABLE),
					dao.WithCollectionId("testCollection1"),
				},
				token:    &pagination.PageToken{},
				pageSize: 20,
			},
			want:    []*model.CollectionMutualFund{sampleCollMf2},
			wantErr: false,
		},
		// TODO(MIHIR): Fix this test. It should only return one fund
		{
			name: "get for user group present in db",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithMutualFundInternalStatus(pb.MutualFundInternalStatus_AVAILABLE),
					dao.WithCollectionId("testCollection1"),
					dao.WithMutualFundsUserGroup([]string{"INTERNAL"}),
				},
				token:    &pagination.PageToken{},
				pageSize: 20,
			},
			want:    []*model.CollectionMutualFund{sampleCollMf, sampleCollMf2},
			wantErr: false,
		},
		{
			name: "get all pages",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithCollectionId("testCollection1"),
				},
				token:    &pagination.PageToken{},
				pageSize: 20,
			},
			want:    []*model.CollectionMutualFund{sampleCollMf, sampleCollMf2},
			wantErr: false,
		},
		{
			name: "get page by page",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithCollectionId("testCollection1"),
				},
				token:    &pagination.PageToken{},
				pageSize: 1,
			},
			want:    []*model.CollectionMutualFund{sampleCollMf, sampleCollMf2},
			wantErr: false,
		},
		{
			name: "get  version filter android successful - get all",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithAndroidVersion(136),
					dao.WithCollectionId("testCollection1"),
				},
				token:    &pagination.PageToken{},
				pageSize: 20,
			},
			want:    []*model.CollectionMutualFund{sampleCollMf, sampleCollMf2},
			wantErr: false,
		},
		{
			name: "get  version filter ios successful - get all",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithIOSVersion(160),
					dao.WithCollectionId("testCollection1"),
				},
				token:    &pagination.PageToken{},
				pageSize: 20,
			},
			want:    []*model.CollectionMutualFund{sampleCollMf, sampleCollMf2},
			wantErr: false,
		},
		{
			name: "get  version filter android successful - get filtered",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithAndroidVersion(100),
					dao.WithCollectionId("testCollection1"),
				},
				token:    &pagination.PageToken{},
				pageSize: 20,
			},
			want:    []*model.CollectionMutualFund{sampleCollMf2},
			wantErr: false,
		},
		{
			name: "get  version filter ios successful - get filtered",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithIOSVersion(120),
					dao.WithCollectionId("testCollection1"),
				},
				token:    &pagination.PageToken{},
				pageSize: 20,
			},
			want:    []*model.CollectionMutualFund{sampleCollMf2},
			wantErr: false,
		}, {
			name: "get err record not found because of deleted at entry",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithIOSVersion(120),
					dao.WithCollectionId("testCollection99"),
				},
				token:    &pagination.PageToken{},
				pageSize: 20,
			},
			want:    nil,
			wantErr: true,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInMf)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			pageToken := tt.args.token
			for i := 0; i < len(tt.want); i += int(tt.args.pageSize) {
				gots, newPgCtx, err := collMfTS.dao.GetPaginatedFundsInCollectionByFilters(tt.args.ctx, pageToken, tt.args.pageSize, pb.MutualFundFieldMask_CURRENT_AUM, false, tt.args.filters...)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetPaginatedFundsInCollectionByFilters() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				err = pageToken.Unmarshal(newPgCtx.AfterToken)
				if err != nil {
					t.Errorf("GetPaginatedFundsInCollectionByFilters() error in page Token= %v", err)
				}
				if newPgCtx.HasAfter && uint32(len(gots)) != tt.args.pageSize {
					t.Errorf("GetPaginatedFundsInCollectionByFilters() error, unexpected length of output")
				}
				for j, got := range gots {
					if i+j >= len(tt.want) {
						t.Errorf("GetPaginatedFundsInCollectionByFilters() error = %v, unexpected length of output", err)
						return
					}
					opts := []cmp.Option{
						protocmp.Transform(),
					}

					if diff := cmp.Diff(got, tt.want[i+j], opts...); diff != "" {
						t.Errorf("GetPaginatedFundsInCollectionByFilters() got = %v,\n want %v", got, tt.want[i+j])
					}
				}
			}
		})
	}
}

func TestCollectionMutualFundCRDB_GetFundCountInCollectionByFilters(t *testing.T) {
	type args struct {
		ctx     context.Context
		filters []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "get Status successful",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithMutualFundInternalStatus(pb.MutualFundInternalStatus_UNAVAILABLE),
					dao.WithCollectionId("testCollection1"),
				},
			},
			want:    1,
			wantErr: false,
		},
		{
			name: "get empty user group successful",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithMutualFundInternalStatus(pb.MutualFundInternalStatus_UNAVAILABLE),
					dao.WithCollectionId("testCollection1"),
				},
			},
			want:    1,
			wantErr: false,
		},
		{
			name: "get for user group present in db",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithMutualFundInternalStatus(pb.MutualFundInternalStatus_AVAILABLE),
					dao.WithCollectionId("testCollection1"),
					dao.WithMutualFundsUserGroup([]string{"INTERNAL"}),
				},
			},
			want:    1,
			wantErr: false,
		},
		{
			name: "get all",
			args: args{
				ctx:     context.Background(),
				filters: nil,
			},
			want:    3,
			wantErr: false,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInMf)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := collMfTS.dao.GetFundCountInCollectionByFilters(tt.args.ctx, tt.args.filters...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFundCountInCollectionByFilters() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetFundCountInCollectionByFilters() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCollectionMutualFundCRDB_GetFundIdsInCollectionByFilters(t *testing.T) {
	type args struct {
		ctx     context.Context
		filters []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr bool
	}{
		{
			name: "get Status successful",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithMutualFundInternalStatus(pb.MutualFundInternalStatus_UNAVAILABLE),
					dao.WithCollectionId("testCollection1"),
				},
			},
			want:    []string{sampleCollMf2.MutualFundId},
			wantErr: false,
		},
		{
			name: "get for user group present in db",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithMutualFundInternalStatus(pb.MutualFundInternalStatus_AVAILABLE),
					dao.WithCollectionId("testCollection1"),
					dao.WithMutualFundsUserGroup([]string{"INTERNAL"}),
				},
			},
			want:    []string{sampleCollMf.MutualFundId},
			wantErr: false,
		},
		{
			name: "get all fund ids",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithCollectionId("testCollection1"),
				},
			},
			want:    []string{sampleCollMf.MutualFundId, sampleCollMf2.MutualFundId},
			wantErr: false,
		},

		{
			name: "get  version filter android successful - get all",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithAndroidVersion(136),
					dao.WithCollectionId("testCollection1"),
				},
			},
			want:    []string{sampleCollMf.MutualFundId, sampleCollMf2.MutualFundId},
			wantErr: false,
		},
		{
			name: "get  version filter ios successful - get all",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithIOSVersion(160),
					dao.WithCollectionId("testCollection1"),
				},
			},
			want:    []string{sampleCollMf.MutualFundId, sampleCollMf2.MutualFundId},
			wantErr: false,
		},
		{
			name: "get  version filter android successful - get filtered",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithAndroidVersion(100),
					dao.WithCollectionId("testCollection1"),
				},
			},
			want:    []string{sampleCollMf2.MutualFundId},
			wantErr: false,
		},
		{
			name: "get  version filter ios successful - get filtered",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithIOSVersion(120),
					dao.WithCollectionId("testCollection1"),
				},
			},
			want:    []string{sampleCollMf2.MutualFundId},
			wantErr: false,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInMf)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			collectionMutualFundDao := NewCollectionMutualFundCRDB(db)
			got, err := collectionMutualFundDao.GetFundIdsInCollectionByFilters(tt.args.ctx, tt.args.filters...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFundIdsInCollectionByFilters() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFundIdsInCollectionByFilters() got = %v, want %v", got, tt.want)
			}
		})
	}
}
