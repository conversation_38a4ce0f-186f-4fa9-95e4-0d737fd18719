package impl

import (
	"context"
	"sort"
	"strings"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/google/uuid"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/testing/protocmp"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	pb "github.com/epifi/gamma/api/order"
	genConf "github.com/epifi/gamma/investment/config/genconf"
	"github.com/epifi/gamma/investment/mutualfund/dao"
	"github.com/epifi/be-common/pkg/money"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	test "github.com/epifi/be-common/pkg/test/v2"
)

type RTAOrderDataTestSuite struct {
	db   *gormv2.DB
	conf *genConf.Config
	dao  dao.RTAOrderDataDao
}

func NewRTAOrderDataTestSuite(db *gormv2.DB, conf *genConf.Config, dao dao.RTAOrderDataDao) *RTAOrderDataTestSuite {
	return &RTAOrderDataTestSuite{
		db:   db,
		conf: conf,
		dao:  dao,
	}
}

var (
	rtaOrderDataTS               *RTAOrderDataTestSuite
	tablesAffectedInRTAOrderData = []string{"mf_rta_order_data"}
)

func TestRTAOrderDataCrdb_BatchUpsert(t *testing.T) {
	id1 := uuid.New().String()
	id2 := uuid.New().String()
	id3 := uuid.New().String()
	id4 := uuid.New().String()
	id5 := uuid.New().String()

	currentTime := timestamp.Now()

	type args struct {
		ctx           context.Context
		rtaOrderDatas []*orderPb.RTAOrderData
	}
	ctx := context.Background()
	tests := []struct {
		name    string
		args    args
		prepare func()
		wantErr bool
		want    []*orderPb.RTAOrderData
	}{
		{
			name:    "insert without conflicts",
			prepare: func() {},
			wantErr: false,
			args: args{
				ctx: ctx,
				rtaOrderDatas: []*orderPb.RTAOrderData{
					getRTAOrderDataForTest(id1, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V1", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, ""),
					getRTAOrderDataForTest(id2, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V2", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, ""),
				},
			},
			want: []*orderPb.RTAOrderData{
				getRTAOrderDataForTest(id2, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V2", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, ""),
				getRTAOrderDataForTest(id1, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V1", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, ""),
			},
		},
		{
			name: "update on conflicts",
			prepare: func() {
				err := rtaOrderDataTS.dao.BatchUpsert(ctx, []*orderPb.RTAOrderData{
					getRTAOrderDataForTest(id1, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V1", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, ""),
					getRTAOrderDataForTest(id2, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V2", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, ""),
				})
				if err != nil {
					panic(err)
				}
			},
			wantErr: false,
			args: args{
				ctx: ctx,
				rtaOrderDatas: []*orderPb.RTAOrderData{
					getRTAOrderDataForTest(id4, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V1", "F2", money.AmountINR(100), money.AmountINR(100), 100.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_FAILURE, &orderPb.RTAOrderMetaData{}, currentTime, ""),
					getRTAOrderDataForTest(id5, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V2", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, ""),
					getRTAOrderDataForTest(id3, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V3", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, ""),
				},
			},
			want: []*orderPb.RTAOrderData{
				getRTAOrderDataForTest(id3, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V3", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, ""),
				getRTAOrderDataForTest(id2, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V2", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, ""),
				getRTAOrderDataForTest(id1, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V1", "F2", money.AmountINR(100), money.AmountINR(100), 100.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_FAILURE, &orderPb.RTAOrderMetaData{}, currentTime, ""),
			},
		},
		{
			name: "ignore upset on mapped_mf_order_id column",
			prepare: func() {
				err := rtaOrderDataTS.dao.BatchUpsert(ctx, []*orderPb.RTAOrderData{
					getRTAOrderDataForTest(id1, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V1", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, "123"),
				})
				if err != nil {
					panic(err)
				}
			},
			wantErr: false,
			args: args{
				ctx: ctx,
				rtaOrderDatas: []*orderPb.RTAOrderData{
					getRTAOrderDataForTest(id1, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V1", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, "456"),
				},
			},
			want: []*orderPb.RTAOrderData{
				getRTAOrderDataForTest(id1, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V1", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, "123"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfnhTS.db, tablesAffectedInRTAOrderData)
			tt.prepare()
			err := rtaOrderDataTS.dao.BatchUpsert(ctx, tt.args.rtaOrderDatas)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchUpsert() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.wantErr {
				return
			}

			var ids []string
			for _, val := range tt.want {
				ids = append(ids, val.Id)
			}

			got, _, err := rtaOrderDataTS.dao.GetPaginatedOrdersByFilters(ctx, nil, 100, storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB { return db.Where("id in (?)", ids) }))
			if err != nil {
				t.Errorf("BatchUpsert() error in GetPaginatedOrdersByFilters %v", err)
			}

			sort.SliceStable(got, func(i, j int) bool {
				if strings.Compare(got[i].Id, got[j].Id) > 0 {
					return true
				} else {
					return false
				}
			})
			sort.SliceStable(tt.want, func(i, j int) bool {
				if strings.Compare(tt.want[i].Id, tt.want[j].Id) > 0 {
					return true
				} else {
					return false
				}
			})

			for i, gotOrder := range got {
				if gotOrder != nil {
					tt.want[i].CreatedAt = gotOrder.GetCreatedAt()
					tt.want[i].UpdatedAt = gotOrder.GetUpdatedAt()
					tt.want[i].OrderDate.Nanos = gotOrder.GetOrderDate().GetNanos()
				}
				opts := []cmp.Option{
					protocmp.Transform(),
					cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
				}
				if diff := cmp.Diff(gotOrder, tt.want[i], opts...); diff != "" {
					t.Errorf("TestRTAOrderDataCrdb_BatchUpsert() got = %v,\nwant %v,\ndiff %v", gotOrder, tt.want[i], diff)
				}
			}
		})
	}
}

func TestRTAOrderDataCrdb_Update(t *testing.T) {

	id := uuid.New().String()
	currentTime := timestamp.Now()
	type args struct {
		ctx           context.Context
		updateMasks   []orderPb.RTAOrderDataFieldMask
		rtaOrderData  *orderPb.RTAOrderData
		prepareUpdate func() (*orderPb.RTAOrderData, error)
	}
	tests := []struct {
		name    string
		args    args
		want    *orderPb.RTAOrderData
		wantErr bool
	}{
		{
			name: "empty id",
			args: args{
				ctx:         context.Background(),
				updateMasks: []orderPb.RTAOrderDataFieldMask{orderPb.RTAOrderDataFieldMask_RTA_ORDER_DATA_FIELD_MASK_MAPPED_ORDER_ID},
				prepareUpdate: func() (*orderPb.RTAOrderData, error) {
					rtaOrderData := getRTAOrderDataForTest(id, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V3", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, "")
					_ = rtaOrderDataTS.dao.BatchUpsert(context.Background(), []*orderPb.RTAOrderData{rtaOrderData})
					return rtaOrderData, nil
				},
				rtaOrderData: &orderPb.RTAOrderData{Id: "", MappedMfOrderId: "123"},
			},
			wantErr: true,
			want:    nil,
		},
		{
			name: "invalid order id",
			args: args{
				ctx:         context.Background(),
				updateMasks: []orderPb.RTAOrderDataFieldMask{orderPb.RTAOrderDataFieldMask_RTA_ORDER_DATA_FIELD_MASK_MAPPED_ORDER_ID},
				prepareUpdate: func() (*orderPb.RTAOrderData, error) {
					rtaOrderData := getRTAOrderDataForTest(id, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V3", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, "")
					_ = rtaOrderDataTS.dao.BatchUpsert(context.Background(), []*orderPb.RTAOrderData{rtaOrderData})
					return rtaOrderData, nil
				},
				rtaOrderData: &orderPb.RTAOrderData{Id: uuid.New().String(), MappedMfOrderId: "123"},
			},
			wantErr: true,
			want:    nil,
		},
		{
			name: "update mapped order id",
			args: args{
				ctx:         context.Background(),
				updateMasks: []orderPb.RTAOrderDataFieldMask{orderPb.RTAOrderDataFieldMask_RTA_ORDER_DATA_FIELD_MASK_MAPPED_ORDER_ID},
				prepareUpdate: func() (*orderPb.RTAOrderData, error) {
					rtaOrderData := getRTAOrderDataForTest(id, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V3", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, "")
					_ = rtaOrderDataTS.dao.BatchUpsert(context.Background(), []*orderPb.RTAOrderData{rtaOrderData})
					return rtaOrderData, nil
				},
				rtaOrderData: &orderPb.RTAOrderData{Id: id, MappedMfOrderId: "123", OrderDate: currentTime},
			},
			wantErr: false,
			want:    getRTAOrderDataForTest(id, "A1", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V3", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_BUY, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, "123"),
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			test.PrepareScopedDatabase(t, orderTS.conf.EpifiDb().GetName(), orderTS.db, tablesAffectedInRTAOrderData)
			_, err := tt.args.prepareUpdate()
			if err != nil {
				t.Error("unexpected error in create DB entry", err)
				return
			}
			err = rtaOrderDataTS.dao.Update(tt.args.ctx, tt.args.rtaOrderData, tt.args.updateMasks)
			if (err != nil) != tt.wantErr {
				t.Errorf("TestRTAOrderDataCrdb_Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr {
				return
			}
			got, _, err := rtaOrderDataTS.dao.GetPaginatedOrdersByFilters(tt.args.ctx, nil, 100, storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB { return db.Where("id in (?)", tt.args.rtaOrderData.Id) }))
			if err != nil {
				t.Errorf("TestRTAOrderDataCrdb_Update() error in GetPaginatedOrdersByFilters %v", err)
			}

			gotOrder := got[0]
			if gotOrder != nil {
				tt.want.CreatedAt = gotOrder.GetCreatedAt()
				tt.want.UpdatedAt = gotOrder.GetUpdatedAt()
				tt.want.OrderDate = gotOrder.GetOrderDate()
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
			}
			if diff := cmp.Diff(gotOrder, tt.want, opts...); diff != "" {
				t.Errorf("TestRTAOrderDataCrdb_BatchUpsert() got = %v,\nwant %v,\ndiff %v", gotOrder, tt.want, diff)
			}

		})
	}
}

func TestRTAOrderDataCrdb_GetDistinctMFIdsWithFilters(t *testing.T) {
	id := uuid.New().String()
	id2 := uuid.New().String()
	currentTime := timestamp.New(time.Unix(1673381612, 0))
	type args struct {
		ctx           context.Context
		actorId       string
		orderType     orderPb.OrderType
		startDate     *date.Date
		endDate       *date.Date
		orderStatus   orderPb.RTAOrderStatus
		updateMasks   []orderPb.RTAOrderDataFieldMask
		rtaOrderData  *orderPb.RTAOrderData
		prepareUpdate func() ([]*orderPb.RTAOrderData, error)
		rtaOrderDatas []*orderPb.RTAOrderData
	}
	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr bool
	}{
		{
			name: "successfully fetched the distinct MFIDs from the table with filters",
			args: args{
				ctx:       context.Background(),
				actorId:   "testActor",
				orderType: orderPb.OrderType_SELL,
				startDate: &date.Date{
					Year:  2022,
					Month: 8,
					Day:   12,
				},
				endDate: &date.Date{
					Year:  2023,
					Month: 04,
					Day:   12,
				},
				orderStatus: orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS,
				rtaOrderDatas: []*orderPb.RTAOrderData{
					getRTAOrderDataForTest(id, "testActor", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V2", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_SELL, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, ""),
				},
			},
			wantErr: false,
			want:    []string{"MF211116PruFuSSJTO6zZmIs+LFEjg=="},
		},
		{
			name: "multiple entries with the same MF Id",
			args: args{
				ctx:       context.Background(),
				actorId:   "testActor",
				orderType: orderPb.OrderType_SELL,
				startDate: &date.Date{
					Year:  2022,
					Month: 8,
					Day:   12,
				},
				endDate: &date.Date{
					Year:  2023,
					Month: 04,
					Day:   12,
				},
				orderStatus: orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS,
				rtaOrderDatas: []*orderPb.RTAOrderData{
					getRTAOrderDataForTest(id, "testActor", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V3", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_SELL, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, ""),
					getRTAOrderDataForTest(id2, "testActor", "MF211116PruFuSSJTO6zZmIs+LFEjg==", mfPb.Amc_ICICI_PRUDENTIAL, "123", "V2", "F1", money.AmountINR(100), money.AmountINR(100), 10.0, orderPb.OrderType_SELL, orderPb.RTAOrderStatus_RTA_ORDER_STATUS_SUCCESS, &orderPb.RTAOrderMetaData{}, currentTime, ""),
				},
			},
			wantErr: false,
			want:    []string{"MF211116PruFuSSJTO6zZmIs+LFEjg=="},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			test.PrepareScopedDatabase(t, orderTS.conf.EpifiDb().GetName(), orderTS.db, tablesAffectedInRTAOrderData)
			err := rtaOrderDataTS.dao.BatchUpsert(tt.args.ctx, tt.args.rtaOrderDatas)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchUpsert() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.wantErr {
				return
			}

			got, err := rtaOrderDataTS.dao.GetDistinctMFIdsWithFilters(tt.args.ctx, dao.WithActorId(tt.args.actorId), dao.WithMFRtOrderStatus(tt.args.orderStatus), dao.WithOrderType(tt.args.orderType), dao.WithMfRtaOrderBetweenDate(tt.args.startDate, tt.args.endDate))
			if err != nil {
				t.Errorf("TestRTAOrderDataCrdb_GetDistinctMFIdsWithFilters() error %v", err)
			}

			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("TestRTAOrderDataCrdb_BatchUpsert() got = %v,\nwant %v,\ndiff %v", got, tt.want, diff)
			}

		})
	}
}

func getRTAOrderDataForTest(id string, ActorId string, MutualFundId string, Amc mfPb.Amc, schemeCode string, VendorOrderId string, FolioId string, Amount *money.Money, Nav *money.Money, Units float64, OrderType orderPb.OrderType, Status orderPb.RTAOrderStatus, MetaData *orderPb.RTAOrderMetaData, OrderDate *timestamp.Timestamp, MappedMfOrderId string) *orderPb.RTAOrderData {
	return &orderPb.RTAOrderData{
		Id:              id,
		ActorId:         ActorId,
		MutualFundId:    MutualFundId,
		Amc:             Amc,
		SchemeCode:      schemeCode,
		VendorOrderId:   VendorOrderId,
		FolioId:         FolioId,
		Amount:          Amount.Pb,
		Nav:             Nav.Pb,
		Units:           Units,
		OrderType:       OrderType,
		Status:          Status,
		MetaData:        MetaData,
		OrderDate:       OrderDate,
		MappedMfOrderId: MappedMfOrderId,
	}
}
