package model

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/epifi/gamma/api/investment/dynamic_ui_element"
)

type DynamicUIElement struct {
	Id string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	// primary identifier of dynamic_ui_element table
	VariantName string
	// ContentJson include colors, deeplink, shadows, texts for a variant_name
	ContentJson   *pb.ContentJson
	CreatedAt     time.Time
	UpdatedAt     time.Time
	DeletedAtUnix int64
}

func NewDynamicUIElement(proto *pb.DynamicUIElement) *DynamicUIElement {
	model := &DynamicUIElement{
		Id:            proto.GetId(),
		VariantName:   proto.GetVariantName(),
		ContentJson:   proto.GetContentJson(),
		DeletedAtUnix: proto.GetDeletedAtUnix(),
	}
	if proto.GetCreatedAt().IsValid() {
		model.CreatedAt = proto.GetCreatedAt().AsTime()
	}
	if proto.GetUpdatedAt().IsValid() {
		model.UpdatedAt = proto.GetUpdatedAt().AsTime()
	}
	return model
}

func (s *DynamicUIElement) ToProto() *pb.DynamicUIElement {
	return &pb.DynamicUIElement{
		Id:            s.Id,
		VariantName:   s.VariantName,
		ContentJson:   s.ContentJson,
		CreatedAt:     timestamppb.New(s.CreatedAt),
		UpdatedAt:     timestamppb.New(s.UpdatedAt),
		DeletedAtUnix: s.DeletedAtUnix,
	}
}

func (*DynamicUIElement) TableName() string {
	return "dynamic_ui_element"
}
