package pagination

import (
	"encoding/base64"
	"encoding/json"
	"fmt"

	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
)

// SearchPageToken can be used for implementing pagination for
// databases which provide an option to fetch from the xth row.
// Ex: ElasticSearch, Zinc. Both of them support 'from' field which fetches
// documents from that row
type SearchPageToken struct {
	LastRowToken uint32
}

func (p *SearchPageToken) Marshal() (string, error) {
	if p == nil {
		return "", nil
	}
	s, err := json.Marshal(p)
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("failed to marshal page token: %w", err)
	}
	return base64.StdEncoding.EncodeToString(s), nil
}

func (p *SearchPageToken) Unmarshal(s string) error {
	if s == "" {
		return nil
	}
	ds, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		return fmt.Errorf("failed to unmarshal page token: %w", err)
	}
	if p == nil {
		*p = SearchPageToken{}
	}
	return json.Unmarshal(ds, p)
}

// GetSearchPageToken returns SearchPageToken by unmarshalling token string from req.
// if there is no page token string, we return nil
func GetSearchPageToken(req *rpc.PageContextRequest) (*SearchPageToken, error) {
	var tokenStr string
	if req.GetAfterToken() != "" {
		tokenStr = req.GetAfterToken()
	} else {
		tokenStr = req.GetBeforeToken()
	}
	if tokenStr == "" {
		return nil, nil
	}
	token := &SearchPageToken{}
	err := token.Unmarshal(tokenStr)
	return token, err
}

// NewSearchPageCtxResp constructs response with next page details.
// Rows passed to this function should be fetched with size = pageSize + 1
// Extra row is fetched for next page details and will be removed in the end result returned
// from the function.
func NewSearchPageCtxResp(pageToken *SearchPageToken, rows []proto.Message, pageSize uint32) ([]proto.Message, *rpc.PageContextResponse, error) {
	// Default is no new page
	pageCtxResp := &rpc.PageContextResponse{
		BeforeToken: "",
		HasBefore:   false,
		AfterToken:  "",
		HasAfter:    false,
	}
	// If current page size is > pageSize then more results can be fetched.
	if uint32(len(rows)) > pageSize {
		var err error
		pt := &SearchPageToken{
			LastRowToken: pageToken.LastRowToken + pageSize,
		}
		pageCtxResp.AfterToken, err = pt.Marshal()
		if err != nil {
			return nil, nil, err
		}
		pageCtxResp.HasAfter = true
		return rows[:len(rows)-1], pageCtxResp, nil
	}

	return rows, pageCtxResp, nil
}
