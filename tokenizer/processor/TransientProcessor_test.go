package processor

import (
	"context"
	"testing"

	aws "github.com/epifi/be-common/pkg/aws/v2/config"

	"github.com/aws/aws-sdk-go-v2/service/kms"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"

	tokenizerPb "github.com/epifi/gamma/api/tokenizer"
	"github.com/epifi/gamma/tokenizer/config"
	"github.com/epifi/gamma/tokenizer/crypto"
	"github.com/epifi/gamma/tokenizer/model"
	mock_crypto "github.com/epifi/gamma/tokenizer/test/mocks/crypto"
	mock_dao "github.com/epifi/gamma/tokenizer/test/mocks/dao"
)

func TestTransientProcessor_Detokenize(t *testing.T) {
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()
	t.Parallel()
	mockStore := mock_dao.NewMockTransientStore(ctr)
	mockCrypto := mock_crypto.NewMockCrypto(ctr)
	info := &tokenizerPb.DetokenizationInfo{
		Token: "test_token_1",
	}
	scope := tokenizerPb.Scope_FI_FEDERAL
	tests := []struct {
		name    string
		mocks   interface{}
		want    string
		want1   tokenizerPb.DetokenizationError
		wantErr bool
	}{
		{
			name: "data fetch failed",
			mocks: []interface{}{
				mockStore.EXPECT().Fetch(gomock.Any(), gomock.Any()).Return(nil, errors.New("data fetch failed")),
			},
			want:    "",
			want1:   tokenizerPb.DetokenizationError_FAILED_DETOKENIZATION,
			wantErr: true,
		},
		{
			name: "data not found",
			mocks: []interface{}{
				mockStore.EXPECT().Fetch(gomock.Any(), gomock.Any()).Return(nil, nil),
			},
			want:    "",
			want1:   tokenizerPb.DetokenizationError_KEY_NOT_FOUND,
			wantErr: true,
		},
		{
			name: "data decryption failed",
			mocks: []interface{}{
				mockStore.EXPECT().Fetch(gomock.Any(), gomock.Any()).Return(&model.StoreData{
					Token:      "token",
					CipherText: []byte("cypher_text"),
					KeyId:      "key_id",
				}, nil),
				mockCrypto.EXPECT().Decrypt(gomock.Any(), gomock.Any(), gomock.Any()).Return("", errors.New("data decryption failed")),
			},
			want:    "",
			want1:   tokenizerPb.DetokenizationError_FAILED_DETOKENIZATION,
			wantErr: true,
		},
		{
			name: "getting key id failed",
			mocks: []interface{}{
				mockStore.EXPECT().Fetch(gomock.Any(), gomock.Any()).Return(&model.StoreData{
					Token:      "token",
					CipherText: []byte("cypher_text"),
					KeyId:      "key_id",
				}, nil),
				mockCrypto.EXPECT().Decrypt(gomock.Any(), gomock.Any(), gomock.Any()).Return("sensitive_data", nil),
				mockCrypto.EXPECT().GenerateHash(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("hash_token"),
				mockCrypto.EXPECT().GetKeyId(gomock.Any(), gomock.Any()).Return("", errors.New("getting key id failed")),
			},
			want:    "",
			want1:   tokenizerPb.DetokenizationError_FAILED_DETOKENIZATION,
			wantErr: true,
		},
		{
			name: "no re-encryption: success",
			mocks: []interface{}{
				mockStore.EXPECT().Fetch(gomock.Any(), gomock.Any()).Return(&model.StoreData{
					Token:      "token",
					CipherText: []byte("cypher_text"),
					KeyId:      "key_id",
				}, nil),
				mockCrypto.EXPECT().Decrypt(gomock.Any(), gomock.Any(), gomock.Any()).Return("sensitive_data", nil),
				mockCrypto.EXPECT().GenerateHash(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("hash_token"),
				mockCrypto.EXPECT().GetKeyId(gomock.Any(), gomock.Any()).Return("key_id", nil),
			},
			want:    "sensitive_data",
			want1:   tokenizerPb.DetokenizationError_UNSPECIFIED_DETOKENIZATION_ERROR,
			wantErr: false,
		},
		{
			name: "re-encryption: data encryption failed",
			mocks: []interface{}{
				mockStore.EXPECT().Fetch(gomock.Any(), gomock.Any()).Return(&model.StoreData{
					Token:      "token",
					CipherText: []byte("cypher_text"),
					KeyId:      "key_id",
				}, nil),
				mockCrypto.EXPECT().Decrypt(gomock.Any(), gomock.Any(), gomock.Any()).Return("sensitive_data", nil),
				mockCrypto.EXPECT().GenerateHash(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("hash_token"),
				mockCrypto.EXPECT().GetKeyId(gomock.Any(), gomock.Any()).Return("key_id_2", nil),
				mockCrypto.EXPECT().Encrypt(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("data encryption failed")),
			},
			want:    "",
			want1:   tokenizerPb.DetokenizationError_FAILED_DETOKENIZATION,
			wantErr: true,
		},
		{
			name: "re-encryption: data persistence failed",
			mocks: []interface{}{
				mockStore.EXPECT().Fetch(gomock.Any(), gomock.Any()).Return(&model.StoreData{
					Token:      "token",
					CipherText: []byte("cypher_text"),
					KeyId:      "key_id",
				}, nil),
				mockCrypto.EXPECT().Decrypt(gomock.Any(), gomock.Any(), gomock.Any()).Return("sensitive_data", nil),
				mockCrypto.EXPECT().GenerateHash(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("hash_token"),
				mockCrypto.EXPECT().GetKeyId(gomock.Any(), gomock.Any()).Return("key_id_2", nil),
				mockCrypto.EXPECT().Encrypt(gomock.Any(), gomock.Any(), gomock.Any()).Return([]byte("cypher_text_2"), nil),
				mockStore.EXPECT().Persist(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("data persistence failed")),
			},
			want:    "",
			want1:   tokenizerPb.DetokenizationError_FAILED_DETOKENIZATION,
			wantErr: true,
		},
		{
			name: "re-encryption: success",
			mocks: []interface{}{
				mockStore.EXPECT().Fetch(gomock.Any(), gomock.Any()).Return(&model.StoreData{
					Token:      "token",
					CipherText: []byte("cypher_text"),
					KeyId:      "key_id",
				}, nil),
				mockCrypto.EXPECT().Decrypt(gomock.Any(), gomock.Any(), gomock.Any()).Return("sensitive_data", nil),
				mockCrypto.EXPECT().GenerateHash(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("hash_token"),
				mockCrypto.EXPECT().GetKeyId(gomock.Any(), gomock.Any()).Return("key_id_2", nil),
				mockCrypto.EXPECT().Encrypt(gomock.Any(), gomock.Any(), gomock.Any()).Return([]byte("cypher_text_2"), nil),
				mockStore.EXPECT().Persist(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
			},
			want:    "sensitive_data",
			want1:   tokenizerPb.DetokenizationError_UNSPECIFIED_DETOKENIZATION_ERROR,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewTransientProcessor(mockStore, mockCrypto, conf)
			got, got1, err := p.Detokenize(context.Background(), info, scope)
			if (err != nil) != tt.wantErr {
				t.Errorf("Detokenize() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("Detokenize() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("Detokenize() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestTransientProcessor_Tokenize(t *testing.T) {
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()
	t.Parallel()
	mockStore := mock_dao.NewMockTransientStore(ctr)
	mockCrypto := mock_crypto.NewMockCrypto(ctr)
	info := &tokenizerPb.TokenizationInfo{
		Id:            "id_1",
		SensitiveData: "sensitive_data_1",
		Scope:         tokenizerPb.Scope_FI_FEDERAL,
		StorageParams: &tokenizerPb.TokenizationInfo_TransientStorageParams_{
			TransientStorageParams: &tokenizerPb.TokenizationInfo_TransientStorageParams{
				StorageType: tokenizerPb.StorageType_TRANSIENT,
				TTL:         1000,
			},
		},
	}
	tests := []struct {
		name           string
		mocks          interface{}
		want           string
		skipTokenCheck bool
		wantErr        bool
	}{
		{
			name: "failed to get key id",
			mocks: []interface{}{
				mockCrypto.EXPECT().GenerateHash(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("hash_token_1"),
				mockCrypto.EXPECT().GetKeyId(gomock.Any(), gomock.Any()).Return("key_id_1", errors.New("failed to get key id")),
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "data encryption failed",
			mocks: []interface{}{
				mockCrypto.EXPECT().GenerateHash(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("hash_token_1"),
				mockCrypto.EXPECT().GetKeyId(gomock.Any(), gomock.Any()).Return("key_id_1", nil),
				mockCrypto.EXPECT().Encrypt(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("encryption failed")),
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "data persistence failed",
			mocks: []interface{}{
				mockCrypto.EXPECT().GenerateHash(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("hash_token_1"),
				mockCrypto.EXPECT().GetKeyId(gomock.Any(), gomock.Any()).Return("key_id_1", nil),
				mockCrypto.EXPECT().Encrypt(gomock.Any(), gomock.Any(), gomock.Any()).Return([]byte("cypher_text_1"), nil),
				mockStore.EXPECT().Persist(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("data persistence failed")),
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "success",
			mocks: []interface{}{
				mockCrypto.EXPECT().GenerateHash(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("hash_token_1"),
				mockCrypto.EXPECT().GetKeyId(gomock.Any(), gomock.Any()).Return("key_id_1", nil),
				mockCrypto.EXPECT().Encrypt(gomock.Any(), gomock.Any(), gomock.Any()).Return([]byte("cypher_text_1"), nil),
				mockStore.EXPECT().Persist(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
			},
			want:           "",
			skipTokenCheck: true,
			wantErr:        false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewTransientProcessor(mockStore, mockCrypto, conf)
			got, err := p.Tokenize(context.Background(), info)
			if (err != nil) != tt.wantErr {
				t.Errorf("Tokenize() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.skipTokenCheck && got != tt.want {
				t.Errorf("Tokenize() got = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestTransientProcessor_TokenizeWithLocalstack can be used to validate the implementation with localstack integration.
// Prerequisite: Start localstack and load all the resources.
func TestTransientProcessor_TokenizeWithLocalstack(t *testing.T) {
	t.Skip("Skipping test case used for development and experiments")
	ctr := gomock.NewController(t)

	t.Parallel()
	mockStore := mock_dao.NewMockTransientStore(ctr)
	conf, err := config.Load()
	if err != nil {
		t.Errorf("error loading config : %v", err)
		return
	}
	ctx := context.Background()

	awsConf, awsErr := aws.NewAWSConfig(ctx, conf.Aws.Region, conf.Tracing.Enable)
	if awsErr != nil {
		t.Errorf("error loading config : %v", err)
	}
	federalKmsHandle := kms.NewFromConfig(awsConf)
	federalKmsSymmetricCrypto, _ := crypto.NewKmsSymmetricCryptoV2(federalKmsHandle, conf.FederalKMSKeysIds, conf.FederalHMACSHA256KeyId)

	info := &tokenizerPb.TokenizationInfo{
		Id:            "id_1",
		SensitiveData: "sensitive_data_1",
		Scope:         tokenizerPb.Scope_FI_FEDERAL,
		StorageParams: &tokenizerPb.TokenizationInfo_TransientStorageParams_{
			TransientStorageParams: &tokenizerPb.TokenizationInfo_TransientStorageParams{
				StorageType: tokenizerPb.StorageType_TRANSIENT,
				TTL:         1000,
			},
		},
	}
	tests := []struct {
		name           string
		mocks          interface{}
		want           string
		skipTokenCheck bool
		wantErr        bool
	}{

		{
			name: "success",
			mocks: []interface{}{
				mockStore.EXPECT().Persist(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
			},
			want:           "",
			skipTokenCheck: true,
			wantErr:        false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewTransientProcessor(mockStore, federalKmsSymmetricCrypto, conf)
			got, err := p.Tokenize(context.Background(), info)
			if (err != nil) != tt.wantErr {
				t.Errorf("Tokenize() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.skipTokenCheck && got != tt.want {
				t.Errorf("Tokenize() got = %v, want %v", got, tt.want)
			}
		})
	}
}
