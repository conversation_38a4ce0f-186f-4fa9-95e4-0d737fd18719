// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"github.com/epifi/be-common/pkg/crypto/pgp"
	"github.com/epifi/be-common/pkg/secrets/aws"
	"github.com/jonboulle/clockwork"
)

// Injectors from wire.go:

func InitializeInMemoryStore(client *secretsmanager.Client) *pgp.InMemoryEntityStore {
	clock := clockwork.NewRealClock()
	awsSecretsManager := aws.NewAwsSecretManager(client)
	inMemoryEntityStore := pgp.NewMemoryEntityStore(clock, awsSecretsManager)
	return inMemoryEntityStore
}

func InitializePGPCryptor(es pgp.EntityStore) *pgp.OpenPgpCryptorWithMultiKey {
	openPgpCryptorWithMultiKey := pgp.NewOpenPgpCryptorWithMultiKey(es)
	return openPgpCryptorWithMultiKey
}
