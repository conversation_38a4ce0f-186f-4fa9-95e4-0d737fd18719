package apputils

import (
	"context"

	types "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/frontend/app"
	"github.com/epifi/be-common/pkg/frontend/app/genconf"
	"github.com/epifi/be-common/pkg/logger"
)

// IsFeatureEnabledDynamic is a util function to check if the feature is enabled and user's app version satisfies a feature's
// min app version for user's platform which are derived from dynamic featureConfig objects. If the feature is not supported on a platform, set version as NO_VERSION.
// If the platform or version are zero values we check the FallbackToEnableFeature flag
func IsFeatureEnabledDynamic(ctx context.Context, featureCfg *genconf.FeatureConfig, userCfg *app.UserConfig) bool {
	// All apps are expected to have version > 0.
	// If it's 0, we'll consider it as latest version.
	checkVer := func(userVer, minVer uint32) bool {
		if userVer == 0 {
			return featureCfg.FallbackToEnableFeature()
		}
		return userVer >= minVer
	}

	if featureCfg == nil {
		logger.Error(ctx, "nil feature config")
		return false
	}

	// early return if the feature is disabled
	if featureCfg.DisableFeature() {
		return false
	}

	if userCfg == nil {
		logger.Error(ctx, "nil user config")
		return featureCfg.FallbackToEnableFeature()
	}

	for _, unsupportedPlatform := range featureCfg.UnsupportedPlatforms() {
		if unsupportedPlatform == userCfg.Platform {
			return false
		}
	}

	switch userCfg.Platform {
	case types.Platform_ANDROID:
		return checkVer(userCfg.AppVersion, featureCfg.MinAndroidVersion())

	case types.Platform_IOS:
		return checkVer(userCfg.AppVersion, featureCfg.MinIOSVersion())

	default:
		return featureCfg.FallbackToEnableFeature()
	}
}

func IsFeatureEnabledFromCtxDynamic(ctx context.Context, featureCfg *genconf.FeatureConfig) bool {
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	return IsFeatureEnabledDynamic(ctx, featureCfg, &app.UserConfig{
		Platform:   platform,
		AppVersion: uint32(version),
	})
}
