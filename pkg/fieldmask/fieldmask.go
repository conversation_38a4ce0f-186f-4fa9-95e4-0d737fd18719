package fieldmask

import (
	"github.com/samber/lo"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
)

func RemovePath(fm *fieldmaskpb.FieldMask, message proto.Message, path string) (newFm *fieldmaskpb.FieldMask, removed bool, err error) {
	paths := fm.GetPaths()
	if ind := lo.IndexOf(paths, path); ind >= 0 {
		newPaths := append([]string{}, paths[:ind]...)
		newPaths = append(newPaths, paths[ind+1:]...)
		newFm, err = fieldmaskpb.New(message, newPaths...)
		if err != nil {
			return nil, false, err
		}
		return newFm, true, nil
	}
	return fm, false, nil
}
