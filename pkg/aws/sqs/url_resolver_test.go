package sqs

import (
	"errors"
	"fmt"
	"testing"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/pkg/aws/sqs/mocks"
)

func Test_queueURLResolver(t *testing.T) {
	t.<PERSON>l()
	type args struct {
		queueName           string
		queueOwnerAccountId string
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(mockSqsApi *mocks.MockSQSAPI)
		want           string
		wantErr        bool
	}{
		{
			name: "unable to resolve URL",
			args: args{
				queueName:           "new-queue-name-1",
				queueOwnerAccountId: "test owner",
			},
			setupMockCalls: func(mockSqsApi *mocks.MockSQSAPI) {
				mockSqsApi.EXPECT().GetQueueUrl(gomock.Any()).Return(nil, errors.New("test error")).Times(1)
			},
			wantErr: true,
		},
		{
			name: "resolve and set in queueURLMap",
			args: args{
				queueName:           "new-queue-name-2",
				queueOwnerAccountId: "test owner",
			},
			setupMockCalls: func(mockSqsApi *mocks.MockSQSAPI) {
				mockSqsApi.EXPECT().GetQueueUrl(gomock.Any()).Return(&sqs.GetQueueUrlOutput{QueueUrl: aws.String("test url")}, nil).Times(1)
			},
			want:    "test url",
			wantErr: false,
		},
		{
			name: "URL already in queueURLMap",
			args: args{
				queueName:           "queue-name-in-queueURLMap",
				queueOwnerAccountId: "test owner",
			},
			setupMockCalls: func(mockSqsApi *mocks.MockSQSAPI) {
				mockSqsApi.EXPECT().GetQueueUrl(gomock.Any()).Return(&sqs.GetQueueUrlOutput{QueueUrl: aws.String("URL already in queueURLMap")}, nil).Times(1)
				queueURLResolver(mockSqsApi, "queue-name-in-queueURLMap", "test owner")
			},
			want:    "URL already in queueURLMap",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockSqsApi := mocks.NewMockSQSAPI(ctr)
			if tt.setupMockCalls != nil {
				tt.setupMockCalls(mockSqsApi)
			}
			got, err := queueURLResolver(mockSqsApi, tt.args.queueName, tt.args.queueOwnerAccountId)
			if (err != nil) != tt.wantErr {
				t.Errorf("queueURLResolver() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("queueURLResolver() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetConfigKeyForSubscriberRateLimiting(t *testing.T) {
	t.Parallel()
	type args struct {
		queueName  string
		methodName string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "happy flow",
			args: args{
				queueName:  queueName,
				methodName: methodName,
			},
			want: fmt.Sprintf("%s:%s", queueName, methodName),
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			if got := GetConfigKeyForSubscriberRateLimiting(tt.args.queueName, tt.args.methodName); got != tt.want {
				t.Errorf("GetConfigKeyForSubscriberRateLimiting() = %v, want %v", got, tt.want)
			}
		})
	}
}
