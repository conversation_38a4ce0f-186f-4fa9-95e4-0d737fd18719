//nolint:depguard
package ecr

import (
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/credentials/stscreds"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ecr"
)

type Client struct {
	*ecr.ECR
}

// instantiate an ecr client from an aws session
func InitECRClient(sess *session.Session) (*Client, error) {
	// Create a ECR service client.
	c := ecr.New(sess)
	return &Client{
		ECR: c,
	}, nil
}

// instantiate an ecr client from an aws session using an assumed role
func InitECRClientAssumingRole(sess *session.Session, assumeRoleArn string) (*Client, error) {
	// Create a ECR service client configured with credentials from assumed role.
	c := ecr.New(sess, &aws.Config{Credentials: stscreds.NewCredentials(sess, assumeRoleArn)})
	return &Client{
		ECR: c,
	}, nil
}

// instantiate an ecr client from an aws session using config
func InitECRClientWithConfig(sess *session.Session, creds *credentials.Credentials) (*Client, error) {
	// Create a ECR service client configured with credentials
	c := ecr.New(sess, &aws.Config{Credentials: creds})
	return &Client{
		ECR: c,
	}, nil
}

// get ecr images with repository name
func GetEcrImagesDetailsWithRepoName(client *Client, registryId *string, repositoryName *string) ([]*ecr.ImageDetail, error) {
	var allImages []*ecr.ImageDetail
	var nextToken *string

	for {
		// Create describe images input
		input := &ecr.DescribeImagesInput{
			RegistryId:     registryId,
			RepositoryName: repositoryName,
			NextToken:      nextToken,
		}

		res, err := client.DescribeImages(input)
		if err != nil {
			return nil, err
		}

		allImages = append(allImages, res.ImageDetails...)

		// Check if there are more images to retrieve
		if res.NextToken == nil {
			break // No more images, exit the loop
		}

		nextToken = res.NextToken
	}

	return allImages, nil
}

// get ecr images
func GetEcrImagesDetails(client *Client, registryId *string) ([]*ecr.ImageDetail, error) {
	var allImages []*ecr.ImageDetail
	var nextToken *string

	for {
		// Create describe images input
		input := &ecr.DescribeImagesInput{
			RegistryId: registryId,
			NextToken:  nextToken,
		}

		res, err := client.DescribeImages(input)
		if err != nil {
			return nil, err
		}

		allImages = append(allImages, res.ImageDetails...)

		// Check if there are more images to retrieve
		if res.NextToken == nil {
			break // No more images, exit the loop
		}

		nextToken = res.NextToken
	}

	return allImages, nil
}
