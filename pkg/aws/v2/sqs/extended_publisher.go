package sqs

import (
	"context"
	"fmt"
	"strconv"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"google.golang.org/protobuf/proto"

	awsv2 "github.com/epifi/be-common/pkg/aws/v2"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	queuev2 "github.com/epifi/be-common/pkg/queue/v2"
)

type ServiceName string

// publisher for extended sqs msg
type ExtendedSqsPublisher struct {
	queueSvc  queuev2.QueueService
	pubMsg    queue.PublishMessage
	s3Client  s3.S3Client
	queueName QueueName
	// will be used in generating key for storing file in s3bucket
	serviceName ServiceName
}

// factory method to initialise a extended publisher
func NewExtendedPublisher(queueSvc queuev2.QueueService, pubMsg queue.PublishMessage,
	s3Client s3.S3Client, queueName QueueName, serviceName ServiceName) *ExtendedSqsPublisher {
	if pubMsg == nil {
		pubMsg = queue.NewDefaultMessage()
	}
	return &ExtendedSqsPublisher{
		queueSvc:    queueSvc,
		pubMsg:      pubMsg,
		s3Client:    s3Client,
		queueName:   queueName,
		serviceName: serviceName,
	}
}

// PublishExtendedMessage publishes  message to the pipeline
// If the message size is <= 256 kb adds the message to the sqs queue (sqs queue has a threshold of 256kb)
// if the message size > 256Kb the following approach is taken
//  1. Upload the message to s3 bucket
//  2. Add message attribute USE_S3 = true to the sqs message
//  3. publish the location of the message (uploaded to s3) to the queue along with the message attributes
//
// If USE_S3 attribute is true the subscriber should get the location of the uploaded original msg from the published
//
//	sqs message and download the original msg from s3 ot be passed to the consumer
//
// If USE_S3 attribute is false the subscriber should send the sqs message as it is to the consumer
func (p *ExtendedSqsPublisher) PublishExtendedMessage(ctx context.Context, msg proto.Message) (string, error) {
	var (
		err    error
		newMsg string
		useS3  bool
	)

	if v, ok := msg.(protoValidator); ok {
		if err = v.Validate(); err != nil {
			return "", fmt.Errorf("invalid message %v", err.Error())
		}
	}

	data := p.pubMsg.NewPublishMessage(msg)
	logger.Debug(ctx, "Adding message to the queue")

	val, err := data.GetPublishString()
	if err != nil {
		return "", err
	}

	msgBytes := []byte(val)
	if isMessageLarge(msgBytes) {
		newMsg, err = p.storeInS3(ctx, val)
		if err != nil {
			return "", fmt.Errorf("error storing object in s3: %v", err)
		}
		useS3 = true
	} else {
		newMsg = val
		useS3 = false
	}

	gm, marshalErr := p.Marshal(newMsg)
	if marshalErr != nil {
		return "", fmt.Errorf("unable to marshal queue message: %w", marshalErr)
	}
	err = setExtendedSqsMessageAttributes(gm, useS3)
	if err != nil {
		return "", fmt.Errorf("unable to set extended sqs message attributes: %v", err)
	}

	msgId, err := p.queueSvc.AddToQueue(ctx, gm, nil)
	if err != nil {
		return "", fmt.Errorf("rpc call to add to queue failed: %w", err)
	}

	return msgId, nil
}

// returns true if the message size is greater then the threshold
// returns false if the message size is lss then the threshold
func isMessageLarge(msgBytes []byte) bool {
	return len(msgBytes) > msgSizeThreshold
}

// storeInS3 uploads the message to a s3 bucket and returns the key corresponding to message
func (p *ExtendedSqsPublisher) storeInS3(ctx context.Context, msg string) (string, error) {
	msgInBytes := []byte(msg)
	key := string(p.queueName) + "/" + string(p.serviceName) + "/" + idgen.RandAlphaNumericString(5)

	err := p.s3Client.Write(ctx, key, msgInBytes, "private")
	if err != nil {
		return "", err
	}
	return key, nil
}

func (p *ExtendedSqsPublisher) Marshal(val string) (interface{}, error) {
	var sqsInpMessage = &sqs.SendMessageInput{}
	sqsInpMessage.MessageBody = awssdk.String(val)
	return sqsInpMessage, nil
}

// setExtendedSqsMessageAttributes converts the value into queue message attributes and call the default setMessageAttributes funtion
func setExtendedSqsMessageAttributes(msg interface{}, useS3 bool) error {
	messageAttribute, err := awsv2.NewDefaultStringMessageAttribute(extendedSqsMessageAttributeName, strconv.FormatBool(useS3))
	if err != nil {
		return err
	}
	messageAttributes := []queue.MessageAttribute{
		messageAttribute,
	}
	return setMessageAttributes(msg, messageAttributes)
}

// setExtendedSqsMessageAttributes sets a custom SQS message attribute for an SQS message.
//
// Amazon SQS lets us include structured metadata (such as timestamps, geospatial data, signatures, and identifiers)
// with messages using message attributes. Each message can have up to 10 attributes.
// Message attributes are optional and separate from the message body (however, they are sent alongside it).
// The subscriber can use message attributes to handle a message in a particular way without having to process
// the message body first.
// Ref- https://docs.aws.amazon.com/AWSSimpleQueueService/latest/SQSDeveloperGuide/sqs-message-attributes.html
//
// takes in the list of queue message attributes and sets the message attributes for the message attribute
func setMessageAttributes(msg interface{}, messageAttributes []queue.MessageAttribute) error {
	sqsInpMsg, ok := msg.(*sqs.SendMessageInput)
	if !ok {
		return fmt.Errorf("expected sqs.SendMessagInput got %T", msg)
	}

	customAttributeMap := make(map[string]types.MessageAttributeValue)

	for _, messageAttribute := range messageAttributes {
		sqsMessageAttribute := types.MessageAttributeValue{}
		dataType, err := messageAttribute.GetDataType()
		if err != nil {
			return err
		}
		value, err := messageAttribute.GetValue()
		if err != nil {
			return err
		}
		name, err := messageAttribute.GetName()
		if err != nil {
			return err
		}
		sqsMessageAttribute.DataType = dataType

		switch {
		case *dataType == string(awsv2.String) || *dataType == string(awsv2.Number):
			stringValue, ok := value.(*string)
			if !ok {
				return fmt.Errorf("data type and value mismatch")
			}
			sqsMessageAttribute.StringValue = stringValue
		case *dataType == string(awsv2.Binary):
			binaryValue, ok := value.([]byte)
			if !ok {
				return fmt.Errorf("data type and value mismatch")
			}
			sqsMessageAttribute.BinaryValue = binaryValue
		}
		customAttributeMap[*name] = sqsMessageAttribute
	}
	sqsInpMsg.MessageAttributes = customAttributeMap
	return nil
}
