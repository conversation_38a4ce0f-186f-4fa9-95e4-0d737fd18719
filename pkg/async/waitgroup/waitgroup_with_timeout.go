package waitgroup

import (
	"context"
	"sync"
	"time"

	"github.com/epifi/be-common/pkg/logger"
)

// Deprecated: in favour of using the SafeWait function
type WaitGroup struct {
	*sync.WaitGroup
	timeout time.Duration
}

// NewWaitGroup gives a sync.WaitGroup object with a timeout, WaitWithTimeout() function should be used instead of Wait()
func NewWaitGroup(timeout time.Duration) *WaitGroup {
	return &WaitGroup{
		timeout:   timeout,
		WaitGroup: &sync.WaitGroup{},
	}
}

// WaitWithTimeout waits for the waitgroup to finish or times out after the specified duration.
// Returns true if the waitgroup finished before timeout, false otherwise.
func (w *WaitGroup) WaitWithTimeout(ctx context.Context) bool {
	// Create a channel to receive a signal when the waitgroup is done
	done := make(chan struct{})

	// Start a goroutine to wait for the waitgroup to finish
	//nocustomlint:goroutine
	go func() {
		defer logger.RecoverPanicAndError(ctx)
		defer close(done)
		w.Wait()
	}()

	// Select statement to wait for either the waitgroup to finish or the timeout duration
	select {
	case <-done:
		// Waitgroup finished before timeout
		return true
	case <-time.After(w.timeout):
		// Timeout occurred
		return false
	}
}

// SafeWait waits for the wait group to finish or times out after the specified duration.
// Returns true if the wait group finished before timeout, false otherwise.
func SafeWait(wg *sync.WaitGroup, timeout time.Duration) bool {
	// Create a channel to receive a signal when the waitgroup is done
	done := make(chan struct{})

	// Start a goroutine to wait for the waitgroup to finish
	//nocustomlint:goroutine
	go func() {
		defer logger.RecoverPanicAndError(context.Background())
		defer close(done)
		wg.Wait()
	}()

	// Select statement to wait for either the waitgroup to finish or the timeout duration
	select {
	case <-done:
		// Waitgroup finished before timeout
		return true
	case <-time.After(timeout):
		// Timeout occurred
		return false
	}
}

// SafeWaitWithDefaultTimeout waits for the wait group to finish or times out after 30 seconds.
// Returns true if the wait group finished before 30s, false otherwise.
func SafeWaitWithDefaultTimeout(wg *sync.WaitGroup) bool {
	return SafeWait(wg, 30*time.Second)
}

// SafeWaitCtx waits for the wait group to finish or times out after the context is cancelled or
// its deadline is exceeded. Returns true if the wait group finished before the context was
// cancelled or its deadline was exceeded, false otherwise.
func SafeWaitCtx(ctx context.Context, wg *sync.WaitGroup) bool {
	done := make(chan struct{})

	//nocustomlint:goroutine
	go func() {
		defer logger.RecoverPanicAndError(ctx)
		defer close(done)
		wg.Wait()
	}()

	select {
	case <-done:
		return true
	case <-ctx.Done():
		return false
	}
}
