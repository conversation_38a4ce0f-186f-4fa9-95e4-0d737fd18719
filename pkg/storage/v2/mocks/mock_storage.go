// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/storage/v2/transaction.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	gomock "github.com/golang/mock/gomock"
)

// MockIdempotentTxnExecutor is a mock of IdempotentTxnExecutor interface.
type MockIdempotentTxnExecutor struct {
	ctrl     *gomock.Controller
	recorder *MockIdempotentTxnExecutorMockRecorder
}

// MockIdempotentTxnExecutorMockRecorder is the mock recorder for MockIdempotentTxnExecutor.
type MockIdempotentTxnExecutorMockRecorder struct {
	mock *MockIdempotentTxnExecutor
}

// NewMockIdempotentTxnExecutor creates a new mock instance.
func NewMockIdempotentTxnExecutor(ctrl *gomock.Controller) *MockIdempotentTxnExecutor {
	mock := &MockIdempotentTxnExecutor{ctrl: ctrl}
	mock.recorder = &MockIdempotentTxnExecutorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIdempotentTxnExecutor) EXPECT() *MockIdempotentTxnExecutorMockRecorder {
	return m.recorder
}

// RunIdempotentTxn mocks base method.
func (m *MockIdempotentTxnExecutor) RunIdempotentTxn(ctx context.Context, maxRetries uint, fn storagev2.InIdempotentTransaction) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RunIdempotentTxn", ctx, maxRetries, fn)
	ret0, _ := ret[0].(error)
	return ret0
}

// RunIdempotentTxn indicates an expected call of RunIdempotentTxn.
func (mr *MockIdempotentTxnExecutorMockRecorder) RunIdempotentTxn(ctx, maxRetries, fn interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunIdempotentTxn", reflect.TypeOf((*MockIdempotentTxnExecutor)(nil).RunIdempotentTxn), ctx, maxRetries, fn)
}

// RunTxn mocks base method.
func (m *MockIdempotentTxnExecutor) RunTxn(ctx context.Context, fn storagev2.InTransaction) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RunTxn", ctx, fn)
	ret0, _ := ret[0].(error)
	return ret0
}

// RunTxn indicates an expected call of RunTxn.
func (mr *MockIdempotentTxnExecutorMockRecorder) RunTxn(ctx, fn interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunTxn", reflect.TypeOf((*MockIdempotentTxnExecutor)(nil).RunTxn), ctx, fn)
}

// MockTxnExecutor is a mock of TxnExecutor interface.
type MockTxnExecutor struct {
	ctrl     *gomock.Controller
	recorder *MockTxnExecutorMockRecorder
}

// MockTxnExecutorMockRecorder is the mock recorder for MockTxnExecutor.
type MockTxnExecutorMockRecorder struct {
	mock *MockTxnExecutor
}

// NewMockTxnExecutor creates a new mock instance.
func NewMockTxnExecutor(ctrl *gomock.Controller) *MockTxnExecutor {
	mock := &MockTxnExecutor{ctrl: ctrl}
	mock.recorder = &MockTxnExecutorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTxnExecutor) EXPECT() *MockTxnExecutorMockRecorder {
	return m.recorder
}

// RunTxn mocks base method.
func (m *MockTxnExecutor) RunTxn(ctx context.Context, fn storagev2.InTransaction) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RunTxn", ctx, fn)
	ret0, _ := ret[0].(error)
	return ret0
}

// RunTxn indicates an expected call of RunTxn.
func (mr *MockTxnExecutorMockRecorder) RunTxn(ctx, fn interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunTxn", reflect.TypeOf((*MockTxnExecutor)(nil).RunTxn), ctx, fn)
}
