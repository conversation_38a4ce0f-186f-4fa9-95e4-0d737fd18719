package stream

import (
	"google.golang.org/protobuf/proto"
)

// Message published to a stream
type Message interface {
	proto.Message
	Validate() error

	// GetPartitionKey returns the key which is used to determine the target shard for the Message
	// All Messages with same partition key are mapped to same shard
	// Partition Key should ensure uniform distribution of Messages across shards for optimal
	// utilisation and preventing hot shards. Unique UUID per message is a good partition key candidate.
	GetPartitionKey() string
}

// GetPartition returns the partition number for the key
type GetPartition func(key string, numPartitions int) int
