package test

import (
	"context"
	"fmt"

	errorsPkg "github.com/pkg/errors"
	workflowSvcPb "go.temporal.io/api/workflowservice/v1"
	temporalInterceptor "go.temporal.io/sdk/interceptor"
	"go.temporal.io/sdk/worker"
	"go.temporal.io/sdk/workflow"

	workflowPkg "github.com/epifi/be-common/pkg/epifitemporal/workflow"

	"github.com/epifi/be-common/pkg/cfg"
	cfgV2 "github.com/epifi/be-common/pkg/cfg/v2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	epifitemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	epifitemporalWorker "github.com/epifi/be-common/pkg/epifitemporal/worker"
	"github.com/epifi/be-common/pkg/logger"
)

// NewWorkflowReplayer creates a new workflow replayer with required interceptors and returns that
func NewWorkflowReplayer(namespace string, workflowParamsList cfgV2.WorkflowParamsList, defaultActivityParamsList cfgV2.ActivityParamsList, interceptors ...temporalInterceptor.WorkerInterceptor) (worker.WorkflowReplayer, error) {
	err := epifitemporal.InitWorkflowParams(workflowParamsList.GetWorkflowParamsMap())
	if err != nil {
		return nil, errorsPkg.Wrap(err, "failed to load workflow params")
	}
	err = epifitemporal.InitDefaultActivityParams(defaultActivityParamsList.GetActivityParamsMap())
	if err != nil {
		return nil, errorsPkg.Wrap(err, "failed to load default activity params map")
	}

	defaultInterceptors, err := epifitemporalWorker.GetDefaultInterceptor(namespace, workflowParamsList, defaultActivityParamsList, cfgV2.PausedWorkflowsList{}, nil)
	if err != nil {
		return nil, err
	}

	wo := worker.WorkflowReplayerOptions{}
	wo.Interceptors = append(defaultInterceptors, interceptors...)
	return worker.NewWorkflowReplayerWithOptions(wo)
}

type WorkflowReplayerConfig struct {
	DefaultActivityParamsList cfgV2.ActivityParamsList
	WorkflowParamsList        cfgV2.WorkflowParamsList
}

func LoadWorkflowReplayerConfig(worker epifitemporal.Worker) (*WorkflowReplayerConfig, error) {
	conf := &WorkflowReplayerConfig{}

	koanf, _, err := cfg.LoadWorkerConfigUsingKoanf("", "", cfg.ServiceName(worker))
	if err != nil {
		return nil, errorsPkg.Wrap(err, fmt.Sprintf("failed to load config for worker: %s", worker))
	}

	if err = koanf.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf)); err != nil {
		return nil, errorsPkg.Wrap(err, fmt.Sprintf("failed to unmarshal config for worker: %s", worker))
	}

	return conf, nil
}

// WorkflowReplayerTestSuite exposes necessary apis to help with workflow replay
type WorkflowReplayerTestSuite struct {
	namespace   string
	conf        *WorkflowReplayerConfig
	wfSvcClient workflowSvcPb.WorkflowServiceClient
}

func NewWorkflowReplayTestSuite(ns epifitemporal.Namespace, conf *WorkflowReplayerConfig, wfSvcClient workflowSvcPb.WorkflowServiceClient) (*WorkflowReplayerTestSuite, error) {
	namespaceName, err := ns.GetName()
	if err != nil {
		return nil, err
	}

	return &WorkflowReplayerTestSuite{
		namespace:   namespaceName,
		conf:        conf,
		wfSvcClient: wfSvcClient,
	}, nil
}

// CheckWorkflowNonDeterminism checks workflow non-determinism by listing workflow executions based on provided filters and
// then replaying workflow events based on the execution history downloaded. In case failure while replaying any execution
// is encountered, it throws non-determinism error
func (wrts *WorkflowReplayerTestSuite) CheckWorkflowNonDeterminism(ctx context.Context, workflowType epifitemporal.Workflow, wfDef any, limit int32, filters ...workflowPkg.FilterOption) error {
	execs, err := wrts.FetchWorkflowExecutions(ctx, workflowType, limit, filters...)
	if err != nil {
		return err
	}

	if len(execs) == 0 {
		return errorsPkg.Wrap(epifierrors.ErrRecordNotFound, "no workflows found with given filters")
	}

	for _, exec := range execs {
		err = wrts.ReplayWorkflowEvents(ctx, wfDef, exec)
		if err != nil {
			return err
		}
	}

	return nil
}

// FetchWorkflowExecutions fetches workflow executions based on the given filters using advanced visibility of temporal
func (wrts *WorkflowReplayerTestSuite) FetchWorkflowExecutions(ctx context.Context, workflowType epifitemporal.Workflow, limit int32, filters ...workflowPkg.FilterOption) ([]workflow.Execution, error) {
	var wfExecutions []workflow.Execution

	wfExecList, _, err := workflowPkg.ListWorkflows(ctx, wrts.wfSvcClient, wrts.namespace, workflowType, limit, nil, filters...)
	if err != nil {
		return nil, err
	}

	for _, exec := range wfExecList {
		wfExecutions = append(wfExecutions, workflow.Execution{
			ID:    exec.Execution.WorkflowId,
			RunID: exec.Execution.RunId,
		})
	}

	return wfExecutions, nil
}

// ReplayWorkflowEvents loads a workflow execution history from the Temporal service and executes a single workflow task for it.
// Use for testing the backwards compatibility of code changes and troubleshooting workflows in a debugger.
func (wrts *WorkflowReplayerTestSuite) ReplayWorkflowEvents(ctx context.Context, wfDef any, wfExec workflow.Execution) error {
	replayer, err := NewWorkflowReplayer(wrts.namespace, wrts.conf.WorkflowParamsList, wrts.conf.DefaultActivityParamsList)
	if err != nil {
		return errorsPkg.Wrap(err, fmt.Sprintf("failed to initialise workflow replayer: %s", wrts.namespace))
	}

	replayer.RegisterWorkflow(wfDef)
	return replayer.ReplayWorkflowExecution(ctx, wrts.wfSvcClient, epifitemporalLogging.NewZapAdapter(logger.Log), wrts.namespace, wfExec)
}
