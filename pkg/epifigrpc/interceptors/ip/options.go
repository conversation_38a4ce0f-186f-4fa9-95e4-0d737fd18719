package ip

// WhitelistOptions holds configuration settings for the IP whitelist interceptor.
// All fields are optional and default to zero values (false, empty string, 0)
// which generally means whitelisting will be permissive or disabled unless configured.
type WhitelistOptions struct {
	EnableWhitelist bool   // EnableWhitelist enables or disables the IP whitelisting check.
	WhitelistedIPs  string // WhitelistedIPs is a comma-separated string of IPs that are allowed.
	NumberOfHops    int    // NumberOfHops indicates how many hops (proxies, LBs) are expected to be in front of the service, used for X-Forwarded-For parsing.
	VPCCIDRPrefix   string // VPCCIDRPrefix is used in X-Forwarded-For parsing to identify and potentially skip VPC-internal IPs.
}

// WhitelistOption is a function type used to configure the Interceptor WhitelistOptions.
type WhitelistOption func(*WhitelistOptions)

// WithWhitelistEnabled sets the EnableWhitelist option.
func WithWhitelistEnabled(enable bool) WhitelistOption {
	return func(o *WhitelistOptions) {
		o.EnableWhitelist = enable
	}
}

// WithWhitelistedIPs sets the WhitelistedIPs option.
func WithWhitelistedIPs(ips string) WhitelistOption {
	return func(o *WhitelistOptions) {
		o.WhitelistedIPs = ips
	}
}

// WithNumberOfHops sets the NumberOfHops option.
func WithNumberOfHops(hops int) WhitelistOption {
	return func(o *WhitelistOptions) {
		o.NumberOfHops = hops
	}
}

// WithVPCCIDRPrefix sets the VPCCIDRPrefix option.
func WithVPCCIDRPrefix(prefix string) WhitelistOption {
	return func(o *WhitelistOptions) {
		o.VPCCIDRPrefix = prefix
	}
}
