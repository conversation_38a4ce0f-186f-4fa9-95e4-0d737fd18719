# RateLimit Server Interceptor 

It is useful for server side rate limiting calls to a given rpc.

## Attribute based RPC rate limiter

Attribute Rate Limiter works on rate limiting based on the attributes of a resource. It works on sliding window algorithm.
This rate limiter allows rate limiting on the parameters of a gRPC request (ex. actor_id, vendor). It allows multiple 
rate limits to be configured for a single resource, which are applied in the order it is configured in.

This rate limiter does not support client based/priority based rate limiting. However, similar logic can be mimicked by 
defining a list of rate limiters with some thought.

### How to configure and use

Going through an example will make it easier to understand how to configure one.

```protobuf
syntax = "proto3";

package vendorgateway.pan;

service Pan {
    rpc ValidateV2 (ValidateV2Request) returns (ValidateV2Response);
}

message ValidateV2Request {
    message RequestHeader {
        enum Vendor {
            VENDOR_UNSPECIFIED = 0;
            FEDERAL_BANK = 1;
            IN_HOUSE = 2;
        }
        Vendor vendor = 1;
    }
    RequestHeader header = 1;
    string pan = 2;
    string name_on_card = 3;
    message Date {
        int32 year = 1;
        int32 month = 2;
        int32 day = 3;
    }
    Date date_of_birth = 4;
    string father_name = 5;
}

message ValidateV2Response {}
```
Let us configure rate limiting for the above RPC.
```yaml
AttributeRateLimiterParams:
  ResourceMap:
    vendorgateway_pan_pan_validatev2: # full gRPC name
      RateLimitList:
        "0": # all calls with regardless of parameters will have a rate limit of 7/min
          ArrayElement:
            Position: 0
          Rate: 7
          Period: 1m
          Attribute: ATTRIBUTE_NONE
          AttributeValueRegexp: '.*'
        "1": # all calls with vendor as FEDERAL will have rate limit of 5/m
          ArrayElement:
            Position: 1
          Rate: 2
          Period: 1m
          Attribute: ATTRIBUTE_VG_REQ_VENDOR
          AttributeValueRegexp: '(?i)FEDERAL'
        "2": # all calls with vendor as IN_HOUSE will have rate limit of 5/m
          ArrayElement:
            Position: 2
          Rate: 5
          Period: 1m
          Attribute: ATTRIBUTE_VG_REQ_VENDOR
          AttributeValueRegexp: '(?i)IN_HOUSE'
  Namespace: "vendorgateway"
```
The resource has three rate limiters applied on it, 
1. No attributes - all calls are rate limited at 7/min
2. If vendor in ValidateV2Request matches the regexp '(?i)FEDERAL', apply a rate limit of 2/min.
ex. If the RPC request has value of vendor as FEDERAL_BANK, then this rate limit will be applied since it matches the regexp.
3. If vendor in ValidateV2Request matches the regexp '(?i)IN_HOUSE', apply a rate limit of 5/min.

#### Attribute field in the configuration
Path to the definition `api/pkg/ratelimiter/attributeextractor/enum.proto`
The configuration field "Attribute" in every RateLimitList element defines how and what fields in the request are to be 
used to form the rate limiting key. This is dictated by the implementation of attributeextractor.Extractor that was used while initialising the rate limiter, 
which varies server to server. 

ex. ATTRIBUTE_VG_REQ_VENDOR extracts the field vendorgateway.Vendor from the field
vendorgateway.RequestHeader in any gRPC request parameters. How the field value is extracted for this attribute is defined 
in the GrpcReqExtractor implementation of attributeExtractor.Extractor

To define an extraction that is not generic (ex. account number in GetAccountDetails request), a custom attribute 
extractor is to be defined with enums appended to `api.pkg.ratelimiter.attributeextractor.Attribute`.
**Each server should have its own implementation of AttributeExtractor interface.** This is to avoid bloating each implementation 
and to keep each rate limiter that is being initialised (which is one per server) to be limited to its own set of supported attributes.
**Note: each new implementation should call the default extractor internally if the Attribute encountered is a common one and
defined in ImplementedInPkg function (`api/pkg/ratelimiter/attributeextractor/enum.go`).**

In case the attribute encountered is commonly observed across RPCs (ex. actorId from context), define the extraction logic 
in the common extractor (`pkg/ratelimiter/attributeextractor/gprc_extractor.go`) and add the enum defined to the function 
ImplementedInPkg in `api/pkg/ratelimiter/attributeextractor/enum.go` so that it can be used from all attribute extractor
implementations.

#### AttributeValueRegexp field in the configuration
The configuration field "AttributeValueRegexp" is present so that that particular rate limit is only applied in case the 
attribute's value (ex. VENDOR_FEDERAL_BANK can be a value for ATTRIBUTE_VG_REQ_VENDOR) matches the regular expression.

#### Adding rate limiter for an RPC to a server
1. Define AttributeRateLimiterParams in config.go (refer vendorgateway)
2. Define the constructor for RateLimiterV2 with the attributeExtractor that needs to be used.
(refer RateLimitServerInterceptorV2WithDefaultKeyGen in `pkg/epifigrpc/interceptors/servergen_wire/wire.go`)
3. Add the ratelimiter to GRPCUnaryInterceptors of the server in server-definition-.yml file with the path to the constructor.

## RPC Rate limiter V2

V2 is an incremental implementation on top of existing V1 based rate limiting implementation, with an additional support 
for ability to perform prioritised ratelimiting between client and origin client based on the priority distribution 
specified per RPC.

### Difference between Client and Origin Client
* client represents immediate caller of the RPC 
* origin client represents the point of origin of the RPC call trace in the backend stack.
  e.g. if there is a chain of services in a call graph such as `Service A -> Service B -> Service C`.
  For `Service C`, `Service A` will be origin client and `Service B` will be client.


### How to use
In order, to use this functionality one has to follow the below mentioned steps:

1. Decide if we need a custom key generation implementation or not. Refer to `IKeyGenerator` in `pkg/epifigrpc/interceptors/ratelimit/helper.go`
2. Add and define config params for `GrpcRatelimiterParams` as part of the application load config. A sample config for the
same might look like:
```yaml
GrpcRatelimiterParams:
  RateLimitConfig:
    Namespace: "vendorgateway-rpc"
    ResourceMap:
      # rate limit keys in map are as per GenerateKey method in vendorgateway/interceptor/ratelimiter/keygen.go
      vendorgateway_openbanking_savings_savings_getbalance_federal_bank:
        Rate: 25
        Period: 1s
        PriorityDistribution:
          IsEnabled: true
          High: 60
          Medium: 20
          Low: 20
      # rate limit keys in map as per the default key generator
      #  If DefaultKeyGenerator is used then rpc name key in rate limit config is the full grpc method name of rpc 
      # (check path variable in https://github.com/grpc/grpc/blob/master/doc/PROTOCOL-HTTP2.md) transformed using 
      # 'transformGrpcMethodName' function in pkg/epifigrpc/interceptors/ratelimit/helper.go
      svca_svcb_svcc_rpc1:
        Rate: 25
        Period: 1s
        PriorityDistribution:
          IsEnabled: true
          High: 60
          Medium: 20
          Low: 20
  ClientPriority:
    - RpcName: vendorgateway_openbanking_savings_savings_getbalance_federal_bank
      ClientPriorities:
        - ClientId: deposit_consumer_updatedepositbalance
          ClientType: GRPC
          Priority: High
        - ClientId: deposit_deposit_getaccountdetailsbyid
          ClientType: GRPC
          Priority: Medium
        - ClientId: savings_savings_getaccountbalance
          ClientType: GRPC
          Priority: Low
      OriginClientPriorities:
        - ClientId: B2CFundTransfer
          ClientType: Workflow
          Priority: Low
        - ClientId: frontend_account_signup_signup_addoauthaccount
          ClientType: GRPC
          Priority: High
    - RpcName: svca_svcb_svcc_rpc1
      ClientPriorities:
        - ClientId: svcp_svcq_svcr_rcp1
          ClientType: GRPC
          Priority: Medium
      OriginClientPriorities:
        - ClientId: WorkflowABC
          ClientType: Workflow
          Priority: Low
```

3. Initialise the interceptor as part of the gRPC server initialisation
```go

var interceptors []grpc.UnaryServerInterceptor
if conf.GrpcRatelimiterParams != nil {
  rc := storage.NewRedisClientFromConfig(cfg.GetRedisOptions(cfg.RATELIMITER_REDIS), conf.Tracing.Enable)
  rl, rlErr := ratelimiter.NewRateLimiter(store.NewSlidingWindowLogWithRedis(rc), conf.GrpcRatelimiterParams.RateLimitConfig)
  if rlErr != nil {
    logger.Panic("failed to initialize rate limit client", zap.Error(rlErr))
  }
  
  interceptors = append(interceptors, ratelimit.NewRateLimitServerInterceptorV2(rl, conf.GrpcRatelimiterParams, wire.InitialiseVgKeyGenerator()))
}

s := epifigrpc.NewSecureServerWithServerOption(conf.Flags.TrimDebugMessageFromStatus, conf.Application.Name, vgLatencyHistogramBuckets, vendorGatewayServerOptions, interceptors...)
```

### How prioritisation works

```yaml
svca_svcb_svcc_rpc1:
  Rate: 25
  Period: 1s
  PriorityDistribution:
    IsEnabled: true
    High: 60
    Medium: 20
    Low: 20
```

This distribution doesn't guarantee exact rate utilisation for a certain priority. Rate limiting is done on a best effort
basis i.e. a high priority requests can take rate quota from a medium priority request in case all the rate quota is 
exhausted for high priority, however, the reverse isn't possible. Same transitive association rule can be applied between 
high > medium > low. Thus, a requests at certain priority may utilise more than or less than the allocated quota of rate
depending on the request pattern in runtime for a given resource.

```yaml
ClientPriority:
  - RpcName: svca_svcb_svcc_rpc1
    ClientPriorities:
      - ClientId: svcp_svcq_svcr_rcp1
        ClientType: GRPC
        Priority: Medium
    OriginClientPriorities:
      - ClientId: WorkflowABC
        ClientType: Workflow
        Priority: Low
```

This configuration defines a list of clients and their corresponding priorities.

To provide more flexible matching criteria, the system also supports a prefix-based matching mechanism for comparing
client information against configured client IDs. To perform prefix-based matching a wild card(*) suffix must be
added to the ClientId.

e.g.
Client with ID "abc123" matches the configuration entry with the ClientID "abc*" because "abc123" shares the same
prefix "abc." Another client with ID "abcd456" also matches the same configuration entry, as "abcd*" starts with "abc."

In situations where multiple clients share the same prefix and, consequently, match the same configuration entry,
conflicts may arise in their priorities. To resolve these conflicts, the configuration follows an "insertion order"
resolution strategy. The priority of the first encountered client with the conflicting prefix takes precedence over
the priorities of subsequent clients sharing the same prefix.

## RPC Rate limiter [Deprecated]

### How to use:

* Add rate limit config in config file of the service whose rpcs need rate limiting.

* If we want a custom rate limit key generator we can implement one as per our use-case otherwise DefaultKeyGenerator will be used. If DefaultKeyGenerator is used
  then rpc name key in rate limit config is the full grpc method name of rpc (check path variable in https://github.com/grpc/grpc/blob/master/doc/PROTOCOL-HTTP2.md) transformed using 'transformGrpcMethodName' function in pkg/epifigrpc/interceptors/ratelimit/helper.go otherwise if a custom key generator is used then the rpc name key in rate limit config depends on the key generation logic.

* Create a rate limiter instance using rate limiter library present in pkg/ratelimiter. This will be used in interceptor.

* Create interceptor instance using NewRateLimitServerInterceptor function and pass it in interceptor options while creating the grpc server.

[Ref Usage](https://github.com/epiFi/gamma/pull/17900)
