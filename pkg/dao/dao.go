package dao

import (
	"context"
	"reflect"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/pagination"
	storage "github.com/epifi/be-common/pkg/storage/v2"
)

// Model is the interface that should be implemented by the Gorm model types, which wish to use this package.
type Model[P proto.Message] interface {
	GetProto() P
}

// modelArr represents an array of Model which implements the pagination.Rows method. It is used to generate the
// page token.
type modelArr[P proto.Message, M Model[P]] struct {
	models          []M
	timestampGetter func(M) time.Time
}

// newModels returns a new instance of modelArr
func newModels[P proto.Message, M Model[P]](models []M, timestampGetter func(M) time.Time) *modelArr[P, M] {
	return &modelArr[P, M]{
		models:          models,
		timestampGetter: timestampGetter,
	}
}

func (ms *modelArr[P, M]) Slice(start, end int) pagination.Rows {
	return &modelArr[P, M]{ms.models[start:end], ms.timestampGetter}
}
func (ms *modelArr[P, M]) GetTimestamp(index int) time.Time {
	return ms.timestampGetter(ms.models[index])
}
func (ms *modelArr[P, M]) Size() int { return len(ms.models) }

// GetModelFunc is a function that maps a proto message to Model (which is implemented by the Gorm model)
type GetModelFunc[P proto.Message] func(P) Model[P]

// DAO represents the generic DAO struct
type DAO[P proto.Message, M Model[P]] struct {
	db                      *gorm.DB
	getModelFunc            GetModelFunc[P]
	maxPageSizeProviderFunc func() uint32
	getPaginationParamFunc  func(M) time.Time
}

type Options[P proto.Message, M Model[P]] func(*DAO[P, M])

// WithPagination can be used to initialise the DAO with pagination.
func WithPagination[P proto.Message, M Model[P]](maxPageSizeProviderFunc func() uint32, getPaginationParamFunc func(M) time.Time) func(dao *DAO[P, M]) {
	return func(dao *DAO[P, M]) {
		dao.maxPageSizeProviderFunc = maxPageSizeProviderFunc
		dao.getPaginationParamFunc = getPaginationParamFunc
	}
}

// New returns a new instance of a generic DAO method that can be used to implement DAO functionality
// for different protobuf types. It takes the gorm.DB instance, getModelFunc is a function mapping protobuf type to
// gorm Model, and options (e.g. WithPagination). Imp: The getModelFunc must handle for nil proto values as well, or else
// some methods may throw panic.
func New[Proto proto.Message, DaoModel Model[Proto]](db *gorm.DB, getModelFunc GetModelFunc[Proto], options ...Options[Proto, DaoModel]) *DAO[Proto, DaoModel] {
	dao := &DAO[Proto, DaoModel]{
		db:           db,
		getModelFunc: getModelFunc,
	}

	for _, o := range options {
		o(dao)
	}
	return dao
}

func applyFiltersToGormDB(db *gorm.DB, filter ...storage.FilterOption) *gorm.DB {
	for _, spec := range filter {
		db = spec.ApplyInGorm(db)
	}
	return db
}

// FindOne fetches only a single entity from the DB using the passed filters. If fieldmask is nil epifierrors.ErrInvalidArgument
// is returned.
func (d *DAO[P, M]) FindOne(ctx context.Context, fieldMask *fieldmaskpb.FieldMask, filters ...storage.FilterOption) (p P, err error) {
	if fieldMask == nil || len(fieldMask.GetPaths()) == 0 {
		return p, errors.Wrap(epifierrors.ErrInvalidArgument, "fieldmask cannot be empty")
	}

	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	db = db.Select(fieldMask.GetPaths())

	var mod M
	mod = reflect.New(reflect.TypeOf(mod).Elem()).Interface().(M)

	db = applyFiltersToGormDB(db, filters...)

	res := db.Limit(1).Find(mod)

	if res.Error != nil {
		return p, errors.Wrap(res.Error, "error in fetching data by column")
	}
	if res.RowsAffected == 0 {
		return p, epifierrors.ErrRecordNotFound
	}
	return mod.GetProto(), nil
}

// Find returns all the entities that match the given filters. If fieldmask is nil epifierrors.ErrInvalidArgument
// is returned.
func (d *DAO[P, M]) Find(ctx context.Context, fieldMask *fieldmaskpb.FieldMask, filters ...storage.FilterOption) (pbRes []P, err error) {
	if fieldMask == nil || len(fieldMask.GetPaths()) == 0 {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "fieldmask cannot be empty")
	}

	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	db = db.Select(fieldMask.GetPaths())
	db = applyFiltersToGormDB(db, filters...)

	var models []M

	res := db.Find(&models)
	if res.Error != nil {
		return pbRes, errors.Wrap(res.Error, "error in fetching data by column")
	}
	if res.RowsAffected == 0 {
		return pbRes, nil
	}

	pbRes = make([]P, 0, len(models))
	for i := 0; i < len(models); i++ {
		pbRes = append(pbRes, models[i].GetProto())
	}

	return pbRes, nil
}

// FindPaginated returns the requested data in batches of pageSize. The pagination is done on the basis of the passed
// paginationCol, after sorting them in descending order. Currently, pagination is supported only on timestamp type columns
// so make sure, the paginationCol contains Time data.
func (d *DAO[P, M]) FindPaginated(
	ctx context.Context, fieldmask *fieldmaskpb.FieldMask, paginationCol string, pageToken *pagination.PageToken, pageSize uint32, filters ...storage.FilterOption,
) (pbRes []P, pageCtxResp *rpc.PageContextResponse, err error) {
	if d.getPaginationParamFunc == nil {
		return nil, nil, errors.Wrap(epifierrors.ErrUnimplemented, "initialise DAO using WithPagination option to use this function")
	}
	if fieldmask == nil || len(fieldmask.GetPaths()) == 0 {
		return nil, nil, errors.Wrap(epifierrors.ErrInvalidArgument, "fieldmask cannot be empty")
	}
	if pageSize == 0 || pageSize > d.maxPageSizeProviderFunc() {
		pageSize = d.maxPageSizeProviderFunc()
	}

	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	db = db.Select(fieldmask.GetPaths())

	var models []M

	db = applyFiltersToGormDB(db, filters...)
	db = d.addPagination(db, paginationCol, pageToken, pageSize)
	res := db.Find(&models)
	if res.Error != nil {
		return nil, nil, errors.Wrap(res.Error, "failed to find records")
	}

	rows, resp, err := pagination.NewPageCtxResp(pageToken, int(pageSize), newModels[P, M](models, d.getPaginationParamFunc))
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to create new page context response")
	}

	models = rows.(*modelArr[P, M]).models
	pageCtxResp = resp
	pbRes = make([]P, 0, len(models))
	for _, m := range models {
		pbRes = append(pbRes, m.GetProto())
	}
	return pbRes, pageCtxResp, nil
}

func (d *DAO[P, M]) addPagination(db *gorm.DB, createdAtCol string, pageToken *pagination.PageToken, pageSize uint32) *gorm.DB {
	if pageToken != nil {
		if pageToken.IsReverse {
			db = db.Where(createdAtCol+" >= ?", pageToken.Timestamp.AsTime()).
				Order(createdAtCol)
		} else {
			db = db.Where(createdAtCol+" <= ?", pageToken.Timestamp.AsTime()).
				Order(createdAtCol + " DESC")
		}
		db = db.Offset(int(pageToken.Offset))
	} else {
		db = db.Order(createdAtCol + " DESC")
	}
	// fetch pageSize + 1 extra row to compute next page availability.
	db = db.Limit(int(pageSize + 1))
	return db
}

// CreateOne inserts an entity into the DB.
func (d *DAO[P, M]) CreateOne(ctx context.Context, dataPb P) (p P, err error) {
	dataModel := d.getModelFunc(dataPb)
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	res := db.Create(dataModel)
	if res.Error != nil {
		if storage.IsDuplicateRowError(res.Error) {
			return p, epifierrors.ErrDuplicateEntry
		}
		return p, errors.Wrap(res.Error, "error creating new entry")
	}
	return dataModel.GetProto(), nil
}

// Create inserts multiple entities into the DB.
func (d *DAO[P, M]) Create(ctx context.Context, dataPb []P) (pbRes []P, err error) {
	models := make([]M, 0, len(dataPb))
	for _, pb := range dataPb {
		models = append(models, d.getModelFunc(pb).(M))
	}

	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	if err := db.Create(models).Error; err != nil {
		return nil, errors.Wrap(err, "error creating new entries")
	}

	pbRes = make([]P, 0, len(models))
	for _, m := range models {
		pbRes = append(pbRes, m.GetProto())
	}

	return pbRes, nil
}

// Update updates the row identified by the passed proto. If the dataPb proto has the primary-key field set, then, it is
// used in the where clause along with any of the passed filters, else you need to pass the filter for where clause.
// If no rows are updated, then epifierrors.ErrNoRowsAffected is returned. Note that, by default, the zero value fields
// in dataPb are not updated. To update those fields, you need to explicitly pass the column in updateMask.
func (d *DAO[P, M]) Update(ctx context.Context, dataPb P, updateMask *fieldmaskpb.FieldMask, filters ...storage.FilterOption) error {
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	if updateMask != nil && len(updateMask.GetPaths()) > 0 {
		db = db.Select(updateMask.GetPaths())
	}

	dataModel := d.getModelFunc(dataPb)
	db = applyFiltersToGormDB(db, filters...)

	res := db.Updates(dataModel)
	if res.Error != nil {
		return errors.Wrap(res.Error, "error in updating data")
	}
	if res.RowsAffected == 0 {
		return epifierrors.ErrNoRowsAffected
	}
	return nil
}

// UpdateAndReturn updates the given rows and returns the rows. You can use the returnMask to select
// the columns that you want to be returned. If nil, epifierrors.ErrInvalidArgument is returned. If no rows are
// updated, then epifierrors.ErrNoRowsAffected is returned. Please note that you need to explicitly pass filters for the where
// clause, or else this will return gorm.ErrMissingWhereClause is returned. And if you want to update a zero value, then
// you need to explicitly select the column in fieldmask.
func (d *DAO[P, M]) UpdateAndReturn(ctx context.Context, dataPb P, updateMask *fieldmaskpb.FieldMask, returnMask *fieldmaskpb.FieldMask, filters ...storage.FilterOption) ([]P, error) {
	if returnMask == nil {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "returnMask cannot be empty")
	}

	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	if updateMask != nil && len(updateMask.GetPaths()) > 0 {
		db = db.Select(updateMask.GetPaths())
	}

	dataModel := d.getModelFunc(dataPb)
	db = applyFiltersToGormDB(db, filters...)

	returnCols := make([]clause.Column, 0)
	if returnMask != nil {
		for _, colName := range returnMask.GetPaths() {
			returnCols = append(returnCols, clause.Column{Name: colName})
		}
	}

	var models []M
	res := db.Model(&models).Clauses(clause.Returning{Columns: returnCols}).Updates(dataModel)
	if res.Error != nil {
		return nil, errors.Wrap(res.Error, "error in updating data")
	}
	if res.RowsAffected == 0 {
		return nil, epifierrors.ErrNoRowsAffected
	}

	pbRes := make([]P, 0, len(models))
	for i := 0; i < len(models); i++ {
		pbRes = append(pbRes, models[i].GetProto())
	}

	return pbRes, nil
}

// Delete deletes multiple rows from the DB. If no rows are deleted, epifierrors.ErrNoRowsAffected is returned.
func (d *DAO[P, M]) Delete(ctx context.Context, filters ...storage.FilterOption) error {
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	db = applyFiltersToGormDB(db, filters...)

	var mod M
	mod = reflect.New(reflect.TypeOf(mod).Elem()).Interface().(M)
	res := db.Delete(mod)
	if res.Error != nil {
		return errors.Wrap(res.Error, "error while deleting data from db")
	}
	if res.RowsAffected == 0 {
		return epifierrors.ErrNoRowsAffected
	}
	return nil
}
