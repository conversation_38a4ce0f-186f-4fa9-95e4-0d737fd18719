package processor

import (
	"context"
	"errors"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	beCommonPb "github.com/epifi/be-common/api/sherlock/dev/dbstate"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gringott/sgkyc/dao"
)

type DevKycVendorDataEntity struct {
	kycVendorDataDao dao.KYCVendorDataDao
}

func NewDevKycVendorDataEntity(kycVendorDataDao dao.KYCVendorDataDao) *DevKycVendorDataEntity {
	return &DevKycVendorDataEntity{
		kycVendorDataDao: kycVendorDataDao,
	}
}

func (s *DevKycVendorDataEntity) FetchParamList() ([]*beCommonPb.ParameterMeta, error) {
	paramList := []*beCommonPb.ParameterMeta{
		{
			Name:            ReferenceId,
			Label:           ReferenceIdLabel,
			Type:            beCommonPb.ParameterDataType_STRING,
			ParameterOption: beCommonPb.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (s *DevKycVendorDataEntity) FetchData(ctx context.Context, filters []*beCommonPb.Filter) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}
	referenceId := ""
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ReferenceId:
			referenceId = filter.GetStringValue()
		}
	}

	if referenceId != "" {
		vendorData, err := s.kycVendorDataDao.GetByRefID(ctx, referenceId)
		if err != nil {
			return "", err
		}

		marshalledVendorData, errMarshal := protojson.Marshal(vendorData)
		if errMarshal != nil {
			logger.Error(ctx, "error while marshalling applicant", zap.Error(errMarshal))
			return "", errMarshal
		}
		return string(marshalledVendorData), nil
	}

	return "mandatory parameters missing", nil
}
