package processor

import (
	"context"
	"encoding/csv"
	"fmt"
	"strings"
	"time"

	"net/http"

	"cloud.google.com/go/bigquery"
	"cloud.google.com/go/storage"
	"go.uber.org/zap"
	"google.golang.org/api/googleapi"
	"google.golang.org/api/iterator"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/slack_bot/config/genconf"
)

// DataProcessor handles BigQuery and GCS operations
type DataProcessor struct {
	bigQueryClient      *bigquery.Client
	storageClient       *storage.Client
	gcsBucket           string
	projectID           string
	region              string
	secretName          string
	dataServicesDataset string
}

// QueryResult holds the result of the data extraction
type QueryResult struct {
	RequestID     string    `json:"request_id"`
	RequesterID   string    `json:"requester_id"`
	FormType      string    `json:"form_type"`
	Query         string    `json:"query,omitempty"`
	DataPoints    []string  `json:"data_points,omitempty"`
	Reason        string    `json:"reason"`
	Status        string    `json:"status"`
	QueryJobID    string    `json:"query_job_id"`
	OutputFileURL string    `json:"output_file_url"`
	PresignedURL  string    `json:"presigned_url"`
	RowCount      int64     `json:"row_count"`
	FileSize      int64     `json:"file_size"`
	ProcessedAt   time.Time `json:"processed_at"`
	ProcessedBy   string    `json:"processed_by"`
}

func NewDataProcessor(
	ctx context.Context,
	conf *genconf.Config,
	bigQueryClient *bigquery.Client,
	storageClient *storage.Client,
) (*DataProcessor, error) {
	return &DataProcessor{
		bigQueryClient:      bigQueryClient,
		storageClient:       storageClient,
		gcsBucket:           conf.BigQuery().GCSBucket,
		projectID:           conf.BigQuery().ProjectID,
		region:              conf.BigQuery().Region,
		secretName:          conf.BigQuery().SecretName,
		dataServicesDataset: conf.BigQuery().DataServicesDataset,
	}, nil
}

// ProcessDataExtraction handles the complete data processing pipeline
func (dp *DataProcessor) ProcessDataExtraction(ctx context.Context, formData map[string]interface{}, processorID string) (*QueryResult, error) {
	formType := formData["formType"].(string)
	requesterID := formData["requesterId"].(string)
	reason := formData["reason"].(string)

	requestID := fmt.Sprintf("de_%s_%d", requesterID, time.Now().Unix())

	result := &QueryResult{
		RequestID:   requestID,
		RequesterID: requesterID,
		FormType:    formType,
		Reason:      reason,
		Status:      "processing",
		ProcessedAt: time.Now(),
		ProcessedBy: processorID,
	}

	var query string

	switch formType {
	case "query_based":
		query = formData["query"].(string)
		result.Query = query

		workflowResult, err := dp.ExecuteWorkflow(ctx, query, requestID)
		if err != nil {
			result.Status = "failed"
			logger.Error(ctx, "Failed to execute workflow", zap.Error(err))
			return result, fmt.Errorf("failed to execute workflow: %w", err)
		}

		result.QueryJobID = workflowResult.QueryJobID
		result.OutputFileURL = workflowResult.OutputFileURL
		result.PresignedURL = workflowResult.PresignedURL
		result.RowCount = workflowResult.RowCount
		result.FileSize = workflowResult.FileSize
		result.Status = "completed"

	case "attributes_based":
		// For attributes-based requests, just store the data points
		// The actual processing will happen later when user uploads CSV file
		dataPoints := formData["dataPoints"].([]interface{})
		var dataPointsStr []string
		for _, dp := range dataPoints {
			dataPointsStr = append(dataPointsStr, dp.(string))
		}
		result.DataPoints = dataPointsStr
		result.Status = "pending_upload"
	default:
		logger.Error(ctx, "Invalid form type", zap.String("formType", formType))
		return nil, fmt.Errorf("invalid form type: %s", formType)
	}

	return result, nil
}

// executeWorkflow runs the complete BigQuery to GCS workflow
func (dp *DataProcessor) ExecuteWorkflow(ctx context.Context, query string, requestID string) (*QueryResult, error) {
	timeoutCtx, cancel := context.WithTimeout(ctx, 30*time.Minute)
	defer cancel()

	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("%s_%s", "bq_results", timestamp)

	bqQuery := dp.bigQueryClient.Query(query)

	job, err := bqQuery.Run(timeoutCtx)
	if err != nil {
		if timeoutCtx.Err() == context.DeadlineExceeded {
			logger.Error(ctx, "Query execution timed out after 30 minutes")
			return nil, fmt.Errorf("query execution timed out after 30 minutes")
		}
		logger.Error(ctx, "Failed to execute BigQuery query", zap.Error(err))
		return nil, fmt.Errorf("failed to execute BigQuery query: %w", err)
	}

	status, err := job.Wait(timeoutCtx)
	if err != nil {
		if timeoutCtx.Err() == context.DeadlineExceeded {
			logger.Error(ctx, "Query execution timed out after 30 minutes - query was too complex or data set too large")
			return nil, fmt.Errorf("query execution timed out after 30 minutes - query was too complex or data set too large")
		}
		logger.Error(ctx, "Failed to wait for BigQuery job", zap.Error(err))
		return nil, fmt.Errorf("BigQuery job failed: %w", err)
	}

	if status.Err() != nil {
		logger.Error(ctx, "BigQuery job completed with errors", zap.Error(status.Err()))
		return nil, fmt.Errorf("BigQuery job completed with errors: %w", status.Err())
	}

	iter, err := job.Read(timeoutCtx)
	if err != nil {
		logger.Error(ctx, "Failed to read query results", zap.Error(err))
		return nil, fmt.Errorf("failed to read query results: %w", err)
	}

	csvData, rowCount, err := convertToCSV(iter)
	if err != nil {
		logger.Error(ctx, "Failed to convert results to CSV", zap.Error(err))
		return nil, fmt.Errorf("failed to convert results to CSV: %w", err)
	}

	// Upload to GCS
	object := dp.storageClient.Bucket(dp.gcsBucket).Object(fileName)
	writer := object.NewWriter(ctx)
	writer.ContentType = "text/csv"

	if _, err := writer.Write(csvData); err != nil {
		logger.Error(ctx, "Failed to write to GCS", zap.Error(err))
		return nil, fmt.Errorf("failed to write to GCS: %w", err)
	}
	if err := writer.Close(); err != nil {
		logger.Error(ctx, "Failed to close GCS writer", zap.Error(err))
		return nil, fmt.Errorf("failed to close GCS writer: %w", err)
	}

	presignedURL, err := dp.GeneratePresignedDownloadURL(ctx, fileName)
	if err != nil {
		logger.Error(ctx, "Failed to generate presigned URL for upload", zap.Error(err))
		return nil, fmt.Errorf("failed to generate presigned URL for upload: %w", err)
	}

	result := &QueryResult{
		QueryJobID:    job.ID(),
		OutputFileURL: fmt.Sprintf("gs://%s/%s", dp.gcsBucket, fileName),
		PresignedURL:  presignedURL,
		RowCount:      rowCount,
		FileSize:      int64(len(csvData)),
	}

	return result, nil
}

func convertToCSV(iter *bigquery.RowIterator) ([]byte, int64, error) {
	var csvBuilder strings.Builder
	writer := csv.NewWriter(&csvBuilder)

	schema := iter.Schema
	headers := make([]string, len(schema))
	for i, field := range schema {
		headers[i] = field.Name
	}
	if err := writer.Write(headers); err != nil {
		logger.Error(context.Background(), "Failed to write CSV header", zap.Error(err))
		return nil, 0, fmt.Errorf("failed to write CSV header: %w", err)
	}

	// Write data rows
	var rowCount int64
	for {
		var row []bigquery.Value
		err := iter.Next(&row)
		if err == iterator.Done {
			break
		}
		if err != nil {
			logger.Error(context.Background(), "Failed to read row", zap.Error(err))
			return nil, 0, fmt.Errorf("failed to read row: %w", err)
		}

		// Convert row values to strings
		stringRow := make([]string, len(row))
		for i, value := range row {
			stringRow[i] = fmt.Sprintf("%v", value)
		}

		if err := writer.Write(stringRow); err != nil {
			logger.Error(context.Background(), "Failed to write CSV row", zap.Error(err))
			return nil, 0, fmt.Errorf("failed to write CSV row: %w", err)
		}
		rowCount++
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		logger.Error(context.Background(), "Failed to flush CSV writer", zap.Error(err))
		return nil, 0, fmt.Errorf("failed to flush CSV writer: %w", err)
	}

	return []byte(csvBuilder.String()), rowCount, nil
}

// Close closes the clients
func (dp *DataProcessor) Close() error {
	if err := dp.bigQueryClient.Close(); err != nil {
		logger.Error(context.Background(), "Failed to close BigQuery client", zap.Error(err))
		return fmt.Errorf("failed to close BigQuery client: %w", err)
	}
	if err := dp.storageClient.Close(); err != nil {
		logger.Error(context.Background(), "Failed to close Storage client", zap.Error(err))
		return fmt.Errorf("failed to close Storage client: %w", err)
	}
	return nil
}

// ValidateQuery performs a dry run validation of a BigQuery SQL query
func (dp *DataProcessor) ValidateQuery(ctx context.Context, query string) error {
	bqQuery := dp.bigQueryClient.Query(query)
	bqQuery.DryRun = true
	bqQuery.Location = dp.region

	job, err := bqQuery.Run(ctx)
	if err != nil {
		logger.Error(ctx, "Query validation failed", zap.Error(err), zap.String("query", query))
		return fmt.Errorf("query validation failed: %w", err)
	}

	// Dry run is not asynchronous, so get the latest status and statistics
	status := job.LastStatus()
	if err := status.Err(); err != nil {
		logger.Error(ctx, "Query validation failed with errors", zap.Error(err), zap.String("query", query))
		return fmt.Errorf("query validation failed: %w", err)
	}

	return nil
}

// GetStorageClient returns the GCS storage client
func (dp *DataProcessor) GetStorageClient() *storage.Client {
	return dp.storageClient
}

// GetGCSBucket returns the GCS bucket name
func (dp *DataProcessor) GetGCSBucket() string {
	return dp.gcsBucket
}

func (dp *DataProcessor) CreateBigQueryTable(ctx context.Context, gcsURL string, requestId string, columnHeaders, dataTypes []string, columnCount int) (string, error) {
	schema := bigquery.Schema{}
	for i, header := range columnHeaders {
		cleanHeaderName := strings.TrimSpace(header)

		var fieldType bigquery.FieldType
		if i < len(dataTypes) {
			switch dataTypes[i] {
			case "INT64":
				fieldType = bigquery.IntegerFieldType
			case "FLOAT64":
				fieldType = bigquery.FloatFieldType
			case "DATE":
				fieldType = bigquery.DateFieldType
			case "TIMESTAMP":
				fieldType = bigquery.TimestampFieldType
			default:
				fieldType = bigquery.StringFieldType
			}
		} else {
			fieldType = bigquery.StringFieldType
		}

		schema = append(schema, &bigquery.FieldSchema{
			Name: cleanHeaderName,
			Type: fieldType,
		})
	}

	metaData := &bigquery.TableMetadata{
		Schema: schema,
	}
	return dp.createTable(ctx, metaData, requestId, gcsURL, false, 1)
}

func (dp *DataProcessor) createTable(ctx context.Context, metaData *bigquery.TableMetadata, requestId string, gcsURL string, autoDetect bool, skipLeadingRows int) (string, error) {
	projectID := dp.projectID
	datasetID := dp.dataServicesDataset
	requestID := strings.ReplaceAll(requestId, ".", "_")
	tableID := fmt.Sprintf("att_data_extract_%s", requestID)

	fullTableName := fmt.Sprintf("%s.%s.%s", projectID, datasetID, tableID)
	tableRef := dp.bigQueryClient.Dataset(datasetID).Table(tableID)
	if err := tableRef.Create(ctx, metaData); err != nil {
		logger.Error(ctx, "Failed to create table", zap.Error(err))
		return "", fmt.Errorf("failed to create table: %w", err)
	}
	gcsRef := bigquery.NewGCSReference(gcsURL)
	gcsRef.SourceFormat = bigquery.CSV
	gcsRef.AutoDetect = autoDetect
	gcsRef.SkipLeadingRows = int64(skipLeadingRows)
	loader := dp.bigQueryClient.Dataset(datasetID).Table(tableID).LoaderFrom(gcsRef)
	loader.WriteDisposition = bigquery.WriteEmpty

	job, err := loader.Run(ctx)
	if err != nil {
		logger.Error(ctx, "Failed to run BigQuery job", zap.Error(err))
		return "", fmt.Errorf("failed to run BigQuery job: %w", err)
	}
	status, err := job.Wait(ctx)
	if err != nil {
		logger.Error(ctx, "Failed to wait for BigQuery job", zap.Error(err))
		return "", fmt.Errorf("failed to wait for BigQuery job: %w", err)
	}

	if status.Err() != nil {
		logger.Error(ctx, "BigQuery job completed with error", zap.Error(status.Err()))
		return "", fmt.Errorf("job completed with error: %v", status.Err())
	}
	return fullTableName, nil
}

func (dp *DataProcessor) CheckBigQueryTableExists(ctx context.Context, tableID string) (bool, error) {
	datasetID := dp.dataServicesDataset

	// Create table reference
	tableRef := dp.bigQueryClient.Dataset(datasetID).Table(tableID)

	// Try to get table metadata
	_, err := tableRef.Metadata(ctx)
	if err != nil {
		// Check if it's a "not found" error
		if e, ok := err.(*googleapi.Error); ok {
			if e.Code == http.StatusNotFound {
				return false, nil
			}
		}
		logger.Error(ctx, "Error checking table metadata", zap.Error(err))
		return false, fmt.Errorf("error checking table metadata: %w", err)
	}

	return true, nil
}

// GeneratePresignedDownloadURL generates a presigned URL for downloading files from GCS
func (dp *DataProcessor) GeneratePresignedDownloadURL(ctx context.Context, fileName string) (string, error) {
	// Create a signed URL that expires in 6 hours
	opts := &storage.SignedURLOptions{
		Scheme:  storage.SigningSchemeV4,
		Method:  "GET",
		Expires: time.Now().Add(6 * time.Hour),
	}

	signedURL, err := dp.storageClient.Bucket(dp.gcsBucket).SignedURL(fileName, opts)
	if err != nil {
		logger.Error(ctx, "Failed to generate presigned download URL", zap.Error(err))
		return "", fmt.Errorf("failed to generate presigned download URL: %w", err)
	}

	return signedURL, nil
}
