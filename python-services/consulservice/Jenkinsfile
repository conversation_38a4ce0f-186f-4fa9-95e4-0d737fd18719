pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '20'))
    }
    parameters
            {
                string(name: '<PERSON><PERSON><PERSON>', defaultValue: 'master', description: 'If IMAGE_ID is provided, then this value of branch is not used')
                choice(name: 'TARGET', choices: ['consulservice'], description: 'Launching Pipeline for { consulservice }')
                extendedChoice(
                        defaultValue: '',
                        description: 'Select ENVs you want to deploy server to',
                        multiSelectDelimiter: ',',
                        name: 'ENV',
                        value: 'demo,staging,qa,uat',
                        quoteValue: false,
                        saveJSONParameterToFile: false,
                        type: 'PT_CHECKBOX',
                        visibleItemCount: 10
                )
                string(name: 'IMAGE_ID', defaultValue: '', description: 'If you provide Image Id e.g ami-xxxxxxxxxx, It will not build the binary.')
                booleanParam(name: 'REFRESH', defaultValue: false, description: '<PERSON><PERSON>, ignore this variable! This is for DevOps to refresh Jenkins<PERSON>le.')
            }
    environment {
        TARGET = "${params.TARGET}"
        ENV = "${params.ENV}"
        BRANCH = "${params.BRANCH}"
    }
    stages {
        stage('Infra clone') {
            steps {
                dir('infra') {
                    deleteDir()
                    git credentialsId: 'github-app-epifi', poll: false, url: 'https://github.com/epiFi/infra.git', branch: "aditya/terraform-upgrade"
                }
                script {
                    if (params.REFRESH == true) {
                        echo "Jenkins file was loaded....... finish build now"
                        currentBuild.displayName = "#${BUILD_NUMBER}-${TARGET}-Refresh"
                        currentBuild.description = "#${BUILD_NUMBER}-${TARGET}-Refresh"
                        currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                        sleep(5)
                    }
                    IMAGE_ID = "${params.IMAGE_ID}"
                    if (params.ENV == '') {
                        currentBuild.displayName = "#${BUILD_NUMBER}-${TARGET}-${BRANCH}-BUILD-AMI"
                        currentBuild.description = "#${BUILD_NUMBER}-${TARGET}-${BRANCH}-BUILD-AMI"
                    } else {
                        currentBuild.displayName = "#${BUILD_NUMBER}-${TARGET}-${BRANCH}-${ENV}"
                        currentBuild.description = "#${BUILD_NUMBER}-${TARGET}-${BRANCH}-${ENV}"
                    }
                }
            }
        }
        stage('Build binary') {
            when {
                expression { IMAGE_ID == '' }
            }
            steps {
                dir('consulservice') {
                    // Wipe the workspace so we are building completely clean
                    deleteDir()
                    git credentialsId: 'github-app-epifi', poll: false, url: 'https://github.com/epiFi/consulservice.git', branch: "${BRANCH}"
                    sh "chmod 777 ${env.WORKSPACE}/scripts/consulservice/script.sh && ${env.WORKSPACE}/scripts/consulservice/script.sh"
                }
            }
        }
        stage('Build packer') {
            environment {
                BASE_AMI = " epifi-python-golden-image-v2"
                AMI_NAME = "${TARGET}"
                SERVICE_TYPE = "python"
                SERVICE_NAME = "${TARGET}"
                DEST_DIR_PATH = "/home/<USER>/consulservice"
                SYSTEMD_SERVICE_SOURCE = "python-services-v2/templates/consulservice.service"
                SYSTEMD_SERVICE_DEST = "${TARGET}-tmpl.service"
                SOURCE_DIR_PATH = "${WORKSPACE}/consulservice/output/consulservice/"
                PACKER_LOG = "${WORKSPACE}/consulservice/output/${TARGET}_packer.log"
                IMAGE_ID_FILE = "${WORKSPACE}/consulservice/output/image_id.txt"
                MANIFEST_FILE_NAME = "manifest_${TARGET}"
                TARGETAPP = "${TARGET}"
                SETUP_SCRIPT_PATH = "scripts/setup.sh"
            }
            when {
                expression { IMAGE_ID == '' }
            }
            steps {
                dir('infra') {
                    sh "chmod 777 ${env.WORKSPACE}/scripts/common_packer.sh && ${env.WORKSPACE}/scripts/common_packer.sh"
                }
                script {
                    IMAGE_ID = readFile(file: "${IMAGE_ID_FILE}")
                    echo "${IMAGE_ID}"
                    echo "Adding build related tags to ${IMAGE_ID}"
                    sh "aws ec2 create-tags --tags Key=BUILD_REF,Value=\${BUILD_NUMBER} --resources ${IMAGE_ID}"
                }
            }
        }
        stage('Deployment') {
            when {
                expression { return IMAGE_ID.startsWith('ami-') }
                expression { params.ENV != '' }
            }
            steps {
                script {
                    def envs = [:]
                    "$ENV".split(',').each {
                        envs["${it}"] = {
                            stage("Get Blue/Green Versions") {
                                script {
                                    ROLE_ARN = sh(script: "set +x && echo \${${"${it}".toUpperCase()}_ACCOUNT_ROLE}", returnStdout: true).trim()
                                    echo "Role ARN to be used for ${it} is: ${ROLE_ARN}"
                                    aws_credentials_json = sh(script: "set +x && aws sts assume-role --role-arn '${ROLE_ARN}' --role-session-name ${it}-${env.TARGET}-deploy --region ap-south-1", returnStdout: true).trim()
                                    AWS_ACCESS_KEY_ID = sh(script: "set +x && echo '${aws_credentials_json}' | jq --exit-status --raw-output .Credentials.AccessKeyId", returnStdout: true).trim()
                                    AWS_SECRET_ACCESS_KEY = sh(script: "set +x && echo '$aws_credentials_json' | jq --exit-status --raw-output .Credentials.SecretAccessKey", returnStdout: true).trim()
                                    AWS_SESSION_TOKEN = sh(script: "set +x && echo '$aws_credentials_json' | jq --exit-status --raw-output .Credentials.SessionToken", returnStdout: true).trim()
                                    lt_blue = sh(script: "set +x && AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}  AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN} aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names ${it}-${env.TARGET}-blue-asg --query 'AutoScalingGroups[0].LaunchTemplate.Version' --output text", returnStdout: true).trim()
                                    lt_green = sh(script: "set +x && AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}  AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN} aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names ${it}-${env.TARGET}-green-asg --query 'AutoScalingGroups[0].LaunchTemplate.Version' --output text", returnStdout: true).trim()
                                    sh "echo launch template version for blue: ${lt_blue} - Initial value"
                                    sh "echo launch template version for green: ${lt_green} - Initial value"
                                    if (lt_green == 'None') {
                                        echo "lt_green is ${lt_green}"
                                        if (lt_blue == 1) {
                                            echo "lt_blue is ${lt_blue}, so setting lt_green to 1"
                                            lt_green = 1
                                        } else {
                                            if (lt_blue == 'None') {
                                                echo "lt_green is ${lt_green} and lt_blue is ${lt_blue}. So setting both to 1"
                                                lt_blue = 1
                                                lt_green = 1
                                            } else {
                                                echo "lt_green is ${lt_green} and lt_blue is ${lt_blue} - in expr"
                                                lt_green = sh(script: "expr ${lt_blue} + 1", returnStdout: true).trim()
                                            }
                                        }
                                    }
                                    if (lt_blue == 'None') {
                                        echo "lt_blue is ${lt_blue}"
                                        if (lt_green == 1) {
                                            echo "lt_green is ${lt_green}, so setting lt_blue to 1"
                                            lt_blue = 1
                                        } else {
                                            echo "lt_blue is ${lt_blue} and lt_green is ${lt_green} - in expr"
                                            lt_blue = sh(script: "expr ${lt_green} + 1", returnStdout: true).trim()
                                        }
                                    }
                                    sh "echo launch template version for blue: ${lt_blue}"
                                    sh "echo launch template version for green: ${lt_green}"
                                }
                            }
                            stage("Deploy to ${it}") {
                                echo "Environment is: ${it}"
                                dir("${env.WORKSPACE}/${it}")
                                        {
                                            deleteDir()
                                            sh "cp -r ${env.WORKSPACE}/infra/terraform ${env.WORKSPACE}/${it}/"
                                        }
                                dir("${env.WORKSPACE}/${it}/terraform/python-services/${env.TARGET}")
                                        {
                                            is_passive_listener = sh(script: "set +x && chmod 777 ${env.WORKSPACE}/scripts/check_passive_listener.py && ${env.WORKSPACE}/scripts/check_passive_listener.py ${TARGET} ${it}", returnStdout: true).trim()
                                            sh "mkdir -p $HOME/.terraform.d/plugin-cache"
                                            sh "export TF_PLUGIN_CACHE_DIR=$HOME/.terraform.d/plugin-cache"
                                            sh "terraform init -backend-config=s3-backend-${it}.conf"
                                            sh "echo is_passive_listener: ${is_passive_listener}"
                                            if (is_passive_listener.toBoolean()) {
                                                sh "echo Passive listener exists before blue/green - so cleaning it up first....."
                                                sh "terraform apply -input=false -auto-approve -var swap=false -var rollback=false -var clean=true -var blue_version=${lt_blue} -var green_version=${lt_green} -var-file=../../env/${it}.tf -var min=1 -var max=1 -var desired=1 -var image_id=${IMAGE_ID}"
                                            }
                                            sh "terraform apply -input=false -auto-approve -var swap=false -var rollback=false -var clean=false -var blue_version=${lt_blue} -var green_version=${lt_green} -var-file=../../env/${it}.tf -var min=1 -var max=1 -var desired=1 -var image_id=${IMAGE_ID}"
                                        }
                            }
                            stage("Swap target group") {
                                dir("${env.WORKSPACE}/${it}/terraform/python-services/${env.TARGET}")
                                        {
                                            lt_blue = sh(script: "set +x && AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}  AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN} aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names ${it}-${env.TARGET}-blue-asg --query 'AutoScalingGroups[0].LaunchTemplate.Version' --output text", returnStdout: true).trim()
                                            lt_green = sh(script: "set +x && AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}  AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN} aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names ${it}-${env.TARGET}-green-asg --query 'AutoScalingGroups[0].LaunchTemplate.Version' --output text", returnStdout: true).trim()
                                            sh "echo launch template version for blue: ${lt_blue} - in swap"
                                            sh "echo launch template version for green: ${lt_green} - in swap"
                                            sh "terraform apply -input=false -auto-approve -var swap=true -var rollback=false -var clean=false -var blue_version=${lt_blue} -var green_version=${lt_green} -var-file=../../env/${it}.tf -var min=1 -var max=1 -var desired=1 -var image_id=${IMAGE_ID}"
                                        }
                            }
                            stage("Finalize Deployment") {
                                dir("${env.WORKSPACE}/${it}/terraform/python-services/${env.TARGET}")
                                        {
                                            lt_blue = sh(script: "set +x && AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}  AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN} aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names ${it}-${env.TARGET}-blue-asg --query 'AutoScalingGroups[0].LaunchTemplate.Version' --output text", returnStdout: true).trim()
                                            lt_green = sh(script: "set +x && AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}  AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN} aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names ${it}-${env.TARGET}-green-asg --query 'AutoScalingGroups[0].LaunchTemplate.Version' --output text", returnStdout: true).trim()
                                            sh "echo launch template version for blue: ${lt_blue} - in clean"
                                            sh "echo launch template version for green: ${lt_green} - in clean"
                                            sh "echo going to wait for 1 minute before deleting old infra"
                                            sh "sleep 60"
                                            sh "terraform apply -input=false -auto-approve -var swap=false -var rollback=false -var clean=true -var blue_version=${lt_blue} -var green_version=${lt_green} -var-file=../../env/${it}.tf -var min=1 -var max=1 -var desired=1 -var image_id=${IMAGE_ID}"
                                            sh "aws ec2 create-tags --tags Key=DEPLOY_REF_${it},Value=\${BUILD_NUMBER} --resources ${IMAGE_ID}"
                                        }
                            }
                        }
                    }
                    parallel envs
                }
            }
        }
    }
}
