package comments

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	cmPb "github.com/epifi/gamma/api/risk/case_management"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

//go:generate mockgen -source=./comments_helper.go -destination=../../../test/mocks/risk_ops/review/comments/mock_comments_helper.go -package=mock_comments

type ICommentsHelper interface {
	// GetComments returns comments created against input entity type and entity id
	// Fails with epifierrors.ErrInvalidArgument if entity id or entity type is nil in query
	// Fails with record not found if comments do not exist.
	GetComments(ctx context.Context, query *reviewPb.CommentQuery) ([]*reviewPb.Comment, error)
	AddCommentForCase(ctx context.Context, caseId string, commentType reviewPb.CaseCommentType, comment string, analystEmail string) error
}

type CommentsHelper struct {
	caseManagementClient cmPb.CaseManagementClient
}

func NewCommentsHelper(cmClient cmPb.CaseManagementClient) *CommentsHelper {
	return &CommentsHelper{caseManagementClient: cmClient}
}

func (h *CommentsHelper) GetComments(ctx context.Context, query *reviewPb.CommentQuery) ([]*reviewPb.Comment, error) {
	if err := query.Validate(); err != nil {
		return nil, fmt.Errorf("query validation failed %w %s", epifierrors.ErrInvalidArgument, err.Error())
	}
	resp, err := h.caseManagementClient.ListComments(ctx, &cmPb.ListCommentsRequest{
		Query: query,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return nil, fmt.Errorf("comments do not exist %w", epifierrors.ErrRecordNotFound)
		}
		return nil, fmt.Errorf("failed to fetch comments %w", err)
	}
	return resp.GetComments(), nil
}

func (h *CommentsHelper) AddCommentForCase(ctx context.Context, caseId string, commentType reviewPb.CaseCommentType, commentText string, analystEmail string) error {
	comment := &reviewPb.Comment{
		EntityType:   reviewPb.ReviewEntityType_REVIEW_ENTITY_TYPE_CASE,
		EntityId:     caseId,
		CommentType:  &reviewPb.CommentType{CommentType: &reviewPb.CommentType_CaseCommentType{CaseCommentType: commentType}},
		Comment:      commentText,
		AddedByEmail: analystEmail,
		CaseId:       caseId,
	}
	if caseId == "" || comment.Validate() != nil || analystEmail == "" {
		logger.Error(ctx, "mandatory params not passed for adding comments to the case",
			zap.String(logger.CASE_ID, caseId), zap.String(logger.AGENT_EMAIL, analystEmail),
			zap.String(logger.COMMENT_TYPE, commentType.String()), zap.Any(logger.COMMENT, comment))
		return fmt.Errorf("mandatory params not passed for adding comments to the case")
	}

	createCommentResp, err := h.caseManagementClient.CreateComment(ctx, &cmPb.CreateCommentRequest{Comment: comment})
	if err = epifigrpc.RPCError(createCommentResp, err); err != nil {
		return fmt.Errorf("failed to create comment %w", err)
	}
	return nil
}
