// Code generated by MockGen. DO NOT EDIT.
// Source: order_with_transaction.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	rpc "github.com/epifi/be-common/api/rpc"
	risk_ops "github.com/epifi/gamma/api/cx/risk_ops"
	order "github.com/epifi/gamma/api/order"
	gomock "github.com/golang/mock/gomock"
)

// MockOrderWithTransactionManager is a mock of OrderWithTransactionManager interface.
type MockOrderWithTransactionManager struct {
	ctrl     *gomock.Controller
	recorder *MockOrderWithTransactionManagerMockRecorder
}

// MockOrderWithTransactionManagerMockRecorder is the mock recorder for MockOrderWithTransactionManager.
type MockOrderWithTransactionManagerMockRecorder struct {
	mock *MockOrderWithTransactionManager
}

// NewMockOrderWithTransactionManager creates a new mock instance.
func NewMockOrderWithTransactionManager(ctrl *gomock.Controller) *MockOrderWithTransactionManager {
	mock := &MockOrderWithTransactionManager{ctrl: ctrl}
	mock.recorder = &MockOrderWithTransactionManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrderWithTransactionManager) EXPECT() *MockOrderWithTransactionManagerMockRecorder {
	return m.recorder
}

// GetByActorId mocks base method.
func (m *MockOrderWithTransactionManager) GetByActorId(ctx context.Context, req *risk_ops.GetTransactionsRequest) ([]*order.OrderWithTransactions, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", ctx, req)
	ret0, _ := ret[0].([]*order.OrderWithTransactions)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockOrderWithTransactionManagerMockRecorder) GetByActorId(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockOrderWithTransactionManager)(nil).GetByActorId), ctx, req)
}
