package dao

import (
	"context"
	"reflect"
	"testing"

	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"

	"github.com/stretchr/testify/assert"
	gormV2 "gorm.io/gorm"

	caPb "github.com/epifi/gamma/api/cx/customer_auth"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/customer_auth/dao/model"
	"github.com/epifi/gamma/cx/test"
)

type AuthFactorRetryLogsDAOTestSuite struct {
	db       *gormV2.DB
	conf     *config.Config
	RetryLog IAuthFactorRetryLogsDao
}

var (
	authFactorRetryLogsTestSuite AuthFactorRetryLogsDAOTestSuite
	testRetryLog                 = &model.CustomerAuthenticationFactorsRetryLogs{
		AuthId:           "test-auth-id",
		AuthFactor:       caPb.AuthFactor_DOB,
		AuthFactorStatus: caPb.AuthFactorStatus_VERIFICATION_SUCCESS,
	}
	testRetryLogFixture1 = &model.CustomerAuthenticationFactorsRetryLogs{
		Id:               "562d7fcf-3a31-484c-96e4-a252f41c0e10",
		AuthId:           "432d7fcf-3a31-484c-96e4-a252f41c0f10",
		AuthFactor:       caPb.AuthFactor_DOB,
		AuthFactorStatus: caPb.AuthFactorStatus_VERIFICATION_SUCCESS,
	}
	testRetryLogFixture2 = &model.CustomerAuthenticationFactorsRetryLogs{
		Id:               "562d7fcf-3a31-484c-96e4-a252f41c0e11",
		AuthId:           "432d7fcf-3a31-484c-96e4-a252f41c0f10",
		AuthFactor:       caPb.AuthFactor_LAST_FIVE_PAN_CHARACTERS,
		AuthFactorStatus: caPb.AuthFactorStatus_VERIFICATION_SUCCESS,
	}
	updateRetryLogFixture1 = &model.CustomerAuthenticationFactorsRetryLogs{
		Id:               "562d7fcf-3a31-484c-96e4-a252f41c0e10",
		AuthId:           "432d7fcf-3a31-484c-96e4-a252f41c0f10",
		AuthFactor:       caPb.AuthFactor_DOB,
		AuthFactorStatus: caPb.AuthFactorStatus_VERIFICATION_FAILED_INTERNAL,
	}
	updateRetryLogFixtureWithoutID = &model.CustomerAuthenticationFactorsRetryLogs{
		AuthId:           "432d7fcf-3a31-484c-96e4-a252f41c0f10",
		AuthFactor:       caPb.AuthFactor_DOB,
		AuthFactorStatus: caPb.AuthFactorStatus_VERIFICATION_FAILED_INTERNAL,
	}
)

func TestAuthFactorRetryLogsDAO_Create(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *model.CustomerAuthenticationFactorsRetryLogs
	}
	tests := []struct {
		name    string
		args    args
		want    *model.CustomerAuthenticationFactorsRetryLogs
		wantErr bool
	}{
		{
			name: "successful creation of record",
			args: args{
				ctx:     context.Background(),
				request: testRetryLog,
			},
			want:    testRetryLog,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, authFactorRetryLogsTestSuite.db, authFactorRetryLogsTestSuite.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := authFactorRetryLogsTestSuite.RetryLog.Create(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isRetryLogsDeepEqual(got, tt.want) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAuthFactorRetryLogsDAO_GetByPrimaryKey(t *testing.T) {
	type args struct {
		ctx context.Context
		Id  string
	}
	tests := []struct {
		name    string
		args    args
		want    *model.CustomerAuthenticationFactorsRetryLogs
		wantErr bool
	}{
		{
			name: "get retry logs fixture1 success",
			args: args{
				ctx: context.Background(),
				Id:  testRetryLogFixture1.Id,
			},
			want:    testRetryLogFixture1,
			wantErr: false,
		},
		{
			name: "get retry logs fixture2 success",
			args: args{
				ctx: context.Background(),
				Id:  testRetryLogFixture2.Id,
			},
			want:    testRetryLogFixture2,
			wantErr: false,
		},
		{
			name: "no id in request error",
			args: args{
				ctx: context.Background(),
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, authFactorRetryLogsTestSuite.db, authFactorRetryLogsTestSuite.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := authFactorRetryLogsTestSuite.RetryLog.GetByPrimaryKey(tt.args.ctx, tt.args.Id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByPrimaryKey() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isRetryLogsDeepEqual(got, tt.want) {
				t.Errorf("GetByPrimaryKey() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAuthFactorRetryLogsDAO_Update(t *testing.T) {
	type args struct {
		ctx        context.Context
		retryLog   *model.CustomerAuthenticationFactorsRetryLogs
		updateMask []caPb.CustomerAuthenticationFactorsRetryLogsMask
	}
	tests := []struct {
		name    string
		args    args
		want    *model.CustomerAuthenticationFactorsRetryLogs
		wantErr bool
	}{
		{
			name: "successful update",
			args: args{
				ctx:        context.Background(),
				retryLog:   updateRetryLogFixture1,
				updateMask: []caPb.CustomerAuthenticationFactorsRetryLogsMask{caPb.CustomerAuthenticationFactorsRetryLogsMask_RL_AUTH_FACTOR_STATUS},
			},
			want:    updateRetryLogFixture1,
			wantErr: false,
		},
		{
			name: "no update mask passed in update request",
			args: args{
				ctx:        context.Background(),
				retryLog:   updateRetryLogFixture1,
				updateMask: []caPb.CustomerAuthenticationFactorsRetryLogsMask{},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failure because of no id given",
			args: args{
				ctx:        context.Background(),
				retryLog:   updateRetryLogFixtureWithoutID,
				updateMask: []caPb.CustomerAuthenticationFactorsRetryLogsMask{caPb.CustomerAuthenticationFactorsRetryLogsMask_RL_AUTH_FACTOR_STATUS},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, authFactorRetryLogsTestSuite.db, authFactorRetryLogsTestSuite.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := authFactorRetryLogsTestSuite.RetryLog.Update(tt.args.ctx, tt.args.retryLog, tt.args.updateMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isRetryLogsDeepEqual(got, tt.want) {
				t.Errorf("update() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAuthFactorRetryLogsDAO_GetAllAuthFactorsAudits(t *testing.T) {
	type args struct {
		ctx              context.Context
		authId           string
		authFactor       caPb.AuthFactor
		authFactorStatus caPb.AuthFactorStatus
	}
	tests := []struct {
		name      string
		args      args
		wantCount int
		wantErr   bool
	}{
		{
			name: "failure because of no auth id in request",
			args: args{
				ctx:              context.Background(),
				authFactorStatus: testRetryLogFixture1.AuthFactorStatus,
			},
			wantCount: 0,
			wantErr:   true,
		},
		{
			name: "get retry logs with only auth id filter fixture 1",
			args: args{
				ctx:              context.Background(),
				authId:           testRetryLogFixture1.AuthId,
				authFactor:       caPb.AuthFactor_AUTH_FACTOR_UNSPECIFIED,
				authFactorStatus: caPb.AuthFactorStatus_AUTH_FACTOR_STATUS_UNSPECIFIED,
			},
			wantCount: 4,
			wantErr:   false,
		},
		{
			name: "get retry logs with only auth id filter fixture 2",
			args: args{
				ctx:              context.Background(),
				authId:           testAuthFactorStateFixture2.AuthId,
				authFactor:       caPb.AuthFactor_AUTH_FACTOR_UNSPECIFIED,
				authFactorStatus: caPb.AuthFactorStatus_AUTH_FACTOR_STATUS_UNSPECIFIED,
			},
			wantCount: 3,
			wantErr:   false,
		},
		{
			name: "get retry logs with auth id and auth factor filter",
			args: args{
				ctx:              context.Background(),
				authId:           testAuthFactorStateFixture1.AuthId,
				authFactor:       caPb.AuthFactor_DOB,
				authFactorStatus: caPb.AuthFactorStatus_AUTH_FACTOR_STATUS_UNSPECIFIED,
			},
			wantCount: 2,
			wantErr:   false,
		},
		{
			name: "get retry logs with auth id and auth factor status filter",
			args: args{
				ctx:              context.Background(),
				authId:           testAuthFactorStateFixture1.AuthId,
				authFactorStatus: caPb.AuthFactorStatus_VERIFICATION_SUCCESS,
			},
			wantCount: 2,
			wantErr:   false,
		},
		{
			name: "get retry logs with auth id, auth factor and auth factor status filter",
			args: args{
				ctx:              context.Background(),
				authId:           testAuthFactorStateFixture1.AuthId,
				authFactor:       caPb.AuthFactor_DOB,
				authFactorStatus: caPb.AuthFactorStatus_VERIFICATION_SUCCESS,
			},
			wantCount: 1,
			wantErr:   false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, authFactorRetryLogsTestSuite.db, authFactorRetryLogsTestSuite.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := authFactorRetryLogsTestSuite.RetryLog.GetAllAuthFactorsAudits(tt.args.ctx, tt.args.authId, tt.args.authFactor, tt.args.authFactorStatus)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllAuthFactorsStates() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equal(t, tt.wantCount, len(got))
		})
	}
}

func isRetryLogsDeepEqual(actual *model.CustomerAuthenticationFactorsRetryLogs, expected *model.CustomerAuthenticationFactorsRetryLogs) bool {
	if actual != nil && expected != nil {
		expected.CreatedAt = actual.CreatedAt
		expected.UpdatedAt = actual.UpdatedAt
	}
	return reflect.DeepEqual(actual, expected)
}
