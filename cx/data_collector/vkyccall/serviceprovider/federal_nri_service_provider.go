package serviceprovider

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	"go.uber.org/zap"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"

	cxvkyccallpb "github.com/epifi/gamma/api/cx/data_collector/vkyccall"
	types "github.com/epifi/gamma/api/typesv2"
	typespb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/webui"
	vgNcPb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	vkyccalltypes "github.com/epifi/gamma/api/vkyccall/types"
	"github.com/epifi/gamma/pkg/address"
)

const dateLayout = "02 Jan 2006"

const (
	userDetailsHeaderKey       string = "user_details"
	applicantFormDataHeaderKey string = "applicant_form_data"
	panDataHeaderKey           string = "pan_data"
	passportDataHeaderKey      string = "passport_data"
	emiratesIDDataHeaderKey    string = "emirates_id_data"
	qatarIDDataHeaderKey       string = "qatar_id_data"
	matchScoreHeaderKey        string = "match_score"
	ckycDocHeaderKey           string = "ckyc_doc_data"
)

type FederalNRIServiceProvider struct {
	vgNameCheckClient vgNcPb.UNNameCheckClient
}

func NewFederalNRIServiceProvider(vgNameCheckClient vgNcPb.UNNameCheckClient) *FederalNRIServiceProvider {
	return &FederalNRIServiceProvider{
		vgNameCheckClient: vgNameCheckClient,
	}
}

var _ ServiceProvider = &FederalNRIServiceProvider{}

// nolint:funlen
func (f *FederalNRIServiceProvider) GetCXReport(_ context.Context, req *GetCXReportRequest) (*GetCXReportResponse, error) {
	beReport := req.BeReport
	formattedUserDetails, err := f.getUserDetailsTable(beReport)
	if err != nil {
		return nil, fmt.Errorf("error in getting formatted user details: %w", err)
	}
	// TODO(verifi) : Cleanup deprecated fields
	return &GetCXReportResponse{
		CXReport: &cxvkyccallpb.Report{
			UserDetails:      formattedUserDetails,
			UserImageDetails: beReport.GetUserImageDetails(),
			QuestionAnswers:  beReport.GetQuestionAnswers(),
			LocationCheckReport: &cxvkyccallpb.LocationCheckReport{
				FormattedAddress:         address.ConvertPostalAddressToString(beReport.GetLocationCheckReport().GetAddress()),
				IpAddress:                beReport.GetLocationCheckReport().GetIpAddress(),
				Distance:                 getDistanceString(beReport.GetLocationCheckReport().GetIsDistanceWithinRange(), beReport.GetLocationCheckReport().GetDistanceInKm()),
				FormattedLocationDetails: beReport.GetLocationCheckReport().GetLocationDetails(),
			},
			BrowserAndIpDetails: &cxvkyccallpb.BrowserAndIPDetails{
				InternetServiceProvider: beReport.GetBrowserAndIpDetails().GetInternetServiceProvider(),
				IpCountryCode:           beReport.GetBrowserAndIpDetails().GetIpCountryCode(),
			},
			MeetingId:    beReport.GetMeetingId(),
			OmegleCallId: beReport.GetOmegleCallId(),
			AgentRemarks: beReport.GetAgentRemarks(),
			FaceMatchResults: &cxvkyccallpb.FaceMatchResults{
				ImageMatchResults: []*cxvkyccallpb.FaceMatchResults_ImageMatchResult{
					{
						ReferenceImageDetails:      beReport.GetUserImageDetails().GetPanUserImageUrl(),
						InputImageDetails:          beReport.GetUserImageDetails().GetCapturedUserImageUrl(),
						MatchScorePercent:          beReport.GetPanDocumentResults().GetFaceMatchResult().GetMatchScorePercent(),
						ReferenceImageDocumentType: typespb.DocumentType_DOCUMENT_TYPE_PAN,
						Title:                      "Face match with PAN",
					},
					{
						ReferenceImageDetails:      beReport.GetUserImageDetails().GetPassportUserImageUrl(),
						InputImageDetails:          beReport.GetUserImageDetails().GetCapturedUserImageUrl(),
						MatchScorePercent:          beReport.GetPassportDocumentResults().GetFaceMatchResult().GetMatchScorePercent(),
						ReferenceImageDocumentType: typespb.DocumentType_DOCUMENT_TYPE_PASSPORT,
						Title:                      "Face match with Passport",
					},
					{
						ReferenceImageDetails:      beReport.GetUserImageDetails().GetEmiratesIdUserImageUrl(),
						InputImageDetails:          beReport.GetUserImageDetails().GetCapturedUserImageUrl(),
						MatchScorePercent:          beReport.GetEmiratesIdDocumentResults().GetFaceMatchResult().GetMatchScorePercent(),
						ReferenceImageDocumentType: typespb.DocumentType_DOCUMENT_TYPE_EMIRATES_ID,
						Title:                      "Face match with Emirates ID",
					},
				},
			},
			AddressMatchResult: &cxvkyccallpb.AddressMatchResult{
				Title:                      "Address Match with Passport",
				ReferenceImageDetails:      beReport.GetUserImageDetails().GetPassportRearImageUrl(),
				ReferenceImageDocumentType: typespb.DocumentType_DOCUMENT_TYPE_PASSPORT_BACK,
				PermanentAddress:           address.ConvertPostalAddressToString(beReport.GetApplicantDetails().GetAddressDetails().GetPermanentAddress()),
			},
			VerifiedDocumentDetails: []*cxvkyccallpb.VerifiedDocumentDetails{
				{
					DocumentType: typespb.DocumentType_DOCUMENT_TYPE_EMIRATES_ID,
					Title:        "Emirates ID",
					FrontImage:   beReport.GetUserImageDetails().GetEmiratesIdFrontImageUrl(),
					BackImage:    beReport.GetUserImageDetails().GetEmiratesIdBackImageUrl(),
				},
			},
		},
	}, nil
}

func (f *FederalNRIServiceProvider) GetPassportOcrDetailsMatchResult(ctx context.Context, req *GetPassportOcrDetailsMatchResultRequest) (*GetPassportOcrDetailsMatchResultResponse, error) {
	ocrDetailsMatchResults := &webui.Table{
		TableHeaders: []*webui.TableHeader{
			{
				Label:     "User Details",
				HeaderKey: userDetailsHeaderKey,
				IsVisible: true,
			},
			{
				Label:     "Applicant form data",
				HeaderKey: applicantFormDataHeaderKey,
				IsVisible: true,
			},
			{
				// OCR data
				Label:     "Passport Data",
				HeaderKey: passportDataHeaderKey,
				IsVisible: true,
			},
			{
				Label:     "Emirates Data",
				HeaderKey: emiratesIDDataHeaderKey,
				IsVisible: true,
			},
			{
				Label:     "Match Score",
				HeaderKey: "match_score",
				IsVisible: true,
			},
		},
		TableRows: []*webui.TableRow{},
		TableName: "Match Results",
	}
	passportDetails := req.GetApplicantDocumentDetails().GetPassportDetails()
	matchResult := req.GetPassportMatchResult()
	emiratesIdDetails, err := getEmiratesIDDetails(req.GetBeReport().GetApplicantDetails().GetDocumentDetails())
	if err != nil {
		logger.Error(ctx, "emirates ID details not found", zap.Error(err))
		return nil, err
	}

	nameMatchScore := productOf2Percentages(float64(matchResult.GetName().GetMatchScorePercent()),
		f.nameMatch(ctx, passportDetails.GetName().ToString(), emiratesIdDetails.GetName().ToString())*100)
	ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), getOcrTableDataRow(map[string]string{
		userDetailsHeaderKey:       "Name",
		applicantFormDataHeaderKey: passportDetails.GetName().ToString(),
		passportDataHeaderKey:      req.GetExtractedPassportDetails().GetPassportDetails().GetName().ToString(),
		emiratesIDDataHeaderKey:    emiratesIdDetails.GetName().ToString(),
		matchScoreHeaderKey:        strconv.FormatFloat(nameMatchScore, 'f', 1, 64) + "%",
	}))

	passportPanDobScore := 0
	if datetime.DateEquals(typespb.GetBeDate(passportDetails.GetDateOfBirth()), typespb.GetBeDate(emiratesIdDetails.GetDateOfBirth())) {
		passportPanDobScore = 100
	}
	dobMatchScore := productOf2Percentages(float64(matchResult.GetDateOfBirth().GetMatchScorePercent()), float64(passportPanDobScore))
	ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), getOcrTableDataRow(map[string]string{
		userDetailsHeaderKey:       "Date of Birth",
		applicantFormDataHeaderKey: datetime.DateToDDMMYYYY(typespb.GetBeDate(passportDetails.GetDateOfBirth())),
		passportDataHeaderKey:      datetime.DateToDDMMYYYY(typespb.GetBeDate(req.GetExtractedPassportDetails().GetPassportDetails().GetDateOfBirth())),
		emiratesIDDataHeaderKey:    datetime.DateToDDMMYYYY(typespb.GetBeDate(emiratesIdDetails.GetDateOfBirth())),
		matchScoreHeaderKey:        strconv.FormatFloat(dobMatchScore, 'f', 2, 64) + "%",
	}))

	ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), getOcrTableDataRow(map[string]string{
		userDetailsHeaderKey:       "Passport Number",
		applicantFormDataHeaderKey: passportDetails.GetPassportNumber(),
		passportDataHeaderKey:      req.GetExtractedPassportDetails().GetPassportDetails().GetPassportNumber(),
		emiratesIDDataHeaderKey:    "",
		matchScoreHeaderKey:        strconv.FormatFloat(float64(matchResult.GetPassportNumber().GetMatchScorePercent()), 'f', 2, 64) + "%",
	}))

	ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), getOcrTableDataRow(map[string]string{
		userDetailsHeaderKey:       "Passport expiry date",
		applicantFormDataHeaderKey: datetime.DateToDDMMYYYY(typespb.GetBeDate(passportDetails.GetDateOfExpiry())),
		passportDataHeaderKey:      datetime.DateToDDMMYYYY(typespb.GetBeDate(req.GetExtractedPassportDetails().GetPassportDetails().GetDateOfExpiry())),
		emiratesIDDataHeaderKey:    "",
		matchScoreHeaderKey:        strconv.FormatFloat(float64(matchResult.GetDateOfExpiry().GetMatchScorePercent()), 'f', 2, 64) + "%",
	}))
	ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), getOcrTableDataRow(map[string]string{
		userDetailsHeaderKey:       "EmiratesID",
		applicantFormDataHeaderKey: emiratesIdDetails.GetId(),
		passportDataHeaderKey:      "",
		emiratesIDDataHeaderKey:    emiratesIdDetails.GetId(),
		matchScoreHeaderKey:        "100%",
	}))
	ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), getOcrTableDataRow(map[string]string{
		userDetailsHeaderKey:       "Emirates ID expiry date",
		applicantFormDataHeaderKey: datetime.DateToDDMMYYYY(typespb.GetBeDate(emiratesIdDetails.GetDateOfExpiry())),
		passportDataHeaderKey:      "",
		emiratesIDDataHeaderKey:    datetime.DateToDDMMYYYY(typespb.GetBeDate(emiratesIdDetails.GetDateOfExpiry())),
		matchScoreHeaderKey:        "100%",
	}))

	return &GetPassportOcrDetailsMatchResultResponse{
		OcrDetailsMatchResult: ocrDetailsMatchResults,
	}, nil
}

func productOf3Percentages(a, b, c float32) float64 {
	return (float64(a) * float64(b) * float64(c)) / 10000
}

func productOfNPercentages(list ...float32) float64 {
	res := float64(100)
	for _, v := range list {
		res = (res * float64(v)) / 100
	}
	return res
}

// nolint: funlen
func (f *FederalNRIServiceProvider) getUserDetailsTable(beReport *vkyccalltypes.Report) (*webui.Table, error) {

	applicantPanDetails, panErr := getPanCardDetails(beReport.GetApplicantDetails().GetDocumentDetails())
	if panErr != nil {
		return nil, panErr
	}

	applicantPassportDetails, passportErr := getPassportDetails(beReport.GetApplicantDetails().GetDocumentDetails())
	if passportErr != nil {
		return nil, passportErr
	}

	applicantEmiratesIdDetails, emiratesIdErr := getEmiratesIDDetails(beReport.GetApplicantDetails().GetDocumentDetails())
	if emiratesIdErr != nil {
		return nil, emiratesIdErr
	}

	panDetails := getPanDocumentData(beReport.GetPanDocumentResults())

	if panDetails == nil {
		return nil, errors.New("pan details not found in the fetched documents")
	}

	nameRow := f.getUserDetailsTableRow("NAME",
		beReport.GetApplicantDetails().GetApplicantName().ToString(),
		panDetails.Name.ToString(),
		beReport.GetPassportDocumentResults().GetOcrDocumentDetails().GetPassportDetails().GetName().ToString(),
		beReport.GetEmiratesIdDocumentResults().GetOcrDocumentDetails().GetEmiratesIdDetails().GetName().ToString(),
		strconv.FormatFloat(
			productOf3Percentages(
				beReport.GetPanDocumentResults().GetPanMatchResult().GetName().GetMatchScorePercent(),
				beReport.GetPassportDocumentResults().GetPassportMatchResult().GetName().GetMatchScorePercent(),
				beReport.GetEmiratesIdDocumentResults().GetEmiratesIdMatchResult().GetName().GetMatchScorePercent(),
			),
			'f', 1, 64)+"%",
	)

	dobRow := f.getUserDetailsTableRow("DATE OF BIRTH",
		datetime.DateToString(beReport.GetApplicantDetails().GetDob(), dateLayout, nil),
		datetime.DateToString(panDetails.DateOfBirth, dateLayout, nil),
		datetime.DateToString(types.GetBeDate(beReport.GetPassportDocumentResults().GetOcrDocumentDetails().GetPassportDetails().GetDateOfBirth()), dateLayout, nil),
		datetime.DateToString(types.GetBeDate(beReport.GetEmiratesIdDocumentResults().GetOcrDocumentDetails().GetEmiratesIdDetails().GetDob()), dateLayout, nil),
		strconv.FormatFloat(
			productOf3Percentages(
				beReport.GetPanDocumentResults().GetPanMatchResult().GetDateOfBirth().GetMatchScorePercent(),
				beReport.GetPassportDocumentResults().GetPassportMatchResult().GetDateOfBirth().GetMatchScorePercent(),
				beReport.GetEmiratesIdDocumentResults().GetEmiratesIdMatchResult().GetDateOfBirth().GetMatchScorePercent(),
			),
			'f', 1, 64)+"%",
	)
	parentsNameRow := f.getUserDetailsTableRow("PARENT'S NAME",
		beReport.GetApplicantDetails().GetGuardianDetails().GetFatherName().ToString()+","+
			beReport.GetApplicantDetails().GetGuardianDetails().GetMotherName().ToString(),
		panDetails.GuardianName.ToString(),
		"-",
		"-",
		strconv.FormatFloat(
			float64(beReport.GetPanDocumentResults().GetPanMatchResult().GetParentName().GetMatchScorePercent()),
			'f', 1, 64)+"%",
	)

	panNumRow := f.getUserDetailsTableRow("PAN NUMBER",
		applicantPanDetails.GetId(),
		panDetails.PanNumber,
		"-",
		"-",
		strconv.FormatFloat(
			float64(beReport.GetPanDocumentResults().GetPanMatchResult().GetPanNumber().GetMatchScorePercent()),
			'f', 1, 64)+"%",
	)

	passportNumRow := f.getUserDetailsTableRow("PASSPORT NUMBER",
		applicantPassportDetails.GetPassportNumber(),
		"-",
		beReport.GetPassportDocumentResults().GetOcrDocumentDetails().GetPassportDetails().GetPassportNumber(),
		"-",
		strconv.FormatFloat(
			float64(beReport.GetPassportDocumentResults().GetPassportMatchResult().GetPassportNumber().GetMatchScorePercent()),
			'f', 1, 64)+"%",
	)

	emiratesIdRow := f.getUserDetailsTableRow("EMIRATES ID",
		applicantEmiratesIdDetails.GetId(),
		"-",
		"-",
		beReport.GetEmiratesIdDocumentResults().GetOcrDocumentDetails().GetEmiratesIdDetails().GetIdentityNumber(),
		strconv.FormatFloat(
			float64(beReport.GetEmiratesIdDocumentResults().GetEmiratesIdMatchResult().GetId().GetMatchScorePercent()),
			'f', 1, 64)+"%",
	)

	t := &webui.Table{
		TableRows: []*webui.TableRow{nameRow, dobRow, parentsNameRow, panNumRow, passportNumRow, emiratesIdRow},
		TableHeaders: []*webui.TableHeader{
			{
				Label:     "User Details",
				HeaderKey: userDetailsHeaderKey,
				IsVisible: true,
			},
			{
				Label:     "Applicant form data",
				HeaderKey: applicantFormDataHeaderKey,
				IsVisible: true,
			},
			{
				// Filled inside the type switch
				Label:     string(panDetails.PanType) + " data",
				HeaderKey: panDataHeaderKey,
				IsVisible: true,
			},
			{
				// Filled inside the type switch
				Label:     "Passport data",
				HeaderKey: passportDataHeaderKey,
				IsVisible: true,
			},
			{
				// Filled inside the type switch
				Label:     "Emirates ID data",
				HeaderKey: emiratesIDDataHeaderKey,
				IsVisible: true,
			},
			{
				Label:     "Match Score",
				HeaderKey: matchScoreHeaderKey,
				IsVisible: true,
			},
		},
	}

	return t, nil
}

func (f *FederalNRIServiceProvider) getUserDetailsTableRow(userDetails, applicationData, panData, passportData, emiratesIDData, matchScore string) *webui.TableRow {
	return &webui.TableRow{
		HeaderKeyCellMap: map[string]*webui.TableCell{
			userDetailsHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: userDetails},
			},
			applicantFormDataHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: applicationData},
			},
			panDataHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: panData},
			},
			passportDataHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: passportData},
			},
			emiratesIDDataHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: emiratesIDData},
			},
			matchScoreHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: matchScore},
			},
		},
	}
}

func getPanCardDetails(documentDetails []*types.DocumentDetails) (*types.PanDocumentDetails, error) {
	for _, documentDetail := range documentDetails {
		if documentDetail.GetDocumentType() == types.DocumentType_DOCUMENT_TYPE_PAN {
			return documentDetail.GetPanDetails(), nil
		}
	}
	return nil, errors.New("no pan details found in the fetched documents")
}

func getPassportDetails(documentDetails []*types.DocumentDetails) (*types.PassportData, error) {
	for _, documentDetail := range documentDetails {
		if documentDetail.GetDocumentType() == types.DocumentType_PASSPORT {
			return documentDetail.GetPassportDetails(), nil
		}
	}
	return nil, errors.New("no passport details found in the fetched documents")
}

func getEmiratesIDDetails(documentDetails []*types.DocumentDetails) (*types.CountryIdDetails, error) {
	for _, documentDetail := range documentDetails {
		if documentDetail.GetDocumentType() == types.DocumentType_DOCUMENT_TYPE_COUNTRY_ID || documentDetail.GetDocumentType() == types.DocumentType_DOCUMENT_TYPE_EMIRATES_ID {
			return documentDetail.GetCountryIdDetails(), nil
		}
	}
	return nil, errors.New("no country ID details found in the fetched documents")
}

func getDistanceString(doesMatch bool, distance float64) string {
	if doesMatch {
		return "< " + strconv.FormatFloat(distance, 'f', 0, 64) + " KM"
	}
	return "> " + strconv.FormatFloat(distance, 'f', 0, 64) + " KM"
}

// The method checks for the presence of ePAN document details in the PANDocumentResult. If available,
// it returns the ePAN details. Otherwise, it falls back to returning OCR-based PAN document details.
func getPanDocumentData(panDocumentResult *vkyccalltypes.PANDocumentResult) *PanDetails {
	if panDocumentResult.GetEpanDocumentDetails() != nil {
		return &PanDetails{
			Name:         panDocumentResult.GetEpanDocumentDetails().GetEpanDetails().GetName(),
			DateOfBirth:  panDocumentResult.GetEpanDocumentDetails().GetEpanDetails().GetDob(),
			GuardianName: panDocumentResult.GetEpanDocumentDetails().GetEpanDetails().GetGuardianInfo().GetGuardianName(),
			PanNumber:    panDocumentResult.GetEpanDocumentDetails().GetEpanDetails().GetPanNumber(),
			PanType:      EPAN,
		}
	}
	return &PanDetails{
		Name:         panDocumentResult.GetOcrDocumentDetails().GetPanDetails().GetName(),
		DateOfBirth:  panDocumentResult.GetOcrDocumentDetails().GetPanDetails().GetDateOfBirth(),
		GuardianName: panDocumentResult.GetOcrDocumentDetails().GetPanDetails().GetGuardianInfo().GetGuardianName(),
		PanNumber:    panDocumentResult.GetOcrDocumentDetails().GetPanDetails().GetId(),
		PanType:      PHYSICAL_PAN,
	}
}

func (f *FederalNRIServiceProvider) nameMatch(ctx context.Context, name1 string, name2 string) float64 {
	nameMatchRes, err := f.vgNameCheckClient.NameMatch(ctx, &vgNcPb.NameMatchRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_IN_HOUSE,
		},
		Name_1: name1,
		Name_2: name2,
	})
	if rpcErr := epifigrpc.RPCError(nameMatchRes, err); rpcErr != nil {
		logger.Error(ctx, "error in name match", zap.Error(rpcErr))
		return 0
	}
	return float64(nameMatchRes.GetScore())
}

func productOf2Percentages(a, b float64) float64 {
	return (a * b) / 100
}
