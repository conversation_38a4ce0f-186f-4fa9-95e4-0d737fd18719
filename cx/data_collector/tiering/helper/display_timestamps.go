package helper

import (
	"time"

	"github.com/pkg/errors"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	ISTTimeZoneLocation = "Asia/Kolkata"
)

// ConvertTimestampToISOString converts timestamp to IST display string to show in sherlock
func ConvertTimestampToISOString(ts *timestampPb.Timestamp) (string, error) {
	timeZoneLocation, conversionErr := time.LoadLocation(ISTTimeZoneLocation)
	if conversionErr != nil {
		return "", errors.Wrap(conversionErr, "error loading IST time location")
	}
	return ts.AsTime().In(timeZoneLocation).Format("Jan 02 2006 15:04:05"), nil
}
