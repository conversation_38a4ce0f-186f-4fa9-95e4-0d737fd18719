package fetch_data_processor

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"go.uber.org/zap"

	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	chatbotWorkflowPb "github.com/epifi/gamma/api/cx/chat/bot/workflow"
	kycPb "github.com/epifi/gamma/api/kyc"
	salaryProgramPb "github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type FetchSalaryProgramRegistrationDetailsProcessor struct {
	salaryProgramClient       salaryProgramPb.SalaryProgramClient
	bankCustomerServiceClient bankCustomerPb.BankCustomerServiceClient
}

func NewFetchSalaryProgramRegistrationDetailsProcessor(
	salaryProgramClient salaryProgramPb.SalaryProgramClient,
	bankCustomerServiceClient bankCustomerPb.BankCustomerServiceClient,
) *FetchSalaryProgramRegistrationDetailsProcessor {
	return &FetchSalaryProgramRegistrationDetailsProcessor{
		salaryProgramClient:       salaryProgramClient,
		bankCustomerServiceClient: bankCustomerServiceClient,
	}
}

const (
	True  = "true"
	False = "false"
)

// nolint: funlen
func (f *FetchSalaryProgramRegistrationDetailsProcessor) FetchData(ctx context.Context, workflowEntity chatbotWorkflowPb.WorkflowEntity, params *chatbotWorkflowPb.FetchDataParameters) (*chatbotWorkflowPb.WorkflowData, error) {

	if err := f.validateWorkflowEntityAndFetchDataParameters(workflowEntity, params); err != nil {
		logger.Error(ctx, "invalid parameters in WORKFLOW_ENTITY_FETCH_SALARY_PROGRAM_REGISTRATION_DETAILS", zap.Error(err), zap.String(logger.ACTOR_ID_V2, params.GetFetchSalaryProgramRegistrationDetailsParameters().GetActorId()))
		return nil, fmt.Errorf("invalid parameters in WORKFLOW_ENTITY_FETCH_SALARY_PROGRAM_REGISTRATION_DETAILS")
	}

	res := &chatbotWorkflowPb.FetchSalaryProgramRegistrationDetailsData{}

	registrationDetailsRes, err := f.salaryProgramClient.GetRegistrationDetails(ctx, &salaryProgramPb.GetRegistrationDetailsRequest{
		ActorId:  params.GetFetchSalaryProgramRegistrationDetailsParameters().GetActorId(),
		FlowType: salaryProgramPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if e := epifigrpc.RPCError(registrationDetailsRes, err); e != nil {
		logger.Error(ctx, "error in GetRegistrationDetails rpc", zap.Error(e))
		return nil, fmt.Errorf("error in GetRegistrationDetails rpc")
	}

	getBankCustomerRes, err := f.bankCustomerServiceClient.GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{
			ActorId: params.GetFetchSalaryProgramRegistrationDetailsParameters().GetActorId(),
		},
	})
	if e := epifigrpc.RPCError(getBankCustomerRes, err); e != nil {
		logger.Error(ctx, "error in GetBankCustomer rpc", zap.Error(e))
		return nil, fmt.Errorf("error in GetBankCustomer rpc")
	}

	// if user has not started salary program registration
	if registrationDetailsRes.GetRegistrationStatus() == salaryProgramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_NOT_INITIATED {
		res.HasUserCompletedEmploymentConfirmation = False

		kycCompletionStatus, getRegistrationStageStatusFromKycLevelErr := f.getRegistrationStageStatusFromKycLevel(ctx, getBankCustomerRes.GetBankCustomer().GetKycInfo().GetKycLevel())
		if getRegistrationStageStatusFromKycLevelErr != nil {
			return nil, getRegistrationStageStatusFromKycLevelErr
		}
		res.HasUserCompletedFullKyc = kycCompletionStatus

		return &chatbotWorkflowPb.WorkflowData{
			Data: &chatbotWorkflowPb.WorkflowData_FetchSalaryProgramRegistrationDetailsData{
				FetchSalaryProgramRegistrationDetailsData: res,
			},
		}, nil
	}

	employmentConfirmationRegStageDetailsRes, err := f.salaryProgramClient.GetRegistrationStageDetails(ctx, &salaryProgramPb.GetRegistrationStageDetailsRequest{
		RegistrationId: registrationDetailsRes.GetRegistrationId(),
		StageName:      salaryProgramPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_EMPLOYMENT_CONFIRMATION,
	})
	if e := epifigrpc.RPCError(employmentConfirmationRegStageDetailsRes, err); e != nil {
		logger.Error(ctx, "error in GetRegistrationStageDetails rpc", zap.Error(e), zap.String(logger.STAGE, salaryProgramPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_EMPLOYMENT_CONFIRMATION.String()))
		return nil, fmt.Errorf("error in GetRegistrationStageDetails rpc")
	}

	switch employmentConfirmationRegStageDetailsRes.GetStageStatus() {
	case salaryProgramPb.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_NOT_INITIATED, salaryProgramPb.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_INITIATED:
		res.HasUserCompletedEmploymentConfirmation = False
	case salaryProgramPb.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_COMPLETED:
		res.HasUserCompletedEmploymentConfirmation = True
	default:
		logger.Error(ctx, "unsupported salary program registration stage status", zap.String("StageStatus", employmentConfirmationRegStageDetailsRes.GetStageStatus().String()))
		return nil, fmt.Errorf("unsupported salary program registration stage status")
	}

	registrationStageDetailsForKycCompletionRes, err := f.salaryProgramClient.GetRegistrationStageDetails(ctx, &salaryProgramPb.GetRegistrationStageDetailsRequest{
		RegistrationId: registrationDetailsRes.GetRegistrationId(),
		StageName:      salaryProgramPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_FULL_KYC_COMPLETION,
	})
	if e := epifigrpc.RPCError(registrationStageDetailsForKycCompletionRes, err); e != nil {
		logger.Error(ctx, "error in GetRegistrationStageDetails rpc", zap.Error(e), zap.String(logger.STAGE, salaryProgramPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_FULL_KYC_COMPLETION.String()))
		return nil, fmt.Errorf("error in GetRegistrationStageDetails rpc")
	}

	switch registrationStageDetailsForKycCompletionRes.GetStageStatus() {
	case salaryProgramPb.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_NOT_INITIATED:
		kycStageStatus, getRegistrationStageStatusFromKycLevelErr := f.getRegistrationStageStatusFromKycLevel(ctx, getBankCustomerRes.GetBankCustomer().GetKycInfo().GetKycLevel())
		if getRegistrationStageStatusFromKycLevelErr != nil {
			return nil, getRegistrationStageStatusFromKycLevelErr
		}
		res.HasUserCompletedFullKyc = kycStageStatus
	case salaryProgramPb.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_INITIATED:
		res.HasUserCompletedFullKyc = False
	case salaryProgramPb.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_COMPLETED:
		res.HasUserCompletedFullKyc = True
	default:
		logger.Error(ctx, "unsupported salary program registration stage status", zap.String("StageStatus", employmentConfirmationRegStageDetailsRes.GetStageStatus().String()))
		return nil, fmt.Errorf("unsupported salary program registration stage status")
	}

	return &chatbotWorkflowPb.WorkflowData{
		Data: &chatbotWorkflowPb.WorkflowData_FetchSalaryProgramRegistrationDetailsData{
			FetchSalaryProgramRegistrationDetailsData: res,
		},
	}, nil
}

func (f *FetchSalaryProgramRegistrationDetailsProcessor) getRegistrationStageStatusFromKycLevel(ctx context.Context, kycLevel kycPb.KYCLevel) (string, error) {
	switch kycLevel {
	case kycPb.KYCLevel_FULL_KYC:
		return True, nil
	case kycPb.KYCLevel_MIN_KYC:
		return False, nil
	default:
		logger.Error(ctx, "got unhandled kyc level", zap.String("kycLevel", kycLevel.String()))
		return False, fmt.Errorf("got unhandled kyc level")
	}
}

func (f *FetchSalaryProgramRegistrationDetailsProcessor) validateWorkflowEntityAndFetchDataParameters(workflowEntity chatbotWorkflowPb.WorkflowEntity, params *chatbotWorkflowPb.FetchDataParameters) error {

	if workflowEntity != chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_FETCH_SALARY_PROGRAM_REGISTRATION_DETAILS {
		return fmt.Errorf("wrong workflowEntity should be WORKFLOW_ENTITY_FETCH_SALARY_PROGRAM_REGISTRATION_DETAILS")
	}
	if params.GetFetchSalaryProgramRegistrationDetailsParameters().GetActorId() == "" {
		return fmt.Errorf("empty actorId not allowed")
	}
	return nil

}
