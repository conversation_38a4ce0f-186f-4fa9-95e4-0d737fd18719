package activity

import (
	"context"
	"time"

	watsonInternal "github.com/epifi/gamma/cx/internal/watson"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	cxActivityPb "github.com/epifi/gamma/api/cx/activity"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	watsonCollector "github.com/epifi/gamma/cx/watson/collector"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/durationpb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
)

// GetWatsonConfig - activity wrapper to read Watson config from the worker config
func (p *Processor) GetWatsonConfig(ctx context.Context, req *cxActivityPb.GetWatsonConfigRequest) (*cxActivityPb.GetWatsonConfigResponse, error) {
	lg := activity.GetLogger(ctx)
	if p.conf == nil || p.conf.WatsonConfig == nil {
		lg.Error("failed to read Watson config", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, epifitemporal.NewPermanentError(errors.New("failed to read Watson config"))
	}
	incidentResolutionWaitDurationConf := p.conf.WatsonConfig.IncidentResolutionWaitDuration
	incidentResolutionWaitDuration := durationpb.New(incidentResolutionWaitDurationConf)
	if !incidentResolutionWaitDuration.IsValid() {
		lg.Error("invalid duration in WatsonConfig for IncidentResolutionWaitDuration", zap.Duration("IncidentResolutionWaitDuration", incidentResolutionWaitDurationConf),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, epifitemporal.NewPermanentError(errors.New("invalid duration in WatsonConfig for IncidentResolutionWaitDuration"))
	}
	incident := req.GetIncident()
	activityHelper := p.watsonActivityHelperFactory.GetActivityHelper(incident)
	// Fetch incident category config for given incident based on incident category Id
	incidentCategoryConfig, getConfigErr := activityHelper.GetWatsonIncidentCategoryConfig(ctx, incident.GetIncidentCategoryId())
	if getConfigErr != nil || incidentCategoryConfig == nil {
		lg.Error("error while fetching incident category config", zap.String(logger.INCIDENT_CATEGORY_ID, incident.GetIncidentCategoryId()), zap.Error(getConfigErr))
		return nil, epifitemporal.NewPermanentError(errors.Wrap(getConfigErr, "error while fetching incident category config"))
	}
	lg.Debug("successfully read watson config", zap.Any("IncidentResolutionWaitDuration", incidentResolutionWaitDuration),
		zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))

	definedDuration := durationpb.New(incidentCategoryConfig.AutoClosurePeriod)
	if incidentCategoryConfig.AutoClosurePeriod != 0 && definedDuration.IsValid() {
		incidentResolutionWaitDuration = definedDuration
	}
	ticketCreationDelay := durationpb.New(incidentCategoryConfig.TicketCreationDelay)
	creationCommsDelay := durationpb.New(incidentCategoryConfig.CommsDelay)
	return &cxActivityPb.GetWatsonConfigResponse{
		IncidentResolutionWaitDuration: incidentResolutionWaitDuration,
		TicketStatusCommsConfigMap:     incidentCategoryConfig.TicketStatusCommsConfigMap,
		TicketCreationDelay:            ticketCreationDelay,
		PostTicketCreationCommsDelay:   creationCommsDelay,
	}, nil
}

// ValidateAndCreateWatsonIncident - activity to validate an incident reported to Watson service and create a db entry for the same
func (p *Processor) ValidateAndCreateWatsonIncident(ctx context.Context, req *cxActivityPb.ValidateAndCreateWatsonIncidentRequest) (*cxActivityPb.ValidateAndCreateWatsonIncidentResponse, error) {
	lg := activity.GetLogger(ctx)
	clientService := req.GetIncident().GetClient()
	incident := req.GetIncident()
	_, err := p.checkIfIncidentIsStillValid(ctx, incident)
	if err != nil {
		lg.Error("incident validity check failed for creating incident", zap.String(logger.CLIENT, clientService.String()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, err
	}

	// updating incident state before logging in db
	incident.IncidentState = watsonPb.IncidentState_INCIDENT_STATE_LOGGED_IN_DB
	// since the incident is valid, create an entry in the db
	createdIncident, err := p.watsonIncidentDao.Create(ctx, incident)
	if err != nil {
		lg.Error("failed to created watson incident entry in the db", zap.String(logger.SERVICE, clientService.String()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		if errors.Is(err, epifierrors.ErrAlreadyExists) {
			return nil, epifitemporal.NewPermanentError(errors.Wrap(err, "failed to created watson incident entry in the db - already exists"))
		}
		return nil, epifitemporal.NewTransientError(errors.Wrap(err, "failed to created watson incident entry in the db"))
	}
	lg.Debug("successfully created watson incident", zap.String(logger.WATSON_INCIDENT_ID, createdIncident.GetId()),
		zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
	return &cxActivityPb.ValidateAndCreateWatsonIncidentResponse{
		Incident: createdIncident,
	}, nil
}

// checks if the created incident is still valid by invoking the client service's implementation of watson collector
// returns WatsonIncidentInfoCollector implementation of the client service and error
func (p *Processor) checkIfIncidentIsStillValid(ctx context.Context, incident *watsonPb.Incident) (watsonCollector.WatsonIncidentInfoCollector, error) {
	clientService := incident.GetClient()

	clientCollectorImplementation, err := p.watsonIncidentInfoCollectorFactory.GetWatsonIncidentInfoCollector(clientService)
	if err != nil {
		return nil, epifitemporal.NewPermanentError(errors.Wrap(err, "error while getting collector for service"))
	}
	isIncidentValidResp, err := clientCollectorImplementation.IsIncidentValid(ctx, &watsonPb.IsIncidentValidRequest{Incident: watsonInternal.GetIncidentDetailsForClient(incident)})
	if te := epifigrpc.RPCError(isIncidentValidResp, err); te != nil {
		// TODO: differentiate between retryable and non-retryable errors here and return error accordingly
		return nil, epifitemporal.NewTransientError(errors.Wrap(te, "error while checking incident validity from client service"))
	}
	if !isIncidentValidResp.GetIsIncidentValid() {
		err := p.UpdateIncidentState(ctx, incident, watsonPb.IncidentState_INCIDENT_STATE_INVALID)
		if err != nil {
			// UpdateIncidentState wraps the error with permanent/ transient. Hence, not wrapping here.
			return nil, err
		}
		return nil, epifitemporal.NewPermanentError(errors.New("incident is invalid as reported by the client service"))
	}
	return clientCollectorImplementation, nil
}

// updating incident state in db
func (p *Processor) UpdateIncidentState(ctx context.Context, incident *watsonPb.Incident, incidentState watsonPb.IncidentState) error {
	incident.IncidentState = incidentState
	err := p.watsonIncidentDao.Update(ctx, incident, []watsonPb.IncidentMask{watsonPb.IncidentMask_INCIDENT_MASK_INCIDENT_STATE})
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "incident not found while updating the incident state in db", zap.String(logger.WATSON_INCIDENT_ID, incident.GetId()), zap.Any("incidentState", incident.GetIncidentState()))
		return epifitemporal.NewPermanentError(errors.Wrap(err, "incident not found in db while updating incident state"))
	}
	if err != nil {
		logger.Error(ctx, "error while updating the incident state in db", zap.String(logger.WATSON_INCIDENT_ID, incident.GetId()), zap.Any("incidentState", incident.GetIncidentState()))
		return epifitemporal.NewTransientError(errors.Wrap(err, "error while updating the incident state in db"))
	}
	return nil
}

// Sleeps for the given duration since the given timestamp
// i.e. it blocks execution until the time : givenTimestamp+delayRequiredSinceGivenTime
func sleepForDurationSinceGivenTime(givenTimestamp *timestampPb.Timestamp, delayRequiredSinceGivenTime time.Duration) error {
	// if givenTimestamp is not populated, it defaults to zero unix seconds (00:00:00 UTC on 1 January 1970)
	if !givenTimestamp.IsValid() || givenTimestamp.GetSeconds() == 0 {
		return epifitemporal.NewPermanentError(errors.New("given timestamp cannot be zero for sleepForDurationSinceGivenTime"))
	}
	// sleep function exits immediately if the duration is negative or zero. So no need of negative check
	time.Sleep(delayRequiredSinceGivenTime - time.Since(givenTimestamp.AsTime()))
	return nil
}

func getTemporalError(err error) error {
	switch {
	case errors.Is(err, watsonInternal.ErrClientNotRegistered):
		return epifitemporal.NewPermanentError(errors.Wrap(err, "permanent error: client not registered"))
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return epifitemporal.NewPermanentError(errors.Wrap(err, "permanent error: record not found"))
	case errors.Is(err, watsonInternal.ErrInvalidIncidentCategoryId):
		return epifitemporal.NewPermanentError(errors.Wrap(err, "permanent error: invalid incident category id"))
	case errors.Is(err, watsonInternal.ErrInvalidArguments):
		return epifitemporal.NewPermanentError(errors.Wrap(err, "permanent error: invalid arguments"))
	case errors.Is(err, watsonInternal.ErrFailedToCallClient):
		return epifitemporal.NewTransientError(errors.Wrap(err, "transient error: failed to call client"))
	case errors.Is(err, epifierrors.ErrAlreadyExists):
		return epifitemporal.NewPermanentError(errors.Wrap(err, "permanent error: record already exists"))
	case errors.Is(err, watsonInternal.ErrConfigNotFound):
		return epifitemporal.NewPermanentError(errors.Wrap(err, "permanent error: config not found"))
	case errors.Is(err, epifierrors.ErrMethodUnimplemented):
		return epifitemporal.NewPermanentError(errors.Wrap(err, "permanent error: method not implemented"))
	default:
		return epifitemporal.NewTransientError(errors.Wrap(err, "transient error: unhandled"))
	}
}
