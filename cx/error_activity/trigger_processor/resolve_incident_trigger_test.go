package trigger_processor

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	"github.com/epifi/gamma/api/cx/watson/mocks"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/cx/error_activity/entity"
	"github.com/epifi/be-common/pkg/epifierrors"
)

var (
	incident1 = &watsonPb.IncidentDetailsForClient{
		Client:			typesPb.ServiceName_CX_SERVICE,
		ActorId:		"ABC1",
		ClientRequestId:	"CRId1",
		IssueCategoryId:	"1234",
	}
)

func TestResolveIncidentTriggerProcessor_ProcessTrigger(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockWatsonClient := mocks.NewMockWatsonClient(ctr)

	type args struct {
		ctx		context.Context
		incident	*entity.IncidentDetails
	}
	tests := []struct {
		name	string
		args	args
		mocks	[]interface{}
		wantErr	error
	}{
		{
			name:	"no open incident found",
			args: args{
				ctx:		context.Background(),
				incident:	&entity.IncidentDetails{},
			},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).
					Return(&watsonPb.GetIncidentsForClientResponse{Status: rpc.StatusRecordNotFound()}, nil),
			},
			wantErr:	epifierrors.ErrRecordNotFound,
		},
		{
			name:	"status ok, but received empty list",
			args: args{
				ctx:		context.Background(),
				incident:	&entity.IncidentDetails{},
			},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).
					Return(&watsonPb.GetIncidentsForClientResponse{Status: rpc.StatusOk()}, nil),
			},
			wantErr:	epifierrors.ErrRecordNotFound,
		},
		{
			name:	"internal error while fetching incidents",
			args: args{
				ctx:		context.Background(),
				incident:	&entity.IncidentDetails{},
			},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).
					Return(&watsonPb.GetIncidentsForClientResponse{Status: rpc.StatusInternal()}, nil),
			},
			wantErr:	rpc.StatusAsError(rpc.StatusInternal()),
		},
		{
			name:	"error while calling RPC to resolve incident",
			args: args{
				ctx:		context.Background(),
				incident:	&entity.IncidentDetails{},
			},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).
					Return(&watsonPb.GetIncidentsForClientResponse{
						Status:		rpc.StatusOk(),
						Incidents:	[]*watsonPb.IncidentDetailsForClient{incident1}}, nil),
				mockWatsonClient.EXPECT().IngestEvent(gomock.Any(), gomock.Any()).Return(&watsonPb.IngestEventResponse{Status: rpc.StatusInternal()}, nil),
			},
			wantErr:	rpc.StatusAsError(rpc.StatusInternal()),
		},
		{
			name:	"success: incident resolution completed",
			args: args{
				ctx:	context.Background(),
				incident: &entity.IncidentDetails{
					ActorId:		"ABC",
					IssueCategoryId:	"123",
					ClientRequestId:	"1234",
				},
			},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).
					Return(&watsonPb.GetIncidentsForClientResponse{
						Status:		rpc.StatusOk(),
						Incidents:	[]*watsonPb.IncidentDetailsForClient{incident1}}, nil),
				mockWatsonClient.EXPECT().IngestEvent(gomock.Any(), gomock.Any()).Return(&watsonPb.IngestEventResponse{Status: rpc.StatusOk()}, nil),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := NewResolveIncidentTriggerProcessor(mockWatsonClient)
			if err := r.ProcessEventTrigger(tt.args.ctx, tt.args.incident); (err != nil) && !errors.Is(err, tt.wantErr) {
				t.Errorf("ProcessEventTrigger() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
