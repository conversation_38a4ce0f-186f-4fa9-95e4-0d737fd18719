package collector

import (
	"context"
	"flag"
	"os"
	"sort"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	casbinPb "github.com/epifi/gamma/api/casbin"
	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/test"
	mock_get_banners_helper "github.com/epifi/gamma/cx/test/mocks/sherlock_banners/helper"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, genConf, _, teardown := test.InitTestServer(true)

	csbcTS = CxSherlockBannerCollectorTestSuite{
		genConf: genConf,
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type CxSherlockBannerCollectorTestSuite struct {
	genConf *cxGenConf.Config
}

const (
	actorId1 = "actor-id-1"
)

var (
	csbcTS CxSherlockBannerCollectorTestSuite

	banner1 = &sbPb.SherlockBanner{
		Id:            "banner-id-1",
		BannerContent: &sbPb.SherlockBannerContent{Title: "t1", Body: "b1"},
		StartTime:     "2022-06-13T15:30:00",
		EndTime:       "2122-03-13T15:30:00",
		MappingItems: &sbPb.MappingItems{
			SherlockUserRoles: []casbinPb.AccessLevel{casbinPb.AccessLevel_AGENT, casbinPb.AccessLevel_FEDERAL_AGENT},
		},
	}
	getBannerReq1 = &sbPb.GetBannersRequest{
		ActiveNowFlag: true,
		Filters: &sbPb.BannerFilters{
			MappingItems: &sbPb.MappingItems{
				ActorIdList: []string{actorId1},
			},
			StructureTypeList: []sbPb.SherlockBannerStructureType{sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_LABEL_MESSAGE_LIST},
		},
		PageContextRequest: &rpcPb.PageContextRequest{
			PageSize: maxNumOfBanners,
		},
	}
)

func TestCxSherlockBannersCollector_FetchSherlockBanners(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockGetBannersHelper := mock_get_banners_helper.NewMockGetBannersHelper(ctr)

	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *sbPb.FetchSherlockBannersRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *sbPb.FetchSherlockBannersResponse
		wantErr bool
	}{
		{
			name: "failed: Record not found",
			args: args{
				ctx: context.Background(),
				req: &sbPb.FetchSherlockBannersRequest{
					ActorId:           actorId1,
					StructureTypeList: []sbPb.SherlockBannerStructureType{sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_LABEL_MESSAGE_LIST},
				},
				mocks: []interface{}{
					mockGetBannersHelper.EXPECT().GetBanners(context.Background(), getBannerReq1).Return(&sbPb.GetBannersResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil),
				},
			},
			want: &sbPb.FetchSherlockBannersResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "failed: internal error",
			args: args{
				ctx: context.Background(),
				req: &sbPb.FetchSherlockBannersRequest{
					ActorId:           actorId1,
					StructureTypeList: []sbPb.SherlockBannerStructureType{sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_LABEL_MESSAGE_LIST},
				},
				mocks: []interface{}{
					mockGetBannersHelper.EXPECT().GetBanners(context.Background(), getBannerReq1).Return(&sbPb.GetBannersResponse{
						Status: rpcPb.StatusInternal(),
					}, nil),
				},
			},
			want: &sbPb.FetchSherlockBannersResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &sbPb.FetchSherlockBannersRequest{
					ActorId:           actorId1,
					StructureTypeList: []sbPb.SherlockBannerStructureType{sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_LABEL_MESSAGE_LIST},
				},
				mocks: []interface{}{
					mockGetBannersHelper.EXPECT().GetBanners(context.Background(), getBannerReq1).Return(&sbPb.GetBannersResponse{
						Status:  rpcPb.StatusOk(),
						Banners: []*sbPb.SherlockBanner{banner1},
					}, nil),
				},
			},
			want: &sbPb.FetchSherlockBannersResponse{
				Status:  rpcPb.StatusOk(),
				Banners: []*sbPb.SherlockBanner{banner1},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewCxSherlockBannersCollector(mockGetBannersHelper)
			got, err := s.FetchSherlockBanners(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchBanners() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isDeepEqualFetchBannersResp(got, tt.want) {
				t.Errorf("FetchBanners() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func isDeepEqualFetchBannersResp(got, want *sbPb.FetchSherlockBannersResponse) bool {
	return proto.Equal(got.GetStatus(), want.GetStatus()) &&
		isDeepEqualSherlockBannersList(got.GetBanners(), want.GetBanners())
}

func isDeepEqualSherlockBannersList(got, want []*sbPb.SherlockBanner) bool {
	if len(got) != len(want) {
		return false
	}
	sort.Slice(got, func(i, j int) bool {
		return got[i].GetId() < got[j].GetId()
	})
	sort.Slice(want, func(i, j int) bool {
		return want[i].GetId() < want[j].GetId()
	})
	for i := 0; i < len(got); i++ {
		if !(isSherlockBannerEqual(got[i], want[i])) {
			return false
		}
	}
	return true
}

func isSherlockBannerEqual(got, want *sbPb.SherlockBanner) bool {
	return proto.Equal(got.GetBannerContent(), want.GetBannerContent()) &&
		got.GetStartTime() == want.GetStartTime() && got.GetEndTime() == want.GetEndTime() &&
		isDeepEqualMappingItems(got.GetMappingItems(), want.GetMappingItems())
}

func isDeepEqualMappingItems(got, want *sbPb.MappingItems) bool {
	gotRoles := got.GetSherlockUserRoles()
	wantRoles := want.GetSherlockUserRoles()
	if len(gotRoles) != len(wantRoles) {
		return false
	}
	sort.Slice(gotRoles, func(i, j int) bool {
		return int(gotRoles[i]) < int(gotRoles[j])
	})
	sort.Slice(wantRoles, func(i, j int) bool {
		return int(wantRoles[i]) < int(wantRoles[j])
	})
	for i := 0; i < len(gotRoles); i++ {
		if gotRoles[i] != wantRoles[i] {
			return false
		}
	}
	return true
}
