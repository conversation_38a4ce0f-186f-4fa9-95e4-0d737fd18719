package fittt

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type FitttUpdateHomeCard struct {
	rmsClient manager.RuleManagerClient
}

func NewFitttUpdateHomeCard(rmsClient manager.RuleManagerClient) *FitttUpdateHomeCard {
	return &FitttUpdateHomeCard{
		rmsClient: rmsClient,
	}
}

func (r *FitttUpdateHomeCard) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	paramList := []*dsPb.ParameterMeta{
		{
			Name:            homeCardId,
			Label:           "Id",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            homeCardState,
			Label:           "State",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
			Options:         homeCardStates,
		},
		{
			Name:            homeCardWeight,
			Label:           "Weight",
			Type:            dsPb.ParameterDataType_INTEGER,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            homeCardDeepLink,
			Label:           "Deeplink",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            homeCardData,
			Label:           "Card Data",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (r *FitttUpdateHomeCard) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("all filters cannot be nil")
	}

	updatedHomeCard := &manager.UpdateHomeCardRequest{}
	homeCard := &manager.HomeCard{}
	// setting up the homecard
	updatedHomeCard.Card = homeCard
	// keeping the default state as inactive incase no input is
	// received to prevent any additional errors
	homeCard.State = manager.HomeCardState_HOME_CARD_INACTIVE

	// get filter data within homeCard instance
	filter_err := parse_filters(ctx, filters, homeCard)
	if filter_err != nil {
		logger.Error(ctx, "error while parsing filters", zap.Error(filter_err))
		return "", filter_err
	}

	// call the update card api from rms service
	resp, err := r.rmsClient.UpdateHomeCard(ctx, updatedHomeCard)
	if rpc_err := epifigrpc.RPCError(resp, err); rpc_err != nil {
		logger.Error(ctx, "error while updating homecard", zap.Error(rpc_err))
		return "", rpc_err
	}

	return get_marshalled_update_response(ctx, resp)
}

func parse_filters(ctx context.Context, filters []*dsPb.Filter, homeCard *manager.HomeCard) error {
	protoUnmarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case homeCardId:
			id := filter.GetStringValue()
			if id == "" {
				logger.Error(ctx, "home card id cannot be empty", zap.String("filter", filter.String()))
				return status.Error(codes.InvalidArgument, "home card id cannot be empty")
			}
			homeCard.Id = id
		case homeCardState:
			stateStr := filter.GetDropdownValue()
			if stateStr == manager.HomeCardState_HOME_CARD_ACTIVE.String() {
				homeCard.State = manager.HomeCardState_HOME_CARD_ACTIVE
			}
		case homeCardWeight:
			homeCard.Weight = int32(filter.GetIntegerValue())
			if homeCard.Weight < 0 {
				logger.Error(ctx, "home card weight cannot be negative", zap.String("filter", filter.String()))
				return status.Error(codes.InvalidArgument, "home card weight cannot be negative")
			}
		case homeCardDeepLink:
			deeplink := &deeplink.Deeplink{}
			if err := protoUnmarshaller.Unmarshal([]byte(filter.GetStringValue()), deeplink); err != nil {
				logger.Error(ctx, "error unmarshalling deeplink to proto", zap.String("filter", filter.String()), zap.Error(err))
				return status.Error(codes.InvalidArgument, "error unmarshalling deeplink to proto")
			}
			homeCard.Deeplink = deeplink
		case homeCardData:
			card_data := &manager.HomeCardData{}
			if err := protoUnmarshaller.Unmarshal([]byte(filter.GetStringValue()), card_data); err != nil {
				logger.Error(ctx, "error unmarshalling homecard data to proto", zap.String("filter", filter.String()), zap.Error(err))
				return status.Error(codes.InvalidArgument, "error unmarshalling homecard data to proto")
			}
			homeCard.CardData = card_data
		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return status.Error(codes.InvalidArgument, "invalid filter")
		}
	}
	return nil
}

func get_marshalled_update_response(ctx context.Context, resp *manager.UpdateHomeCardResponse) (string, error) {
	// returned marshalled response
	marshalledRes, m_err := protojson.Marshal(resp)
	if m_err != nil {
		logger.Error(ctx, "error marshalling update homecard response", zap.Error(m_err))
		return "", m_err
	}
	return string(marshalledRes), nil
}
