package db_states

import (
	"context"
	"sort"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/gamma/api/casbin"
	"github.com/epifi/gamma/cx/developer/db_states/redactconf"

	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/gamma/cx/developer/db_states/collector"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	dbStatePb "github.com/epifi/gamma/api/cx/developer/db_state"

	"github.com/epifi/be-common/pkg/redactor"

	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
)

type Service struct {
	collectorFactory collector.ICollectorFactory
	genConf          *cxGenConf.Config
	casbinClient     casbin.CasbinClient
}

func NewDbStatesServer(collectorFactory collector.ICollectorFactory, genConf *cxGenConf.Config, casbinClient casbin.CasbinClient) *Service {
	return &Service{
		collectorFactory: collectorFactory,
		genConf:          genConf,
		casbinClient:     casbinClient,
	}
}

var _ dbStatePb.DBStateServer = &Service{}

func (s *Service) GetEntityListForService(ctx context.Context, request *dbStatePb.GetEntityListForServiceRequest) (*dbStatePb.GetEntityListForServiceResponse, error) {
	if request.GetService() == dbStatePb.Service_SERVICE_UNSPECIFIED {
		logger.Error(ctx, "mandatory parameter service not passed",
			zap.String(logger.AGENT_EMAIL, request.GetHeader().GetAgentEmail()),
		)
		return &dbStatePb.GetEntityListForServiceResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("mandatory parameter service not passed"),
		}, nil
	}

	if !s.genConf.DbStateConfig().IsRbacEnabled() {
		return s.getAllEntities(ctx, request)
	}
	return s.getEntitiesWithPermissions(ctx, request)
}

func (s *Service) GetParameterListForEntity(ctx context.Context, request *dbStatePb.GetParameterListForEntityRequest) (*dbStatePb.GetParameterListForEntityResponse, error) {
	if request.GetService() == dbStatePb.Service_SERVICE_UNSPECIFIED || request.GetEntity() == "" {
		logger.Error(ctx, "mandatory parameter service or entity not passed",
			zap.String(logger.AGENT_EMAIL, request.GetHeader().GetAgentEmail()),
		)
		return &dbStatePb.GetParameterListForEntityResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("mandatory parameter service or entity not passed"),
		}, nil
	}

	verifyErr := s.isDbStateAccessAllowed(ctx, request.GetService(), request.GetEntity(),
		request.GetHeader().GetAgentEmail(), request.GetHeader().GetAccessLevel())
	if verifyErr != nil {
		logger.Error(ctx, "failed to verify permissions", zap.Error(verifyErr))
		switch {
		case errors.Is(verifyErr, epifierrors.ErrPermissionDenied):
			return &dbStatePb.GetParameterListForEntityResponse{Status: rpcPb.StatusPermissionDenied()}, nil
		case errors.Is(verifyErr, epifierrors.ErrInvalidArgument):
			return &dbStatePb.GetParameterListForEntityResponse{Status: rpcPb.StatusInvalidArgument()}, nil
		default:
			return &dbStatePb.GetParameterListForEntityResponse{Status: rpcPb.StatusInternal()}, nil
		}
	}

	collectorSvc, err := s.collectorFactory.GetCollector(request.GetService())
	if err != nil {
		logger.Error(ctx, "error while getting collector for service",
			zap.String("service", request.GetService().String()),
		)
		return &dbStatePb.GetParameterListForEntityResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to get collector for service"),
		}, nil
	}

	resp, err := collectorSvc.GetParameterList(ctx, &dbStatePb.GetParameterListRequest{
		Service: request.GetService(),
		Entity:  request.GetEntity(),
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "failed to fetch entity list", zap.Error(te))
		if err != nil || resp == nil {
			return &dbStatePb.GetParameterListForEntityResponse{Status: rpcPb.StatusInternal()}, nil
		}
	}
	// return response with status same as returned by collector
	return &dbStatePb.GetParameterListForEntityResponse{Status: resp.GetStatus(), ParameterList: resp.ParameterList}, nil
}

func (s *Service) GetDataForEntity(ctx context.Context, request *dbStatePb.GetDataForEntityRequest) (*dbStatePb.GetDataForEntityResponse, error) {
	if request.GetService() == dbStatePb.Service_SERVICE_UNSPECIFIED || request.GetEntity() == "" {
		logger.Error(ctx, "mandatory parameter service or entity not passed",
			zap.String(logger.AGENT_EMAIL, request.GetHeader().GetAgentEmail()),
		)
		return &dbStatePb.GetDataForEntityResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("mandatory parameter service or entity not passed"),
		}, nil
	}

	verifyErr := s.isDbStateAccessAllowed(ctx, request.GetService(), request.GetEntity(),
		request.GetHeader().GetAgentEmail(), request.GetHeader().GetAccessLevel())
	if verifyErr != nil {
		logger.Error(ctx, "failed to verify permissions", zap.Error(verifyErr))
		switch {
		case errors.Is(verifyErr, epifierrors.ErrPermissionDenied):
			return &dbStatePb.GetDataForEntityResponse{Status: rpcPb.StatusPermissionDenied()}, nil
		case errors.Is(verifyErr, epifierrors.ErrInvalidArgument):
			return &dbStatePb.GetDataForEntityResponse{Status: rpcPb.StatusInvalidArgument()}, nil
		default:
			return &dbStatePb.GetDataForEntityResponse{Status: rpcPb.StatusInternal()}, nil
		}
	}

	collectorSvc, err := s.collectorFactory.GetCollector(request.GetService())
	if err != nil {
		logger.Error(ctx, "error while getting collector for service",
			zap.String("service", request.GetService().String()),
		)
		return &dbStatePb.GetDataForEntityResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to get collector for service"),
		}, nil
	}
	// TODO (sachin): check if filter validation is needed here
	resp, err := collectorSvc.GetData(ctx, &dbStatePb.GetDataRequest{
		Service: request.GetService(),
		Entity:  request.GetEntity(),
		Filters: request.GetFilters(),
	})
	// log error
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "failed to fetch data", zap.Error(te))
		if err != nil || resp == nil {
			return &dbStatePb.GetDataForEntityResponse{Status: rpcPb.StatusInternal()}, nil
		}
	}
	if s.genConf.IsRedactionEnabledForDBStates() {
		h := &redactor.JsonRedactor{}

		redactedJsonResp, redErr := h.Redact([]byte(resp.GetJsonResponse()), redactconf.DefaultDbStateRedactionConf)
		if redErr != nil {
			logger.Error(ctx, "failed to redact JsonResponse", zap.Error(redErr))
			logger.Debug(ctx, "failed to redact JsonResponse", zap.Error(redErr))
			return &dbStatePb.GetDataForEntityResponse{Status: rpcPb.StatusInternal()}, nil
		}
		logger.Debug(ctx, "redacted successfully")
		return &dbStatePb.GetDataForEntityResponse{
			Status:       resp.GetStatus(),
			JsonResponse: string(redactedJsonResp),
			PageContext:  resp.GetPageContext(),
		}, nil
	}

	// return response with status same as returned by collector
	return &dbStatePb.GetDataForEntityResponse{
		Status:       resp.GetStatus(),
		JsonResponse: resp.GetJsonResponse(),
		PageContext:  resp.GetPageContext(),
	}, nil
}

func (s *Service) isDbStateAccessAllowed(ctx context.Context, service dbStatePb.Service, entity, agentId string, accessLvl casbin.AccessLevel) error {
	if !s.genConf.DbStateConfig().IsRbacEnabled() {
		return nil
	}
	entitySevCombo := dbStatePb.NewEntityServiceCombination(service.String(), entity)
	verifyResp, verifyErr := s.casbinClient.VerifyUserPermissions(ctx, &casbin.VerifyUserPermissionsRequest{
		UserId:      agentId,
		Resource:    entitySevCombo.GetResourceString(),
		AccessLevel: accessLvl,
	})
	if te := epifigrpc.RPCError(verifyResp, verifyErr); te != nil {
		switch {
		case verifyResp.GetStatus().IsPermissionDenied():
			return errors.Wrap(epifierrors.ErrPermissionDenied, te.Error())
		case verifyResp.GetStatus().IsInvalidArgument():
			return errors.Wrap(epifierrors.ErrInvalidArgument, te.Error())
		default:
			return errors.Wrap(te, "failed to verify permissions")
		}
	}
	return nil
}

func (s *Service) GetServiceList(ctx context.Context, req *dbStatePb.GetServiceListRequest) (*dbStatePb.GetServiceListResponse, error) {
	if !s.genConf.DbStateConfig().IsRbacEnabled() {
		return s.getAllServices()
	}

	serviceList, err := s.getServicesWithPermissions(ctx, req.GetHeader().GetAccessLevel().String())
	if err != nil {
		return &dbStatePb.GetServiceListResponse{Status: rpcPb.StatusInternal()}, nil
	}

	return &dbStatePb.GetServiceListResponse{
		Status:      rpcPb.StatusOk(),
		ServiceList: serviceList,
	}, nil
}

func (s *Service) getAllServices() (*dbStatePb.GetServiceListResponse, error) {
	var serviceList []dbStatePb.Service
	for idx := 1; idx < len(dbStatePb.Service_name); idx++ {
		serviceList = append(serviceList, dbStatePb.Service(idx))
	}
	// sort by service name alphabetically
	sort.Slice(serviceList, func(i, j int) bool {
		return serviceList[i].String() < serviceList[j].String()
	})
	return &dbStatePb.GetServiceListResponse{
		Status:      rpcPb.StatusOk(),
		ServiceList: serviceList,
	}, nil
}

func (s *Service) getServicesWithPermissions(ctx context.Context, agentEmail string) ([]dbStatePb.Service, error) {
	resp, err := s.casbinClient.GetPermissionsForUser(ctx, &casbin.GetPermissionsForUserRequest{
		UserId: agentEmail,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "failed to fetch permissions", zap.Error(te))
		return nil, err
	}

	var serviceList []dbStatePb.Service
	for _, perm := range resp.GetPermissionList() {
		if dbStatePb.IsDbStateResourceString(perm.GetResource()) {
			dbStateResource, err := dbStatePb.ParseDbStateResourceString(perm.GetResource())
			if err != nil {
				logger.Error(ctx, "failed to parse db state resource string", zap.String("resource", perm.GetResource()))
				continue
			}
			if !lo.Contains(serviceList, dbStateResource.GetService()) {
				serviceList = append(serviceList, dbStateResource.GetService())
			}
		}
	}
	sort.Slice(serviceList, func(i, j int) bool {
		return serviceList[i].String() < serviceList[j].String()
	})
	return serviceList, nil
}

func (s *Service) getAllEntities(ctx context.Context, request *dbStatePb.GetEntityListForServiceRequest) (*dbStatePb.GetEntityListForServiceResponse, error) {
	collectorSvc, err := s.collectorFactory.GetCollector(request.GetService())
	if err != nil {
		logger.Error(ctx, "error while getting collector for service",
			zap.String("service", request.GetService().String()),
		)
		return &dbStatePb.GetEntityListForServiceResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to get collector for service"),
		}, nil
	}
	resp, err := collectorSvc.GetEntityList(ctx, &dbStatePb.GetEntityListRequest{
		Service: request.GetService(),
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "failed to fetch entity list", zap.Error(te))
		if err != nil || resp == nil {
			return &dbStatePb.GetEntityListForServiceResponse{Status: rpcPb.StatusInternal()}, nil
		}
	}
	return &dbStatePb.GetEntityListForServiceResponse{
		Status:     resp.GetStatus(),
		EntityList: resp.GetEntityList(),
	}, nil
}

func (s *Service) getEntitiesWithPermissions(ctx context.Context, request *dbStatePb.GetEntityListForServiceRequest) (*dbStatePb.GetEntityListForServiceResponse, error) {
	resp, err := s.casbinClient.GetPermissionsForUser(ctx, &casbin.GetPermissionsForUserRequest{
		UserId: request.GetHeader().GetAccessLevel().String(),
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "failed to fetch permissions", zap.Error(te))
		if err != nil || resp == nil {
			return &dbStatePb.GetEntityListForServiceResponse{Status: rpcPb.StatusInternal()}, nil
		}
	}

	var entityList []string
	for _, perm := range resp.GetPermissionList() {
		if dbStatePb.IsDbStateResourceString(perm.GetResource()) {
			dbStateResource, err := dbStatePb.ParseDbStateResourceString(perm.GetResource())
			if err != nil {
				logger.Error(ctx, "failed to parse db state resource string", zap.String("resource", perm.GetResource()))
				continue
			}
			if dbStateResource.GetService() == request.GetService() {
				entityList = append(entityList, dbStateResource.GetEntity())
			}
		}
	}
	sort.Slice(entityList, func(i, j int) bool {
		return entityList[i] < entityList[j]
	})
	return &dbStatePb.GetEntityListForServiceResponse{
		Status:     resp.GetStatus(),
		EntityList: entityList,
	}, nil
}
