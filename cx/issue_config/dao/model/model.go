package model

import (
	"time"

	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/nulltypes"
	"github.com/epifi/be-common/pkg/pagination"

	icPb "github.com/epifi/gamma/api/cx/issue_config"
)

type IssueConfig struct {
	Id              string `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	IssueCategoryId string
	ConfigType      icPb.ConfigType
	ConfigPayload   *icPb.ConfigPayload
	ConfigVersion   int64
	CreatedAt       time.Time
	UpdatedAt       time.Time
	UpdatedBy       nulltypes.NullString
	ApprovedBy      nulltypes.NullString
	IsLatestVersion bool
}

func NewIssueConfigFromProtoMsg(config *icPb.IssueConfig) *IssueConfig {
	return &IssueConfig{
		IssueCategoryId: config.GetIssueCategoryId(),
		ConfigPayload:   config.GetConfigPayload(),
		ConfigVersion:   config.GetConfigVersion(),
		ConfigType:      config.GetConfigType(),
		UpdatedBy:       nulltypes.NewNullString(config.GetUpdatedBy()),
		ApprovedBy:      nulltypes.NewNullString(config.GetApprovedBy()),
		IsLatestVersion: config.GetIsLatestVersion(),
	}
}

func (issueConfig *IssueConfig) ToProtoMessage() *icPb.IssueConfig {
	if issueConfig == nil {
		return nil
	}
	return &icPb.IssueConfig{
		Id:              issueConfig.Id,
		IssueCategoryId: issueConfig.IssueCategoryId,
		ConfigType:      issueConfig.ConfigType,
		ConfigPayload:   issueConfig.ConfigPayload,
		ConfigVersion:   issueConfig.ConfigVersion,
		CreatedAt:       timestamp.New(issueConfig.CreatedAt),
		UpdatedAt:       timestamp.New(issueConfig.UpdatedAt),
		UpdatedBy:       issueConfig.UpdatedBy.GetValue(),
		ApprovedBy:      issueConfig.ApprovedBy.GetValue(),
		IsLatestVersion: issueConfig.IsLatestVersion,
	}
}

func ToProtoList(modelList []*IssueConfig) []*icPb.IssueConfig {
	var protoList []*icPb.IssueConfig
	for _, ic := range modelList {
		protoList = append(protoList, ic.ToProtoMessage())
	}
	return protoList
}

type IssueConfigList []*icPb.IssueConfig

func (i IssueConfigList) Slice(start, end int) pagination.Rows {
	return i[start:end]
}

func (i IssueConfigList) GetTimestamp(index int) time.Time {
	return i[index].GetCreatedAt().AsTime()
}

func (i IssueConfigList) Size() int {
	return len(i)
}

var _ pagination.Rows = &IssueConfigList{}
