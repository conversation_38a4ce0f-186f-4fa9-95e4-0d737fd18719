package upi_test

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/be-common/api/rpc"
	upiPb "github.com/epifi/gamma/api/upi"
)

func TestService_CheckVerifiedMerchant(t *testing.T) {
	upiService, mocks, deferFun := getUpiTestServiceWithMock(t)
	mocks.brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	defer deferFun()

	type mockGetVerifiedEntry struct {
		enable   bool
		req      string
		response *upiPb.VerifiedAddressEntry
		err      error
	}

	type args struct {
		ctx context.Context
		req *upiPb.CheckVerifiedMerchantRequest
	}
	tests := []struct {
		name                 string
		args                 args
		mockGetVerifiedEntry mockGetVerifiedEntry
		want                 *upiPb.CheckVerifiedMerchantResponse
		wantErr              bool
	}{
		{
			name: "return status ok for verified merchant exist in db",
			args: args{
				ctx: context.Background(),
				req: &upiPb.CheckVerifiedMerchantRequest{
					Vpa: "test-urn@okaxis",
				},
			},
			mockGetVerifiedEntry: mockGetVerifiedEntry{
				enable: true,
				req:    "test-urn@okaxis",
				response: &upiPb.VerifiedAddressEntry{
					Id:          "id-1",
					MerchantVpa: "test-urn@okaxis",
					Name:        "name",
					Url:         "url",
					KeyCode:     "key-code",
					Type:        "type",
					Ki:          "ki",
					KeyValue:    "keyvalue",
				},
				err: nil,
			},
			want: &upiPb.CheckVerifiedMerchantResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "return status record not found for verified merchant exist in db",
			args: args{
				ctx: context.Background(),
				req: &upiPb.CheckVerifiedMerchantRequest{
					Vpa: "vpa-not-exist@okaxis",
				},
			},
			mockGetVerifiedEntry: mockGetVerifiedEntry{
				enable:   true,
				req:      "vpa-not-exist@okaxis",
				response: nil,
				err:      epifierrors.ErrRecordNotFound,
			},
			want: &upiPb.CheckVerifiedMerchantResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetVerifiedEntry.enable {
				mocks.mockVerifiedAddressEntryDao.EXPECT().GetVerifiedAddressEntryByVpa(context.Background(), tt.mockGetVerifiedEntry.req).
					Return(tt.mockGetVerifiedEntry.response, tt.mockGetVerifiedEntry.err)
			}
			got, err := upiService.CheckVerifiedMerchant(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckVerifiedMerchant() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckVerifiedMerchant() got = %v, want %v", got, tt.want)
			}
		})
	}
}
