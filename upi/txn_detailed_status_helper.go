package upi

import (
	"github.com/golang/protobuf/ptypes"

	paymentPb "github.com/epifi/gamma/api/order/payment"
)

// GetTxnDetailedStatusUpiForAck return detailed status for acknowledgment response
func GetTxnDetailedStatusUpiForAck(statue paymentPb.TransactionDetailedStatus_DetailedStatus_State, rawStatusCode,
	rawStatusDescription, statusCode, statusDescriptionPayer, statusDescriptionPayee string,
	api paymentPb.TransactionDetailedStatus_DetailedStatus_API) *paymentPb.TransactionDetailedStatus_DetailedStatus {
	return &paymentPb.TransactionDetailedStatus_DetailedStatus{
		// for ack payer and payee status codes are same
		StatusCodePayer:        statusCode,
		StatusCodePayee:        statusCode,
		StatusDescriptionPayer: statusDescriptionPayer,
		StatusDescriptionPayee: statusDescriptionPayee,
		CreatedAt:              ptypes.TimestampNow(),
		State:                  statue,
		RawStatusCode:          rawStatusCode,
		RawStatusDescription:   rawStatusDescription,
		Api:                    api,
	}
}

// GetTxnDetailedStatusForSystemErr returns detailed status for epifi system errors while processing the transactions
// TODO(nitesh): take care of code duplicate code
func GetTxnDetailedStatusForSystemErr(err error, api paymentPb.TransactionDetailedStatus_DetailedStatus_API) *paymentPb.TransactionDetailedStatus_DetailedStatus {
	return &paymentPb.TransactionDetailedStatus_DetailedStatus{
		SystemErrorDescription: err.Error(),
		ErrorCategory:          paymentPb.TransactionDetailedStatus_DetailedStatus_SYSTEM_ERROR,
		Api:                    api,
		CreatedAt:              ptypes.TimestampNow(),
	}
}
