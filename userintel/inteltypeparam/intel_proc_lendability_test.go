package inteltypeparam

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	structPb "google.golang.org/protobuf/types/known/structpb"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	mockEvents "github.com/epifi/be-common/pkg/events/mocks"

	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	mockCreditReport "github.com/epifi/gamma/api/creditreportv2/mocks"
	employmentPb "github.com/epifi/gamma/api/employment"
	mockEmp "github.com/epifi/gamma/api/employment/mocks"
	"github.com/epifi/gamma/api/preapprovedloan/lendability"
	mockPal "github.com/epifi/gamma/api/preapprovedloan/lendability/mocks"
	userPb "github.com/epifi/gamma/api/user"
	mockUser "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/api/userintel"
	mockFs "github.com/epifi/gamma/featurestore/mocks"
	fsModel "github.com/epifi/gamma/featurestore/model"
	"github.com/epifi/gamma/userintel/constants"
	mockUi "github.com/epifi/gamma/userintel/test/mocks"
)

func TestLendabilityProc_ProcessIntel(t *testing.T) {
	type args struct {
		ctx context.Context
		req *IntelTypeProcRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mock *mockDep)
		want       *IntelTypeProcResponse
		wantErr    bool
	}{
		{
			name: "#1 error in getting credit report",
			args: args{
				ctx: context.Background(),
				req: &IntelTypeProcRequest{
					ActorId: actorId1,
				},
			},
			setupMocks: func(mock *mockDep) {
				mock.crManager.EXPECT().GetCreditReport(gomock.Any(), &creditReportV2Pb.GetCreditReportRequest{
					ActorId: actorId1,
				}).Return(&creditReportV2Pb.GetCreditReportResponse{
					Status: rpc.StatusInternal(),
				}, nil)
				mock.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: actorId1,
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{},
					},
				}, nil)

				// Employment info success
				mock.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), &employmentPb.GetEmploymentInfoRequest{
					ActorId: actorId1,
				}).Return(&employmentPb.GetEmploymentInfoResponse{
					Status:         rpc.StatusOk(),
					EmploymentData: &employmentPb.EmploymentData{},
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "#2 error because credit report verification status is not found",
			args: args{
				ctx: context.Background(),
				req: &IntelTypeProcRequest{
					ActorId: actorId1,
				},
			},
			setupMocks: func(mock *mockDep) {
				mock.crManager.EXPECT().GetCreditReport(gomock.Any(), &creditReportV2Pb.GetCreditReportRequest{
					ActorId: actorId1,
				}).Return(&creditReportV2Pb.GetCreditReportResponse{
					Status:             rpc.StatusOk(),
					CreditReport:       &creditReportV2Pb.CreditReport{},
					VerificationStatus: creditReportV2Pb.VerificationStatus_VERIFICATION_STATUS_FAILED_REPORT_NOT_FOUND,
				}, nil)
				mock.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: actorId1,
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{},
					},
				}, nil)

				// Employment info success
				mock.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), &employmentPb.GetEmploymentInfoRequest{
					ActorId: actorId1,
				}).Return(&employmentPb.GetEmploymentInfoResponse{
					Status:         rpc.StatusOk(),
					EmploymentData: &employmentPb.EmploymentData{},
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "#3 error in getting user details",
			args: args{
				ctx: context.Background(),
				req: &IntelTypeProcRequest{
					ActorId: actorId1,
				},
			},
			setupMocks: func(mock *mockDep) {
				mock.crManager.EXPECT().GetCreditReport(gomock.Any(), &creditReportV2Pb.GetCreditReportRequest{
					ActorId: actorId1,
				}).Return(&creditReportV2Pb.GetCreditReportResponse{
					Status:             rpc.StatusOk(),
					CreditReport:       &creditReportV2Pb.CreditReport{},
					CreditReportData:   &creditReportV2Pb.CreditReportData{},
					VerificationStatus: creditReportV2Pb.VerificationStatus_VERIFICATION_STATUS_SUCCESS,
				}, nil)

				mock.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: actorId1,
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusInternal(),
				}, nil)

				mock.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), &employmentPb.GetEmploymentInfoRequest{
					ActorId: actorId1,
				}).Return(&employmentPb.GetEmploymentInfoResponse{
					Status:         rpc.StatusOk(),
					EmploymentData: &employmentPb.EmploymentData{},
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "#4 error in getting employment info",
			args: args{
				ctx: context.Background(),
				req: &IntelTypeProcRequest{
					ActorId: actorId1,
				},
			},
			setupMocks: func(mock *mockDep) {
				// Credit report success
				mock.crManager.EXPECT().GetCreditReport(gomock.Any(), &creditReportV2Pb.GetCreditReportRequest{
					ActorId: actorId1,
				}).Return(&creditReportV2Pb.GetCreditReportResponse{
					Status:             rpc.StatusOk(),
					CreditReport:       &creditReportV2Pb.CreditReport{},
					CreditReportData:   &creditReportV2Pb.CreditReportData{},
					VerificationStatus: creditReportV2Pb.VerificationStatus_VERIFICATION_STATUS_SUCCESS,
				}, nil)

				// User details success
				mock.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: actorId1,
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{},
					},
				}, nil)

				// Employment info error
				mock.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), &employmentPb.GetEmploymentInfoRequest{
					ActorId: actorId1,
				}).Return(&employmentPb.GetEmploymentInfoResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "#5 error in extracting feature",
			args: args{
				ctx: context.Background(),
				req: &IntelTypeProcRequest{
					ActorId: actorId1,
				},
			},
			setupMocks: func(mock *mockDep) {
				// Credit report success
				mock.crManager.EXPECT().GetCreditReport(gomock.Any(), &creditReportV2Pb.GetCreditReportRequest{
					ActorId: actorId1,
				}).Return(&creditReportV2Pb.GetCreditReportResponse{
					Status:             rpc.StatusOk(),
					CreditReport:       &creditReportV2Pb.CreditReport{},
					CreditReportData:   &creditReportV2Pb.CreditReportData{},
					VerificationStatus: creditReportV2Pb.VerificationStatus_VERIFICATION_STATUS_SUCCESS,
				}, nil)

				// User details success
				mock.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: actorId1,
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{},
					},
				}, nil)

				// Employment info success
				mock.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), &employmentPb.GetEmploymentInfoRequest{
					ActorId: actorId1,
				}).Return(&employmentPb.GetEmploymentInfoResponse{
					Status:         rpc.StatusOk(),
					EmploymentData: &employmentPb.EmploymentData{},
				}, nil)

				// Feature store error
				mock.fsFactory.EXPECT().GetFeatureStoreClient(commonvgpb.Vendor_FENNEL_FEATURE_STORE).Return(mock.fsClient, nil)
				mock.fsClient.EXPECT().ExtractFeature(gomock.Any(), gomock.Any()).Return(nil, errors.New("error in extract feature response"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "#6 error in storing user intel",
			args: args{
				ctx: context.Background(),
				req: &IntelTypeProcRequest{
					ActorId:   actorId1,
					IntelType: userintel.IntelType_INTEL_TYPE_LENDABILITY,
				},
			},
			setupMocks: func(mock *mockDep) {
				// Credit report success
				mock.crManager.EXPECT().GetCreditReport(gomock.Any(), &creditReportV2Pb.GetCreditReportRequest{
					ActorId: actorId1,
				}).Return(&creditReportV2Pb.GetCreditReportResponse{
					Status:             rpc.StatusOk(),
					CreditReport:       &creditReportV2Pb.CreditReport{},
					CreditReportData:   &creditReportV2Pb.CreditReportData{},
					VerificationStatus: creditReportV2Pb.VerificationStatus_VERIFICATION_STATUS_SUCCESS,
				}, nil)

				// User details success
				mock.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: actorId1,
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{},
					},
				}, nil)

				// Employment info success
				mock.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), &employmentPb.GetEmploymentInfoRequest{
					ActorId: actorId1,
				}).Return(&employmentPb.GetEmploymentInfoResponse{
					Status:         rpc.StatusOk(),
					EmploymentData: &employmentPb.EmploymentData{},
				}, nil)

				// Feature store success
				mock.fsFactory.EXPECT().GetFeatureStoreClient(commonvgpb.Vendor_FENNEL_FEATURE_STORE).Return(mock.fsClient, nil)
				mock.fsClient.EXPECT().ExtractFeature(gomock.Any(), gomock.Any()).Return(&fsModel.ExtractFeatureResponse{
					FeaturesSetList: []fsModel.FeatureSet{
						{
							FeatureList: []fsModel.Feature{
								{
									Name: "federal_lendability",
									Value: &structPb.Value{
										Kind: &structPb.Value_ListValue{
											ListValue: &structPb.ListValue{
												Values: []*structPb.Value{
													{
														Kind: &structPb.Value_NumberValue{
															NumberValue: 1,
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				}, nil)

				mock.palLendabilityClient.EXPECT().GetActorLendability(gomock.Any(), &lendability.GetActorLendabilityRequest{
					ActorId: actorId1,
					Flow:    lendability.Flow_FLOW_SAVINGS_ACCOUNT,
				}).Return(&lendability.GetActorLendabilityResponse{
					Status:             rpc.StatusOk(),
					LendabilityDetails: &lendability.LendabilityDetails{},
				}, nil)

				mock.eventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

				// DAO operation error
				mock.uiDao.EXPECT().GetByActorIdAndIntelType(gomock.Any(), actorId1, userintel.IntelType_INTEL_TYPE_LENDABILITY).Return(nil, errors.New("error in ui dao"))
				mock.uiDao.EXPECT().SoftDelete(gomock.Any(), actorId1, userintel.IntelType_INTEL_TYPE_LENDABILITY).Return(errors.New("error in ui dao"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "#7 successful",
			args: args{
				ctx: context.Background(),
				req: &IntelTypeProcRequest{
					ActorId:   actorId1,
					IntelType: userintel.IntelType_INTEL_TYPE_LENDABILITY,
				},
			},
			setupMocks: func(mock *mockDep) {
				// Credit report success
				mock.crManager.EXPECT().GetCreditReport(gomock.Any(), &creditReportV2Pb.GetCreditReportRequest{
					ActorId: actorId1,
				}).Return(&creditReportV2Pb.GetCreditReportResponse{
					Status:             rpc.StatusOk(),
					CreditReport:       &creditReportV2Pb.CreditReport{},
					CreditReportData:   &creditReportV2Pb.CreditReportData{},
					VerificationStatus: creditReportV2Pb.VerificationStatus_VERIFICATION_STATUS_SUCCESS,
				}, nil)

				// User details success
				mock.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: actorId1,
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{},
					},
				}, nil)

				// Employment info success
				mock.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), &employmentPb.GetEmploymentInfoRequest{
					ActorId: actorId1,
				}).Return(&employmentPb.GetEmploymentInfoResponse{
					Status:         rpc.StatusOk(),
					EmploymentData: &employmentPb.EmploymentData{},
				}, nil)
				// Feature store success
				mock.fsFactory.EXPECT().GetFeatureStoreClient(commonvgpb.Vendor_FENNEL_FEATURE_STORE).Return(mock.fsClient, nil)
				mock.fsClient.EXPECT().ExtractFeature(gomock.Any(), gomock.AssignableToTypeOf(&fsModel.ExtractFeatureRequest{})).Return(&fsModel.ExtractFeatureResponse{
					FeaturesSetList: []fsModel.FeatureSet{
						{
							FeatureList: []fsModel.Feature{
								{
									Name: constants.ABFL_PL_REPORT_ELIGIBLE_FLAG_V2,
									Value: &structPb.Value{
										Kind: &structPb.Value_NumberValue{
											NumberValue: 1,
										},
									},
								},
							},
						},
					}}, nil)

				mock.palLendabilityClient.EXPECT().GetActorLendability(gomock.Any(), &lendability.GetActorLendabilityRequest{
					ActorId: actorId1,
					Flow:    lendability.Flow_FLOW_SAVINGS_ACCOUNT,
				}).Return(&lendability.GetActorLendabilityResponse{
					Status:             rpc.StatusOk(),
					LendabilityDetails: &lendability.LendabilityDetails{},
				}, nil)

				mock.eventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

				// DAO operations success
				mock.uiDao.EXPECT().GetByActorIdAndIntelType(gomock.Any(), actorId1, userintel.IntelType_INTEL_TYPE_LENDABILITY).Return(nil, epifierrors.ErrRecordNotFound)
				mock.uiDao.EXPECT().SoftDelete(gomock.Any(), actorId1, userintel.IntelType_INTEL_TYPE_LENDABILITY).Return(nil)
				mock.uiDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()
			mockCrManager := mockCreditReport.NewMockCreditReportManagerClient(ctr)
			mockFeatureStoreFactory := mockFs.NewMockIFactory(ctr)
			mockUiDao := mockUi.NewMockUserIntelDao(ctr)
			mockFsClient := mockFs.NewMockIFeatureStore(ctr)
			mockUserClient := mockUser.NewMockUsersClient(ctr)
			mockEmploymentClient := mockEmp.NewMockEmploymentClient(ctr)
			mockPalClient := mockPal.NewMockLendabilityClient(ctr)
			mockEventBroker := mockEvents.NewMockBroker(ctr)

			if tt.setupMocks != nil {
				tt.setupMocks(&mockDep{
					crManager:            mockCrManager,
					fsFactory:            mockFeatureStoreFactory,
					uiDao:                mockUiDao,
					fsClient:             mockFsClient,
					userClient:           mockUserClient,
					employmentClient:     mockEmploymentClient,
					palLendabilityClient: mockPalClient,
					eventBroker:          mockEventBroker,
				})
			}
			s := &LendabilityProc{
				creditReportManager:  mockCrManager,
				featureStoreFactory:  mockFeatureStoreFactory,
				uiDao:                mockUiDao,
				userClient:           mockUserClient,
				employmentClient:     mockEmploymentClient,
				palLendabilityClient: mockPalClient,
				eventBroker:          mockEventBroker,
			}
			got, err := s.ProcessIntel(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				assert.Equalf(t, tt.want, got, "ProcessIntel(%v, %v)", tt.wantErr, got)
			}
		})
	}
}
