package model

import (
	"time"

	"github.com/epifi/be-common/api/typesv2/common"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	modelsPb "github.com/epifi/gamma/api/vkyccall/models"
	"github.com/epifi/gamma/vkyccall/types"
)

// todo (saiteja): remove this
type WaitingUserDetails struct {
	ActorId         string
	CallInitiatedAt time.Time
	ApplicationId   string
	LocationToken   *types.LocationToken
	IpToken         string
	IsVpnSet        bool
	OmegleCallId    string
	Isp             string
	// do not add new details
	// all fields here are already present in UserCallDetails
	IsDummyActor bool
}

func NewWaitingUserDetails(pb *modelsPb.WaitingUserDetails) *WaitingUserDetails {
	return &WaitingUserDetails{
		ActorId:         pb.GetActorId(),
		CallInitiatedAt: pb.GetCallInitiatedAt().AsTime(),
		ApplicationId:   pb.GetApplicationId(),
		LocationToken:   types.NewLocationToken(pb.GetLocationToken()),
		IpToken:         pb.GetIpToken(),
		IsVpnSet:        pb.GetIsVpnSet(),
		OmegleCallId:    pb.GetOmegleCallId(),
		Isp:             pb.GetIsp(),
		IsDummyActor:    pb.GetIsDummyActor().ToBool(),
	}
}

func (w *WaitingUserDetails) ToProto() *modelsPb.WaitingUserDetails {
	return &modelsPb.WaitingUserDetails{
		ActorId:         w.ActorId,
		CallInitiatedAt: timestamp.New(w.CallInitiatedAt),
		ApplicationId:   w.ApplicationId,
		IpToken:         w.IpToken,
		LocationToken:   w.LocationToken.ToProto(),
		IsVpnSet:        w.IsVpnSet,
		OmegleCallId:    w.OmegleCallId,
		Isp:             w.Isp,
		IsDummyActor:    common.BoolToBooleanEnum(w.IsDummyActor),
	}
}
