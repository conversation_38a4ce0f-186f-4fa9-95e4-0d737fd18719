package dao

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/wire"
	"github.com/jonboulle/clockwork"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	modelsPb "github.com/epifi/gamma/api/vkyccall/models"
	"github.com/epifi/gamma/vkyccall/config/genconf"
	"github.com/epifi/gamma/vkyccall/model"
)

var VkycCallRedisDaoWireSet = wire.NewSet(NewVkycCallRedisDao, wire.Bind(new(VkycCallDao), new(*VkycCallRedisDao)))

type VkycCallRedisDao struct {
	cacheStorage cache.CacheStorage
	conf         *genconf.Config
	clock        clockwork.Clock
}

/* DAO documentation:
Auditor flow (post agent approval)
AddReportToAuditorWaitQueue
MeetingId -> AuditorReview
MeetingId add to PendingRedisSetName
MeetingId append to PendingAuditReportsQueue

AssignReportToAuditor
MeetingId pop from PendingAuditReportsQueue
AuditorId -> MeetingId
MeetingId add to InProgressRedisSetName
*/

/*
Handling for dummy actor:
Dummy actor has to be whitelisted in config and only dummmy whitelisted agent or auditor can interact with dummy user

We populate IsDummyActor flag in WaitingUserDetails and UserCallDetails to identify a dummy actor
For dummyActor, instead of adding waitingUserDetails to CustomerQueue, we set it in cache and retrieve it from cache when agent passes dummyActorId in method options

We populate IsDummyReport flag in AuditorReviewDetails to identify a dummy report
For dummyReport, we ignore adding corresponding meetingId to PendingAuditReportsQueue.
To get the corresponding report, AssignReportToAuditor has method options to pass dummyActorId using which we fetch the respective report
*/

var _ VkycCallDao = &VkycCallRedisDao{}

func NewVkycCallRedisDao(cacheStorage cache.CacheStorage, conf *genconf.Config, clock clockwork.Clock) *VkycCallRedisDao {
	return &VkycCallRedisDao{
		cacheStorage: cacheStorage,
		conf:         conf,
		clock:        clock,
	}
}

func (r *VkycCallRedisDao) AddUserToWaitingQueue(ctx context.Context, details *model.WaitingUserDetails) error {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "AddUserToWaitingQueue", time.Now())
	waitingUserModel := details
	actorId := waitingUserModel.ActorId

	waitingUserModel.CallInitiatedAt = r.clock.Now()
	data, err := proto.Marshal(waitingUserModel.ToProto())
	if err != nil {
		return errors.Wrap(err, "error in marshalling model to string")
	}

	// The queue & the set need to persist for the entire lifetime, hence 0 expiration
	insertedCtr, err := r.cacheStorage.CreateOrAddToSet(ctx, r.conf.DefaultDataTTL(), r.conf.CustomerQueue().RedisSetName, actorId)
	if err != nil {
		return errors.Wrap(err, "failed to add user to waiting set")
	}
	if insertedCtr == 0 {
		return nil
	}
	if details.IsDummyActor {
		logger.Info(ctx, "dummy actor, not adding to waiting queue", zap.String(logger.ACTOR_ID_V2, details.ActorId))
		return r.addDummyWaitingUserDetailsInCache(ctx, details)
	}
	err = r.cacheStorage.CreateOrAppendToList(ctx, r.conf.DefaultDataTTL(), r.conf.CustomerQueue().QueueName, string(data))
	if err != nil {
		return errors.Wrap(err, "failed to append message to end of queue")
	}

	return nil
}

func (r *VkycCallRedisDao) GetWaitingQueueSize(ctx context.Context) (int, error) {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "GetWaitingQueueSize", time.Now())
	queueLen, err := r.cacheStorage.ListLength(ctx, r.conf.CustomerQueue().QueueName)
	if err != nil {
		return 0, errors.Wrap(err, "error in getting length of list")
	}
	return int(queueLen), nil
}

func (r *VkycCallRedisDao) GetFrontQueuedUser(ctx context.Context, opts ...*GetFrontQueuedUserOptions) (*model.WaitingUserDetails, error) {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "GetFrontQueuedUser", time.Now())
	var frontQueuedUserOpts *GetFrontQueuedUserOptions
	if len(opts) > 0 {
		frontQueuedUserOpts = opts[0]
	}
	if frontQueuedUserOpts.GetDummyActorId() != "" {
		logger.Info(ctx, "get dummy actor waitingUserDetails")
		return r.getDummyWaitingUserFromCache(ctx, frontQueuedUserOpts.GetDummyActorId())
	}
	// TODO: implement in transaction
	waitingUserRawStr, listErr := r.cacheStorage.PopFromListHead(ctx, r.conf.CustomerQueue().QueueName)
	if listErr != nil {
		return nil, errors.Wrap(listErr, "error getting queued user from the queue")
	}

	waitingUserProto := &modelsPb.WaitingUserDetails{}
	unmarshalErr := proto.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(waitingUserRawStr), waitingUserProto)
	if unmarshalErr != nil {
		return nil, errors.Wrap(unmarshalErr, "failed to unmarshal data into waiting user details")
	}

	removeErr := r.cacheStorage.RemoveFromSet(ctx, r.conf.CustomerQueue().RedisSetName, waitingUserProto.GetActorId())
	if removeErr != nil {
		// TODO: Put user back in queue if removing from set fails
		return nil, errors.Wrap(removeErr, "error removing waiting user from set")
	}
	return model.NewWaitingUserDetails(waitingUserProto), nil
}

func (r *VkycCallRedisDao) SetUserCallDetails(ctx context.Context, actorId string, callDetails *model.UserCallDetails) error {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "SetUserCallDetails", time.Now())
	userToCallDetailsKeyName := fmt.Sprintf("%s:%s", r.conf.CustomerQueue().UserToCallDetailsKeyPrefix, actorId)
	callIdToActorIdKeyName := fmt.Sprintf("%s:%s", r.conf.CustomerQueue().CallIdToActorIdKeyPrefix, callDetails.RoomId)

	rawBytes, marshalErr := proto.Marshal(callDetails.ToProto())
	if marshalErr != nil {
		return errors.Wrap(marshalErr, "error marshalling call details to bytes")
	}

	setErr := r.cacheStorage.MultiSet(ctx,
		[]string{userToCallDetailsKeyName, callIdToActorIdKeyName},
		[]string{string(rawBytes), actorId},
		[]time.Duration{r.conf.DefaultDataTTL(), r.conf.DefaultDataTTL()})
	if setErr != nil {
		return errors.Wrap(setErr, "error setting user call details in data store")
	}
	return nil
}

func (r *VkycCallRedisDao) GetCallDetailsForActor(ctx context.Context, actorId string) (*model.UserCallDetails, error) {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "GetCallDetailsForActor", time.Now())
	keyName := fmt.Sprintf("%s:%s", r.conf.CustomerQueue().UserToCallDetailsKeyPrefix, actorId)
	rawUserCallDetailsString, getErr := r.cacheStorage.Get(ctx, keyName)
	if getErr != nil {
		return nil, errors.Wrap(getErr, "error getting call details from data store")
	}

	userCallDetailsProto := &modelsPb.UserCallDetails{}
	unmarshalErr := proto.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(rawUserCallDetailsString), userCallDetailsProto)
	if unmarshalErr != nil {
		return nil, errors.Wrap(unmarshalErr, "error unmarshalling data to user call details")
	}
	return model.NewUserCallDetails(userCallDetailsProto), nil
}

func (r *VkycCallRedisDao) RemoveRoomDetailsForActor(ctx context.Context, actorId string, callId string) error {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "RemoveRoomDetailsForActor", time.Now())
	userToCallDetailsKeyName := fmt.Sprintf("%s:%s", r.conf.CustomerQueue().UserToCallDetailsKeyPrefix, actorId)
	callIdToActorIdKeyName := fmt.Sprintf("%s:%s", r.conf.CustomerQueue().CallIdToActorIdKeyPrefix, callId)
	return r.cacheStorage.Delete(ctx, userToCallDetailsKeyName, callIdToActorIdKeyName)
}

func (r *VkycCallRedisDao) GetActorForCallId(ctx context.Context, callId string) (string, error) {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "GetActorForCallId", time.Now())
	callIdToActorIdKeyName := fmt.Sprintf("%s:%s", r.conf.CustomerQueue().CallIdToActorIdKeyPrefix, callId)
	return r.cacheStorage.Get(ctx, callIdToActorIdKeyName)
}

func (r *VkycCallRedisDao) GetPendingAuditReportQueueSize(ctx context.Context) (int, error) {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "GetPendingAuditReportQueueSize", time.Now())
	queueLen, err := r.cacheStorage.ListLength(ctx, r.conf.Auditor().PendingAuditReportsQueue())
	if err != nil {
		return 0, errors.Wrap(err, "error in getting length of list")
	}
	return int(queueLen), nil
}

func (r *VkycCallRedisDao) AddReportToAuditorWaitQueue(ctx context.Context, details *model.AuditorReviewDetails) error {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "AddReportToAuditorWaitQueue", time.Now())

	rawBytes, marshalErr := proto.Marshal(details.ToProto())
	if marshalErr != nil {
		return errors.Wrap(marshalErr, "error in marshaling auditor review details")
	}

	// first check & set if an audit report entry with the same callId exists. if yes then don't proceed.
	nReportsAdded, getErr := r.cacheStorage.CreateOrAddToSet(ctx, r.conf.DefaultDataTTL(), r.conf.Auditor().PendingRedisSetName(), details.MeetingId)
	if getErr != nil {
		return errors.Wrap(getErr, "error in checking if audit report data exists")
	}
	if nReportsAdded == 0 {
		return errors.Wrap(epifierrors.ErrAlreadyExists, "audit report for callId: "+details.MeetingId+" already exists")
	}

	// TODO(Sundeep): This entire operation should be atomic
	// first store auditor details in redis
	// then add the call id to wait queue
	reportStoreErr := r.cacheStorage.Set(ctx, r.getKeyNameForAuditReportDetails(details.MeetingId), string(rawBytes), r.conf.DefaultDataTTL())
	if reportStoreErr != nil {
		return errors.Wrap(reportStoreErr, "error in storing audit report data")
	}
	if details.IsDummyReport {
		logger.Info(ctx, "ignore adding dummy report to pending queue", zap.String("omegle_call_id", details.OmegleCallId))
		return nil
	}
	addQueueErr := r.cacheStorage.CreateOrAppendToList(ctx, r.conf.DefaultDataTTL(), r.conf.Auditor().PendingAuditReportsQueue(), details.MeetingId)
	if addQueueErr != nil {
		return errors.Wrap(addQueueErr, "error in adding call details to wait queue")
	}
	return nil
}

func (r *VkycCallRedisDao) AssignReportToAuditor(ctx context.Context, auditorId string, opts ...*AssignReportToAuditorOptions) (*model.AuditorReviewDetails, error) {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "AssignReportToAuditor", time.Now())
	var daoMethodOptions *AssignReportToAuditorOptions
	if len(opts) > 0 {
		daoMethodOptions = opts[0]
	}
	// TODO(Sundeep): all the operations should be atomic
	meetingId, meetingIdErr := r.getReportForActiveCall(ctx, daoMethodOptions)
	if meetingIdErr != nil {
		return nil, errors.Wrap(meetingIdErr, "error in fetching auditor review report from redis")
	}

	data, dataErr := r.cacheStorage.Get(ctx, r.getKeyNameForAuditReportDetails(meetingId))
	if dataErr != nil {
		return nil, errors.Wrap(dataErr, "error in fetching auditor review report from redis")
	}

	auditorReviewReport := &modelsPb.AuditorReviewDetails{}
	unmarshalErr := proto.Unmarshal([]byte(data), auditorReviewReport)
	if unmarshalErr != nil {
		return nil, errors.Wrap(unmarshalErr, "error in unmarshalling auditor review report")
	}

	markInProgressErr := r.moveAuditReviewReportToInProgress(ctx, auditorReviewReport.GetMeetingId())
	if markInProgressErr != nil {
		return nil, errors.Wrap(markInProgressErr, "error in marking audit report as in progress")
	}

	// store the mapping of auditor ID to meeting ID, so that it can be retrieved later
	setAuditReportErr := r.cacheStorage.Set(ctx, r.getKeyNameForAuditorIdToCallIdMap(auditorId), meetingId, r.conf.DefaultDataTTL())
	if setAuditReportErr != nil {
		return nil, errors.Wrap(setAuditReportErr, "error in setting auditor id to meeting id mapping")
	}

	auditorReviewReport.ReviewStatus = modelsPb.AuditorReviewStatus_AUDITOR_REVIEW_STATUS_IN_PROGRESS
	auditorReviewReport.AuditorId = auditorId
	auditorReviewReport.AuditorAssignedTime = timestamppb.New(r.clock.Now())

	setReportErr := r.setAuditorReviewReport(ctx, meetingId, auditorReviewReport)
	if setReportErr != nil {
		return nil, errors.Wrap(setReportErr, "error in updating auditor review report to in-progress state")
	}

	// Add report back to pending reports even for in progress status, so that we can resassign if auditor drops off without reviewing the report
	addQueueErr := r.cacheStorage.CreateOrAppendToList(ctx, r.conf.DefaultDataTTL(), r.conf.Auditor().PendingAuditReportsQueue(), auditorReviewReport.GetMeetingId())
	if addQueueErr != nil {
		return nil, errors.Wrap(addQueueErr, "error in adding call details to wait queue")
	}

	return model.NewAuditorReviewDetails(auditorReviewReport), nil
}

// getReportForActiveCall returns report for an active call, it is possible that user starts a new call post which we need to deprecate the old report
// nolint: funlen
// todo (saiteja): add UTs for this method, figure out a more better way to handle this
func (r *VkycCallRedisDao) getReportForActiveCall(ctx context.Context, daoMethodOptions *AssignReportToAuditorOptions) (string, error) {
	if daoMethodOptions.GetDummyActorId() != "" {
		callDetails, err := r.GetCallDetailsForActor(ctx, daoMethodOptions.GetDummyActorId())
		if err != nil {
			return "", errors.Wrap(err, "error in getting call details for actor")
		}
		logger.Info(ctx, "returning meetingId for dummy actor")
		return callDetails.RoomId, nil
	}
	firstMeetingId := ""
	for {
		meetingId, meetingIdErr := r.cacheStorage.PopFromListHead(ctx, r.conf.Auditor().PendingAuditReportsQueue())
		if meetingIdErr != nil {
			return "", errors.Wrap(meetingIdErr, "error in fetching meetingId")
		}

		auditReport, getReportErr := r.GetAuditReportForCall(ctx, meetingId)
		if getReportErr != nil {
			return "", errors.Wrap(getReportErr, "error in fetching auditor review report from redis")
		}
		logger.Info(ctx, "popped meeting id", zap.String(logger.OZONETEL_CALLER_ID, meetingId), zap.String(logger.STATUS, auditReport.ReviewStatus.String()))

		if firstMeetingId == meetingId {
			logger.Info(ctx, "reached end of queue, no active reports found")
			// add report back to queue
			addQueueErr := r.addReportBackToAuditorQueue(ctx, meetingId)
			if addQueueErr != nil {
				return "", addQueueErr
			}
			return "", epifierrors.ErrRecordNotFound
		}

		if firstMeetingId == "" {
			firstMeetingId = meetingId
		}

		switch auditReport.ReviewStatus {
		case modelsPb.AuditorReviewStatus_AUDITOR_REVIEW_STATUS_IN_PROGRESS:
			logger.Info(ctx, "time since assigned to auditor", zap.Duration(logger.DURATION, time.Since(auditReport.AssignedTime)))
			if time.Since(auditReport.AssignedTime) > r.conf.Auditor().AuditorSessionDuration() {
				logger.Info(ctx, "session expired for auditor, reassigning to a different auditor", zap.String(logger.OZONETEL_CALLER_ID, meetingId))
				return meetingId, nil
			}
			// Report review in progress, add report back to pending reports queue
			addQueueErr := r.addReportBackToAuditorQueue(ctx, meetingId)
			if addQueueErr != nil {
				return "", addQueueErr
			}
			continue
		case modelsPb.AuditorReviewStatus_AUDITOR_REVIEW_STATUS_APPROVED, modelsPb.AuditorReviewStatus_AUDITOR_REVIEW_STATUS_REJECTED:
			// report reached terminal state, check for next report
			continue
		case modelsPb.AuditorReviewStatus_AUDITOR_REVIEW_STATUS_PENDING:
			// we remove report from pending redis set in case of call reset so that it won't be picked for auditor review
			// todo: handle case when report is assigned to auditor
			isMember, err := r.cacheStorage.IsSetMember(ctx, r.conf.Auditor().PendingRedisSetName(), meetingId)
			if err != nil {
				return "", errors.Wrap(err, "error in checking if meeting is part of set")
			}
			if !isMember {
				logger.Info(ctx, "call is not part of pending set considering call as not active", zap.String(logger.OZONETEL_CALLER_ID, meetingId))
				continue
			}
			logger.Info(ctx, "call is part of pending set considering call as active", zap.String(logger.OZONETEL_CALLER_ID, meetingId))
		default:
			logger.Info(ctx, "invalid review status", zap.String(logger.OZONETEL_CALLER_ID, meetingId))
			return "", fmt.Errorf("invalid review status %v", auditReport.ReviewStatus)
		}

		return meetingId, nil
	}
}

func (r *VkycCallRedisDao) addReportBackToAuditorQueue(ctx context.Context, meetingId string) error {
	addQueueErr := r.cacheStorage.CreateOrAppendToList(ctx, r.conf.DefaultDataTTL(), r.conf.Auditor().PendingAuditReportsQueue(), meetingId)
	if addQueueErr != nil {
		return errors.Wrap(addQueueErr, "error in adding auditor review details back to wait queue")
	}
	return nil
}

func (r *VkycCallRedisDao) moveAuditReviewReportToInProgress(ctx context.Context, meetingId string) error {
	nAdded, addToSetErr := r.cacheStorage.CreateOrAddToSet(ctx, r.conf.DefaultDataTTL(), r.conf.Auditor().InProgressRedisSetName(), meetingId)
	if addToSetErr != nil {
		return errors.Wrap(addToSetErr, "error in add call id to in progress redis set")
	}
	if nAdded <= 0 {
		logger.Info(ctx, "auditor review already in progress", zap.String(logger.OZONETEL_CALLER_ID, meetingId))
	}

	removeErr := r.cacheStorage.RemoveFromSet(ctx, r.conf.Auditor().PendingRedisSetName(), meetingId)
	if removeErr != nil {
		// TODO(Sundeep): Revert addition to in-progress set if removal from pending set fails
		return errors.Wrap(removeErr, "error in removing call id from in progress set")
	}
	return nil
}

func (r *VkycCallRedisDao) GetAuditReportForAuditor(ctx context.Context, auditorId string) (*model.AuditorReviewDetails, error) {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "GetAuditReportForAuditor", time.Now())
	callId, callIdErr := r.cacheStorage.Get(ctx, r.getKeyNameForAuditorIdToCallIdMap(auditorId))
	if callIdErr != nil {
		return nil, errors.Wrap(callIdErr, "error in retrieving callId from auditorId in redis")
	}

	return r.GetAuditReportForCall(ctx, callId)
}

func (r *VkycCallRedisDao) GetAuditReportForCall(ctx context.Context, callID string) (*model.AuditorReviewDetails, error) {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "GetAuditReportForCall", time.Now())
	rawAuditReport, rawAuditReportErr := r.cacheStorage.Get(ctx, r.getKeyNameForAuditReportDetails(callID))
	if rawAuditReportErr != nil {
		return nil, errors.Wrap(rawAuditReportErr, "error in fetching audit report data from redis")
	}

	auditReportData := &modelsPb.AuditorReviewDetails{}
	unmarshalErr := proto.Unmarshal([]byte(rawAuditReport), auditReportData)
	if unmarshalErr != nil {
		return nil, errors.Wrap(unmarshalErr, "error in unmarshalling audit report data")
	}

	return model.NewAuditorReviewDetails(auditReportData), nil
}

func (r *VkycCallRedisDao) setAuditorReviewReport(ctx context.Context, meetingId string, report *modelsPb.AuditorReviewDetails) error {
	marshaledData, marshalErr := proto.Marshal(report)
	if marshalErr != nil {
		return errors.Wrap(marshalErr, "error in marshaling auditor review report")
	}

	setDataErr := r.cacheStorage.Set(ctx, r.getKeyNameForAuditReportDetails(meetingId), string(marshaledData), r.conf.DefaultDataTTL())
	if setDataErr != nil {
		return errors.Wrap(setDataErr, "error in setting auditor review data in redis")
	}
	return nil
}

func (r *VkycCallRedisDao) ConcludeAuditorReview(ctx context.Context, auditorId string, report *model.AuditorReviewDetails) error {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "ConcludeAuditorReview", time.Now())
	callId, callIdErr := r.cacheStorage.Get(ctx, r.getKeyNameForAuditorIdToCallIdMap(auditorId))
	if callIdErr != nil {
		return errors.Wrap(callIdErr, "error in retrieving callId from auditorId in redis")
	}

	setReportErr := r.setAuditorReviewReport(ctx, callId, report.ToProto())
	if setReportErr != nil {
		return errors.Wrap(setReportErr, "error in setting auditor review report")
	}

	removeErr := r.RemoveMeetingIdFromAuditInProgress(ctx, callId)
	if removeErr != nil {
		return errors.Wrap(removeErr, "error in removing auditor from in-progress set")
	}
	return nil
}

func (r *VkycCallRedisDao) getKeyNameForAuditorIdToCallIdMap(auditorId string) string {
	return r.conf.Auditor().AudtiorIdToReportIdMapPrefix() + auditorId
}

func (r *VkycCallRedisDao) getKeyNameForAuditReportDetails(callId string) string {
	return r.conf.Auditor().AuditReportDataPrefix() + callId
}

func (r *VkycCallRedisDao) RemoveMeetingIdFromAuditInProgress(ctx context.Context, meetingId string) error {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "RemoveMeetingIdFromAuditInProgress", time.Now())
	removeErr := r.cacheStorage.RemoveFromSet(ctx, r.conf.Auditor().InProgressRedisSetName(), meetingId)
	if removeErr != nil {
		return errors.Wrap(removeErr, "error in removing meeting id from in-progress set")
	}
	return nil
}

func (r *VkycCallRedisDao) RemoveMeetingIdFromAuditInPending(ctx context.Context, meetingId string) error {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "RemoveMeetingIdFromAuditInPending", time.Now())
	removeErr := r.cacheStorage.RemoveFromSet(ctx, r.conf.Auditor().PendingRedisSetName(), meetingId)
	if removeErr != nil {
		return errors.Wrap(removeErr, "error in removing meeting id from in-progress set")
	}
	return nil
}

func (r *VkycCallRedisDao) SetUserOnline(ctx context.Context, actorId string) error {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "SetUserOnline", time.Now())
	if err := r.cacheStorage.Set(ctx, getUserOnlineKey(actorId), "", r.conf.UserOnlineTTL()); err != nil {
		return errors.Wrap(err, "error in setting user online")
	}
	return nil
}

func (r *VkycCallRedisDao) IsUserOnline(ctx context.Context, actorId string) (bool, error) {
	defer metric_util.TrackDuration("vkyccall/dao", "VkycCallRedisDao", "IsUserOnline", time.Now())
	if _, err := r.cacheStorage.Get(ctx, getUserOnlineKey(actorId)); err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

func getUserOnlineKey(actorId string) string {
	return "user_online:" + actorId
}

func (r *VkycCallRedisDao) addDummyWaitingUserDetailsInCache(ctx context.Context, waitingUserDetails *model.WaitingUserDetails) error {
	detailsBytes, err := json.Marshal(waitingUserDetails)
	if err != nil {
		return errors.Wrap(err, "error in marshalling waiting user details")
	}
	if err = r.cacheStorage.Set(ctx, getDummyWaitingUserDetailsKey(waitingUserDetails.ActorId), string(detailsBytes), r.conf.DefaultDataTTL()); err != nil {
		return errors.Wrap(err, "error in storing waiting user details in cache")
	}
	return nil
}

func (r *VkycCallRedisDao) getDummyWaitingUserFromCache(ctx context.Context, actorId string) (*model.WaitingUserDetails, error) {
	waitingUserDetailsStr, err := r.cacheStorage.Get(ctx, getDummyWaitingUserDetailsKey(actorId))
	if err != nil {
		return nil, errors.Wrap(err, "error in storing waiting user details in cache")
	}
	var waitingUserDetails *model.WaitingUserDetails
	if err = json.Unmarshal([]byte(waitingUserDetailsStr), &waitingUserDetails); err != nil {
		return nil, errors.Wrap(err, "error in marshalling waiting user details")
	}
	return waitingUserDetails, nil
}

func getDummyWaitingUserDetailsKey(actorId string) string {
	return "dummy_waiting_user_details:" + actorId
}
