//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"
	"github.com/jonboulle/clockwork"

	"github.com/epifi/gamma/vkyccall/troubleshoot"
	sgTypes "github.com/epifi/gamma/vkyccall/wire/stockguardian/types"

	"github.com/epifi/be-common/pkg/cache"

	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/gamma/pkg/pubsub"
	"github.com/epifi/gamma/pkg/pubsub/redis"
	"github.com/epifi/gamma/vkyccall"
	"github.com/epifi/gamma/vkyccall/config/genconf"
	"github.com/epifi/gamma/vkyccall/dao"
	"github.com/epifi/gamma/vkyccall/developer"
	"github.com/epifi/gamma/vkyccall/developer/processor"
	"github.com/epifi/gamma/vkyccall/event"
	"github.com/epifi/gamma/vkyccall/serviceprovider"
	"github.com/epifi/gamma/vkyccall/wire/types"
)

func newRealClockProvider() clockwork.Clock {
	return clockwork.NewRealClock()
}

//go:generate wire
func InitializeVKYCCallService(conf *genconf.Config, vkyccallRedisStore types.VkycCallRedisStore, callDataS3Client types.CallDataStorageS3Client, vkycCallVgClient sgTypes.VgVKYCCallClientToVendorNotificationServer, omegleClient sgTypes.OmegleClientToAurorServer, locationClient sgTypes.UserLocationClientToVendorNotificationServer,
	authLocClient sgTypes.AuthLocationClientToVendorNotificationServer, obfuscatorClient sgTypes.ObfuscatorClientToVendorNotificationServer, matcherClient sgTypes.OmegleMatcherClientToAurorServer, omegleOcrClient sgTypes.OmegleOcrClientToAurorServer, vgOcrClient sgTypes.VgOcrClientToVendorNotificationServer,
	onbClient sgTypes.OnbClientToVendorNotificationServer, broker events.Broker) (*vkyccall.Service, error) {
	wire.Build(
		newRealClockProvider,
		types.VkycCallRedisStoreProvider,
		types.CallDataStorageS3ClientProvider,
		cache.RedisStorageWireSet,
		RedisPubSubProvider,
		dao.VkycCallRedisDaoWireSet,
		vkyccall.NewVKYCService,
		wire.Bind(new(event.EventLogger), new(*event.VkycCallEventLogger)),
		event.NewVkycCallEventLogger,
		sgTypes.UserLocationClientToVendorNotificationServerProvider,
		sgTypes.AuthLocationClientToVendorNotificationServerProvider,
		sgTypes.VgVKYCCallClientToVendorNotificationServerProvider,
		sgTypes.ObfuscatorClientToVendorNotificationServerProvider,
		sgTypes.OmegleClientToAurorServerProvider,
		sgTypes.OmegleMatcherClientToAurorServerProvider,
		sgTypes.OmegleOcrClientToAurorServerProvider,
		sgTypes.VgOcrClientToVendorNotificationServerProvider,
		sgTypes.OnbClientToVendorNotificationServerProvider,
		serviceprovider.NewSelector,
		serviceprovider.NewStockGuardianMatrixServiceProvider,
		serviceprovider.NewFederalNRIQatarServiceProvider,
		serviceprovider.NewFederalNRIUAEServiceProvider,
		serviceprovider.NewDefaultServiceProvider,
	)
	return &vkyccall.Service{}, nil
}

func InitializeVKYCCallTroubleshootService(conf *genconf.Config, vkyccallRedisStore types.VkycCallRedisStore) (*troubleshoot.Service, error) {
	wire.Build(
		newRealClockProvider,
		types.VkycCallRedisStoreProvider,
		cache.RedisStorageWireSet,
		dao.VkycCallRedisDaoWireSet,
		troubleshoot.NewService,
	)
	return &troubleshoot.Service{}, nil
}

func RedisPubSubProvider(conf *genconf.Config, redisStore types.VkycCallRedisStore) pubsub.PubSub {
	return redis.NewGoRedisPubSub(redisStore, conf.CustomerQueue().LocalBufferSize)
}

func InitialiseDevVkycCallService(vkyccallRedisStore types.VkycCallRedisStore, conf *genconf.Config) *developer.DevVkycCallService {
	wire.Build(
		newRealClockProvider,
		types.VkycCallRedisStoreProvider,
		cache.RedisStorageWireSet,
		dao.VkycCallRedisDaoWireSet,
		developer.NewDevVkycCallService,
		developer.NewDevFactory,
		processor.NewAuditorReviewDetailsProcessor,
		processor.NewCallDetailProcessor,
		processor.NewCallIdToActorIdProcessor,
	)
	return &developer.DevVkycCallService{}
}
