Application:
  Environment: "test"
  Name: "budgeting"

Aws:
  Region: "ap-south-1"


Flags:
  TrimDebugMessageFromStatus: false

Server:
  Ports:
    GrpcPort: 8005
    GrpcSecurePort: 9517
    HttpPort: 9092

ReminderProcessorSubscriber:
  StartOnServerStart: true
  NumWorkers: 3
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "reminder-subscription-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "reminder-subscription-event"

WhatsAppReminderTime:
  CommunicationDailyCron: "5 8 * * *"

RmsEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 3
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "budgeting-reminder-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "rms-budgeting-reminder"


CategorizerBudgetingEventsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "reminder-category-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "reminder-category"

ExecuteReminderActionSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "reminder-action-executor-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "reminder-action"

ReminderProcessorPublisher:
  QueueName: "reminder-subscription-event-consumer-queue"

RMSEventPublisher:
  QueueName : "rms-event-queue"

ReminderActionExecutorPublisher:
  QueueName: "reminder-action-executor-queue"

Secrets:
  Ids:
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    DbCredentials: "{\"username\": \"root\", \"password\": \"\"}"


Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

BudgetingDb:
  AppName: "budgeting"
  StatementTimeout: 5m
  Name: "budgeting_test"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

IsCcDueReminderSmsDisabled: true
