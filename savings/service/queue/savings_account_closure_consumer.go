package queue

import (
	"context"

	"go.uber.org/zap"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/gamma/api/pay/savings_account/consumer"
	saConsumerPb "github.com/epifi/gamma/api/savings/consumer"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/savings/data"
)

type SaClosureClosureConsumerService struct {
	dataProcessor data.ISaClosureDataProcessor
}

func NewSaClosureClosureConsumerService(dataProcessor data.ISaClosureDataProcessor) *SaClosureClosureConsumerService {
	return &SaClosureClosureConsumerService{dataProcessor: dataProcessor}
}

var (
	permanentFailureStatus = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
	transientFailureStatus = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
	successStatus          = queuePb.MessageConsumptionStatus_SUCCESS
)

func (s *SaClosureClosureConsumerService) ProcessBalanceUpdateEvent(ctx context.Context, update *consumer.BalanceUpdate) (*saConsumerPb.ProcessBalanceUpdateEventResponse, error) {
	actorId := update.GetActorId()
	if actorId == "" {
		logger.Error(ctx, "actor id cannot be empty")
		return &saConsumerPb.ProcessBalanceUpdateEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: permanentFailureStatus},
		}, nil
	}

	autoCancelErr := s.dataProcessor.AutoCancelSaClosureRequest(ctx, actorId)
	if autoCancelErr != nil {
		logger.Error(ctx, "failed to auto cancel sa closure request", zap.Error(autoCancelErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &saConsumerPb.ProcessBalanceUpdateEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{Status: transientFailureStatus},
		}, nil
	}

	return &saConsumerPb.ProcessBalanceUpdateEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
	}, nil
}
