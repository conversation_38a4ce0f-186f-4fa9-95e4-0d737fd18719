package transactionAggregate

import (
	"context"
	"sync"

	"github.com/golang/protobuf/ptypes/timestamp"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/gamma/api/accounts"
	bePaymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type PayTxnAggregate struct {
	payClient payPb.PayClient
}

func NewPayTxnAggregate(payClient payPb.PayClient) *PayTxnAggregate {
	return &PayTxnAggregate{
		payClient: payClient,
	}

}

// buildTxnAggregrateRequest creates request for pay txn aggregrates RPC request
func buildTxnAggregrateRequest(actorId string, fromTime, toTime *timestamp.Timestamp, transactionType bePaymentPb.AccountingEntryType, accountTypes []accounts.Type, piIds []string) *payPb.GetTransactionAggregatesRequest {
	return &payPb.GetTransactionAggregatesRequest{
		ActorId:             actorId,
		FromTime:            fromTime,
		ToTime:              toTime,
		AccountingEntryType: transactionType,
		AccountTypes:        accountTypes,
		PiIds:               piIds,
		TransactionsStatus:  []bePaymentPb.TransactionStatus{bePaymentPb.TransactionStatus_SUCCESS},
	}
}

// It calculates credit and debit transaction aggregates using pay GetTransactionAggregates RPC which is powered by pinot
func (p *PayTxnAggregate) CalculateTransactionAggregate(ctx context.Context, aggregateRequest *TxnAggregateRequestParams) (*moneyPb.Money, *moneyPb.Money, error) {
	wg := &sync.WaitGroup{}
	wg.Add(2)
	var (
		currentTimeRangeCredit *payPb.GetTransactionAggregatesResponse
		currentTimeRangeDebit  *payPb.GetTransactionAggregatesResponse
		txnAggErr              error
		finalErr               error
	)
	goroutine.RunWithCtx(ctx, func(ctx context.Context) {
		currentTimeRangeCredit, txnAggErr = p.payClient.GetTransactionAggregates(ctx, buildTxnAggregrateRequest(aggregateRequest.ActorId, aggregateRequest.FromTime, aggregateRequest.ToTime, bePaymentPb.AccountingEntryType_CREDIT, []accounts.Type{accounts.Type_SAVINGS}, aggregateRequest.PiIds))
		if rpcError := epifigrpc.RPCError(currentTimeRangeCredit, txnAggErr); rpcError != nil {
			logger.Error(ctx, "error in fetching credit transaction aggregate", zap.Error(rpcError), zap.String(logger.ACTOR_ID_V2, aggregateRequest.ActorId))
			finalErr = txnAggErr
			return
		}
		wg.Done()
	})
	goroutine.RunWithCtx(ctx, func(ctx context.Context) {
		currentTimeRangeDebit, txnAggErr = p.payClient.GetTransactionAggregates(ctx, buildTxnAggregrateRequest(aggregateRequest.ActorId, aggregateRequest.FromTime, aggregateRequest.ToTime, bePaymentPb.AccountingEntryType_DEBIT, []accounts.Type{accounts.Type_SAVINGS}, aggregateRequest.PiIds))
		if rpcError := epifigrpc.RPCError(currentTimeRangeDebit, txnAggErr); rpcError != nil {
			logger.Error(ctx, "error in fetching debit transaction aggregate", zap.Error(rpcError), zap.String(logger.ACTOR_ID_V2, aggregateRequest.ActorId))
			finalErr = txnAggErr
			return
		}
		wg.Done()
	})
	wg.Wait()
	return currentTimeRangeCredit.GetTransactionAggregates().GetSumAmount(), currentTimeRangeDebit.GetTransactionAggregates().GetSumAmount(), finalErr
}
