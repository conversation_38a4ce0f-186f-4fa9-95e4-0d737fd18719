// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/github.com/epifi/gamma/savings/service/transactionAggregate/transaction_aggregate.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	transactionAggregate "github.com/epifi/gamma/savings/service/transactionAggregate"
	gomock "github.com/golang/mock/gomock"
	money "google.golang.org/genproto/googleapis/type/money"
)

// MockTransactionAggregate is a mock of TransactionAggregate interface.
type MockTransactionAggregate struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionAggregateMockRecorder
}

// MockTransactionAggregateMockRecorder is the mock recorder for MockTransactionAggregate.
type MockTransactionAggregateMockRecorder struct {
	mock *MockTransactionAggregate
}

// NewMockTransactionAggregate creates a new mock instance.
func NewMockTransactionAggregate(ctrl *gomock.Controller) *MockTransactionAggregate {
	mock := &MockTransactionAggregate{ctrl: ctrl}
	mock.recorder = &MockTransactionAggregateMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransactionAggregate) EXPECT() *MockTransactionAggregateMockRecorder {
	return m.recorder
}

// CalculateTransactionAggregate mocks base method.
func (m *MockTransactionAggregate) CalculateTransactionAggregate(ctx context.Context, aggregateRequest *transactionAggregate.TxnAggregateRequestParams) (*money.Money, *money.Money, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculateTransactionAggregate", ctx, aggregateRequest)
	ret0, _ := ret[0].(*money.Money)
	ret1, _ := ret[1].(*money.Money)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CalculateTransactionAggregate indicates an expected call of CalculateTransactionAggregate.
func (mr *MockTransactionAggregateMockRecorder) CalculateTransactionAggregate(ctx, aggregateRequest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculateTransactionAggregate", reflect.TypeOf((*MockTransactionAggregate)(nil).CalculateTransactionAggregate), ctx, aggregateRequest)
}
