// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/github.com/epifi/gamma/usstocks/account/suitability_questions/question_manager.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	account "github.com/epifi/gamma/api/usstocks/account"
	suitability_questions "github.com/epifi/gamma/usstocks/account/suitability_questions"
	gomock "github.com/golang/mock/gomock"
)

// MockISuitabilityQuestionManager is a mock of ISuitabilityQuestionManager interface.
type MockISuitabilityQuestionManager struct {
	ctrl     *gomock.Controller
	recorder *MockISuitabilityQuestionManagerMockRecorder
}

// MockISuitabilityQuestionManagerMockRecorder is the mock recorder for MockISuitabilityQuestionManager.
type MockISuitabilityQuestionManagerMockRecorder struct {
	mock *MockISuitabilityQuestionManager
}

// NewMockISuitabilityQuestionManager creates a new mock instance.
func NewMockISuitabilityQuestionManager(ctrl *gomock.Controller) *MockISuitabilityQuestionManager {
	mock := &MockISuitabilityQuestionManager{ctrl: ctrl}
	mock.recorder = &MockISuitabilityQuestionManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISuitabilityQuestionManager) EXPECT() *MockISuitabilityQuestionManagerMockRecorder {
	return m.recorder
}

// CalculateScore mocks base method.
func (m *MockISuitabilityQuestionManager) CalculateScore(ctx context.Context, questionAndAnswerMap map[account.SuitabilityQuestionType]*account.SuitabilityAnswer) (int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculateScore", ctx, questionAndAnswerMap)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculateScore indicates an expected call of CalculateScore.
func (mr *MockISuitabilityQuestionManagerMockRecorder) CalculateScore(ctx, questionAndAnswerMap interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculateScore", reflect.TypeOf((*MockISuitabilityQuestionManager)(nil).CalculateScore), ctx, questionAndAnswerMap)
}

// GetQuestions mocks base method.
func (m *MockISuitabilityQuestionManager) GetQuestions(ctx context.Context, questionTypes []account.SuitabilityQuestionType, actorId string) (*suitability_questions.GetQuestionsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQuestions", ctx, questionTypes, actorId)
	ret0, _ := ret[0].(*suitability_questions.GetQuestionsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQuestions indicates an expected call of GetQuestions.
func (mr *MockISuitabilityQuestionManagerMockRecorder) GetQuestions(ctx, questionTypes, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuestions", reflect.TypeOf((*MockISuitabilityQuestionManager)(nil).GetQuestions), ctx, questionTypes, actorId)
}

// ValidateAnswers mocks base method.
func (m *MockISuitabilityQuestionManager) ValidateAnswers(ctx context.Context, questionAndAnswerMap map[account.SuitabilityQuestionType]*account.SuitabilityAnswer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateAnswers", ctx, questionAndAnswerMap)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateAnswers indicates an expected call of ValidateAnswers.
func (mr *MockISuitabilityQuestionManagerMockRecorder) ValidateAnswers(ctx, questionAndAnswerMap interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateAnswers", reflect.TypeOf((*MockISuitabilityQuestionManager)(nil).ValidateAnswers), ctx, questionAndAnswerMap)
}
