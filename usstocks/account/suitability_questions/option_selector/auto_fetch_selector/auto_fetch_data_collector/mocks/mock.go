// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/github.com/epifi/gamma/usstocks/account/suitability_questions/option_selector/auto_fetch_selector/auto_fetch_data_collector/auto_fetch_data_collector.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	account "github.com/epifi/gamma/api/usstocks/account"
	auto_fetch_data_collector "github.com/epifi/gamma/usstocks/account/suitability_questions/option_selector/auto_fetch_selector/auto_fetch_data_collector"
	gomock "github.com/golang/mock/gomock"
)

// MockIAutoFetchDataCollector is a mock of IAutoFetchDataCollector interface.
type MockIAutoFetchDataCollector struct {
	ctrl     *gomock.Controller
	recorder *MockIAutoFetchDataCollectorMockRecorder
}

// MockIAutoFetchDataCollectorMockRecorder is the mock recorder for MockIAutoFetchDataCollector.
type MockIAutoFetchDataCollectorMockRecorder struct {
	mock *MockIAutoFetchDataCollector
}

// NewMockIAutoFetchDataCollector creates a new mock instance.
func NewMockIAutoFetchDataCollector(ctrl *gomock.Controller) *MockIAutoFetchDataCollector {
	mock := &MockIAutoFetchDataCollector{ctrl: ctrl}
	mock.recorder = &MockIAutoFetchDataCollectorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAutoFetchDataCollector) EXPECT() *MockIAutoFetchDataCollectorMockRecorder {
	return m.recorder
}

// GetOptions mocks base method.
func (m *MockIAutoFetchDataCollector) GetOptions(ctx context.Context, actorId string) (*auto_fetch_data_collector.GetOptionsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOptions", ctx, actorId)
	ret0, _ := ret[0].(*auto_fetch_data_collector.GetOptionsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOptions indicates an expected call of GetOptions.
func (mr *MockIAutoFetchDataCollectorMockRecorder) GetOptions(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOptions", reflect.TypeOf((*MockIAutoFetchDataCollector)(nil).GetOptions), ctx, actorId)
}

// GetType mocks base method.
func (m *MockIAutoFetchDataCollector) GetType() account.AutoFetchOptionType {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetType")
	ret0, _ := ret[0].(account.AutoFetchOptionType)
	return ret0
}

// GetType indicates an expected call of GetType.
func (mr *MockIAutoFetchDataCollectorMockRecorder) GetType() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetType", reflect.TypeOf((*MockIAutoFetchDataCollector)(nil).GetType))
}

// GoExpressionParamMap mocks base method.
func (m *MockIAutoFetchDataCollector) GoExpressionParamMap(answer *account.SuitabilityAnswer) (map[string]interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GoExpressionParamMap", answer)
	ret0, _ := ret[0].(map[string]interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GoExpressionParamMap indicates an expected call of GoExpressionParamMap.
func (mr *MockIAutoFetchDataCollectorMockRecorder) GoExpressionParamMap(answer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GoExpressionParamMap", reflect.TypeOf((*MockIAutoFetchDataCollector)(nil).GoExpressionParamMap), answer)
}

// ValidateAnswer mocks base method.
func (m *MockIAutoFetchDataCollector) ValidateAnswer(answer *account.SuitabilityAnswer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateAnswer", answer)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateAnswer indicates an expected call of ValidateAnswer.
func (mr *MockIAutoFetchDataCollectorMockRecorder) ValidateAnswer(answer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateAnswer", reflect.TypeOf((*MockIAutoFetchDataCollector)(nil).ValidateAnswer), answer)
}
