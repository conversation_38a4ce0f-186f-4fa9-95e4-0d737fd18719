package catalog

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	usstocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) UpdateWatchlist(
	ctx context.Context,
	req *usstocksCatalogPb.UpdateWatchlistRequest,
) (*usstocksCatalogPb.UpdateWatchlistResponse, error) {
	var (
		res = &usstocksCatalogPb.UpdateWatchlistResponse{}
	)
	stockId := req.GetStockId()
	watchlistId, err := s.createOrGetWatchlistIdToUpdate(ctx, req)
	if err != nil {
		logger.Error(ctx, "failed to call createOrGetWatchlist", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	switch req.GetAction() {
	case usstocksCatalogPb.WatchlistAction_WATCHLIST_ACTION_ADD:
		_, err := s.watchlistStockMappingDao.Create(ctx, &usstocksCatalogPb.WatchlistStockMapping{
			WatchlistId: watchlistId,
			StockId:     stockId,
		})
		if err != nil {
			if errors.Is(err, epifierrors.ErrDuplicateEntry) {
				return &usstocksCatalogPb.UpdateWatchlistResponse{
					Status: &rpc.Status{
						Code: uint32(usstocksCatalogPb.UpdateWatchlistResponse_STOCK_ALREADY_PRESENT),
					},
				}, nil
			}
			logger.Error(ctx, "failed to call watchlistStockMappingDao.Create", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	case usstocksCatalogPb.WatchlistAction_WATCHLIST_ACTION_REMOVE:
		err := s.watchlistStockMappingDao.Delete(ctx, watchlistId, stockId)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRowNotUpdated) {
				return &usstocksCatalogPb.UpdateWatchlistResponse{
					Status: &rpc.Status{
						Code: uint32(usstocksCatalogPb.UpdateWatchlistResponse_STOCK_NOT_PRESENT),
					},
				}, nil
			}
			logger.Error(ctx, "failed to call watchlistStockMappingDao.Delete", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	default:
		logger.Error(ctx, "invalid action", zap.Any("action", req.GetAction()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Status = rpc.StatusOk()
	return res, nil
}

// createOrGetWatchlistIdToUpdate returns the watchlist id to update based on the request.
// if actor doesn't have any watchlist, it will create one and return the id
// if actor already have a watchlist, it will return the id
func (s *Service) createOrGetWatchlistIdToUpdate(
	ctx context.Context,
	req *usstocksCatalogPb.UpdateWatchlistRequest,
) (string, error) {
	switch req.GetIdentifier().(type) {
	case *usstocksCatalogPb.UpdateWatchlistRequest_WatchlistId:
		// if watchlist id is provided, return it
		return req.GetWatchlistId(), nil
	case *usstocksCatalogPb.UpdateWatchlistRequest_ActorId:
		watchlist, err := s.watchlistDao.CreateOrGetByActorId(ctx, &usstocksCatalogPb.Watchlist{
			ActorId: req.GetActorId(),
		})
		if err != nil {
			return "", fmt.Errorf("failed to call watchlistDao.CreateOrGetByActorId: %w", err)
		}
		return watchlist.GetId(), nil
	default:
		return "", fmt.Errorf("invalid identifier: %v", req.GetIdentifier())
	}
}
