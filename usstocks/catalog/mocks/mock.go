// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	catalog "github.com/epifi/gamma/api/usstocks/catalog"
	gomock "github.com/golang/mock/gomock"
)

// MockIGetStocksUtil is a mock of IGetStocksUtil interface.
type MockIGetStocksUtil struct {
	ctrl     *gomock.Controller
	recorder *MockIGetStocksUtilMockRecorder
}

// MockIGetStocksUtilMockRecorder is the mock recorder for MockIGetStocksUtil.
type MockIGetStocksUtilMockRecorder struct {
	mock *MockIGetStocksUtil
}

// NewMockIGetStocksUtil creates a new mock instance.
func NewMockIGetStocksUtil(ctrl *gomock.Controller) *MockIGetStocksUtil {
	mock := &MockIGetStocksUtil{ctrl: ctrl}
	mock.recorder = &MockIGetStocksUtilMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIGetStocksUtil) EXPECT() *MockIGetStocksUtilMockRecorder {
	return m.recorder
}

// GetStocks mocks base method.
func (m *MockIGetStocksUtil) GetStocks(ctx context.Context, req *catalog.GetStocksRequest) (*catalog.GetStocksResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStocks", ctx, req)
	ret0, _ := ret[0].(*catalog.GetStocksResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStocks indicates an expected call of GetStocks.
func (mr *MockIGetStocksUtilMockRecorder) GetStocks(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStocks", reflect.TypeOf((*MockIGetStocksUtil)(nil).GetStocks), ctx, req)
}
