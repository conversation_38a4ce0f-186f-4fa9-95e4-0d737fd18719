package workflow_test

import (
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"

	celestialPb "github.com/epifi/be-common/api/celestial/activity"
	celestialWorkflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifitemporal"
	celestial2 "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
	usstocks2 "github.com/epifi/be-common/pkg/epifitemporal/namespace/usstocks"
	"github.com/epifi/be-common/pkg/storage"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/pay/payload"
	"github.com/epifi/gamma/api/usstocks"
	ussActivityPb "github.com/epifi/gamma/api/usstocks/activity"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
	payload2 "github.com/epifi/gamma/api/usstocks/payload"
	usstockWorkflowPb "github.com/epifi/gamma/api/usstocks/workflow"
	"github.com/epifi/gamma/api/vendorgateway/stocks"
	celestialActivity "github.com/epifi/gamma/celestial/activity/v2"
	ussWorkflow "github.com/epifi/gamma/usstocks/workflow"
)

type activityMock struct {
	enable bool
	name   string
	req    interface{}
	res    interface{}
	err    error
	times  int
}

type signalMock struct {
	delay      time.Duration
	signal     interface{}
	signalName epifitemporal.Signal
}

func updateOrderLocalActivityMock(dep *mockDependencies) {
	dep.orderDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	dep.orderDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&orderPb.Order{ActorId: "actor-id"}, nil).MaxTimes(1)
	dep.investmentInstrumentQueuePublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil).MaxTimes(1)
}

var (
	sampleTxnId                  = "dummy-txn-id"
	sampleSwiftTxnId             = "dummy-swift-id"
	sampleUTR                    = "sample-utr"
	poolTransferSignalPayload, _ = protojson.Marshal(&payload.InternationalFundTransferStatusSignal{
		TxnId:  sampleTxnId,
		Status: internationalfundtransfer.InternationalFundTransferStatus_POOL_ACCOUNT_TRANSFER_SUCCESSFUL,
	})
	poolTransferSignalPayload2, _ = protojson.Marshal(&payload.InternationalFundTransferStatusSignal{
		TxnId:     sampleTxnId,
		UtrNumber: sampleUTR,
		Status:    internationalfundtransfer.InternationalFundTransferStatus_POOL_ACCOUNT_TRANSFER_SUCCESSFUL,
	})
	poolTransferFailedSignalPayload, _ = protojson.Marshal(&payload.InternationalFundTransferStatusSignal{
		TxnId:  "dummy-txn-id",
		Status: internationalfundtransfer.InternationalFundTransferStatus_POOL_ACCOUNT_TRANSFER_FAILED,
	})
	initiationFailedSignalPayload, _ = protojson.Marshal(&payload2.OrderInitiationFailedSignal{})
	iftSignalPayload, _              = protojson.Marshal(&payload.InternationalFundTransferStatusSignal{
		TxnId:  sampleSwiftTxnId,
		Status: internationalfundtransfer.InternationalFundTransferStatus_INTERNATIONAL_FUND_TRANSFER_SUCCESS,
	})
	lrsFailedSignalPayload, _ = protojson.Marshal(&payload.InternationalFundTransferStatusSignal{
		TxnId:  "dummy-swift-id",
		Status: internationalfundtransfer.InternationalFundTransferStatus_LRS_LIMIT_BREACHED,
	})
	orderFulfilledSignalPayload, _ = protojson.Marshal(&payload.OrderFulfilmentStatusResponseSignal{
		Status: internationalfundtransfer.OrderFulfilmentStatus_ORDER_FULFILMENT_STATUS_SUCCESS,
	})
	orderFailedSignalPayload, _ = protojson.Marshal(&payload.OrderFulfilmentStatusResponseSignal{
		Status: internationalfundtransfer.OrderFulfilmentStatus_ORDER_FULFILMENT_STATUS_FAILURE,
	})
	refundSuccessSignal, _ = protojson.Marshal(&payload.InternationalFundTransferStatusSignal{
		TxnId:  "dummy-refund-id",
		Status: internationalfundtransfer.InternationalFundTransferStatus_REFUND_SUCCESSFUL,
	})
)

// NOTE: this has local activity of update order if this activity changes, workflow test case also need to be changed(ie updateOrderLocalActivityMock)
func TestBuyUsStocks(t *testing.T) {
	const defaultWorkflowID = "default-test-workflow-id"
	filledAt := timestamppb.New(storage.PgNow())

	wfReqPayload1, _ := protojson.Marshal(&usstockWorkflowPb.BuyUSStocksRequest{Order: &orderPb.Order{
		Id:          clientReqId,
		FundingType: usstocks.OrderFundingType_ORDER_FUNDING_TYPE_WALLET,
	}})

	tests := []struct {
		name                        string
		req                         *celestialWorkflowPb.Request
		wantErr                     bool
		activities                  []activityMock
		poolTransferSignal          *signalMock
		IFTSignal                   *signalMock
		orderInitiationFailedSignal *signalMock
		refundSignal                *signalMock
		mockLocalActivity           func(*mockDependencies)
	}{
		{
			name:              "buy order with wallet",
			req:               &celestialWorkflowPb.Request{},
			mockLocalActivity: updateOrderLocalActivityMock,
			activities: []activityMock{
				{
					enable: true,
					times:  1,
					name:   string(epifitemporal.GetWorkflowProcessingParamsV2),
					req: &celestialPb.GetWorkflowProcessingParamsV2Request{
						RequestHeader: &celestialPb.RequestHeader{
							Ownership: ussWorkflow.USSAlpacaOwnership,
						},
						WfReqId: defaultWorkflowID,
					},
					res: &celestialPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &celestialWorkflowPb.ProcessingParams{
							Payload: wfReqPayload1,
							ClientReqId: &celestialWorkflowPb.ClientReqId{
								Id:     clientReqId,
								Client: celestialWorkflowPb.Client_US_STOCKS,
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					times:  1,
					name:   string(epifitemporal.InitiateWorkflowStageV2),
					req: &celestialPb.InitiateWorkflowStageV2Request{
						RequestHeader: &celestialPb.RequestHeader{
							// Ownership: ussWorkflow.USSAlpacaOwnership,
						},
						WfReqId:   defaultWorkflowID,
						StageEnum: celestial2.GetStageEnumFromStage(usstocks2.UpdateWfReqIdInOrderStage),
						Status:    stagePb.Status_INITIATED,
					},
					err: nil,
				},
				{
					enable: true,
					times:  1,
					name:   string(epifitemporal.UpdateWorkflowStage),
					req: &celestialPb.UpdateWorkflowStageRequest{
						RequestHeader: &celestialPb.RequestHeader{
							// Ownership: ussWorkflow.USSAlpacaOwnership,
						},
						WfReqId:   defaultWorkflowID,
						StageEnum: celestial2.GetStageEnumFromStage(usstocks2.UpdateWfReqIdInOrderStage),
						Status:    stagePb.Status_SUCCESSFUL,
					},
					err: nil,
				},
				{
					enable: true,
					times:  1,
					name:   string(epifitemporal.InitiateWorkflowStageV2),
					req: &celestialPb.InitiateWorkflowStageV2Request{
						RequestHeader: &celestialPb.RequestHeader{
							// Ownership: ussWorkflow.USSAlpacaOwnership,
						},
						WfReqId:   defaultWorkflowID,
						StageEnum: celestial2.GetStageEnumFromStage(usstocks2.SendBuyOrderStage),
						Status:    stagePb.Status_INITIATED,
					},
					err: nil,
				},
				{
					enable: true,
					times:  0,
					name:   string(usstocks2.SendBuyOrder),
					req: &ussActivityPb.SendBuyOrderRequest{
						RequestHeader: &celestialPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ussWorkflow.USSAlpacaOwnership,
						},
					},
					res: &ussActivityPb.SendBuyOrderResponse{ResponseHeader: nil, VgOrder: &stocks.Order{Id: "dummy-vendor-id"}},
					err: nil,
				},
				{
					enable: true,
					times:  1,
					name:   string(epifitemporal.UpdateWorkflowStage),
					req: &celestialPb.UpdateWorkflowStageRequest{
						RequestHeader: &celestialPb.RequestHeader{
							// Ownership: ussWorkflow.USSAlpacaOwnership,
						},
						WfReqId:   defaultWorkflowID,
						StageEnum: celestial2.GetStageEnumFromStage(usstocks2.SendBuyOrderStage),
						Status:    stagePb.Status_SUCCESSFUL,
					},
					err: nil,
				},
				{
					enable: true,
					times:  0,
					name:   string(epifitemporal.PublishWorkflowUpdateEventV2),
					req: &celestialPb.PublishWorkflowUpdateEventV2Request{
						RequestHeader: &celestialPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
					},
					err: nil,
				},
				{
					enable: true,
					times:  1,
					name:   string(usstocks2.UpdateOrder),
					req: &ussActivityPb.UpdateOrderRequest{
						RequestHeader: &celestialPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ussWorkflow.USSAlpacaOwnership,
						},
						OrderId: clientReqId,
						Order: &orderPb.Order{
							VendorOrderId: "dummy-vendor-id",
						},
						FieldMasks: []orderPb.OrderFieldMask{orderPb.OrderFieldMask_ORDER_FIELD_MASK_VENDOR_ORDER_ID},
					},
					res: &ussActivityPb.UpdateOrderResponse{ResponseHeader: nil},
					err: nil,
				},
				{
					enable: true,
					times:  1,
					name:   string(epifitemporal.InitiateWorkflowStageV2),
					req: &celestialPb.InitiateWorkflowStageV2Request{
						RequestHeader: &celestialPb.RequestHeader{
							// Ownership: ussWorkflow.USSAlpacaOwnership,
						},
						WfReqId:   defaultWorkflowID,
						StageEnum: celestial2.GetStageEnumFromStage(usstocks2.TrackOrderStage),
						Status:    stagePb.Status_INITIATED,
					},
					err: nil,
				},
				{
					enable: true,
					times:  1,
					name:   string(usstocks2.TrackOrderStatus),
					req: &ussActivityPb.TrackOrderStatusRequest{
						RequestHeader: &celestialPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ussWorkflow.USSAlpacaOwnership,
						},
					},
					res: &ussActivityPb.TrackOrderStatusResponse{ResponseHeader: nil, Order: &stocks.Order{Id: "dummy-vendor-id",
						FilledQty: 1.42, Notional: &moneyPb.Money{
							CurrencyCode: "USD",
							Units:        100,
							Nanos:        0,
						}, OrderStatus: stocks.OrderStatus_ORDER_STATUS_FILLED, FilledAt: filledAt}},
					err: nil,
				},
				{
					enable: true,
					times:  1,
					name:   string(epifitemporal.UpdateWorkflowStage),
					req: &celestialPb.UpdateWorkflowStageRequest{
						RequestHeader: &celestialPb.RequestHeader{
							// Ownership: ussWorkflow.USSAlpacaOwnership,
						},
						WfReqId:   defaultWorkflowID,
						StageEnum: celestial2.GetStageEnumFromStage(usstocks2.TrackOrderStage),
						Status:    stagePb.Status_SUCCESSFUL,
					},
					err: nil,
				},
				{
					enable: true,
					times:  1,
					name:   string(usstocks2.UpdateOrder),
					req: &ussActivityPb.UpdateOrderRequest{
						RequestHeader: &celestialPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ussWorkflow.USSAlpacaOwnership,
						},
						OrderId: clientReqId,
						Order: &orderPb.Order{
							QtyConfirmed: 1.42,
							AmountConfirmed: &moneyPb.Money{
								CurrencyCode: "USD",
								Units:        100,
								Nanos:        0,
							},
							FulfilledAt: filledAt,
							State:       usstocks.OrderState_ORDER_SUCCESS,
						},
						FieldMasks: []orderPb.OrderFieldMask{
							orderPb.OrderFieldMask_ORDER_FIELD_MASK_QTY_CONFIRMED,
							orderPb.OrderFieldMask_ORDER_FIELD_MASK_FULFILLED_AT,
							orderPb.OrderFieldMask_ORDER_FIELD_MASK_AMOUNT_CONFIRMED,
							orderPb.OrderFieldMask_ORDER_FIELD_MASK_ORDER_STATE,
						},
					},
					res: &ussActivityPb.UpdateOrderResponse{ResponseHeader: nil},
					err: nil,
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(ussWorkflow.BuyUsStocks)
			env.RegisterActivity(&celestialActivity.Processor{})
			// NOTE: this has local activity in update order if this activity changes workflow test case also need to be changed
			ussProcessor, mockProcessorDependencies, close := getProcessorWithMocks(t)
			defer close()
			env.RegisterActivity(ussProcessor)
			if tt.mockLocalActivity != nil {
				tt.mockLocalActivity(mockProcessorDependencies)
			}
			env.RegisterDelayedCallback(func() {
				if tt.poolTransferSignal != nil {
					err := env.SignalWorkflowByID(defaultWorkflowID, string(tt.poolTransferSignal.signalName), tt.poolTransferSignal.signal)
					if err != nil {
						t.Errorf("failed to send signal %s to workflow: %v", payNs.ForeignFundTransferStatusSignal, err)
					}
				}

				if tt.IFTSignal != nil {
					err := env.SignalWorkflowByID(defaultWorkflowID, string(tt.IFTSignal.signalName), tt.IFTSignal.signal)
					if err != nil {
						t.Errorf("failed to send signal %s to workflow: %v", payNs.ForeignFundTransferStatusSignal, err)
					}
				}

				if tt.refundSignal != nil {
					err := env.SignalWorkflowByID(defaultWorkflowID, string(tt.refundSignal.signalName), tt.refundSignal.signal)
					if err != nil {
						t.Errorf("failed to send signal %s to workflow: %v", payNs.ForeignFundTransferStatusSignal, err)
					}
				}
			}, 10)

			for _, act := range tt.activities {
				if act.res != nil {
					env.OnActivity(act.name, mock.Anything, act.req).Return(act.res, act.err)
				} else {
					env.OnActivity(act.name, mock.Anything, act.req).Return(act.err)
				}
			}
			// Push notifications are sent async and are not mandatory
			// Workflow can terminate before these async activities are executed, hence mocks are not strict
			env.OnActivity(string(usstocks2.SendPushNotification), mock.Anything, mock.Anything).Return(mock.Anything, mock.Anything).Maybe()

			env.ExecuteWorkflow(ussWorkflow.BuyUsStocks, tt.req)
			assert.True(t, env.IsWorkflowCompleted())

			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("BuyUsStocks() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}
			env.AssertExpectations(t)
		})
	}
}
