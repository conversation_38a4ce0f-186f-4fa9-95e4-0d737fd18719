package model

import (
	"database/sql/driver"
	"fmt"
	"time"

	mPb "github.com/epifi/gamma/api/merchant"
	"github.com/epifi/gamma/api/vendorgateway/gplace"

	"github.com/lib/pq"
	"google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"
	"gorm.io/gorm/schema"
)

// Defining only those fields which are needed now
type MerchantPiGplaceData struct {
	Id                  string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	PiId                string
	GplaceTypes         GplaceTypes
	BusinessStatus      gplace.BusinessStatus
	FormattedAddress    string
	Geometry            *gplace.Geometry
	IconUrl             string
	IconMaskBaseUri     string
	IconBackgroundColor string
	PlaceName           string
	PlaceId             string

	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gormv2.DeletedAt
}

func NewMerchantPiGplaceData(v *mPb.MerchantPiGplaceData) *MerchantPiGplaceData {
	if v == nil {
		return nil
	}
	model := &MerchantPiGplaceData{
		PiId:                v.GetPiId(),
		GplaceTypes:         v.GetGplaceTypes(),
		BusinessStatus:      v.GetBusinessStatus(),
		FormattedAddress:    v.GetFormattedAddress(),
		Geometry:            v.GetGeometry(),
		IconUrl:             v.GetIconUrl(),
		IconMaskBaseUri:     v.GetIconMaskBaseUri(),
		IconBackgroundColor: v.GetIconBackgroundColor(),
		PlaceName:           v.GetPlaceName(),
		PlaceId:             v.GetPlaceId(),
	}
	if v.GetCreatedAt() != nil {
		model.CreatedAt = v.GetCreatedAt().AsTime()
	}
	if v.GetUpdatedAt() != nil {
		model.UpdatedAt = v.GetUpdatedAt().AsTime()
	}
	if v.GetDeletedAt() != nil {
		model.DeletedAt = gormv2.DeletedAt{
			Time:  v.GetDeletedAt().AsTime(),
			Valid: true,
		}
	}
	return model
}

func (m *MerchantPiGplaceData) ToProto() *mPb.MerchantPiGplaceData {
	if m == nil {
		return nil
	}

	proto := &mPb.MerchantPiGplaceData{
		Id:                  m.Id,
		PiId:                m.PiId,
		GplaceTypes:         m.GplaceTypes,
		BusinessStatus:      m.BusinessStatus,
		FormattedAddress:    m.FormattedAddress,
		Geometry:            m.Geometry,
		IconUrl:             m.IconUrl,
		IconMaskBaseUri:     m.IconMaskBaseUri,
		IconBackgroundColor: m.IconBackgroundColor,
		PlaceName:           m.PlaceName,
		PlaceId:             m.PlaceId,
		CreatedAt:           timestamppb.New(m.CreatedAt),
		UpdatedAt:           timestamppb.New(m.UpdatedAt),
	}
	if m.DeletedAt.Valid {
		proto.DeletedAt = timestamppb.New(m.DeletedAt.Time)
	}

	return proto
}

type GplaceTypes []gplace.GPlaceType

func (x GplaceTypes) Value() (driver.Value, error) {
	var groups []string
	for _, g := range x {
		groups = append(groups, g.String())
	}
	strArr := pq.StringArray(groups)
	return strArr.Value()
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *GplaceTypes) Scan(src interface{}) error {
	strArr := pq.StringArray([]string{})
	if err := strArr.Scan(src); err != nil {
		return fmt.Errorf("failed to scan []string using pq.StrinArray, str: %v", src)
	}

	var groups []gplace.GPlaceType
	for _, v := range strArr {
		groups = append(groups, gplace.GPlaceType(gplace.GPlaceType_value[v]))
	}

	*x = groups
	return nil
}

func (GplaceTypes) GormDataType() string {
	return "text"
}

func (GplaceTypes) GormDBDataType(db *gormv2.DB, _ *schema.Field) string {

	// returns different database type based on driver name
	switch db.Dialector.Name() {
	case "mysql", "sqlite":
		return "text"
	}
	return ""
}
