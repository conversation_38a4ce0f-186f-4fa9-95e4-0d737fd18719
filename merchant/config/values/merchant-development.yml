Application:
  Environment: "development"
  Name: "merchant"

RedisOptions:
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 14
  ClientName: merchant

MerchantCacheConfig:
  IsCachingEnabled: true
  MerchantIdPrefix: "merchant_id_"
  CacheTTl: "2m"

MerchantPiCacheConfig:
  IsCachingEnabled: true
  MerchantPiIdPrefix: "merchant_pi_id_"
  OldMerchantIdPrefix: "old_merchant_id_"
  CacheTTl: "2m"
  OldMerchantIdToNewMerchantIdCacheTTl: "360h" #15 days

Server:
  Ports:
    GrpcPort: 8092
    GrpcSecurePort: 9501
    HttpPort: 9892
    HttpPProfPort: 9990

MerchantDb:
  Host: "localhost"
  Port: 5432
  DbType: "PGDB"
  Name: "merchant"
  DbServerAlias: "PLUTUS_RDS"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "merchant"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

AWS:
  Region: "ap-south-1"

Flags:
  TrimDebugMessageFromStatus: true

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

knownMerchantMap:
  MerchToActorsMap:
    "zomato": []
    "amazon": []
    "swiggy": []
    "flipkart": []
    "dunzo": []
    "big_basket": []
    "myntra": []
    "nykaa": []

Secrets:
  Ids:
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    GoogleCloudProfilingServiceAccountKey: "development"

Profiling:
  StackDriverProfiling:
    ProjectId: "development"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

