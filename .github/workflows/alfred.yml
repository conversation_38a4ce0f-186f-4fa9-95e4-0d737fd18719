name: <PERSON>

on:
  # Trigger the workflow manually only through UI
  # https://github.blog/changelog/2020-07-06-github-actions-manual-triggers-with-workflow_dispatch/
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  run_checks:
    uses: ./.github/workflows/base-workflow.yml
    with:
      runner_type: "go.crdb.postgres"
      lci_stamp_key: "alfred"
      services: "alfred"
      start_cockroachdb: true
      start_postgres: true
      run_test_parallel: true
      paths_to_trigger: |
        impacted_paths:
          - 'gamma/alfred/**'
          - 'gamma/cmd/user/config/alfred-test.yml'
          - 'gamma/cmd/user/config/alfred-params.yml'
          - 'gamma/db/epifi/fixture.sql'
          - 'gamma/db/epifi/latest.sql'
          - 'gamma/.github/workflows/alfred.yml'
          - 'gamma/pkg/chequebook'
          - 'be-common/pkg/constants'
          - '!gamma/alfred/config/values/alfred-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'
    secrets: inherit
