name: User
on:
  # Trigger the workflow manually only through UI
  # https://github.blog/changelog/2020-07-06-github-actions-manual-triggers-with-workflow_dispatch/
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  run_checks:
    uses: ./.github/workflows/base-workflow.yml
    with:
      runner_type: "go.crdb.postgres"
      services: "user"
      start_cockroachdb: true
      start_postgres: true
      start_redis: true
      run_test_parallel: true
      lci_stamp_key: "user"
      paths_to_trigger: |
        impacted_paths:
          - 'gamma/user/**'
          - 'gamma/cmd/user/config/user-test.yml'
          - 'gamma/cmd/user/config/user-params.yml'
          - 'gamma/db/epifi/fixture.sql'
          - 'gamma/db/epifi/latest.sql'
          - 'gamma/.github/workflows/user.yml'
          - 'gamma/.github/workflows/user_dao.yml'
          - 'gamma/pkg/auth'
          - 'gamma/pkg/changefeed'
          - 'be-common/pkg/constants'
          - 'be-common/pkg/counter'
          - 'gamma/pkg/decisionmodel'
          - 'gamma/pkg/employment'
          - 'gamma/pkg/firefly'
          - '!gamma/user/config/values/user-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'
    secrets: inherit
