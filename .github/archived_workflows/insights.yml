name: Insights
on:
 # Trigger the workflow when the pull request is labeled
  # This is done to avoid workflows triggers on each commit in the PR.
  # A jenkins job will be used to label the PR and add correct tags in description to check condition in the job's build rule
  pull_request:
    types: [ labeled ]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  run_checks:
    uses: ./.github/workflows/base-workflow.yml
    with:
      runner_type: "go.crdb.postgres"
      services: "insights"
      start_postgres: true
      start_cockroachdb: true
      paths_to_trigger: |
        impacted_paths:
          - 'pkg/**'
          - '!pkg/usstocks/**'
          - 'insights/**'
          - 'cmd/insights/config/insights-test.yml'
          - 'cmd/insights/config/insights-params.yml'
          - 'db/insights/fixture.sql'
          - 'db/insights/latest.sql'
          - '.github/workflows/insights.yml'
          - 'go.mod'
          - 'docker-compose.test.yml'
    secrets: inherit
