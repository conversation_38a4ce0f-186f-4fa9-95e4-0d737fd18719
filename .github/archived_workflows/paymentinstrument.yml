name: Payment Instrument
on:
 # Trigger the workflow when the pull request is labeled
  # This is done to avoid workflows triggers on each commit in the PR.
  # A jenkins job will be used to label the PR and add correct tags in description to check condition in the job's build rule
  pull_request:
    types: [ labeled ]
# Uncomment this to run on merge to master
#  push:
#    branches:
#      - master

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  run_checks:
    uses: ./.github/workflows/base-workflow.yml
    with:
      runner_type: "go.crdb.postgres"
      services: "paymentinstrument"
      start_cockroachdb: true
      start_postgres: true
      start_pinot: false
      start_redis: true
      use_crdb_schema_backup: false
      run_test_parallel: false
      paths_to_trigger: |
        impacted_paths:
          - 'pkg/**'
          - '!pkg/usstocks/**'
          - 'paymentinstrument/**'
          - 'cmd/paymentinstrument/config/paymentinstrument-test.yml'
          - 'cmd/paymentinstrument/config/paymentinstrument-params.yml'
          - 'db/epifi/fixture.sql'
          - 'db/epifi/latest.sql'
          - '.github/workflows/paymentinstrument.yml'
          - 'go.mod'
    secrets: inherit
