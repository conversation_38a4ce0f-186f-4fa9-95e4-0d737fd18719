resource "aws_acm_certificate" "cert" {
  private_key       = acme_certificate.certificate.private_key_pem
  certificate_body  = acme_certificate.certificate.certificate_pem
  certificate_chain = acme_certificate.certificate.issuer_pem
  depends_on        = [acme_certificate.certificate]
}

resource "aws_secretsmanager_secret" "lets_encrypt_account" {
  name       = "qa/lets-encrypt-account"
  depends_on = [tls_private_key.private_key]
}

resource "aws_secretsmanager_secret_version" "lets_encrypt_account" {
  secret_id     = aws_secretsmanager_secret.lets_encrypt_account.id
  secret_string = tls_private_key.private_key.private_key_pem
  depends_on    = [tls_private_key.private_key]
}

resource "aws_secretsmanager_secret" "letsencrypt_cert" {
  name       = "epifi/pki/letsencrypt"
  depends_on = [acme_certificate.certificate]
}

resource "aws_secretsmanager_secret_version" "letsencrypt_certs" {
  secret_id     = aws_secretsmanager_secret.letsencrypt_cert.id
  secret_string = local.data
  depends_on    = [acme_certificate.certificate]
}

locals {
  data = jsonencode({
    key    = acme_certificate.certificate.private_key_pem
    cert   = acme_certificate.certificate.certificate_pem
    cacert = acme_certificate.certificate.issuer_pem
  })
}
