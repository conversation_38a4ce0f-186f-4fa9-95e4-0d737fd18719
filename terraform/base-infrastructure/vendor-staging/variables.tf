variable "this_account_id" {
  type        = string
  default     = "************"
  description = "account id of vendor staging"
}

variable "env" {
  description = "Name of the Environment, e.g. demo, staging, production etc"
  type        = string
  default     = "vendor-staging"
}

variable "cmk_key_id" {
  type        = string
  default     = "arn:aws:kms:ap-south-1:************:key/e1f4b99f-22e9-4b5e-ba1d-d8c4a8bdb3f1"
  description = "key id of the cmk in deploy account"
}
