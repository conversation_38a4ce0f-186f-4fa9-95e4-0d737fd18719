data "aws_iam_policy_document" "epifi-ozonetel-transcription-policy" {
  statement {
    sid    = "UAT"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::epifi-ozonetel-transcription",
      "arn:aws:s3:::epifi-ozonetel-transcription/*",
    ]

    actions = ["s3:*"]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
  }

  statement {
    sid    = "Deploy"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::epifi-ozonetel-transcription",
      "arn:aws:s3:::epifi-ozonetel-transcription/*",
    ]

    actions = ["s3:*"]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
  }

  statement {
    sid    = "Demo"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::epifi-ozonetel-transcription",
      "arn:aws:s3:::epifi-ozonetel-transcription/*",
    ]

    actions = ["s3:*"]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
  }

  statement {
    sid    = "Vendor Staging"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::epifi-ozonetel-transcription",
      "arn:aws:s3:::epifi-ozonetel-transcription/*",
    ]

    actions = ["s3:*"]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::432457413665:root"]
    }
  }

  statement {
    sid    = "Staging"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::epifi-ozonetel-transcription",
      "arn:aws:s3:::epifi-ozonetel-transcription/*",
    ]

    actions = ["s3:*"]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
  }

  statement {
    sid    = "QA"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::epifi-ozonetel-transcription",
      "arn:aws:s3:::epifi-ozonetel-transcription/*",
    ]

    actions = ["s3:*"]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
  }

  statement {
    sid    = "Data-dev"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::epifi-ozonetel-transcription",
      "arn:aws:s3:::epifi-ozonetel-transcription/*",
    ]

    actions = ["s3:*"]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
  }
}

data "aws_iam_policy_document" "epifi-data-rewards-dev-policy" {
  version = "2012-10-17"
  statement {
    sid    = "OtherAccountAccess"
    effect = "Allow"

    principals {
      type = "AWS"
      identifiers = [
        "arn:aws:iam::************:root",
        "arn:aws:iam::************:root",
        "arn:aws:iam::************:root",
        "arn:aws:iam::************:root",
        "arn:aws:iam::************:root"
      ]
    }
    actions = ["s3:*"]
    resources = [
      "arn:aws:s3:::epifi-data-rewards-dev",
      "arn:aws:s3:::epifi-data-rewards-dev/*",
    ]
  }
}

data "aws_iam_policy_document" "stage-epifi-ds-policy" {
  statement {
    actions = [
      "s3:GetObject",
      "s3:List*",
      "s3:GetObjectVersion",
      "s3:GetBucketAcl",
      "s3:GetBucketLocation",
    ]
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
    resources = [
      "arn:aws:s3:::stage-epifi-ds",
      "arn:aws:s3:::stage-epifi-ds/liveness/*",
      "arn:aws:s3:::stage-epifi-ds/merchant_service/*",
    ]
    sid = "RODataProd"
  }
  statement {
    sid    = "UAT"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::stage-epifi-ds",
      "arn:aws:s3:::stage-epifi-ds/*",
    ]

    actions = ["s3:*"]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
  }

  statement {
    sid    = "Deploy"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::stage-epifi-ds",
      "arn:aws:s3:::stage-epifi-ds/*",
    ]

    actions = ["s3:*"]


    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
  }

  statement {
    sid    = "Demo"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::stage-epifi-ds",
      "arn:aws:s3:::stage-epifi-ds/*",
    ]

    actions = ["s3:*"]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
  }

  statement {
    sid    = "Vendor Staging"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::stage-epifi-ds",
      "arn:aws:s3:::stage-epifi-ds/*",
    ]

    actions = ["s3:*"]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::432457413665:root"]
    }
  }

  statement {
    sid    = "Staging"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::stage-epifi-ds",
      "arn:aws:s3:::stage-epifi-ds/*",
    ]

    actions = ["s3:*"]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
  }

  statement {
    sid    = "QA"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::stage-epifi-ds",
      "arn:aws:s3:::stage-epifi-ds/*",
    ]

    actions = ["s3:*"]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
  }

  statement {
    sid    = "Data-dev"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::stage-epifi-ds",
      "arn:aws:s3:::stage-epifi-ds/*",
    ]

    actions = ["s3:*"]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
  }

  statement {
    sid    = "Demo-jenkins"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::stage-epifi-ds",
      "arn:aws:s3:::stage-epifi-ds/*",
    ]

    actions = [
      "s3:GetObject",
      "s3:GetObjectVersion",
      "s3:GetObjectAcl",
      "s3:ListBucket",
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:role/role_decrypt_deploy"]
    }
  }

  statement {
    sid    = "UAT-jenkins"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::stage-epifi-ds",
      "arn:aws:s3:::stage-epifi-ds/*",
    ]

    actions = [
      "s3:GetObject",
      "s3:GetObjectVersion",
      "s3:GetObjectAcl",
      "s3:ListBucket",
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:role/role_decrypt_deploy"]
    }
  }

  statement {
    sid    = "Staging-jenkins"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::stage-epifi-ds",
      "arn:aws:s3:::stage-epifi-ds/*",
    ]

    actions = [
      "s3:GetObject",
      "s3:GetObjectVersion",
      "s3:GetObjectAcl",
      "s3:ListBucket",
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:role/role_decrypt_deploy"]
    }
  }

  statement {
    sid    = "QA-jenkins"
    effect = "Allow"

    resources = [
      "arn:aws:s3:::stage-epifi-ds",
      "arn:aws:s3:::stage-epifi-ds/*",
    ]

    actions = [
      "s3:GetObject",
      "s3:GetObjectVersion",
      "s3:GetObjectAcl",
      "s3:ListBucket",
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:role/role_decrypt_deploy"]
    }
  }
}

data "aws_iam_policy_document" "epifi-federal-raw-dev-policy" {
  version = "2012-10-17"
  statement {
    sid    = "OtherAccountAccess"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
    actions = ["s3:*"]
    resources = [
      "arn:aws:s3:::epifi-federal-raw-dev",
      "arn:aws:s3:::epifi-federal-raw-dev/*",
    ]
  }
}

data "aws_iam_policy_document" "epifi-federal-ds-dev-policy" {
  version = "2012-10-17"
  statement {
    sid    = "OtherAccountAccess"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
    actions = ["s3:*"]
    resources = [
      "arn:aws:s3:::epifi-federal-ds-dev",
      "arn:aws:s3:::epifi-federal-ds-dev/*",
    ]
  }
}

data "aws_iam_policy_document" "epifi-federal-dna-dev-policy" {
  version = "2012-10-17"
  statement {
    sid    = "OtherAccountAccess"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
    actions = ["s3:*"]
    resources = [
      "arn:aws:s3:::epifi-federal-dna-dev",
      "arn:aws:s3:::epifi-federal-dna-dev/*",
    ]
  }
}

data "aws_iam_policy_document" "epifi-federal-sf-dev-policy" {
  version = "2012-10-17"
  statement {
    sid    = "OtherAccountAccess"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
    actions = ["s3:*"]
    resources = [
      "arn:aws:s3:::epifi-federal-sf-dev",
      "arn:aws:s3:::epifi-federal-sf-dev/*",
    ]
  }
}

data "aws_iam_policy_document" "epifi-federal-dp-dev-policy" {
  version = "2012-10-17"
  statement {
    sid    = "OtherAccountAccess"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
    actions = ["s3:*"]
    resources = [
      "arn:aws:s3:::epifi-federal-dp-dev",
      "arn:aws:s3:::epifi-federal-dp-dev/*",
    ]
  }
}
