resource "aws_alb" "service-alb" {
  name                       = "${var.tenant}-${var.env}-${var.service_name}-alb"
  internal                   = var.allow_inbound_from_internet ? false : true
  ip_address_type            = "ipv4"
  load_balancer_type         = "application"
  enable_deletion_protection = true
  subnets                    = var.desired_subnets_for_lb
  security_groups            = [aws_security_group.ecs-alb-sg.id]

}

resource "aws_alb_target_group" "service_target_group" {
  name                 = "${var.tenant}-${var.env}-${var.service_name}-tg"
  port                 = var.container_port
  protocol             = "HTTP"
  target_type          = "ip"
  vpc_id               = var.aws_vpc
  deregistration_delay = var.deregistration_delay

  health_check {
    healthy_threshold   = "3"
    interval            = "10"
    protocol            = "HTTP"
    timeout             = "5"
    matcher             = "200"
    path                = var.health_check_path
    unhealthy_threshold = "3"
    port                = var.container_port
  }
}

resource "aws_alb_listener" "http" {
  load_balancer_arn = aws_alb.service-alb.id
  port              = 80

  default_action {
    type = "redirect"

    redirect {
      port        = 443
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}


resource "aws_alb_listener" "https" {
  load_balancer_arn = aws_alb.service-alb.id
  port              = 443
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-FS-1-2-2019-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_alb_target_group.service_target_group.arn
  }
}
resource "aws_lb_listener_rule" "devops-dashboard" {
  count        = var.service_name == "dashboard" ? 1 : 0
  listener_arn = aws_alb_listener.https.arn

  action {
    type = "authenticate-cognito"
    authenticate_cognito {
      user_pool_arn       = var.user_pool_arn
      user_pool_client_id = var.user_pool_client_id
      user_pool_domain    = var.user_pool_domain
    }
  }
  action {
    target_group_arn = aws_alb_target_group.service_target_group.id
    type             = "forward"
  }
  condition {
    host_header {
      values = ["devops-${var.env}.pointz.in"]
    }
  }
}
###################### Security Group Stuff ####################################

resource "aws_security_group_rule" "lb-all-vpc" {
  count             = var.pci_scoped_service ? 0 : 1
  type              = "ingress"
  protocol          = "tcp"
  from_port         = 0
  to_port           = 65535
  cidr_blocks       = ["10.0.0.0/8"]
  security_group_id = aws_security_group.ecs-alb-sg.id
}

resource "aws_security_group_rule" "lb-all-self" {
  type              = "ingress"
  protocol          = -1
  from_port         = 0
  to_port           = 0
  self              = true
  security_group_id = aws_security_group.ecs-alb-sg.id
}

resource "aws_security_group_rule" "deploy-account" {
  type              = "ingress"
  protocol          = -1
  from_port         = 0
  to_port           = 0
  cidr_blocks       = var.monitoring_cidr
  security_group_id = aws_security_group.ecs-alb-sg.id
}

resource "aws_security_group_rule" "lb-vpn-vpc" {
  count             = var.allow_inbound_from_internet ? 0 : 1
  type              = "ingress"
  protocol          = "tcp"
  from_port         = 0
  to_port           = 65535
  cidr_blocks       = var.vpn_cidr
  security_group_id = aws_security_group.ecs-alb-sg.id
}

resource "aws_security_group_rule" "lb-all-internet" {
  count             = var.allow_inbound_from_internet ? 1 : 0
  type              = "ingress"
  protocol          = "tcp"
  from_port         = 0
  to_port           = 65535
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.ecs-alb-sg.id
}

resource "aws_security_group_rule" "lb-egress-all" {
  count             = var.allow_outbound_to_internet ? 0 : 1
  type              = "egress"
  protocol          = "tcp"
  from_port         = 0
  to_port           = 65535
  cidr_blocks       = var.vpc_cidr
  security_group_id = aws_security_group.ecs-alb-sg.id
}


resource "aws_security_group_rule" "lb-egress-all-internet" {
  count             = var.allow_outbound_to_internet ? 1 : 0
  type              = "egress"
  protocol          = "tcp"
  from_port         = 0
  to_port           = 65535
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.ecs-alb-sg.id
}

resource "aws_security_group" "ecs-alb-sg" {
  name                   = "${var.tenant}-${var.env}-${var.service_name}-alb-sg"
  vpc_id                 = var.aws_vpc
  revoke_rules_on_delete = true

  tags = merge(
    local.tags,
    {
      "Name" = "${var.tenant}-${var.env}-${var.service_name}-alb-sg"
    }
  )
}


resource "aws_security_group_rule" "ecs-egress-all" {
  type              = "egress"
  protocol          = "tcp"
  from_port         = 0
  to_port           = 65535
  cidr_blocks       = var.vpc_cidr
  security_group_id = aws_security_group.ecs_task.id
}


resource "aws_security_group_rule" "ecs-egress-all-internet" {
  type              = "egress"
  protocol          = "tcp"
  from_port         = 0
  to_port           = 65535
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.ecs_task.id
}

resource "aws_security_group_rule" "ecs-task" {
  from_port                = var.container_port
  protocol                 = "tcp"
  to_port                  = var.container_port
  type                     = "ingress"
  source_security_group_id = aws_security_group.ecs-alb-sg.id
  security_group_id        = aws_security_group.ecs_task.id
}


resource "aws_security_group" "ecs_task" {
  name                   = "${var.tenant}-${var.env}-${var.service_name}-ecs-sg"
  vpc_id                 = var.aws_vpc
  revoke_rules_on_delete = true

  tags = merge(
    local.tags,
    {
      "Name" = "${var.tenant}-${var.env}-${var.service_name}-ecs-sg"
    }
  )
}

###################IAM role and attachments#######################
# ECS Task execution Role
data "aws_iam_policy_document" "ecs_task_execution_role" {
  version = "2012-10-17"
  statement {
    sid     = ""
    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}

data "aws_iam_policy_document" "Assumerole_other_envs_document" {
  count = var.service_name == "dashboard" ? 1 : 0
  dynamic "statement" {
    iterator = account_num
    for_each = var.account_ids
    content {
      actions   = ["sts:AssumeRole"]
      resources = ["arn:aws:iam::${account_num.value}:role/${var.role_to_assume}"]
    }
  }
}

resource "aws_iam_policy" "dashboard-to-assume-role-on-other-envs" {
  count  = var.service_name == "dashboard" ? 1 : 0
  name   = "dashboard-to-assume-role-on-other-envs"
  policy = data.aws_iam_policy_document.Assumerole_other_envs_document[0].json
}

resource "aws_iam_role_policy_attachment" "jenkins-assume-role-attachment" {
  count      = var.service_name == "dashboard" ? 1 : 0
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.dashboard-to-assume-role-on-other-envs[0].arn
}

data "aws_iam_policy_document" "cloudwatch-logs" {
  version = "2012-10-17"
  statement {
    actions = [
      "logs:CreateLogStream",
      "logs:PutLogEvents"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_role" "ecs_task_execution_role" {
  name               = "${var.tenant}-${var.env}-${var.service_name}-task-execution-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_task_execution_role.json
  tags = merge(
    local.tags,
    {
      "Name" = "${var.tenant}-${var.env}-${var.service_name}-alb-sg"
    }
  )
}

resource "aws_iam_role_policy" "ecs_task_execution_role_logs" {
  name   = "${var.tenant}-${var.env}-${var.service_name}-cloudwatch-logs"
  role   = aws_iam_role.ecs_task_execution_role.name
  policy = data.aws_iam_policy_document.cloudwatch-logs.json
}

resource "aws_iam_role_policy_attachment" "ecs_task_execution_role" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

resource "aws_iam_role_policy_attachment" "ec2_full_access" {
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2FullAccess"
}

resource "aws_iam_role_policy_attachment" "ec2_full_access_task_execution" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2FullAccess"
}

data "aws_iam_policy_document" "ecs_task_role" {
  version = "2012-10-17"
  statement {
    sid     = ""
    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "ecs_task_role" {
  name               = "${var.tenant}-${var.env}-${var.service_name}-task-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_task_role.json
  tags = merge(
    local.tags,
    {
      "Name" = "${var.tenant}-${var.env}-${var.service_name}-alb-sg"
    }
  )
}

resource "aws_iam_role_policy_attachment" "ecs_task_role" {
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

resource "aws_iam_role_policy_attachment" "ecs_full_access" {
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonECS_FullAccess"
}

resource "aws_iam_role_policy_attachment" "ecr_access" {
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = "arn:aws:iam::aws:policy/EC2InstanceProfileForImageBuilderECRContainerBuilds"
}

########################### ECS Task Definition and Service ########################################
resource "aws_ecs_task_definition" "ecs_task_defination" {
  family                   = "${var.tenant}-${var.env}-${var.service_name}"
  container_definitions    = var.container_definitions
  requires_compatibilities = ["FARGATE"]
  task_role_arn            = aws_iam_role.ecs_task_role.arn
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  network_mode             = "awsvpc"
  cpu                      = var.fargate_cpu
  memory                   = var.fargate_memory
  tags = merge(
    local.tags,
    {
      "Name" = "${var.tenant}-${var.env}-${var.service_name}-alb-sg"
    }
  )
}

resource "aws_ecs_service" "ecs_service" {
  name                               = "${var.tenant}-${var.env}-${var.service_name}"
  cluster                            = "st-prod-monitoring"
  task_definition                    = aws_ecs_task_definition.ecs_task_defination.arn
  desired_count                      = var.app_count
  launch_type                        = var.launch_type
  propagate_tags                     = "TASK_DEFINITION"
  deployment_maximum_percent         = 200
  deployment_minimum_healthy_percent = 100
  force_new_deployment               = var.force_new_deployment
  health_check_grace_period_seconds  = 10

  network_configuration {
    subnets          = var.desired_subnets_for_lb
    security_groups  = [aws_security_group.ecs_task.id]
    assign_public_ip = var.allow_inbound_from_internet ? true : false
  }

  load_balancer {
    target_group_arn = aws_alb_target_group.service_target_group.id
    container_name   = "prod-nsdl-forwardsecrecy"
    container_port   = var.container_port
  }

  tags = merge(
    local.tags,
    {
      "Name" = "${var.tenant}-${var.env}-${var.service_name}-alb-sg"
    }
  )

  depends_on = [aws_alb.service-alb, aws_iam_role.ecs_task_execution_role]
}

####################################### Cloudwatch Group and Log Stream #################################
resource "aws_cloudwatch_log_group" "ecs_log_group" {
  name              = "/ecs/${var.env}/${var.service_name}"
  retention_in_days = 7

  tags = merge(
    local.tags,
    {
      "Name" = "${var.tenant}-${var.env}-${var.service_name}-alb-sg"
    }
  )
}

resource "aws_cloudwatch_log_stream" "ecs_log_stream" {
  log_group_name = aws_cloudwatch_log_group.ecs_log_group.name
  name           = "${var.tenant}-${var.env}-${var.service_name}"
}