terraform {
  backend "s3" {
    # Pass configuration from file through command line
  }
}

module "popular-faqs" {
  source                  = "../../modules/iam_role/k8s/v1"
  app                     = "popular-faqs"
  eks_cluster_oidc_issuer = var.eks_cluster_oidc_issuer
  env                     = var.env
  s3Permissions = [{
    resource = "stage-epifi-ds"
    actions  = ["s3:*"]
  }]
  owner    = var.owner
  textract = []
}

module "ocular" {
  source                  = "../../modules/iam_role/k8s/v1"
  app                     = "ocular"
  eks_cluster_oidc_issuer = var.eks_cluster_oidc_issuer
  env                     = var.env
  s3Permissions           = []
  owner                   = var.owner
  textract                = [1]
}

module "facematch" {
  source                  = "../../modules/iam_role/k8s/v1"
  app                     = "facematch"
  eks_cluster_oidc_issuer = var.eks_cluster_oidc_issuer
  env                     = var.env
  s3Permissions = [{
    resource = "epifi-liveness"
    actions  = ["s3:*"]
  }]
  owner = var.owner
  rekognitionPermissions = [{
    resource = [
      "*"
    ]
    actions = [
      "rekognition:*"
    ]
  }]
  textract = []
}

module "onboarding-risk-detection" {
  source                  = "../../modules/iam_role/k8s/v1"
  app                     = "onboarding-risk-detection"
  eks_cluster_oidc_issuer = var.eks_cluster_oidc_issuer
  env                     = var.env
  s3Permissions = [{
    resource = "epifi-data-services-dev"
    actions  = ["s3:ListBucket", "s3:GetObject"]
  }]
  owner                  = var.owner
  rekognitionPermissions = []
  textract               = []
}

module "liveness" {
  source                  = "../../modules/iam_role/k8s/v1"
  app                     = "liveness"
  eks_cluster_oidc_issuer = var.eks_cluster_oidc_issuer
  env                     = var.env
  s3Permissions = [
    {
      resource = "epifi-prod-liveness"
      actions  = ["s3:ListBucket", "s3:GetObject"]
    }
  ]
  owner                  = var.owner
  rekognitionPermissions = []
  textract               = []
}
