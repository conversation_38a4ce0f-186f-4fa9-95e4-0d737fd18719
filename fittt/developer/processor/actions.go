package processor

import (
	"context"
	"fmt"
	"strings"
	"time"

	"google.golang.org/protobuf/encoding/protojson"

	actionpb "github.com/epifi/gamma/api/fittt/action"

	"github.com/epifi/gamma/api/fittt/developer"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	devPb "github.com/epifi/gamma/api/fittt/developer"
	"github.com/epifi/gamma/fittt/dao"
)

type DevActions struct {
	actionsDao dao.FitttActionsDao
}

func NewDevActions(actionsDao dao.FitttActionsDao) *DevActions {
	return &DevActions{actionsDao: actionsDao}
}

const (
	subscriptionIdParam = "subscription_id"
	pageSize            = "page_size"
	offset              = "offset"
)

func (d *DevActions) FetchParamList(ctx context.Context, entity devPb.FITTTEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            IdParam,
			Label:           "Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            actionExecutionIdParam,
			Label:           "Exec ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            subscriptionIdParam,
			Label:           "Subscription ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            pageSize,
			Label:           "Page Size",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		}, {
			Name:            offset,
			Label:           "Page Offset",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            createdAfterParam,
			Label:           "Created After - RFC3339 format",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            createdBeforeParam,
			Label:           "Created Before - RFC3339 format",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *DevActions) FetchData(ctx context.Context, entity developer.FITTTEntity, filters []*db_state.Filter) (string, error) {
	id, execId, subsId, createdAfter, createdBefore, pageSize, err := d.getParams(ctx, filters)
	if err != nil {
		logger.Error(ctx, "error while getting params", zap.Error(err))
		return "", err
	}
	if pageSize == 0 {
		pageSize = 20
	}
	var actions []*actionpb.Action
	switch {
	case id != "":
		a, err2 := d.actionsDao.GetActionById(ctx, id)
		if err2 != nil {
			logger.Error(ctx, "error while fetching actions from fit service", zap.String("actionId", id), zap.Error(err2))
			return "", err2
		}
		actions = []*actionpb.Action{a}
	case execId != "":
		a, err2 := d.actionsDao.GetActionByExecutionId(ctx, execId)
		if err2 != nil {
			logger.Error(ctx, "error while fetching actions from fit service", zap.String("actionId", id), zap.Error(err2))
			return "", err2
		}
		actions = []*actionpb.Action{a}
	default:
		actions, err = d.actionsDao.GetActions(ctx, subsId, createdAfter, createdBefore, pageSize)
		if err != nil {
			logger.Error(ctx, "error while fetching actions from fit service", zap.String(logger.SUBSCRIPTION_ID, subsId), zap.Error(err))
			return "", err
		}
	}

	marshalOptions := protojson.MarshalOptions{}
	marshalOptions.UseEnumNumbers = false
	var elements []string
	for _, action := range actions {
		a, err := marshalOptions.Marshal(action)
		if err != nil {
			logger.Error(ctx, "cannot marshal actions to json", zap.Error(err))
			return fmt.Sprintf("{\"error\": \"cannot marshal actions to json \", \"error\":\"%v\"}", err.Error()), nil
		}
		elements = append(elements, string(a))
	}
	return "[" + strings.Join(elements, ",") + "]", nil
}

func (d *DevActions) getParams(ctx context.Context, filters []*db_state.Filter) (id string, execId string, subsId string, createdAfter *time.Time, createdBefore *time.Time, limit int, err error) {
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case IdParam:
			id = filter.GetStringValue()
		case subscriptionIdParam:
			subsId = filter.GetStringValue()
		case actionExecutionIdParam:
			execId = filter.GetStringValue()
		case createdAfterParam:
			if filter.GetStringValue() == "" {
				continue
			}
			t, err := time.Parse(time.RFC3339, filter.GetStringValue())
			if err != nil {
				logger.Error(ctx, "failed to parse createdAfter", zap.Error(err))
				return "", "", "", nil, nil, 0, fmt.Errorf("invalid RFC3339 format created after. %w", err)
			}
			createdAfter = &t
		case createdBeforeParam:
			if filter.GetStringValue() == "" {
				continue
			}
			t, err := time.Parse(time.RFC3339, filter.GetStringValue())
			if err != nil {
				logger.Error(ctx, "failed to parse createdBefore", zap.Error(err))
				return "", "", "", nil, nil, 0, fmt.Errorf("invalid RFC3339 format created before. %w", err)
			}
			createdBefore = &t
		case limitParam:
			limit = int(filter.GetIntegerValue())
		}
	}
	return
}

// ActionJson omits sensitive fields like ActionData holding deposit account ID and deposit amount.
type ActionJson struct {
	ActionId       string
	ActorId        string
	ExecutionId    string
	SubscriptionId string
	CreatedAt      time.Time
	UpdatedAt      time.Time
	DeletedAt      *time.Time
}
