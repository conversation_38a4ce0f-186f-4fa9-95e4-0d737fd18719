package offervendor

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	gammanames "github.com/epifi/gamma/pkg/names"

	"context"
	"fmt"
	"strings"

	"github.com/epifi/be-common/pkg/epifigrpc"

	userPb "github.com/epifi/gamma/api/user"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"

	casperPb "github.com/epifi/gamma/api/casper"
	qwikcilverVgPb "github.com/epifi/gamma/api/vendorgateway/offers/qwikcilver"
	"github.com/epifi/gamma/casper/redemption/dao"
	"github.com/epifi/gamma/casper/redemption/dao/model"
)

// todo (utkarsh) : should we convert it into an enum ?
// Following are all the order statuses supported by Qwikcilver
const (
	// Note : as per vendor recommendation, case-insensitive comparison should be used for comparing order status.
	// This indicates order is created, but haven’t initiated payment redemption
	qwikcilverPendingOrderStatus = "PENDING"
	// This indicates payment is received and cards are getting activated.
	qwikcilverProcessingOrderStatus = "PROCESSING"
	// This indicates order is canceled and money is reversed.
	qwikcilverCancelledOrderStatus = "CANCELED"
	// This indicates payment is received and cards are activated.
	qwikcilverCompleteOrderStatus = "COMPLETE"

	// custom error message for cases where qwikcilver throws error for not supporting particular denomination for a product SKU.
	qwikcilverDenominationNotAvailable = "Denomination is not available"
	// custom error message for cases where qwikcilver throws error for restricted products for a particular SKU.
	qwikcilverProductsAreRestricted = "Unable to process your order as some of the products are restricted for your account"
)

const (
	// We have created this custom order status to denote cases where order was not found in vendor system with some vendorRefId.
	// This is possible in cases when the vendorRefId was generated in our system, but we failed to call vendor's create order api
	// or create order api was called but it got timed out.
	notFoundOrderStatus = "NOT_FOUND"
)

type qwikcilverVendor struct {
	qwikcilverVgClient      qwikcilverVgPb.QwikcilverClient
	qwikcilverRedemptionDao dao.IQwikcilverRedemptionDao
	userClient              userPb.UsersClient
}

func NewQwikcilverVendor(qwikcilverVgClient qwikcilverVgPb.QwikcilverClient, qwikcilverRedemptionDao dao.IQwikcilverRedemptionDao, userClient userPb.UsersClient) *qwikcilverVendor {
	return &qwikcilverVendor{qwikcilverVgClient: qwikcilverVgClient, qwikcilverRedemptionDao: qwikcilverRedemptionDao, userClient: userClient}
}

// compile time check to make sure qwikcilverVendor implements IOfferVendor
var _ IOfferVendor = &qwikcilverVendor{}

func (q *qwikcilverVendor) RedeemOffer(ctx context.Context, req *RedeemOfferRequest) (*RedeemOfferResponse, error) {
	resp := &RedeemOfferResponse{}

	// get or create vendor redemption entry
	qwikcilverRedemption, err := q.getOrCreateQwikcilverRedemptionEntry(ctx, req.ReferenceId, req.OfferType, req.RequestSource)
	if err != nil {
		logger.Error(ctx, "error fetching or creating qwikcilver redemption entry", zap.String(logger.REDEEMED_OFFER_ID, req.ReferenceId), zap.Error(err))
		// returning in progress as we want the client to treat it as transient failure.
		resp.RedemptionStatus = IN_PROGRESS
		return resp, nil
	}

	// get or create vendor order using vendorRefId
	vendorOrderStatus, vendorEgvCardDetails, err := q.getOrCreateVendorOrder(ctx, req.ActorId, qwikcilverRedemption.VendorRefId, req.VendorOfferMetadata.GetQwikcilverVendorOfferMetadata().GetSkuId(), req.OfferMetadata.GiftCardMetadata.GetGiftCardValue())
	if err != nil {
		logger.Error(ctx, "error fetching or creating qwikcilver vendor order", zap.String(logger.REDEEMED_OFFER_ID, req.ReferenceId), zap.Error(err))
		// updates the redemption status based on error responses.
		updateRedemptionStatusBasedOnError(resp, err)
		return resp, nil
	}

	// handle the vendor order status and return response
	// todo (utkarsh) : refactor it into a separate function ?
	switch {
	// if order is in competed state, parse the vendor egv details to internal gift card proto and return success response
	case strings.EqualFold(vendorOrderStatus, qwikcilverCompleteOrderStatus):
		logger.Info(ctx, "qwikcilver egv order successful", zap.String(logger.REDEEMED_OFFER_ID, req.ReferenceId))
		if len(vendorEgvCardDetails) == 0 {
			logger.Error(ctx, "empty egv details from qwikcilver vendor for a successful order", zap.String(logger.REDEEMED_OFFER_ID, req.ReferenceId))
			// returning in progress as we want the client to treat it as transient failure.
			resp.RedemptionStatus = IN_PROGRESS
		}
		resp.OfferDetails = &RedeemedOfferDetails{
			EGiftCardDetails: convertQwikcilverEGVCardToInternalEGVCard(ctx, vendorEgvCardDetails[0]),
		}
		resp.RedemptionStatus = SUCCESS

	case strings.EqualFold(vendorOrderStatus, qwikcilverCancelledOrderStatus):
		logger.Info(ctx, "qwikcilver egv order failed", zap.String(logger.REDEEMED_OFFER_ID, req.ReferenceId))
		resp.RedemptionStatus = FAILED

	case strings.EqualFold(vendorOrderStatus, qwikcilverPendingOrderStatus), strings.EqualFold(vendorOrderStatus, qwikcilverProcessingOrderStatus):
		resp.RedemptionStatus = IN_PROGRESS
	default:
		// returning in progress as we want the client to treat it as transient failure.
		resp.RedemptionStatus = IN_PROGRESS
		logger.Error(ctx, "unhandled qwikcilver vendor order status", zap.String(logger.REDEEMED_OFFER_ID, req.ReferenceId), zap.String(logger.ORDER_STATUS, vendorOrderStatus))
	}
	return resp, nil
}

func (q *qwikcilverVendor) GetRedeemedOffersDetailsRedirectionUrl(ctx context.Context, req *RedeemedOffersDetailsRedirectionUrlRequest) (string, error) {
	return "", fmt.Errorf("vendor does not supports redeemed offer details page redirection url")
}

// getOrCreateVendorOrder fetches the vendor order (if already created) using vendorRefId or creates an order with given vendorRefId if not
// already created. Idempotency on order is enforced using vendorRefId. Returns current order status and egv card details.
func (q *qwikcilverVendor) getOrCreateVendorOrder(ctx context.Context, actorId, vendorRefId string, egvProductSku string, egvAmount int32) (string, []*qwikcilverVgPb.Card, error) {
	var egvCards []*qwikcilverVgPb.Card

	// get vendor orderId and order status
	vendorOrderId, vendorOrderStatus, err := q.getVendorOrderIdAndStatus(ctx, vendorRefId)
	if err != nil {
		return "", nil, err
	}

	// handle order status and return response
	switch {
	// if order not already created, create one.
	case strings.EqualFold(vendorOrderStatus, notFoundOrderStatus):
		if vendorOrderStatus, egvCards, err = q.createVendorEGVOrder(ctx, actorId, vendorRefId, egvProductSku, egvAmount); err != nil {
			return "", nil, err
		}
		return vendorOrderStatus, egvCards, nil

	// if order is in completed state, fetch and return egv card details
	case strings.EqualFold(vendorOrderStatus, qwikcilverCompleteOrderStatus):
		if egvCards, err = q.getEGVOrderDetailsFromVendor(ctx, vendorOrderId); err != nil {
			return "", nil, err
		}
		return vendorOrderStatus, egvCards, nil

	case strings.EqualFold(vendorOrderStatus, qwikcilverCancelledOrderStatus):
		return vendorOrderStatus, nil, nil

	case strings.EqualFold(vendorOrderStatus, qwikcilverPendingOrderStatus), strings.EqualFold(vendorOrderStatus, qwikcilverProcessingOrderStatus):
		return vendorOrderStatus, nil, nil

	default:
		logger.Error(ctx, "unhandled qwikcilver vendor order status", zap.String(logger.REFERENCE_ID, vendorRefId), zap.String(logger.ORDER_STATUS, vendorOrderStatus))
		return "", nil, fmt.Errorf("unhandled qwikcilver vendor order status : %s", vendorOrderStatus)
	}
}

// getVendorOrderIdAndStatus returns the orderId AND orderStatus of vendor order which has given refId (each order has a unique refId)
func (q *qwikcilverVendor) getVendorOrderIdAndStatus(ctx context.Context, vendorRefId string) (string, string, error) {
	vendorOrderStatusResp, err := q.qwikcilverVgClient.GetOrderStatus(ctx, &qwikcilverVgPb.GetOrderStatusRequest{
		Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_QWIKCILVER},
		RefNumber: vendorRefId,
	})
	if err != nil || !vendorOrderStatusResp.GetStatus().IsSuccess() && !vendorOrderStatusResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error fetching order status from qwikcilver vendor", zap.String(logger.REFERENCE_ID, vendorRefId), zap.Any(logger.RPC_STATUS, vendorOrderStatusResp.GetStatus()), zap.Error(err))
		return "", "", errors.New("error fetching order status from qwikcilver vendor")
	}
	if vendorOrderStatusResp.GetStatus().IsRecordNotFound() {
		return "", notFoundOrderStatus, nil
	}
	return vendorOrderStatusResp.GetOrderId(), vendorOrderStatusResp.GetOrderStatus(), nil
}

// getEGVOrderDetailsFromVendor fetches the EGV Card details from qwikcilver vendor for given vendorOrderId.
func (q *qwikcilverVendor) getEGVOrderDetailsFromVendor(ctx context.Context, vendorOrderId string) ([]*qwikcilverVgPb.Card, error) {
	vendorEGVOrderDetailsResp, err := q.qwikcilverVgClient.GetActivatedCardDetails(ctx, &qwikcilverVgPb.GetActivatedCardDetailsRequest{
		Header:  &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_QWIKCILVER},
		OrderId: vendorOrderId,
	})
	if err != nil || !vendorEGVOrderDetailsResp.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching egv order details from qwikcilver vendor", zap.String(logger.ORDER_ID, vendorOrderId), zap.Any(logger.RPC_STATUS, vendorEGVOrderDetailsResp.GetStatus()), zap.Error(err))
		return nil, errors.New("error fetching egv order details from qwikcilver vendor")
	}
	return vendorEGVOrderDetailsResp.GetCards(), nil
}

// createVendorEGVOrder creates an egv order at qwikcilver vendor end.
// returns order status and egv card details.
func (q *qwikcilverVendor) createVendorEGVOrder(ctx context.Context, actorId, vendorRefId string, productSku string, egvAmount int32) (string, []*qwikcilverVgPb.Card, error) {
	userDetailsRes, err := q.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{ActorId: actorId},
	})
	if rpcErr := epifigrpc.RPCError(userDetailsRes, err); rpcErr != nil {
		return "", nil, fmt.Errorf("userClient.GetUser rpc call failed, err : %w", rpcErr)
	}
	userProfile := userDetailsRes.GetUser().GetProfile()

	vendorCreateOrderResp, err := q.qwikcilverVgClient.CreateOrder(ctx, &qwikcilverVgPb.CreateOrderRequest{
		Header:     &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_QWIKCILVER},
		RefNumber:  vendorRefId,
		ProductSku: productSku,
		Amount:     egvAmount,
		UserDetails: &qwikcilverVgPb.CreateOrderRequest_UserDetails{
			Name:        gammanames.BestNameFromProfile(ctx, userProfile),
			PhoneNumber: userProfile.GetPhoneNumber(),
		},
	})
	if rpcErr := epifigrpc.RPCError(vendorCreateOrderResp, err); rpcErr != nil || !vendorCreateOrderResp.GetStatus().IsSuccess() {
		logger.Error(ctx, "error creating qwikcilver egv order", zap.String(logger.REFERENCE_ID, vendorRefId), zap.String("productSku", productSku), zap.Int32("egvAmount", egvAmount), zap.Any(logger.RPC_STATUS, vendorCreateOrderResp.GetStatus()), zap.Error(rpcErr))
		return "", nil, getSpecificVendorOrderErrorResponse(rpcErr)
	}
	return vendorCreateOrderResp.GetOrderStatus(), vendorCreateOrderResp.GetCards(), nil
}

// getOrCreateQwikcilverRedemptionEntry fetches or creates a qwikcilver redemption entry.
// Idempotency on this entry is enforced using refId.
func (q *qwikcilverVendor) getOrCreateQwikcilverRedemptionEntry(ctx context.Context, refId string, offerType casperPb.OfferType, requestSource casperPb.RequestSource) (*model.QwikcilverRedemption, error) {
	// fetch entry details from db (if present) using refId
	qwikcilverRedemption, err := q.qwikcilverRedemptionDao.GetByRefId(ctx, refId)
	if err != nil {
		return nil, errors.Wrap(err, "error fetching qwikcilver redemption details from db")
	}
	if qwikcilverRedemption != nil {
		return qwikcilverRedemption, nil
	}
	// if entry is not present, create one now.
	// idempotency is ensured by db unique constraint check on refId
	qwikcilverRedemption, err = q.qwikcilverRedemptionDao.Create(ctx, &model.QwikcilverRedemption{
		RefId:         refId,
		OfferType:     offerType,
		VendorRefId:   generateVendorRefId(),
		RequestSource: requestSource,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error fetching qwikcilver redemption details from db")
	}
	return qwikcilverRedemption, nil
}

// Converts qwikcilver vendor specific EGV card details proto to an internal egv card details proto.
func convertQwikcilverEGVCardToInternalEGVCard(_ context.Context, qwikcilverEGVCard *qwikcilverVgPb.Card) *casperPb.EGiftCardDetails {
	return &casperPb.EGiftCardDetails{
		CardNo:         qwikcilverEGVCard.GetCardNumber(),
		Pin:            qwikcilverEGVCard.GetCardPin(),
		ActivationCode: qwikcilverEGVCard.GetActivationCode(),
		ActivationUrl:  qwikcilverEGVCard.GetActivationUrl(),
		ExpiryTime:     qwikcilverEGVCard.GetValidity(),
	}
}

// generates a unique refId to be used for initiating vendor redemption.
// Prefixed short org code (EPI) in refId as recommended by the vendor.
func generateVendorRefId() string {
	return fmt.Sprintf("EPI_%s", idgen.RandAlphaNumericString(15))
}

// function to handle for sending specific errors based on vendor's error response.
func getSpecificVendorOrderErrorResponse(err error) error {
	switch {
	case strings.Contains(err.Error(), qwikcilverDenominationNotAvailable):
		return errors.New(qwikcilverDenominationNotAvailable)
	case strings.Contains(err.Error(), qwikcilverProductsAreRestricted):
		return errors.New(qwikcilverProductsAreRestricted)
	default:
		return errors.New("error creating egv order details")
	}
}

// updates the redemption status based on error responses.
// for cases like denomination not available we want to mark the redemption status to FAILED to mark it as terminal state.
func updateRedemptionStatusBasedOnError(resp *RedeemOfferResponse, err error) {
	switch err.Error() {
	case qwikcilverDenominationNotAvailable:
		resp.RedemptionStatus = FAILED
	case qwikcilverProductsAreRestricted:
		resp.RedemptionStatus = FAILED
	default:
		// returning in progress as we want the client to treat it as transient failure.
		resp.RedemptionStatus = IN_PROGRESS
	}
}
