package dao

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	casperPb "github.com/epifi/gamma/api/casper"
	"github.com/epifi/gamma/casper/config/genconf"
	"github.com/epifi/gamma/casper/dao/model"
)

type OfferListingCacheDao struct {
	OfferListingDao
	cacheStorage cache.CacheStorage
	cacheConfig  *genconf.CacheConfig
}

func NewOfferListingCacheDao(cacheStorage cache.CacheStorage, offerListingPgdbDao *OfferListingDaoImpl, cacheConfig *genconf.CacheConfig) *OfferListingCacheDao {
	return &OfferListingCacheDao{
		OfferListingDao: offerListingPgdbDao,
		cacheStorage:    cacheStorage,
		cacheConfig:     cacheConfig,
	}
}

func (o *OfferListingCacheDao) GetActiveOfferListingsByFilters(ctx context.Context, filters *model.GetActiveOfferListingFilters) (map[casperPb.OfferRedemptionMode][]*casperPb.OfferListing, error) {
	defer metric_util.TrackDuration("casper/dao", "OfferListingCacheDao", "GetActiveOfferListingsByFilters", time.Now())
	if len(filters.RedemptionModes) == 0 {
		return nil, fmt.Errorf("redemption modes cannot be empty")
	}

	redemptionModeToListingsMap, offerListingToBeFetchedFromDbForRedemptionModes, err := o.fetchActiveOfferListingsFromCache(ctx, filters.RedemptionModes)
	if err != nil {
		logger.Error(ctx, "error fetching offer listings from cache", zap.Error(err))
		offerListingToBeFetchedFromDbForRedemptionModes = filters.RedemptionModes
	}

	if len(offerListingToBeFetchedFromDbForRedemptionModes) > 0 {
		redemptionModeToOfferListingsMapFromDb, getErr := o.OfferListingDao.GetActiveOfferListingsByFilters(ctx, &model.GetActiveOfferListingFilters{
			RedemptionModes: offerListingToBeFetchedFromDbForRedemptionModes,
		})
		if getErr != nil {
			return nil, fmt.Errorf("error fetching offer listings from db, err: %w", getErr)
		}

		setErr := o.setActiveOfferListingsInCache(ctx, redemptionModeToOfferListingsMapFromDb)
		if setErr != nil {
			logger.Error(ctx, "error while setting offer listings in cache", zap.Error(setErr))
		}
		redemptionModeToListingsMap = lo.Assign(redemptionModeToListingsMap, redemptionModeToOfferListingsMapFromDb)
	}

	logger.Debug(ctx, "casper cache, fetched offer listings")
	return redemptionModeToListingsMap, nil
}

func (o *OfferListingCacheDao) UpdateOfferListing(ctx context.Context, req *casperPb.UpdateOfferListingRequest) error {
	defer metric_util.TrackDuration("casper/dao", "OfferListingCacheDao", "UpdateOfferListing", time.Now())

	// delete active listings for all redemption modes from cache before updating any listing to keep the cache updated
	err := o.deleteActiveOfferListingForAllRedemptionModesFromCache(ctx)
	if err != nil {
		return fmt.Errorf("error while deleting offer listing from cache, id: %s, err: %w", req.GetOfferListingId(), err)
	}

	return o.OfferListingDao.UpdateOfferListing(ctx, req)
}

func (o *OfferListingCacheDao) DeleteOfferListingById(ctx context.Context, offerListingId string) error {
	defer metric_util.TrackDuration("casper/dao", "OfferListingCacheDao", "DeleteOfferListingById", time.Now())

	// delete active listings for all redemption modes from cache before deleting any listing to keep the cache updated
	err := o.deleteActiveOfferListingForAllRedemptionModesFromCache(ctx)
	if err != nil {
		return fmt.Errorf("error while deleting offer listing from cache, id: %s, err: %w", offerListingId, err)
	}

	return o.OfferListingDao.DeleteOfferListingById(ctx, offerListingId)
}

func (o *OfferListingCacheDao) fetchActiveOfferListingsFromCache(ctx context.Context, redemptionModes []casperPb.OfferRedemptionMode) (map[casperPb.OfferRedemptionMode][]*casperPb.OfferListing, []casperPb.OfferRedemptionMode, error) {
	var keys []string
	for _, redemptionMode := range redemptionModes {
		keys = append(keys, getOfferListingCacheKey(redemptionMode.String()))
	}

	var keysToBeFetchedFromDb []casperPb.OfferRedemptionMode
	cacheResp, err := o.cacheStorage.MultiGet(ctx, keys)
	if err != nil {
		return nil, nil, fmt.Errorf("error while fetching listings from cache, err: %w", err)
	}

	redemptionModeToListingsMap := make(map[casperPb.OfferRedemptionMode][]*casperPb.OfferListing)
	for idx, listingsString := range cacheResp {
		if listingsString == "" {
			keysToBeFetchedFromDb = append(keysToBeFetchedFromDb, redemptionModes[idx])
			continue
		}

		offerListings := &casperPb.OfferListings{}
		err = proto.Unmarshal([]byte(listingsString), offerListings)
		if err != nil {
			logger.Error(ctx, "failed to unmarshal offer listings from cache", zap.Error(err))
			// get this actor from db
			keysToBeFetchedFromDb = append(keysToBeFetchedFromDb, redemptionModes[idx])
			// Failed to unmarshall value. Delete this from cache (best-effort).
			err = o.deleteActiveOfferListingForRedemptionModeFromCache(ctx, redemptionModes[idx])
			if err != nil {
				logger.Error(ctx, "failed to delete actor in cache", zap.String("redemptionMode", redemptionModes[idx].String()), zap.Error(err))
			}
			continue
		}
		redemptionModeToListingsMap[redemptionModes[idx]] = append(redemptionModeToListingsMap[redemptionModes[idx]], offerListings.GetOfferListings()...)
	}

	return redemptionModeToListingsMap, keysToBeFetchedFromDb, nil
}

func (o *OfferListingCacheDao) setActiveOfferListingsInCache(ctx context.Context, redemptionModeToListingsMap map[casperPb.OfferRedemptionMode][]*casperPb.OfferListing) error {
	cacheKeys := make([]string, 0, len(redemptionModeToListingsMap))
	marshalledListingsList := make([]string, 0, len(redemptionModeToListingsMap))
	expirationTimeList := make([]time.Duration, 0, len(redemptionModeToListingsMap))

	for redemptionMode, listings := range redemptionModeToListingsMap {
		marshalledListings, err := proto.Marshal(&casperPb.OfferListings{
			OfferListings: listings,
		})
		if err != nil {
			logger.Error(ctx, "error while marshalling the offer listings", zap.Error(err))
			continue
		}

		cacheKeys = append(cacheKeys, getOfferListingCacheKey(redemptionMode.String()))
		marshalledListingsList = append(marshalledListingsList, string(marshalledListings))
		expirationTimeList = append(expirationTimeList, o.cacheConfig.CacheTtl())
	}

	err := o.cacheStorage.MultiSet(ctx, cacheKeys, marshalledListingsList, expirationTimeList)
	if err != nil {
		return fmt.Errorf("error while setting offer listings in cache, err: %w", err)
	}

	return nil
}

func (o *OfferListingCacheDao) deleteActiveOfferListingForRedemptionModeFromCache(ctx context.Context, redemptionMode casperPb.OfferRedemptionMode) error {
	return o.cacheStorage.Delete(ctx, getOfferListingCacheKey(redemptionMode.String()))
}

func (o *OfferListingCacheDao) deleteActiveOfferListingForAllRedemptionModesFromCache(ctx context.Context) error {
	var keys []string
	for redemptionMode := range casperPb.OfferRedemptionMode_value {
		if redemptionMode == casperPb.OfferRedemptionMode_UNSPECIFIED_REDEMPTION_MODE.String() {
			continue
		}
		keys = append(keys, getOfferListingCacheKey(redemptionMode))
	}
	return o.cacheStorage.Delete(ctx, keys...)
}

func getOfferListingCacheKey(redemptionMode string) string {
	return fmt.Sprintf(casperOfferListingForRedemptionModeCacheKey, redemptionMode)
}
