// Code generated by MockGen. DO NOT EDIT.
// Source: casper/exchanger/redemption_statemachine/rewardunitscalculator/calculator.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	exchanger "github.com/epifi/gamma/api/casper/exchanger"
	gomock "github.com/golang/mock/gomock"
)

// MockIRewardUnitsCalculator is a mock of IRewardUnitsCalculator interface
type MockIRewardUnitsCalculator struct {
	ctrl     *gomock.Controller
	recorder *MockIRewardUnitsCalculatorMockRecorder
}

// MockIRewardUnitsCalculatorMockRecorder is the mock recorder for MockIRewardUnitsCalculator
type MockIRewardUnitsCalculatorMockRecorder struct {
	mock *MockIRewardUnitsCalculator
}

// NewMockIRewardUnitsCalculator creates a new mock instance
func NewMockIRewardUnitsCalculator(ctrl *gomock.Controller) *MockIRewardUnitsCalculator {
	mock := &MockIRewardUnitsCalculator{ctrl: ctrl}
	mock.recorder = &MockIRewardUnitsCalculatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockIRewardUnitsCalculator) EXPECT() *MockIRewardUnitsCalculatorMockRecorder {
	return m.recorder
}

// CalculateUsingRangeProbabilityUnitsConfig mocks base method
func (m *MockIRewardUnitsCalculator) CalculateUsingRangeProbabilityUnitsConfig(config *exchanger.RangeProbabilityUnitsConfig) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculateUsingRangeProbabilityUnitsConfig", config)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculateUsingRangeProbabilityUnitsConfig indicates an expected call of CalculateUsingRangeProbabilityUnitsConfig
func (mr *MockIRewardUnitsCalculatorMockRecorder) CalculateUsingRangeProbabilityUnitsConfig(config interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculateUsingRangeProbabilityUnitsConfig", reflect.TypeOf((*MockIRewardUnitsCalculator)(nil).CalculateUsingRangeProbabilityUnitsConfig), config)
}
