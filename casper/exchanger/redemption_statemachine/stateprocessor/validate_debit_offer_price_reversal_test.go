package stateprocessor

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	exchangerPb "github.com/epifi/gamma/api/casper/exchanger"
	mockDao "github.com/epifi/gamma/casper/test/mocks/exchanger/dao"
	mockHelper "github.com/epifi/gamma/casper/test/mocks/helper"
)

func TestValidateDebitedOfferPriceReversalProcessor_Process(t *testing.T) {
	var (
		exchangerOrder1 = &exchangerPb.ExchangerOfferOrder{ActorId: "actor-1", State: exchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTIONS_GENERATION_FAILED, ExternalId: "external-id-1"}
		exchangerOffer1 = &exchangerPb.ExchangerOffer{RedemptionPrice: 200}
	)

	type args struct {
		ctx            context.Context
		exchangerOrder *exchangerPb.ExchangerOfferOrder
		exchangerOffer *exchangerPb.ExchangerOffer
		in3            *exchangerPb.TransitionAction
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockExchangerOrderDao *mockDao.MockIExchangerOfferOrderDao, mockUserHelperService *mockHelper.MockIUserHelperService)
		want       *StateProcessingResp
		wantErr    bool
	}{
		{
			name: "should return error when CheckIfRewardGeneratedForExchangerOfferOrderDebitReversal returns error",
			args: args{
				ctx:            context.Background(),
				exchangerOrder: exchangerOrder1,
				exchangerOffer: exchangerOffer1,
			},
			setupMocks: func(mockExchangerOrderDao *mockDao.MockIExchangerOfferOrderDao, mockUserHelperService *mockHelper.MockIUserHelperService) {
				mockUserHelperService.EXPECT().CheckIfRewardGeneratedForExchangerOfferOrderDebitReversal(gomock.Any(), exchangerOrder1, exchangerOffer1).
					Return(false, "", fmt.Errorf("error while calling CheckIfRewardGeneratedForExchangerOfferOrderDebitReversal"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return same redemption state when CheckIfRewardGeneratedForExchangerOfferOrderDebitReversal returns no reward and no error",
			args: args{
				ctx:            context.Background(),
				exchangerOrder: exchangerOrder1,
				exchangerOffer: exchangerOffer1,
			},
			setupMocks: func(mockExchangerOrderDao *mockDao.MockIExchangerOfferOrderDao, mockUserHelperService *mockHelper.MockIUserHelperService) {
				mockUserHelperService.EXPECT().CheckIfRewardGeneratedForExchangerOfferOrderDebitReversal(gomock.Any(), exchangerOrder1, exchangerOffer1).
					Return(false, "", nil)
			},
			want:    &StateProcessingResp{ReachedState: exchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTIONS_GENERATION_FAILED},
			wantErr: false,
		},
		{
			name: "should return no error when a reward has refunded units = redemption price",
			args: args{
				ctx:            context.Background(),
				exchangerOrder: exchangerOrder1,
				exchangerOffer: exchangerOffer1,
			},
			setupMocks: func(mockExchangerOrderDao *mockDao.MockIExchangerOfferOrderDao, mockUserHelperService *mockHelper.MockIUserHelperService) {
				mockUserHelperService.EXPECT().CheckIfRewardGeneratedForExchangerOfferOrderDebitReversal(gomock.Any(), exchangerOrder1, exchangerOffer1).
					Return(true, "", nil)
				mockExchangerOrderDao.EXPECT().Update(context.Background(), gomock.Any(), gomock.Any()).Return(nil)
			},
			want:    &StateProcessingResp{ReachedState: exchangerPb.ExchangerOfferOrderState_ORDER_STATE_REVERSED_DEBITED_OFFER_AMOUNT},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			mockExchangerOrderDao := mockDao.NewMockIExchangerOfferOrderDao(ctr)
			mockUserHelperService := mockHelper.NewMockIUserHelperService(ctr)

			tt.setupMocks(mockExchangerOrderDao, mockUserHelperService)

			v := NewValidateDebitedOfferPriceReversalProcessor(mockExchangerOrderDao, mockUserHelperService)
			got, err := v.Process(tt.args.ctx, tt.args.exchangerOrder, tt.args.exchangerOffer, tt.args.in3, nil)
			if (err != nil) != tt.wantErr {
				t.Errorf("Process() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Process() got = %v, want %v", got, tt.want)
			}
		})
	}
}
