// nolint
package creditcard

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/firefly/enums"
	creditCardPb "github.com/epifi/gamma/api/simulator/lending/creditcard"
	creditCardVendorPb "github.com/epifi/gamma/api/vendors/m2p/lending/creditcard"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/simulator/lending/creditcard/dao/model"
)

func (s *Service) RepayLoanAmount(ctx context.Context, loanAmountRequest *creditCardVendorPb.RepayLoanAmountRequest) (*creditCardVendorPb.RepayLoanAmountResponse, error) {
	_, customerDbError := s.CreditCardVendorResponseDao.GetById(ctx, loanAmountRequest.GetEntityId(), creditCardPb.VendorResponseType_VENDOR_RESPONSE_TYPE_REGISTER_CUSTOMER_RESPONSE, commonvgpb.Vendor_M2P)
	if customerDbError != nil {
		return getRepaymentInvalidEntityIdResponse(), nil
	}

	creditCardResponses, err := s.CreditCardVendorResponseDao.GetByEntityId(ctx, loanAmountRequest.GetEntityId(), creditCardPb.VendorResponseType_VENDOR_RESPONSE_TYPE_CREDIT_CARD_DETAILS, commonvgpb.Vendor_M2P)
	if err != nil || len(creditCardResponses) == 0 {
		return getRepaymentInvalidEntityIdResponse(), nil
	}

	activeCard := &creditCardPb.CreditCardDetails{}
	for _, creditcard := range creditCardResponses {
		if creditcard.GetResponseData().GetCreditCardDetails().GetCardStatus() == enums.CardStatus_ALLOCATED.String() {
			activeCard = creditcard.GetResponseData().GetCreditCardDetails()
		}
	}

	if loanAmountRequest.GetAmount() < 0 {
		return getRepaymentInvalidDataErrorResponse(), nil
	}

	if strings.Compare(loanAmountRequest.GetProductId(), productId) != 0 {
		return getRepaymentWalletForCriterionErrorResponse(), nil
	}

	txnId, err := getTxnId(loanAmountRequest.GetExternalTransactionId(), "")
	if err != nil {
		logger.Error(ctx, "error in fetching txn id:", zap.Error(err))
		return getRepaymentInvalidDataErrorResponse(), nil
	}
	txnDbResponse, err := s.CreditCardTransactionsDao.GetById(ctx, commonvgpb.Vendor_M2P, txnId)
	if txnDbResponse != nil {
		return &creditCardVendorPb.RepayLoanAmountResponse{
			TransactionId: &creditCardVendorPb.TransactionIdObject{
				TransactionId: txnDbResponse.TransactionDetails.TransactionReferenceId,
			},
		}, nil
	}

	txnRefString := generateRandomNumberString(9)
	txnRefNumber, _ := strconv.ParseInt(txnRefString, 10, 64)
	currTimeStamp := timestampPb.Now()
	currTime := currTimeStamp.AsTime()
	currTimeStr := strconv.Itoa(int(currTime.UnixMilli()))
	amount := fmt.Sprintf("%.2f", loanAmountRequest.GetAmount())
	txn := &model.CreditCardTransaction{
		Id:                      txnId,
		ReferenceId:             loanAmountRequest.GetEntityId(),
		TransactionType:         "ECOM",
		TransactionTransferType: "CREDIT",
		TransactionTime:         &currTime,
		TransactionStatus:       "PAYMENT_SUCCESS",
		BillingStatus:           billingStatusBilled,
		AuthorizationStatus:     authorizationStatusSettled,
		CreatedAt:               &currTime,
		UpdatedAt:               &currTime,
		TransactionDetails: &creditCardVendorPb.Transaction{
			Amount:                  amount,
			TransactionType:         loanAmountRequest.GetTransactionType(),
			TransactionTransferType: loanAmountRequest.GetTransactionOrigin(),
			TransactionTime:         currTime.UnixMilli(),
			TransactionReferenceId:  txnRefNumber,
			BeneficiaryName:         loanAmountRequest.GetOtherPartyName(),
			BeneficiaryId:           loanAmountRequest.GetOtherPartyId(),
			TransactionDescription:  loanAmountRequest.GetTransactionDescription(),
			TransactionOrigin:       loanAmountRequest.GetTransactionOrigin(),
			TransactionStatus:       "PAYMENT_SUCCESS",
			CustomerWalletName:      "GENERAL",
			ExternalTransactionId:   loanAmountRequest.GetExternalTransactionId(),
			ConvertedAmount:         loanAmountRequest.GetAmount(),
			KitNumber:               activeCard.GetKitNumber(),
			OtherPartyName:          loanAmountRequest.GetOtherPartyName(),
			OtherPartyId:            loanAmountRequest.GetOtherPartyName(),
			CreditDebit:             "CREDIT",
			TransactionCategory:     "DIRECT_CREDIT",
			ExtTransactionId:        loanAmountRequest.GetExternalTransactionId(),
			AuthorizationStatus:     authorizationStatusSettled,
			TransactionDate:         currTime.UnixMilli(),
		},
		Vendor: commonvgpb.Vendor_M2P,
	}

	_, txnErr := s.CreditCardTransactionsDao.Create(ctx, txn)
	if txnErr != nil {
		return getRepaymentInvalidDataErrorResponse(), nil
	}

	cardNotificationRequest := &creditCardVendorPb.CardNotificationRequest{
		EntityId:           loanAmountRequest.GetEntityId(),
		TxnReferenceNumber: txnRefString,
		TxnDate:            datetime.TimestampToString(currTimeStamp, "20060102150405", datetime.IST),
		TransactionType:    "DIRECT_CREDIT",
		TxnStatus:          "PAYMENT_SUCCESS",
		Amount:             amount,
		TxnOrigin:          "MOBILE",
		CardEnding:         activeCard.GetCardNumber()[len(activeCard.GetCardNumber())-4:],
		WalletList: &creditCardVendorPb.WalletList{
			General: &creditCardVendorPb.WalletList_General{
				CurrencyCode: "356",
			},
		},
		Description:         loanAmountRequest.GetTransactionDescription(),
		TransactionDateTime: currTimeStr,
		Crdr:                "CREDIT",
		ExternalTxnId:       loanAmountRequest.GetExternalTransactionId(),
	}

	err = s.updateCreditLimitFromTransaction(ctx, &creditCardPb.DevActionRequest{
		Request: &creditCardPb.DevActionRequest_CardNotificationRequest{CardNotificationRequest: cardNotificationRequest},
	})
	if err != nil {
		logger.Error(ctx, "unable to update limits", zap.Error(err))
		return getRepaymentInvalidDataErrorResponse(), nil
	}

	_, err = s.VendorNotificationCcClient.CardNotification(ctx, cardNotificationRequest)
	if err != nil {
		logger.Error(ctx, "error while sending notif to vendorgateway", zap.Error(err))
	}

	return &creditCardVendorPb.RepayLoanAmountResponse{
		TransactionId: &creditCardVendorPb.TransactionIdObject{
			TransactionId: txnRefNumber,
		},
	}, nil
}

func getRepaymentWalletForCriterionErrorResponse() *creditCardVendorPb.RepayLoanAmountResponse {
	return &creditCardVendorPb.RepayLoanAmountResponse{
		Exception: &creditCardVendorPb.Exception{
			DetailMessage: "Wallet for criteria not found",
			ShortMessage:  "Wallet for criteria not found",
			LanguageCode:  "en",
			ErrorCode:     "Y222",
		},
	}
}

func getRepaymentInvalidDataErrorResponse() *creditCardVendorPb.RepayLoanAmountResponse {
	return &creditCardVendorPb.RepayLoanAmountResponse{
		Exception: &creditCardVendorPb.Exception{
			DetailMessage: "Invalid Data",
			ShortMessage:  "Invalid Data",
			LanguageCode:  "en",
			ErrorCode:     "Y202",
		},
	}
}

func getRepaymentInvalidEntityIdResponse() *creditCardVendorPb.RepayLoanAmountResponse {
	return &creditCardVendorPb.RepayLoanAmountResponse{
		Exception: &creditCardVendorPb.Exception{
			DetailMessage: "Entity Id Invalid",
			ShortMessage:  "Entity Id Invalid",
			LanguageCode:  "en",
			ErrorCode:     "Y226",
		},
	}
}
