package federal

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/vendors/federal"
	card "github.com/epifi/gamma/simulator/openbanking/rsa/card_data"
)

func (cps *CardProvisioningService) CardCVVEnquiry(ctx context.Context, req *federal.CardCVVEnquiryRequest) (*federal.CardCVVEnquiryResponse, error) {

	cardModel, err := cps.cardDao.GetById(ctx, req.GetCardUniqueId())
	if err != nil {
		logger.Error(ctx, "error in getting card from db by cardUniqueId", zap.String("card-unique-id", req.GetCardUniqueId()), zap.Error(err))
		return nil, err
	}

	var cardData federal.CardData
	err = protojson.Unmarshal([]byte(cardModel.CardData), &cardData)
	if err != nil {
		return nil, err
	}

	encryptedCardData, err := card.GetEncryptedCardData(cps.rsaCryptor, &cardData)
	if err != nil {
		logger.ErrorNoCtx("failed to encrypt card data", zap.Error(err))
		return nil, err
	}

	responseCode, responseReason, responseAction := cps.ValidateCredBlock(req.GetCredBlock(), req.GetMobileNumber())

	return &federal.CardCVVEnquiryResponse{
		SenderCode:     req.GetSenderCode(),
		DeviceToken:    req.GetDeviceToken(),
		RequestId:      req.GetRequestId(),
		CardData:       encryptedCardData,
		ResponseCode:   responseCode,
		ResponseReason: responseReason,
		ResponseAction: responseAction,
	}, nil
}
