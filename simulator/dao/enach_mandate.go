package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	gormctxV2 "github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	simulatorEnachPb "github.com/epifi/gamma/api/simulator/openbanking/enach"
	"github.com/epifi/gamma/simulator/dao/model"
)

type EnachMandateDaoCrdb struct {
	db *gorm.DB
}

func NewEnachMandateDaoCrdb(db *gorm.DB) *EnachMandateDaoCrdb {
	return &EnachMandateDaoCrdb{db: db}
}

// compile time check to ensure EnachMandateDaoCrdb implements EnachMandateDao
var _ EnachMandateDao = &EnachMandateDaoCrdb{}

func (e *EnachMandateDaoCrdb) Create(ctx context.Context, enachMandate *simulatorEnachPb.EnachMandate) error {
	defer metric_util.TrackDuration("simulator/dao", "EnachMandateDaoCrdb", "Create", time.Now())

	db := gormctxV2.FromContextOrDefault(ctx, e.db)

	mandateDbModel := model.NewEnachMandateDbModel(enachMandate)
	if err := db.Create(mandateDbModel).Error; err != nil {
		return fmt.Errorf("failed to create enach mandate in simulator db, %w", err)
	}
	return nil
}

func (e *EnachMandateDaoCrdb) GetByUmrn(ctx context.Context, umrn string) (*simulatorEnachPb.EnachMandate, error) {
	defer metric_util.TrackDuration("simulator/dao", "EnachMandateDaoCrdb", "GetByUmrn", time.Now())

	db := gormctxV2.FromContextOrDefault(ctx, e.db)
	mandateDbModel := &model.EnachMandate{}
	if err := db.Where("umrn = ?", umrn).Take(mandateDbModel).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch enach mandate by umrn, %w", err)
	}
	return mandateDbModel.GetProto(), nil
}
