// Code generated by MockGen. DO NOT EDIT.
// Source: ./dao.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	dao "github.com/epifi/gamma/analyser/dao"
	model "github.com/epifi/gamma/analyser/dao/model"
	enums "github.com/epifi/gamma/api/analyser/enums"
	txnaggregates "github.com/epifi/gamma/api/analyser/txnaggregates"
	categorizer "github.com/epifi/gamma/api/categorizer"
	order "github.com/epifi/gamma/api/order"
	payment "github.com/epifi/gamma/api/order/payment"
	amount_filter "github.com/epifi/gamma/api/pay/amount_filter"
	external "github.com/epifi/gamma/api/tiering/external"
	types "github.com/epifi/gamma/api/typesv2"
	gomock "github.com/golang/mock/gomock"
)

// MockAnalyserTransactionUpdateHandlerDao is a mock of AnalyserTransactionUpdateHandlerDao interface.
type MockAnalyserTransactionUpdateHandlerDao struct {
	ctrl     *gomock.Controller
	recorder *MockAnalyserTransactionUpdateHandlerDaoMockRecorder
}

// MockAnalyserTransactionUpdateHandlerDaoMockRecorder is the mock recorder for MockAnalyserTransactionUpdateHandlerDao.
type MockAnalyserTransactionUpdateHandlerDaoMockRecorder struct {
	mock *MockAnalyserTransactionUpdateHandlerDao
}

// NewMockAnalyserTransactionUpdateHandlerDao creates a new mock instance.
func NewMockAnalyserTransactionUpdateHandlerDao(ctrl *gomock.Controller) *MockAnalyserTransactionUpdateHandlerDao {
	mock := &MockAnalyserTransactionUpdateHandlerDao{ctrl: ctrl}
	mock.recorder = &MockAnalyserTransactionUpdateHandlerDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAnalyserTransactionUpdateHandlerDao) EXPECT() *MockAnalyserTransactionUpdateHandlerDaoMockRecorder {
	return m.recorder
}

// GetCategoryAggregates mocks base method.
func (m *MockAnalyserTransactionUpdateHandlerDao) GetCategoryAggregates(ctx context.Context, actorId string, fromAccountIds []string, fromTime, toTime int64, updatedTxnIds []string, l0Ontologies []categorizer.L0, accountingEntry payment.AccountingEntryType) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCategoryAggregates", ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCategoryAggregates indicates an expected call of GetCategoryAggregates.
func (mr *MockAnalyserTransactionUpdateHandlerDaoMockRecorder) GetCategoryAggregates(ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategoryAggregates", reflect.TypeOf((*MockAnalyserTransactionUpdateHandlerDao)(nil).GetCategoryAggregates), ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry)
}

// GetDerivedEntityAggregates mocks base method.
func (m *MockAnalyserTransactionUpdateHandlerDao) GetDerivedEntityAggregates(ctx context.Context, fromActorId string, fromAccountIds []string, toActorTypes []types.ActorType, fromTime, toTime int64, excludedTxnIds []string, l0Ontologies []categorizer.L0, ontologyIds []string) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDerivedEntityAggregates", ctx, fromActorId, fromAccountIds, toActorTypes, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDerivedEntityAggregates indicates an expected call of GetDerivedEntityAggregates.
func (mr *MockAnalyserTransactionUpdateHandlerDaoMockRecorder) GetDerivedEntityAggregates(ctx, fromActorId, fromAccountIds, toActorTypes, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDerivedEntityAggregates", reflect.TypeOf((*MockAnalyserTransactionUpdateHandlerDao)(nil).GetDerivedEntityAggregates), ctx, fromActorId, fromAccountIds, toActorTypes, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds)
}

// GetMerchantAggregates mocks base method.
func (m *MockAnalyserTransactionUpdateHandlerDao) GetMerchantAggregates(ctx context.Context, actorId string, fromAccountIds []string, fromTime, toTime int64, updatedTxnIds []string, l0Ontologies []categorizer.L0, ontologyIds []string) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMerchantAggregates", ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, ontologyIds)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMerchantAggregates indicates an expected call of GetMerchantAggregates.
func (mr *MockAnalyserTransactionUpdateHandlerDaoMockRecorder) GetMerchantAggregates(ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, ontologyIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMerchantAggregates", reflect.TypeOf((*MockAnalyserTransactionUpdateHandlerDao)(nil).GetMerchantAggregates), ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, ontologyIds)
}

// GetTimeAggregates mocks base method.
func (m *MockAnalyserTransactionUpdateHandlerDao) GetTimeAggregates(ctx context.Context, actorId string, fromAccountIds []string, fromTime, toTime int64, column txnaggregates.GroupByDimension, updatedTxnIds, ontologyIds []string) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTimeAggregates", ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTimeAggregates indicates an expected call of GetTimeAggregates.
func (mr *MockAnalyserTransactionUpdateHandlerDaoMockRecorder) GetTimeAggregates(ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTimeAggregates", reflect.TypeOf((*MockAnalyserTransactionUpdateHandlerDao)(nil).GetTimeAggregates), ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds)
}

// GetTransactionDetails mocks base method.
func (m *MockAnalyserTransactionUpdateHandlerDao) GetTransactionDetails(ctx context.Context, fromActorId, transactionId string, updatedTxnIds []string, fromTime, toTime int64) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionDetails", ctx, fromActorId, transactionId, updatedTxnIds, fromTime, toTime)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionDetails indicates an expected call of GetTransactionDetails.
func (mr *MockAnalyserTransactionUpdateHandlerDaoMockRecorder) GetTransactionDetails(ctx, fromActorId, transactionId, updatedTxnIds, fromTime, toTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionDetails", reflect.TypeOf((*MockAnalyserTransactionUpdateHandlerDao)(nil).GetTransactionDetails), ctx, fromActorId, transactionId, updatedTxnIds, fromTime, toTime)
}

// GetTransactionTimeStampAggregate mocks base method.
func (m *MockAnalyserTransactionUpdateHandlerDao) GetTransactionTimeStampAggregate(ctx context.Context, actorId string, fromAccountIds []string, accountingEntryType payment.AccountingEntryType, excludedOntologyIds []string, aggregateType enums.AggregateType, updatedTxnIds []string) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionTimeStampAggregate", ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionTimeStampAggregate indicates an expected call of GetTransactionTimeStampAggregate.
func (mr *MockAnalyserTransactionUpdateHandlerDaoMockRecorder) GetTransactionTimeStampAggregate(ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionTimeStampAggregate", reflect.TypeOf((*MockAnalyserTransactionUpdateHandlerDao)(nil).GetTransactionTimeStampAggregate), ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds)
}

// GetUserAggregates mocks base method.
func (m *MockAnalyserTransactionUpdateHandlerDao) GetUserAggregates(ctx context.Context, fromActorId string, fromAccountIds []string, fromTime, toTime int64, excludedTxnIds []string, l0Ontologies []categorizer.L0, ontologyIds []string) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAggregates", ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAggregates indicates an expected call of GetUserAggregates.
func (mr *MockAnalyserTransactionUpdateHandlerDaoMockRecorder) GetUserAggregates(ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAggregates", reflect.TypeOf((*MockAnalyserTransactionUpdateHandlerDao)(nil).GetUserAggregates), ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds)
}

// MockAnalyserTransactionDao is a mock of AnalyserTransactionDao interface.
type MockAnalyserTransactionDao struct {
	ctrl     *gomock.Controller
	recorder *MockAnalyserTransactionDaoMockRecorder
}

// MockAnalyserTransactionDaoMockRecorder is the mock recorder for MockAnalyserTransactionDao.
type MockAnalyserTransactionDaoMockRecorder struct {
	mock *MockAnalyserTransactionDao
}

// NewMockAnalyserTransactionDao creates a new mock instance.
func NewMockAnalyserTransactionDao(ctrl *gomock.Controller) *MockAnalyserTransactionDao {
	mock := &MockAnalyserTransactionDao{ctrl: ctrl}
	mock.recorder = &MockAnalyserTransactionDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAnalyserTransactionDao) EXPECT() *MockAnalyserTransactionDaoMockRecorder {
	return m.recorder
}

// GetCategoryAggregates mocks base method.
func (m *MockAnalyserTransactionDao) GetCategoryAggregates(ctx context.Context, actorId string, fromAccountIds []string, fromTime, toTime int64, updatedTxnIds []string, l0Ontologies []categorizer.L0, accountingEntry payment.AccountingEntryType) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCategoryAggregates", ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCategoryAggregates indicates an expected call of GetCategoryAggregates.
func (mr *MockAnalyserTransactionDaoMockRecorder) GetCategoryAggregates(ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategoryAggregates", reflect.TypeOf((*MockAnalyserTransactionDao)(nil).GetCategoryAggregates), ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry)
}

// GetDerivedEntityAggregates mocks base method.
func (m *MockAnalyserTransactionDao) GetDerivedEntityAggregates(ctx context.Context, fromActorId string, fromAccountIds []string, toActorTypes []types.ActorType, fromTime, toTime int64, excludedTxnIds []string, l0Ontologies []categorizer.L0, ontologyIds []string) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDerivedEntityAggregates", ctx, fromActorId, fromAccountIds, toActorTypes, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDerivedEntityAggregates indicates an expected call of GetDerivedEntityAggregates.
func (mr *MockAnalyserTransactionDaoMockRecorder) GetDerivedEntityAggregates(ctx, fromActorId, fromAccountIds, toActorTypes, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDerivedEntityAggregates", reflect.TypeOf((*MockAnalyserTransactionDao)(nil).GetDerivedEntityAggregates), ctx, fromActorId, fromAccountIds, toActorTypes, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds)
}

// GetMerchantAggregates mocks base method.
func (m *MockAnalyserTransactionDao) GetMerchantAggregates(ctx context.Context, actorId string, fromAccountIds []string, fromTime, toTime int64, updatedTxnIds []string, l0Ontologies []categorizer.L0, ontologyIds []string) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMerchantAggregates", ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, ontologyIds)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMerchantAggregates indicates an expected call of GetMerchantAggregates.
func (mr *MockAnalyserTransactionDaoMockRecorder) GetMerchantAggregates(ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, ontologyIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMerchantAggregates", reflect.TypeOf((*MockAnalyserTransactionDao)(nil).GetMerchantAggregates), ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, ontologyIds)
}

// GetMinUpdatedAt mocks base method.
func (m *MockAnalyserTransactionDao) GetMinUpdatedAt(ctx context.Context) (time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinUpdatedAt", ctx)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMinUpdatedAt indicates an expected call of GetMinUpdatedAt.
func (mr *MockAnalyserTransactionDaoMockRecorder) GetMinUpdatedAt(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinUpdatedAt", reflect.TypeOf((*MockAnalyserTransactionDao)(nil).GetMinUpdatedAt), ctx)
}

// GetTimeAggregates mocks base method.
func (m *MockAnalyserTransactionDao) GetTimeAggregates(ctx context.Context, actorId string, fromAccountIds []string, fromTime, toTime int64, column txnaggregates.GroupByDimension, updatedTxnIds, ontologyIds []string) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTimeAggregates", ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTimeAggregates indicates an expected call of GetTimeAggregates.
func (mr *MockAnalyserTransactionDaoMockRecorder) GetTimeAggregates(ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTimeAggregates", reflect.TypeOf((*MockAnalyserTransactionDao)(nil).GetTimeAggregates), ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds)
}

// GetTimeAggregatesV2 mocks base method.
func (m *MockAnalyserTransactionDao) GetTimeAggregatesV2(ctx context.Context, input *dao.AggregatesInput) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTimeAggregatesV2", ctx, input)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTimeAggregatesV2 indicates an expected call of GetTimeAggregatesV2.
func (mr *MockAnalyserTransactionDaoMockRecorder) GetTimeAggregatesV2(ctx, input interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTimeAggregatesV2", reflect.TypeOf((*MockAnalyserTransactionDao)(nil).GetTimeAggregatesV2), ctx, input)
}

// GetTransaction mocks base method.
func (m *MockAnalyserTransactionDao) GetTransaction(ctx context.Context, transactionId string) ([]map[string]interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransaction", ctx, transactionId)
	ret0, _ := ret[0].([]map[string]interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransaction indicates an expected call of GetTransaction.
func (mr *MockAnalyserTransactionDaoMockRecorder) GetTransaction(ctx, transactionId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransaction", reflect.TypeOf((*MockAnalyserTransactionDao)(nil).GetTransaction), ctx, transactionId)
}

// GetTransactionAggregates mocks base method.
func (m *MockAnalyserTransactionDao) GetTransactionAggregates(ctx context.Context, ActorId string, piFilter, otherPiFilter []string, accountingEntryType payment.AccountingEntryType, fromExecutedTime, toExecutedTime, fromCreatedTime, toCreatedTime int64, paymentProtocol []payment.PaymentProtocol, transactionsStatus []payment.TransactionStatus, orderTags []order.OrderTag, provenance []order.OrderProvenance, workflow []order.OrderWorkflow, uiEntryPoint []order.UIEntryPoint, amountFilter *amount_filter.AmountFilter, actorTier external.Tier, toActorId string) (*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionAggregates", ctx, ActorId, piFilter, otherPiFilter, accountingEntryType, fromExecutedTime, toExecutedTime, fromCreatedTime, toCreatedTime, paymentProtocol, transactionsStatus, orderTags, provenance, workflow, uiEntryPoint, amountFilter, actorTier, toActorId)
	ret0, _ := ret[0].(*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionAggregates indicates an expected call of GetTransactionAggregates.
func (mr *MockAnalyserTransactionDaoMockRecorder) GetTransactionAggregates(ctx, ActorId, piFilter, otherPiFilter, accountingEntryType, fromExecutedTime, toExecutedTime, fromCreatedTime, toCreatedTime, paymentProtocol, transactionsStatus, orderTags, provenance, workflow, uiEntryPoint, amountFilter, actorTier, toActorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionAggregates", reflect.TypeOf((*MockAnalyserTransactionDao)(nil).GetTransactionAggregates), ctx, ActorId, piFilter, otherPiFilter, accountingEntryType, fromExecutedTime, toExecutedTime, fromCreatedTime, toCreatedTime, paymentProtocol, transactionsStatus, orderTags, provenance, workflow, uiEntryPoint, amountFilter, actorTier, toActorId)
}

// GetTransactionDetails mocks base method.
func (m *MockAnalyserTransactionDao) GetTransactionDetails(ctx context.Context, fromActorId, transactionId string, updatedTxnIds []string, fromTime, toTime int64) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionDetails", ctx, fromActorId, transactionId, updatedTxnIds, fromTime, toTime)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionDetails indicates an expected call of GetTransactionDetails.
func (mr *MockAnalyserTransactionDaoMockRecorder) GetTransactionDetails(ctx, fromActorId, transactionId, updatedTxnIds, fromTime, toTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionDetails", reflect.TypeOf((*MockAnalyserTransactionDao)(nil).GetTransactionDetails), ctx, fromActorId, transactionId, updatedTxnIds, fromTime, toTime)
}

// GetTransactionTimeStampAggregate mocks base method.
func (m *MockAnalyserTransactionDao) GetTransactionTimeStampAggregate(ctx context.Context, actorId string, fromAccountIds []string, accountingEntryType payment.AccountingEntryType, excludedOntologyIds []string, aggregateType enums.AggregateType, updatedTxnIds []string) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionTimeStampAggregate", ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionTimeStampAggregate indicates an expected call of GetTransactionTimeStampAggregate.
func (mr *MockAnalyserTransactionDaoMockRecorder) GetTransactionTimeStampAggregate(ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionTimeStampAggregate", reflect.TypeOf((*MockAnalyserTransactionDao)(nil).GetTransactionTimeStampAggregate), ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds)
}

// GetUserAggregates mocks base method.
func (m *MockAnalyserTransactionDao) GetUserAggregates(ctx context.Context, fromActorId string, fromAccountIds []string, fromTime, toTime int64, excludedTxnIds []string, l0Ontologies []categorizer.L0, ontologyIds []string) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAggregates", ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAggregates indicates an expected call of GetUserAggregates.
func (mr *MockAnalyserTransactionDaoMockRecorder) GetUserAggregates(ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAggregates", reflect.TypeOf((*MockAnalyserTransactionDao)(nil).GetUserAggregates), ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds)
}

// MockAnalyserTransactionUpdateDao is a mock of AnalyserTransactionUpdateDao interface.
type MockAnalyserTransactionUpdateDao struct {
	ctrl     *gomock.Controller
	recorder *MockAnalyserTransactionUpdateDaoMockRecorder
}

// MockAnalyserTransactionUpdateDaoMockRecorder is the mock recorder for MockAnalyserTransactionUpdateDao.
type MockAnalyserTransactionUpdateDaoMockRecorder struct {
	mock *MockAnalyserTransactionUpdateDao
}

// NewMockAnalyserTransactionUpdateDao creates a new mock instance.
func NewMockAnalyserTransactionUpdateDao(ctrl *gomock.Controller) *MockAnalyserTransactionUpdateDao {
	mock := &MockAnalyserTransactionUpdateDao{ctrl: ctrl}
	mock.recorder = &MockAnalyserTransactionUpdateDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAnalyserTransactionUpdateDao) EXPECT() *MockAnalyserTransactionUpdateDaoMockRecorder {
	return m.recorder
}

// GetCategoryAggregates mocks base method.
func (m *MockAnalyserTransactionUpdateDao) GetCategoryAggregates(ctx context.Context, actorId string, fromAccountIds []string, fromTime, toTime int64, updatedTxnIds []string, l0Ontologies []categorizer.L0, accountingEntry payment.AccountingEntryType) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCategoryAggregates", ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCategoryAggregates indicates an expected call of GetCategoryAggregates.
func (mr *MockAnalyserTransactionUpdateDaoMockRecorder) GetCategoryAggregates(ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategoryAggregates", reflect.TypeOf((*MockAnalyserTransactionUpdateDao)(nil).GetCategoryAggregates), ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry)
}

// GetDerivedEntityAggregates mocks base method.
func (m *MockAnalyserTransactionUpdateDao) GetDerivedEntityAggregates(ctx context.Context, fromActorId string, fromAccountIds []string, toActorTypes []types.ActorType, fromTime, toTime int64, excludedTxnIds []string, l0Ontologies []categorizer.L0, ontologyIds []string) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDerivedEntityAggregates", ctx, fromActorId, fromAccountIds, toActorTypes, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDerivedEntityAggregates indicates an expected call of GetDerivedEntityAggregates.
func (mr *MockAnalyserTransactionUpdateDaoMockRecorder) GetDerivedEntityAggregates(ctx, fromActorId, fromAccountIds, toActorTypes, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDerivedEntityAggregates", reflect.TypeOf((*MockAnalyserTransactionUpdateDao)(nil).GetDerivedEntityAggregates), ctx, fromActorId, fromAccountIds, toActorTypes, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds)
}

// GetMerchantAggregates mocks base method.
func (m *MockAnalyserTransactionUpdateDao) GetMerchantAggregates(ctx context.Context, actorId string, fromAccountIds []string, fromTime, toTime int64, updatedTxnIds []string, l0Ontologies []categorizer.L0, ontologyIds []string) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMerchantAggregates", ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, ontologyIds)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMerchantAggregates indicates an expected call of GetMerchantAggregates.
func (mr *MockAnalyserTransactionUpdateDaoMockRecorder) GetMerchantAggregates(ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, ontologyIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMerchantAggregates", reflect.TypeOf((*MockAnalyserTransactionUpdateDao)(nil).GetMerchantAggregates), ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, ontologyIds)
}

// GetTimeAggregates mocks base method.
func (m *MockAnalyserTransactionUpdateDao) GetTimeAggregates(ctx context.Context, actorId string, fromAccountIds []string, fromTime, toTime int64, column txnaggregates.GroupByDimension, updatedTxnIds, ontologyIds []string) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTimeAggregates", ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTimeAggregates indicates an expected call of GetTimeAggregates.
func (mr *MockAnalyserTransactionUpdateDaoMockRecorder) GetTimeAggregates(ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTimeAggregates", reflect.TypeOf((*MockAnalyserTransactionUpdateDao)(nil).GetTimeAggregates), ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds)
}

// GetTransaction mocks base method.
func (m *MockAnalyserTransactionUpdateDao) GetTransaction(ctx context.Context, transactionId string) ([]map[string]interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransaction", ctx, transactionId)
	ret0, _ := ret[0].([]map[string]interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransaction indicates an expected call of GetTransaction.
func (mr *MockAnalyserTransactionUpdateDaoMockRecorder) GetTransaction(ctx, transactionId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransaction", reflect.TypeOf((*MockAnalyserTransactionUpdateDao)(nil).GetTransaction), ctx, transactionId)
}

// GetTransactionDetails mocks base method.
func (m *MockAnalyserTransactionUpdateDao) GetTransactionDetails(ctx context.Context, fromActorId, transactionId string, updatedTxnIds []string, fromTime, toTime int64) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionDetails", ctx, fromActorId, transactionId, updatedTxnIds, fromTime, toTime)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionDetails indicates an expected call of GetTransactionDetails.
func (mr *MockAnalyserTransactionUpdateDaoMockRecorder) GetTransactionDetails(ctx, fromActorId, transactionId, updatedTxnIds, fromTime, toTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionDetails", reflect.TypeOf((*MockAnalyserTransactionUpdateDao)(nil).GetTransactionDetails), ctx, fromActorId, transactionId, updatedTxnIds, fromTime, toTime)
}

// GetTransactionTimeStampAggregate mocks base method.
func (m *MockAnalyserTransactionUpdateDao) GetTransactionTimeStampAggregate(ctx context.Context, actorId string, fromAccountIds []string, accountingEntryType payment.AccountingEntryType, excludedOntologyIds []string, aggregateType enums.AggregateType, updatedTxnIds []string) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionTimeStampAggregate", ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionTimeStampAggregate indicates an expected call of GetTransactionTimeStampAggregate.
func (mr *MockAnalyserTransactionUpdateDaoMockRecorder) GetTransactionTimeStampAggregate(ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionTimeStampAggregate", reflect.TypeOf((*MockAnalyserTransactionUpdateDao)(nil).GetTransactionTimeStampAggregate), ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds)
}

// GetUserAggregates mocks base method.
func (m *MockAnalyserTransactionUpdateDao) GetUserAggregates(ctx context.Context, fromActorId string, fromAccountIds []string, fromTime, toTime int64, excludedTxnIds []string, l0Ontologies []categorizer.L0, ontologyIds []string) ([]*model.AnalyserTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAggregates", ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds)
	ret0, _ := ret[0].([]*model.AnalyserTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAggregates indicates an expected call of GetUserAggregates.
func (mr *MockAnalyserTransactionUpdateDaoMockRecorder) GetUserAggregates(ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAggregates", reflect.TypeOf((*MockAnalyserTransactionUpdateDao)(nil).GetUserAggregates), ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds)
}

// MockAnalyserAATransactionUpdateHandlerDao is a mock of AnalyserAATransactionUpdateHandlerDao interface.
type MockAnalyserAATransactionUpdateHandlerDao struct {
	ctrl     *gomock.Controller
	recorder *MockAnalyserAATransactionUpdateHandlerDaoMockRecorder
}

// MockAnalyserAATransactionUpdateHandlerDaoMockRecorder is the mock recorder for MockAnalyserAATransactionUpdateHandlerDao.
type MockAnalyserAATransactionUpdateHandlerDaoMockRecorder struct {
	mock *MockAnalyserAATransactionUpdateHandlerDao
}

// NewMockAnalyserAATransactionUpdateHandlerDao creates a new mock instance.
func NewMockAnalyserAATransactionUpdateHandlerDao(ctrl *gomock.Controller) *MockAnalyserAATransactionUpdateHandlerDao {
	mock := &MockAnalyserAATransactionUpdateHandlerDao{ctrl: ctrl}
	mock.recorder = &MockAnalyserAATransactionUpdateHandlerDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAnalyserAATransactionUpdateHandlerDao) EXPECT() *MockAnalyserAATransactionUpdateHandlerDaoMockRecorder {
	return m.recorder
}

// GetCategoryAggregates mocks base method.
func (m *MockAnalyserAATransactionUpdateHandlerDao) GetCategoryAggregates(ctx context.Context, actorId string, fromAccountIds []string, fromTime, toTime int64, updatedTxnIds []string, l0Ontologies []categorizer.L0, accountingEntry payment.AccountingEntryType) ([]*model.AnalyserAATransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCategoryAggregates", ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry)
	ret0, _ := ret[0].([]*model.AnalyserAATransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCategoryAggregates indicates an expected call of GetCategoryAggregates.
func (mr *MockAnalyserAATransactionUpdateHandlerDaoMockRecorder) GetCategoryAggregates(ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategoryAggregates", reflect.TypeOf((*MockAnalyserAATransactionUpdateHandlerDao)(nil).GetCategoryAggregates), ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry)
}

// GetTimeAggregates mocks base method.
func (m *MockAnalyserAATransactionUpdateHandlerDao) GetTimeAggregates(ctx context.Context, actorId string, fromAccountIds []string, fromTime, toTime int64, column txnaggregates.GroupByDimension, updatedTxnIds, ontologyIds []string) ([]*model.AnalyserAATransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTimeAggregates", ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds)
	ret0, _ := ret[0].([]*model.AnalyserAATransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTimeAggregates indicates an expected call of GetTimeAggregates.
func (mr *MockAnalyserAATransactionUpdateHandlerDaoMockRecorder) GetTimeAggregates(ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTimeAggregates", reflect.TypeOf((*MockAnalyserAATransactionUpdateHandlerDao)(nil).GetTimeAggregates), ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds)
}

// GetTransactionTimeStampAggregate mocks base method.
func (m *MockAnalyserAATransactionUpdateHandlerDao) GetTransactionTimeStampAggregate(ctx context.Context, actorId string, fromAccountIds []string, accountingEntryType payment.AccountingEntryType, excludedOntologyIds []string, aggregateType enums.AggregateType, updatedTxnIds []string) ([]*model.AnalyserAATransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionTimeStampAggregate", ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds)
	ret0, _ := ret[0].([]*model.AnalyserAATransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionTimeStampAggregate indicates an expected call of GetTransactionTimeStampAggregate.
func (mr *MockAnalyserAATransactionUpdateHandlerDaoMockRecorder) GetTransactionTimeStampAggregate(ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionTimeStampAggregate", reflect.TypeOf((*MockAnalyserAATransactionUpdateHandlerDao)(nil).GetTransactionTimeStampAggregate), ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds)
}

// GetUserAggregates mocks base method.
func (m *MockAnalyserAATransactionUpdateHandlerDao) GetUserAggregates(ctx context.Context, fromActorId string, fromAccountIds []string, fromTime, toTime int64, excludedTxnIds []string, l0Ontologies []categorizer.L0, ontologyIds []string, accountingEntry payment.AccountingEntryType) ([]*model.AnalyserAATransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAggregates", ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds, accountingEntry)
	ret0, _ := ret[0].([]*model.AnalyserAATransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAggregates indicates an expected call of GetUserAggregates.
func (mr *MockAnalyserAATransactionUpdateHandlerDaoMockRecorder) GetUserAggregates(ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds, accountingEntry interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAggregates", reflect.TypeOf((*MockAnalyserAATransactionUpdateHandlerDao)(nil).GetUserAggregates), ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds, accountingEntry)
}

// MockAnalyserAATransactionDao is a mock of AnalyserAATransactionDao interface.
type MockAnalyserAATransactionDao struct {
	ctrl     *gomock.Controller
	recorder *MockAnalyserAATransactionDaoMockRecorder
}

// MockAnalyserAATransactionDaoMockRecorder is the mock recorder for MockAnalyserAATransactionDao.
type MockAnalyserAATransactionDaoMockRecorder struct {
	mock *MockAnalyserAATransactionDao
}

// NewMockAnalyserAATransactionDao creates a new mock instance.
func NewMockAnalyserAATransactionDao(ctrl *gomock.Controller) *MockAnalyserAATransactionDao {
	mock := &MockAnalyserAATransactionDao{ctrl: ctrl}
	mock.recorder = &MockAnalyserAATransactionDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAnalyserAATransactionDao) EXPECT() *MockAnalyserAATransactionDaoMockRecorder {
	return m.recorder
}

// GetCategoryAggregates mocks base method.
func (m *MockAnalyserAATransactionDao) GetCategoryAggregates(ctx context.Context, actorId string, fromAccountIds []string, fromTime, toTime int64, updatedTxnIds []string, l0Ontologies []categorizer.L0, accountingEntry payment.AccountingEntryType) ([]*model.AnalyserAATransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCategoryAggregates", ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry)
	ret0, _ := ret[0].([]*model.AnalyserAATransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCategoryAggregates indicates an expected call of GetCategoryAggregates.
func (mr *MockAnalyserAATransactionDaoMockRecorder) GetCategoryAggregates(ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategoryAggregates", reflect.TypeOf((*MockAnalyserAATransactionDao)(nil).GetCategoryAggregates), ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry)
}

// GetMinUpdatedAt mocks base method.
func (m *MockAnalyserAATransactionDao) GetMinUpdatedAt(ctx context.Context) (time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinUpdatedAt", ctx)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMinUpdatedAt indicates an expected call of GetMinUpdatedAt.
func (mr *MockAnalyserAATransactionDaoMockRecorder) GetMinUpdatedAt(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinUpdatedAt", reflect.TypeOf((*MockAnalyserAATransactionDao)(nil).GetMinUpdatedAt), ctx)
}

// GetTimeAggregates mocks base method.
func (m *MockAnalyserAATransactionDao) GetTimeAggregates(ctx context.Context, actorId string, fromAccountIds []string, fromTime, toTime int64, column txnaggregates.GroupByDimension, updatedTxnIds, ontologyIds []string) ([]*model.AnalyserAATransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTimeAggregates", ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds)
	ret0, _ := ret[0].([]*model.AnalyserAATransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTimeAggregates indicates an expected call of GetTimeAggregates.
func (mr *MockAnalyserAATransactionDaoMockRecorder) GetTimeAggregates(ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTimeAggregates", reflect.TypeOf((*MockAnalyserAATransactionDao)(nil).GetTimeAggregates), ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds)
}

// GetTransaction mocks base method.
func (m *MockAnalyserAATransactionDao) GetTransaction(ctx context.Context, aaTxnId string) ([]map[string]interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransaction", ctx, aaTxnId)
	ret0, _ := ret[0].([]map[string]interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransaction indicates an expected call of GetTransaction.
func (mr *MockAnalyserAATransactionDaoMockRecorder) GetTransaction(ctx, aaTxnId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransaction", reflect.TypeOf((*MockAnalyserAATransactionDao)(nil).GetTransaction), ctx, aaTxnId)
}

// GetTransactionTimeStampAggregate mocks base method.
func (m *MockAnalyserAATransactionDao) GetTransactionTimeStampAggregate(ctx context.Context, actorId string, fromAccountIds []string, accountingEntryType payment.AccountingEntryType, excludedOntologyIds []string, aggregateType enums.AggregateType, updatedTxnIds []string) ([]*model.AnalyserAATransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionTimeStampAggregate", ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds)
	ret0, _ := ret[0].([]*model.AnalyserAATransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionTimeStampAggregate indicates an expected call of GetTransactionTimeStampAggregate.
func (mr *MockAnalyserAATransactionDaoMockRecorder) GetTransactionTimeStampAggregate(ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionTimeStampAggregate", reflect.TypeOf((*MockAnalyserAATransactionDao)(nil).GetTransactionTimeStampAggregate), ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds)
}

// GetUserAggregates mocks base method.
func (m *MockAnalyserAATransactionDao) GetUserAggregates(ctx context.Context, fromActorId string, fromAccountIds []string, fromTime, toTime int64, excludedTxnIds []string, l0Ontologies []categorizer.L0, ontologyIds []string, accountingEntry payment.AccountingEntryType) ([]*model.AnalyserAATransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAggregates", ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds, accountingEntry)
	ret0, _ := ret[0].([]*model.AnalyserAATransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAggregates indicates an expected call of GetUserAggregates.
func (mr *MockAnalyserAATransactionDaoMockRecorder) GetUserAggregates(ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds, accountingEntry interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAggregates", reflect.TypeOf((*MockAnalyserAATransactionDao)(nil).GetUserAggregates), ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds, accountingEntry)
}

// MockAnalyserAATransactionUpdateDao is a mock of AnalyserAATransactionUpdateDao interface.
type MockAnalyserAATransactionUpdateDao struct {
	ctrl     *gomock.Controller
	recorder *MockAnalyserAATransactionUpdateDaoMockRecorder
}

// MockAnalyserAATransactionUpdateDaoMockRecorder is the mock recorder for MockAnalyserAATransactionUpdateDao.
type MockAnalyserAATransactionUpdateDaoMockRecorder struct {
	mock *MockAnalyserAATransactionUpdateDao
}

// NewMockAnalyserAATransactionUpdateDao creates a new mock instance.
func NewMockAnalyserAATransactionUpdateDao(ctrl *gomock.Controller) *MockAnalyserAATransactionUpdateDao {
	mock := &MockAnalyserAATransactionUpdateDao{ctrl: ctrl}
	mock.recorder = &MockAnalyserAATransactionUpdateDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAnalyserAATransactionUpdateDao) EXPECT() *MockAnalyserAATransactionUpdateDaoMockRecorder {
	return m.recorder
}

// GetCategoryAggregates mocks base method.
func (m *MockAnalyserAATransactionUpdateDao) GetCategoryAggregates(ctx context.Context, actorId string, fromAccountIds []string, fromTime, toTime int64, updatedTxnIds []string, l0Ontologies []categorizer.L0, accountingEntry payment.AccountingEntryType) ([]*model.AnalyserAATransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCategoryAggregates", ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry)
	ret0, _ := ret[0].([]*model.AnalyserAATransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCategoryAggregates indicates an expected call of GetCategoryAggregates.
func (mr *MockAnalyserAATransactionUpdateDaoMockRecorder) GetCategoryAggregates(ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategoryAggregates", reflect.TypeOf((*MockAnalyserAATransactionUpdateDao)(nil).GetCategoryAggregates), ctx, actorId, fromAccountIds, fromTime, toTime, updatedTxnIds, l0Ontologies, accountingEntry)
}

// GetTimeAggregates mocks base method.
func (m *MockAnalyserAATransactionUpdateDao) GetTimeAggregates(ctx context.Context, actorId string, fromAccountIds []string, fromTime, toTime int64, column txnaggregates.GroupByDimension, updatedTxnIds, ontologyIds []string) ([]*model.AnalyserAATransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTimeAggregates", ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds)
	ret0, _ := ret[0].([]*model.AnalyserAATransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTimeAggregates indicates an expected call of GetTimeAggregates.
func (mr *MockAnalyserAATransactionUpdateDaoMockRecorder) GetTimeAggregates(ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTimeAggregates", reflect.TypeOf((*MockAnalyserAATransactionUpdateDao)(nil).GetTimeAggregates), ctx, actorId, fromAccountIds, fromTime, toTime, column, updatedTxnIds, ontologyIds)
}

// GetTransaction mocks base method.
func (m *MockAnalyserAATransactionUpdateDao) GetTransaction(ctx context.Context, aaTxnId string) ([]map[string]interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransaction", ctx, aaTxnId)
	ret0, _ := ret[0].([]map[string]interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransaction indicates an expected call of GetTransaction.
func (mr *MockAnalyserAATransactionUpdateDaoMockRecorder) GetTransaction(ctx, aaTxnId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransaction", reflect.TypeOf((*MockAnalyserAATransactionUpdateDao)(nil).GetTransaction), ctx, aaTxnId)
}

// GetTransactionTimeStampAggregate mocks base method.
func (m *MockAnalyserAATransactionUpdateDao) GetTransactionTimeStampAggregate(ctx context.Context, actorId string, fromAccountIds []string, accountingEntryType payment.AccountingEntryType, excludedOntologyIds []string, aggregateType enums.AggregateType, updatedTxnIds []string) ([]*model.AnalyserAATransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionTimeStampAggregate", ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds)
	ret0, _ := ret[0].([]*model.AnalyserAATransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionTimeStampAggregate indicates an expected call of GetTransactionTimeStampAggregate.
func (mr *MockAnalyserAATransactionUpdateDaoMockRecorder) GetTransactionTimeStampAggregate(ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionTimeStampAggregate", reflect.TypeOf((*MockAnalyserAATransactionUpdateDao)(nil).GetTransactionTimeStampAggregate), ctx, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, updatedTxnIds)
}

// GetUserAggregates mocks base method.
func (m *MockAnalyserAATransactionUpdateDao) GetUserAggregates(ctx context.Context, fromActorId string, fromAccountIds []string, fromTime, toTime int64, excludedTxnIds []string, l0Ontologies []categorizer.L0, ontologyIds []string, accountingEntry payment.AccountingEntryType) ([]*model.AnalyserAATransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAggregates", ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds, accountingEntry)
	ret0, _ := ret[0].([]*model.AnalyserAATransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAggregates indicates an expected call of GetUserAggregates.
func (mr *MockAnalyserAATransactionUpdateDaoMockRecorder) GetUserAggregates(ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds, accountingEntry interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAggregates", reflect.TypeOf((*MockAnalyserAATransactionUpdateDao)(nil).GetUserAggregates), ctx, fromActorId, fromAccountIds, fromTime, toTime, excludedTxnIds, l0Ontologies, ontologyIds, accountingEntry)
}
