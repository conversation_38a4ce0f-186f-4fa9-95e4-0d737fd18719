package consumer

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"

	"github.com/epifi/gamma/analyser/config"
	"github.com/epifi/gamma/analyser/config/genconf"
	"github.com/epifi/gamma/api/order/payment"
	pinotAnalyserPb "github.com/epifi/gamma/api/pinot/analyser"
	"github.com/epifi/be-common/pkg/datetime"
)

func TestService_isStatusProcessable(t *testing.T) {
	tests := []struct {
		name   string
		status payment.TransactionStatus
		want   bool
	}{
		{
			name:   "for SUCCESS status",
			status: payment.TransactionStatus_SUCCESS,
			want:   true,
		},
		{
			name:   "for FAILED status",
			status: payment.TransactionStatus_FAILED,
			want:   true,
		},
		{
			name:   "for REVERSED status",
			status: payment.TransactionStatus_REVERSED,
			want:   true,
		},
		{
			name:   "for IN_PROGRESS status",
			status: payment.TransactionStatus_IN_PROGRESS,
			want:   false,
		},
		{
			name:   "for EXPIRED status",
			status: payment.TransactionStatus_EXPIRED,
			want:   false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			s := &Service{}
			if got := s.isStatusProcessable(tt.status); got != tt.want {
				t.Errorf("isStatusProcessable() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_publishTransactionUpdate(t *testing.T) {
	defaultConfig := &config.Config{
		TransactionsPinotPublisherConf: &config.PinotRealtimeUpdates{
			DualPublishWindow: time.Hour * 24 * 30,
		},
	}
	tests := []struct {
		name    string
		config  *config.Config
		txn     *pinotAnalyserPb.Transaction
		before  func(f *fields)
		wantErr bool
	}{
		{
			name:   "successfully publish transaction within dual publish window",
			config: defaultConfig,
			txn: &pinotAnalyserPb.Transaction{
				TransactionId:            "txn-id",
				TransactionCreatedAtUnix: time.Date(2022, time.December, 2, 0, 0, 0, 0, datetime.IST).UnixMilli(),
			},
			before: func(f *fields) {
				ctl := gomock.NewController(t)
				defer ctl.Finish()
				now := time.Date(2022, time.December, 10, 0, 0, 0, 0, datetime.IST)
				f.mockClock.EXPECT().Now().Return(now).MaxTimes(2)
				publishedMessage := &pinotAnalyserPb.Transaction{
					TransactionId:            "txn-id",
					TransactionCreatedAtUnix: time.Date(2022, time.December, 2, 0, 0, 0, 0, datetime.IST).UnixMilli(),
					ViewUpdatedAtUnix:        now.UnixMilli(),
				}
				f.mockTransactionProducer.EXPECT().PublishMessage(gomock.Any(), publishedMessage).Return("", nil)
				f.mockTransactionUpdateProducer.EXPECT().PublishMessage(gomock.Any(), publishedMessage).Return("", nil)
			},
		},
		{
			name:   "successfully publish transaction outside of dual publish window",
			config: defaultConfig,
			txn: &pinotAnalyserPb.Transaction{
				TransactionId:            "txn-id",
				TransactionCreatedAtUnix: time.Date(2022, time.November, 2, 0, 0, 0, 0, datetime.IST).UnixMilli(),
			},
			before: func(f *fields) {
				ctl := gomock.NewController(t)
				defer ctl.Finish()
				now := time.Date(2022, time.December, 10, 0, 0, 0, 0, datetime.IST)
				f.mockClock.EXPECT().Now().Return(now).MaxTimes(2)
				publishedMessage := &pinotAnalyserPb.Transaction{
					TransactionId:            "txn-id",
					TransactionCreatedAtUnix: time.Date(2022, time.November, 2, 0, 0, 0, 0, datetime.IST).UnixMilli(),
					ViewUpdatedAtUnix:        now.UnixMilli(),
				}
				f.mockTransactionUpdateProducer.EXPECT().PublishMessage(gomock.Any(), publishedMessage).Return("", nil)
			},
		},
		{
			name:   "failure to publish to transactions stream should return error",
			config: defaultConfig,
			txn: &pinotAnalyserPb.Transaction{
				TransactionId:            "txn-id",
				TransactionCreatedAtUnix: time.Date(2022, time.December, 2, 0, 0, 0, 0, datetime.IST).UnixMilli(),
			},
			before: func(f *fields) {
				ctl := gomock.NewController(t)
				defer ctl.Finish()
				now := time.Date(2022, time.December, 10, 0, 0, 0, 0, datetime.IST)
				f.mockClock.EXPECT().Now().Return(now).MaxTimes(2)
				publishedMessage := &pinotAnalyserPb.Transaction{
					TransactionId:            "txn-id",
					TransactionCreatedAtUnix: time.Date(2022, time.December, 2, 0, 0, 0, 0, datetime.IST).UnixMilli(),
					ViewUpdatedAtUnix:        now.UnixMilli(),
				}
				f.mockTransactionProducer.EXPECT().PublishMessage(gomock.Any(), publishedMessage).Return("", fmt.Errorf("some err"))
				f.mockTransactionUpdateProducer.EXPECT().PublishMessage(gomock.Any(), publishedMessage).Return("", nil).MaxTimes(1)
			},
			wantErr: true,
		},
		{
			name:   "failure to publish to transactions stream should return error",
			config: defaultConfig,
			txn: &pinotAnalyserPb.Transaction{
				TransactionId:            "txn-id",
				TransactionCreatedAtUnix: time.Date(2022, time.December, 2, 0, 0, 0, 0, datetime.IST).UnixMilli(),
			},
			before: func(f *fields) {
				ctl := gomock.NewController(t)
				defer ctl.Finish()
				now := time.Date(2022, time.December, 10, 0, 0, 0, 0, datetime.IST)
				f.mockClock.EXPECT().Now().Return(now).MaxTimes(2)
				publishedMessage := &pinotAnalyserPb.Transaction{
					TransactionId:            "txn-id",
					TransactionCreatedAtUnix: time.Date(2022, time.December, 2, 0, 0, 0, 0, datetime.IST).UnixMilli(),
					ViewUpdatedAtUnix:        now.UnixMilli(),
				}
				f.mockTransactionProducer.EXPECT().PublishMessage(gomock.Any(), publishedMessage).Return("", nil).MaxTimes(1)
				f.mockTransactionUpdateProducer.EXPECT().PublishMessage(gomock.Any(), publishedMessage).Return("", fmt.Errorf("some err"))
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctl := gomock.NewController(t)
			defer ctl.Finish()
			dynconf, _ := genconf.NewConfig()
			err := dynconf.Set(tt.config, false, nil)
			if err != nil {
				t.Errorf("failed to set static config in dynamic config: %v", err)
			}
			f := initMocks(ctl)
			tt.before(f)
			s := &Service{dynconf: dynconf, clock: f.mockClock, transactionsProducer: f.mockTransactionProducer, transactionsUpdateProducer: f.mockTransactionUpdateProducer}
			if err := s.publishTransactionUpdate(context.Background(), tt.txn); (err != nil) != tt.wantErr {
				t.Errorf("publishTransactionUpdate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
