package consumer

import (
	"context"

	"go.uber.org/zap"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/gamma/api/analyser/investment/consumer"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
)

// RefreshMFPortfolioAnalytics refreshes the MF portfolio history incrementally for the given list of actors
func (s *Service) RefreshMFPortfolioAnalytics(ctx context.Context, req *consumer.RefreshMFPortfolioAnalyticsRequest) (*consumer.RefreshMFPortfolioAnalyticsResponse, error) {
	var res = &consumer.RefreshMFPortfolioAnalyticsResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{},
	}
	err := s.investmentAnalyserEventProcessor.RefreshMfPortfolioAnalytics(ctx, req)
	if err != nil {
		logger.Error(ctx, "error in refreshing mf portfolio analytics", zap.Error(err))
		res.ResponseHeader.Status = queue.GetStatusFrom(err)
		return res, nil
	}
	logger.Debug(ctx, "successfully refreshed mf portfolio analytics for given actors", zap.Error(err), zap.String(logger.PAYLOAD, req.String()))
	res.ResponseHeader = successHeader
	return res, nil
}
