package model

import (
	"time"

	"gorm.io/gorm"

	inAppReferralPb "github.com/epifi/gamma/api/inappreferral"
	inAppReferralEnumPb "github.com/epifi/gamma/api/inappreferral/enums"
	"github.com/epifi/be-common/pkg/nulltypes"
)

// struct representing the FiniteCode data model.
type FiniteCode struct {
	Id string `gorm:"primary_key"`

	// Unique code used as finite code by <PERSON>ferrer to refer someone
	Code string

	// Actor who owns the finite code, basically the referrer actor
	// This will be null if channel type is influencer or customer service
	ActorId nulltypes.NullString

	// Channel to which the finite code belongs to, could be in-app referral, influencer, etc.
	Channel inAppReferralEnumPb.FiniteCodeChannel

	// Type of the finite code, can be regular or golden ticket.
	// Golden ticket will be a special type of finite code using which referee can onboard without employment check
	Type inAppReferralEnumPb.FiniteCodeType

	// Claim limit for the finite code. Each finite code can only be availed this number of time to onboard on the app.
	ClaimLimit int32

	// Number of times the finite code has been claimed. This value always has be to less than equal to the claim limit.
	ClaimedCount int32

	// Specifies if the finite code is active or not. If not, the finite code cannot be used to onboard.
	// By default during finite code creation this field will be true
	// When finite code is exhausted, value will be NULL
	IsActive nulltypes.NullBool

	// Specifies if employment check needs to be done for a user using the finite code to onboard on the app.
	// For golden ticket finite code this will be true
	SkipEmploymentCheckPrivilege bool

	// info regarding the unique referral link which can be shared across for inviting folks to Fi
	ReferralInviteLinkInfo *inAppReferralPb.ReferralInviteLinkInfo

	// Phone number referral code used by Referrer to refer someone to Fi
	PhoneNumberReferralCode nulltypes.NullString

	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt
}
