//go:generate mockgen -source=dao.go -destination=./mocks/mock_dao.go -package=mocks
//go:generate dao_metrics_gen .
package dao

import (
	"context"
	"time"

	inAppReferralEnumPb "github.com/epifi/gamma/api/inappreferral/enums"
	notificationPb "github.com/epifi/gamma/api/inappreferral/notification"
	"github.com/epifi/gamma/inappreferral/dao/model"

	"github.com/google/wire"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/pagination"

	inAppReferralPb "github.com/epifi/gamma/api/inappreferral"
)

var WireSet = wire.NewSet(
	NewFiniteCodeDaoCrdb, wire.Bind(new(FiniteCodeDao), new(*FiniteCodeDaoCrdb)),
	NewFiniteCodeClaimsDaoCrdb, wire.Bind(new(FiniteCodeClaimDao), new(*FiniteCodeClaimDaoCrdb)),
	NewInAppReferralUnlockDaoCrdb, wire.Bind(new(InAppReferralUnlockDao), new(*InAppReferralUnlockDaoCrdb)),
	NewReferralsSegmentedComponentDaoCRDB, wire.Bind(new(IReferralsSegmentedComponentDao), new(*ReferralsSegmentedComponentDaoCRDB)),
	NewNotificationConfigInfoDaoCRDB, wire.Bind(new(INotificationConfigInfoDao), new(*NotificationConfigInfoCrdb)),
)

// FiniteCodeDao defines all the DB operations that can be done on FiniteCode entity
type FiniteCodeDao interface {
	// Create creates a new record entry in the FiniteCode table.
	// In case a record with the finite code exists, it returns that record
	Create(ctx context.Context, finiteCode *inAppReferralPb.FiniteCode) (*inAppReferralPb.FiniteCode, error)

	// IncrementClaimedCount increments the claimed_count by 1 for a finite code.
	// This method is to be used whenever a finite code is claimed by a referee.
	// If the incremented claimed_count is greater than the claim_limit, the method returns an error.
	IncrementClaimedCount(ctx context.Context, finiteCode *inAppReferralPb.FiniteCode) (*inAppReferralPb.FiniteCode, error)

	// GetFiniteCodesForActor returns a list of finite codes for an actor ID.
	GetFiniteCodesForActor(ctx context.Context, actorId string) ([]*inAppReferralPb.FiniteCode, error)

	// GetFiniteCodesOfChannelAndTypeForActor returns a list of finite codes for an actor ID for a list of channels and type
	// channels and type are optional params.
	GetFiniteCodesOfChannelAndTypeForActor(ctx context.Context, actorId string, channels []inAppReferralEnumPb.FiniteCodeChannel,
		types []inAppReferralEnumPb.FiniteCodeType) ([]*inAppReferralPb.FiniteCode, error)

	// GetByFiniteCode fetches a finite code object by it's unique code
	GetByFiniteCode(ctx context.Context, finiteCode string) (*inAppReferralPb.FiniteCode, error)

	// GetById fetches a finite code object by id
	GetById(ctx context.Context, id string) (*inAppReferralPb.FiniteCode, error)

	// GetFiniteCodesByIds gets a list of finite codes for a list of finite code Ids
	// Note: if for a finite code id, no corresponding record is found, the method will just skip fetching for that id
	GetFiniteCodesByIds(ctx context.Context, finiteCodeIds []string) ([]*inAppReferralPb.FiniteCode, error)

	// DisableFiniteCode disables a finite code by setting the is_active flag to NULL
	DisableFiniteCode(ctx context.Context, finiteCode *inAppReferralPb.FiniteCode) error

	// StoreReferralInviteLinkInfoForActor stores/replaces the referral invite link info for the actor
	StoreReferralInviteLinkInfoForActor(ctx context.Context, actorId string, referralInviteLinkInfo *inAppReferralPb.ReferralInviteLinkInfo) error

	// DeprecateFiniteCode soft deletes a finite code by setting the deleted_at field.
	// No access is granted to a user who tries to on-board via this deprecated finite code.
	// In case there's an issue in soft deleting the finite code, the method returns an error, else NULL.
	// DeprecateFiniteCode(ctx context.Context, finiteCode *inAppReferralPb.FiniteCode) error

	// GetByPhoneNumber fetches a finite code entry by the associated phone number
	GetByPhoneNumber(ctx context.Context, phoneNumber string) (*inAppReferralPb.FiniteCode, error)

	// UpdatePhoneNumberReferralCodeById updates the phone_number_referral_code column in the finite-code entry for the particular id.
	UpdatePhoneNumberReferralCodeById(ctx context.Context, id string, phoneNumber string) error

	// MarkPhoneNumberAsNullById updates the phone_number_referral_code column in the finite-code entry for the particular id to null
	MarkPhoneNumberAsNullById(ctx context.Context, id string) error
}

// FiniteCodeClaimsDao defines all the DB operations that can be done on FiniteCode entity
type FiniteCodeClaimDao interface {
	// Create creates a new record entry in the FiniteCodeClaims table.
	// In case a record with the finite code exists, it returns that record
	Create(ctx context.Context, finiteCodeClaims *inAppReferralPb.FiniteCodeClaim) (*inAppReferralPb.FiniteCodeClaim, error)

	// GetRefereesByReferrerAndFiniteCode gets a list of all referees by referrer Id and finite code Id.
	// If finiteCodeId param is present, the list of referees fetched are filtered out for that finite code id.
	// The number of referees returned is managed by the limit param.
	// NOTE: the dao method fetches one record more than the limit to figure out nextPageToken at RPC level.
	GetRefereesByReferrerAndFiniteCode(ctx context.Context, referrerActorId, finiteCodeId string,
		pageSize int32, pageToken *pagination.PageToken) ([]*inAppReferralPb.FiniteCodeClaim, *rpc.PageContextResponse, error)

	// GetRefereesCountByReferrerAndFiniteCode gets count of all referees by referrer Id and finite code Id.
	// If finiteCodeId param is present, the count of referees fetched are filtered out for that finite code id.
	GetRefereesCountByReferrerAndFiniteCode(ctx context.Context, referrerActorId, finiteCodeId string) (int64, error)

	// GetByRefereeActorId gets a finite code for a referee actor ID.
	// The method should return max one record as only one record is possible per referee actor Id.
	// If no record is found, the method returns RecordNotFound error.
	GetByRefereeActorId(ctx context.Context, refereeActorId string) (*inAppReferralPb.FiniteCodeClaim, error)

	// AcquireLockAndGetByRefereeActorId gets a finite code for a referee actor ID by acquiring lock over the row.
	// The method should return max one record as only one record is possible per referee actor Id.
	// If no record is found, the method returns RecordNotFound error.
	AcquireLockAndGetByRefereeActorId(ctx context.Context, refereeActorId string) (*inAppReferralPb.FiniteCodeClaim, error)

	// UpdateCommsInfo updates the comms_info field for a finite code claim.
	// Note: As comms info is a map holding comms information for referrals for different scenarios, the map will keep on
	// growing with even new comms event. This dao method doesn't handle the appending logic, and that is to be handled at
	// service level. The dao method will just update the comms info to the one passed in params.
	UpdateCommsInfo(ctx context.Context, finiteCodeClaim *inAppReferralPb.FiniteCodeClaim) error

	// UpdateRefereeOnboardingStatus updates the refereeOnboardingStatus of a given refereeActorId
	UpdateRefereeOnboardingStatus(
		ctx context.Context,
		refereeActorId string,
		updatedRefereeOnboardingStatus, currentRefereeOnboardingStatus inAppReferralEnumPb.RefereeOnbStage,
	) error
}

type InAppReferralUnlockDao interface {
	Create(ctx context.Context, inAppReferralUnlock *inAppReferralPb.InAppReferralUnlock) (*inAppReferralPb.InAppReferralUnlock, error)

	// UpdateByActorIdAndFiniteCodeType updates an inAppReferralUnlock record by actorId and finiteCodeType.
	// The method can be used to update the avg eod balance, unlocked_at or the is_unlocked fields.
	UpdateByActorIdAndFiniteCodeType(ctx context.Context, inAppReferralUnlock *inAppReferralPb.InAppReferralUnlock) error

	// GetByActorId fetches inAppReferralUnlock records for an actorId.
	// return RecordNotFound error if no record present for the actorId.
	GetByActorId(ctx context.Context, actorId string) ([]*inAppReferralPb.InAppReferralUnlock, error)

	// GetByActorIdAndFiniteCodeType gets an inAppReferralUnlock record for an actorId and finiteCodeType. As actorId+finiteCodeType combination is unique per record, the method returns
	// only one record or RecordNotFound error
	GetByActorIdAndFiniteCodeType(ctx context.Context, actorId string, finiteCodeType inAppReferralEnumPb.FiniteCodeType) (*inAppReferralPb.InAppReferralUnlock, error)

	// UpdateCommsInfo updates the comms_info field for a InAppReferralUnlock.
	// Note: As comms info is a map holding comms information for referrals for different scenarios, the map will keep on
	// growing with even new comms event. This dao method doesn't handle the appending logic, and that is to be handled at
	// service level. The dao method will just update the comms info to the one passed in params.
	UpdateCommsInfo(ctx context.Context, inAppReferralUnlock *inAppReferralPb.InAppReferralUnlock) error

	// UnlockForActorIdAndFiniteCodeType unlocks the in-app-referral for the actor by setting the minimum required conditions
	UnlockForActorIdAndFiniteCodeType(ctx context.Context, actorId string, finiteCodeType inAppReferralEnumPb.FiniteCodeType) (*inAppReferralPb.InAppReferralUnlock, error)

	// AcquireLockAndGetByActorIdAndFiniteCodeType acquires a row level db lock on an in-app-referral-unlock entry using an ongoing transaction
	AcquireLockAndGetByActorIdAndFiniteCodeType(ctx context.Context, actorId string, finiteCodeType inAppReferralEnumPb.FiniteCodeType) (*inAppReferralPb.InAppReferralUnlock, error)
}

type IReferralsSegmentedComponentDao interface {
	Create(ctx context.Context, referralsSegmentedComponent *inAppReferralPb.ReferralsSegmentedComponent) (*inAppReferralPb.ReferralsSegmentedComponent, error)
	Update(ctx context.Context, id string, componentDetails *inAppReferralPb.ComponentDetails, activeSince, activeTill time.Time) (*inAppReferralPb.ReferralsSegmentedComponent, error)
	FetchReferralsSegmentedComponents(ctx context.Context, filters *model.FetchReferralsSegmentedComponentsFilters, fieldMask []model.ReferralsSegmentedComponentFieldMask) ([]*inAppReferralPb.ReferralsSegmentedComponent, error)
	SoftDelete(ctx context.Context, id string) error
}

type INotificationConfigInfoDao interface {
	// creates a new notification config
	Create(ctx context.Context, notificationConfigInfo *notificationPb.NotificationConfigInfo) (*notificationPb.NotificationConfigInfo, error)
	UpdateById(ctx context.Context, id string, notificationInfo *notificationPb.NotificationConfigInfo, updateMask []inAppReferralEnumPb.NotificationConfigInfoFieldMask) error
	UpdateContent(ctx context.Context, id string, contentInfo *notificationPb.ContentInfo) (*notificationPb.NotificationConfigInfo, error)
	UpdateStatus(ctx context.Context, id string, status inAppReferralEnumPb.ConfigStatus) (*notificationPb.NotificationConfigInfo, error)
	FetchConfigs(ctx context.Context, filters *model.NotificationConfigInfoFilters) ([]*notificationPb.NotificationConfigInfo, error)
	SoftDelete(ctx context.Context, id string) error
}
