package consumer_test

import (
	"os"
	"testing"

	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	"github.com/epifi/gamma/accounts/config"
	accountDaoMocks "github.com/epifi/gamma/accounts/dao/mocks"
	aisavingsMocks "github.com/epifi/gamma/accounts/internal/savings/mocks"
	userProcessorMocks "github.com/epifi/gamma/accounts/internal/user/mocks"
	"github.com/epifi/gamma/accounts/statement/consumer"
	"github.com/epifi/gamma/accounts/test"
	commsMocks "github.com/epifi/gamma/api/comms/mocks"
	docsMocks "github.com/epifi/gamma/api/docs/mocks"
	userMock "github.com/epifi/gamma/api/user/mocks"

	"github.com/golang/mock/gomock"
	gormv2 "gorm.io/gorm"
)

var (
	db           *gormv2.DB
	accountsConf *config.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {

	var teardown func()

	accountsConf, _, db, teardown = test.InitTestServer()

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type mockService struct {
	mockDocsClient                   *docsMocks.MockDocsClient
	MockStatementDataProcessorClient *aisavingsMocks.MockIStatementDataProcessor
	mockUserProcessorClient          *userProcessorMocks.MockUserProcessor
	mockUsersClient                  *userMock.MockUsersClient
	mockCommsClient                  *commsMocks.MockCommsClient
	mockAccountsDao                  *accountDaoMocks.MockAccountStatementRequestDao
	mockCelestialSvc                 *celestialMocks.MockCelestialClient
}

func getAccountStatementConsumerService(t *testing.T) (*consumer.StatementConsumerService, *mockService, func()) {

	ctr := gomock.NewController(t)

	mockDocsClient := docsMocks.NewMockDocsClient(ctr)
	MockStatementDataProcessorClient := aisavingsMocks.NewMockIStatementDataProcessor(ctr)
	mockUserProcessorClient := userProcessorMocks.NewMockUserProcessor(ctr)
	mockUsersClient := userMock.NewMockUsersClient(ctr)
	mockCommsClient := commsMocks.NewMockCommsClient(ctr)
	mockAccountsDao := accountDaoMocks.NewMockAccountStatementRequestDao(ctr)
	mockCelestialSvc := celestialMocks.NewMockCelestialClient(ctr)

	mockServices := &mockService{
		mockDocsClient:                   mockDocsClient,
		MockStatementDataProcessorClient: MockStatementDataProcessorClient,
		mockUserProcessorClient:          mockUserProcessorClient,
		mockUsersClient:                  mockUsersClient,
		mockCommsClient:                  mockCommsClient,
		mockAccountsDao:                  mockAccountsDao,
		mockCelestialSvc:                 mockCelestialSvc,
	}

	svc := consumer.NewConsumerService(mockCommsClient, nil, mockDocsClient, mockUsersClient, accountsConf, mockAccountsDao,
		nil, nil, nil, nil, mockUserProcessorClient, MockStatementDataProcessorClient, nil, mockCelestialSvc)

	return svc, mockServices, func() {
		defer ctr.Finish()
	}
}
