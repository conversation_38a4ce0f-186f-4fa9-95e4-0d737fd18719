CREATE TABLE IF NOT EXISTS networth_refresh_session
(
    id                      VARCHAR                  NOT NULL PRIMARY KEY,
    actor_id                VARCHAR                  NOT NULL,
    asset_refresh_details   JSONB                    NULL,
    created_at              TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at              TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    deleted_at              TIMESTAMP WITH TIME ZONE
);

COMMENT ON TABLE networth_refresh_session IS 'table for storing all networth refresh sessions initiated by the user';
COMMENT ON COLUMN networth_refresh_session.actor_id IS 'actor_id to whom the assets belongs';
COMMENT ON COLUMN networth_refresh_session.asset_refresh_details IS 'ordered list of assets to be refreshed with their refresh info';
