CREATE TABLE IF NOT EXISTS comms_retry_logs (
	id 				UUID DEFAULT public.uuid_generate_v4() NOT NULL,
	comms_msg_id 	VARCHAR NOT NULL,
	vendor 			VARCHAR NOT NULL,
	vendor_account  VARCHAR NULL,
	vendor_msg_id 	VARCHAR NOT NULL,
	status 			VARCHAR NOT NULL,
	created_at 		TIMESTAMPTZ NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at 	    TIMESTAMPTZ NULL DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY(id)
);

CREATE INDEX IF NOT EXISTS comms_retry_logs_updated_at_key ON comms_retry_logs (updated_at ASC);
CREATE INDEX IF NOT EXISTS comms_retry_logs_comms_msg_id_key ON comms_retry_logs (comms_msg_id ASC);
CREATE INDEX IF NOT EXISTS comms_retry_logs_vendor_msg_id_vendor_key ON comms_retry_logs (vendor_msg_id ASC, vendor ASC);

comment on table comms_retry_logs is 'table to track history for vendor msg id and status in cases where we are retrying msg on comms service end';
comment on column comms_retry_logs.vendor_account is 'vendor account: EPIFI, FEDERAL, etc';
comment on column comms_retry_logs.vendor is '{"proto_type":"vendorgateway.Vendor", "comment":"specifies which vendor was used for comms: ACL, KALEYRA, etc"';
comment on column comms_retry_logs.status is '{"proto_type":"comms.MessageState", "comment":"specifies status of the message: delivered, failed, etc"';
