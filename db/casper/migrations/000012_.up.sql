create table if not exists offline_redemptions
(
    id uuid default uuid_generate_v4() not null constraint offline_redemptions_id_pk primary key,
    -- request refId
    ref_id varchar not null,
    actor_id varchar not null,
    vendor_name varchar not null,
    offline_redemption_metadata jsonb,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    constraint offline_redemption_unique_ref_id unique (ref_id)
);
