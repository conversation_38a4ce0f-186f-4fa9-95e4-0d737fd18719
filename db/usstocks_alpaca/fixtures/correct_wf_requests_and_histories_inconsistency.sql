-- marking release sell lock SUCCE<PERSON> for swift completed cases```
update workflow_requests set stage = 'RELEASE_SELL_LOCK', status = 'SUCCESSFUL', updated_at = now() where id in (
    select wf_req_id
    from orders
    where id in ('USSO2302183fWzK6Qbwh', 'USSO2302132C6Sm3uvzU','USSO230110geK4GKxfFz', 'USSO221229AGRVR0HVTaSpiakldJUQ4A==')
);

insert into workflow_histories (id, wf_req_id, stage, status, completed_at) values
    (gen_random_uuid(), 'WFR230218aCyRDPGJSmuvAAq/1WPZXA==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
    (gen_random_uuid(), 'WFR230213/k/mX1EXQACA3PQWSeIV8Q==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
    (gen_random_uuid(), 'WFR230110jWPXul6ATKWKIEMIVrtadA==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
    (gen_random_uuid(), 'WFR221229Wm3fJxwERL2Wwt0zFV5ZXQ==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now());


update workflow_requests set stage = 'REFUND_PAYMENT', status = 'SUCCESSFUL', updated_at = now() where id in (
    select wf_req_id
    from orders
    where id in ('USSO221228RhkRSK+cRYKrQMvoBqRAEw==', 'USSO221228vZhQfkKUQ1qjVrRffW+wkA==', 'USSO2212273Y4r7I6zTDmWYuWw6KAKUw==', 'USSO2212276v+Abb3PRZ+bamGlvhRcwQ==')
);
