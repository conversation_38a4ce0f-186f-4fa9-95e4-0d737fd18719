-- Resetting details of an incomplete e-sign transaction
-- when it fails repeatedly from e-sign vendor's end. The reason for failure is currently unknown
-- and vendor has been asked about it.
UPDATE onboarding_details
SET personal_details = personal_details::jsonb - 'advisoryAgreementDetails'
WHERE actor_id in ('AC220227mqSjZ8gDTraVq2yOutdXvw==',
				   'AC210615+Oo6OoUiRXCBOvhsYV9icA==',
					'AC221210bJh1SNq2Sa26dyuMsgcwlQ==');



-- updating WOB step (pan step verification -- PAN Upload atte,pts exhausted) after manual review

update onboarding_details
set personal_details = personal_details || jsonb_build_object('panDetails', personal_details->'panDetails' || jsonb_build_object('s3Paths', json_build_array('converted_doc_proof_image/AC2107066PMoBBSOSoeWRvvEY3NyyQ==/e6cfd045-a6f1-4926-87ed-83d3efc28f11/DOCUMENT_PROOF_TYPE_PAN/0.JPEG'))),
	customer_provided_data=customer_provided_data||jsonb_build_object('pan',jsonb_build_object('proofType', 'DOCUMENT_PROOF_TYPE_PAN', 'id', '**********', 's3Paths', json_build_array('converted_doc_proof_image/AC2107066PMoBBSOSoeWRvvEY3NyyQ==/e6cfd045-a6f1-4926-87ed-83d3efc28f11/DOCUMENT_PROOF_TYPE_PAN/0.JPEG')))
where id = '39baef65-0e61-4c29-b572-4b56687f90f3';
