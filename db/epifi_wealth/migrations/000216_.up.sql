CREATE TABLE IF NOT EXISTS mf_external_nft_requests (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	external_id STRING NOT NULL,
	client_reference_id STRING NOT NULL,
	type string NOT NULL,
	status STRING NOT NULL,
	sub_status STRING NOT NULL,
	vendor STRING NOT NULL,
	provenance STRING NOT NULL,
	details JSONB NOT NULL DEFAULT '{}':::JSONB,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_external_nft_requests_id_pkey PRIMARY KEY (id ASC),
	INDEX mf_external_nft_requests_updated_at_idx (updated_at DESC),
	INDEX mf_external_nft_requests_actor_id_created_at_idx (actor_id DESC, created_at DESC),
	UNIQUE INDEX mf_external_nft_requests_external_id_key (external_id DESC),
	UNIQUE INDEX mf_external_nft_requests_client_reference_id_key (client_reference_id DESC)
);
