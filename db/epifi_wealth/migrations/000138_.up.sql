CREATE TABLE IF NOT EXISTS do_once_tasks (
  task_name STRING NOT NULL,
  deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
  PRIMARY KEY (task_name, deleted_at_unix),
  INDEX do_once_tasks_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE do_once_tasks IS 'to identify tasks by their unique names that should be done exactly once';
COMMENT ON COLUMN do_once_tasks.task_name IS 'unique identifier for a task';
COMMENT ON COLUMN do_once_tasks.deleted_at_unix IS 'non-zero for soft-deleted tasks so that another task with same name can be created again once previous task is soft-deleted';
