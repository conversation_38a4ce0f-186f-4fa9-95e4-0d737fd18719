CREATE TABLE IF NOT EXISTS mutual_fund_external_holdings_summaries (
	id					  UUID         NOT NULL DEFAULT gen_random_uuid(),
	actor_id			  STRING       NOT NULL,
	email                 STRING,
	amc_code              STRING       NOT NULL,
	amc_name              STRING       NOT NULL,
	folio                 STRING       NOT NULL,
	scheme_code           STRING       NOT NULL,
	scheme_name           STRING       NOT NULL,
	kyc_status            STRING       NOT NULL,
	broker_code           STRING       NOT NULL,
	broker_name           STRING       NOT NULL,
	rta_code              STRING       NOT NULL,
	opening_balance       JSONB        NOT NULL,
	closing_balance       JSONB        NOT NULL,
	market_value          JSONB        NOT NULL,
	nav                   JSONB        NOT NULL,
	is_demat              BOOLEAN      NOT NULL,
	asset_type            STRING       NOT NULL,
	isin                  STRING       NOT NULL,

	last_transaction_date TIMESTAMPTZ  NOT NULL,
	last_nav_date         TIMESTAMPTZ  NOT NULL,

	created_at			  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
	updated_at			  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
	deleted_at			  TIMESTAMPTZ,

	PRIMARY KEY (actor_id ASC, id ASC),

	INDEX mutual_fund_external_holdings_summaries_updated_at_idx (updated_at DESC),
    INDEX mutual_fund_external_holdings_summaries_actor_id_last_transaction_date_idx (last_transaction_date DESC) where deleted_at is not null
	);

COMMENT ON TABLE public.mutual_fund_external_holdings_summaries IS 'table to store mutual fund holdings summaries';
COMMENT ON COLUMN public.mutual_fund_external_holdings_summaries.amc_name IS '{"proto_type":"investment.mutualfund.AMC", "comment":"mutual fund amc"}';
COMMENT ON COLUMN public.mutual_fund_external_holdings_summaries.opening_balance IS '{"struct_type":"pkg.money.Money", "comment":"opening balance"}';
COMMENT ON COLUMN public.mutual_fund_external_holdings_summaries.closing_balance IS '{"struct_type":"pkg.money.Money", "comment":"closing balance"}';
COMMENT ON COLUMN public.mutual_fund_external_holdings_summaries.market_value IS '{"struct_type":"pkg.money.Money", "comment":"market value of the scheme units"}';
COMMENT ON COLUMN public.mutual_fund_external_holdings_summaries.nav IS '{"struct_type":"pkg.money.Money", "comment":"nav of scheme as per last_nav_date"}';
