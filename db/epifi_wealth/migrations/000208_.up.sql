DROP TABLE IF EXISTS mf_folio_nominee_update_status;

CREATE TABLE IF NOT EXISTS mf_nominee_info (
	id              		string 			NOT NULL,
	actor_id                string      	NOT NULL,
	nominee_id              string      	NOT NULL,
	folio_id				string      	NOT NULL,
	vendor 					string 			NOT NULL,
	vendor_request_id 		string 			NOT NULL,
	update_status  			string      	NOT NULL,
	failure_reason  		string 			NOT NULL,
	created_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
	updated_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
	deleted_at              TIMESTAMPTZ     NULL,

	PRIMARY KEY (id ASC),
	UNIQUE (actor_id, nominee_id, folio_id),
	UNIQUE (vendor_request_id),
	INDEX updated_at(updated_at DESC),
	CONSTRAINT fk_folio_ledger_id FOREIGN KEY (folio_id) REFERENCES mf_folio_ledger(id)
	);

COMMENT ON TABLE mf_nominee_info IS 'stores the nominee update status for a given folio id.';
COMMENT ON COLUMN mf_nominee_info.update_status IS 'stores the status (success/failure) of the nominee update at vendor.';
COMMENT ON COLUMN mf_nominee_info.failure_reason IS 'stores the reason for nominee update failure if the nominee update failed. This is only relevant  when update_status represents failure';
COMMENT ON COLUMN mf_nominee_info.vendor_request_id IS 'stores a unique request id used to make idempotent calls to vendor';
