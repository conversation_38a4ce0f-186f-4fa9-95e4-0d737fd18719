update mutual_funds
set version_support_info = '{"minSupportedAndroidAppVersion": 150, "minSupportedIosAppVersion": 202}',
	internal_status      = 'AVAILABLE',
	allowed_user_groups  = null
where isin_number in (
					  'INF251K01OZ7',
					  'INF205K014Q7',
					  'INF582M01GY0',
					  'INF251K01OT0',
					  'INF205K01KR8',
					  'INF582M01ES7',
					  'INF955L01KC8',
					  'INF205K01KT4',
					  'INF251K01HR8',
					  'INF205K01LE4',
					  'INF251K01GP4',
					  'INF205K01RF8',
					  'INF582M01DU5',
					  'INF955L01FR6',
					  'INF205K01I83',
					  'INF955L01HF7',
					  'INF205K01LN5',
					  'INF582M01DE9',
					  'INF251K01GW0',
					  'INF582M01799',
					  'INF251K01HF3',
					  'INF205K01NT8',
					  'INF582M01765',
					  'INF251K01PT7',
					  'INF205KA1338',
					  'INF205K01MD4',
					  'INF955L01KK1',
					  'INF205K01NG5',
					  'INF955L01IL3',
					  'INF205KA1049',
					  'INF582M01EC1',
					  'INF205KA1494',
					  'INF582M01633',
					  'INF251K01PN0',
					  'INF205KA1213',
					  'INF582M01FS4',
					  'INF251K01QT5',
					  'INF205K01B49',
					  'INF205KA1270',
					  'INF205K01A24',
					  'INF955L01AD7',
					  'INF205K01SN0',
					  'INF955L01JU2',
					  'INF205K01MA0',
					  'INF582M01GA0',
					  'INF251K01HN7',
					  'INF205K01LB0',
					  'INF582M01CU7',
					  'INF955L01AL0',
					  'INF205K01MF9',
					  'INF582M01674',
					  'INF251K01GJ7',
					  'INF205K01NY8',
					  'INF251K01LU4',
					  'INF205KA1429',
					  'INF582M01GQ6',
					  'INF251K01HQ0',
					  'INF205K01MV6',
					  'INF582M01GI3',
					  'INF955L01ID0',
					  'INF205K01RY9',
					  'INF582M01HG5',
					  'INF955L01AG0',
					  'INF205K01MS2',
					  'INF955L01HV4',
					  'INF205KA1163',
					  'INF582M01FA2',
					  'INF955L01971',
					  'INF205K01KY4',
					  'INF205K01NJ9',
					  'INF955L01BD5',
					  'INF205K01UH8',
					  'INF205K013T3',
					  'INF582M01BU9',
					  'INF955L01HA8',
					  'INF205K01TH0',
					  'INF582M01EK4'
	);
