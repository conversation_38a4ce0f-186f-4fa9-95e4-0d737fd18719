CREATE TABLE IF NOT EXISTS golden_tickets (
    -- Golden ticket code.
    golden_ticket_code STRING NOT NULL,

    -- referral_code associated with the golden ticket.
    referral_code STRING NOT NULL REFERENCES referrals(referral_code),

    status STRING,
    -- email to which the golden ticket is forwarded.
    referee_email STRING,

    created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ,

	PRIMARY KEY (golden_ticket_code),
	INDEX idx_referral_code(referral_code)
);
alter table waitlist_users add column if not exists user_type string;
