CREATE UNIQUE INDEX IF NOT EXISTS ovom_vendor_order_id_order_type_partial_unique_idx ON order_vendor_order_map (vendor_order_id, order_direction) WHERE order_direction = 'ORDER_DIRECTION_FORWARD';
COMMENT ON COLUMN order_vendor_order_map.order_direction IS 'enum to specify the direction of payment. ORDER_DIRECTION_FORWARD would mean that the payment is a debit w.r.t the user. ORDER_DIRECTION_REVERSE would mean that the payment is a reversal of an already done payment';
