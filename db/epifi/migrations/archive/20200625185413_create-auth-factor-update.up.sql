CREATE TABLE IF NOT EXISTS auth_factor_updates
(
    id             UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    actor_id       STRING,
    overall_status STRING,
    context        JSONB,
    created_at     TIMESTAMPTZ      NOT NULL DEFAULT NOW(),
    updated_at     TIMESTAMPTZ      NOT NULL DEFAULT NOW(),
    deleted_at     TIMESTAMPTZ,

    -- unique index on actor_id and deleted_at to make sure
    -- only one auth factor update attempt is in progress
    UNIQUE INDEX actor_id_deleted_at_key (actor_id, deleted_at)
);
