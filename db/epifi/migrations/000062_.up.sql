CREATE TABLE IF NOT EXISTS pi_state_logs (
    id SERIAL NOT NULL ,
    pi_id STRING NOT NULL,
    source STRING NOT NULL,
    state STRING NOT NULL,
    reason STRING NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    PRIMARY KEY (pi_id, id),
    CONSTRAINT fk_pi_state_log_pi_id FOREIGN KEY (pi_id) REFERENCES payment_instruments(id),
    UNIQUE INDEX pi_state_log_id(id)
);
