-- https://monorail.pointz.in/p/fi-app/issues/detail?id=52395
-- P2P_FUND_TRANSFER orders are IN_PAYMENT when txn status is FAILED
-- Related to https://monorail.pointz.in/p/fi-app/issues/detail?id=54457
-- Moving orders to PAYMENT_FAILED, WF to FAILED in workflow_requests and workflow_histories tables
-- Details : https://docs.google.com/spreadsheets/d/1VFcgd8M09h6jnPdPmzYJS8w2PM7exUDfVymOLdnLbPs/edit#gid=0

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODlhz5MaMURDWoGYbnkR4Yzg230612==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODGvC15lJwTy2PLQZb9FqsYA230612==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODfSlYyP2NTNWRoPUSJjWlBw230612==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODjGMR70vtRk2RDlw1x/1Oxg230504==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODlgWEQrm3TKGnVk4vkxXHhQ230504==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD29MQa+YbQLis2tiAfbRzdQ230504==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD6+K8DegMRRavS6sUoZduow230504==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODijoqkHwDTkeCpgtoh5PE/Q230504==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD13EFhZu5QACFRtpC+j+KeQ230504==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODbJM35tQsTOixKoVICOYXdg230503==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODsP3A+qQuRgmYFzYFITHjeQ230503==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODLCsD/Pm2QfSnE3Rs/ee8kA230503==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODa7oP1nhdRtSipFtHF6iKQA230503==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODbrURgW/QTtqbdsqc26Tm3w230503==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODgNGK6ln0QiycoYsJhkLa3g230503==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODD5u3i2LGQ+ua580HD7AAiw230503==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODvXG8ULbCSJCWo59s3+2lag230503==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODv3WGgDtSS7y8uOOvLqaShg230503==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODbsrg8GZCRBybzfqDgPadOw230503==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODPQsh92btTPS/tdwDyv97bQ230503==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODNPMA+U59TQqxnp6CQWEsHw230503==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODL0zIoeBxSv2C+biDSkfB+g230502==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODV1raItAxR/GujV2ExwzW7w230502==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD5zU8OJPjRkKmqC14MAXEvg230502==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'ODqo8efNaMRCu38WDMY7o6hA230502==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD221206PlBhjhrCTRO86QunT/0iOw==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD221206m4bFX8XwT7uqYlYA2/NEFA==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD221206LXI/CeYbR1yU9FbXvJzWUA==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD221206RniuQ+ZARZi9kEXGrjezPQ==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD221206Mds+mg1VT7yYaLPzB3yLKA==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD2202033X6V/MO6SYuqYs1bmWOnXQ==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220203e0gUGimnTZeicumPjS2e5g==' AND status = 'IN_PAYMENT' ;
