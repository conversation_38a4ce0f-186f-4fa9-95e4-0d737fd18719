-- https://monorail.pointz.in/p/fi-app/issues/detail?id=66404
-- Discussion thread : https://epifi.slack.com/archives/C0101Q3TXFF/p1699944969613289
-- WF fixture : db/epifi/fixtures/update_workflow_requests_and_histories_tables-1.sql

UPDATE orders SET status = 'PAID', updated_at = NOW() where id = 'ODTGWCynmjRnCwIED2A4R4jA231031==' AND status = 'PAYMENT_FAILED' ;
UPDATE orders SET status = 'PAID', updated_at = NOW() where id = 'ODGOqAEJidQXiVTO0NPvQyEA231024==' AND status = 'PAYMENT_FAILED' ;
UPDATE orders SET status = 'PAID', updated_at = NOW() where id = 'ODyYD2PleKSO2rbuYj4FNgrQ231028==' AND status = 'PAYMENT_FAILED' ;
UPDATE orders SET status = 'PAID', updated_at = NOW() where id = 'ODbqFdy/oYRKSkk1La8Yb8ww231009==' AND status = 'PAYMENT_FAILED' ;
