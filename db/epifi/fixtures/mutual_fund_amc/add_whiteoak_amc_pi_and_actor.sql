-- Ensure 'actor_name', 'verified_name' and 'name in bank account details' corresponds
-- to a value which we want to display to the user in receipts, transactions etc.

-- Adding actors for all onboarded AMCs for mutual fund investments
UPSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-whiteoak-amc-business-account', 'EXTERNAL_USER', null, 'Whiteoak Capital', 'EPIFI_WEALTH');


-- Adding pi for onboarded amcs
UPSERT INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification) VALUES
	('paymentinstrument-whiteoak-amc-business-account', 'BANK_ACCOUNT', 'Whiteoak Capital Mutual Fund', '{"account": {"account_type": "CURRENT", "actual_account_number": "WOCAMCEPIFINA200015185", "ifsc_code": "HDFC0000240", "name": "Whiteoak Capital Mutual Fund", "secure_account_number": "xxxxxxxxxxxxxxxxx15185"}}', 'CREATED', '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');
