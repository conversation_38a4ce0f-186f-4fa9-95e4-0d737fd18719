INSERT
INTO actors(id, type, entity_id, name)
VALUES ('pg-adjustment-generic-vendor-actor-id', 'EXTERNAL_USER', null, 'PG Adjustment Actor')
ON CONFLICT (id) DO UPDATE SET type      = excluded.type,
							   entity_id = excluded.entity_id,
							   name      = excluded.name;

INSERT
INTO actors(id, type, entity_id, name)
VALUES ('pg-refund-no-forward-payment-actor-id', 'EXTERNAL_USER', null, 'PG Refund Actor')
ON CONFLICT (id) DO UPDATE SET type      = excluded.type,
							   entity_id = excluded.entity_id,
							   name      = excluded.name;
