CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE IF NOT EXISTS credit_cards (
	id VARCHAR NOT NULL,
	actor_id VARCHAR NOT NULL,
	account_id VARCHAR NOT NULL,
	form VARCHAR NOT NULL,
	network_type VARCHAR NOT NULL,
	vendor_identifier VARCHAR NOT NULL,
	vendor VARCHAR NOT NULL,
	controls_data JSONB NOT NULL,
	issuance_fee JSONB NOT NULL,
	basic_info JSONB NOT NULL,
	state VARCHAR NOT NULL,
	limits JSONB NOT NULL,
	card_sku_type VARCHAR NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL
);

COMMENT ON TABLE credit_cards IS 'table to store data relevant to credit card';
COMMENT ON COLUMN credit_cards.form IS 'whether the card is in physical or digital form';
COMMENT ON COLUMN credit_cards.vendor_identifier IS 'the unique id for the card on the vendors end';
COMMENT ON COLUMN credit_cards.controls_data IS 'Blob containing information regarding the card controls i.e. if a given control is enabled or disabled';
COMMENT ON COLUMN credit_cards.basic_info IS 'to store the tokenized card number, expiry, emboss name, masked_card_number';
COMMENT ON COLUMN credit_cards.limits IS 'card limit wrt region and control i.e domestic, international, etc.';
CREATE TABLE IF NOT EXISTS card_audits (
	id VARCHAR NOT NULL,
	card_id VARCHAR NOT NULL,
	change_type VARCHAR NOT NULL,
	details JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL
);

COMMENT ON TABLE card_audits IS 'table used for maintaining history for changes in cards table.';
COMMENT ON COLUMN card_audits.change_type IS 'Enum denoting type of flow';
COMMENT ON COLUMN card_audits.details IS 'column containing information related to each request type, example : in case of limit update we will store the current limits';
CREATE TABLE IF NOT EXISTS credit_accounts (
	id VARCHAR NOT NULL,
	actor_id VARCHAR NOT NULL,
	reference_id VARCHAR NOT NULL,
	billed_amount FLOAT8 NOT NULL,
	unbilled_amount FLOAT8 NOT NULL,
	total_outstanding FLOAT8 NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL,
	total_limit FLOAT8 NOT NULL DEFAULT 0.0::FLOAT8,
	last_synced_at TIMESTAMPTZ NULL,
	card_program VARCHAR NULL,
	collateral_details JSONB NULL,
	due_info JSONB NULL
);

COMMENT ON TABLE credit_accounts IS 'table to store data relevant to credit card account';
COMMENT ON COLUMN credit_accounts.reference_id IS 'this may vary for vendor to vendor for the current vendor this will be entity id passed by us to vendor by the vendor for a card';
COMMENT ON COLUMN credit_accounts.card_program IS 'stores the details associated with card program under which an account is created in arn format';
COMMENT ON COLUMN credit_accounts.collateral_details IS 'stores the collateral information against which the credit line is issued';
COMMENT ON COLUMN credit_accounts.due_info IS 'stores the due information for the user';

CREATE TABLE IF NOT EXISTS credit_card_transactions (
	id VARCHAR NOT NULL,
	account_id VARCHAR NOT NULL,
	card_id VARCHAR NOT NULL,
	amount JSONB NOT NULL,
	balance JSONB NOT NULL,
	txn_time TIMESTAMPTZ NOT NULL,
	txn_status VARCHAR NOT NULL,
	txn_category VARCHAR NOT NULL,
	txn_origin VARCHAR NOT NULL,
	txn_type VARCHAR NOT NULL,
	beneficiary_info JSONB NULL,
	conversion_info JSONB NULL,
	description VARCHAR NULL,
	dispute_info JSONB NULL,
	external_txn_id VARCHAR NULL,
	bill_ref_no VARCHAR NULL,
	bank_txn_id VARCHAR NULL,
	auth_code VARCHAR NULL,
	acquirer_id VARCHAR NULL,
	retrieval_reference_no VARCHAR NULL,
	sor_txn_id VARCHAR NULL,
	txn_reference_no VARCHAR NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL,
	failure_info JSONB NULL,
	vendor_ext_txn_id VARCHAR NULL,
	parent_transaction_id VARCHAR NULL,
	child_transaction_ids JSONB NULL,
	dedupe_id VARCHAR NULL,
	transaction_authorization_status VARCHAR NULL
);
COMMENT ON COLUMN credit_card_transactions.dedupe_id IS '{"proto_type":"firefly.accounting.internal.cardTransaction.dedupe_id", "comment": "Unique derived id to uniquely identify transaction from provided vendor data"}';

CREATE TABLE IF NOT EXISTS cc_offers (
	id VARCHAR NOT NULL,
	actor_id VARCHAR NOT NULL,
	vendor_offer_id VARCHAR NOT NULL,
	vendor VARCHAR NOT NULL,
	offer_constraints JSONB NOT NULL,
	valid_since TIMESTAMPTZ NOT NULL,
	valid_till TIMESTAMPTZ NOT NULL,
	deactivated_at TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL,
	cc_offer_eligibility_criteria_id VARCHAR NULL,
	card_program VARCHAR NULL,
	offer_details JSONB NULL
);
COMMENT ON TABLE cc_offers IS 'table to store all the cc offers received from vendor';
COMMENT ON COLUMN cc_offers.actor_id IS 'actor for which cc offer is available';
COMMENT ON COLUMN cc_offers.vendor_offer_id IS 'id for the offer provided by vendor';
COMMENT ON COLUMN cc_offers.vendor IS '{"proto_type":"cc.Vendor", "comment":"vendor who has offered cc"}';
COMMENT ON COLUMN cc_offers.offer_constraints IS '{"proto_type":"ccPb.OfferConstraints", "comment":"cc offer constraints like max cc amount, max EMI amount, max cc tenure"}';
COMMENT ON COLUMN cc_offers.valid_since IS 'cc offer validity start time';
COMMENT ON COLUMN cc_offers.valid_till IS 'cc offer validity end time';
COMMENT ON COLUMN cc_offers.deactivated_at IS 'cc offer deactivation time';
COMMENT ON COLUMN cc_offers.created_at IS 'cc offer creation time';
COMMENT ON COLUMN cc_offers.updated_at IS 'cc offer latest update time';
COMMENT ON COLUMN cc_offers.card_program IS 'stores the details associated with card program under which an offer is created in arn format';
COMMENT ON COLUMN cc_offers.offer_details IS 'stores the details regarding the offer like the parent offer id in case of secured cards';

CREATE TABLE IF NOT EXISTS cc_offer_eligibility_criteria (
	id VARCHAR NOT NULL,
	actor_id VARCHAR NOT NULL,
	vendor VARCHAR NOT NULL,
	status VARCHAR NOT NULL,
	vendor_response JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL,
	provenance VARCHAR NULL);
COMMENT ON TABLE cc_offer_eligibility_criteria IS 'table to store all the actors who are eligible for cc from BA side';
COMMENT ON COLUMN cc_offer_eligibility_criteria.actor_id IS 'actor who is eligible for cc from BA';
COMMENT ON COLUMN cc_offer_eligibility_criteria.vendor IS '{"proto_type":"creditcard.Vendor", "comment":"vendor to whom cc offer is requested"}';
COMMENT ON COLUMN cc_offer_eligibility_criteria.status IS '{"proto_type":"creditcard.CreditCardOfferEligibilityCriteriaStatus", "comment":"status of the eligibility"}';
COMMENT ON COLUMN cc_offer_eligibility_criteria.vendor_response IS '{"proto_type":"creditcard.VendorResponse", "comment":"response timestamp from vendor"}';

CREATE TABLE IF NOT EXISTS credit_card_bills (
	id VARCHAR NOT NULL,
	actor_id VARCHAR NOT NULL,
	account_id VARCHAR NOT NULL,
	last_statement_balance JSONB NULL,
	current_statement_amount JSONB NULL,
	total_credit JSONB NULL,
	total_debit JSONB NULL,
	cash JSONB NULL,
	purchase JSONB NULL,
	min_due JSONB NULL,
	total_due JSONB NULL,
	statement_date TIMESTAMPTZ NOT NULL,
	soft_due_date TIMESTAMPTZ NOT NULL,
	hard_due_date TIMESTAMPTZ NOT NULL,
	rewards_info JSONB NULL,
	analytics_info JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL,
	statement_summary JSONB NULL,
	available_limit JSONB NULL,
	s3_path VARCHAR NULL,
	reward_id VARCHAR NULL);
CREATE TABLE IF NOT EXISTS card_requests (
	id VARCHAR NOT NULL,
	card_id VARCHAR NOT NULL,
	actor_id VARCHAR NOT NULL,
	orch_id VARCHAR NOT NULL,
	vendor VARCHAR NOT NULL,
	request_details JSONB NOT NULL,
	next_action JSONB NULL,
	workflow VARCHAR NOT NULL,
	status VARCHAR NOT NULL,
	provenance VARCHAR NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL
);
COMMENT ON TABLE card_requests IS 'entity where we will keep track of all card requests and will store the state machine for each request';
COMMENT ON COLUMN card_requests.orch_id IS 'id for orchestrating the request at celestial/orchestrator';
COMMENT ON COLUMN card_requests.request_details IS 'Metadata for a given request. This can be user provided information or information collected internally for initiating the flow';
COMMENT ON COLUMN card_requests.next_action IS 'deeplink of the next screen to be shown to the user when client polls';
COMMENT ON COLUMN card_requests.status IS 'high level status of the request';
COMMENT ON COLUMN card_requests.provenance IS 'enum denoting entry point for the request, APP/SHERLOCK etc';
CREATE TABLE IF NOT EXISTS card_request_stages (
	id VARCHAR NOT NULL,
	card_request_id VARCHAR NOT NULL,
	external_request_id VARCHAR NOT NULL,
	orch_id VARCHAR NOT NULL,
	stage VARCHAR NOT NULL,
	stage_execution_details JSONB NOT NULL,
	status VARCHAR NOT NULL,
	sub_status VARCHAR NOT NULL,
	staled_at TIMESTAMPTZ NULL,
	completed_at TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL
);
COMMENT ON TABLE card_request_stages IS 'table to store specific stage related data for card_requests';
COMMENT ON COLUMN card_request_stages.stage_execution_details IS 'metadata for the given stage';
COMMENT ON COLUMN card_request_stages.status IS 'high level status of the request stage';
COMMENT ON COLUMN card_request_stages.sub_status IS 'granular level status of the request stage';
COMMENT ON COLUMN card_request_stages.staled_at IS 'time to make step stale so that re-execution of the step can be done';
COMMENT ON COLUMN card_request_stages.completed_at IS 'time at which the request stage is completed';
CREATE TABLE IF NOT EXISTS credit_card_skus (
	sku_type VARCHAR NOT NULL,
	type VARCHAR NOT NULL,
	feature_info JSONB NOT NULL,
	vendor_card_sku JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL
);
COMMENT ON TABLE credit_card_skus IS 'table to define the card variants offered by Epifi';
COMMENT ON COLUMN credit_card_skus.type IS 'type of card as in DEBIT/CREDIT';
COMMENT ON COLUMN credit_card_skus.feature_info IS 'card variant information like number of free cards, etc';
COMMENT ON COLUMN credit_card_skus.vendor_card_sku IS 'Vendor specific information related to the above features';
CREATE TABLE IF NOT EXISTS credit_card_sku_overrides (
	actor_id VARCHAR NOT NULL,
	card_sku_type VARCHAR NOT NULL,
	feature_override_info JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL
);
COMMENT ON TABLE credit_card_sku_overrides IS 'table to store the special provisions for an actor like more number of card provisions, etc.';
COMMENT ON COLUMN credit_card_sku_overrides.feature_override_info IS 'blob containing the information regarding the override information specific to the actor';
CREATE TABLE IF NOT EXISTS transaction_additional_infos (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	transaction_id VARCHAR NOT NULL,
	pi_to VARCHAR NOT NULL,
	pi_from VARCHAR NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL,
	actor_from VARCHAR NULL,
	actor_to VARCHAR NULL,
	txn_time TIMESTAMPTZ NOT NULL,
	enriched_beneficiary_info JSONB NULL,
	bill_ref_id VARCHAR NULL
);
CREATE TABLE IF NOT EXISTS do_once_tasks (
	task_name VARCHAR NOT NULL,
	deleted_at_unix INT8 NOT NULL DEFAULT 0::INT8,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE do_once_tasks IS 'to identify tasks by their unique names that should be done exactly once';
COMMENT ON COLUMN do_once_tasks.task_name IS 'unique identifier for a task';
COMMENT ON COLUMN do_once_tasks.deleted_at_unix IS 'non-zero for soft-deleted tasks so that another task with same name can be created again once previous task is soft-deleted';
CREATE TABLE IF NOT EXISTS disputed_transactions (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	transaction_id VARCHAR NOT NULL,
	actor_id VARCHAR NOT NULL,
	account_id VARCHAR NOT NULL,
	ext_dispute_ref VARCHAR NOT NULL,
	dispute_state VARCHAR NOT NULL,
	disputed_at TIMESTAMPTZ NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL,
	dispute_details JSONB NULL
);
COMMENT ON TABLE disputed_transactions IS 'entity to keep track of disputed transactions';
COMMENT ON COLUMN disputed_transactions.transaction_id IS 'id of transaction from credit_card_transactions table';
COMMENT ON COLUMN disputed_transactions.ext_dispute_ref IS 'external dispute reference id from vendor';
COMMENT ON COLUMN disputed_transactions.dispute_state IS 'current state of dispute';
COMMENT ON COLUMN disputed_transactions.disputed_at IS 'timestamp at which dispute was raised';
CREATE TABLE IF NOT EXISTS credit_card_payment_info (
	id VARCHAR NOT NULL,
	bill_info_id VARCHAR NULL,
	external_txn_id VARCHAR NULL,
	order_id VARCHAR NULL,
	vendor_txn_ref_no VARCHAR NOT NULL,
	payment_date TIMESTAMPTZ NOT NULL,
	amount JSONB NULL,
	payment_status VARCHAR NOT NULL,
	payment_sub_status VARCHAR NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL,
	account_id VARCHAR NULL,
	repayment_type VARCHAR NULL
);
CREATE TABLE IF NOT EXISTS loan_account (
	id VARCHAR NOT NULL,
	actor_id VARCHAR NOT NULL,
	transaction_loan_offers_id VARCHAR NULL,
	vendor_loan_id VARCHAR NULL,
	tenure_in_months INT8 NULL,
	amount_info JSONB NULL,
	interest_info JSONB NULL,
	fee_info JSONB NULL,
	repayment_info JSONB NULL,
	summary JSONB NULL,
	loan_schedule JSONB NULL,
	status VARCHAR NULL,
	disbursed_date DATE NULL,
	loan_end_date DATE NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL
);
CREATE TABLE IF NOT EXISTS transaction_loan_offers (
	id VARCHAR NOT NULL,
	actor_id VARCHAR NOT NULL,
	transaction_id VARCHAR NULL,
	tenure_in_months INT8 NULL,
	vendor_loan_request_id VARCHAR NULL,
	amount_info JSONB NULL,
	interest_info JSONB NULL,
	processing_fee_info JSONB NULL,
	summary JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL
);

CREATE INDEX IF NOT EXISTS card_audits_updated_at_idx ON card_audits (updated_at ASC);
CREATE INDEX IF NOT EXISTS card_request_stages_updated_at ON card_request_stages (updated_at ASC);
CREATE INDEX IF NOT EXISTS card_requests_updated_at ON card_requests (updated_at ASC);
CREATE INDEX IF NOT EXISTS cc_offers_updated_at_idx ON cc_offers (updated_at ASC);
CREATE INDEX IF NOT EXISTS credit_accounts_updated_at_idx ON credit_accounts (updated_at ASC);
CREATE INDEX IF NOT EXISTS credit_card_loan_account_updated_at_idx ON loan_account (updated_at ASC);
CREATE INDEX IF NOT EXISTS credit_card_sku_overrides_updated_at_idx ON credit_card_sku_overrides (updated_at ASC);
CREATE INDEX IF NOT EXISTS credit_card_skus_updated_at_idx ON credit_card_skus (updated_at ASC);
CREATE INDEX IF NOT EXISTS credit_card_transactions_updated_at_idx ON credit_card_transactions (updated_at ASC);
CREATE INDEX IF NOT EXISTS credit_cards_updated_at_idx ON credit_cards (updated_at ASC);
CREATE INDEX IF NOT EXISTS disputed_transactions_updated_at_idx ON disputed_transactions (updated_at ASC);
CREATE INDEX IF NOT EXISTS do_once_tasks_updated_at_idx ON do_once_tasks (updated_at ASC);
CREATE INDEX IF NOT EXISTS transaction_additional_infos_updated_at_idx ON transaction_additional_infos (updated_at ASC);
CREATE INDEX IF NOT EXISTS credit_card_bills_updated_at_idx ON credit_card_bills  (updated_at DESC);
CREATE INDEX IF NOT EXISTS credit_card_payment_info_updated_at_idx ON credit_card_payment_info  (updated_at DESC);
CREATE INDEX IF NOT EXISTS cc_offer_eligibility_criteria_updated_at_idx ON cc_offer_eligibility_criteria  (updated_at DESC);
CREATE INDEX IF NOT EXISTS credit_card_transaction_loan_offers_updated_at_idx ON transaction_loan_offers (updated_at);
