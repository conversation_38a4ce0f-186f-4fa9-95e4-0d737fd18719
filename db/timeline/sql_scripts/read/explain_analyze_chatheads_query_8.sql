EXPLAIN ANALYZE
(SELECT *
 FROM timelines
 WHERE ownership = 'EPIFI_TECH'
   AND primary_actor_id = 'AC210622KqPeekvVTOuMmZaVRMZBew=='
   AND deleted_at IS NULL
   AND last_event_updated_at < now()
 ORDER BY last_event_updated_at desc
 LIMIT 20)
UNION
(SELECT *
 FROM timelines
 WHERE ownership = 'EPIFI_TECH'
   AND secondary_actor_id = 'AC210622KqPeekvVTOuMmZaVRMZBew=='
   AND secondary_actor_state != 'NOT_VISIBLE'
   AND deleted_at IS NULL
   AND last_event_updated_at < now()
 ORDER BY last_event_updated_at desc
 LIMIT 20)
ORDER BY last_event_updated_at DESC
LIMIT 20;
