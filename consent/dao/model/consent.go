package model

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"time"

	"gorm.io/gorm"

	"github.com/epifi/gamma/api/consent"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/be-common/pkg/nulltypes"
)

// Consent model records the consents provided by the actor.
type Consent struct {
	ID        string `gorm:"primary_key;type:uuid;default:gen_random_uuid()"`
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt

	// Actor that has provided the consent
	// Deprecated: Do not use. Use ActorId instead
	Actor *types.Actor

	// ActorId of the Actor that has provided the consent
	ActorId string

	// Device fingerprint from where actor has given the consent
	Device *commontypes.Device

	// ConsentType of Consent that the actor has provided
	ConsentType consent.ConsentType

	// Version of Consent, if applicable Ex: Terms and condition
	Version int

	// time after which consent expires. For non-expiring consents it will be null
	ExpiresAt nulltypes.NullTime

	// Obfuscated token, referencing ip_address from pii_tokens table
	IpAddressToken string

	// Client Request id is to dedupe consent for a flow
	ClientReqId nulltypes.NullString

	// source of consent
	Provenance consent.ConsentProvenance
}
