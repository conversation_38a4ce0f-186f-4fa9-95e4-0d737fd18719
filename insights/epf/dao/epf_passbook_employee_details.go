//nolint:dupl
package dao

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	dbTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	epfModel "github.com/epifi/gamma/api/insights/epf/model"
	"github.com/epifi/gamma/insights/epf/dao/model"

	"github.com/epifi/be-common/pkg/logger"
)

type EpfPassbookEmployeeDetailsDaoPGDB struct {
	DB dbTypes.InsightsPGDB
}

func NewEpfPassbookEmployeeDetailsDaoPGDB(db dbTypes.InsightsPGDB) *EpfPassbookEmployeeDetailsDaoPGDB {
	return &EpfPassbookEmployeeDetailsDaoPGDB{DB: db}
}

func (e *EpfPassbookEmployeeDetailsDaoPGDB) Create(ctx context.Context, data *epfModel.EpfPassbookEmployeeDetails) (*epfModel.EpfPassbookEmployeeDetails, error) {
	defer metric_util.TrackDuration("insights/epf/dao", "EpfPassbookEmployeeDetailsDaoPGDB", "Create", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, e.DB)
	epfPassbookEmployeeDetailsModel := model.NewEpfPassbookEmployeeDetails(data)
	resp := db.Create(epfPassbookEmployeeDetailsModel)
	if resp.Error != nil && storagev2.IsDuplicateRowError(resp.Error) {
		return nil, fmt.Errorf("entry with same uan id already exists  : %w", epifierrors.ErrDuplicateEntry)
	}
	if resp.Error != nil {
		return nil, fmt.Errorf("unable to create DB entry for epfPassbookEmployeeDetailsModel : %w", resp.Error)
	}
	return epfPassbookEmployeeDetailsModel.ToProto(), nil
}

func (e *EpfPassbookEmployeeDetailsDaoPGDB) DeleteByActorId(ctx context.Context, actorId string) error {
	defer metric_util.TrackDuration("insights/epf/dao", "EpfPassbookEmployeeDetailsDaoPGDB", "DeleteByActorId", time.Now())
	if actorId == "" {
		return fmt.Errorf("actor id cannot be empty")
	}

	curTime := time.Now()
	db := gormctxv2.FromContextOrDefault(ctx, e.DB)
	result := db.Model(&model.EpfPassbookEmployeeDetails{}).
		Where("actor_id = ? AND deleted_at IS NULL", actorId).
		Updates(map[string]interface{}{
			"deleted_at": gorm.DeletedAt{
				Time:  curTime,
				Valid: true,
			},
			"updated_at": curTime,
		})

	if result.Error != nil {
		return fmt.Errorf("failed to soft delete epf passbook employee details: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		logger.Info(ctx, "no epf passbook employee details found for given actor id", zap.String("actor_id", actorId))
		return epifierrors.ErrRecordNotFound

	}

	return nil
}
