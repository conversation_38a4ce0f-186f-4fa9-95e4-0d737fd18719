package investment_declaration

//go:generate mockgen -source=processor.go -destination=./mocks/mock_processor.go package=mocks

import (
	"context"
	"errors"
	"fmt"

	"github.com/google/wire"

	rpcPb "github.com/epifi/be-common/api/rpc"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	networthModelPb "github.com/epifi/gamma/api/insights/networth/model"
	payPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/insights/networth/dao"
	"github.com/epifi/gamma/insights/networth/investment_declaration/calculator"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
)

var WireInvestmentDeclarationProcessorSet = wire.NewSet(NewInvestmentDeclarationProcessor, wire.Bind(new(IProcessor), new(*InvestmentDeclarationProcessor)))

type IProcessor interface {
	GetInvestmentDetails(ctx context.Context, actorId string, instrumentTypes []typesPb.InvestmentInstrumentType,
		pageContext *rpcPb.PageContextRequest) ([]*networthPb.InvestmentDetails, *rpcPb.PageContextResponse, error)
	GetAllInvestmentDetails(ctx context.Context, actorId string, instrumentTypes []typesPb.InvestmentInstrumentType) ([]*networthPb.InvestmentDetails, error)
	EnrichInvestmentDetails(ctx context.Context, declaration *networthModelPb.InvestmentDeclaration) (*networthPb.InvestmentDetails, error)
}

type InvestmentDeclarationProcessor struct {
	investmentDeclarationDao    dao.InvestmentDeclarationDao
	investmentCalculatorFactory calculator.InvestmentCalculatorFactory
	iftClient                   payPb.InternationalFundTransferClient
}

func NewInvestmentDeclarationProcessor(
	investmentDeclarationDao dao.InvestmentDeclarationDao,
	investmentCalculatorFactory calculator.InvestmentCalculatorFactory,
	iftClient payPb.InternationalFundTransferClient,
) *InvestmentDeclarationProcessor {
	return &InvestmentDeclarationProcessor{
		investmentDeclarationDao:    investmentDeclarationDao,
		investmentCalculatorFactory: investmentCalculatorFactory,
		iftClient:                   iftClient,
	}
}

// GetInvestmentDetails returned enriched details for manually added investments
func (p *InvestmentDeclarationProcessor) GetInvestmentDetails(ctx context.Context, actorId string, instrumentTypes []typesPb.InvestmentInstrumentType,
	pageContext *rpcPb.PageContextRequest) ([]*networthPb.InvestmentDetails, *rpcPb.PageContextResponse, error) {
	filterOptions := make([]storagev2.FilterOption, 0)
	pageToken, err := pagination.GetPageToken(pageContext)
	if err != nil {
		return nil, nil, fmt.Errorf("unable to fetch pageToken from page context: %w", err)
	}

	if len(instrumentTypes) > 0 {
		filterOptions = append(filterOptions, dao.WithInstrumentTypes(instrumentTypes))
	}
	declarations, pagecontext, err := p.investmentDeclarationDao.GetByActorAndFilters(ctx, actorId, pageToken, pageContext.GetPageSize(), filterOptions...)
	if err != nil {
		return nil, nil, fmt.Errorf("unable to fetch investment declarations from db: %w", err)
	}
	var investmentsDetails []*networthPb.InvestmentDetails
	for _, declaration := range declarations {
		details, err := p.EnrichInvestmentDetails(ctx, declaration)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to enrich investments details: %w", err)
		}
		investmentsDetails = append(investmentsDetails, details)
	}

	return investmentsDetails, pagecontext, nil
}

// EnrichInvestmentDetails enriches additional details representing current state and performance of investment
func (p *InvestmentDeclarationProcessor) EnrichInvestmentDetails(ctx context.Context, declaration *networthModelPb.InvestmentDeclaration) (*networthPb.InvestmentDetails, error) {
	investmentCalculator, err := p.investmentCalculatorFactory.GetCalculator(ctx, declaration.GetInstrumentType())
	if err != nil {
		return nil, fmt.Errorf("failed to get investment calculator for instrument: %w", err)
	}

	currentValue, err := investmentCalculator.GetCurrentValue(ctx, declaration)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate current value for declaration id %v: %w", declaration.GetId(), err)
	}

	computedDetails, err := investmentCalculator.GetComputedInvestmentDetails(ctx, declaration)
	if err != nil {
		return nil, fmt.Errorf("failed to get extra values for declaration id %v: %w", declaration.GetId(), err)
	}

	return &networthPb.InvestmentDetails{
		InvestmentDeclaration:     declaration,
		CurrentValue:              currentValue,
		ComputedInvestmentDetails: computedDetails,
	}, nil
}

func (p *InvestmentDeclarationProcessor) GetAllInvestmentDetails(ctx context.Context, actorId string, instrumentTypes []typesPb.InvestmentInstrumentType) ([]*networthPb.InvestmentDetails, error) {
	var investmentDetails []*networthPb.InvestmentDetails
	details, pageContext, err := p.GetInvestmentDetails(ctx, actorId, instrumentTypes, &rpcPb.PageContextRequest{
		PageSize: 1000,
	})
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("error while getting manual deposit details: %w", err)
	}
	investmentDetails = append(investmentDetails, details...)
	if errors.Is(err, epifierrors.ErrRecordNotFound) || len(investmentDetails) == 0 {
		return nil, fmt.Errorf("no investment details found: %w", epifierrors.ErrRecordNotFound)
	}

	for pageContext.GetHasAfter() {
		details, pageContext, err = p.GetInvestmentDetails(ctx, actorId, instrumentTypes, &rpcPb.PageContextRequest{
			Token: &rpcPb.PageContextRequest_AfterToken{
				AfterToken: pageContext.GetAfterToken(),
			},
			PageSize: 1000,
		})
		if err != nil {
			return nil, fmt.Errorf("error while paginating manual deposit details: %w", err)
		}
		investmentDetails = append(investmentDetails, details...)
	}

	return investmentDetails, nil
}
