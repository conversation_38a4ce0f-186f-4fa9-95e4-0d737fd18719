package developer

import (
	"fmt"

	"github.com/epifi/gamma/api/insights/developer"
	"github.com/epifi/gamma/insights/developer/processor"
)

type DevFactory struct {
	MerchantQuery          *processor.MerchantQuery
	MessageProcessingState *processor.MessageProcessingState
	MailSyncLog            *processor.MailSyncLog
	Merchant               *processor.Merchant
	InsightFramework       *processor.InsightFramework
	InsightSegment         *processor.InsightSegment
	InsightContextMapping  *processor.InsightContextMapping
	GenerationScriptRun    *processor.GenerationScriptRun
	ContentTemplate        *processor.ContentTemplate
	InsightEngagement      *processor.InsightEngagement
	EpfPassbookRequest     *processor.EpfPassbookRequest
	AssetHistory           *processor.AssetHistory
}

func NewDevFactory(merchantQuery *processor.MerchantQuery, messageProcessingState *processor.MessageProcessingState,
	mailSyncLog *processor.MailSyncLog, merchant *processor.Merchant, insightFramework *processor.InsightFramework,
	insightSegment *processor.InsightSegment, insightContextMapping *processor.InsightContextMapping,
	generationScriptRun *processor.GenerationScriptRun, contentTemplate *processor.ContentTemplate,
	insightEngagement *processor.InsightEngagement, epfPassbookRequest *processor.EpfPassbookRequest, assetHistory *processor.AssetHistory,
) *DevFactory {
	return &DevFactory{
		MessageProcessingState: messageProcessingState,
		MailSyncLog:            mailSyncLog,
		MerchantQuery:          merchantQuery,
		Merchant:               merchant,
		InsightFramework:       insightFramework,
		InsightSegment:         insightSegment,
		InsightContextMapping:  insightContextMapping,
		GenerationScriptRun:    generationScriptRun,
		ContentTemplate:        contentTemplate,
		InsightEngagement:      insightEngagement,
		EpfPassbookRequest:     epfPassbookRequest,
		AssetHistory:           assetHistory,
	}
}

func (d *DevFactory) getParameterListImpl(entity developer.InsightsEntity) (IParameterFetcher, error) {
	switch entity {
	case developer.InsightsEntity_MERCHANT_QUERY:
		return d.MerchantQuery, nil
	case developer.InsightsEntity_MAIL_SYNC_REQUEST:
		return d.MailSyncLog, nil
	case developer.InsightsEntity_MESSAGE_PROCESSING_STATE:
		return d.MessageProcessingState, nil
	case developer.InsightsEntity_MERCHANT:
		return d.Merchant, nil
	case developer.InsightsEntity_INSIGHT_FRAMEWORK:
		return d.InsightFramework, nil
	case developer.InsightsEntity_INSIGHT_SEGMENT:
		return d.InsightSegment, nil
	case developer.InsightsEntity_INSIGHT_CONTEXT_MAPPING:
		return d.InsightContextMapping, nil
	case developer.InsightsEntity_GENERATION_SCRIPT_RUN:
		return d.GenerationScriptRun, nil
	case developer.InsightsEntity_CONTENT_TEMPLATE:
		return d.ContentTemplate, nil
	case developer.InsightsEntity_INSIGHT_ENGAGEMENT:
		return d.InsightEngagement, nil
	case developer.InsightsEntity_EPF_PASSBOOK_REQUEST:
		return d.EpfPassbookRequest, nil
	case developer.InsightsEntity_ASSET_HISTORY:
		return d.AssetHistory, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}

func (d *DevFactory) getDataImpl(entity developer.InsightsEntity) (IDataFetcher, error) {
	switch entity {
	case developer.InsightsEntity_MERCHANT_QUERY:
		return d.MerchantQuery, nil
	case developer.InsightsEntity_MAIL_SYNC_REQUEST:
		return d.MailSyncLog, nil
	case developer.InsightsEntity_MESSAGE_PROCESSING_STATE:
		return d.MessageProcessingState, nil
	case developer.InsightsEntity_MERCHANT:
		return d.Merchant, nil
	case developer.InsightsEntity_INSIGHT_FRAMEWORK:
		return d.InsightFramework, nil
	case developer.InsightsEntity_INSIGHT_SEGMENT:
		return d.InsightSegment, nil
	case developer.InsightsEntity_INSIGHT_CONTEXT_MAPPING:
		return d.InsightContextMapping, nil
	case developer.InsightsEntity_GENERATION_SCRIPT_RUN:
		return d.GenerationScriptRun, nil
	case developer.InsightsEntity_CONTENT_TEMPLATE:
		return d.ContentTemplate, nil
	case developer.InsightsEntity_INSIGHT_ENGAGEMENT:
		return d.InsightEngagement, nil
	case developer.InsightsEntity_EPF_PASSBOOK_REQUEST:
		return d.EpfPassbookRequest, nil
	case developer.InsightsEntity_ASSET_HISTORY:
		return d.AssetHistory, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}
